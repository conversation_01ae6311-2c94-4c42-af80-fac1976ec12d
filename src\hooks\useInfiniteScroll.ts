"use client";

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions<T> {
  fetchFunction: (page: number, limit: number) => Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
    };
  }>;
  limit?: number;
  enabled?: boolean;
  dependencies?: any[];
  onError?: (error: Error) => void;
  throttleMs?: number;
  rootMargin?: string;
  threshold?: number;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  total: number;
  loadMore: () => void;
  refresh: () => void;
  reset: () => void;
  loadMoreRef: React.RefObject<HTMLDivElement>;
}

export function useInfiniteScroll<T = any>({
  fetchFunction,
  limit = 10,
  enabled = true,
  dependencies = [],
  onError,
  throttleMs = 200,
  rootMargin = '200px',
  threshold = 0.1,
}: UseInfiniteScrollOptions<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);

  // Refs for intersection observer
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isLoadingRef = useRef(false);

  // Fetch data function
  const fetchData = useCallback(async (pageNum: number, append: boolean = false) => {
    if (isLoadingRef.current) return;
    
    try {
      isLoadingRef.current = true;
      
      if (pageNum === 1) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      const result = await fetchFunction(pageNum, limit);
      
      if (append) {
        setData(prev => {
          // Prevent duplicates
          const existingIds = new Set(prev.map((item: any) => item.id));
          const newItems = result.data.filter((item: any) => !existingIds.has(item.id));
          return [...prev, ...newItems];
        });
      } else {
        setData(result.data);
      }

      setHasMore(result.pagination.hasMore);
      setPage(pageNum);
      setTotal(result.pagination.total);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';
      setError(errorMessage);
      onError?.(err instanceof Error ? err : new Error(errorMessage));
    } finally {
      setLoading(false);
      setLoadingMore(false);
      isLoadingRef.current = false;
    }
  }, [fetchFunction, limit, onError]);

  // Load more function
  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore && !isLoadingRef.current) {
      fetchData(page + 1, true);
    }
  }, [fetchData, loadingMore, hasMore, page]);

  // Throttled load more
  const throttledLoadMore = useCallback(() => {
    if (throttleTimeoutRef.current) {
      clearTimeout(throttleTimeoutRef.current);
    }
    
    throttleTimeoutRef.current = setTimeout(() => {
      loadMore();
    }, throttleMs);
  }, [loadMore, throttleMs]);

  // Setup intersection observer
  useEffect(() => {
    if (!enabled || !loadMoreRef.current) return;

    // Cleanup previous observer
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
          throttledLoadMore();
        }
      },
      {
        root: null,
        rootMargin,
        threshold,
      }
    );

    // Start observing
    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, [enabled, hasMore, loading, loadingMore, throttledLoadMore, rootMargin, threshold]);

  // Initial fetch and dependency changes
  useEffect(() => {
    if (enabled) {
      setData([]);
      setPage(1);
      setHasMore(true);
      setError(null);
      fetchData(1, false);
    }
  }, [enabled, ...dependencies]);

  // Refresh function
  const refresh = useCallback(() => {
    setData([]);
    setPage(1);
    setHasMore(true);
    setError(null);
    fetchData(1, false);
  }, [fetchData]);

  // Reset function
  const reset = useCallback(() => {
    setData([]);
    setLoading(false);
    setLoadingMore(false);
    setError(null);
    setHasMore(true);
    setPage(1);
    setTotal(0);
  }, []);

  return {
    data,
    loading,
    loadingMore,
    error,
    hasMore,
    page,
    total,
    loadMore,
    refresh,
    reset,
    loadMoreRef,
  };
}
