"use client";

import { Fragment } from "react";
import { Listbox, Transition } from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/24/outline";
import { CheckBadgeIcon } from "@heroicons/react/24/solid";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { cn } from "@/lib/utils";

interface FanPage {
  id: string;
  name: string;
  username: string;
  profileImage: string | null;
  isVerified: boolean;
}

interface PageSelectorProps {
  pages: FanPage[];
  selectedPageId: string | null;
  onPageSelect: (pageId: string) => void;
  className?: string;
  variant?: 'default' | 'compact';
}

export function PageSelector({ pages, selectedPageId, onPageSelect, className, variant = 'default' }: PageSelectorProps) {
  const selectedPage = pages.find(page => page.id === selectedPageId);

  if (pages.length === 0) {
    return (
      <div className={cn("text-sm text-gray-500", className)}>
        No fan pages found
      </div>
    );
  }

  if (pages.length === 1) {
    const page = pages[0];

    if (variant === 'compact') {
      return (
        <div className={cn("flex items-center space-x-2 px-3 py-2 bg-white rounded-lg border border-gray-200 shadow-sm", className)}>
          <div className="relative">
            {page.profileImage ? (
              <OptimizedImage
                src={page.profileImage}
                alt={page.name}
                width={24}
                height={24}
                className="h-6 w-6 rounded-full object-cover"
              />
            ) : (
              <div className="h-6 w-6 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-xs font-bold text-white">
                  {page.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            {page.isVerified && (
              <CheckBadgeIcon className="absolute -bottom-0.5 -right-0.5 h-3 w-3 text-blue-500 bg-white rounded-full" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {page.name}
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className={cn("flex items-center space-x-4 p-4 bg-gradient-to-r from-white to-gray-50 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200", className)}>
        <div className="relative">
          {page.profileImage ? (
            <OptimizedImage
              src={page.profileImage}
              alt={page.name}
              width={48}
              height={48}
              className="h-12 w-12 rounded-full object-cover ring-2 ring-white shadow-md"
            />
          ) : (
            <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center ring-2 ring-white shadow-md">
              <span className="text-lg font-bold text-white">
                {page.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          {page.isVerified && (
            <CheckBadgeIcon className="absolute -bottom-1 -right-1 h-5 w-5 text-blue-500 bg-white rounded-full" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="text-base font-semibold text-gray-900 truncate">
              {page.name}
            </p>
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
          </div>
          <p className="text-sm text-gray-600 truncate">
            @{page.username}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Listbox value={selectedPageId} onChange={onPageSelect}>
        <div className="relative">
          <Listbox.Button className={cn(
            "relative w-full cursor-pointer text-left shadow-sm border border-gray-200 hover:border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 transition-all duration-200",
            variant === 'compact'
              ? "rounded-lg bg-white py-2 pl-3 pr-8"
              : "rounded-xl bg-gradient-to-r from-white to-gray-50 py-4 pl-4 pr-12"
          )}>
            {selectedPage ? (
              variant === 'compact' ? (
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    {selectedPage.profileImage ? (
                      <OptimizedImage
                        src={selectedPage.profileImage}
                        alt={selectedPage.name}
                        width={20}
                        height={20}
                        className="h-5 w-5 rounded-full object-cover"
                      />
                    ) : (
                      <div className="h-5 w-5 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-xs font-bold text-white">
                          {selectedPage.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    {selectedPage.isVerified && (
                      <CheckBadgeIcon className="absolute -bottom-0.5 -right-0.5 h-3 w-3 text-blue-500 bg-white rounded-full" />
                    )}
                  </div>
                  <span className="text-sm font-medium text-gray-900 truncate">
                    {selectedPage.name}
                  </span>
                </div>
              ) : (
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    {selectedPage.profileImage ? (
                      <OptimizedImage
                        src={selectedPage.profileImage}
                        alt={selectedPage.name}
                        width={40}
                        height={40}
                        className="h-10 w-10 rounded-full object-cover ring-2 ring-white shadow-sm"
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center ring-2 ring-white shadow-sm">
                        <span className="text-sm font-bold text-white">
                          {selectedPage.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    {selectedPage.isVerified && (
                      <CheckBadgeIcon className="absolute -bottom-1 -right-1 h-4 w-4 text-blue-500 bg-white rounded-full" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-semibold text-gray-900 truncate">
                        {selectedPage.name}
                      </span>
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Selected
                      </span>
                    </div>
                    <span className="text-xs text-gray-600 truncate">
                      @{selectedPage.username}
                    </span>
                  </div>
                </div>
              )
            ) : (
              <div className="flex items-center space-x-3">
                <div className={cn(
                  "rounded-full bg-gray-200 flex items-center justify-center",
                  variant === 'compact' ? "h-5 w-5" : "h-10 w-10"
                )}>
                  <span className={cn(
                    "font-medium text-gray-500",
                    variant === 'compact' ? "text-xs" : "text-sm"
                  )}>?</span>
                </div>
                <span className={cn(
                  "text-gray-500 font-medium",
                  variant === 'compact' ? "text-sm" : "text-base"
                )}>Select a fan page</span>
              </div>
            )}
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon
                className="h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options className="absolute z-50 mt-3 w-96 max-h-80 overflow-auto rounded-2xl bg-white/95 backdrop-blur-xl py-3 text-base shadow-2xl ring-1 ring-black/5 focus:outline-none sm:text-sm border border-white/20">
              {/* Dropdown Header */}
              <div className="px-4 py-2 border-b border-gray-100/50">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Select Fan Page</span>
                </div>
              </div>
              {pages.map((page, index) => (
                <Listbox.Option
                  key={page.id}
                  className={({ active, selected }) =>
                    cn(
                      "relative cursor-pointer select-none py-5 pl-4 pr-12 mx-2 my-1 rounded-xl transition-all duration-200 transform",
                      active
                        ? "bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 text-blue-900 scale-[1.02] shadow-lg ring-2 ring-blue-200/50"
                        : "text-gray-900 hover:bg-gray-50/80 hover:scale-[1.01]",
                      selected && "bg-gradient-to-r from-blue-100 to-purple-100 ring-2 ring-blue-300/50"
                    )
                  }
                  value={page.id}
                >
                  {({ selected, active }) => (
                    <>
                      <div className="flex items-center space-x-4">
                        <div className="relative">
                          {/* Avatar with enhanced styling */}
                          <div className={cn(
                            "relative transition-all duration-200",
                            active && "scale-110"
                          )}>
                            {page.profileImage ? (
                              <OptimizedImage
                                src={page.profileImage}
                                alt={page.name}
                                width={56}
                                height={56}
                                className={cn(
                                  "h-14 w-14 rounded-full object-cover shadow-lg transition-all duration-200",
                                  active ? "ring-3 ring-blue-300/50" : "ring-2 ring-white"
                                )}
                              />
                            ) : (
                              <div className={cn(
                                "h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg transition-all duration-200",
                                active ? "ring-3 ring-blue-300/50" : "ring-2 ring-white"
                              )}>
                                <span className="text-xl font-bold text-white">
                                  {page.name.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                            {page.isVerified && (
                              <CheckBadgeIcon className="absolute -bottom-1 -right-1 h-6 w-6 text-blue-500 bg-white rounded-full shadow-sm" />
                            )}
                          </div>

                          {/* Online indicator */}
                          <div className="absolute -top-1 -left-1">
                            <div className="w-5 h-5 bg-green-500 rounded-full border-2 border-white shadow-sm animate-pulse"></div>
                          </div>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <span
                                className={cn(
                                  "text-lg font-semibold truncate transition-colors duration-200",
                                  selected ? "text-blue-900" : active ? "text-blue-800" : "text-gray-900"
                                )}
                              >
                                {page.name}
                              </span>
                              {selected && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-sm">
                                  ✓ Active
                                </span>
                              )}
                            </div>

                            {/* Page stats */}
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <span className="bg-gray-100 px-2 py-1 rounded-full font-medium">
                                Page
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between mt-2">
                            <span className="text-sm text-gray-600 truncate">
                              @{page.username}
                            </span>

                            {/* Hover action */}
                            {active && (
                              <span className="text-xs text-blue-600 font-medium animate-pulse">
                                Click to select →
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {selected ? (
                        <div className="absolute top-4 right-3">
                          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-full p-1.5 shadow-lg animate-pulse">
                            <CheckIcon className="h-4 w-4 text-white" aria-hidden="true" />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </Listbox.Option>
              ))}

              {/* Dropdown Footer */}
              <div className="px-4 py-3 border-t border-gray-100/50 mt-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    {pages.length} page{pages.length !== 1 ? 's' : ''} available
                  </span>
                  <button className="text-xs text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">
                    Manage Pages →
                  </button>
                </div>
              </div>
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>
    </div>
  );
}
