"use client";

import { useState, useEffect, Fragment } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Menu, Transition } from "@headlessui/react";
import {
  UserGroupIcon,
  UserPlusIcon,
  UsersIcon,
  ChevronDownIcon,
  UserIcon,
  ArrowPathIcon,
  Cog6ToothIcon,
  MagnifyingGlassIcon,
  SparklesIcon
} from "@heroicons/react/24/outline";
import { NotificationBadge } from "@/components/notifications/NotificationBadge";
import { Spinner } from "@/components/ui/Spinner";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { cn } from "@/lib/utils";
import { toast } from "react-hot-toast";
import { formatDistanceToNow } from "date-fns";

interface Subscription {
  id: string;
  targetUserId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
}

interface Subscriber {
  id: string;
  subscriberId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
}

export function SubscriptionsDropdown() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'following' | 'followers'>('following');

  useEffect(() => {
    if (session?.user) {
      fetchData();
    }
  }, [session]);

  const fetchData = async () => {
    if (!session?.user) return;

    try {
      setLoading(true);

      // Fetch subscriptions (following)
      const subscriptionsResponse = await fetch('/api/subscriptions?type=subscriptions&limit=5');
      if (subscriptionsResponse.ok) {
        const subscriptionsData = await subscriptionsResponse.json();
        setSubscriptions(subscriptionsData.subscriptions || []);
      }

      // Fetch subscribers (followers)
      const subscribersResponse = await fetch('/api/subscriptions?type=subscribers&limit=5');
      if (subscribersResponse.ok) {
        const subscribersData = await subscribersResponse.json();
        setSubscribers(subscribersData.subscribers || []);
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const isActive = pathname?.startsWith('/subscriptions') || false;

  return (
    <Menu as="div" className="relative">
      <Menu.Button
        className={cn(
          "flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",
          isActive
            ? "bg-blue-50 text-blue-600"
            : "text-gray-700 hover:bg-gray-100"
        )}
      >
        <UserGroupIcon className="h-5 w-5" />
        <span className="hidden sm:inline">Subscriptions</span>
        <ChevronDownIcon className="h-4 w-4" />
        {subscribers.length > 0 && (
          <NotificationBadge
            count={subscribers.length}
            animate={true}
            size="sm"
            color="blue"
            className="-top-1 -right-1"
          />
        )}
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-150"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-50 mt-2 w-96 origin-top-right rounded-xl bg-white shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden">
          {/* Enhanced Header with Gradient */}
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-white/20 rounded-full p-2">
                  <UserGroupIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">Subscriptions</h3>
                  <p className="text-blue-100 text-sm">
                    {subscribers.length > 0 ? `${subscribers.length} new followers` : 'Manage your connections'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
                  title="Refresh"
                >
                  <ArrowPathIcon className={cn(
                    "h-5 w-5 text-white transition-transform",
                    refreshing && "animate-spin"
                  )} />
                </button>
                <Link
                  href="/subscriptions/settings"
                  className="bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
                  title="Subscription Settings"
                >
                  <Cog6ToothIcon className="h-5 w-5 text-white" />
                </Link>
              </div>
            </div>
          </div>

          {/* Enhanced Quick Actions Grid */}
          <div className="p-4 bg-gray-50">
            <div className="grid grid-cols-2 gap-3">
              <Link
                href="/subscriptions"
                className="group flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105"
              >
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-full p-3 mb-2 group-hover:from-blue-600 group-hover:to-blue-700 transition-colors">
                  <UserGroupIcon className="h-5 w-5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">All Subscriptions</span>
              </Link>

              <Link
                href="/subscriptions?tab=followers"
                className="group flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 relative"
              >
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-full p-3 mb-2 group-hover:from-green-600 group-hover:to-green-700 transition-colors">
                  <UsersIcon className="h-5 w-5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">Followers</span>
                {subscribers.length > 0 && (
                  <NotificationBadge
                    count={subscribers.length}
                    animate={true}
                    size="sm"
                    color="green"
                    className="-top-1 -right-1"
                  />
                )}
              </Link>

              <Link
                href="/subscriptions?tab=following"
                className="group flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105"
              >
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-full p-3 mb-2 group-hover:from-purple-600 group-hover:to-purple-700 transition-colors">
                  <UserPlusIcon className="h-5 w-5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">Following</span>
              </Link>

              <Link
                href="/subscriptions?tab=suggestions"
                className="group flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105"
              >
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-full p-3 mb-2 group-hover:from-orange-600 group-hover:to-orange-700 transition-colors">
                  <SparklesIcon className="h-5 w-5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">Discover</span>
              </Link>
            </div>
          </div>

          {/* Enhanced Filter Tabs */}
          <div className="px-4 py-2 bg-white border-b border-gray-100">
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('following')}
                className={cn(
                  "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                  activeTab === 'following'
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                Following ({subscriptions.length})
              </button>
              <button
                onClick={() => setActiveTab('followers')}
                className={cn(
                  "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                  activeTab === 'followers'
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                Followers ({subscribers.length})
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Spinner size="sm" />
                <span className="ml-2 text-sm text-gray-500">Loading...</span>
              </div>
            ) : (
              <div className="p-4">
                {activeTab === 'following' ? (
                  subscriptions.length === 0 ? (
                    <div className="text-center py-8">
                      <UserPlusIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 mb-3">No subscriptions yet</p>
                      <Link
                        href="/subscriptions?tab=suggestions"
                        className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        <SparklesIcon className="h-3 w-3 mr-1" />
                        Discover People
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {subscriptions.slice(0, 4).map((subscription) => (
                        <Link
                          key={subscription.id}
                          href={`/user/${subscription.username}`}
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                          <div className="relative h-10 w-10 flex-shrink-0">
                            {subscription.image ? (
                              <OptimizedImage
                                src={subscription.image}
                                alt={subscription.name}
                                width={40}
                                height={40}
                                className="rounded-full object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
                                <UserIcon className="h-5 w-5 text-gray-500" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {subscription.name}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              @{subscription.username}
                            </p>
                          </div>
                        </Link>
                      ))}
                      {subscriptions.length > 4 && (
                        <Link
                          href="/subscriptions?tab=following"
                          className="block text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                          View all {subscriptions.length} subscriptions
                        </Link>
                      )}
                    </div>
                  )
                ) : (
                  subscribers.length === 0 ? (
                    <div className="text-center py-8">
                      <UsersIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 mb-3">No followers yet</p>
                      <Link
                        href="/subscriptions?tab=suggestions"
                        className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        <SparklesIcon className="h-3 w-3 mr-1" />
                        Discover People
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {subscribers.slice(0, 4).map((subscriber) => (
                        <Link
                          key={subscriber.id}
                          href={`/user/${subscriber.username}`}
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                          <div className="relative h-10 w-10 flex-shrink-0">
                            {subscriber.image ? (
                              <OptimizedImage
                                src={subscriber.image}
                                alt={subscriber.name}
                                width={40}
                                height={40}
                                className="rounded-full object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full flex items-center justify-center">
                                <UserIcon className="h-5 w-5 text-gray-500" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {subscriber.name}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              @{subscriber.username}
                            </p>
                          </div>
                        </Link>
                      ))}
                      {subscribers.length > 4 && (
                        <Link
                          href="/subscriptions?tab=followers"
                          className="block text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                          View all {subscribers.length} followers
                        </Link>
                      )}
                    </div>
                  )
                )}
              </div>
            )}
          </div>

          {/* Enhanced Footer */}
          <div className="border-t border-gray-100 bg-gray-50 px-4 py-3">
            <div className="flex items-center justify-between">
              <Link
                href="/subscriptions"
                className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors"
              >
                View All Subscriptions
              </Link>
              <Link
                href="/subscriptions?tab=suggestions"
                className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
              >
                <MagnifyingGlassIcon className="h-3 w-3 mr-1" />
                Find People
              </Link>
            </div>
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
