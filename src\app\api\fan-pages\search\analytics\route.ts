import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Simple in-memory storage for search analytics (in production, use a database)
const searchAnalytics = new Map<string, { count: number; lastSearched: Date }>();

// POST /api/fan-pages/search/analytics - Track search queries
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { query, category, filters } = await request.json();

    if (!query?.trim()) {
      return NextResponse.json({ success: false });
    }

    const searchKey = query.toLowerCase().trim();
    const existing = searchAnalytics.get(searchKey);
    
    if (existing) {
      existing.count += 1;
      existing.lastSearched = new Date();
    } else {
      searchAnalytics.set(searchKey, {
        count: 1,
        lastSearched: new Date()
      });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error("Error tracking search analytics:", error);
    return NextResponse.json({ success: false });
  }
}

// GET /api/fan-pages/search/analytics - Get popular search terms (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // In a real app, check if user is admin
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");

    // Convert map to array and sort by count
    const popularSearches = Array.from(searchAnalytics.entries())
      .map(([query, data]) => ({ query, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);

    return NextResponse.json({
      popularSearches,
      totalQueries: searchAnalytics.size
    });

  } catch (error) {
    console.error("Error fetching search analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics" },
      { status: 500 }
    );
  }
}
