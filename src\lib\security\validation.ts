import Joi from 'joi';

/**
 * Common validation schemas
 */
export const validationSchemas = {
  // User validation
  user: {
    email: Joi.string().email().max(255).required(),
    password: Joi.string().min(8).max(128).pattern(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    ).required().messages({
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    }),
    name: Joi.string().min(2).max(100).pattern(/^[a-zA-Z\s]+$/).required(),
    username: Joi.string().min(3).max(30).pattern(/^[a-zA-Z0-9_]+$/).required(),
    bio: Joi.string().max(500).allow(''),
    website: Joi.string().uri().max(255).allow(''),
    location: Joi.string().max(100).allow(''),
  },

  // Post validation
  post: {
    content: Joi.string().min(1).max(5000).required(),
    privacy: Joi.string().valid('public', 'friends', 'private').default('public'),
    groupId: Joi.string().uuid().allow(null),
    images: Joi.array().items(Joi.string().uri()).max(10),
    tags: Joi.array().items(Joi.string().max(50)).max(20),
  },

  // Comment validation
  comment: {
    content: Joi.string().min(1).max(1000).required(),
    postId: Joi.string().uuid().required(),
    parentId: Joi.string().uuid().allow(null),
  },

  // Group validation
  group: {
    name: Joi.string().min(3).max(100).required(),
    description: Joi.string().max(1000).allow(''),
    privacy: Joi.string().valid('public', 'private').default('public'),
    rules: Joi.string().max(2000).allow(''),
    category: Joi.string().max(50).allow(''),
  },

  // Blog validation
  blog: {
    title: Joi.string().min(5).max(200).required(),
    content: Joi.string().min(100).max(50000).required(),
    excerpt: Joi.string().max(500).allow(''),
    tags: Joi.array().items(Joi.string().max(50)).max(10),
    category: Joi.string().max(50).allow(''),
    status: Joi.string().valid('draft', 'published', 'archived').default('draft'),
    featuredImage: Joi.string().uri().allow(''),
    seoTitle: Joi.string().max(60).allow(''),
    seoDescription: Joi.string().max(160).allow(''),
  },

  // Subscription validation
  subscription: {
    planId: Joi.string().uuid().required(),
    paymentMethod: Joi.string().valid('stripe', 'paypal', 'wallet').required(),
    billingCycle: Joi.string().valid('monthly', 'yearly').default('monthly'),
  },

  // Wallet validation
  wallet: {
    amount: Joi.number().positive().precision(2).max(10000).required(),
    type: Joi.string().valid('deposit', 'withdrawal', 'transfer').required(),
    description: Joi.string().max(255).allow(''),
  },

  // Admin validation
  admin: {
    userId: Joi.string().uuid().required(),
    action: Joi.string().valid('ban', 'unban', 'promote', 'demote', 'delete').required(),
    reason: Joi.string().max(500).allow(''),
    duration: Joi.number().positive().allow(null),
  },

  // File upload validation
  upload: {
    fileType: Joi.string().valid('image', 'video', 'document').required(),
    fileName: Joi.string().max(255).pattern(/^[a-zA-Z0-9._-]+$/).required(),
    fileSize: Joi.number().positive().max(50 * 1024 * 1024), // 50MB max
  },

  // Search validation
  search: {
    query: Joi.string().min(1).max(100).required(),
    type: Joi.string().valid('posts', 'users', 'groups', 'blogs').default('posts'),
    limit: Joi.number().integer().min(1).max(50).default(20),
    offset: Joi.number().integer().min(0).default(0),
  },

  // Pagination validation
  pagination: {
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().max(50).default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  },
};

/**
 * Sanitize input to prevent XSS attacks
 */
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/vbscript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
}

/**
 * Validate request body against schema
 */
export function validateRequestBody(body: any, schema: Joi.ObjectSchema): {
  isValid: boolean;
  data?: any;
  errors?: string[];
} {
  try {
    const { error, value } = schema.validate(body, {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });
    
    if (error) {
      const errors = error.details.map(detail => detail.message);
      return { isValid: false, errors };
    }
    
    // Sanitize the validated data
    const sanitizedData = sanitizeInput(value);
    
    return { isValid: true, data: sanitizedData };
  } catch (err) {
    return { 
      isValid: false, 
      errors: ['Validation failed: Invalid input format'] 
    };
  }
}

/**
 * Validate query parameters
 */
export function validateQueryParams(params: any, schema: Joi.ObjectSchema): {
  isValid: boolean;
  data?: any;
  errors?: string[];
} {
  return validateRequestBody(params, schema);
}

/**
 * Create validation middleware for API routes
 */
export function createValidationMiddleware(schema: Joi.ObjectSchema) {
  return (body: any) => {
    const result = validateRequestBody(body, schema);
    if (!result.isValid) {
      throw new Error(`Validation failed: ${result.errors?.join(', ')}`);
    }
    return result.data;
  };
}

/**
 * Common validation patterns
 */
export const validationPatterns = {
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  username: /^[a-zA-Z0-9_]{3,30}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  phone: /^\+?[\d\s\-\(\)]{10,}$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
};

/**
 * File validation
 */
export const fileValidation = {
  images: {
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxSize: 5 * 1024 * 1024, // 5MB
    maxDimensions: { width: 4096, height: 4096 },
  },
  videos: {
    allowedTypes: ['video/mp4', 'video/webm', 'video/ogg'],
    maxSize: 100 * 1024 * 1024, // 100MB
    maxDuration: 600, // 10 minutes
  },
  documents: {
    allowedTypes: ['application/pdf', 'text/plain', 'application/msword'],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
};

/**
 * Validate file upload
 */
export function validateFile(file: File, type: 'images' | 'videos' | 'documents'): {
  isValid: boolean;
  errors?: string[];
} {
  const config = fileValidation[type];
  const errors: string[] = [];
  
  // Check file type
  if (!config.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }
  
  // Check file size
  if (file.size > config.maxSize) {
    errors.push(`File size exceeds maximum allowed size of ${config.maxSize / 1024 / 1024}MB`);
  }
  
  // Check file name
  if (!/^[a-zA-Z0-9._-]+$/.test(file.name)) {
    errors.push('File name contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined,
  };
}

/**
 * Rate limiting validation
 */
export function validateRateLimit(identifier: string, limit: number, windowMs: number): boolean {
  // This would typically use Redis in production
  // For now, using in-memory storage
  const key = `rate_limit:${identifier}`;
  const now = Date.now();
  
  // Implementation would go here
  return true; // Placeholder
}

/**
 * SQL injection detection
 * Improved to avoid false positives with legitimate content
 */
export function detectSQLInjection(input: string): boolean {
  const sqlPatterns = [
    // SQL keywords in suspicious contexts (with operators/spaces around them)
    /[\s\+\-\*\/\(\)\=\<\>\!\&\|]+(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)[\s\+\-\*\/\(\)\=\<\>\!\&\|]+/gi,
    // SQL comment patterns
    /(--|\/\*|\*\/)/g,
    // Classic SQL injection patterns
    /(\b(OR|AND)\b\s+\d+\s*=\s*\d+)/gi,
    /(\b(OR|AND)\b\s+['"]\w+['"]?\s*=\s*['"]\w+['"]?)/gi,
    // Union-based injection
    /union[\s\+]+select/gi,
    // Time-based injection patterns
    /(sleep|waitfor|delay)\s*\(/gi,
  ];

  return sqlPatterns.some(pattern => pattern.test(input));
}

/**
 * XSS detection
 */
export function detectXSS(input: string): boolean {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /on\w+\s*=/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
  ];
  
  return xssPatterns.some(pattern => pattern.test(input));
}
