import { useState, useCallback, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'react-hot-toast';

interface UseFanPageFollowProps {
  pageId: string;
  pageName: string;
  initialIsFollowing: boolean;
  initialFollowerCount: number;
  onUpdate?: (data: { isFollowing: boolean; followerCount: number }) => void;
  useQuickApi?: boolean; // Use faster API endpoint
}

export function useFanPageFollow({
  pageId,
  pageName,
  initialIsFollowing,
  initialFollowerCount,
  onUpdate,
  useQuickApi = false,
}: UseFanPageFollowProps) {
  const { data: session } = useSession();
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing);
  const [followerCount, setFollowerCount] = useState(initialFollowerCount);
  const [isLoading, setIsLoading] = useState(false);
  const requestRef = useRef<AbortController | null>(null);

  const toggleFollow = useCallback(async () => {
    if (!session) {
      toast.error("Please sign in to follow pages");
      return;
    }

    if (isLoading) {
      return; // Prevent multiple simultaneous requests
    }

    // Cancel any pending request
    if (requestRef.current) {
      requestRef.current.abort();
    }

    // Create new abort controller
    requestRef.current = new AbortController();

    // Store original values for rollback
    const originalIsFollowing = isFollowing;
    const originalFollowerCount = followerCount;

    // Optimistic update - Update UI immediately
    const newIsFollowing = !isFollowing;
    const newFollowerCount = newIsFollowing
      ? followerCount + 1
      : Math.max(0, followerCount - 1);

    setIsFollowing(newIsFollowing);
    setFollowerCount(newFollowerCount);
    setIsLoading(true);

    try {
      let response;
      let result;

      if (useQuickApi) {
        // Use quick API for faster response
        response = await fetch(`/api/fan-pages/${pageId}/follow-quick`, {
          method: 'POST',
          signal: requestRef.current.signal,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: originalIsFollowing ? 'unfollow' : 'follow'
          }),
        });
      } else {
        // Use original API
        const method = originalIsFollowing ? "DELETE" : "POST";
        response = await fetch(`/api/fan-pages/${pageId}/follow`, {
          method,
          signal: requestRef.current.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      result = await response.json();

      if (response.ok) {
        // Success - show toast
        toast.success(
          newIsFollowing
            ? `You are now following ${pageName}!`
            : `You unfollowed ${pageName}`,
          {
            duration: 2000,
            icon: newIsFollowing ? '🎉' : '👋',
            style: {
              background: newIsFollowing ? '#10B981' : '#6B7280',
              color: '#fff',
            },
          }
        );

        // Update with server response
        const serverIsFollowing = useQuickApi ? result.isFollowing : result.isFollowing;
        const serverFollowerCount = useQuickApi ? result.followerCount : newFollowerCount;

        setIsFollowing(serverIsFollowing);
        setFollowerCount(serverFollowerCount);

        // Call onUpdate if provided
        if (onUpdate) {
          onUpdate({
            isFollowing: serverIsFollowing,
            followerCount: serverFollowerCount
          });
        }
      } else {
        throw new Error(result.error || "Failed to update follow status");
      }
    } catch (error: any) {
      // Rollback optimistic update on error
      setIsFollowing(originalIsFollowing);
      setFollowerCount(originalFollowerCount);

      if (error.name !== 'AbortError') {
        console.error("Error toggling follow:", error);
        toast.error(
          error.message ||
          `Failed to ${originalIsFollowing ? 'unfollow' : 'follow'} page. Please try again.`,
          {
            duration: 4000,
          }
        );
      }
    } finally {
      setIsLoading(false);
      requestRef.current = null;
    }
  }, [session, isFollowing, followerCount, isLoading, pageId, pageName, onUpdate, useQuickApi]);

  // Cleanup on unmount - abort any pending requests
  useEffect(() => {
    return () => {
      if (requestRef.current) {
        requestRef.current.abort();
        requestRef.current = null;
      }
    };
  }, []);

  return {
    isFollowing,
    followerCount,
    isLoading,
    toggleFollow,
  };
}
