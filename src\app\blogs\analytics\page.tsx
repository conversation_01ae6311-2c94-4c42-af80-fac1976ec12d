"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import { Navbar } from "@/components/layout/Navbar";
import { MobileNavbar } from "@/components/layout/MobileNavbar";
import {
  ChartBarIcon,
  EyeIcon,
  DocumentTextIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ArrowLeftIcon,
  CalendarIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface BlogAnalytics {
  id: string;
  title: string;
  slug: string;
  viewCount: number;
  totalViews: number;
  uniqueViews: number;
  todayViews: number;
  weeklyViews: number;
  monthlyViews: number;
  monetization?: {
    status: 'pending' | 'approved' | 'rejected';
    totalEarnings: string;
    uniqueReads: number;
    cprRate: string;
  };
  deviceBreakdown: Array<{
    device: string;
    count: number;
  }>;
  topCountries: Array<{
    country: string;
    count: number;
  }>;
  createdAt: string;
}

interface AnalyticsOverview {
  totalBlogs: number;
  totalViews: number;
  totalUniqueViews: number;
  totalEarnings: string;
  averageViewsPerBlog: number;
  topPerformingBlog: {
    title: string;
    slug: string;
    views: number;
  } | null;
}

export default function BlogAnalyticsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [analytics, setAnalytics] = useState<BlogAnalytics[]>([]);
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === "loading") return; // Still loading session

    if (status === "unauthenticated" || !session?.user) {
      router.push('/auth/signin');
      return;
    }

    fetchAnalytics();
  }, [session, status, router]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch blog analytics data
      const response = await fetch('/api/author/blogs-analytics');
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const data = await response.json();
      
      if (data.success) {
        setAnalytics(data.blogs || []);
        setOverview(data.overview || null);
      } else {
        throw new Error(data.message || 'Failed to load analytics');
      }
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case 'mobile':
        return <DevicePhoneMobileIcon className="h-4 w-4" />;
      case 'tablet':
        return <DeviceTabletIcon className="h-4 w-4" />;
      case 'desktop':
      default:
        return <ComputerDesktopIcon className="h-4 w-4" />;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Show loading while session is being loaded
  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <ChartBarIcon className="h-12 w-12 mx-auto" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Analytics</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={fetchAnalytics}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Desktop Navbar */}
      <div className="hidden sm:block">
        <Navbar />
      </div>
      {/* Mobile Navbar */}
      <MobileNavbar />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-20 sm:pt-24">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Blog Analytics</h1>
              <p className="text-gray-600 mt-1">Comprehensive insights into your blog performance</p>
            </div>
          </div>
        </div>

        {/* Overview Cards */}
        {overview && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DocumentTextIcon className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Blogs</p>
                    <p className="text-2xl font-bold text-gray-900">{overview.totalBlogs}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <EyeIcon className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Views</p>
                    <p className="text-2xl font-bold text-gray-900">{formatNumber(overview.totalViews)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <ArrowTrendingUpIcon className="h-8 w-8 text-purple-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Unique Views</p>
                    <p className="text-2xl font-bold text-gray-900">{formatNumber(overview.totalUniqueViews)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CurrencyDollarIcon className="h-8 w-8 text-yellow-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                    <p className="text-2xl font-bold text-gray-900">${parseFloat(overview.totalEarnings).toFixed(2)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Top Performing Blog */}
        {overview?.topPerformingBlog && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowTrendingUpIcon className="h-5 w-5" />
                Top Performing Blog
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {overview.topPerformingBlog.title}
                  </h3>
                  <p className="text-gray-600">
                    {formatNumber(overview.topPerformingBlog.views)} views
                  </p>
                </div>
                <Link
                  href={`/blogs/${overview.topPerformingBlog.slug}`}
                  className="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-transparent hover:bg-gray-100 h-10 px-4 py-2"
                >
                  View Blog
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Individual Blog Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ChartBarIcon className="h-5 w-5" />
              Individual Blog Performance
            </CardTitle>
            <CardDescription>
              Detailed analytics for each of your blog posts
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analytics.length === 0 ? (
              <div className="text-center py-8">
                <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Blog Analytics Available
                </h3>
                <p className="text-gray-600 mb-4">
                  Create your first blog post to start tracking analytics.
                </p>
                <Link
                  href="/blogs/create"
                  className="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2"
                >
                  Create Your First Blog
                </Link>
              </div>
            ) : (
              <div className="space-y-6">
                {analytics.map((blog) => (
                  <div key={blog.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {blog.title}
                        </h3>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span className="flex items-center gap-1">
                            <CalendarIcon className="h-4 w-4" />
                            {new Date(blog.createdAt).toLocaleDateString()}
                          </span>
                          {blog.monetization && (
                            <Badge 
                              variant={blog.monetization.status === 'approved' ? 'success' : 
                                     blog.monetization.status === 'pending' ? 'warning' : 'destructive'}
                            >
                              {blog.monetization.status}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <Link
                        href={`/blogs/${blog.slug}`}
                        className="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-transparent hover:bg-gray-100 h-8 px-3 text-xs"
                      >
                        View Blog
                      </Link>
                    </div>

                    {/* Analytics Grid */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-lg font-semibold text-blue-600">
                          {formatNumber(blog.totalViews)}
                        </div>
                        <div className="text-xs text-blue-700">Total Views</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-lg font-semibold text-green-600">
                          {formatNumber(blog.uniqueViews)}
                        </div>
                        <div className="text-xs text-green-700">Unique Views</div>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <div className="text-lg font-semibold text-purple-600">
                          {formatNumber(blog.weeklyViews)}
                        </div>
                        <div className="text-xs text-purple-700">This Week</div>
                      </div>
                      <div className="text-center p-3 bg-orange-50 rounded-lg">
                        <div className="text-lg font-semibold text-orange-600">
                          {formatNumber(blog.monthlyViews)}
                        </div>
                        <div className="text-xs text-orange-700">This Month</div>
                      </div>
                    </div>

                    {/* Monetization Info */}
                    {blog.monetization && blog.monetization.status === 'approved' && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                        <div className="text-center p-3 bg-yellow-50 rounded-lg">
                          <div className="text-lg font-semibold text-yellow-600">
                            ${parseFloat(blog.monetization.totalEarnings).toFixed(2)}
                          </div>
                          <div className="text-xs text-yellow-700">Earnings</div>
                        </div>
                        <div className="text-center p-3 bg-indigo-50 rounded-lg">
                          <div className="text-lg font-semibold text-indigo-600">
                            {blog.monetization.uniqueReads}
                          </div>
                          <div className="text-xs text-indigo-700">Qualified Reads</div>
                        </div>
                        <div className="text-center p-3 bg-pink-50 rounded-lg">
                          <div className="text-lg font-semibold text-pink-600">
                            ${blog.monetization.cprRate}
                          </div>
                          <div className="text-xs text-pink-700">CPR Rate</div>
                        </div>
                      </div>
                    )}

                    {/* Device Breakdown */}
                    {blog.deviceBreakdown && blog.deviceBreakdown.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Device Breakdown</h4>
                        <div className="flex gap-2">
                          {blog.deviceBreakdown.map((device, index) => (
                            <div key={index} className="flex items-center gap-1 text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
                              {getDeviceIcon(device.device)}
                              <span className="capitalize">{device.device}</span>
                              <span className="font-medium">{device.count}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
