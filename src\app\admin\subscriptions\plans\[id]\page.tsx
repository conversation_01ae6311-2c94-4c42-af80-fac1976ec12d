"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { PlanBadge } from "@/components/ui/PlanBadge";
import { toast } from "react-hot-toast";
import {
  PencilIcon,
  TrashIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon,
  UsersIcon,
  DocumentTextIcon,
  PhotoIcon,
  ServerIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string | null;
  price: string;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  features: string[];
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
  // Badge system fields
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor: string;
  customBadgeUrl: string | null;
  badgePriority: number;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export default function ViewSubscriptionPlanPage() {
  const params = useParams();
  const router = useRouter();
  const planId = params.id as string;
  
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (planId) {
      fetchPlan();
    }
  }, [planId]);

  const fetchPlan = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch subscription plan');
      }
      const data = await response.json();
      setPlan(data.plan);
    } catch (error) {
      console.error('Error fetching plan:', error);
      toast.error('Failed to load subscription plan');
      router.push('/admin/subscriptions/plans');
    } finally {
      setIsLoading(false);
    }
  };

  const togglePlanStatus = async () => {
    if (!plan) return;
    
    try {
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !plan.isActive,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update plan status');
      }

      await fetchPlan();
      toast.success(`Plan ${!plan.isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Error updating plan status:', error);
      toast.error('Failed to update plan status');
    }
  };

  const deletePlan = async () => {
    if (!plan) return;
    
    if (!confirm('Are you sure you want to delete this subscription plan? This action cannot be undone.')) {
      return;
    }

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete plan');
      }

      toast.success('Plan deleted successfully');
      router.push('/admin/subscriptions/plans');
    } catch (error) {
      console.error('Error deleting plan:', error);
      toast.error('Failed to delete plan');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatPrice = (price: string, currency: string, billingCycle: string) => {
    const amount = parseFloat(price);
    return `${currency} ${amount.toFixed(2)}/${billingCycle === 'yearly' ? 'year' : 'month'}`;
  };

  const formatLimit = (value: number, unit: string) => {
    if (value === -1) return 'Unlimited';
    return `${value} ${unit}`;
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!plan) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Plan not found</h2>
          <p className="mt-2 text-gray-600">The subscription plan you're looking for doesn't exist.</p>
          <Link href="/admin/subscriptions/plans" className="mt-4 inline-block">
            <Button>
              <ArrowLeftIcon className="mr-2 h-5 w-5" />
              Back to Plans
            </Button>
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6">
        <div className="flex items-center space-x-4 mb-4">
          <Link href="/admin/subscriptions/plans">
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Back to Plans
            </Button>
          </Link>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-3">
            <h1 className="text-2xl font-bold text-gray-900">{plan.displayName}</h1>
            <PlanBadge
              badgeType={plan.badgeType}
              badgeColor={plan.badgeColor}
              customBadgeUrl={plan.customBadgeUrl}
              size="md"
            />
            <Badge
              variant={plan.isActive ? "success" : "secondary"}
              className="text-sm"
            >
              {plan.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <Link href={`/admin/subscriptions/plans/${planId}/edit`}>
              <Button>
                <PencilIcon className="mr-2 h-5 w-5" />
                Edit Plan
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={togglePlanStatus}
              className={plan.isActive ? "text-red-600 hover:text-red-700" : "text-green-600 hover:text-green-700"}
            >
              {plan.isActive ? (
                <XCircleIcon className="mr-2 h-5 w-5" />
              ) : (
                <CheckCircleIcon className="mr-2 h-5 w-5" />
              )}
              {plan.isActive ? "Deactivate" : "Activate"}
            </Button>
            <Button
              variant="outline"
              onClick={deletePlan}
              disabled={isDeleting}
              className="text-red-600 hover:text-red-700"
            >
              {isDeleting ? (
                <Spinner size="sm" />
              ) : (
                <TrashIcon className="mr-2 h-5 w-5" />
              )}
              Delete
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Plan Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Plan Name</label>
                <p className="mt-1 text-sm text-gray-900">{plan.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Display Name</label>
                <p className="mt-1 text-sm text-gray-900">{plan.displayName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Price</label>
                <p className="mt-1 text-lg font-semibold text-blue-600">
                  {formatPrice(plan.price, plan.currency, plan.billingCycle)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Billing Cycle</label>
                <p className="mt-1 text-sm text-gray-900 capitalize">{plan.billingCycle}</p>
              </div>
            </div>
            {plan.description && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <p className="mt-1 text-sm text-gray-900">{plan.description}</p>
              </div>
            )}
          </Card>

          {/* Features */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Features</h2>
            {plan.features && plan.features.length > 0 ? (
              <ul className="space-y-2">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-700">
                    <CheckCircleIcon className="mr-2 h-4 w-4 text-green-500" />
                    {feature}
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500">No features listed</p>
            )}
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Limits & Permissions */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Limits & Permissions</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <DocumentTextIcon className="mr-2 h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-700">Posts</span>
                </div>
                <span className="text-sm font-medium">{formatLimit(plan.maxPosts, 'posts')}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <ServerIcon className="mr-2 h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-700">Storage</span>
                </div>
                <span className="text-sm font-medium">{formatLimit(plan.maxStorage, 'MB')}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <UserGroupIcon className="mr-2 h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-700">Groups</span>
                </div>
                <span className="text-sm font-medium">{formatLimit(plan.maxGroups, 'groups')}</span>
              </div>
            </div>
            
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Permissions</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Fan Pages</span>
                  <Badge variant={plan.canCreateFanPages ? "success" : "secondary"} className="text-xs">
                    {plan.canCreateFanPages ? "Allowed" : "Not Allowed"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Stores</span>
                  <Badge variant={plan.canCreateStores ? "success" : "secondary"} className="text-xs">
                    {plan.canCreateStores ? "Allowed" : "Not Allowed"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Blog Monetization</span>
                  <Badge variant={plan.canMonetizeBlogs ? "success" : "secondary"} className="text-xs">
                    {plan.canMonetizeBlogs ? "Allowed" : "Not Allowed"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Priority Support</span>
                  <Badge variant={plan.prioritySupport ? "success" : "secondary"} className="text-xs">
                    {plan.prioritySupport ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
            </div>
          </Card>

          {/* Badge Settings */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Badge Settings</h2>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Badge Type</label>
                <div className="mt-1 flex items-center space-x-2">
                  <PlanBadge
                    badgeType={plan.badgeType}
                    badgeColor={plan.badgeColor}
                    customBadgeUrl={plan.customBadgeUrl}
                    size="md"
                  />
                  <span className="text-sm text-gray-900 capitalize">{plan.badgeType}</span>
                </div>
              </div>
              {plan.badgeType !== 'none' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Badge Color</label>
                    <div className="mt-1 flex items-center space-x-2">
                      <div 
                        className="w-4 h-4 rounded border border-gray-300"
                        style={{ backgroundColor: plan.badgeColor }}
                      />
                      <span className="text-sm text-gray-900">{plan.badgeColor}</span>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Badge Priority</label>
                    <p className="mt-1 text-sm text-gray-900">{plan.badgePriority}</p>
                  </div>
                  {plan.badgeType === 'custom' && plan.customBadgeUrl && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Custom Badge URL</label>
                      <p className="mt-1 text-sm text-gray-900 break-all">{plan.customBadgeUrl}</p>
                    </div>
                  )}
                </>
              )}
            </div>
          </Card>

          {/* Meta Information */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Meta Information</h2>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Sort Order</label>
                <p className="mt-1 text-sm text-gray-900">{plan.sortOrder}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Created At</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(plan.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(plan.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
