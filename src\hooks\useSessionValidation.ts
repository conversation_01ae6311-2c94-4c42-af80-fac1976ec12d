import { useSession, signOut } from "next-auth/react";
import { useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";

interface UseSessionValidationOptions {
  checkInterval?: number; // in milliseconds
  redirectOnInvalid?: boolean;
  showToast?: boolean;
}

export function useSessionValidation(options: UseSessionValidationOptions = {}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const {
    checkInterval = 30000, // 30 seconds
    redirectOnInvalid = true,
    showToast = true
  } = options;

  const checkSessionStatus = useCallback(async () => {
    if (!session?.user?.id || status !== "authenticated") {
      return;
    }

    try {
      const response = await fetch('/api/auth/session-status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          const data = await response.json();
          
          if (data.code === 'ACCOUNT_DISABLED') {
            if (showToast) {
              toast.error(data.message || "Your account has been disabled or suspended");
            }
            
            // Sign out and redirect
            await signOut({ redirect: false });
            
            if (redirectOnInvalid) {
              router.push('/login?error=account_disabled');
            }
            
            return false;
          }
        }
      }
      
      return true;
    } catch (error) {
      console.error('Session validation error:', error);
      return true; // Don't invalidate on network errors
    }
  }, [session?.user?.id, status, router, redirectOnInvalid, showToast]);

  useEffect(() => {
    if (status === "authenticated" && session?.user?.id) {
      // Initial check
      checkSessionStatus();
      
      // Set up interval checking
      const interval = setInterval(checkSessionStatus, checkInterval);
      
      return () => clearInterval(interval);
    }
  }, [status, session?.user?.id, checkSessionStatus, checkInterval]);

  return {
    isValid: status === "authenticated" && session?.user?.id,
    checkSessionStatus
  };
}

// Hook specifically for admin pages
export function useAdminSessionValidation(options: UseSessionValidationOptions = {}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const {
    checkInterval = 30000,
    redirectOnInvalid = true,
    showToast = true
  } = options;

  const checkAdminSessionStatus = useCallback(async () => {
    if (!session?.user?.id || status !== "authenticated" || !session.user.isAdmin) {
      return false;
    }

    try {
      const response = await fetch('/api/auth/session-status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          const data = await response.json();
          
          if (data.code === 'ACCOUNT_DISABLED') {
            if (showToast) {
              toast.error(data.message || "Your admin account has been disabled or suspended");
            }
            
            // Sign out and redirect to admin login
            await signOut({ redirect: false });
            
            if (redirectOnInvalid) {
              router.push('/admin/login?error=account_disabled');
            }
            
            return false;
          }
        }
      }
      
      return true;
    } catch (error) {
      console.error('Admin session validation error:', error);
      return true; // Don't invalidate on network errors
    }
  }, [session?.user?.id, session?.user?.isAdmin, status, router, redirectOnInvalid, showToast]);

  useEffect(() => {
    if (status === "authenticated" && session?.user?.id && session.user.isAdmin) {
      // Initial check
      checkAdminSessionStatus();
      
      // Set up interval checking
      const interval = setInterval(checkAdminSessionStatus, checkInterval);
      
      return () => clearInterval(interval);
    }
  }, [status, session?.user?.id, session?.user?.isAdmin, checkAdminSessionStatus, checkInterval]);

  return {
    isValid: status === "authenticated" && session?.user?.id && session.user.isAdmin,
    checkAdminSessionStatus
  };
}
