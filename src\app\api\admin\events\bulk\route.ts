import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventAttendees } from "@/lib/db/schema";
import { z } from "zod";
import { eq, inArray } from "drizzle-orm";

const bulkDeleteSchema = z.object({
  eventIds: z.array(z.string()),
});

// Bulk delete events
export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = bulkDeleteSchema.parse(body);

    if (validatedData.eventIds.length === 0) {
      return NextResponse.json(
        { message: "No event IDs provided" },
        { status: 400 }
      );
    }

    // Delete all attendees first (to maintain referential integrity)
    await db.delete(eventAttendees)
      .where(inArray(eventAttendees.eventId, validatedData.eventIds));

    // Delete the events
    const result = await db.delete(events)
      .where(inArray(events.id, validatedData.eventIds));

    return NextResponse.json({
      message: `${validatedData.eventIds.length} events deleted successfully`,
      count: validatedData.eventIds.length,
    });
  } catch (error) {
    console.error("Error bulk deleting events:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: "Failed to delete events" },
      { status: 500 }
    );
  }
}
