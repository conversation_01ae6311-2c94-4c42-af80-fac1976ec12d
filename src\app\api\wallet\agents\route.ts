import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const agents = await WalletService.getActiveAgents();

    // Remove sensitive data before sending to client
    const sanitizedAgents = agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      phone: agent.phone,
      location: agent.location,
      serviceType: agent.serviceType,
      commission: agent.commission,
      rating: agent.rating,
      totalTransactions: agent.totalTransactions,
      dailyLimit: agent.dailyLimit,
      currentDailyAmount: agent.currentDailyAmount,
    }));

    return NextResponse.json({
      success: true,
      data: sanitizedAgents,
    });
  } catch (error: any) {
    console.error("Error fetching agents:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch agents" 
      },
      { status: 500 }
    );
  }
}
