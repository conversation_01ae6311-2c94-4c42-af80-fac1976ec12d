"use client";

import { useState, useEffect, Suspense } from "react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { LockClosedIcon } from "@heroicons/react/24/outline";
import { Spinner } from "@/components/ui/Spinner";

// Component that uses useSearchParams
function AdminLoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get("callbackUrl") || "/admin/dashboard";
  const errorParam = searchParams?.get("error");

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Handle URL error parameters
  useEffect(() => {
    if (errorParam === 'account_disabled') {
      setError("Your account has been disabled or suspended. Please contact support.");
    }
  }, [errorParam]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError("Please enter both email and password");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      console.log("Attempting admin login with:", { email, isAdmin: true });

      const result = await signIn("credentials", {
        redirect: false,
        emailOrUsername: email,
        password,
        isAdmin: "true", // This flags the login as an admin login attempt
      });

      console.log("Admin login result:", result);

      if (result?.error) {
        if (result.error === 'CredentialsSignin') {
          setError("Invalid email or password, account disabled/suspended, or you don't have admin access");
        } else {
          setError("Login failed. Please check your credentials and account status.");
        }
        setIsLoading(false);
        return;
      }

      // Wait a moment for the session to be updated
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check session status
      try {
        const sessionResponse = await fetch('/api/debug/session');
        const sessionData = await sessionResponse.json();
        console.log('Session check after login:', sessionData);

        if (!sessionData.isAdmin) {
          console.log('Warning: User logged in but isAdmin flag is not set in session');
          setError("Login successful but admin access not granted. Please contact support.");
          setIsLoading(false);
          return;
        }
      } catch (error) {
        console.error('Error checking session:', error);
      }

      // Redirect to callback URL or dashboard
      console.log("Redirecting to:", callbackUrl);
      router.push(callbackUrl);
    } catch (error) {
      console.error("Login error:", error);
      setError("An error occurred during login");
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col justify-center bg-gray-100 py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Image
            src="/logo.png"
            alt="HIFNF Logo"
            width={120}
            height={40}
            priority
            className="h-12 w-auto"
          />
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Admin Login
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
          {error && (
            <div className="mb-4 rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">{error}</h3>
                </div>
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Email or Username
              </label>
              <div className="mt-1">
                <Input
                  id="email"
                  name="email"
                  type="text"
                  autoComplete="username"
                  required
                  placeholder="Enter your email or username"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                Password
              </label>
              <div className="mt-1">
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>

            <div>
              <Button
                type="submit"
                className="w-full flex justify-center"
                isLoading={isLoading}
              >
                <LockClosedIcon className="mr-2 h-5 w-5" />
                Sign in
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function AdminLoginPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen flex-col justify-center bg-gray-100 py-12 sm:px-6 lg:px-8">
        <div className="flex justify-center">
          <Spinner size="lg" />
        </div>
      </div>
    }>
      <AdminLoginContent />
    </Suspense>
  );
}
