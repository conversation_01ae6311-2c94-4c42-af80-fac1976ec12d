"use client";

import { useState, useEffect, Fragment } from "react";
import { Dialog, Transition, Tab } from "@headlessui/react";
import { XMarkIcon, HandThumbUpIcon, HandThumbDownIcon } from "@heroicons/react/24/outline";
import { HandThumbUpIcon as HandThumbUpSolidIcon, HandThumbDownIcon as HandThumbDownSolidIcon } from "@heroicons/react/24/solid";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface User {
  id: string;
  name: string;
  username?: string | null;
  image?: string | null;
}

interface Reaction {
  id: string;
  type: 'like' | 'dislike';
  createdAt: string;
  user: User;
}

interface ReactionsData {
  likes: Reaction[];
  dislikes: Reaction[];
  total: number;
}

interface ReactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  postId: string;
}

export function ReactionModal({ isOpen, onClose, postId }: ReactionModalProps) {
  const [reactions, setReactions] = useState<ReactionsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      const fetchReactions = async () => {
        try {
          setLoading(true);
          const response = await fetch(`/api/posts/${postId}/reactions`);
          
          if (!response.ok) {
            throw new Error('Failed to fetch reactions');
          }

          const data = await response.json();
          setReactions(data.reactions);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Unknown error');
        } finally {
          setLoading(false);
        }
      };

      fetchReactions();
    }
  }, [isOpen, postId]);

  const UserItem = ({ reaction }: { reaction: Reaction }) => (
    <Link 
      href={`/user/${reaction.user.username || reaction.user.id}`}
      className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors duration-200"
      onClick={onClose}
    >
      {reaction.user.image ? (
        <Image
          src={reaction.user.image}
          alt={reaction.user.name}
          width={40}
          height={40}
          className="h-10 w-10 rounded-full"
        />
      ) : (
        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
          <span className="text-sm font-medium text-gray-600">
            {reaction.user.name.charAt(0).toUpperCase()}
          </span>
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {reaction.user.name}
        </p>
        {reaction.user.username && (
          <p className="text-sm text-gray-500 truncate">
            @{reaction.user.username}
          </p>
        )}
      </div>
      
      <div className={cn(
        "h-6 w-6 rounded-full flex items-center justify-center",
        reaction.type === 'like' ? "bg-blue-500" : "bg-red-500"
      )}>
        {reaction.type === 'like' ? (
          <HandThumbUpSolidIcon className="h-3 w-3 text-white" />
        ) : (
          <HandThumbDownSolidIcon className="h-3 w-3 text-white" />
        )}
      </div>
    </Link>
  );

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-4">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    Reactions
                  </Dialog.Title>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  </div>
                ) : error ? (
                  <div className="text-center py-8 text-red-500">
                    Error loading reactions
                  </div>
                ) : reactions && reactions.total > 0 ? (
                  <Tab.Group>
                    <Tab.List className="flex space-x-1 rounded-xl bg-gray-100 p-1 mb-4">
                      <Tab
                        className={({ selected }) =>
                          cn(
                            'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                            'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                            selected
                              ? 'bg-white shadow text-blue-700'
                              : 'text-gray-700 hover:bg-white/[0.12] hover:text-blue-600'
                          )
                        }
                      >
                        <div className="flex items-center justify-center space-x-2">
                          <HandThumbUpIcon className="h-4 w-4" />
                          <span>Likes ({reactions.likes.length})</span>
                        </div>
                      </Tab>
                      <Tab
                        className={({ selected }) =>
                          cn(
                            'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                            'ring-white ring-opacity-60 ring-offset-2 ring-offset-red-400 focus:outline-none focus:ring-2',
                            selected
                              ? 'bg-white shadow text-red-700'
                              : 'text-gray-700 hover:bg-white/[0.12] hover:text-red-600'
                          )
                        }
                      >
                        <div className="flex items-center justify-center space-x-2">
                          <HandThumbDownIcon className="h-4 w-4" />
                          <span>Dislikes ({reactions.dislikes.length})</span>
                        </div>
                      </Tab>
                    </Tab.List>
                    <Tab.Panels>
                      <Tab.Panel className="max-h-96 overflow-y-auto">
                        {reactions.likes.length > 0 ? (
                          <div className="space-y-1">
                            {reactions.likes.map((reaction) => (
                              <UserItem key={reaction.id} reaction={reaction} />
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            No likes yet
                          </div>
                        )}
                      </Tab.Panel>
                      <Tab.Panel className="max-h-96 overflow-y-auto">
                        {reactions.dislikes.length > 0 ? (
                          <div className="space-y-1">
                            {reactions.dislikes.map((reaction) => (
                              <UserItem key={reaction.id} reaction={reaction} />
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            No dislikes yet
                          </div>
                        )}
                      </Tab.Panel>
                    </Tab.Panels>
                  </Tab.Group>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No reactions yet
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
