"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { <PERSON>alog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, PhotoIcon, CalendarIcon, MapPinIcon, GlobeAltIcon, LockClosedIcon, UsersIcon } from "@heroicons/react/24/outline";
import { useDropzone } from "react-dropzone";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";

interface CreateEventFormProps {
  isOpen: boolean;
  onClose: () => void;
}

const eventCategories = [
  "Music",
  "Food & Drink",
  "Sports & Fitness",
  "Arts & Culture",
  "Business & Professional",
  "Community & Causes",
  "Education",
  "Technology",
  "Travel & Outdoor",
  "Health & Wellness",
  "Hobbies & Special Interest",
  "Other"
];

export function CreateEventForm({ isOpen, onClose }: CreateEventFormProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form fields
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [startDate, setStartDate] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endDate, setEndDate] = useState("");
  const [endTime, setEndTime] = useState("");
  const [location, setLocation] = useState("");
  const [isOnline, setIsOnline] = useState(false);
  const [onlineLink, setOnlineLink] = useState("");
  const [visibility, setVisibility] = useState<"public" | "private" | "friends">("public");
  const [category, setCategory] = useState("");
  const [coverImage, setCoverImage] = useState<File | null>(null);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setCoverImage(acceptedFiles[0]);
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate form
      if (!name || !startDate || !startTime || !endDate || !endTime) {
        throw new Error("Please fill in all required fields");
      }

      if (name.length < 2) {
        throw new Error("Event name must be at least 2 characters");
      }

      if (name.length > 255) {
        throw new Error("Event name must be less than 255 characters");
      }

      if (description && description.length > 5000) {
        throw new Error("Description must be less than 5000 characters");
      }

      // Combine date and time
      let startDateTime, endDateTime;

      try {
        startDateTime = new Date(`${startDate}T${startTime}`);
        if (isNaN(startDateTime.getTime())) {
          throw new Error("Invalid start date/time format");
        }
      } catch (e) {
        throw new Error("Invalid start date/time format");
      }

      try {
        endDateTime = new Date(`${endDate}T${endTime}`);
        if (isNaN(endDateTime.getTime())) {
          throw new Error("Invalid end date/time format");
        }
      } catch (e) {
        throw new Error("Invalid end date/time format");
      }

      // Validate dates
      if (endDateTime <= startDateTime) {
        throw new Error("End time must be after start time");
      }

      // Validate location
      if (!isOnline && location && location.length > 255) {
        throw new Error("Location must be less than 255 characters");
      }

      // Validate online link if it's an online event
      if (isOnline && onlineLink) {
        try {
          // Check if the link has a valid URL format
          new URL(onlineLink);
        } catch (e) {
          throw new Error("Please enter a valid URL for the online event link");
        }
      }

      // Validate category
      if (category && category.length > 100) {
        throw new Error("Category must be less than 100 characters");
      }

      // Upload cover image if provided
      let coverImageUrl = null;
      if (coverImage) {
        try {
          const uploadedImages = await uploadMultipleToCloudinary([coverImage]);
          if (uploadedImages.length > 0) {
            coverImageUrl = uploadedImages[0];
          }
        } catch (e) {
          console.error("Error uploading image:", e);
          throw new Error("Failed to upload cover image. Please try again.");
        }
      }

      // Prepare event data
      const eventData = {
        name,
        description: description || undefined,
        startTime: startDateTime.toISOString(),
        endTime: endDateTime.toISOString(),
        location: isOnline ? null : (location || undefined),
        isOnline,
        onlineLink: isOnline && onlineLink ? onlineLink : null,
        visibility,
        category: category || null,
        coverImage: coverImageUrl,
      };

      console.log("Submitting event data:", eventData);

      // Create event
      const response = await fetch("/api/events", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(eventData),
      });

      if (!response.ok) {
        const data = await response.json();

        if (data.formattedErrors && Array.isArray(data.formattedErrors)) {
          // Display formatted validation errors
          throw new Error(`Validation errors: ${data.formattedErrors.join(', ')}`);
        } else {
          throw new Error(data.message || "Failed to create event");
        }
      }

      const event = await response.json();

      // Reset form and close dialog
      resetForm();
      onClose();

      // Navigate to the new event page
      router.push(`/events/${event.id}`);
      router.refresh();
    } catch (err) {
      console.error("Error creating event:", err);
      setError(err instanceof Error ? err.message : "Failed to create event");
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setName("");
    setDescription("");
    setStartDate("");
    setStartTime("");
    setEndDate("");
    setEndTime("");
    setLocation("");
    setIsOnline(false);
    setOnlineLink("");
    setVisibility("public");
    setCategory("");
    setCoverImage(null);
    setError(null);
  };

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        if (!isSubmitting) {
          resetForm();
          onClose();
        }
      }}
      className="relative z-50"
    >
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="w-full max-w-2xl rounded-lg bg-white p-6 max-h-[90vh] overflow-y-auto">
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Create Event
          </DialogTitle>
          <button
            type="button"
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
            onClick={() => {
              if (!isSubmitting) {
                resetForm();
                onClose();
              }
            }}
            disabled={isSubmitting}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>

          {error && (
            <div className="mt-4 rounded-md bg-red-50 p-4 text-sm text-red-700">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="mt-6 space-y-6">
            {/* Event Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Event Name <span className="text-red-500">*</span>
              </label>
              <Input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Give your event a name"
                required
                className="mt-1"
              />
            </div>

            {/* Cover Image */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Cover Image
              </label>
              <div
                {...getRootProps()}
                className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6"
              >
                <div className="space-y-1 text-center">
                  <input {...getInputProps()} />
                  {coverImage ? (
                    <div>
                      <p className="text-sm text-gray-600">{coverImage.name}</p>
                      <p className="text-xs text-gray-500">
                        {(coverImage.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setCoverImage(null);
                        }}
                        className="mt-2 text-sm text-red-600 hover:text-red-500"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <>
                      <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="text-xs text-gray-500">
                        PNG, JPG, GIF up to 10MB
                      </p>
                      <p className="text-sm text-gray-600">
                        Drag and drop or click to upload
                      </p>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                  Start Date <span className="text-red-500">*</span>
                </label>
                <div className="mt-1 flex items-center">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    required
                  />
                </div>
              </div>
              <div>
                <label htmlFor="startTime" className="block text-sm font-medium text-gray-700">
                  Start Time <span className="text-red-500">*</span>
                </label>
                <Input
                  id="startTime"
                  type="time"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                  End Date <span className="text-red-500">*</span>
                </label>
                <div className="mt-1 flex items-center">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    required
                  />
                </div>
              </div>
              <div>
                <label htmlFor="endTime" className="block text-sm font-medium text-gray-700">
                  End Time <span className="text-red-500">*</span>
                </label>
                <Input
                  id="endTime"
                  type="time"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            {/* Location */}
            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                  Location
                </label>
                <div className="flex items-center">
                  <input
                    id="isOnline"
                    type="checkbox"
                    checked={isOnline}
                    onChange={(e) => setIsOnline(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isOnline" className="ml-2 text-sm text-gray-600">
                    This is an online event
                  </label>
                </div>
              </div>
              {isOnline ? (
                <div className="mt-1">
                  <div className="flex items-center">
                    <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <Input
                      id="onlineLink"
                      type="url"
                      value={onlineLink}
                      onChange={(e) => setOnlineLink(e.target.value)}
                      placeholder="Add a link to your online event"
                    />
                  </div>
                </div>
              ) : (
                <div className="mt-1">
                  <div className="flex items-center">
                    <MapPinIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <Input
                      id="location"
                      type="text"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      placeholder="Add a venue or address"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="Tell people what your event is about"
              />
            </div>

            {/* Category and Visibility */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  id="category"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                >
                  <option value="">Select a category</option>
                  {eventCategories.map((cat) => (
                    <option key={cat} value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="visibility" className="block text-sm font-medium text-gray-700">
                  Who can see your event?
                </label>
                <div className="mt-1 space-y-2">
                  <div className="flex items-center">
                    <input
                      id="visibility-public"
                      name="visibility"
                      type="radio"
                      checked={visibility === "public"}
                      onChange={() => setVisibility("public")}
                      className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="visibility-public" className="ml-2 flex items-center text-sm text-gray-700">
                      <GlobeAltIcon className="h-4 w-4 mr-1" />
                      Public - Anyone can see this event
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="visibility-friends"
                      name="visibility"
                      type="radio"
                      checked={visibility === "friends"}
                      onChange={() => setVisibility("friends")}
                      className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="visibility-friends" className="ml-2 flex items-center text-sm text-gray-700">
                      <UsersIcon className="h-4 w-4 mr-1" />
                      Friends - Only your friends can see this event
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="visibility-private"
                      name="visibility"
                      type="radio"
                      checked={visibility === "private"}
                      onChange={() => setVisibility("private")}
                      className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="visibility-private" className="ml-2 flex items-center text-sm text-gray-700">
                      <LockClosedIcon className="h-4 w-4 mr-1" />
                      Private - Only people you invite can see this event
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (!isSubmitting) {
                    resetForm();
                    onClose();
                  }
                }}
                disabled={isSubmitting}
                className="mr-3"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Creating..." : "Create Event"}
              </Button>
            </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
