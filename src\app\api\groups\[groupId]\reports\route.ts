import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, groupReports, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, or, desc } from "drizzle-orm";

const reportSchema = z.object({
  reportedUserId: z.string().optional(),
  postId: z.string().optional(),
  reason: z.enum(["spam", "harassment", "inappropriate_content", "violation", "other"]),
  description: z.string().max(1000).optional(),
});

const reportUpdateSchema = z.object({
  status: z.enum(["pending", "reviewed"]),
});

// Get reports for a group (admin only)
export async function GET(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user is an admin or moderator of the group
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id),
        or(
          eq(groupMembers.role, "admin"),
          eq(groupMembers.role, "moderator")
        )
      ),
    });

    const isCreator = group.creatorId === session.user.id;

    if (!userMembership && !isCreator) {
      return NextResponse.json(
        { message: "Only admins and moderators can view reports" },
        { status: 403 }
      );
    }

    // Fetch reports from the database
    const reports = await db.query.groupReports.findMany({
      where: eq(groupReports.groupId, groupId),
      orderBy: [desc(groupReports.createdAt)],
      with: {
        reporter: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        reportedUser: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        post: true,
      },
    });

    return NextResponse.json(reports);
  } catch (error) {
    console.error("Error fetching group reports:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a report for a group
export async function POST(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const body = await req.json();
    const validatedData = reportSchema.parse(body);

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user is a member of the group
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id)
      ),
    });

    if (!userMembership) {
      return NextResponse.json(
        { message: "You must be a member to report content in this group" },
        { status: 403 }
      );
    }

    // Validate that at least one of reportedUserId or postId is provided
    if (!validatedData.reportedUserId && !validatedData.postId) {
      return NextResponse.json(
        { message: "Either reportedUserId or postId must be provided" },
        { status: 400 }
      );
    }

    const reportId = uuidv4();

    // Insert the report into the database
    await db.insert(groupReports).values({
      id: reportId,
      groupId,
      reporterId: session.user.id,
      reportedUserId: validatedData.reportedUserId || null,
      postId: validatedData.postId || null,
      reason: validatedData.reason,
      description: validatedData.description || null,
      status: "pending",
    });

    // Notify group admins about the report
    const admins = await db.query.groupMembers.findMany({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.role, "admin")
      ),
    });

    // Create a notification for each admin
    for (const admin of admins) {
      if (admin.userId !== session.user.id) { // Don't notify the reporter
        const notificationId = uuidv4();
        await db.insert(notifications).values({
          id: notificationId,
          recipientId: admin.userId,
          type: "group_announcement" as const,
          senderId: session.user.id,
          groupId,
        });
      }
    }

    return NextResponse.json(
      { message: "Report submitted successfully", id: reportId },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating group report:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a report status (admin only)
export async function PUT(
  req: Request,
  context: { params: Promise<{ groupId: string, reportId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const url = new URL(req.url);
    const reportId = url.searchParams.get("reportId");

    if (!reportId) {
      return NextResponse.json(
        { message: "Report ID is required" },
        { status: 400 }
      );
    }

    const body = await req.json();
    const validatedData = reportUpdateSchema.parse(body);

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the report exists
    const report = await db.query.groupReports.findFirst({
      where: and(
        eq(groupReports.id, reportId),
        eq(groupReports.groupId, groupId)
      ),
    });

    if (!report) {
      return NextResponse.json(
        { message: "Report not found" },
        { status: 404 }
      );
    }

    // Check if the user is an admin or moderator of the group
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id),
        or(
          eq(groupMembers.role, "admin"),
          eq(groupMembers.role, "moderator")
        )
      ),
    });

    const isCreator = group.creatorId === session.user.id;

    if (!userMembership && !isCreator) {
      return NextResponse.json(
        { message: "Only admins and moderators can update reports" },
        { status: 403 }
      );
    }

    // Update the report status
    await db.update(groupReports)
      .set({ status: validatedData.status })
      .where(eq(groupReports.id, reportId));

    return NextResponse.json(
      { message: "Report status updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating group report:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
