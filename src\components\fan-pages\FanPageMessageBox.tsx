"use client";

import { useState, useRef, useEffect } from "react";
import { useFanPageMessage } from "@/contexts/FanPageMessageContext";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import Image from "next/image";
import {
  XMarkIcon,
  PaperAirplaneIcon,
  MinusIcon,
  ChatBubbleLeftRightIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

export function FanPageMessageBox() {
  const { state, closeMessageBox, sendMessage, markAsRead } = useFanPageMessage();
  const [messageInput, setMessageInput] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (!isMinimized && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [state.messages, isMinimized]);

  // Focus input when opened
  useEffect(() => {
    if (state.isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [state.isOpen, isMinimized]);

  // Mark as read when opened
  useEffect(() => {
    if (state.isOpen && !isMinimized && state.unreadCount > 0) {
      markAsRead();
    }
  }, [state.isOpen, isMinimized, state.unreadCount, markAsRead]);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || isSending) return;

    setIsSending(true);
    try {
      await sendMessage(messageInput.trim());
      setMessageInput("");
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!state.isOpen) return null;

  return (
    <div className="fixed bottom-0 right-4 z-50 w-80 bg-white border border-gray-200 rounded-t-lg shadow-2xl">
      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-blue-600 text-white rounded-t-lg">
        <div className="flex items-center space-x-3">
          {state.pageImage ? (
            <Image
              src={state.pageImage}
              alt={state.pageName || "Page"}
              width={32}
              height={32}
              className="rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <ChatBubbleLeftRightIcon className="h-4 w-4" />
            </div>
          )}
          <div>
            <h3 className="font-semibold text-sm">{state.pageName}</h3>
            <div className="flex items-center space-x-2">
              <p className="text-xs text-blue-100">Send a message</p>
              <Link
                href={`/messages?conversation=fanpage_${state.pageId}`}
                className="text-xs text-blue-100 hover:text-white flex items-center space-x-1 transition-colors"
                title="View in Messages"
              >
                <ArrowTopRightOnSquareIcon className="h-3 w-3" />
                <span>View</span>
              </Link>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 hover:bg-blue-700 rounded transition-colors"
            title={isMinimized ? "Expand" : "Minimize"}
          >
            <MinusIcon className="h-4 w-4" />
          </button>
          <button
            onClick={closeMessageBox}
            className="p-1 hover:bg-blue-700 rounded transition-colors"
            title="Close"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Message Area */}
      {!isMinimized && (
        <>
          <div className="h-80 overflow-y-auto p-3 bg-gray-50">
            {state.isLoading ? (
              <div className="flex items-center justify-center h-full">
                <Spinner size="sm" />
              </div>
            ) : state.messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <ChatBubbleLeftRightIcon className="h-12 w-12 mb-2 text-gray-300" />
                <p className="text-sm text-center">
                  Start a conversation with {state.pageName}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {state.messages.slice().reverse().map((message) => {
                  const isFromPage = (message as any).isFromPage;
                  return (
                    <div key={message.id} className={cn(
                      "flex items-start space-x-2",
                      isFromPage ? "flex-row-reverse space-x-reverse" : ""
                    )}>
                      {message.sender.image ? (
                        <Image
                          src={message.sender.image}
                          alt={message.sender.name || "User"}
                          width={24}
                          height={24}
                          className="rounded-full flex-shrink-0"
                        />
                      ) : (
                        <div className="w-6 h-6 bg-gray-300 rounded-full flex-shrink-0" />
                      )}
                      <div className="flex-1 min-w-0">
                        <div className={cn(
                          "rounded-lg px-3 py-2 shadow-sm border",
                          isFromPage
                            ? "bg-blue-600 text-white ml-auto max-w-xs"
                            : "bg-white text-gray-900"
                        )}>
                          <p className="text-sm">{message.content}</p>
                        </div>
                        <p className={cn(
                          "text-xs text-gray-500 mt-1",
                          isFromPage ? "text-right" : ""
                        )}>
                          {isFromPage ? state.pageName : message.sender.name} • {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                        </p>
                      </div>
                    </div>
                  );
                })}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          {/* Input Area */}
          <div className="p-3 border-t border-gray-200 bg-white rounded-b-lg">
            <div className="flex items-center space-x-2">
              <Input
                ref={inputRef}
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type a message..."
                className="flex-1 text-sm"
                disabled={isSending}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!messageInput.trim() || isSending}
                className={cn(
                  "rounded-full p-2 transition-all",
                  messageInput.trim() && !isSending
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "bg-gray-200 text-gray-400 cursor-not-allowed"
                )}
              >
                {isSending ? (
                  <Spinner size="xs" />
                ) : (
                  <PaperAirplaneIcon className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </>
      )}

      {/* Minimized State */}
      {isMinimized && state.unreadCount > 0 && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
          {state.unreadCount > 9 ? "9+" : state.unreadCount}
        </div>
      )}
    </div>
  );
}
