import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { executeQuery } from "@/lib/db/mysql";
import bcrypt from "bcryptjs";

const resetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, password } = resetPasswordSchema.parse(body);

    // Find user with valid reset token
    const users = await executeQuery(
      "SELECT id, email, resetToken, resetTokenExpiry FROM users WHERE resetToken = ? AND resetTokenExpiry > NOW() AND status = 'active'",
      [token]
    ) as any[];

    if (users.length === 0) {
      return NextResponse.json(
        { error: "Invalid or expired reset token. Please request a new password reset." },
        { status: 400 }
      );
    }

    const user = users[0];

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Update user password and clear reset token
    await executeQuery(
      "UPDATE users SET password = ?, resetToken = NULL, resetTokenExpiry = NULL, updatedAt = NOW() WHERE id = ?",
      [hashedPassword, user.id]
    );

    return NextResponse.json(
      { message: "Password has been reset successfully." },
      { status: 200 }
    );

  } catch (error) {
    console.error("Reset password error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Something went wrong. Please try again." },
      { status: 500 }
    );
  }
}
