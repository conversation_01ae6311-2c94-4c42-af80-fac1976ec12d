"use client";

import { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Spinner } from "@/components/ui/Spinner";
import { Switch } from "@/components/ui/Switch";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserSelect } from "@/components/admin/users/UserSelect";

interface Store {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  logo: string | null;
  banner: string | null;
  location: string | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  isVerified: boolean;
  owner: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface EditStoreModalProps {
  isOpen: boolean;
  onClose: () => void;
  store: Store | null;
  onStoreUpdated: () => void;
}

const storeSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(255, "Name must be less than 255 characters"),
  slug: z
    .string()
    .min(3, "Slug must be at least 3 characters")
    .max(100, "Slug must be less than 100 characters")
    .regex(/^[a-zA-Z0-9-]+$/, "Slug can only contain letters, numbers, and hyphens"),
  description: z.string().max(1000, "Description must be less than 1000 characters").nullable().optional(),
  location: z.string().max(255, "Location must be less than 255 characters").nullable().optional(),
  phone: z.string().max(50, "Phone must be less than 50 characters").nullable().optional(),
  email: z.string().email("Invalid email address").max(255, "Email must be less than 255 characters").nullable().optional(),
  website: z.string().max(255, "Website must be less than 255 characters").nullable().optional(),
  logo: z.string().url("Invalid URL").nullable().optional(),
  banner: z.string().url("Invalid URL").nullable().optional(),
  isVerified: z.boolean().optional(),
  ownerId: z.string().optional(),
});

type StoreFormData = z.infer<typeof storeSchema>;

export function EditStoreModal({ isOpen, onClose, store, onStoreUpdated }: EditStoreModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVerified, setIsVerified] = useState(store?.isVerified || false);
  const [selectedOwnerId, setSelectedOwnerId] = useState<string>(store?.owner.id || "");

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<StoreFormData>({
    resolver: zodResolver(storeSchema),
    defaultValues: {
      name: store?.name || "",
      slug: store?.slug || "",
      description: store?.description || "",
      location: store?.location || "",
      phone: store?.phone || "",
      email: store?.email || "",
      website: store?.website || "",
      logo: store?.logo || "",
      banner: store?.banner || "",
      isVerified: store?.isVerified || false,
    },
  });

  // Reset form when store changes
  useEffect(() => {
    if (store) {
      reset({
        name: store.name,
        slug: store.slug,
        description: store.description || "",
        location: store.location || "",
        phone: store.phone || "",
        email: store.email || "",
        website: store.website || "",
        logo: store.logo || "",
        banner: store.banner || "",
        isVerified: store.isVerified,
        ownerId: store.owner.id,
      });
      setIsVerified(store.isVerified);
      setSelectedOwnerId(store.owner.id);
    }
  }, [store, reset]);

  const onSubmit = async (data: StoreFormData) => {
    if (!store) return;

    setIsSubmitting(true);
    try {
      // Include verification status and owner ID
      const updateData = {
        ...data,
        isVerified,
        ownerId: selectedOwnerId,
      };

      const response = await fetch(`/api/admin/stores/${store.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update store");
      }

      toast.success("Store updated successfully");
      onStoreUpdated();
      onClose();
    } catch (error) {
      console.error("Error updating store:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update store");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!store) return null;

  const handleUserSelect = (userId: string) => {
    setSelectedOwnerId(userId);
    setValue("ownerId", userId);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Edit Store">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Store Name
            </label>
            <Input
              id="name"
              {...register("name")}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
              Slug
            </label>
            <Input
              id="slug"
              {...register("slug")}
              className={errors.slug ? "border-red-500" : ""}
            />
            {errors.slug && (
              <p className="mt-1 text-xs text-red-500">{errors.slug.message}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <Textarea
            id="description"
            {...register("description")}
            className={errors.description ? "border-red-500" : ""}
            rows={3}
          />
          {errors.description && (
            <p className="mt-1 text-xs text-red-500">{errors.description.message}</p>
          )}
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="location" className="block text-sm font-medium text-gray-700">
              Location
            </label>
            <Input
              id="location"
              {...register("location")}
              className={errors.location ? "border-red-500" : ""}
            />
            {errors.location && (
              <p className="mt-1 text-xs text-red-500">{errors.location.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
              Phone
            </label>
            <Input
              id="phone"
              {...register("phone")}
              className={errors.phone ? "border-red-500" : ""}
            />
            {errors.phone && (
              <p className="mt-1 text-xs text-red-500">{errors.phone.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <Input
              id="email"
              type="email"
              {...register("email")}
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-700">
              Website
            </label>
            <Input
              id="website"
              {...register("website")}
              className={errors.website ? "border-red-500" : ""}
            />
            {errors.website && (
              <p className="mt-1 text-xs text-red-500">{errors.website.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="logo" className="block text-sm font-medium text-gray-700">
              Logo URL
            </label>
            <Input
              id="logo"
              {...register("logo")}
              className={errors.logo ? "border-red-500" : ""}
            />
            {errors.logo && (
              <p className="mt-1 text-xs text-red-500">{errors.logo.message}</p>
            )}
            {store.logo && (
              <div className="mt-2">
                <Image
                  src={store.logo}
                  alt="Store Logo"
                  width={50}
                  height={50}
                  className="rounded-full object-cover"
                />
              </div>
            )}
          </div>

          <div>
            <label htmlFor="banner" className="block text-sm font-medium text-gray-700">
              Banner URL
            </label>
            <Input
              id="banner"
              {...register("banner")}
              className={errors.banner ? "border-red-500" : ""}
            />
            {errors.banner && (
              <p className="mt-1 text-xs text-red-500">{errors.banner.message}</p>
            )}
            {store.banner && (
              <div className="mt-2">
                <Image
                  src={store.banner}
                  alt="Store Banner"
                  width={100}
                  height={30}
                  className="rounded object-cover"
                />
              </div>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Store Owner
          </label>
          <UserSelect
            onUserSelect={handleUserSelect}
            selectedUserId={selectedOwnerId}
          />
          {errors.ownerId && (
            <p className="mt-1 text-xs text-red-500">{errors.ownerId.message}</p>
          )}
        </div>

        <div className="flex items-center">
          <Switch
            id="isVerified"
            checked={isVerified}
            onCheckedChange={setIsVerified}
          />
          <label htmlFor="isVerified" className="ml-2 block text-sm font-medium text-gray-700">
            Verified Store
          </label>
        </div>

        <div className="mt-5 flex justify-end space-x-3">
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
            Save Changes
          </Button>
        </div>
      </form>
    </Modal>
  );
}
