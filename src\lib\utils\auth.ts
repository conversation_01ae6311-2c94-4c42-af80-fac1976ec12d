import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";

export async function getSession() {
  return await getServerSession(authOptions);
}

export async function getCurrentUser() {
  const session = await getSession();
  return session?.user;
}

// Use this function to redirect to login if user is not authenticated
export function redirectToLogin() {
  redirect("/login");
}

// This function is kept for backward compatibility
// It's recommended to use getCurrentUser() with manual redirect instead
export async function requireAuth() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/login");
  }

  return user;
}
