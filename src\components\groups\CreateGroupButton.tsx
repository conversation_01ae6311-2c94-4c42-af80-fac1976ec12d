"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/Button";
import { PlusIcon } from "@heroicons/react/24/outline";
import { CreateGroupForm } from "./CreateGroupForm";

export function CreateGroupButton() {
  const [isFormOpen, setIsFormOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setIsFormOpen(true)}>
        <PlusIcon className="h-5 w-5 mr-1" />
        Create Group
      </Button>

      <CreateGroupForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
      />
    </>
  );
}
