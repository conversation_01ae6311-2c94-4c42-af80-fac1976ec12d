import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { SettingsSidebar } from "@/components/settings/SettingsSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { SettingsLayout } from "@/components/settings/SettingsLayout";
import { Button } from "@/components/ui/Button";
import { PlusIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/outline";

export default async function MyAddressesPage() {
  const user = await requireAuth();

  // Mock data for addresses
  const addresses = [
    {
      id: "1",
      name: "Home",
      street: "123 Main Street",
      city: "Dhaka",
      state: "Dhaka",
      postalCode: "1000",
      country: "Bangladesh",
      isDefault: true,
    },
    {
      id: "2",
      name: "Work",
      street: "456 Office Avenue",
      city: "Dhaka",
      state: "Dhaka",
      postalCode: "1205",
      country: "Bangladesh",
      isDefault: false,
    },
  ];

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Settings sidebar */}
          <div className="lg:col-span-1">
            <SettingsSidebar />
          </div>

          {/* Main content */}
          <div className="lg:col-span-2">
            <SettingsLayout 
              title="My Addresses" 
              description="Manage your shipping and billing addresses."
            >
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium text-gray-900">Your Addresses</h4>
                  <Button size="sm">
                    <PlusIcon className="h-4 w-4 mr-1" />
                    Add New Address
                  </Button>
                </div>
                
                {addresses.length > 0 ? (
                  <div className="space-y-4">
                    {addresses.map((address) => (
                      <div 
                        key={address.id} 
                        className="p-4 border border-gray-200 rounded-lg"
                      >
                        <div className="flex justify-between">
                          <div className="flex items-center">
                            <h5 className="text-sm font-medium text-gray-900">{address.name}</h5>
                            {address.isDefault && (
                              <span className="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                Default
                              </span>
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <button className="text-gray-500 hover:text-gray-700">
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button className="text-gray-500 hover:text-red-600">
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="mt-2 text-sm text-gray-500">
                          <p>{address.street}</p>
                          <p>{address.city}, {address.state} {address.postalCode}</p>
                          <p>{address.country}</p>
                        </div>
                        
                        {!address.isDefault && (
                          <div className="mt-3">
                            <Button variant="outline" size="sm">
                              Set as Default
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
                    <p className="text-gray-500">You haven't added any addresses yet.</p>
                    <div className="mt-4">
                      <Button size="sm">
                        <PlusIcon className="h-4 w-4 mr-1" />
                        Add Address
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </SettingsLayout>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}
