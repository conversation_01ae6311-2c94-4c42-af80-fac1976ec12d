import { NextRequest, NextResponse } from 'next/server';

// Cache for 1 hour
const CACHE_DURATION = 60 * 60;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');
    const width = searchParams.get('w');
    const quality = searchParams.get('q');

    if (!imageUrl) {
      return new NextResponse('Missing image URL', { status: 400 });
    }

    // Validate URL format
    let url: URL;
    try {
      url = new URL(imageUrl);
    } catch {
      return new NextResponse('Invalid URL', { status: 400 });
    }

    // Security: Only allow HTTP/HTTPS
    if (!['http:', 'https:'].includes(url.protocol)) {
      return new NextResponse('Invalid protocol', { status: 400 });
    }

    // Security: Block private/local IPs
    const hostname = url.hostname;
    if (
      hostname === 'localhost' ||
      hostname.startsWith('127.') ||
      hostname.startsWith('192.168.') ||
      hostname.startsWith('10.') ||
      hostname.startsWith('172.16.') ||
      hostname.startsWith('172.17.') ||
      hostname.startsWith('172.18.') ||
      hostname.startsWith('172.19.') ||
      hostname.startsWith('172.2') ||
      hostname.startsWith('172.30.') ||
      hostname.startsWith('172.31.')
    ) {
      return new NextResponse('Access denied', { status: 403 });
    }

    // Fetch the image with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ImageProxy/1.0)',
        'Accept': 'image/*',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      return new NextResponse('Failed to fetch image', { status: response.status });
    }

    // Check content type
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.startsWith('image/')) {
      return new NextResponse('Not an image', { status: 400 });
    }

    // Get image data
    const imageBuffer = await response.arrayBuffer();

    // Create response with proper headers
    const headers = new Headers();
    headers.set('Content-Type', contentType);
    headers.set('Cache-Control', `public, max-age=${CACHE_DURATION}, s-maxage=${CACHE_DURATION}`);
    headers.set('Content-Length', imageBuffer.byteLength.toString());

    // Add security headers
    headers.set('X-Content-Type-Options', 'nosniff');
    headers.set('X-Frame-Options', 'DENY');

    return new NextResponse(imageBuffer, {
      status: 200,
      headers,
    });

  } catch (error) {
    console.error('Image proxy error:', error);
    
    if (error instanceof Error && error.name === 'AbortError') {
      return new NextResponse('Request timeout', { status: 408 });
    }
    
    return new NextResponse('Internal server error', { status: 500 });
  }
}
