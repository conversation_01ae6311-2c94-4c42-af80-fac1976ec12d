"use client";

import { useEffect, useState } from "react";

/**
 * Hook to safely check if we're on the client side
 * Prevents hydration mismatches when using browser-specific APIs
 */
export function useClientSide() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to safely get window location
 * Returns empty string during SSR to prevent hydration mismatches
 */
export function useWindowLocation() {
  const [location, setLocation] = useState('');
  const isClient = useClientSide();

  useEffect(() => {
    if (isClient && typeof window !== 'undefined') {
      setLocation(window.location.href);
    }
  }, [isClient]);

  return location;
}

/**
 * Hook to safely detect mobile view
 * Prevents hydration mismatches from window.innerWidth checks
 */
export function useMobileView(breakpoint: number = 1024) {
  const [isMobile, setIsMobile] = useState(false);
  const isClient = useClientSide();

  useEffect(() => {
    if (!isClient) return;

    const checkMobile = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [isClient, breakpoint]);

  return isMobile;
}

/**
 * Hook to detect device type and screen size
 */
export function useDeviceType() {
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const isClient = useClientSide();

  useEffect(() => {
    if (!isClient) return;

    const checkDeviceType = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, [isClient]);

  return deviceType;
}

/**
 * Hook to detect if user prefers reduced motion
 */
export function usePrefersReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const isClient = useClientSide();

  useEffect(() => {
    if (!isClient) return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [isClient]);

  return prefersReducedMotion;
}
