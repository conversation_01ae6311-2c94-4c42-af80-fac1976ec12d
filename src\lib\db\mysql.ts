import mysql from 'mysql2/promise';
import { dbConfig } from '../config';

// Create a MySQL connection pool with optimized configuration
export const pool = mysql.createPool({
  host: dbConfig.host,
  user: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  port: dbConfig.port,
  // Connection pool settings - Optimized for high performance (only valid MySQL2 options)
  waitForConnections: dbConfig.pool.waitForConnections,
  connectionLimit: dbConfig.pool.connectionLimit,
  maxIdle: dbConfig.pool.maxIdle,
  idleTimeout: dbConfig.pool.idleTimeout,
  queueLimit: dbConfig.pool.queueLimit,
  enableKeepAlive: dbConfig.pool.enableKeepAlive,
  keepAliveInitialDelay: dbConfig.pool.keepAliveInitialDelay,
});

// Helper function to execute SQL queries
export async function executeQuery<T>(query: string, params: any[] = []): Promise<T> {
  try {
    const [rows] = await pool.execute(query, params);
    return rows as T;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

// Helper function to execute a transaction
export async function executeTransaction<T>(
  callback: (connection: mysql.PoolConnection) => Promise<T>
): Promise<T> {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}
