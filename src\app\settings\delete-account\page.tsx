import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { SettingsSidebar } from "@/components/settings/SettingsSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { SettingsLayout } from "@/components/settings/SettingsLayout";
import { Button } from "@/components/ui/Button";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

export default async function DeleteAccountPage() {
  const user = await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Settings sidebar */}
          <div className="lg:col-span-1">
            <SettingsSidebar />
          </div>

          {/* Main content */}
          <div className="lg:col-span-2">
            <SettingsLayout 
              title="Delete Account" 
              description="Permanently delete your account and all associated data."
            >
              <div className="space-y-6">
                <div className="bg-red-50 p-4 rounded-lg flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-red-800">Warning: This action cannot be undone</h4>
                    <p className="mt-1 text-sm text-red-700">
                      Deleting your account will permanently remove all your data, including your profile, posts, photos, comments, and messages. This action cannot be reversed.
                    </p>
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Before you delete your account:</h4>
                  
                  <ul className="space-y-2 text-sm text-gray-600 list-disc pl-5">
                    <li>Consider downloading a copy of your data from the "My Information" section</li>
                    <li>If you have an active subscription, cancel it before deleting your account</li>
                    <li>If you have a balance in your wallet, withdraw it before deleting your account</li>
                    <li>Consider temporarily deactivating your account instead of permanently deleting it</li>
                  </ul>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Confirm deletion</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="delete-reason" className="block text-sm font-medium text-gray-700">
                        Why are you deleting your account?
                      </label>
                      <div className="mt-1">
                        <select
                          id="delete-reason"
                          name="delete-reason"
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="">Select a reason</option>
                          <option value="privacy">Privacy concerns</option>
                          <option value="experience">Bad experience</option>
                          <option value="alternative">Found an alternative platform</option>
                          <option value="temporary">Temporary break</option>
                          <option value="other">Other reason</option>
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="feedback" className="block text-sm font-medium text-gray-700">
                        Additional feedback (optional)
                      </label>
                      <div className="mt-1">
                        <textarea
                          id="feedback"
                          name="feedback"
                          rows={3}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Tell us more about your decision"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Enter your password to confirm
                      </label>
                      <div className="mt-1">
                        <input
                          type="password"
                          name="password"
                          id="password"
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Enter your current password"
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="confirm-delete"
                          name="confirm-delete"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="confirm-delete" className="font-medium text-gray-700">
                          I understand that this action is permanent and cannot be undone
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="pt-5">
                  <div className="flex justify-between">
                    <Button variant="outline">
                      Cancel
                    </Button>
                    <Button variant="danger">
                      Delete Account Permanently
                    </Button>
                  </div>
                </div>
              </div>
            </SettingsLayout>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}
