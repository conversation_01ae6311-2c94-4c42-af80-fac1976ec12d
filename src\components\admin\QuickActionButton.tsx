"use client";

import Link from "next/link";
import { ReactNode } from "react";

interface QuickActionButtonProps {
  href: string;
  icon: ReactNode;
  label: string;
  description?: string;
  color?: string;
}

export function QuickActionButton({
  href,
  icon,
  label,
  description,
  color = "bg-blue-50 text-blue-700 hover:bg-blue-100",
}: QuickActionButtonProps) {
  return (
    <Link
      href={href}
      className={`flex items-center rounded-lg p-4 transition-all duration-200 ${color}`}
    >
      <div className="mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-white shadow-sm">
        {icon}
      </div>
      <div>
        <h3 className="text-base font-medium">{label}</h3>
        {description && <p className="mt-1 text-sm opacity-80">{description}</p>}
      </div>
    </Link>
  );
}
