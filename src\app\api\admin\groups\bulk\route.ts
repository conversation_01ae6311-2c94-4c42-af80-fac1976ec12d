import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, posts } from "@/lib/db/schema";
import { z } from "zod";
import { eq, inArray } from "drizzle-orm";

const bulkDeleteSchema = z.object({
  groupIds: z.array(z.string()),
});

// Bulk delete groups
export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { groupIds } = bulkDeleteSchema.parse(body);

    if (groupIds.length === 0) {
      return NextResponse.json(
        { message: "No group IDs provided" },
        { status: 400 }
      );
    }

    // Delete all group members first (due to foreign key constraints)
    await db.delete(groupMembers)
      .where(inArray(groupMembers.groupId, groupIds));

    // Delete all group posts
    await db.delete(posts)
      .where(inArray(posts.groupId, groupIds));

    // Delete the groups
    const result = await db.delete(groups)
      .where(inArray(groups.id, groupIds));

    return NextResponse.json({
      message: `${groupIds.length} groups deleted successfully`,
    });
  } catch (error) {
    console.error("Error performing bulk delete:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
