"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ChevronLeftIcon, InformationCircleIcon } from "@heroicons/react/24/outline";

export function GroupNotFound() {
  return (
    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-6">
        <Link href="/groups">
          <Button variant="outline" size="sm">
            <ChevronLeftIcon className="mr-1 h-4 w-4" />
            Back to Groups
          </Button>
        </Link>
      </div>

      <div className="rounded-lg bg-white p-8 text-center shadow">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
          <InformationCircleIcon className="h-8 w-8 text-red-600" />
        </div>
        <h1 className="mt-4 text-xl font-bold text-gray-900">
          Group Not Found
        </h1>
        <p className="mt-4 text-sm text-gray-500">
          The group you're looking for doesn't exist or has been removed.
        </p>
        <div className="mt-4">
          <Link href="/groups">
            <Button>
              Browse Groups
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
