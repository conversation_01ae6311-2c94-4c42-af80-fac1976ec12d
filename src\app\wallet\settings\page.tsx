import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { WalletSettings } from "@/components/wallet/WalletSettings";

export const dynamic = 'force-dynamic';

export default async function WalletSettingsPage() {
  const user = await requireAuth();

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
        <div className="mb-4 sm:mb-6 lg:mb-8">
          <div className="flex items-start sm:items-center space-x-3 sm:space-x-4">
            <a
              href="/wallet"
              className="text-gray-600 hover:text-gray-900 transition-colors mt-1 sm:mt-0 flex-shrink-0"
            >
              <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </a>
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Wallet Settings</h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1 sm:mt-2">
                Manage your wallet security, payment methods, and preferences
              </p>
            </div>
          </div>
        </div>

        <WalletSettings userId={user.id} />
      </div>
    </MainLayout>
  );
}
