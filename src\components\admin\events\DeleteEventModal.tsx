"use client";

import { useState } from "react";
import { Dialog } from "@headlessui/react";
import { XMarkIcon, ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";
import { Spinner } from "@/components/ui/Spinner";
import { formatDateMedium, formatTimeShort } from "@/lib/utils";

interface Event {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
}

interface DeleteEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  event: Event;
}

export function DeleteEventModal({ isOpen, onClose, onSuccess, event }: DeleteEventModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      const response = await fetch(`/api/admin/events/${event.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to delete event");
      }

      toast.success("Event deleted successfully");
      onSuccess();
    } catch (error) {
      console.error("Error deleting event:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete event");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        if (!isDeleting) {
          onClose();
        }
      }}
      className="relative z-50"
    >
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-md w-full rounded-lg bg-white shadow-xl">
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <Dialog.Title className="text-lg font-semibold text-gray-900">
              Delete Event
            </Dialog.Title>
            <button
              type="button"
              onClick={() => {
                if (!isDeleting) {
                  onClose();
                }
              }}
              disabled={isDeleting}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6">
            <div className="flex items-center justify-center">
              <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
              </div>
            </div>
            <div className="mt-3 text-center sm:mt-5">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                Are you sure you want to delete this event?
              </h3>
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  This action cannot be undone. This will permanently delete the event
                  and remove all associated data.
                </p>
              </div>
              <div className="mt-4 border rounded-md p-4 bg-gray-50">
                <h4 className="font-medium text-gray-900">{event.name}</h4>
                <p className="text-sm text-gray-500 mt-1">
                  {formatDateMedium(event.startTime)} at {formatTimeShort(event.startTime)}
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 px-6 py-4 flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                if (!isDeleting) {
                  onClose();
                }
              }}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="danger"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Deleting...
                </>
              ) : (
                "Delete Event"
              )}
            </Button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
