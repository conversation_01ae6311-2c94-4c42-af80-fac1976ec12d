import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, storeFollows, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and, count } from "drizzle-orm";

export async function POST(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const storeId = params.storeId;

    // Check if store exists
    const existingStore = await db
      .select({
        id: stores.id,
        ownerId: stores.ownerId,
      })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Check if user already follows the store
    const existingFollow = await db
      .select()
      .from(storeFollows)
      .where(
        and(
          eq(storeFollows.storeId, storeId),
          eq(storeFollows.userId, session.user.id)
        )
      )
      .limit(1);

    if (existingFollow.length > 0) {
      return NextResponse.json(
        { message: "You are already following this store" },
        { status: 400 }
      );
    }

    // Create follow
    const followId = uuidv4();
    await db.insert(storeFollows).values({
      id: followId,
      storeId,
      userId: session.user.id,
    });

    // Create notification for store owner (if not self-follow)
    if (existingStore[0].ownerId !== session.user.id) {
      const notificationId = uuidv4();
      await db.insert(notifications).values({
        id: notificationId,
        recipientId: existingStore[0].ownerId,
        type: "store_follow",
        senderId: session.user.id,
        storeId,
        read: false,
      });
    }

    return NextResponse.json(
      { message: "Store followed successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error following store:", error);
    return NextResponse.json(
      { message: "Error following store" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const storeId = params.storeId;

    // Check if follow exists
    const existingFollow = await db
      .select()
      .from(storeFollows)
      .where(
        and(
          eq(storeFollows.storeId, storeId),
          eq(storeFollows.userId, session.user.id)
        )
      )
      .limit(1);

    if (existingFollow.length === 0) {
      return NextResponse.json(
        { message: "You are not following this store" },
        { status: 400 }
      );
    }

    // Delete follow
    await db
      .delete(storeFollows)
      .where(
        and(
          eq(storeFollows.storeId, storeId),
          eq(storeFollows.userId, session.user.id)
        )
      );

    return NextResponse.json(
      { message: "Store unfollowed successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error unfollowing store:", error);
    return NextResponse.json(
      { message: "Error unfollowing store" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const params = await context.params;
    const storeId = params.storeId;

    // Check if store exists
    const existingStore = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Check if user is following the store
    let isFollowing = false;
    if (session?.user) {
      const existingFollow = await db
        .select()
        .from(storeFollows)
        .where(
          and(
            eq(storeFollows.storeId, storeId),
            eq(storeFollows.userId, session.user.id)
          )
        )
        .limit(1);

      isFollowing = existingFollow.length > 0;
    }

    // Get follower count
    const followerCount = await db
      .select({ count: count() })
      .from(storeFollows)
      .where(eq(storeFollows.storeId, storeId));

    return NextResponse.json({
      isFollowing,
      followerCount: followerCount[0]?.count || 0,
    });
  } catch (error) {
    console.error("Error checking follow status:", error);
    return NextResponse.json(
      { message: "Error checking follow status" },
      { status: 500 }
    );
  }
}
