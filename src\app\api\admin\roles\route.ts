import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { adminRoles, adminPermissions, adminRolePermissions } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const roleSchema = z.object({
  name: z.string().min(2).max(100),
  description: z.string().optional(),
  permissions: z.array(z.string()),
});

// Get all roles with their permissions
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all roles
    const roles = await db.query.adminRoles.findMany({
      with: {
        permissions: {
          with: {
            permission: true,
          },
        },
      },
    });

    // Format the response
    const formattedRoles = roles.map((role) => ({
      id: role.id,
      name: role.name,
      description: role.description,
      isSystem: role.isSystem,
      createdAt: role.createdAt,
      permissions: role.permissions.map((rp) => rp.permission.code),
    }));

    return NextResponse.json(formattedRoles, { status: 200 });
  } catch (error) {
    console.error("Error fetching admin roles:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new role
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    
    // Validate the request body
    const validatedData = roleSchema.parse(body);
    
    // Check if role name already exists
    const existingRole = await db.query.adminRoles.findFirst({
      where: eq(adminRoles.name, validatedData.name),
    });
    
    if (existingRole) {
      return NextResponse.json(
        { message: "Role name already exists" },
        { status: 400 }
      );
    }
    
    // Create the role
    const roleId = uuidv4();
    await db.insert(adminRoles).values({
      id: roleId,
      name: validatedData.name,
      description: validatedData.description || null,
      isSystem: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    // Get permission IDs for the given permission codes
    const permissionRecords = await db.query.adminPermissions.findMany({
      where: (adminPermissions, { inArray }) => 
        inArray(adminPermissions.code, validatedData.permissions),
    });
    
    // Create role-permission associations
    if (permissionRecords.length > 0) {
      const rolePermissions = permissionRecords.map((permission) => ({
        id: uuidv4(),
        roleId: roleId,
        permissionId: permission.id,
        createdAt: new Date(),
      }));
      
      await db.insert(adminRolePermissions).values(rolePermissions);
    }
    
    return NextResponse.json(
      { 
        message: "Role created successfully",
        role: {
          id: roleId,
          name: validatedData.name,
          description: validatedData.description,
          isSystem: false,
          permissions: validatedData.permissions,
        }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating admin role:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
