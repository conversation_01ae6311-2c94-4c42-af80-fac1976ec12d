"use client";

import React, { createContext, useContext, useCallback } from 'react';
import { useWalletBalance } from '@/hooks/useWalletBalance';

interface WalletBalance {
  generalBalance: string;
  earningBalance: string;
  totalDeposited: string;
  totalWithdrawn: string;
  totalSent: string;
  totalReceived: string;
}

interface WalletContextType {
  balance: WalletBalance | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

interface WalletProviderProps {
  children: React.ReactNode;
}

export function WalletProvider({ children }: WalletProviderProps) {
  const walletData = useWalletBalance();

  return (
    <WalletContext.Provider value={walletData}>
      {children}
    </WalletContext.Provider>
  );
}

export function useWallet() {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
}

// Hook for components that need to refresh wallet data after transactions
export function useWalletRefresh() {
  const { refetch } = useWallet();
  
  const refreshWallet = useCallback(async () => {
    await refetch();
  }, [refetch]);

  return refreshWallet;
}
