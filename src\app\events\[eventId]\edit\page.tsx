"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { useDropzone } from "react-dropzone";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";
import {
  CalendarIcon,
  MapPinIcon,
  GlobeAltIcon,
  LockClosedIcon,
  UsersIcon,
  PhotoIcon,
  ChevronLeftIcon,
} from "@heroicons/react/24/outline";

const eventCategories = [
  "Music",
  "Food & Drink",
  "Sports & Fitness",
  "Arts & Culture",
  "Business & Professional",
  "Community & Causes",
  "Education",
  "Technology",
  "Travel & Outdoor",
  "Health & Wellness",
  "Hobbies & Special Interest",
  "Other"
];

export default function EditEventPage() {
  // Get the eventId from the URL params
  const params = useParams();
  const eventId = params?.eventId as string;
  const router = useRouter();
  const { data: session } = useSession();

  const [event, setEvent] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form fields
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [startDate, setStartDate] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endDate, setEndDate] = useState("");
  const [endTime, setEndTime] = useState("");
  const [location, setLocation] = useState("");
  const [isOnline, setIsOnline] = useState(false);
  const [onlineLink, setOnlineLink] = useState("");
  const [visibility, setVisibility] = useState<"public" | "private" | "friends">("public");
  const [category, setCategory] = useState("");
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [currentCoverImage, setCurrentCoverImage] = useState<string | null>(null);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setCoverImage(acceptedFiles[0]);
    },
  });

  useEffect(() => {
    const fetchEvent = async () => {
      if (!eventId) {
        setError("Event ID is missing");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log(`Fetching event with ID: ${eventId}`);
        const response = await fetch(`/api/events/${eventId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("Event not found");
          }
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || "Failed to fetch event");
        }

        const data = await response.json();
        console.log("Event data received:", data);
        setEvent(data);

        // Populate form fields
        setName(data.name);
        setDescription(data.description || "");

        const startDateTime = new Date(data.startTime);
        setStartDate(startDateTime.toISOString().split("T")[0]);
        setStartTime(startDateTime.toTimeString().slice(0, 5));

        const endDateTime = new Date(data.endTime);
        setEndDate(endDateTime.toISOString().split("T")[0]);
        setEndTime(endDateTime.toTimeString().slice(0, 5));

        setLocation(data.location || "");
        setIsOnline(data.isOnline || false);
        setOnlineLink(data.onlineLink || "");
        setVisibility(data.visibility);
        setCategory(data.category || "");
        setCurrentCoverImage(data.coverImage);

        // Check if user is the host
        if (session?.user?.id !== data.host.id) {
          router.push(`/events/${eventId}`);
        }
      } catch (err) {
        console.error("Error fetching event:", err);
        setError(err instanceof Error ? err.message : "Failed to load event");
      } finally {
        setIsLoading(false);
      }
    };

    if (session?.user) {
      fetchEvent();
    }
  }, [eventId, session, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!eventId) {
      setError("Event ID is missing");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Validate form
      if (!name || !startDate || !startTime || !endDate || !endTime) {
        throw new Error("Please fill in all required fields");
      }

      if (name.length < 2) {
        throw new Error("Event name must be at least 2 characters");
      }

      if (name.length > 255) {
        throw new Error("Event name must be less than 255 characters");
      }

      if (description && description.length > 5000) {
        throw new Error("Description must be less than 5000 characters");
      }

      // Combine date and time
      let startDateTime, endDateTime;

      try {
        startDateTime = new Date(`${startDate}T${startTime}`);
        if (isNaN(startDateTime.getTime())) {
          throw new Error("Invalid start date/time format");
        }
      } catch (e) {
        throw new Error("Invalid start date/time format");
      }

      try {
        endDateTime = new Date(`${endDate}T${endTime}`);
        if (isNaN(endDateTime.getTime())) {
          throw new Error("Invalid end date/time format");
        }
      } catch (e) {
        throw new Error("Invalid end date/time format");
      }

      // Validate dates
      if (endDateTime <= startDateTime) {
        throw new Error("End time must be after start time");
      }

      // Validate location
      if (!isOnline && location && location.length > 255) {
        throw new Error("Location must be less than 255 characters");
      }

      // Validate online link if it's an online event
      if (isOnline && onlineLink) {
        try {
          // Check if the link has a valid URL format
          new URL(onlineLink);
        } catch (e) {
          throw new Error("Please enter a valid URL for the online event link");
        }
      }

      // Validate category
      if (category && category.length > 100) {
        throw new Error("Category must be less than 100 characters");
      }

      // Upload cover image if provided
      let coverImageUrl = currentCoverImage;
      if (coverImage) {
        try {
          const uploadedImages = await uploadMultipleToCloudinary([coverImage]);
          if (uploadedImages.length > 0) {
            coverImageUrl = uploadedImages[0];
          }
        } catch (e) {
          console.error("Error uploading image:", e);
          throw new Error("Failed to upload cover image. Please try again.");
        }
      }

      console.log(`Updating event with ID: ${eventId}`);
      // Update event
      const response = await fetch(`/api/events/${eventId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description: description || undefined,
          startTime: startDateTime.toISOString(),
          endTime: endDateTime.toISOString(),
          location: isOnline ? null : (location || undefined),
          isOnline,
          onlineLink: isOnline && onlineLink ? onlineLink : null,
          visibility,
          category: category || null,
          coverImage: coverImageUrl,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (errorData.formattedErrors && Array.isArray(errorData.formattedErrors)) {
          // Display formatted validation errors
          throw new Error(`Validation errors: ${errorData.formattedErrors.join(', ')}`);
        } else {
          throw new Error(errorData.message || "Failed to update event");
        }
      }

      console.log("Event updated successfully");
      // Navigate back to the event page
      router.push(`/events/${eventId}`);
      router.refresh();
    } catch (err) {
      console.error("Error updating event:", err);
      setError(err instanceof Error ? err.message : "Failed to update event");
      window.scrollTo(0, 0);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="flex justify-center py-20">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error && !event) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-red-50 p-6 text-red-800">
            <h2 className="text-lg font-semibold">Error</h2>
            <p className="mt-2">{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => router.push("/events")}
            >
              <ChevronLeftIcon className="h-5 w-5 mr-1" />
              Back to Events
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(`/events/${eventId}`)}
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Back to Event
          </Button>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Edit Event</h1>
          </div>

          <div className="p-6">
            {error && (
              <div className="mb-6 rounded-md bg-red-50 p-4 text-sm text-red-700">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Event Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Event Name <span className="text-red-500">*</span>
                </label>
                <Input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Give your event a name"
                  required
                  className="mt-1"
                />
              </div>

              {/* Cover Image */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Cover Image
                </label>
                {currentCoverImage && !coverImage && (
                  <div className="mt-1 mb-3">
                    <div className="relative h-40 w-full rounded-md overflow-hidden">
                      <Image
                        src={currentCoverImage}
                        alt="Current cover image"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => setCurrentCoverImage(null)}
                      className="mt-2 text-sm text-red-600 hover:text-red-500"
                    >
                      Remove current image
                    </button>
                  </div>
                )}
                <div
                  {...getRootProps()}
                  className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6"
                >
                  <div className="space-y-1 text-center">
                    <input {...getInputProps()} />
                    {coverImage ? (
                      <div>
                        <p className="text-sm text-gray-600">{coverImage.name}</p>
                        <p className="text-xs text-gray-500">
                          {(coverImage.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            setCoverImage(null);
                          }}
                          className="mt-2 text-sm text-red-600 hover:text-red-500"
                        >
                          Remove
                        </button>
                      </div>
                    ) : (
                      <>
                        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="text-xs text-gray-500">
                          PNG, JPG, GIF up to 10MB
                        </p>
                        <p className="text-sm text-gray-600">
                          {currentCoverImage
                            ? "Click to change cover image"
                            : "Drag and drop or click to upload"}
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Date and Time */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                    Start Date <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1 flex items-center">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <Input
                      id="startDate"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      required
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="startTime" className="block text-sm font-medium text-gray-700">
                    Start Time <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="startTime"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                    End Date <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1 flex items-center">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <Input
                      id="endDate"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      required
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="endTime" className="block text-sm font-medium text-gray-700">
                    End Time <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="endTime"
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Location */}
              <div>
                <div className="flex items-center justify-between">
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                    Location
                  </label>
                  <div className="flex items-center">
                    <input
                      id="isOnline"
                      type="checkbox"
                      checked={isOnline}
                      onChange={(e) => setIsOnline(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="isOnline" className="ml-2 text-sm text-gray-600">
                      This is an online event
                    </label>
                  </div>
                </div>
                {isOnline ? (
                  <div className="mt-1">
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-2" />
                      <Input
                        id="onlineLink"
                        type="url"
                        value={onlineLink}
                        onChange={(e) => setOnlineLink(e.target.value)}
                        placeholder="Add a link to your online event"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="mt-1">
                    <div className="flex items-center">
                      <MapPinIcon className="h-5 w-5 text-gray-400 mr-2" />
                      <Input
                        id="location"
                        type="text"
                        value={location}
                        onChange={(e) => setLocation(e.target.value)}
                        placeholder="Add a venue or address"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={4}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  placeholder="Tell people what your event is about"
                />
              </div>

              {/* Category and Visibility */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                    Category
                  </label>
                  <select
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="">Select a category</option>
                    {eventCategories.map((cat) => (
                      <option key={cat} value={cat}>
                        {cat}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="visibility" className="block text-sm font-medium text-gray-700">
                    Who can see your event?
                  </label>
                  <div className="mt-1 space-y-2">
                    <div className="flex items-center">
                      <input
                        id="visibility-public"
                        name="visibility"
                        type="radio"
                        checked={visibility === "public"}
                        onChange={() => setVisibility("public")}
                        className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="visibility-public" className="ml-2 flex items-center text-sm text-gray-700">
                        <GlobeAltIcon className="h-4 w-4 mr-1" />
                        Public - Anyone can see this event
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="visibility-friends"
                        name="visibility"
                        type="radio"
                        checked={visibility === "friends"}
                        onChange={() => setVisibility("friends")}
                        className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="visibility-friends" className="ml-2 flex items-center text-sm text-gray-700">
                        <UsersIcon className="h-4 w-4 mr-1" />
                        Friends - Only your friends can see this event
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="visibility-private"
                        name="visibility"
                        type="radio"
                        checked={visibility === "private"}
                        onChange={() => setVisibility("private")}
                        className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="visibility-private" className="ml-2 flex items-center text-sm text-gray-700">
                        <LockClosedIcon className="h-4 w-4 mr-1" />
                        Private - Only people you invite can see this event
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push(`/events/${eventId}`)}
                  disabled={isSubmitting}
                  className="mr-3"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
