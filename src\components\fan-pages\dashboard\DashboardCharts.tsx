"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  UsersIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon
} from "@heroicons/react/24/outline";

interface AnalyticsData {
  followerGrowth: Array<{
    date: string;
    displayDate: string;
    newFollowers: number;
    totalFollowers: number;
  }>;
  postEngagement: Array<{
    date: string;
    displayDate: string;
    posts: number;
    likes: number;
    comments: number;
    engagement: number;
  }>;
  topPosts: Array<{
    id: string;
    content: string;
    createdAt: string;
    likes: number;
    comments: number;
    totalEngagement: number;
  }>;
  summary: {
    totalNewFollowers: number;
    totalPosts: number;
    totalLikes: number;
    totalComments: number;
    totalEngagement: number;
    averageEngagementPerPost: number;
  };
}

interface DashboardChartsProps {
  pageId: string;
}

export function DashboardCharts({ pageId }: DashboardChartsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("30d");

  useEffect(() => {
    fetchAnalytics();
  }, [pageId, timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/fan-pages/${pageId}/dashboard/analytics?range=${timeRange}`);

      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const timeRangeOptions = [
    { value: "7d", label: "7 Days" },
    { value: "30d", label: "30 Days" },
    { value: "90d", label: "90 Days" },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">{error || 'No analytics data available'}</p>
          <Button onClick={fetchAnalytics} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const maxFollowers = Math.max(...analytics.followerGrowth.map(item => item.totalFollowers));
  const maxEngagement = Math.max(...analytics.postEngagement.map(item => item.engagement));

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Analytics Dashboard</h2>
        <div className="flex space-x-2">
          {timeRangeOptions.map((option) => (
            <Button
              key={option.value}
              variant={timeRange === option.value ? "primary" : "outline"}
              size="sm"
              onClick={() => setTimeRange(option.value)}
            >
              {option.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <UsersIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">New Followers</p>
              <p className="text-xl font-bold text-gray-900">{analytics.summary.totalNewFollowers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Posts Created</p>
              <p className="text-xl font-bold text-gray-900">{analytics.summary.totalPosts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <HeartIcon className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Likes</p>
              <p className="text-xl font-bold text-gray-900">{analytics.summary.totalLikes}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ChatBubbleOvalLeftIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Avg. Engagement</p>
              <p className="text-xl font-bold text-gray-900">{analytics.summary.averageEngagementPerPost}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Follower Growth Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Follower Growth</h3>
          <div className="h-64 relative">
            <svg className="w-full h-full" viewBox="0 0 400 200">
              {/* Grid lines */}
              {[0, 1, 2, 3, 4].map((i) => (
                <line
                  key={i}
                  x1="40"
                  y1={40 + (i * 32)}
                  x2="380"
                  y2={40 + (i * 32)}
                  stroke="#f3f4f6"
                  strokeWidth="1"
                />
              ))}

              {/* Y-axis labels */}
              {[0, 1, 2, 3, 4].map((i) => (
                <text
                  key={i}
                  x="35"
                  y={45 + (i * 32)}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {Math.round(maxFollowers - (i * maxFollowers / 4))}
                </text>
              ))}

              {/* Line chart */}
              <polyline
                fill="none"
                stroke="#3b82f6"
                strokeWidth="2"
                points={analytics.followerGrowth.map((item, index) => {
                  const x = 40 + (index * (340 / (analytics.followerGrowth.length - 1)));
                  const y = 40 + (128 * (1 - item.totalFollowers / maxFollowers));
                  return `${x},${y}`;
                }).join(' ')}
              />

              {/* Data points */}
              {analytics.followerGrowth.map((item, index) => {
                const x = 40 + (index * (340 / (analytics.followerGrowth.length - 1)));
                const y = 40 + (128 * (1 - item.totalFollowers / maxFollowers));
                return (
                  <circle
                    key={index}
                    cx={x}
                    cy={y}
                    r="3"
                    fill="#3b82f6"
                  />
                );
              })}

              {/* X-axis labels */}
              {analytics.followerGrowth.filter((_, index) => index % Math.ceil(analytics.followerGrowth.length / 5) === 0).map((item, index) => {
                const originalIndex = analytics.followerGrowth.findIndex(d => d.date === item.date);
                const x = 40 + (originalIndex * (340 / (analytics.followerGrowth.length - 1)));
                return (
                  <text
                    key={index}
                    x={x}
                    y="185"
                    textAnchor="middle"
                    className="text-xs fill-gray-500"
                  >
                    {item.displayDate}
                  </text>
                );
              })}
            </svg>
          </div>
        </div>

        {/* Engagement Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Engagement</h3>
          <div className="h-64 relative">
            <svg className="w-full h-full" viewBox="0 0 400 200">
              {/* Grid lines */}
              {[0, 1, 2, 3, 4].map((i) => (
                <line
                  key={i}
                  x1="40"
                  y1={40 + (i * 32)}
                  x2="380"
                  y2={40 + (i * 32)}
                  stroke="#f3f4f6"
                  strokeWidth="1"
                />
              ))}

              {/* Y-axis labels */}
              {[0, 1, 2, 3, 4].map((i) => (
                <text
                  key={i}
                  x="35"
                  y={45 + (i * 32)}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {Math.round(maxEngagement - (i * maxEngagement / 4))}
                </text>
              ))}

              {/* Bar chart */}
              {analytics.postEngagement.map((item, index) => {
                const x = 40 + (index * (340 / analytics.postEngagement.length));
                const barWidth = 340 / analytics.postEngagement.length * 0.8;
                const height = maxEngagement > 0 ? (128 * item.engagement / maxEngagement) : 0;
                const y = 168 - height;

                return (
                  <rect
                    key={index}
                    x={x - barWidth / 2}
                    y={y}
                    width={barWidth}
                    height={height}
                    fill="#10b981"
                    opacity="0.8"
                  />
                );
              })}

              {/* X-axis labels */}
              {analytics.postEngagement.filter((_, index) => index % Math.ceil(analytics.postEngagement.length / 5) === 0).map((item, index) => {
                const originalIndex = analytics.postEngagement.findIndex(d => d.date === item.date);
                const x = 40 + (originalIndex * (340 / analytics.postEngagement.length));
                return (
                  <text
                    key={index}
                    x={x}
                    y="185"
                    textAnchor="middle"
                    className="text-xs fill-gray-500"
                  >
                    {item.displayDate}
                  </text>
                );
              })}
            </svg>
          </div>
        </div>
      </div>

      {/* Top Posts */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Posts</h3>
        {analytics.topPosts.length === 0 ? (
          <p className="text-gray-500 text-center py-8">No posts found for this period</p>
        ) : (
          <div className="space-y-4">
            {analytics.topPosts.map((post, index) => (
              <div key={post.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-gray-900">#{index + 1}</span>
                    <span className="text-xs text-gray-500">
                      {new Date(post.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{post.content}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span className="flex items-center">
                      <HeartIcon className="h-4 w-4 mr-1 text-red-500" />
                      {post.likes} likes
                    </span>
                    <span className="flex items-center">
                      <ChatBubbleOvalLeftIcon className="h-4 w-4 mr-1 text-blue-500" />
                      {post.comments} comments
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">{post.totalEngagement}</div>
                  <div className="text-xs text-gray-500">total engagement</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
