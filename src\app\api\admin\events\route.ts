import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventAttendees, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, desc, asc, like, or, sql, count } from "drizzle-orm";

const eventSchema = z.object({
  name: z.string().min(2).max(255),
  description: z.string().max(5000).optional().nullable(),
  startTime: z.string().datetime(),
  endTime: z.string().datetime(),
  location: z.string().max(255).optional().nullable(),
  isOnline: z.boolean().optional().default(false),
  onlineLink: z.string().url().optional().nullable(),
  visibility: z.enum(["public", "private", "friends"]).default("public"),
  category: z.string().max(100).optional().nullable(),
  coverImage: z.string().url().optional().nullable(),
  hostId: z.string().optional(), // Optional for admin to set a different host
});

const querySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(10),
  search: z.string().optional().nullable(),
  visibility: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  dateFrom: z.string().optional().nullable(),
  dateTo: z.string().optional().nullable(),
  sort: z.string().optional().default("startTime"),
  order: z.string().optional().default("desc"),
});

// Get all events with admin filtering capabilities
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const query = querySchema.parse({
      page: parseInt(url.searchParams.get("page") || "1"),
      limit: parseInt(url.searchParams.get("limit") || "10"),
      search: url.searchParams.get("search"),
      visibility: url.searchParams.get("visibility"),
      category: url.searchParams.get("category"),
      status: url.searchParams.get("status"),
      dateFrom: url.searchParams.get("dateFrom"),
      dateTo: url.searchParams.get("dateTo"),
      sort: url.searchParams.get("sort") || "startTime",
      order: url.searchParams.get("order") || "desc",
    });

    // Build where conditions
    const whereConditions = [];

    // Apply search filter
    if (query.search) {
      whereConditions.push(
        or(
          like(events.name, `%${query.search}%`),
          like(events.description, `%${query.search}%`)
        )
      );
    }

    // Apply visibility filter
    if (query.visibility && query.visibility !== "all") {
      whereConditions.push(eq(events.visibility, query.visibility as any));
    }

    // Apply category filter
    if (query.category && query.category !== "all") {
      whereConditions.push(eq(events.category, query.category));
    }

    // Apply date range filters
    if (query.dateFrom) {
      const dateFrom = new Date(query.dateFrom);
      whereConditions.push(sql`${events.startTime} >= ${dateFrom}`);
    }

    if (query.dateTo) {
      const dateTo = new Date(query.dateTo);
      whereConditions.push(sql`${events.endTime} <= ${dateTo}`);
    }

    // Apply status filter
    if (query.status && query.status !== "all") {
      const now = new Date();

      if (query.status === "upcoming") {
        whereConditions.push(sql`${events.startTime} > ${now}`);
      } else if (query.status === "ongoing") {
        whereConditions.push(
          and(
            sql`${events.startTime} <= ${now}`,
            sql`${events.endTime} >= ${now}`
          )
        );
      } else if (query.status === "past") {
        whereConditions.push(sql`${events.endTime} < ${now}`);
      }
    }

    // Build the query
    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Apply sorting
    const sortField = query.sort === "name" ? events.name :
                     query.sort === "startTime" ? events.startTime :
                     query.sort === "location" ? events.location :
                     query.sort === "visibility" ? events.visibility :
                     query.sort === "createdAt" ? events.createdAt :
                     events.startTime;

    const orderByClause = query.order === "asc" ? asc(sortField) : desc(sortField);

    // Build complete query using query API
    const allEvents = await db.query.events.findMany({
      where: whereClause,
      orderBy: orderByClause,
    });
    const totalCount = allEvents.length;
    const totalPages = Math.ceil(totalCount / query.limit);

    // Apply pagination
    const paginatedEvents = allEvents.slice((query.page - 1) * query.limit, query.page * query.limit);

    // Format the response with empty attendee counts
    const formattedEvents = await Promise.all(paginatedEvents.map(async (event) => {
      // Fetch host data separately to avoid collation issues
      let hostData = null;
      try {
        hostData = await db.query.users.findFirst({
          where: eq(users.id, event.hostId),
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        });
      } catch (error) {
        console.error("Error fetching host data:", error);
      }

      return {
        ...event,
        host: hostData,
        attendeeCounts: {
          going: 0,
          interested: 0,
          not_going: 0
        },
      };
    }));

    return NextResponse.json({
      events: formattedEvents,
      page: query.page,
      limit: query.limit,
      totalCount,
      totalPages,
    });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { message: "Failed to fetch events" },
      { status: 500 }
    );
  }
}

// Create a new event as admin
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = eventSchema.parse(body);

    // If hostId is provided, check if the user exists
    let hostId = session.user.id;
    if (validatedData.hostId) {
      const userExists = await db.query.users.findFirst({
        where: eq(users.id, validatedData.hostId),
      });

      if (!userExists) {
        return NextResponse.json(
          { message: "Host user not found" },
          { status: 404 }
        );
      }

      hostId = validatedData.hostId;
    }

    // Generate a new UUID for the event
    const eventId = uuidv4();

    // Create the event
    await db.insert(events).values({
      id: eventId,
      name: validatedData.name,
      description: validatedData.description || null,
      startTime: new Date(validatedData.startTime),
      endTime: new Date(validatedData.endTime),
      location: validatedData.location || null,
      isOnline: validatedData.isOnline || false,
      onlineLink: validatedData.onlineLink || null,
      visibility: validatedData.visibility,
      category: validatedData.category || null,
      coverImage: validatedData.coverImage || null,
      hostId: hostId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Fetch the created event
    const createdEvent = await db.query.events.findFirst({
      where: eq(events.id, eventId),
      with: {
        host: true,
      },
    });

    return NextResponse.json(createdEvent);
  } catch (error) {
    console.error("Error creating event:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Failed to create event" },
      { status: 500 }
    );
  }
}
