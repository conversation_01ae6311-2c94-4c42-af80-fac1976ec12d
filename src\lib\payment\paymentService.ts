import { WalletService } from "@/lib/wallet/walletService";
import { SubscriptionService } from "@/lib/subscription/subscriptionService";

export interface PaymentRequest {
  amount: string;
  currency: string;
  gatewayId: string;
  userId: string;
  metadata?: any;
}

export interface PaymentResponse {
  success: boolean;
  paymentUrl?: string;
  transactionId?: string;
  message?: string;
  error?: string;
  walletPayment?: boolean;
}

export class PaymentService {
  // Process payment based on gateway type
  static async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Get gateway configuration
      const gateway = await WalletService.getPaymentGatewayById(request.gatewayId);
      
      if (!gateway || !gateway.isActive) {
        return {
          success: false,
          error: 'Payment gateway not available'
        };
      }

      // Route to appropriate payment processor
      switch (gateway.type) {
        case 'stripe':
          return await this.processStripePayment(request, gateway);
        case 'paypal':
          return await this.processPayPalPayment(request, gateway);
        case 'uddoktapay':
          return await this.processUddoktaPayPayment(request, gateway);
        case 'manual':
          return await this.processManualPayment(request, gateway);
        case 'wallet':
          return await this.processWalletPayment(request, gateway);
        default:
          return {
            success: false,
            error: 'Unsupported payment gateway'
          };
      }
    } catch (error: any) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        error: error.message || 'Payment processing failed'
      };
    }
  }

  // Stripe payment processing
  private static async processStripePayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      // This is a placeholder for Stripe integration
      // In a real implementation, you would:
      // 1. Create a Stripe payment intent
      // 2. Return the client secret for frontend processing
      
      const { config } = gateway;
      
      if (!config.secretKey) {
        return {
          success: false,
          error: 'Stripe configuration incomplete'
        };
      }

      // Placeholder response
      return {
        success: true,
        paymentUrl: `https://checkout.stripe.com/pay/demo?amount=${request.amount}`,
        transactionId: `stripe_${Date.now()}`,
        message: 'Redirecting to Stripe checkout'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Stripe payment failed'
      };
    }
  }

  // PayPal payment processing
  private static async processPayPalPayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      const { config } = gateway;
      
      if (!config.clientId || !config.clientSecret) {
        return {
          success: false,
          error: 'PayPal configuration incomplete'
        };
      }

      // Placeholder response
      return {
        success: true,
        paymentUrl: `https://www.${config.mode === 'live' ? '' : 'sandbox.'}paypal.com/checkoutnow?token=demo_${Date.now()}`,
        transactionId: `paypal_${Date.now()}`,
        message: 'Redirecting to PayPal checkout'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'PayPal payment failed'
      };
    }
  }

  // UddoktaPay payment processing
  private static async processUddoktaPayPayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      const { config } = gateway;
      
      if (!config.apiKey || !config.storeId) {
        return {
          success: false,
          error: 'UddoktaPay configuration incomplete'
        };
      }

      // Placeholder for UddoktaPay API integration
      // In a real implementation, you would make an API call to UddoktaPay
      
      return {
        success: true,
        paymentUrl: `${config.apiUrl}/payment?store_id=${config.storeId}&amount=${request.amount}`,
        transactionId: `uddokta_${Date.now()}`,
        message: 'Redirecting to UddoktaPay checkout'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'UddoktaPay payment failed'
      };
    }
  }

  // Manual payment processing
  private static async processManualPayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      const { config } = gateway;
      
      // For manual payments, we just return instructions
      return {
        success: true,
        message: 'Manual payment instructions provided',
        transactionId: `manual_${Date.now()}`,
        // In a real app, you might redirect to a page with payment instructions
        paymentUrl: `/payment/manual?gateway=${gateway.id}&amount=${request.amount}`
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Manual payment setup failed'
      };
    }
  }

  // Wallet payment processing
  private static async processWalletPayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      // Get user's wallet balance
      const walletBalance = await WalletService.getOrCreateWallet(request.userId);
      const currentBalance = parseFloat(walletBalance.generalBalance);
      const paymentAmount = parseFloat(request.amount);

      // Check if user has sufficient balance
      if (currentBalance < paymentAmount) {
        return {
          success: false,
          error: `Insufficient wallet balance. Available: $${currentBalance.toFixed(2)}, Required: $${paymentAmount.toFixed(2)}`
        };
      }

      // Process wallet payment for subscription
      if (request.metadata?.type === 'subscription_payment') {
        const { subscriptionId, transactionId, planId } = request.metadata;

        // Create wallet transaction for subscription payment
        const walletTransactionId = await WalletService.createTransaction(request.userId, {
          type: 'internal_transfer',
          amount: request.amount,
          walletType: 'general',
          reference: `subscription_payment_${subscriptionId}`,
          note: `Subscription payment for plan ${planId}`,
          metadata: {
            subscriptionId,
            transactionId,
            planId,
            type: 'subscription_payment'
          }
        });

        // Deduct amount from wallet
        await WalletService.updateWalletBalance(
          request.userId,
          'general',
          request.amount,
          'subtract'
        );

        // Mark wallet transaction as completed
        await WalletService.updateTransactionStatus(walletTransactionId, 'completed');

        // Activate subscription immediately since payment is completed
        if (subscriptionId) {
          await SubscriptionService.activateSubscription(subscriptionId, transactionId);
        }

        return {
          success: true,
          message: 'Payment completed successfully using wallet balance',
          transactionId: walletTransactionId,
          walletPayment: true
        };
      }

      // For other wallet payments (deposits, etc.)
      const walletTransactionId = await WalletService.createTransaction(request.userId, {
        type: 'internal_transfer',
        amount: request.amount,
        walletType: 'general',
        reference: `wallet_payment_${Date.now()}`,
        note: 'Wallet payment',
        metadata: request.metadata
      });

      // Deduct amount from wallet
      await WalletService.updateWalletBalance(
        request.userId,
        'general',
        request.amount,
        'subtract'
      );

      // Mark transaction as completed
      await WalletService.updateTransactionStatus(walletTransactionId, 'completed');

      return {
        success: true,
        message: 'Payment completed successfully using wallet balance',
        transactionId: walletTransactionId,
        walletPayment: true
      };

    } catch (error: any) {
      console.error('Wallet payment error:', error);
      return {
        success: false,
        error: error.message || 'Wallet payment failed'
      };
    }
  }

  // Verify payment (webhook handler)
  static async verifyPayment(gatewayType: string, payload: any): Promise<boolean> {
    try {
      switch (gatewayType) {
        case 'stripe':
          return await this.verifyStripePayment(payload);
        case 'paypal':
          return await this.verifyPayPalPayment(payload);
        case 'uddoktapay':
          return await this.verifyUddoktaPayPayment(payload);
        case 'manual':
          return await this.verifyManualPayment(payload);
        case 'wallet':
          return await this.verifyWalletPayment(payload);
        default:
          return false;
      }
    } catch (error) {
      console.error('Payment verification error:', error);
      return false;
    }
  }

  private static async verifyStripePayment(payload: any): Promise<boolean> {
    // Implement Stripe webhook verification
    return true; // Placeholder
  }

  private static async verifyPayPalPayment(payload: any): Promise<boolean> {
    // Implement PayPal webhook verification
    return true; // Placeholder
  }

  private static async verifyUddoktaPayPayment(payload: any): Promise<boolean> {
    // Implement UddoktaPay webhook verification
    return true; // Placeholder
  }

  private static async verifyManualPayment(payload: any): Promise<boolean> {
    // Manual payments require admin approval
    return false; // Always requires manual verification
  }

  private static async verifyWalletPayment(payload: any): Promise<boolean> {
    // Wallet payments are processed immediately, so they're always verified
    return true;
  }
}
