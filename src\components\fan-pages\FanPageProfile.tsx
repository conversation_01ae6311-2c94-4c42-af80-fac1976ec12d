"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { FanPageHeader } from "./FanPageHeader";
import { FanPageTabs } from "./FanPageTabs";
import { FanPagePosts } from "./FanPagePosts";
import { PhotoUploadModal } from "./PhotoUploadModal";
import { FanPagePostModal } from "./FanPagePostModal";

import { useFanPageFollow } from "@/hooks/useFanPageFollow";
import { toast } from "react-hot-toast";
import { OptimizedImage } from "@/components/ui/OptimizedImage";

interface FanPageData {
  id: string;
  name: string;
  username: string;
  category: string;
  description: string | null;
  profileImage: string | null;
  coverImage: string | null;
  website: string | null;
  email: string | null;
  phone: string | null;
  location: string | null;
  isVerified: boolean;
  followerCount: number;
  postCount: number;
  createdAt: Date;
  owner?: {
    id: string;
    name: string | null;
    image: string | null;
  };
  isFollowing: boolean;
  isOwner: boolean;
  userRole: string | null;
  ownerId?: string;
}

interface FanPageProfileProps {
  page: FanPageData;
  onPageUpdate?: (updatedPage: Partial<FanPageData>) => void;
}

export function FanPageProfile({ page, onPageUpdate }: FanPageProfileProps) {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("posts");
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [uploadType, setUploadType] = useState<'profile' | 'cover'>('profile');
  const [postModalOpen, setPostModalOpen] = useState(false);
  const [pageData, setPageData] = useState(page);
  const [postsRefreshKey, setPostsRefreshKey] = useState(0);

  // Use custom hook for follow functionality
  const {
    isFollowing,
    followerCount,
    isLoading: isFollowLoading,
    toggleFollow,
  } = useFanPageFollow({
    pageId: page.id,
    pageName: page.name,
    initialIsFollowing: page.isFollowing,
    initialFollowerCount: page.followerCount,
    onUpdate: onPageUpdate,
    useQuickApi: true, // Enable fast API for main page
  });

  // Update page data when page prop changes
  useEffect(() => {
    setPageData(page);
  }, [page]);

  const handleEditPage = () => {
    // Navigate to edit page
    window.location.href = `/pages/${pageData.username}/edit`;
  };

  const handleUploadCover = () => {
    setUploadType('cover');
    setUploadModalOpen(true);
  };

  const handleUploadProfile = () => {
    setUploadType('profile');
    setUploadModalOpen(true);
  };

  const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', uploadType);

    const response = await fetch('/api/upload/fan-page-image', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      let errorMessage = `Failed to upload ${uploadType} image`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch (jsonError) {
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    return data.url;
  };

  const handlePhotoUpload = async (file: File) => {
    try {
      // Upload the image
      const imageUrl = await uploadImage(file);

      // Update the page data
      const updateData = {
        [uploadType === 'profile' ? 'profileImage' : 'coverImage']: imageUrl,
      };

      const response = await fetch(`/api/fan-pages/${pageData.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('Failed to update page');
      }

      const updatedPage = await response.json();

      // Update local state
      setPageData(prev => ({
        ...prev,
        ...updateData,
      }));

      // Call parent update callback
      if (onPageUpdate) {
        onPageUpdate(updateData);
      }

      toast.success(`${uploadType === 'profile' ? 'Profile' : 'Cover'} photo updated successfully!`);
    } catch (error) {
      console.error('Photo upload error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload photo');
      throw error;
    }
  };

  const handleCreatePost = () => {
    setPostModalOpen(true);
  };

  const handlePostCreated = () => {
    // Refresh posts by updating the key
    setPostsRefreshKey(prev => prev + 1);
  };

  const canPost = pageData.isOwner || pageData.userRole === 'admin' || pageData.userRole === 'editor';

  const pageWithUpdatedCounts = {
    ...pageData,
    followerCount,
    isFollowing,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Page Header */}
      <FanPageHeader
        page={pageWithUpdatedCounts}
        isFollowing={isFollowing}
        isOwner={pageData.isOwner}
        userRole={pageData.userRole}
        onFollowToggle={toggleFollow}
        onEditPage={handleEditPage}
        onUploadCover={handleUploadCover}
        onUploadProfile={handleUploadProfile}
        isFollowLoading={isFollowLoading}
      />

      {/* Navigation Tabs */}
      <FanPageTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        isOwner={pageData.isOwner}
        userRole={pageData.userRole}
        postCount={pageData.postCount}
      />

      {/* Tab Content */}
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === "posts" && (
              <FanPagePosts
                pageId={pageData.id}
                canPost={canPost}
                onCreatePost={handleCreatePost}
                refreshKey={postsRefreshKey}
                pageOwnerId={pageData.ownerId}
              />
            )}

            {activeTab === "about" && (
              <FanPageAbout page={pageData} />
            )}

            {activeTab === "photos" && (
              <FanPagePhotos pageId={pageData.id} />
            )}

            {activeTab === "events" && (
              <FanPageEvents pageId={pageData.id} canCreate={canPost} />
            )}

            {activeTab === "products" && (
              <FanPageProducts pageId={pageData.id} canCreate={canPost} />
            )}

            {activeTab === "insights" && (pageData.isOwner || pageData.userRole === 'admin') && (
              <FanPageInsights pageId={pageData.id} />
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-2">
            <FanPageSidebar page={pageData} />
          </div>
        </div>
      </div>

      {/* Photo Upload Modal */}
      <PhotoUploadModal
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        onUpload={handlePhotoUpload}
        type={uploadType}
        currentImage={uploadType === 'profile' ? pageData.profileImage : pageData.coverImage}
        title={`Upload ${uploadType === 'profile' ? 'Profile' : 'Cover'} Photo`}
      />

      {/* Post Creation Modal */}
      <FanPagePostModal
        isOpen={postModalOpen}
        onClose={() => setPostModalOpen(false)}
        onPostCreated={handlePostCreated}
        pageId={pageData.id}
        pageName={pageData.name}
        pageImage={pageData.profileImage}
      />
    </div>
  );
}

// Placeholder components for tabs that aren't implemented yet
function FanPageAbout({ page }: { page: FanPageData }) {
  return (
    <div className="space-y-6">
      {/* Description Section */}
      {page.description && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
          <div className="flex items-center mb-6">
            <div className="w-3 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-4"></div>
            <h2 className="text-2xl font-bold text-gray-900">About This Page</h2>
          </div>
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-700 leading-relaxed text-lg">{page.description}</p>
          </div>
        </div>
      )}

      {/* Page Details */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
        <div className="flex items-center mb-6">
          <div className="w-3 h-8 bg-gradient-to-b from-green-500 to-emerald-600 rounded-full mr-4"></div>
          <h2 className="text-2xl font-bold text-gray-900">Page Details</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Category */}
          <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-bold text-gray-900 text-lg">Category</h3>
            </div>
            <p className="text-gray-700 font-semibold capitalize text-lg">{page.category.replace('_', ' ')}</p>
          </div>

          {/* Created Date */}
          <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-bold text-gray-900 text-lg">Created</h3>
            </div>
            <p className="text-gray-700 font-semibold text-lg">{new Date(page.createdAt).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}</p>
          </div>
        </div>
      </div>

      {/* Contact Information */}
      {(page.location || page.website || page.email || page.phone) && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
          <div className="flex items-center mb-6">
            <div className="w-3 h-8 bg-gradient-to-b from-purple-500 to-pink-600 rounded-full mr-4"></div>
            <h2 className="text-2xl font-bold text-gray-900">Contact Information</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {page.location && (
              <div className="p-6 bg-gradient-to-r from-red-50 to-pink-50 rounded-xl border border-red-100">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-gray-900 text-lg">Location</h3>
                </div>
                <p className="text-gray-700 font-semibold text-lg">{page.location}</p>
              </div>
            )}

            {page.website && (
              <div className="p-6 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-100">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-gray-900 text-lg">Website</h3>
                </div>
                <a
                  href={page.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 transition-colors font-semibold text-lg hover:underline"
                >
                  {page.website.replace(/^https?:\/\//, '')}
                </a>
              </div>
            )}

            {page.email && (
              <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-gray-900 text-lg">Email</h3>
                </div>
                <a
                  href={`mailto:${page.email}`}
                  className="text-green-600 hover:text-green-800 transition-colors font-semibold text-lg hover:underline"
                >
                  {page.email}
                </a>
              </div>
            )}

            {page.phone && (
              <div className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-gray-900 text-lg">Phone</h3>
                </div>
                <a
                  href={`tel:${page.phone}`}
                  className="text-purple-600 hover:text-purple-800 transition-colors font-semibold text-lg hover:underline"
                >
                  {page.phone}
                </a>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

function FanPagePhotos({ pageId }: { pageId: string }) {
  const [photos, setPhotos] = useState<any[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  const fetchPhotos = async (pageNum: number = 1, append: boolean = false) => {
    try {
      if (pageNum === 1) setLoading(true);
      else setLoadingMore(true);
      setError(null);

      const response = await fetch(`/api/fan-pages/${pageId}/photos?page=${pageNum}&limit=20`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `Failed to fetch photos`);
      }

      const data = await response.json();

      if (append) {
        setPhotos(prev => [...prev, ...data.photos]);
        setPosts(prev => [...prev, ...data.posts]);
      } else {
        setPhotos(data.photos);
        setPosts(data.posts);
      }

      setHasMore(data.pagination?.page < data.pagination?.totalPages);
      setPage(pageNum);

    } catch (error) {
      console.error("Error fetching photos:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch photos");
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    if (pageId && pageId.trim() !== '') {
      fetchPhotos();
    }
  }, [pageId]);

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      fetchPhotos(page + 1, true);
    }
  };



  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
        <div className="flex items-center mb-6">
          <div className="w-3 h-8 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full mr-4"></div>
          <h2 className="text-2xl font-bold text-gray-900">Photos</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
        <div className="flex items-center mb-6">
          <div className="w-3 h-8 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full mr-4"></div>
          <h2 className="text-2xl font-bold text-gray-900">Photos</h2>
        </div>
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Error loading photos</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => fetchPhotos()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-3 h-8 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full mr-4"></div>
            <h2 className="text-2xl font-bold text-gray-900">Photos</h2>
          </div>
          {photos.length > 0 && (
            <span className="text-sm text-gray-500">{photos.length} photos</span>
          )}
        </div>

        {photos.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gradient-to-r from-pink-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-12 h-12 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">No photos yet</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">Photos from posts will appear here. Start by creating posts with images to build your photo gallery.</p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Navigate to posts tab to create a post
                  const postsTab = document.querySelector('[data-tab="posts"]') as HTMLElement;
                  if (postsTab) {
                    postsTab.click();
                  }
                }}
                className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 font-medium"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Create Post
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-4">
              {photos.map((photo, index) => (
                <div
                  key={photo.id}
                  className="group relative aspect-square overflow-hidden rounded-xl bg-gray-100 cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"

                >
                  <OptimizedImage
                    src={photo.url}
                    alt={`Photo ${index + 1} from ${photo.fanPage.name}`}
                    fill
                    className="object-cover"
                  />

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300" />

                  {/* Photo info */}
                  <div className="absolute bottom-0 left-0 right-0 p-3 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                          </svg>
                          <span className="font-medium">{photo.likeCount}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                          </svg>
                          <span className="font-medium">{photo.commentCount}</span>
                        </div>
                      </div>
                      <div className="text-xs opacity-90 font-medium">
                        {new Date(photo.createdAt).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                      </div>
                    </div>
                  </div>

                  {/* View icon */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 backdrop-blur-sm">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center mt-8">
                <button
                  onClick={loadMore}
                  disabled={loadingMore}
                  className="px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingMore ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Loading...</span>
                    </div>
                  ) : (
                    'Load More Photos'
                  )}
                </button>
              </div>
            )}
          </>
        )}
      </div>


    </>
  );
}

function FanPageEvents({ pageId, canCreate }: { pageId: string; canCreate: boolean }) {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
      <div className="flex items-center mb-6">
        <div className="w-3 h-8 bg-gradient-to-b from-green-500 to-emerald-600 rounded-full mr-4"></div>
        <h2 className="text-2xl font-bold text-gray-900">Events</h2>
      </div>
      <div className="text-center py-12">
        <div className="w-20 h-20 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Events Coming Soon</h3>
        <p className="text-gray-600">Event management feature will be available soon. Create and manage events!</p>
      </div>
    </div>
  );
}

function FanPageProducts({ pageId, canCreate }: { pageId: string; canCreate: boolean }) {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
      <div className="flex items-center mb-6">
        <div className="w-3 h-8 bg-gradient-to-b from-orange-500 to-red-600 rounded-full mr-4"></div>
        <h2 className="text-2xl font-bold text-gray-900">Products</h2>
      </div>
      <div className="text-center py-12">
        <div className="w-20 h-20 bg-gradient-to-r from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-10 h-10 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM8 15a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Products Coming Soon</h3>
        <p className="text-gray-600">Product showcase feature will be available soon. Display your products!</p>
      </div>
    </div>
  );
}

function FanPageInsights({ pageId }: { pageId: string }) {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
      <div className="flex items-center mb-6">
        <div className="w-3 h-8 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full mr-4"></div>
        <h2 className="text-2xl font-bold text-gray-900">Insights & Analytics</h2>
      </div>
      <div className="text-center py-12">
        <div className="w-20 h-20 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-10 h-10 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Analytics Coming Soon</h3>
        <p className="text-gray-600">Detailed insights and analytics dashboard will be available soon!</p>
      </div>
    </div>
  );
}

function FanPagePhotosPreview({ pageId }: { pageId: string }) {
  const [photos, setPhotos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [photoStats, setPhotoStats] = useState<{ totalPhotos: number; postsWithPhotos: number } | null>(null);

  const fetchPhotos = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch photos and stats in parallel
      const [photosResponse, statsResponse] = await Promise.all([
        fetch(`/api/fan-pages/${pageId}/photos?page=1&limit=6`),
        fetch(`/api/fan-pages/${pageId}/photos/stats`)
      ]);

      if (!photosResponse.ok) {
        const errorData = await photosResponse.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || 'Failed to fetch photos');
      }

      const photosData = await photosResponse.json();
      setPhotos(photosData.photos || []);

      // Stats are optional, don't fail if they can't be fetched
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setPhotoStats(statsData);
      }

    } catch (error) {
      console.error('Error fetching photos:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch photos');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (pageId && pageId.trim() !== '') {
      fetchPhotos();
    }
  }, [pageId]);



  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 className="font-bold text-gray-900 mb-4 text-lg flex items-center">
          <div className="w-2 h-6 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full mr-3"></div>
          Photos
        </h3>
        <div className="grid grid-cols-3 gap-2">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 className="font-bold text-gray-900 mb-4 text-lg flex items-center">
          <div className="w-2 h-6 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full mr-3"></div>
          Photos
        </h3>
        <div className="text-center py-4">
          <p className="text-sm text-red-600 mb-2">{error}</p>
          <button
            onClick={fetchPhotos}
            className="text-xs text-blue-600 hover:text-blue-800 font-medium transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-bold text-gray-900 text-lg flex items-center">
            <div className="w-2 h-6 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full mr-3"></div>
            Photos
          </h3>
          {(photos.length > 0 || photoStats) && (
            <span className="text-sm text-gray-500">
              {photoStats ? `${photoStats.totalPhotos} photos` : `${photos.length}+ photos`}
            </span>
          )}
        </div>

        {photos.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gradient-to-r from-pink-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-8 h-8 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">No photos yet</h4>
            <p className="text-sm text-gray-600">Photos from posts will appear here</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-3 gap-2 mb-4">
              {photos.slice(0, 6).map((photo, index) => (
                <div
                  key={photo.id}
                  className="aspect-square relative group cursor-pointer overflow-hidden rounded-lg bg-gray-100"

                >
                  <OptimizedImage
                    src={photo.url}
                    alt={`Photo ${index + 1}`}
                    fill
                    className="object-cover transition-transform duration-200 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                    <svg className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm5 3a2 2 0 11-4 0 2 2 0 014 0zm4.5 8.5l-3-3-1.5 1.5-3-3V16h7.5z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center space-y-2">
              <button
                onClick={() => {
                  // Navigate to photos tab
                  const photosTab = document.querySelector('[data-tab="photos"]') as HTMLElement;
                  if (photosTab) {
                    photosTab.click();
                  }
                }}
                className="block w-full text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors hover:underline"
              >
                View all photos
              </button>
              {photoStats && photoStats.postsWithPhotos > 0 && (
                <p className="text-xs text-gray-500">
                  From {photoStats.postsWithPhotos} posts
                </p>
              )}
            </div>
          </>
        )}
      </div>


    </>
  );
}

function FanPageSidebar({ page }: { page: FanPageData }) {
  return (
    <div className="space-y-6">
      {/* Page Intro */}
      {page.description && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <h3 className="font-bold text-gray-900 mb-4 text-lg flex items-center">
            <div className="w-2 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-3"></div>
            About
          </h3>
          <p className="text-gray-700 leading-relaxed">{page.description}</p>
        </div>
      )}

      {/* Page Information */}
      {(page.location || page.website || page.email) && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <h3 className="font-bold text-gray-900 mb-6 text-lg flex items-center">
            <div className="w-2 h-6 bg-gradient-to-b from-green-500 to-emerald-600 rounded-full mr-3"></div>
            Contact Information
          </h3>
          <div className="space-y-4">
            {page.location && (
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Location</p>
                  <p className="font-medium text-gray-900">{page.location}</p>
                </div>
              </div>
            )}

            {page.website && (
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Website</p>
                  <a href={page.website} target="_blank" rel="noopener noreferrer" className="font-medium text-blue-600 hover:text-blue-800 transition-colors">
                    {page.website.replace(/^https?:\/\//, '')}
                  </a>
                </div>
              </div>
            )}

            {page.email && (
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <a href={`mailto:${page.email}`} className="font-medium text-green-600 hover:text-green-800 transition-colors">
                    {page.email}
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Photos Preview */}
      <FanPagePhotosPreview pageId={page.id} />

      {/* Verification Badge */}
      {page.isVerified && (
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="font-bold text-lg">Verified Page</h3>
          </div>
          <p className="text-blue-100 text-sm">This page has been verified and is authentic.</p>
        </div>
      )}
    </div>
  );
}
