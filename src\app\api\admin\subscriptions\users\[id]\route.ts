import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { userSubscriptions, subscriptionPlans, users, subscriptionTransactions } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const updateSubscriptionSchema = z.object({
  planId: z.string().optional(),
  status: z.enum(['active', 'cancelled', 'expired', 'pending', 'suspended']).optional(),
  endDate: z.string().optional(),
  nextBillingDate: z.string().optional(),
  autoRenew: z.boolean().optional(),
  cancelReason: z.string().optional(),
});

// GET - Fetch single user subscription with details
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Get subscription with user and plan details
    const subscription = await db
      .select({
        id: userSubscriptions.id,
        userId: userSubscriptions.userId,
        planId: userSubscriptions.planId,
        status: userSubscriptions.status,
        startDate: userSubscriptions.startDate,
        endDate: userSubscriptions.endDate,
        nextBillingDate: userSubscriptions.nextBillingDate,
        cancelledAt: userSubscriptions.cancelledAt,
        cancelReason: userSubscriptions.cancelReason,
        autoRenew: userSubscriptions.autoRenew,
        paymentMethod: userSubscriptions.paymentMethod,
        lastPaymentDate: userSubscriptions.lastPaymentDate,
        createdAt: userSubscriptions.createdAt,
        updatedAt: userSubscriptions.updatedAt,
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
        plan: {
          id: subscriptionPlans.id,
          name: subscriptionPlans.name,
          displayName: subscriptionPlans.displayName,
          description: subscriptionPlans.description,
          price: subscriptionPlans.price,
          currency: subscriptionPlans.currency,
          billingCycle: subscriptionPlans.billingCycle,
          features: subscriptionPlans.features,
        },
      })
      .from(userSubscriptions)
      .innerJoin(users, eq(userSubscriptions.userId, users.id))
      .innerJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id))
      .where(eq(userSubscriptions.id, params.id))
      .limit(1);

    if (subscription.length === 0) {
      return NextResponse.json(
        { message: "Subscription not found" },
        { status: 404 }
      );
    }

    // Get transaction history for this subscription
    const transactions = await db
      .select({
        id: subscriptionTransactions.id,
        type: subscriptionTransactions.type,
        amount: subscriptionTransactions.amount,
        currency: subscriptionTransactions.currency,
        status: subscriptionTransactions.status,
        paymentGateway: subscriptionTransactions.paymentGateway,
        description: subscriptionTransactions.description,
        createdAt: subscriptionTransactions.createdAt,
        processedAt: subscriptionTransactions.processedAt,
      })
      .from(subscriptionTransactions)
      .where(eq(subscriptionTransactions.subscriptionId, params.id))
      .orderBy(desc(subscriptionTransactions.createdAt))
      .limit(10);

    return NextResponse.json({
      success: true,
      subscription: subscription[0],
      transactions,
    });
  } catch (error) {
    console.error("Error fetching subscription details:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch subscription details",
      },
      { status: 500 }
    );
  }
}

// PATCH - Update user subscription
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateSubscriptionSchema.parse(body);

    // Check if subscription exists
    const existingSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, params.id),
    });

    if (!existingSubscription) {
      return NextResponse.json(
        { message: "Subscription not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updatedAt: new Date(),
    };

    // Convert date strings to Date objects
    if (validatedData.endDate) {
      updateData.endDate = new Date(validatedData.endDate);
    }
    if (validatedData.nextBillingDate) {
      updateData.nextBillingDate = new Date(validatedData.nextBillingDate);
    }

    // If changing status to cancelled, set cancelledAt
    if (validatedData.status === 'cancelled' && existingSubscription.status !== 'cancelled') {
      updateData.cancelledAt = new Date();
      updateData.autoRenew = false;
    }

    // If reactivating, clear cancellation data
    if (validatedData.status === 'active' && existingSubscription.status === 'cancelled') {
      updateData.cancelledAt = null;
      updateData.cancelReason = null;
    }

    // Update the subscription
    await db
      .update(userSubscriptions)
      .set(updateData)
      .where(eq(userSubscriptions.id, params.id));

    // Create audit log transaction
    const auditTransactionId = uuidv4();
    await db.insert(subscriptionTransactions).values({
      id: auditTransactionId,
      subscriptionId: params.id,
      userId: existingSubscription.userId,
      planId: existingSubscription.planId,
      type: 'cancellation', // Using cancellation as a generic admin action type
      amount: '0.00',
      currency: 'USD',
      status: 'completed',
      paymentGateway: 'admin',
      gatewayTransactionId: `admin_${Date.now()}`,
      description: `Admin updated subscription: ${JSON.stringify(validatedData)}`,
      processedAt: new Date(),
      metadata: JSON.stringify({
        adminAction: true,
        adminUserId: session.user.id,
        changes: validatedData,
        previousStatus: existingSubscription.status,
      }),
    });

    // Fetch updated subscription
    const updatedSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, params.id),
    });

    return NextResponse.json({
      success: true,
      message: "Subscription updated successfully",
      subscription: updatedSubscription,
    });
  } catch (error) {
    console.error("Error updating subscription:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to update subscription",
      },
      { status: 500 }
    );
  }
}

// DELETE - Cancel/Delete user subscription
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reason = searchParams.get('reason') || 'Cancelled by admin';
    const hardDelete = searchParams.get('hard') === 'true';

    // Check if subscription exists
    const existingSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, params.id),
    });

    if (!existingSubscription) {
      return NextResponse.json(
        { message: "Subscription not found" },
        { status: 404 }
      );
    }

    if (hardDelete) {
      // Hard delete - remove from database (use with caution)
      await db.delete(userSubscriptions).where(eq(userSubscriptions.id, params.id));
      
      return NextResponse.json({
        success: true,
        message: "Subscription permanently deleted",
      });
    } else {
      // Soft delete - mark as cancelled
      await db
        .update(userSubscriptions)
        .set({
          status: 'cancelled',
          cancelledAt: new Date(),
          cancelReason: reason,
          autoRenew: false,
          updatedAt: new Date(),
        })
        .where(eq(userSubscriptions.id, params.id));

      // Create audit log
      const auditTransactionId = uuidv4();
      await db.insert(subscriptionTransactions).values({
        id: auditTransactionId,
        subscriptionId: params.id,
        userId: existingSubscription.userId,
        planId: existingSubscription.planId,
        type: 'cancellation',
        amount: '0.00',
        currency: 'USD',
        status: 'completed',
        paymentGateway: 'admin',
        gatewayTransactionId: `admin_cancel_${Date.now()}`,
        description: `Admin cancelled subscription: ${reason}`,
        processedAt: new Date(),
        metadata: JSON.stringify({
          adminAction: true,
          adminUserId: session.user.id,
          cancelReason: reason,
        }),
      });

      return NextResponse.json({
        success: true,
        message: "Subscription cancelled successfully",
      });
    }
  } catch (error) {
    console.error("Error deleting subscription:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete subscription",
      },
      { status: 500 }
    );
  }
}
