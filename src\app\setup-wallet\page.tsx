"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";

export default function SetupWalletPage() {
  const [loading, setLoading] = useState(false);

  const setupWallet = async () => {
    setLoading(true);
    try {
      // Setup wallet gateway
      const setupResponse = await fetch('/api/setup-wallet-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const setupResult = await setupResponse.json();
      console.log('Setup result:', setupResult);

      if (setupResult.success) {
        toast.success('Wallet gateway setup successful!');
        
        // Add test balance
        const balanceResponse = await fetch('/api/test/add-wallet-balance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ amount: 100 }),
        });

        const balanceResult = await balanceResponse.json();
        console.log('Balance result:', balanceResult);

        if (balanceResult.success) {
          toast.success(`Added $100 to wallet! Total: $${balanceResult.data.newBalance}`);
        }
      } else {
        toast.error(setupResult.message || 'Setup failed');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Setup failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Setup Wallet Payment</h1>
        
        <div className="space-y-4">
          <Button
            onClick={setupWallet}
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Setting up...' : 'Setup Wallet & Add $100 Balance'}
          </Button>

          <div className="pt-4 border-t">
            <Button
              onClick={() => window.location.href = '/manage-subscription'}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Go to Subscription Management
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
