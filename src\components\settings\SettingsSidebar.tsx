"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  UserIcon,
  Cog6ToothIcon,
  LinkIcon,
  BellIcon,
  LockClosedIcon,
  KeyIcon,
  ComputerDesktopIcon,
  ShieldCheckIcon,
  NoSymbolIcon,
  IdentificationIcon,
  HomeIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

export function SettingsSidebar() {
  const pathname = usePathname();

  const settingsItems = [
    { name: "General", href: "/settings/general", icon: Cog6ToothIcon },
    { name: "Profile", href: "/settings/profile", icon: UserIcon },
    { name: "Social Links", href: "/settings/social-links", icon: LinkIcon },
    { name: "Notification Settings", href: "/settings/notifications", icon: BellIcon },
    { name: "Privacy", href: "/settings/privacy", icon: LockClosedIcon },
    { name: "Password", href: "/settings/password", icon: KeyIcon },
    { name: "Manage Sessions", href: "/settings/sessions", icon: ComputerDesktopIcon },
    { name: "Two-factor Authentication", href: "/settings/two-factor", icon: ShieldCheckIcon },
    { name: "Blocked Users", href: "/settings/blocked-users", icon: NoSymbolIcon },
    { name: "My Information", href: "/settings/information", icon: IdentificationIcon },
    { name: "My Addresses", href: "/settings/addresses", icon: HomeIcon },
    { name: "Delete Account", href: "/settings/delete-account", icon: TrashIcon },
  ];

  return (
    <div className="w-full">
      <div className="space-y-1 overflow-y-auto pr-2 pt-4">
        <h2 className="px-2 text-lg font-semibold text-gray-900 mb-4">Settings</h2>

        {/* Settings Navigation */}
        <nav className="space-y-1">
          {settingsItems.map((item) => {
            const isActive = pathname ? (pathname === item.href || pathname.startsWith(item.href + '/')) : false;

            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center rounded-xl px-2 py-2 text-sm font-medium transition-all duration-200",
                  isActive
                    ? "bg-blue-50 text-blue-600 shadow-sm"
                    : "text-gray-700 hover:bg-gray-100"
                )}
              >
                <div className={cn(
                  "flex items-center justify-center h-7 w-7 rounded-full mr-2",
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "text-gray-600"
                )}>
                  <item.icon className="h-4 w-4" />
                </div>
                <span>{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
