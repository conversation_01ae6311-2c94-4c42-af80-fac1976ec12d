import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { MonetizationService } from "@/lib/monetization/monetizationService";
import { z } from "zod";

const approvalSchema = z.object({
  monetizationId: z.string().min(1),
  approved: z.boolean(),
  reason: z.string().max(500).optional(),
});

// Get monetization requests
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status') || undefined;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const requests = await MonetizationService.getMonetizationRequests(
      status,
      limit,
      offset
    );

    return NextResponse.json({
      success: true,
      data: requests,
    });
  } catch (error: any) {
    console.error("Error fetching monetization requests:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to fetch monetization requests" 
      },
      { status: 500 }
    );
  }
}

// Process monetization request (approve/reject)
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = approvalSchema.parse(body);

    await MonetizationService.processMonetizationRequest({
      monetizationId: validatedData.monetizationId,
      adminId: session.user.id,
      approved: validatedData.approved,
      reason: validatedData.reason,
    });

    return NextResponse.json({
      success: true,
      message: validatedData.approved 
        ? "Monetization request approved successfully"
        : "Monetization request rejected successfully",
    });
  } catch (error: any) {
    console.error("Error processing monetization request:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to process monetization request" 
      },
      { status: 500 }
    );
  }
}
