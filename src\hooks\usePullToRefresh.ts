"use client";

import { useEffect, useRef, useState, useCallback } from 'react';
import { useMobileView } from './useClientSide';

interface UsePullToRefreshOptions {
  onRefresh: () => Promise<void> | void;
  threshold?: number;
  resistance?: number;
  enabled?: boolean;
}

export function usePullToRefresh({
  onRefresh,
  threshold = 80,
  resistance = 2.5,
  enabled = true
}: UsePullToRefreshOptions) {
  const isMobile = useMobileView();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  
  const startY = useRef(0);
  const currentY = useRef(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!enabled || !isMobile || isRefreshing) return;
    
    // Only trigger if we're at the top of the page
    if (window.scrollY > 0) return;
    
    startY.current = e.touches[0].clientY;
    setIsPulling(false);
  }, [enabled, isMobile, isRefreshing]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!enabled || !isMobile || isRefreshing) return;
    
    // Only trigger if we're at the top of the page
    if (window.scrollY > 0) return;
    
    currentY.current = e.touches[0].clientY;
    const deltaY = currentY.current - startY.current;
    
    if (deltaY > 0) {
      setIsPulling(true);
      // Apply resistance to make it feel natural
      const distance = Math.min(deltaY / resistance, threshold * 1.5);
      setPullDistance(distance);
      
      // Prevent default scrolling when pulling
      if (deltaY > 10) {
        e.preventDefault();
      }
    }
  }, [enabled, isMobile, isRefreshing, resistance, threshold]);

  const handleTouchEnd = useCallback(async () => {
    if (!enabled || !isMobile || isRefreshing || !isPulling) return;
    
    if (pullDistance >= threshold) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }
    
    setIsPulling(false);
    setPullDistance(0);
  }, [enabled, isMobile, isRefreshing, isPulling, pullDistance, threshold, onRefresh]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled || !isMobile) return;

    // Add touch event listeners
    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [enabled, isMobile, handleTouchStart, handleTouchMove, handleTouchEnd]);

  const pullToRefreshProps = {
    ref: containerRef,
    style: {
      transform: isPulling ? `translateY(${pullDistance}px)` : 'translateY(0)',
      transition: isPulling ? 'none' : 'transform 0.3s ease-out',
    }
  };

  const refreshIndicatorProps = {
    style: {
      opacity: isPulling ? Math.min(pullDistance / threshold, 1) : 0,
      transform: `translateY(${Math.max(0, pullDistance - 40)}px) rotate(${pullDistance * 2}deg)`,
      transition: isPulling ? 'none' : 'all 0.3s ease-out',
    }
  };

  return {
    pullToRefreshProps,
    refreshIndicatorProps,
    isRefreshing,
    isPulling,
    pullDistance,
    threshold,
    canRefresh: pullDistance >= threshold
  };
}
