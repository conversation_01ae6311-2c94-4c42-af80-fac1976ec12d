"use client";

import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import {
  BuildingStorefrontIcon,
  MapPinIcon,
  CalendarIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  ClockIcon,
  UserIcon
} from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";
import { DetailedTime } from "@/components/ui/TimeDisplay";

interface StoreInfoProps {
  store: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    location: string | null;
    phone?: string | null;
    email?: string | null;
    website?: string | null;
    isVerified: boolean | null;
    createdAt: Date;
    owner: {
      id: string;
      name: string | null;
      username?: string | null;
      image: string | null;
    } | null;
  };
}

export function StoreInfo({ store }: StoreInfoProps) {
  // Mock data for store hours - in a real app, this would come from the database
  const storeHours = [
    { day: "Monday", hours: "9:00 AM - 5:00 PM" },
    { day: "Tuesday", hours: "9:00 AM - 5:00 PM" },
    { day: "Wednesday", hours: "9:00 AM - 5:00 PM" },
    { day: "Thursday", hours: "9:00 AM - 5:00 PM" },
    { day: "Friday", hours: "9:00 AM - 5:00 PM" },
    { day: "Saturday", hours: "10:00 AM - 3:00 PM" },
    { day: "Sunday", hours: "Closed" },
  ];

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Store Information</h2>

      <div className="space-y-6">
        {/* Store Description */}
        {store.description && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">About</h3>
            <p className="text-gray-600">{store.description}</p>
          </div>
        )}

        {/* Store Details */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Details</h3>
          <div className="space-y-3">
            {/* Owner */}
            {store.owner && (
              <div className="flex items-start">
                <UserIcon className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Owner</p>
                  <div className="flex items-center mt-1">
                    {store.owner.image ? (
                      <div className="h-6 w-6 rounded-full overflow-hidden mr-2">
                        <OptimizedImage
                          src={store.owner.image}
                          alt={store.owner.name || 'Store Owner'}
                          width={24}
                          height={24}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                        <span className="text-xs font-medium text-gray-600">
                          {store.owner.name ? store.owner.name.charAt(0).toUpperCase() : 'O'}
                        </span>
                      </div>
                    )}
                    <Link href={`/user/${store.owner.username || store.owner.id}`} className="text-sm text-blue-600 hover:underline">{store.owner.name || 'Store Owner'}</Link>
                  </div>
                </div>
              </div>
            )}

            {/* Location */}
            {store.location && (
              <div className="flex items-start">
                <MapPinIcon className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Location</p>
                  <p className="text-sm text-gray-600">{store.location}</p>
                </div>
              </div>
            )}

            {/* Created At */}
            <div className="flex items-start">
              <CalendarIcon className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Established</p>
                <p className="text-sm text-gray-600">
                  <DetailedTime
                    date={store.createdAt}
                    autoUpdate={false}
                  />
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        {(store.phone || store.email || store.website) && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Contact Information</h3>
            <div className="space-y-3">
              {store.phone && (
                <div className="flex items-center">
                  <PhoneIcon className="h-5 w-5 text-gray-500 mr-3" />
                  <a href={`tel:${store.phone}`} className="text-sm text-blue-600 hover:underline">
                    {store.phone}
                  </a>
                </div>
              )}

              {store.email && (
                <div className="flex items-center">
                  <EnvelopeIcon className="h-5 w-5 text-gray-500 mr-3" />
                  <a href={`mailto:${store.email}`} className="text-sm text-blue-600 hover:underline">
                    {store.email}
                  </a>
                </div>
              )}

              {store.website && (
                <div className="flex items-center">
                  <GlobeAltIcon className="h-5 w-5 text-gray-500 mr-3" />
                  <a href={store.website} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">
                    {store.website}
                  </a>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Store Hours */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Store Hours</h3>
          <div className="space-y-2">
            {storeHours.map((item) => (
              <div key={item.day} className="flex justify-between">
                <span className="text-sm font-medium text-gray-900">{item.day}</span>
                <span className="text-sm text-gray-600">{item.hours}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
