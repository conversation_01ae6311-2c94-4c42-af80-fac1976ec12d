"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/Tabs";
import { useDropzone } from "react-dropzone";
import { uploadToCloudinary } from "@/lib/cloudinary";
import Image from "next/image";
import { toast } from "react-hot-toast";
import { Switch } from "@/components/ui/Switch";
import {
  PhotoIcon,
  KeyIcon,
  EyeIcon,
  EyeSlashIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

export default function AdminSettingsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [showSecrets, setShowSecrets] = useState({
    google: false,
    github: false,
  });
  const [settings, setSettings] = useState({
    general: {
      site_title: "HIFNF",
      site_description: "A social media platform to connect with friends and share your life",
      logo_url: "/logo.png",
      favicon_url: "/favicon.ico",
      maintenance_mode: false,
    },
    theme: {
      default_theme: "light",
      primary_color: "#3b82f6",
      allow_user_theme_toggle: true,
    },
    features: {
      enable_groups: true,
      enable_events: true,
      enable_marketplace: true,
      enable_pages: true,
    },
    content: {
      allow_image_uploads: true,
      allow_video_uploads: true,
      max_upload_size: 10,
    },
    oauth: {
      google_client_id: "",
      google_client_secret: "",
      google_oauth_enabled: false,
      github_client_id: "",
      github_client_secret: "",
      github_oauth_enabled: false,
    },
    cloudinary: {
      cloudinary_cloud_name: "",
      cloudinary_api_key: "",
      cloudinary_api_secret: "",
    },
    email: {
      email_from_address: "<EMAIL>",
      email_from_name: "HIFNF",
    },
    currency: {
      default_currency: "USD",
      currency_symbol: "$",
      currency_position: "before", // before or after
      decimal_places: 2,
      thousands_separator: ",",
      decimal_separator: ".",
      supported_currencies: "USD,EUR,GBP,BDT,INR",
      auto_currency_detection: false,
      currency_conversion_api: "",
    },
  });

  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [faviconFile, setFaviconFile] = useState<File | null>(null);
  const [faviconPreview, setFaviconPreview] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    async function fetchSettings() {
      try {
        // Fetch all settings (including OAuth)
        const settingsResponse = await fetch('/api/admin/settings');
        if (!settingsResponse.ok) {
          throw new Error('Failed to fetch settings');
        }
        const settingsData = await settingsResponse.json();

        // Convert string values to appropriate types based on their type
        const formattedSettings = Object.entries(settingsData).reduce((acc, [group, groupSettings]) => {
          acc[group] = {};

          Object.entries(groupSettings as Record<string, any>).forEach(([key, setting]) => {
            const { value, type } = setting as { value: string, type: string };

            // Convert value based on type
            let parsedValue: any = value;

            if (type === 'boolean') {
              parsedValue = value === 'true';
            } else if (type === 'number') {
              parsedValue = parseFloat(value);
            }

            acc[group][key] = parsedValue;
          });

          return acc;
        }, {} as Record<string, Record<string, any>>);

        // Merge with default settings to ensure all required properties exist
        setSettings(prevSettings => {
          const mergedSettings = { ...prevSettings };

          // Merge each group
          Object.keys(mergedSettings).forEach(group => {
            if (formattedSettings[group]) {
              (mergedSettings as any)[group] = {
                ...(mergedSettings as any)[group],
                ...formattedSettings[group]
              };
            }
          });

          return mergedSettings;
        });
      } catch (error) {
        console.error('Error fetching settings:', error);
        setErrorMessage('Failed to load settings. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }

    fetchSettings();
  }, []);

  // Logo dropzone
  const { getRootProps: getLogoRootProps, getInputProps: getLogoInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setLogoFile(file);
        setLogoPreview(URL.createObjectURL(file));
      }
    },
  });

  // Favicon dropzone
  const { getRootProps: getFaviconRootProps, getInputProps: getFaviconInputProps } = useDropzone({
    accept: {
      "image/*": [".ico", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setFaviconFile(file);
        setFaviconPreview(URL.createObjectURL(file));
      }
    },
  });

  const handleInputChange = (section: string, key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    setSuccessMessage("");
    setErrorMessage("");

    try {
      // Create a copy of the current settings to update
      let updatedSettings = { ...settings };

      // Upload logo if changed
      if (logoFile) {
        try {
          const logoUrl = await uploadToCloudinary(logoFile);
          updatedSettings = {
            ...updatedSettings,
            general: {
              ...updatedSettings.general,
              logo_url: logoUrl,
            },
          };
        } catch (uploadError) {
          console.error("Error uploading logo:", uploadError);
          throw new Error("Failed to upload logo. Please try again.");
        }
      }

      // Upload favicon if changed
      if (faviconFile) {
        try {
          const faviconUrl = await uploadToCloudinary(faviconFile);
          updatedSettings = {
            ...updatedSettings,
            general: {
              ...updatedSettings.general,
              favicon_url: faviconUrl,
            },
          };
        } catch (uploadError) {
          console.error("Error uploading favicon:", uploadError);
          throw new Error("Failed to upload favicon. Please try again.");
        }
      }

      console.log("Saving settings:", updatedSettings);

      // Save all settings to the API (including OAuth)
      const settingsResponse = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      });

      const responseData = await settingsResponse.json();
      console.log("Settings save response:", responseData);

      if (!settingsResponse.ok) {
        // Handle different types of errors
        if (settingsResponse.status === 207) {
          // Multi-status response - some settings failed
          const failedCount = responseData.details?.failed?.length || 0;
          const successCount = responseData.details?.successful?.length || 0;

          if (failedCount > 0) {
            console.error("Failed settings:", responseData.details.failed);
            setErrorMessage(`${failedCount} settings failed to save. ${successCount} settings saved successfully.`);
          } else {
            setSuccessMessage(`Settings saved successfully! (${successCount} updated)`);
          }
        } else {
          throw new Error(responseData.message || 'Failed to save settings');
        }
      } else {
        // Success
        const updatedCount = responseData.details?.updated || 0;
        setSettings(updatedSettings);
        setSuccessMessage(`Settings saved successfully! (${updatedCount} settings updated)`);
        toast.success("Settings saved successfully!");

        // Clear file states after successful save
        setLogoFile(null);
        setLogoPreview(null);
        setFaviconFile(null);
        setFaviconPreview(null);
      }
    } catch (error) {
      console.error("Error saving settings:", error);

      let errorMessage = "Failed to save settings. Please try again.";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error types
        if (error.message.includes('database')) {
          errorMessage = "Database connection error. Please check your database configuration.";
        } else if (error.message.includes('unauthorized')) {
          errorMessage = "You are not authorized to perform this action.";
        } else if (error.message.includes('network')) {
          errorMessage = "Network error. Please check your internet connection.";
        }
      }

      setErrorMessage(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Site Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your site's general settings and configuration
        </p>
      </div>

      {successMessage && (
        <div className="mb-4 rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="mb-4 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      <div className="overflow-hidden rounded-lg bg-white shadow">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="border-b border-gray-200">
            <TabsList className="flex">
              <TabsTrigger
                value="general"
                className="px-4 py-2 text-sm font-medium"
              >
                General
              </TabsTrigger>
              <TabsTrigger
                value="theme"
                className="px-4 py-2 text-sm font-medium"
              >
                Theme
              </TabsTrigger>
              <TabsTrigger
                value="features"
                className="px-4 py-2 text-sm font-medium"
              >
                Features
              </TabsTrigger>
              <TabsTrigger
                value="content"
                className="px-4 py-2 text-sm font-medium"
              >
                Content
              </TabsTrigger>
              <TabsTrigger
                value="cloudinary"
                className="px-4 py-2 text-sm font-medium"
              >
                Storage
              </TabsTrigger>
              <TabsTrigger
                value="email"
                className="px-4 py-2 text-sm font-medium"
              >
                Email
              </TabsTrigger>
              <TabsTrigger
                value="currency"
                className="px-4 py-2 text-sm font-medium"
              >
                Currency
              </TabsTrigger>
              <TabsTrigger
                value="oauth"
                className="px-4 py-2 text-sm font-medium"
              >
                OAuth
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="p-6">
            <TabsContent value="general">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Site Title
                  </label>
                  <Input
                    type="text"
                    value={settings.general.site_title}
                    onChange={(e) =>
                      handleInputChange("general", "site_title", e.target.value)
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Site Description
                  </label>
                  <textarea
                    rows={3}
                    value={settings.general.site_description}
                    onChange={(e) =>
                      handleInputChange("general", "site_description", e.target.value)
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Logo
                  </label>
                  <div className="mt-1 flex items-center space-x-6">
                    <div className="flex-shrink-0">
                      <Image
                        src={logoPreview || settings.general.logo_url}
                        alt="Site Logo"
                        width={100}
                        height={40}
                        className="h-10 w-auto"
                      />
                    </div>
                    <div
                      {...getLogoRootProps()}
                      className="flex cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-300 px-6 py-4 hover:border-gray-400"
                    >
                      <input {...getLogoInputProps()} />
                      <div className="space-y-1 text-center">
                        <PhotoIcon className="mx-auto h-6 w-6 text-gray-400" />
                        <div className="text-sm text-gray-500">
                          <span className="font-medium text-blue-600 hover:text-blue-500">
                            Upload a new logo
                          </span>{" "}
                          or drag and drop
                        </div>
                        <p className="text-xs text-gray-500">PNG, JPG up to 2MB</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Favicon
                  </label>
                  <div className="mt-1 flex items-center space-x-6">
                    <div className="flex-shrink-0">
                      <Image
                        src={faviconPreview || settings.general.favicon_url}
                        alt="Favicon"
                        width={32}
                        height={32}
                        className="h-8 w-8"
                      />
                    </div>
                    <div
                      {...getFaviconRootProps()}
                      className="flex cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-300 px-6 py-4 hover:border-gray-400"
                    >
                      <input {...getFaviconInputProps()} />
                      <div className="space-y-1 text-center">
                        <PhotoIcon className="mx-auto h-6 w-6 text-gray-400" />
                        <div className="text-sm text-gray-500">
                          <span className="font-medium text-blue-600 hover:text-blue-500">
                            Upload a new favicon
                          </span>{" "}
                          or drag and drop
                        </div>
                        <p className="text-xs text-gray-500">ICO, PNG up to 1MB</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    id="maintenance_mode"
                    name="maintenance_mode"
                    type="checkbox"
                    checked={settings.general.maintenance_mode}
                    onChange={(e) =>
                      handleInputChange("general", "maintenance_mode", e.target.checked)
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="maintenance_mode"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Enable Maintenance Mode
                  </label>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="theme">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Default Theme
                  </label>
                  <select
                    value={settings.theme.default_theme}
                    onChange={(e) =>
                      handleInputChange("theme", "default_theme", e.target.value)
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Primary Color
                  </label>
                  <div className="mt-1 flex items-center">
                    <input
                      type="color"
                      value={settings.theme.primary_color}
                      onChange={(e) =>
                        handleInputChange("theme", "primary_color", e.target.value)
                      }
                      className="h-10 w-10 rounded-md border border-gray-300"
                    />
                    <Input
                      type="text"
                      value={settings.theme.primary_color}
                      onChange={(e) =>
                        handleInputChange("theme", "primary_color", e.target.value)
                      }
                      className="ml-2"
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    id="allow_user_theme_toggle"
                    name="allow_user_theme_toggle"
                    type="checkbox"
                    checked={settings.theme.allow_user_theme_toggle}
                    onChange={(e) =>
                      handleInputChange(
                        "theme",
                        "allow_user_theme_toggle",
                        e.target.checked
                      )
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="allow_user_theme_toggle"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Allow users to toggle between light and dark themes
                  </label>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="features">
              <div className="space-y-6">
                <div className="flex items-center">
                  <input
                    id="enable_groups"
                    name="enable_groups"
                    type="checkbox"
                    checked={settings.features.enable_groups}
                    onChange={(e) =>
                      handleInputChange("features", "enable_groups", e.target.checked)
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="enable_groups"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Enable Groups Feature
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    id="enable_events"
                    name="enable_events"
                    type="checkbox"
                    checked={settings.features.enable_events}
                    onChange={(e) =>
                      handleInputChange("features", "enable_events", e.target.checked)
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="enable_events"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Enable Events Feature
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    id="enable_marketplace"
                    name="enable_marketplace"
                    type="checkbox"
                    checked={settings.features.enable_marketplace}
                    onChange={(e) =>
                      handleInputChange(
                        "features",
                        "enable_marketplace",
                        e.target.checked
                      )
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="enable_marketplace"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Enable Marketplace Feature
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    id="enable_pages"
                    name="enable_pages"
                    type="checkbox"
                    checked={settings.features.enable_pages}
                    onChange={(e) =>
                      handleInputChange("features", "enable_pages", e.target.checked)
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="enable_pages"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Enable Pages Feature
                  </label>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="content">
              <div className="space-y-6">
                <div className="flex items-center">
                  <input
                    id="allow_image_uploads"
                    name="allow_image_uploads"
                    type="checkbox"
                    checked={settings.content.allow_image_uploads}
                    onChange={(e) =>
                      handleInputChange(
                        "content",
                        "allow_image_uploads",
                        e.target.checked
                      )
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="allow_image_uploads"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Allow Image Uploads
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    id="allow_video_uploads"
                    name="allow_video_uploads"
                    type="checkbox"
                    checked={settings.content.allow_video_uploads}
                    onChange={(e) =>
                      handleInputChange(
                        "content",
                        "allow_video_uploads",
                        e.target.checked
                      )
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="allow_video_uploads"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Allow Video Uploads
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Maximum Upload Size (MB)
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max="100"
                    value={settings.content.max_upload_size}
                    onChange={(e) =>
                      handleInputChange(
                        "content",
                        "max_upload_size",
                        parseInt(e.target.value)
                      )
                    }
                    className="mt-1"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="cloudinary">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Cloudinary Cloud Name
                  </label>
                  <Input
                    type="text"
                    value={settings.cloudinary.cloudinary_cloud_name}
                    onChange={(e) =>
                      handleInputChange(
                        "cloudinary",
                        "cloudinary_cloud_name",
                        e.target.value
                      )
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Cloudinary API Key
                  </label>
                  <Input
                    type="text"
                    value={settings.cloudinary.cloudinary_api_key}
                    onChange={(e) =>
                      handleInputChange(
                        "cloudinary",
                        "cloudinary_api_key",
                        e.target.value
                      )
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Cloudinary API Secret
                  </label>
                  <Input
                    type="password"
                    value={settings.cloudinary.cloudinary_api_secret}
                    onChange={(e) =>
                      handleInputChange(
                        "cloudinary",
                        "cloudinary_api_secret",
                        e.target.value
                      )
                    }
                    className="mt-1"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="email">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    From Email Address
                  </label>
                  <Input
                    type="email"
                    value={settings.email.email_from_address}
                    onChange={(e) =>
                      handleInputChange(
                        "email",
                        "email_from_address",
                        e.target.value
                      )
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    From Name
                  </label>
                  <Input
                    type="text"
                    value={settings.email.email_from_name}
                    onChange={(e) =>
                      handleInputChange("email", "email_from_name", e.target.value)
                    }
                    className="mt-1"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="currency">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Default Currency
                    </label>
                    <select
                      value={settings.currency.default_currency}
                      onChange={(e) =>
                        handleInputChange(
                          "currency",
                          "default_currency",
                          e.target.value
                        )
                      }
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="GBP">GBP - British Pound</option>
                      <option value="BDT">BDT - Bangladeshi Taka</option>
                      <option value="INR">INR - Indian Rupee</option>
                      <option value="CAD">CAD - Canadian Dollar</option>
                      <option value="AUD">AUD - Australian Dollar</option>
                      <option value="JPY">JPY - Japanese Yen</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Currency Symbol
                    </label>
                    <Input
                      type="text"
                      value={settings.currency.currency_symbol}
                      onChange={(e) =>
                        handleInputChange(
                          "currency",
                          "currency_symbol",
                          e.target.value
                        )
                      }
                      className="mt-1"
                      placeholder="$"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Currency Position
                    </label>
                    <select
                      value={settings.currency.currency_position}
                      onChange={(e) =>
                        handleInputChange(
                          "currency",
                          "currency_position",
                          e.target.value
                        )
                      }
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="before">Before Amount ($100)</option>
                      <option value="after">After Amount (100$)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Decimal Places
                    </label>
                    <select
                      value={settings.currency.decimal_places}
                      onChange={(e) =>
                        handleInputChange(
                          "currency",
                          "decimal_places",
                          parseInt(e.target.value)
                        )
                      }
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value={0}>0 (100)</option>
                      <option value={1}>1 (100.0)</option>
                      <option value={2}>2 (100.00)</option>
                      <option value={3}>3 (100.000)</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Thousands Separator
                    </label>
                    <select
                      value={settings.currency.thousands_separator}
                      onChange={(e) =>
                        handleInputChange(
                          "currency",
                          "thousands_separator",
                          e.target.value
                        )
                      }
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value=",">Comma (1,000)</option>
                      <option value=".">Dot (1.000)</option>
                      <option value=" ">Space (1 000)</option>
                      <option value="">None (1000)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Decimal Separator
                    </label>
                    <select
                      value={settings.currency.decimal_separator}
                      onChange={(e) =>
                        handleInputChange(
                          "currency",
                          "decimal_separator",
                          e.target.value
                        )
                      }
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value=".">Dot (100.50)</option>
                      <option value=",">Comma (100,50)</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Supported Currencies
                  </label>
                  <Input
                    type="text"
                    value={settings.currency.supported_currencies}
                    onChange={(e) =>
                      handleInputChange(
                        "currency",
                        "supported_currencies",
                        e.target.value
                      )
                    }
                    className="mt-1"
                    placeholder="USD,EUR,GBP,BDT,INR"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Comma-separated list of currency codes that users can select from
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Currency Conversion API Key
                  </label>
                  <Input
                    type="text"
                    value={settings.currency.currency_conversion_api}
                    onChange={(e) =>
                      handleInputChange(
                        "currency",
                        "currency_conversion_api",
                        e.target.value
                      )
                    }
                    className="mt-1"
                    placeholder="Enter API key for currency conversion service"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Optional: API key for real-time currency conversion (e.g., from exchangerate-api.com)
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="auto_currency_detection"
                    checked={settings.currency.auto_currency_detection}
                    onChange={(e) =>
                      handleInputChange(
                        "currency",
                        "auto_currency_detection",
                        e.target.checked
                      )
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="auto_currency_detection" className="ml-2 block text-sm text-gray-900">
                    Auto-detect user currency based on location
                  </label>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">Currency Preview</h4>
                  <div className="text-sm text-blue-800">
                    <p>Sample amount: {(() => {
                      const amount = 1234.56;
                      const formattedAmount = amount.toFixed(settings.currency.decimal_places);
                      const [integerPart, decimalPart] = formattedAmount.split('.');

                      let formattedInteger = integerPart;
                      if (settings.currency.thousands_separator && integerPart.length > 3) {
                        formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, settings.currency.thousands_separator);
                      }

                      let finalAmount = formattedInteger;
                      if (settings.currency.decimal_places > 0 && decimalPart) {
                        finalAmount += settings.currency.decimal_separator + decimalPart;
                      }

                      return settings.currency.currency_position === 'before'
                        ? `${settings.currency.currency_symbol}${finalAmount}`
                        : `${finalAmount}${settings.currency.currency_symbol}`;
                    })()}</p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="oauth">
              <div className="space-y-8">
                {/* Header */}
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <KeyIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">OAuth Settings</h2>
                    <p className="text-gray-600">
                      Configure OAuth providers for user authentication
                    </p>
                  </div>
                </div>

                {/* Google OAuth Section */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                          <svg className="w-6 h-6 text-red-600" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h2 className="text-lg font-semibold text-gray-900">Google OAuth</h2>
                        <p className="text-sm text-gray-600">Configure Google Sign-In</p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.oauth.google_oauth_enabled}
                      onCheckedChange={(checked) => handleInputChange('oauth', 'google_oauth_enabled', checked)}
                    />
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Google Client ID
                      </label>
                      <Input
                        type="text"
                        value={settings.oauth.google_client_id}
                        onChange={(e) => handleInputChange('oauth', 'google_client_id', e.target.value)}
                        placeholder="Enter Google Client ID"
                        disabled={!settings.oauth.google_oauth_enabled}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Google Client Secret
                      </label>
                      <div className="relative">
                        <Input
                          type={showSecrets.google ? "text" : "password"}
                          value={settings.oauth.google_client_secret}
                          onChange={(e) => handleInputChange('oauth', 'google_client_secret', e.target.value)}
                          placeholder="Enter Google Client Secret"
                          disabled={!settings.oauth.google_oauth_enabled}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowSecrets(prev => ({ ...prev, google: !prev.google }))}
                        >
                          {showSecrets.google ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>

                    {settings.oauth.google_oauth_enabled && (
                      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div className="flex items-start">
                          <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                          <div className="text-sm text-blue-800">
                            <p className="font-medium mb-1">Setup Instructions:</p>
                            <ol className="list-decimal list-inside space-y-1">
                              <li>Go to the Google Cloud Console</li>
                              <li>Create or select a project</li>
                              <li>Enable the Google+ API</li>
                              <li>Create OAuth 2.0 credentials</li>
                              <li>Add your domain to authorized origins</li>
                            </ol>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* GitHub OAuth Section */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          <svg className="w-6 h-6 text-gray-900" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h2 className="text-lg font-semibold text-gray-900">GitHub OAuth</h2>
                        <p className="text-sm text-gray-600">Configure GitHub Sign-In</p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.oauth.github_oauth_enabled}
                      onCheckedChange={(checked) => handleInputChange('oauth', 'github_oauth_enabled', checked)}
                    />
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        GitHub Client ID
                      </label>
                      <Input
                        type="text"
                        value={settings.oauth.github_client_id}
                        onChange={(e) => handleInputChange('oauth', 'github_client_id', e.target.value)}
                        placeholder="Enter GitHub Client ID"
                        disabled={!settings.oauth.github_oauth_enabled}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        GitHub Client Secret
                      </label>
                      <div className="relative">
                        <Input
                          type={showSecrets.github ? "text" : "password"}
                          value={settings.oauth.github_client_secret}
                          onChange={(e) => handleInputChange('oauth', 'github_client_secret', e.target.value)}
                          placeholder="Enter GitHub Client Secret"
                          disabled={!settings.oauth.github_oauth_enabled}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowSecrets(prev => ({ ...prev, github: !prev.github }))}
                        >
                          {showSecrets.github ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>

                    {settings.oauth.github_oauth_enabled && (
                      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div className="flex items-start">
                          <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                          <div className="text-sm text-blue-800">
                            <p className="font-medium mb-1">Setup Instructions:</p>
                            <ol className="list-decimal list-inside space-y-1">
                              <li>Go to GitHub Settings → Developer settings</li>
                              <li>Click "OAuth Apps" → "New OAuth App"</li>
                              <li>Fill in application details</li>
                              <li>Set Authorization callback URL</li>
                              <li>Copy Client ID and Client Secret</li>
                            </ol>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Status Section */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">OAuth Status</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Google OAuth</span>
                      <div className="flex items-center">
                        {settings.oauth.google_oauth_enabled && settings.oauth.google_client_id && settings.oauth.google_client_secret ? (
                          <>
                            <ShieldCheckIcon className="h-5 w-5 text-green-500 mr-2" />
                            <span className="text-sm font-medium text-green-600">Configured</span>
                          </>
                        ) : (
                          <>
                            <InformationCircleIcon className="h-5 w-5 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-500">Not configured</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">GitHub OAuth</span>
                      <div className="flex items-center">
                        {settings.oauth.github_oauth_enabled && settings.oauth.github_client_id && settings.oauth.github_client_secret ? (
                          <>
                            <ShieldCheckIcon className="h-5 w-5 text-green-500 mr-2" />
                            <span className="text-sm font-medium text-green-600">Configured</span>
                          </>
                        ) : (
                          <>
                            <InformationCircleIcon className="h-5 w-5 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-500">Not configured</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <div className="border-t border-gray-200 px-6 py-4">
          <Button
            onClick={handleSaveSettings}
            isLoading={isSaving}
            className="ml-auto"
          >
            Save Settings
          </Button>
        </div>
      </div>
    </AdminLayout>
  );
}
