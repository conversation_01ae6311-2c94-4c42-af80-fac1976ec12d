import type * as Party from "partykit/server";

interface NotificationData {
  id: string;
  type: "like" | "comment" | "message" | "follow" | "fan_page_message" | "fan_page_reply" | "subscription";
  recipientId: string;
  senderId: string;
  title?: string;
  message?: string;
  data?: any;
  timestamp: string;
}

interface ConnectionMeta {
  userId?: string;
  authenticated: boolean;
  lastSeen: number;
}

export default class NotificationServer implements Party.Server {
  private connections = new Map<string, ConnectionMeta>();
  private userConnections = new Map<string, Set<string>>();

  constructor(readonly room: Party.Room) {}

  onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    console.log(`Notification server - New connection: ${conn.id}`);
    
    this.connections.set(conn.id, {
      authenticated: false,
      lastSeen: Date.now()
    });

    conn.send(JSON.stringify({
      type: "notification_server_ready",
      connectionId: conn.id,
      timestamp: new Date().toISOString()
    }));
  }

  onMessage(message: string, sender: Party.Connection) {
    try {
      const data = JSON.parse(message);
      const connMeta = this.connections.get(sender.id);
      
      if (!connMeta) return;
      
      connMeta.lastSeen = Date.now();

      switch (data.type) {
        case "auth":
          this.handleAuth(sender, data);
          break;
        
        case "send_notification":
          if (connMeta.authenticated) {
            this.handleSendNotification(data.notification);
          }
          break;
        
        case "mark_read":
          if (connMeta.authenticated) {
            this.handleMarkRead(sender, data);
          }
          break;
        
        case "subscribe_notifications":
          if (connMeta.authenticated) {
            this.handleSubscribe(sender, data.userId);
          }
          break;
      }
    } catch (error) {
      console.error("Notification server error:", error);
      sender.send(JSON.stringify({ type: "error", message: "Invalid message format" }));
    }
  }

  onClose(conn: Party.Connection) {
    const connMeta = this.connections.get(conn.id);
    if (connMeta?.userId) {
      const userConns = this.userConnections.get(connMeta.userId);
      if (userConns) {
        userConns.delete(conn.id);
        if (userConns.size === 0) {
          this.userConnections.delete(connMeta.userId);
        }
      }
    }
    
    this.connections.delete(conn.id);
  }

  private async handleAuth(conn: Party.Connection, message: any) {
    try {
      const userId = await this.verifyToken(message.token);
      
      if (userId) {
        const connMeta = this.connections.get(conn.id)!;
        connMeta.authenticated = true;
        connMeta.userId = userId;
        
        if (!this.userConnections.has(userId)) {
          this.userConnections.set(userId, new Set());
        }
        this.userConnections.get(userId)!.add(conn.id);
        
        conn.send(JSON.stringify({
          type: "notification_auth_success",
          userId,
          timestamp: new Date().toISOString()
        }));
      } else {
        conn.send(JSON.stringify({
          type: "notification_auth_error",
          message: "Invalid token"
        }));
      }
    } catch (error) {
      console.error("Notification auth error:", error);
      conn.send(JSON.stringify({
        type: "notification_auth_error",
        message: "Authentication failed"
      }));
    }
  }

  private handleSendNotification(notification: NotificationData) {
    // Send notification to specific user
    this.sendToUser(notification.recipientId, {
      type: "new_notification",
      notification: {
        ...notification,
        timestamp: new Date().toISOString()
      }
    });
  }

  private handleMarkRead(conn: Party.Connection, data: any) {
    const connMeta = this.connections.get(conn.id)!;
    
    // Broadcast read status to all user's connections
    if (connMeta.userId) {
      this.sendToUser(connMeta.userId, {
        type: "notification_read",
        notificationId: data.notificationId,
        timestamp: new Date().toISOString()
      });
    }
  }

  private handleSubscribe(conn: Party.Connection, userId: string) {
    const connMeta = this.connections.get(conn.id)!;
    
    if (connMeta.userId === userId) {
      conn.send(JSON.stringify({
        type: "notification_subscribed",
        userId,
        timestamp: new Date().toISOString()
      }));
    }
  }

  private sendToUser(userId: string, message: any) {
    const userConns = this.userConnections.get(userId);
    if (userConns) {
      userConns.forEach(connId => {
        const conn = [...this.room.getConnections()].find(c => c.id === connId);
        if (conn) {
          conn.send(JSON.stringify(message));
        }
      });
    }
  }

  private async verifyToken(token: string): Promise<string | null> {
    try {
      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      
      if (payload.exp && payload.exp < Date.now() / 1000) {
        return null;
      }
      
      return payload.sub || payload.id || null;
    } catch (error) {
      console.error("Token verification error:", error);
      return null;
    }
  }

  // Static method to send notifications from API endpoints
  static async sendNotification(roomId: string, notification: NotificationData) {
    try {
      // This would be called from your API endpoints
      // You'd need to configure PartyKit to allow server-to-server communication
      const response = await fetch(`${process.env.PARTYKIT_HOST}/parties/notifications/${roomId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.PARTYKIT_API_TOKEN}`
        },
        body: JSON.stringify({
          type: 'send_notification',
          notification
        })
      });
      
      return response.ok;
    } catch (error) {
      console.error('Failed to send notification via PartyKit:', error);
      return false;
    }
  }
}
