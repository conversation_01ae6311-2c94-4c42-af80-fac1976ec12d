import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, adminRoles, wallets } from "@/lib/db/schema";
import { eq, like, desc, asc, leftJoin, isNotNull } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import bcrypt from "bcryptjs";

const userCreateSchema = z.object({
  name: z.string().min(2).max(255),
  email: z.string().email(),
  password: z.string().min(6),
  role: z.string().default("user"),
  isAdmin: z.boolean().default(false),
  adminRoleId: z.string().optional().nullable(),
});

const querySchema = z.object({
  page: z.coerce.number().default(1),
  limit: z.coerce.number().default(10),
  search: z.string().optional().nullable().transform(val => val || undefined),
  sort: z.enum(["name", "email", "createdAt"]).default("createdAt").optional().nullable().transform(val => val || "createdAt"),
  order: z.enum(["asc", "desc"]).default("desc").optional().nullable().transform(val => val || "desc"),
  filter: z.enum(["all", "admins", "verified", "banned"]).default("all").optional().nullable().transform(val => val || "all"),
});

// Get all users with pagination, search, and filtering
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const query = querySchema.parse({
      page: url.searchParams.get("page") || 1,
      limit: url.searchParams.get("limit") || 10,
      search: url.searchParams.get("search"),
      sort: url.searchParams.get("sort"),
      order: url.searchParams.get("order"),
      filter: url.searchParams.get("filter"),
    });

    // Build the query with wallet data
    let query_builder = db.select({
      id: users.id,
      name: users.name,
      username: users.username,
      email: users.email,
      role: users.role,
      isAdmin: users.isAdmin,
      adminRoleId: users.adminRoleId,
      image: users.image,
      emailVerified: users.emailVerified,
      isVerified: users.isVerified,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt,
      // Wallet data
      generalBalance: wallets.generalBalance,
      earningBalance: wallets.earningBalance,
    }).from(users)
    .leftJoin(wallets, eq(users.id, wallets.userId));

    // Apply search filter
    if (query.search) {
      query_builder = query_builder.where(
        like(users.name, `%${query.search}%`)
      );
    }

    // Apply role/status filter
    if (query.filter === "admins") {
      query_builder = query_builder.where(eq(users.isAdmin, true));
    } else if (query.filter === "verified") {
      query_builder = query_builder.where(isNotNull(users.emailVerified));
    } else if (query.filter === "banned") {
      // For now, we'll filter by suspended status since we don't have a banned field
      query_builder = query_builder.where(eq(users.status, 'suspended'));
    }
    // "all" filter doesn't need any additional where clause

    // Count total results for pagination (before applying limit/offset)
    let countQuery = db.select({ count: users.id }).from(users)
      .leftJoin(wallets, eq(users.id, wallets.userId));

    // Apply the same filters to count query
    if (query.search) {
      countQuery = countQuery.where(like(users.name, `%${query.search}%`));
    }
    if (query.filter === "admins") {
      countQuery = countQuery.where(eq(users.isAdmin, true));
    } else if (query.filter === "verified") {
      countQuery = countQuery.where(isNotNull(users.emailVerified));
    } else if (query.filter === "banned") {
      countQuery = countQuery.where(eq(users.status, 'suspended'));
    }

    const countResult = await countQuery.execute();
    const total = countResult.length;

    // Apply sorting
    if (query.sort === "name") {
      query_builder = query_builder.orderBy(
        query.order === "asc" ? asc(users.name) : desc(users.name)
      );
    } else if (query.sort === "email") {
      query_builder = query_builder.orderBy(
        query.order === "asc" ? asc(users.email) : desc(users.email)
      );
    } else {
      query_builder = query_builder.orderBy(
        query.order === "asc" ? asc(users.createdAt) : desc(users.createdAt)
      );
    }

    // Apply pagination
    const offset = (query.page - 1) * query.limit;
    query_builder = query_builder.limit(query.limit).offset(offset);

    // Execute the query
    const results = await query_builder.execute();

    // Get admin roles for users who are admins
    const adminUserIds = results
      .filter((user) => user.isAdmin && user.adminRoleId)
      .map((user) => user.adminRoleId);

    const adminRolesData = adminUserIds.length
      ? await db.query.adminRoles.findMany({
          where: (adminRoles, { inArray }) =>
            inArray(adminRoles.id, adminUserIds as string[]),
        })
      : [];

    // Map admin role names to users and add derived fields
    const usersWithRoles = results.map((user) => {
      const adminRole = user.adminRoleId
        ? adminRolesData.find((role) => role.id === user.adminRoleId)
        : null;

      return {
        ...user,
        adminRoleName: adminRole?.name || null,
        // Add derived fields for the frontend
        isVerified: user.isVerified || false,
        isBanned: false, // We don't have this field yet, so default to false
        // Wallet balances (default to "0.00" if no wallet exists)
        generalBalance: user.generalBalance || "0.00",
        earningBalance: user.earningBalance || "0.00",
      };
    });

    return NextResponse.json(
      {
        users: usersWithRoles,
        pagination: {
          total,
          page: query.page,
          limit: query.limit,
          totalPages: Math.ceil(total / query.limit),
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching users:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid query parameters", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new user
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();

    // Validate the request body
    const validatedData = userCreateSchema.parse(body);

    // Check if email already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, validatedData.email),
    });

    if (existingUser) {
      return NextResponse.json(
        { message: "Email already exists" },
        { status: 400 }
      );
    }

    // Check if admin role exists if provided
    if (validatedData.adminRoleId) {
      const adminRole = await db.query.adminRoles.findFirst({
        where: eq(adminRoles.id, validatedData.adminRoleId),
      });

      if (!adminRole) {
        return NextResponse.json(
          { message: "Admin role not found" },
          { status: 400 }
        );
      }
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(validatedData.password, 10);

    // Create the user
    const userId = uuidv4();
    await db.insert(users).values({
      id: userId,
      name: validatedData.name,
      email: validatedData.email,
      password: hashedPassword, // Add the hashed password
      role: validatedData.role,
      isAdmin: validatedData.isAdmin,
      adminRoleId: validatedData.adminRoleId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json(
      {
        message: "User created successfully",
        user: {
          id: userId,
          name: validatedData.name,
          email: validatedData.email,
          role: validatedData.role,
          isAdmin: validatedData.isAdmin,
        }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating user:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
