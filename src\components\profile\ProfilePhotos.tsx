"use client";

import { useState, useEffect, useCallback } from "react";
import { PhotoIcon, PlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import Image from "next/image";

interface Photo {
  id: string;
  url: string;
  caption?: string;
  createdAt: string;
}

interface ProfilePhotosProps {
  userId: string;
  isOwnProfile: boolean;
}

export function ProfilePhotos({ userId, isOwnProfile }: ProfilePhotosProps) {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [showPhotoUploader, setShowPhotoUploader] = useState(false);

  const fetchPhotos = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/users/${userId}/photos`);

      if (response.ok) {
        const data = await response.json();
        setPhotos(data.photos || []);
      } else {
        console.error('Failed to fetch photos');
      }
    } catch (error) {
      console.error('Error fetching photos:', error);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchPhotos();
  }, [fetchPhotos]);

  const handlePhotoUpload = () => {
    setShowPhotoUploader(true);
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="aspect-square bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Photos</h2>
            <p className="text-sm text-gray-500 mt-1">
              {photos.length} {photos.length === 1 ? 'photo' : 'photos'}
            </p>
          </div>
          {isOwnProfile && (
            <Button
              onClick={handlePhotoUpload}
              className="flex items-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>Add Photos</span>
            </Button>
          )}
        </div>
      </div>

      {/* Photos Grid */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        {photos.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {photos.map((photo) => (
              <div
                key={photo.id}
                className="aspect-square relative group cursor-pointer overflow-hidden rounded-lg bg-gray-100"

              >
                <Image
                  src={photo.url}
                  alt={photo.caption || "Photo"}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <PhotoIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No photos yet</h3>
            <p className="text-gray-500 mb-6">
              {isOwnProfile
                ? "Share your moments by uploading your first photo."
                : "This user hasn't shared any photos yet."
              }
            </p>
            {isOwnProfile && (
              <Button
                onClick={handlePhotoUpload}
                className="flex items-center space-x-2 mx-auto"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Upload Photo</span>
              </Button>
            )}
          </div>
        )}
      </div>



      {/* Photo Upload Modal */}
      <Dialog
        open={showPhotoUploader}
        onClose={() => setShowPhotoUploader(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <DialogPanel className="mx-auto max-w-md rounded-xl bg-white p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <DialogTitle className="text-lg font-semibold text-gray-900">
                Upload Photos
              </DialogTitle>
              <button
                onClick={() => setShowPhotoUploader(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="text-center py-8">
              <PhotoIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">
                Photo upload functionality will be implemented soon.
              </p>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowPhotoUploader(false)}
              >
                Cancel
              </Button>
            </div>
          </DialogPanel>
        </div>
      </Dialog>
    </div>
  );
}
