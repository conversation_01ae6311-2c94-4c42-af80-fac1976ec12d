import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { db } from "@/lib/db";
import { walletTransactions } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

const updateTransactionSchema = z.object({
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']),
  adminNote: z.string().optional(),
  gatewayTransactionId: z.string().optional(),
});

// Get specific transaction details
export async function GET(
  req: Request,
  context: { params: Promise<{ transactionId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { transactionId } = params;

    const transaction = await db.query.walletTransactions.findFirst({
      where: eq(walletTransactions.id, transactionId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
        toUser: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
        toAgent: {
          columns: {
            id: true,
            name: true,
            serviceType: true,
            phone: true,
          },
        },
      },
    });

    if (!transaction) {
      return NextResponse.json(
        { message: "Transaction not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: transaction,
    });
  } catch (error: any) {
    console.error("Error fetching transaction:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch transaction"
      },
      { status: 500 }
    );
  }
}

// Update transaction status and details
export async function PUT(
  req: Request,
  context: { params: Promise<{ transactionId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { transactionId } = params;

    const body = await req.json();
    const validatedData = updateTransactionSchema.parse(body);

    // Check if transaction exists
    const existingTransaction = await db.query.walletTransactions.findFirst({
      where: eq(walletTransactions.id, transactionId),
    });

    if (!existingTransaction) {
      return NextResponse.json(
        { message: "Transaction not found" },
        { status: 404 }
      );
    }

    // Update transaction
    const updateData: any = {
      status: validatedData.status,
      updatedAt: new Date(),
    };

    if (validatedData.adminNote) {
      // Store admin note in metadata
      const metadata = existingTransaction.metadata || {};
      metadata.adminNote = validatedData.adminNote;
      metadata.adminId = session.user.id;
      metadata.adminUpdatedAt = new Date().toISOString();
      updateData.metadata = metadata;
    }

    if (validatedData.gatewayTransactionId) {
      updateData.gatewayTransactionId = validatedData.gatewayTransactionId;
    }

    await db.update(walletTransactions)
      .set(updateData)
      .where(eq(walletTransactions.id, transactionId));

    // Handle status-specific logic
    if (validatedData.status === 'completed' && existingTransaction.status !== 'completed') {
      // Complete the transaction based on type
      try {
        switch (existingTransaction.type) {
          case 'deposit':
            await WalletService.completeDeposit(transactionId);
            break;
          case 'send':
          case 'receive':
            // These are typically completed automatically
            break;
          case 'cashout':
            // Handle cashout completion if needed
            break;
          default:
            break;
        }
      } catch (error) {
        console.error("Error completing transaction:", error);
        // Revert status update if completion fails
        await db.update(walletTransactions)
          .set({ status: existingTransaction.status })
          .where(eq(walletTransactions.id, transactionId));

        return NextResponse.json(
          {
            success: false,
            message: "Failed to complete transaction: " + (error as Error).message
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: "Transaction updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating transaction:", error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid input data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to update transaction"
      },
      { status: 500 }
    );
  }
}
