"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Card } from "@/components/ui/Card";
import { toast } from "react-hot-toast";
import {
  MagnifyingGlassIcon,
  EyeIcon,
  TrashIcon,
  BuildingStorefrontIcon,
  CheckCircleIcon,
  CubeIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

interface Store {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  logo: string | null;
  banner: string | null;
  location: string | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  productCount: number;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface UserStoresProps {
  userId: string;
}

export function UserStores({ userId }: UserStoresProps) {
  const [stores, setStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });

  useEffect(() => {
    fetchStores();
  }, [userId, pagination.page]);

  const fetchStores = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      const response = await fetch(`/api/admin/users/${userId}/stores?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch stores");
      }

      const data = await response.json();
      setStores(data.stores);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching stores:", error);
      toast.error("Failed to fetch stores");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchStores();
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (isLoading && stores.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">User Stores</h3>
          <p className="text-sm text-gray-600">
            Total: {pagination.total} stores
          </p>
        </div>

        {/* Search */}
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search stores..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              className="pl-10 w-64"
            />
          </div>
          <Button onClick={handleSearch}>Search</Button>
        </div>
      </div>

      {/* Stores List */}
      {stores.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-gray-500">
            <BuildingStorefrontIcon className="mx-auto h-12 w-12 mb-4" />
            <h3 className="text-lg font-medium mb-2">No stores found</h3>
            <p>This user hasn't created any stores yet.</p>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {stores.map((store) => (
            <Card key={store.id} className="p-6">
              {/* Store Banner */}
              <div className="mb-4">
                {store.banner ? (
                  <img
                    src={store.banner}
                    alt={store.name}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-full h-32 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <BuildingStorefrontIcon className="h-12 w-12 text-white" />
                  </div>
                )}
              </div>

              {/* Store Info */}
              <div className="space-y-3">
                {/* Store Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    {store.logo && (
                      <img
                        src={store.logo}
                        alt={store.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    )}
                    <h4 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                      <span>{store.name}</span>
                      {store.isVerified && (
                        <CheckCircleIcon className="h-5 w-5 text-blue-500" />
                      )}
                    </h4>
                  </div>
                  <Badge variant={store.isVerified ? "success" : "secondary"}>
                    {store.isVerified ? "Verified" : "Unverified"}
                  </Badge>
                </div>

                {/* Description */}
                {store.description && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {store.description}
                  </p>
                )}

                {/* Stats */}
                <div className="grid grid-cols-1 gap-4 text-sm">
                  <div className="text-center p-2 bg-blue-50 rounded">
                    <div className="font-semibold text-blue-600">{store.productCount}</div>
                    <div className="text-blue-600 text-xs">Products</div>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="text-xs text-gray-500 space-y-1">
                  {store.email && <div>Email: {store.email}</div>}
                  {store.phone && <div>Phone: {store.phone}</div>}
                  {store.location && <div>Location: {store.location}</div>}
                  {store.website && (
                    <div>
                      Website:
                      <a
                        href={store.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline ml-1"
                      >
                        {store.website}
                      </a>
                    </div>
                  )}
                </div>

                {/* Created Date */}
                <div className="text-xs text-gray-500">
                  Created: {new Date(store.createdAt).toLocaleDateString()}
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
            {pagination.total} results
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={pagination.page === page ? "primary" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              Next
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
