"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON>alog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, KeyIcon, CheckCircleIcon, EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { PinInputField } from "@/components/ui/PinInput";
import { z } from "zod";
import { toast } from "react-hot-toast";

const pinChangeSchema = z.object({
  currentPin: z.string().length(4, "PIN must be 4 digits"),
  newPin: z.string().length(4, "PIN must be 4 digits"),
  confirmPin: z.string().length(4, "PIN must be 4 digits"),
}).refine((data) => data.newPin === data.confirmPin, {
  message: "PINs don't match",
  path: ["confirmPin"],
}).refine((data) => data.currentPin !== data.newPin, {
  message: "New PIN must be different from current PIN",
  path: ["newPin"],
});

const pinSetupSchema = z.object({
  newPin: z.string().length(4, "PIN must be 4 digits"),
  confirmPin: z.string().length(4, "PIN must be 4 digits"),
}).refine((data) => data.newPin === data.confirmPin, {
  message: "PINs don't match",
  path: ["confirmPin"],
});

type PinChangeFormData = z.infer<typeof pinChangeSchema>;
type PinSetupFormData = z.infer<typeof pinSetupSchema>;

interface PinChangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  hasExistingPin: boolean;
}

export function PinChangeModal({
  isOpen,
  onClose,
  onSuccess,
  hasExistingPin
}: PinChangeModalProps) {
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<'input' | 'success'>('input');
  const [showPins, setShowPins] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const schema = hasExistingPin ? pinChangeSchema : pinSetupSchema;

  const {
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<PinChangeFormData | PinSetupFormData>({
    resolver: zodResolver(schema),
  });

  const watchedValues = watch();

  const handleClose = () => {
    reset();
    setCurrentStep('input');
    setShowPins({ current: false, new: false, confirm: false });
    onClose();
  };

  const onSubmit = async (data: PinChangeFormData | PinSetupFormData) => {
    setLoading(true);
    try {
      const endpoint = hasExistingPin ? '/api/wallet/pin/change' : '/api/wallet/pin';
      const response = await fetch(endpoint, {
        method: hasExistingPin ? 'PATCH' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentStep('success');
        toast.success(hasExistingPin ? 'PIN changed successfully!' : 'PIN set successfully!');
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      } else {
        toast.error(result.message || 'Failed to update PIN');
      }
    } catch (error) {
      console.error('Error updating PIN:', error);
      toast.error('Failed to update PIN');
    } finally {
      setLoading(false);
    }
  };

  const getPinStrength = (pin: string) => {
    if (!pin || pin.length < 4) return 0;

    const hasSequential = /0123|1234|2345|3456|4567|5678|6789/.test(pin);
    const hasRepeating = /(\d)\1{2,}/.test(pin);
    const hasCommon = ['0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999', '1234', '4321'].includes(pin);

    if (hasCommon || hasRepeating || hasSequential) return 1;

    const uniqueDigits = new Set(pin.split('')).size;
    if (uniqueDigits === 4) return 3;
    if (uniqueDigits === 3) return 2;
    return 1;
  };

  const getStrengthColor = (strength: number) => {
    switch (strength) {
      case 1: return 'text-red-600';
      case 2: return 'text-yellow-600';
      case 3: return 'text-green-600';
      default: return 'text-gray-400';
    }
  };

  const getStrengthText = (strength: number) => {
    switch (strength) {
      case 1: return 'Weak';
      case 2: return 'Medium';
      case 3: return 'Strong';
      default: return '';
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4 sm:p-6">
        <DialogPanel className="mx-auto max-w-md w-full max-h-[95vh] sm:max-h-[90vh] bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <KeyIcon className="h-6 w-6 text-blue-600" />
              </div>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                {hasExistingPin ? 'Change Wallet PIN' : 'Set Wallet PIN'}
              </DialogTitle>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {currentStep === 'input' ? (
              <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    {hasExistingPin
                      ? 'Enter your current PIN and choose a new 4-digit PIN'
                      : 'Choose a secure 4-digit PIN for your wallet'
                    }
                  </p>
                </div>

                {/* Current PIN (only if changing) */}
                {hasExistingPin && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Current PIN
                    </label>
                    <div className="relative">
                      <PinInputField
                        value={(watchedValues as PinChangeFormData)?.currentPin || ''}
                        onChange={(value) => setValue('currentPin', value)}
                        placeholder="Enter current PIN"
                        showValue={showPins.current}
                        error={hasExistingPin ? (errors as any).currentPin?.message : undefined}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPins(prev => ({ ...prev, current: !prev.current }))}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPins.current ? (
                          <EyeSlashIcon className="h-5 w-5" />
                        ) : (
                          <EyeIcon className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                    {hasExistingPin && (errors as any).currentPin && (
                      <p className="text-sm text-red-600">{(errors as any).currentPin.message}</p>
                    )}
                  </div>
                )}

                {/* New PIN */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {hasExistingPin ? 'New PIN' : 'PIN'}
                  </label>
                  <div className="relative">
                    <PinInputField
                      value={watchedValues?.newPin || ''}
                      onChange={(value) => setValue('newPin', value)}
                      placeholder="Enter new PIN"
                      showValue={showPins.new}
                      error={errors.newPin?.message}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPins(prev => ({ ...prev, new: !prev.new }))}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPins.new ? (
                        <EyeSlashIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>

                  {/* PIN Strength Indicator */}
                  {watchedValues?.newPin && watchedValues.newPin.length === 4 && (
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            getPinStrength(watchedValues.newPin) === 1 ? 'bg-red-500 w-1/3' :
                            getPinStrength(watchedValues.newPin) === 2 ? 'bg-yellow-500 w-2/3' :
                            'bg-green-500 w-full'
                          }`}
                        />
                      </div>
                      <span className={`text-xs font-medium ${getStrengthColor(getPinStrength(watchedValues.newPin))}`}>
                        {getStrengthText(getPinStrength(watchedValues.newPin))}
                      </span>
                    </div>
                  )}

                  {errors.newPin && (
                    <p className="text-sm text-red-600">{errors.newPin.message}</p>
                  )}
                </div>

                {/* Confirm PIN */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Confirm PIN
                  </label>
                  <div className="relative">
                    <PinInputField
                      value={watchedValues?.confirmPin || ''}
                      onChange={(value) => setValue('confirmPin', value)}
                      placeholder="Confirm new PIN"
                      showValue={showPins.confirm}
                      error={errors.confirmPin?.message}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPins(prev => ({ ...prev, confirm: !prev.confirm }))}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPins.confirm ? (
                        <EyeSlashIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                  {errors.confirmPin && (
                    <p className="text-sm text-red-600">{errors.confirmPin.message}</p>
                  )}
                </div>

                {/* Security Tips */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">PIN Security Tips:</h4>
                  <ul className="text-xs text-blue-800 space-y-1">
                    <li>• Avoid sequential numbers (1234, 5678)</li>
                    <li>• Don't use repeated digits (1111, 2222)</li>
                    <li>• Choose a PIN you can remember but others can't guess</li>
                    <li>• Never share your PIN with anyone</li>
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    className="flex-1"
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={loading}
                  >
                    {loading ? 'Processing...' : (hasExistingPin ? 'Change PIN' : 'Set PIN')}
                  </Button>
                </div>
              </form>
            ) : (
              /* Success State */
              <div className="p-6 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircleIcon className="h-10 w-10 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {hasExistingPin ? 'PIN Changed Successfully!' : 'PIN Set Successfully!'}
                </h3>
                <p className="text-sm text-gray-600 mb-6">
                  Your wallet PIN has been {hasExistingPin ? 'updated' : 'set up'}.
                  You can now use it for all wallet transactions.
                </p>
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            )}
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
