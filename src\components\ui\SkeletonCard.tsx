"use client";

interface SkeletonCardProps {
  variant?: "default" | "featured" | "compact";
}

export function SkeletonCard({ variant = "default" }: SkeletonCardProps) {
  if (variant === "featured") {
    return (
      <div className="rounded-3xl bg-white shadow-xl overflow-hidden animate-pulse h-[400px] flex flex-col">
        {/* Cover Image Skeleton */}
        <div className="h-52 bg-gradient-to-br from-gray-200 to-gray-300"></div>

        {/* Content Skeleton */}
        <div className="p-6 flex-1 flex flex-col">
          {/* Author Info Skeleton */}
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
            <div className="ml-3 flex-1">
              <div className="h-3 bg-gray-200 rounded w-24 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-20"></div>
            </div>
          </div>

          {/* Title Skeleton */}
          <div className="space-y-2 mb-3">
            <div className="h-5 bg-gray-200 rounded w-full"></div>
            <div className="h-5 bg-gray-200 rounded w-2/3"></div>
          </div>

          {/* Footer Skeleton */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-auto">
            <div className="flex items-center space-x-4">
              <div className="h-4 bg-gray-200 rounded w-8"></div>
              <div className="h-4 bg-gray-200 rounded w-10"></div>
              <div className="h-4 bg-gray-200 rounded w-8"></div>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-6 h-6 bg-gray-200 rounded"></div>
              <div className="w-6 h-6 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === "compact") {
    return (
      <div className="flex space-x-4 p-5 bg-white rounded-xl border border-gray-100 animate-pulse">
        {/* Cover Image Skeleton */}
        <div className="w-24 h-24 bg-gray-200 rounded-xl flex-shrink-0"></div>

        {/* Content Skeleton */}
        <div className="flex-1 min-w-0 flex flex-col justify-between">
          <div>
            {/* Title Skeleton */}
            <div className="space-y-2 mb-2">
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>

            {/* Meta Skeleton */}
            <div className="h-3 bg-gray-200 rounded w-48 mb-2"></div>
          </div>

          {/* Footer Skeleton */}
          <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="h-3 bg-gray-200 rounded w-8"></div>
              <div className="h-3 bg-gray-200 rounded w-10"></div>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-4 h-4 bg-gray-200 rounded"></div>
              <div className="w-4 h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden animate-pulse h-[400px] flex flex-col">
      {/* Cover Image Skeleton */}
      <div className="h-52 bg-gradient-to-br from-gray-200 to-gray-300"></div>

      {/* Content Skeleton */}
      <div className="p-6 flex-1 flex flex-col">
        {/* Author Info Skeleton */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
          <div className="ml-3 flex-1">
            <div className="h-3 bg-gray-200 rounded w-24 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-20"></div>
          </div>
        </div>

        {/* Title Skeleton */}
        <div className="space-y-2 mb-3">
          <div className="h-5 bg-gray-200 rounded w-full"></div>
          <div className="h-5 bg-gray-200 rounded w-2/3"></div>
        </div>

        {/* Footer Skeleton */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-auto">
          <div className="flex items-center space-x-4">
            <div className="h-4 bg-gray-200 rounded w-8"></div>
            <div className="h-4 bg-gray-200 rounded w-10"></div>
            <div className="h-4 bg-gray-200 rounded w-8"></div>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-6 h-6 bg-gray-200 rounded"></div>
            <div className="w-6 h-6 bg-gray-200 rounded"></div>
            <div className="w-6 h-6 bg-gray-200 rounded"></div>
            <div className="w-6 h-6 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
