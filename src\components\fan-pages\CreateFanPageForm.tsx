"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { 
  BuildingStorefrontIcon, 
  UserIcon, 
  MusicalNoteIcon,
  FilmIcon,
  BriefcaseIcon,
  AcademicCapIcon,
  TrophyIcon,
  MegaphoneIcon,
  NewspaperIcon,
  HeartIcon,
  EllipsisHorizontalIcon
} from "@heroicons/react/24/outline";

const categories = [
  { value: 'musician', label: 'Musician', icon: MusicalNoteIcon },
  { value: 'actor', label: 'Actor', icon: FilmIcon },
  { value: 'brand', label: 'Brand', icon: BuildingStorefrontIcon },
  { value: 'business', label: 'Business', icon: BriefcaseIcon },
  { value: 'organization', label: 'Organization', icon: AcademicCapIcon },
  { value: 'public_figure', label: 'Public Figure', icon: UserIcon },
  { value: 'artist', label: 'Artist', icon: MusicalNoteIcon },
  { value: 'writer', label: 'Writer', icon: NewspaperIcon },
  { value: 'athlete', label: 'Athlete', icon: TrophyIcon },
  { value: 'politician', label: 'Politician', icon: MegaphoneIcon },
  { value: 'entertainment', label: 'Entertainment', icon: FilmIcon },
  { value: 'media', label: 'Media', icon: NewspaperIcon },
  { value: 'community', label: 'Community', icon: HeartIcon },
  { value: 'cause', label: 'Cause', icon: HeartIcon },
  { value: 'other', label: 'Other', icon: EllipsisHorizontalIcon },
];

interface CreateFanPageFormProps {
  onSuccess?: (page: any) => void;
  onCancel?: () => void;
}

export function CreateFanPageForm({ onSuccess, onCancel }: CreateFanPageFormProps) {
  const { data: session } = useSession();
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: "",
    username: "",
    category: "",
    description: "",
    website: "",
    email: "",
    phone: "",
    location: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [usernameError, setUsernameError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear username error when user types
    if (name === "username") {
      setUsernameError(null);
    }
  };

  const validateUsername = (username: string) => {
    if (username.length < 3) {
      return "Username must be at least 3 characters long";
    }
    if (username.length > 50) {
      return "Username must be less than 50 characters";
    }
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
      return "Username can only contain letters, numbers, and underscores";
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setUsernameError(null);

    // Validate required fields
    if (!formData.name.trim()) {
      setError("Page name is required");
      return;
    }

    if (!formData.username.trim()) {
      setError("Username is required");
      return;
    }

    if (!formData.category) {
      setError("Category is required");
      return;
    }

    // Validate username
    const usernameValidation = validateUsername(formData.username);
    if (usernameValidation) {
      setUsernameError(usernameValidation);
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/fan-pages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        if (result.error === "Username is already taken") {
          setUsernameError(result.error);
        } else {
          setError(result.error || "Failed to create fan page");
        }
        return;
      }

      // Success
      if (onSuccess) {
        onSuccess(result.page);
      } else {
        router.push(`/pages/${result.page.username}`);
      }

    } catch (error) {
      console.error("Error creating fan page:", error);
      setError("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!session) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">You must be logged in to create a fan page.</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Create Fan Page</h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Page Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Page Name *
            </label>
            <Input
              id="name"
              name="name"
              type="text"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter your page name"
              required
              maxLength={255}
            />
          </div>

          {/* Username */}
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
              Username *
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                @
              </span>
              <Input
                id="username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleChange}
                placeholder="username"
                required
                className="pl-8"
                maxLength={50}
              />
            </div>
            {usernameError && (
              <p className="mt-1 text-sm text-red-600">{usernameError}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Your page will be available at /pages/{formData.username || "username"}
            </p>
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Tell people about your page..."
              rows={4}
              maxLength={1000}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="mt-1 text-sm text-gray-500">
              {formData.description.length}/1000 characters
            </p>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Contact Information (Optional)</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                  Website
                </label>
                <Input
                  id="website"
                  name="website"
                  type="url"
                  value={formData.website}
                  onChange={handleChange}
                  placeholder="https://example.com"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone
                </label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="+****************"
                  maxLength={50}
                />
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <Input
                  id="location"
                  name="location"
                  type="text"
                  value={formData.location}
                  onChange={handleChange}
                  placeholder="City, Country"
                  maxLength={255}
                />
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Creating..." : "Create Page"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
