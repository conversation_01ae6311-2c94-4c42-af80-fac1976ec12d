import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { ClipboardDocumentIcon, ShareIcon, ArrowPathIcon } from "@heroicons/react/24/outline";
import { useState, useEffect } from "react";
import { getClientAppUrl } from "@/lib/utils/url";

interface ReferralSectionProps {
  className?: string;
}

export function ReferralSection({ className = "" }: ReferralSectionProps) {
  const [referralData, setReferralData] = useState({
    referralCode: "",
    referralLink: "",
    totalReferrals: 0,
    completedReferrals: 0,
    pendingReferrals: 0,
    totalEarnings: 0,
    rewardPerReferral: 5.00,
    settings: {
      isEnabled: true,
      rewardAmount: "5.00",
      minPayoutThreshold: "10.00",
    },
  });

  const [recentReferrals, setRecentReferrals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Fetch referral data
  const fetchReferralData = async () => {
    try {
      setLoading(true);
      console.log('Fetching referral data...');

      // Fetch referral stats from API
      const statsResponse = await fetch('/api/referral/stats');
      console.log('Stats response status:', statsResponse.status);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        console.log('Stats data received:', statsData);

        if (statsData.success) {
          // Correct the referral link URL to match current domain
          const clientUrl = getClientAppUrl();
          const correctedLink = statsData.data.referralCode ?
            `${clientUrl}/signup?ref=${statsData.data.referralCode}` :
            statsData.data.referralLink;

          setReferralData({
            referralCode: statsData.data.referralCode,
            referralLink: correctedLink,
            totalReferrals: statsData.data.totalReferrals,
            completedReferrals: statsData.data.completedReferrals,
            pendingReferrals: statsData.data.pendingReferrals,
            totalEarnings: statsData.data.totalEarnings,
            rewardPerReferral: statsData.data.rewardPerReferral,
            settings: statsData.data.settings,
          });

          setRecentReferrals(statsData.data.recentReferrals || []);
          console.log('Referral data set successfully with corrected URL:', correctedLink);
        } else {
          console.error('Stats API returned success: false');
          await fetchReferralDataFallback();
        }
      } else {
        console.error('Stats API response not ok:', statsResponse.status);
        // Fallback to code generation API
        await fetchReferralDataFallback();
      }

    } catch (error) {
      console.error('Error fetching referral data:', error);
      // Try fallback method
      await fetchReferralDataFallback();
    } finally {
      setLoading(false);
    }
  };

  // Fallback method
  const fetchReferralDataFallback = async () => {
    try {
      console.log('Using fallback method...');

      // Generate referral code from API
      const codeResponse = await fetch('/api/referral/code');
      console.log('Code response status:', codeResponse.status);

      if (codeResponse.ok) {
        const codeData = await codeResponse.json();
        console.log('Code data received:', codeData);

        if (codeData.success) {
          // Generate client-side URL to ensure it matches current domain
          const clientUrl = getClientAppUrl();
          const correctedLink = `${clientUrl}/signup?ref=${codeData.data.code}`;

          setReferralData(prev => ({
            ...prev,
            referralCode: codeData.data.code,
            referralLink: correctedLink,
            settings: {
              isEnabled: true,
              rewardAmount: "5.00",
              minPayoutThreshold: "10.00",
            },
          }));
          console.log('Fallback data set successfully with corrected URL:', correctedLink);
        } else {
          console.error('Code API returned success: false');
        }
      } else {
        console.error('Code API response not ok:', codeResponse.status);
      }
    } catch (error) {
      console.error('Error in fallback referral data fetch:', error);
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchReferralData();
    setRefreshing(false);
  };

  useEffect(() => {
    fetchReferralData();
  }, []);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const shareReferralLink = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Join HIFNF',
        text: 'Join me on HIFNF using my referral link!',
        url: referralData.referralLink,
      });
    } else {
      copyToClipboard(referralData.referralLink);
    }
  };

  return (
    <div className={`space-y-4 sm:space-y-6 ${className}`}>
      {/* Section Header */}
      <div className="border-b border-gray-200 pb-3 sm:pb-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">My Refer & Earn</h2>
            <p className="text-sm text-gray-600 mt-1">Invite friends and earn rewards when they join.</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="w-full sm:w-auto"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Referral Banner */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-4 sm:p-6 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex-1">
            <h4 className="text-base sm:text-lg font-medium">Invite Friends & Earn Rewards</h4>
            <div className="mt-2 text-sm sm:text-base">
              {loading ? "Loading..." : (
                <div className="space-y-1">
                  <p>
                    Earn ${referralData.rewardPerReferral.toFixed(2)} for each friend who joins using your referral link.
                  </p>
                  {!referralData.settings.isEnabled && (
                    <span className="block text-yellow-200 text-sm mt-1">⚠️ Referral program is currently disabled</span>
                  )}
                </div>
              )}
            </div>
          </div>
          {!loading && (
            <div className="text-center sm:text-right flex-shrink-0">
              <p className="text-blue-100 text-sm">Your Code</p>
              <p className="text-lg sm:text-xl font-bold">{referralData.referralCode}</p>
            </div>
          )}
        </div>

        <div className="mt-4 flex flex-col sm:flex-row sm:items-center gap-3">
          <div className="relative flex-grow">
            <Input
              type="text"
              value={loading ? "Loading..." : referralData.referralLink}
              readOnly
              className="pr-10 bg-white/90 text-gray-900 border-0"
            />
            <button
              onClick={() => copyToClipboard(referralData.referralLink)}
              disabled={loading}
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 disabled:opacity-50"
              title={copySuccess ? "Copied!" : "Copy to clipboard"}
            >
              <ClipboardDocumentIcon className={`h-5 w-5 ${copySuccess ? 'text-green-600' : ''}`} />
            </button>
          </div>
          <Button
            onClick={shareReferralLink}
            disabled={loading}
            className="bg-white/20 hover:bg-white/30 text-white border-white/30 disabled:opacity-50"
          >
            <ShareIcon className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </div>

      {/* Referral Stats */}
      {loading ? (
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4 animate-pulse">
              <div className="h-3 bg-gray-200 rounded w-20 mb-2"></div>
              <div className="h-5 bg-gray-200 rounded w-12"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
          <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4">
            <dt className="text-xs font-medium text-gray-500 truncate">Total Referrals</dt>
            <dd className="mt-1 text-lg font-semibold text-gray-900">
              {referralData.totalReferrals}
            </dd>
          </div>

          <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4">
            <dt className="text-xs font-medium text-gray-500 truncate">Completed</dt>
            <dd className="mt-1 text-lg font-semibold text-gray-900">
              {referralData.completedReferrals}
            </dd>
          </div>

          <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4">
            <dt className="text-xs font-medium text-gray-500 truncate">Pending</dt>
            <dd className="mt-1 text-lg font-semibold text-gray-900">
              {referralData.pendingReferrals}
            </dd>
          </div>

          <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4">
            <dt className="text-xs font-medium text-gray-500 truncate">Total Earnings</dt>
            <dd className="mt-1 text-lg font-semibold text-gray-900">
              ${referralData.totalEarnings.toFixed(2)}
            </dd>
          </div>
        </div>
      )}

      {/* Reward Info */}
      {!loading && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-blue-900">Referral Reward</h4>
              <p className="text-sm text-blue-700 mt-1">
                Earn ${referralData.rewardPerReferral.toFixed(2)} for each successful referral
              </p>
            </div>
            <div className="text-right">
              <p className="text-xs text-blue-600">Min. Payout</p>
              <p className="text-lg font-bold text-blue-900">${parseFloat(referralData.settings.minPayoutThreshold).toFixed(2)}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Recent Referrals */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-sm font-medium text-gray-900">Recent Referrals</h4>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading referrals...</p>
          </div>
        ) : recentReferrals.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reward
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentReferrals.map((referral: any) => (
                  <tr key={referral.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                      {referral.name}
                      {referral.username && (
                        <div className="text-xs text-gray-500">@{referral.username}</div>
                      )}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {referral.date}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right font-medium text-green-600">
                      ${referral.reward.toFixed(2)}
                      {referral.paidAt && (
                        <div className="text-xs text-gray-500">Paid</div>
                      )}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                      <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                        referral.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : referral.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {referral.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg m-4">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No referrals yet</h3>
            <p className="text-gray-500">Share your referral link to start earning!</p>
            <p className="text-gray-500 mt-1">Earn ${referralData.rewardPerReferral.toFixed(2)} for each successful referral</p>
          </div>
        )}

        {!loading && recentReferrals.length > 0 && (
          <div className="p-4 border-t border-gray-200 flex justify-center">
            <Button variant="outline" size="sm">
              View All Referrals
            </Button>
          </div>
        )}
      </div>
      
      {/* How It Works */}
      <div className="border border-gray-200 rounded-lg p-4 bg-white">
        <h4 className="text-sm font-medium text-gray-900 mb-3">How It Works</h4>
        <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
          <li>
            <span className="font-medium">Share your referral link</span>
            <span> with friends and family</span>
          </li>
          <li>
            <span className="font-medium">They sign up</span>
            <span> using your unique link</span>
          </li>
          <li>
            <span className="font-medium">You both receive rewards</span>
            <span> when they complete the requirements</span>
          </li>
        </ol>
      </div>
    </div>
  );
}
