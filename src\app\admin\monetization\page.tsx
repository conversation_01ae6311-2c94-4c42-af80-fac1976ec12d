"use client";

import { useState } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { MonetizationSettings } from "@/components/admin/monetization/MonetizationSettings";
import { MonetizationRequests } from "@/components/admin/monetization/MonetizationRequests";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import {
  CogIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CurrencyDollarIcon
} from "@heroicons/react/24/outline";

type TabType = 'settings' | 'requests' | 'analytics';

export default function AdminMonetizationPage() {
  const [activeTab, setActiveTab] = useState<TabType>('requests');

  const tabs = [
    {
      id: 'requests' as TabType,
      name: 'Monetization Requests',
      icon: DocumentTextIcon,
      description: 'Review and approve blog monetization requests',
    },
    {
      id: 'settings' as TabType,
      name: 'Settings',
      icon: CogIcon,
      description: 'Configure monetization parameters',
    },
    {
      id: 'analytics' as TabType,
      name: 'Analytics',
      icon: ChartBarIcon,
      description: 'View monetization statistics and reports',
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'settings':
        return <MonetizationSettings />;
      case 'requests':
        return <MonetizationRequests />;
      case 'analytics':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ChartBarIcon className="h-5 w-5" />
                Monetization Analytics
              </CardTitle>
              <CardDescription>
                Analytics and reporting features coming soon
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <CurrencyDollarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Analytics Dashboard
                </h3>
                <p className="text-gray-600 mb-6">
                  Comprehensive analytics and reporting for blog monetization will be available here.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900">Revenue Tracking</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Monitor total payouts and earnings
                    </p>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900">Read Analytics</h4>
                    <p className="text-sm text-green-700 mt-1">
                      Track qualified reads and engagement
                    </p>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900">Author Performance</h4>
                    <p className="text-sm text-purple-700 mt-1">
                      Analyze top-performing authors
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      default:
        return <MonetizationRequests />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <div>
                <a href="/admin/earning-world" className="text-gray-400 hover:text-gray-500">
                  <ChartBarIcon className="h-5 w-5" aria-hidden="true" />
                  <span className="sr-only">Earning World</span>
                </a>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="h-5 w-5 flex-shrink-0 text-gray-300" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                  <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                </svg>
                <a href="/admin/earning-world" className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                  Earning World
                </a>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="h-5 w-5 flex-shrink-0 text-gray-300" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                  <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                </svg>
                <span className="ml-4 text-sm font-medium text-gray-900">Blog Monetization</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Blog Monetization</h1>
            <p className="mt-2 text-gray-600">
              Manage blog monetization settings, review requests, and track performance
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => window.location.href = '/admin/earning-world'}
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Back to Overview
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon
                    className={`mr-2 h-5 w-5 ${
                      activeTab === tab.id
                        ? 'text-blue-500'
                        : 'text-gray-400 group-hover:text-gray-500'
                    }`}
                  />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {renderTabContent()}
        </div>
      </div>
    </AdminLayout>
  );
}
