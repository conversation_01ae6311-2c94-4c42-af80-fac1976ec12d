"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import {
  BanknotesIcon,
  FunnelIcon,
  ArrowPathIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  image?: string;
}

interface Withdrawal {
  id: string;
  userId: string;
  amount: string;
  fee: string;
  netAmount: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  metadata?: {
    accountType: string;
    accountNumber: string;
    accountName: string;
  };
  note?: string;
  gatewayTransactionId?: string;
  reference?: string;
  createdAt: string;
  updatedAt: string;
  user: User;
}

interface WithdrawalStats {
  pending: number;
  processing: number;
  completed: number;
}

interface WithdrawalResponse {
  withdrawals: Withdrawal[];
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
  };
  stats: WithdrawalStats;
}

export default function AdminWithdrawalsPage() {
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([]);
  const [stats, setStats] = useState<WithdrawalStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<Withdrawal | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [processingId, setProcessingId] = useState<string | null>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [limit] = useState(20);

  // Filters
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    dateFrom: "",
    dateTo: "",
  });

  const fetchWithdrawals = async (page = 1) => {
    try {
      setLoading(page === 1);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.status && { status: filters.status }),
        ...(filters.dateFrom && { dateFrom: filters.dateFrom }),
        ...(filters.dateTo && { dateTo: filters.dateTo }),
      });

      const response = await fetch(`/api/admin/withdrawals?${params}`);
      const result = await response.json();

      if (result.success) {
        setWithdrawals(result.data.withdrawals);
        setStats(result.data.stats);
        setCurrentPage(result.data.pagination.page);
        setTotalPages(result.data.pagination.totalPages);
        setTotalCount(result.data.pagination.totalCount);
      } else {
        toast.error(result.message || "Failed to fetch withdrawals");
      }
    } catch (error) {
      console.error("Error fetching withdrawals:", error);
      toast.error("Failed to fetch withdrawals");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchWithdrawals(currentPage);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    setCurrentPage(1);
    fetchWithdrawals(1);
  };

  const clearFilters = () => {
    setFilters({
      search: "",
      status: "",
      dateFrom: "",
      dateTo: "",
    });
    setCurrentPage(1);
    fetchWithdrawals(1);
  };

  const handleStatusUpdate = async (withdrawalId: string, status: string, adminNote?: string) => {
    try {
      setProcessingId(withdrawalId);

      const response = await fetch(`/api/admin/withdrawals/${withdrawalId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          adminNote,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message);
        fetchWithdrawals(currentPage);
        setIsDetailsModalOpen(false);
      } else {
        toast.error(result.message || "Failed to update withdrawal");
      }
    } catch (error) {
      console.error("Error updating withdrawal:", error);
      toast.error("Failed to update withdrawal");
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", icon: ClockIcon },
      processing: { color: "bg-blue-100 text-blue-800", icon: ArrowPathIcon },
      completed: { color: "bg-green-100 text-green-800", icon: CheckIcon },
      failed: { color: "bg-red-100 text-red-800", icon: XMarkIcon },
      cancelled: { color: "bg-gray-100 text-gray-800", icon: XMarkIcon },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config?.icon || ClockIcon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config?.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  useEffect(() => {
    fetchWithdrawals();
  }, []);

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Withdrawal Management</h1>
            <p className="text-gray-600 mt-1">
              Monitor and manage user withdrawal requests
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <FunnelIcon className="h-5 w-5 mr-2" />
              Filters
            </Button>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArrowPathIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Processing</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.processing}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completed}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        {showFilters && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  placeholder="Name, email, username..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  From Date
                </label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  To Date
                </label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-4">
              <Button onClick={applyFilters}>Apply Filters</Button>
              <Button variant="outline" onClick={clearFilters}>Clear Filters</Button>
            </div>
          </div>
        )}

        {/* Withdrawals Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Withdrawal Requests ({totalCount})
            </h3>
          </div>

          {loading ? (
            <div className="p-8 text-center">
              <ArrowPathIcon className="h-8 w-8 animate-spin mx-auto text-gray-400" />
              <p className="text-gray-500 mt-2">Loading withdrawals...</p>
            </div>
          ) : withdrawals.length === 0 ? (
            <div className="p-8 text-center">
              <BanknotesIcon className="h-12 w-12 mx-auto text-gray-300" />
              <p className="text-gray-500 mt-2">No withdrawal requests found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Account Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {withdrawals.map((withdrawal) => (
                    <tr key={withdrawal.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {withdrawal.user.image ? (
                              <img
                                className="h-10 w-10 rounded-full"
                                src={withdrawal.user.image}
                                alt=""
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {withdrawal.user.name?.charAt(0) || 'U'}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {withdrawal.user.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              @{withdrawal.user.username}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          ${withdrawal.amount}
                        </div>
                        <div className="text-sm text-gray-500">
                          Fee: ${withdrawal.fee}
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          Net: ${withdrawal.netAmount}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {withdrawal.metadata?.accountType}
                        </div>
                        <div className="text-sm text-gray-500">
                          {withdrawal.metadata?.accountNumber}
                        </div>
                        <div className="text-sm text-gray-500">
                          {withdrawal.metadata?.accountName}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(withdrawal.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(withdrawal.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedWithdrawal(withdrawal);
                              setIsDetailsModalOpen(true);
                            }}
                          >
                            <EyeIcon className="h-4 w-4 mr-1" />
                            View
                          </Button>

                          {withdrawal.status === 'pending' && (
                            <>
                              <Button
                                size="sm"
                                onClick={() => handleStatusUpdate(withdrawal.id, 'processing')}
                                disabled={processingId === withdrawal.id}
                              >
                                Process
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleStatusUpdate(withdrawal.id, 'cancelled')}
                                disabled={processingId === withdrawal.id}
                              >
                                Cancel
                              </Button>
                            </>
                          )}

                          {withdrawal.status === 'processing' && (
                            <>
                              <Button
                                size="sm"
                                onClick={() => handleStatusUpdate(withdrawal.id, 'completed')}
                                disabled={processingId === withdrawal.id}
                              >
                                Complete
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleStatusUpdate(withdrawal.id, 'failed')}
                                disabled={processingId === withdrawal.id}
                              >
                                Mark Failed
                              </Button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * limit) + 1} to {Math.min(currentPage * limit, totalCount)} of {totalCount} results
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchWithdrawals(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                >
                  Previous
                </Button>
                <span className="px-3 py-2 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchWithdrawals(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Withdrawal Details Modal */}
        {isDetailsModalOpen && selectedWithdrawal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">
                    Withdrawal Details
                  </h3>
                  <button
                    onClick={() => setIsDetailsModalOpen(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-6">
                {/* User Info */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">User Information</h4>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-3">
                      {selectedWithdrawal.user.image ? (
                        <img
                          className="h-12 w-12 rounded-full"
                          src={selectedWithdrawal.user.image}
                          alt=""
                        />
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                          <span className="text-lg font-medium text-gray-700">
                            {selectedWithdrawal.user.name?.charAt(0) || 'U'}
                          </span>
                        </div>
                      )}
                      <div>
                        <p className="font-medium text-gray-900">{selectedWithdrawal.user.name}</p>
                        <p className="text-sm text-gray-500">@{selectedWithdrawal.user.username}</p>
                        <p className="text-sm text-gray-500">{selectedWithdrawal.user.email}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Amount Details */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Amount Details</h4>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Withdrawal Amount:</span>
                      <span className="text-sm font-medium">${selectedWithdrawal.amount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Fee:</span>
                      <span className="text-sm font-medium">${selectedWithdrawal.fee}</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-sm font-medium text-gray-900">Net Amount:</span>
                      <span className="text-sm font-bold text-gray-900">${selectedWithdrawal.netAmount}</span>
                    </div>
                  </div>
                </div>

                {/* Account Details */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Account Details</h4>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Account Type:</span>
                      <span className="text-sm font-medium">{selectedWithdrawal.metadata?.accountType}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Account Number:</span>
                      <span className="text-sm font-medium">{selectedWithdrawal.metadata?.accountNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Account Name:</span>
                      <span className="text-sm font-medium">{selectedWithdrawal.metadata?.accountName}</span>
                    </div>
                  </div>
                </div>

                {/* Status & Dates */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Status & Timeline</h4>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Status:</span>
                      {getStatusBadge(selectedWithdrawal.status)}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Created:</span>
                      <span className="text-sm font-medium">{formatDate(selectedWithdrawal.createdAt)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Updated:</span>
                      <span className="text-sm font-medium">{formatDate(selectedWithdrawal.updatedAt)}</span>
                    </div>
                    {(selectedWithdrawal.metadata as any)?.processedAt && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Processed:</span>
                        <span className="text-sm font-medium">{formatDate((selectedWithdrawal.metadata as any).processedAt)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Notes */}
                {(selectedWithdrawal.note || (selectedWithdrawal.metadata as any)?.adminNote) && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Notes</h4>
                    <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                      {selectedWithdrawal.note && (
                        <div>
                          <p className="text-sm font-medium text-gray-700">User Note:</p>
                          <p className="text-sm text-gray-600">{selectedWithdrawal.note}</p>
                        </div>
                      )}
                      {(selectedWithdrawal.metadata as any)?.adminNote && (
                        <div>
                          <p className="text-sm font-medium text-gray-700">Admin Note:</p>
                          <p className="text-sm text-gray-600">{(selectedWithdrawal.metadata as any).adminNote}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4 border-t border-gray-200">
                  {selectedWithdrawal.status === 'pending' && (
                    <>
                      <Button
                        onClick={() => handleStatusUpdate(selectedWithdrawal.id, 'processing')}
                        disabled={processingId === selectedWithdrawal.id}
                      >
                        Mark as Processing
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleStatusUpdate(selectedWithdrawal.id, 'cancelled')}
                        disabled={processingId === selectedWithdrawal.id}
                      >
                        Cancel Request
                      </Button>
                    </>
                  )}

                  {selectedWithdrawal.status === 'processing' && (
                    <>
                      <Button
                        onClick={() => handleStatusUpdate(selectedWithdrawal.id, 'completed')}
                        disabled={processingId === selectedWithdrawal.id}
                      >
                        Mark as Completed
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleStatusUpdate(selectedWithdrawal.id, 'failed')}
                        disabled={processingId === selectedWithdrawal.id}
                      >
                        Mark as Failed
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
