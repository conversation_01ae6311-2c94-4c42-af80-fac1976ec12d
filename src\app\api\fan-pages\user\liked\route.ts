import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageFollowers } from "@/lib/db/schema";
import { eq, desc, like, and } from "drizzle-orm";

// GET /api/fan-pages/user/liked - Get user's liked/followed fan pages
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search");
    const category = searchParams.get("category");
    const offset = (page - 1) * limit;

    // Build where conditions for fan pages
    let pageWhereConditions = [eq(fanPages.isActive, true)];

    if (search) {
      pageWhereConditions.push(like(fanPages.name, `%${search}%`));
    }

    if (category && category !== "all") {
      pageWhereConditions.push(eq(fanPages.category, category));
    }

    // Get user's liked/followed pages
    const likedPages = await db
      .select({
        id: fanPages.id,
        name: fanPages.name,
        username: fanPages.username,
        category: fanPages.category,
        description: fanPages.description,
        profileImage: fanPages.profileImage,
        isVerified: fanPages.isVerified,
        followerCount: fanPages.followerCount,
        createdAt: fanPages.createdAt,
        followedAt: fanPageFollowers.createdAt,
      })
      .from(fanPageFollowers)
      .innerJoin(fanPages, eq(fanPageFollowers.fanPageId, fanPages.id))
      .where(
        and(
          eq(fanPageFollowers.userId, session.user.id),
          ...pageWhereConditions
        )
      )
      .orderBy(desc(fanPageFollowers.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await db
      .select({ count: fanPageFollowers.id })
      .from(fanPageFollowers)
      .innerJoin(fanPages, eq(fanPageFollowers.fanPageId, fanPages.id))
      .where(
        and(
          eq(fanPageFollowers.userId, session.user.id),
          ...pageWhereConditions
        )
      );

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    // Add isFollowing flag (always true for liked pages)
    const pagesWithFollowStatus = likedPages.map(page => ({
      id: page.id,
      name: page.name,
      username: page.username,
      category: page.category,
      description: page.description,
      profileImage: page.profileImage,
      isVerified: page.isVerified,
      followerCount: page.followerCount,
      createdAt: page.createdAt,
      isFollowing: true, // User is following these pages
    }));

    return NextResponse.json({
      pages: pagesWithFollowStatus,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });

  } catch (error) {
    console.error("Error fetching user's liked fan pages:", error);
    return NextResponse.json(
      { error: "Failed to fetch liked pages" },
      { status: 500 }
    );
  }
}
