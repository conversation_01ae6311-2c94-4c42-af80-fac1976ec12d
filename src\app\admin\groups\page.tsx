"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from "@/components/ui/Tabs";
import { Select } from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { toast } from "react-hot-toast";
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  TrashIcon,
  PencilIcon,
  UserGroupIcon,
  EyeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  XMarkIcon,
  CheckIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";
import { formatTimeAgo } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import { CreateGroupModal } from "@/components/admin/groups/CreateGroupModal";
import { EditGroupModal } from "@/components/admin/groups/EditGroupModal";
import { DeleteGroupModal } from "@/components/admin/groups/DeleteGroupModal";

interface Group {
  id: string;
  name: string;
  description: string | null;
  visibility: "public" | "private-visible" | "private-hidden";
  category: string | null;
  rules: string | null;
  postPermission: "all-members" | "admin-only";
  coverImage: string | null;
  createdAt: string;
  updatedAt: string;
  memberCount: number;
}

export default function AdminGroupsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [groups, setGroups] = useState<Group[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<Group[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    visibility: "all",
    category: "all",
    dateRange: "all",
    memberCount: "all",
  });
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentGroup, setCurrentGroup] = useState<Group | null>(null);
  const [bulkAction, setBulkAction] = useState("");

  // Fetch groups data
  useEffect(() => {
    fetchGroups();
  }, [currentPage, sortBy, sortOrder]);

  // Apply filters and search
  useEffect(() => {
    if (groups.length > 0) {
      let result = [...groups];

      // Apply search
      if (searchTerm) {
        result = result.filter(
          (group) =>
            group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (group.description &&
              group.description.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply filters
      if (filters.visibility !== "all") {
        result = result.filter((group) => group.visibility === filters.visibility);
      }

      if (filters.category !== "all") {
        result = result.filter((group) => group.category === filters.category);
      }

      // Apply date range filter
      if (filters.dateRange !== "all") {
        const now = new Date();
        let dateLimit;

        switch (filters.dateRange) {
          case "today":
            dateLimit = new Date(now.setDate(now.getDate() - 1));
            break;
          case "week":
            dateLimit = new Date(now.setDate(now.getDate() - 7));
            break;
          case "month":
            dateLimit = new Date(now.setMonth(now.getMonth() - 1));
            break;
          case "year":
            dateLimit = new Date(now.setFullYear(now.getFullYear() - 1));
            break;
          default:
            dateLimit = null;
        }

        if (dateLimit) {
          result = result.filter(
            (group) => new Date(group.createdAt) > dateLimit
          );
        }
      }

      // Apply member count filter
      if (filters.memberCount !== "all") {
        switch (filters.memberCount) {
          case "small":
            result = result.filter((group) => group.memberCount < 10);
            break;
          case "medium":
            result = result.filter(
              (group) => group.memberCount >= 10 && group.memberCount < 50
            );
            break;
          case "large":
            result = result.filter((group) => group.memberCount >= 50);
            break;
          default:
            break;
        }
      }

      setFilteredGroups(result);
    }
  }, [groups, searchTerm, filters]);

  const fetchGroups = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/admin/groups?page=${currentPage}&limit=10&sort=${sortBy}&order=${sortOrder}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch groups");
      }

      const data = await response.json();
      setGroups(data.groups);
      setFilteredGroups(data.groups);
      setTotalPages(data.totalPages || 1);
    } catch (error) {
      console.error("Error fetching groups:", error);
      toast.error("Failed to load groups");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  const handleSelectGroup = (groupId: string) => {
    setSelectedGroups((prev) =>
      prev.includes(groupId)
        ? prev.filter((id) => id !== groupId)
        : [...prev, groupId]
    );
  };

  const handleSelectAllGroups = () => {
    if (selectedGroups.length === filteredGroups.length) {
      setSelectedGroups([]);
    } else {
      setSelectedGroups(filteredGroups.map((group) => group.id));
    }
  };

  const handleEditGroup = (group: Group) => {
    setCurrentGroup(group);
    setIsEditModalOpen(true);
  };

  const handleDeleteGroup = (group: Group) => {
    setCurrentGroup(group);
    setIsDeleteModalOpen(true);
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedGroups.length === 0) return;

    try {
      if (bulkAction === "delete") {
        const confirmed = window.confirm(
          `Are you sure you want to delete ${selectedGroups.length} groups? This action cannot be undone.`
        );

        if (!confirmed) return;

        const response = await fetch("/api/admin/groups/bulk", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ groupIds: selectedGroups }),
        });

        if (!response.ok) {
          throw new Error("Failed to delete groups");
        }

        toast.success(`${selectedGroups.length} groups deleted successfully`);
        fetchGroups();
        setSelectedGroups([]);
      }
    } catch (error) {
      console.error("Error performing bulk action:", error);
      toast.error("Failed to perform bulk action");
    }
  };

  const handleCreateSuccess = (newGroup: Group) => {
    toast.success("Group created successfully");
    fetchGroups();
    setIsCreateModalOpen(false);
  };

  const handleEditSuccess = (updatedGroup: Group) => {
    toast.success("Group updated successfully");
    fetchGroups();
    setIsEditModalOpen(false);
  };

  const handleDeleteSuccess = () => {
    toast.success("Group deleted successfully");
    fetchGroups();
    setIsDeleteModalOpen(false);
  };

  const getVisibilityBadge = (visibility: "public" | "private-visible" | "private-hidden") => {
    switch (visibility) {
      case "public":
        return (
          <Badge variant="success" className="flex items-center">
            <GlobeAltIcon className="mr-1 h-3 w-3" />
            Public
          </Badge>
        );
      case "private-visible":
        return (
          <Badge variant="warning" className="flex items-center">
            <EyeIcon className="mr-1 h-3 w-3" />
            Private (Visible)
          </Badge>
        );
      case "private-hidden":
        return (
          <Badge variant="danger" className="flex items-center">
            <LockClosedIcon className="mr-1 h-3 w-3" />
            Private (Hidden)
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Group Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage all groups, their settings, and members
          </p>
        </div>
        <div className="mt-4 flex space-x-2 sm:mt-0">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <FunnelIcon className="mr-2 h-5 w-5" />
            Filters
          </Button>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <PlusIcon className="mr-2 h-5 w-5" />
            Create Group
          </Button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="mb-6">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
          <div className="relative flex-grow">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search groups..."
              className="pl-10"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
          <Button
            onClick={fetchGroups}
            variant="outline"
            className="flex-shrink-0"
          >
            <ArrowPathIcon className="mr-2 h-5 w-5" />
            Refresh
          </Button>
        </div>

        {showFilters && (
          <div className="mt-4 rounded-lg border border-gray-200 bg-white p-4">
            <h3 className="mb-3 font-medium text-gray-700">Filter Groups</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Visibility
                </label>
                <Select
                  value={filters.visibility}
                  onChange={(e) => handleFilterChange("visibility", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Visibilities</option>
                  <option value="public">Public</option>
                  <option value="private-visible">Private (Visible)</option>
                  <option value="private-hidden">Private (Hidden)</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Category
                </label>
                <Select
                  value={filters.category}
                  onChange={(e) => handleFilterChange("category", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Categories</option>
                  <option value="social">Social</option>
                  <option value="business">Business</option>
                  <option value="education">Education</option>
                  <option value="entertainment">Entertainment</option>
                  <option value="gaming">Gaming</option>
                  <option value="technology">Technology</option>
                  <option value="health">Health</option>
                  <option value="other">Other</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Created
                </label>
                <Select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange("dateRange", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="year">This Year</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Member Count
                </label>
                <Select
                  value={filters.memberCount}
                  onChange={(e) => handleFilterChange("memberCount", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Sizes</option>
                  <option value="small">Small (&lt;10)</option>
                  <option value="medium">Medium (10-50)</option>
                  <option value="large">Large (&gt;50)</option>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk actions */}
      {selectedGroups.length > 0 && (
        <div className="mb-4 flex items-center justify-between rounded-lg bg-blue-50 p-3">
          <span className="text-sm font-medium text-blue-700">
            {selectedGroups.length} groups selected
          </span>
          <div className="flex items-center space-x-2">
            <Select
              value={bulkAction}
              onChange={(e) => setBulkAction(e.target.value)}
              className="text-sm"
            >
              <option value="">Bulk Action</option>
              <option value="delete">Delete Selected</option>
            </Select>
            <Button
              onClick={handleBulkAction}
              disabled={!bulkAction}
              size="sm"
              variant={bulkAction === "delete" ? "danger" : "primary"}
            >
              Apply
            </Button>
          </div>
        </div>
      )}

      {/* Groups table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Spinner size="lg" />
          </div>
        ) : filteredGroups.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center p-4 text-center">
            <UserGroupIcon className="mb-2 h-12 w-12 text-gray-400" />
            <h3 className="mb-1 text-lg font-medium text-gray-900">No groups found</h3>
            <p className="text-gray-500">
              {searchTerm || Object.values(filters).some((v) => v !== "all")
                ? "Try adjusting your search or filters"
                : "Create your first group to get started"}
            </p>
            {(searchTerm || Object.values(filters).some((v) => v !== "all")) && (
              <Button
                onClick={() => {
                  setSearchTerm("");
                  setFilters({
                    visibility: "all",
                    category: "all",
                    dateRange: "all",
                    memberCount: "all",
                  });
                }}
                variant="outline"
                className="mt-4"
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="w-12 px-3 py-3 text-left">
                    <Checkbox
                      checked={
                        selectedGroups.length > 0 &&
                        selectedGroups.length === filteredGroups.length
                      }
                      onChange={handleSelectAllGroups}
                      aria-label="Select all groups"
                    />
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("name")}
                    >
                      Group
                      {sortBy === "name" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("memberCount")}
                    >
                      Members
                      {sortBy === "memberCount" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("visibility")}
                    >
                      Visibility
                      {sortBy === "visibility" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("createdAt")}
                    >
                      Created
                      {sortBy === "createdAt" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {filteredGroups.map((group) => (
                  <tr key={group.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-3 py-4">
                      <Checkbox
                        checked={selectedGroups.includes(group.id)}
                        onChange={() => handleSelectGroup(group.id)}
                        aria-label={`Select ${group.name}`}
                      />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          {group.coverImage ? (
                            <Image
                              src={group.coverImage}
                              alt={group.name}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          ) : (
                            <div className="flex h-10 w-10 items-center justify-center rounded-md bg-blue-100 text-blue-600">
                              <UserGroupIcon className="h-6 w-6" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {group.name}
                          </div>
                          {group.category && (
                            <div className="text-xs text-gray-500">
                              {group.category}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {group.memberCount || 0}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {getVisibilityBadge(group.visibility)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {formatTimeAgo(group.createdAt)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Button
                          onClick={() => router.push(`/admin/groups/${group.id}`)}
                          variant="outline"
                          size="sm"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleEditGroup(group)}
                          variant="outline"
                          size="sm"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleDeleteGroup(group)}
                          variant="danger"
                          size="sm"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && filteredGroups.length > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{filteredGroups.length}</span>{" "}
            groups
          </div>
          <div className="flex space-x-1">
            <Button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            <Button
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modals */}
      <CreateGroupModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />

      {currentGroup && (
        <>
          <EditGroupModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSuccess={handleEditSuccess}
            group={currentGroup}
          />

          <DeleteGroupModal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            onSuccess={handleDeleteSuccess}
            group={currentGroup}
          />
        </>
      )}
    </AdminLayout>
  );
}
