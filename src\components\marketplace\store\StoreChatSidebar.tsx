"use client";

import { useState } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import {
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  FaceSmileIcon,
  PhotoIcon,
  PaperClipIcon
} from "@heroicons/react/24/outline";

interface StoreChatSidebarProps {
  storeId: string;
  storeName: string;
  storeLogo: string | null;
  isOwner: boolean;
}

export function StoreChatSidebar({
  storeId,
  storeName,
  storeLogo,
  isOwner
}: StoreChatSidebarProps) {
  const [message, setMessage] = useState("");

  // Mock data for recent messages - in a real app, this would come from the database
  const recentMessages = [
    {
      id: "1",
      sender: {
        id: "user1",
        name: "<PERSON>",
        image: null,
        isStore: false
      },
      message: "Do you have this product in blue color?",
      timestamp: new Date(Date.now() - 1000 * 60 * 5) // 5 minutes ago
    },
    {
      id: "2",
      sender: {
        id: storeId,
        name: storeName,
        image: storeLogo,
        isStore: true
      },
      message: "Yes, we have it in blue. Would you like to place an order?",
      timestamp: new Date(Date.now() - 1000 * 60 * 4) // 4 minutes ago
    },
    {
      id: "3",
      sender: {
        id: "user1",
        name: "John Doe",
        image: null,
        isStore: false
      },
      message: "Great! What's the price including shipping?",
      timestamp: new Date(Date.now() - 1000 * 60 * 3) // 3 minutes ago
    }
  ];

  const handleSendMessage = () => {
    if (!message.trim()) return;

    // In a real app, this would send the message to the server
    console.log("Sending message:", message);

    // Clear the input
    setMessage("");
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isOwner) {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-6">
            <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-gray-900">Store Chat</h3>
            <p className="text-sm text-gray-500 mb-4">
              As the store owner, you can manage customer messages from your dashboard.
            </p>
            <Link href="/my-store/messages">
              <Button variant="primary">
                Go to Messages
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow flex flex-col h-[600px]">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-100 mr-3">
            {storeLogo ? (
              <OptimizedImage
                src={storeLogo}
                alt={storeName}
                width={40}
                height={40}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-blue-100">
                <span className="text-lg font-semibold text-blue-500">
                  {storeName.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>
          <div>
            <h3 className="font-medium text-gray-900">{storeName}</h3>
            <p className="text-xs text-green-500">Online</p>
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
        <div className="space-y-4">
          {recentMessages.map((msg) => (
            <div
              key={msg.id}
              className={`flex ${msg.sender.isStore ? 'justify-start' : 'justify-end'}`}
            >
              <div className={`flex max-w-[80%] ${msg.sender.isStore ? 'flex-row' : 'flex-row-reverse'}`}>
                <div className={`rounded-lg p-3 ${
                  msg.sender.isStore
                    ? 'bg-white text-gray-800 rounded-tl-none'
                    : 'bg-blue-600 text-white rounded-tr-none'
                }`}>
                  <p className="text-sm">{msg.message}</p>
                  <p className={`text-xs mt-1 ${
                    msg.sender.isStore ? 'text-gray-500' : 'text-blue-100'
                  }`}>
                    {formatTime(msg.timestamp)}
                  </p>
                </div>

                {msg.sender.isStore && (
                  <div className="h-8 w-8 rounded-full overflow-hidden bg-gray-100 mr-2 self-end">
                    {msg.sender.image ? (
                      <OptimizedImage
                        src={msg.sender.image}
                        alt={msg.sender.name}
                        width={32}
                        height={32}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center bg-blue-100">
                        <span className="text-xs font-semibold text-blue-500">
                          {msg.sender.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chat Input */}
      <div className="p-3 border-t border-gray-200">
        <div className="flex items-center">
          <div className="flex space-x-1 mr-2">
            <button className="p-2 rounded-full text-gray-500 hover:bg-gray-100">
              <FaceSmileIcon className="h-5 w-5" />
            </button>
            <button className="p-2 rounded-full text-gray-500 hover:bg-gray-100">
              <PhotoIcon className="h-5 w-5" />
            </button>
            <button className="p-2 rounded-full text-gray-500 hover:bg-gray-100">
              <PaperClipIcon className="h-5 w-5" />
            </button>
          </div>

          <Input
            type="text"
            placeholder="Type a message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="flex-1"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />

          <Button
            className="ml-2"
            onClick={handleSendMessage}
            disabled={!message.trim()}
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}
