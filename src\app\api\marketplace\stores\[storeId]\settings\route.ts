import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, storeSettings } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

const storeSettingsSchema = z.object({
  visibility: z.enum(["public", "private"]).optional(),
  showOutOfStock: z.boolean().optional(),
  showProductViews: z.boolean().optional(),
  emailNotifications: z.boolean().optional(),
  productViewNotifications: z.boolean().optional(),
});

export async function GET(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const params = await context.params;
    const storeId = params.storeId;

    // Check if store exists
    const existingStore = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Get store settings
    const settings = await db
      .select()
      .from(storeSettings)
      .where(eq(storeSettings.storeId, storeId))
      .limit(1);

    // If settings don't exist, return default settings
    if (settings.length === 0) {
      return NextResponse.json({
        visibility: "public",
        showOutOfStock: true,
        showProductViews: true,
        emailNotifications: true,
        productViewNotifications: false,
      });
    }

    return NextResponse.json(settings[0]);
  } catch (error) {
    console.error("Error fetching store settings:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const storeId = params.storeId;
    const body = await request.json();
    const validatedData = storeSettingsSchema.parse(body);

    // Check if store exists and belongs to the user
    const existingStore = await db
      .select()
      .from(stores)
      .where(
        and(
          eq(stores.id, storeId),
          eq(stores.ownerId, session.user.id)
        )
      )
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found or you don't have permission to update it" },
        { status: 404 }
      );
    }

    // Check if settings already exist
    const existingSettings = await db
      .select()
      .from(storeSettings)
      .where(eq(storeSettings.storeId, storeId))
      .limit(1);

    let result;

    if (existingSettings.length === 0) {
      // Create new settings
      result = await db.insert(storeSettings).values({
        id: uuidv4(),
        storeId,
        ...validatedData,
      });
    } else {
      // Update existing settings
      result = await db
        .update(storeSettings)
        .set({
          ...validatedData,
          updatedAt: new Date(),
        })
        .where(eq(storeSettings.storeId, storeId));
    }

    // Get updated settings
    const updatedSettings = await db
      .select()
      .from(storeSettings)
      .where(eq(storeSettings.storeId, storeId))
      .limit(1);

    return NextResponse.json({
      message: "Store settings updated successfully",
      settings: updatedSettings[0],
    });
  } catch (error) {
    console.error("Error updating store settings:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
