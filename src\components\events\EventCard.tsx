"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import { formatDate } from "@/lib/utils";
import { CalendarIcon, MapPinIcon, UserGroupIcon, GlobeAltIcon, LockClosedIcon, UsersIcon } from "@heroicons/react/24/outline";

interface EventCardProps {
  event: {
    id: string;
    name: string;
    description: string | null;
    startTime: string;
    endTime: string;
    location: string | null;
    isOnline: boolean | null;
    coverImage: string | null;
    visibility: string;
    category: string | null;
    attendeeCounts?: {
      going: number;
      interested: number;
      not_going: number;
    };
    host?: {
      id: string;
      name: string;
      username: string | null;
      image: string | null;
    };
  };
  onRsvp?: (eventId: string, status: "going" | "interested" | "not_going") => void;
  userAttendance?: "going" | "interested" | "not_going" | null;
  variant?: "compact" | "full";
}

export function EventCard({ event, onRsvp, userAttendance, variant = "compact" }: EventCardProps) {
  const [isRsvpLoading, setIsRsvpLoading] = useState(false);
  
  const startDate = new Date(event.startTime);
  const formattedDate = formatDate(startDate);
  const formattedTime = startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  
  const monthAbbr = startDate.toLocaleString('default', { month: 'short' }).toUpperCase();
  const day = startDate.getDate();
  
  const handleRsvp = async (status: "going" | "interested" | "not_going") => {
    if (onRsvp) {
      setIsRsvpLoading(true);
      try {
        await onRsvp(event.id, status);
      } finally {
        setIsRsvpLoading(false);
      }
    }
  };

  const getVisibilityIcon = () => {
    switch (event.visibility) {
      case "public":
        return <GlobeAltIcon className="h-4 w-4 text-gray-500" />;
      case "private":
        return <LockClosedIcon className="h-4 w-4 text-gray-500" />;
      case "friends":
        return <UsersIcon className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  if (variant === "compact") {
    return (
      <div className="flex items-start space-x-3 p-4 rounded-lg border border-gray-200 bg-white hover:bg-gray-50 transition-colors">
        <div className="h-14 w-14 flex-shrink-0 rounded-lg bg-blue-100 flex flex-col items-center justify-center">
          <span className="text-xs font-semibold text-blue-600">{monthAbbr}</span>
          <span className="text-sm font-bold text-blue-800">{day}</span>
        </div>
        <div className="flex-1">
          <Link href={`/events/${event.id}`} className="hover:underline">
            <p className="text-sm font-medium text-gray-900">{event.name}</p>
          </Link>
          <p className="text-xs text-gray-500 mt-1">
            {formattedDate} • {formattedTime}
          </p>
          <p className="text-xs text-gray-500 flex items-center mt-0.5">
            {event.location ? (
              <>
                <MapPinIcon className="h-3 w-3 mr-1" />
                {event.location}
              </>
            ) : event.isOnline ? (
              <>
                <GlobeAltIcon className="h-3 w-3 mr-1" />
                Online Event
              </>
            ) : null}
            {event.attendeeCounts && (
              <>
                <span className="mx-1">•</span>
                <UserGroupIcon className="h-3 w-3 mr-1" />
                {event.attendeeCounts.going + event.attendeeCounts.interested} interested
              </>
            )}
          </p>
          {onRsvp && (
            <div className="mt-2">
              <Button 
                size="sm" 
                variant={userAttendance === "going" ? "primary" : "outline"}
                disabled={isRsvpLoading}
                onClick={() => handleRsvp("going")}
              >
                {userAttendance === "going" ? "Going" : "Going"}
              </Button>
              <Button 
                size="sm" 
                variant={userAttendance === "interested" ? "primary" : "outline"}
                className="ml-2"
                disabled={isRsvpLoading}
                onClick={() => handleRsvp("interested")}
              >
                Interested
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
      {event.coverImage ? (
        <div className="relative h-48 w-full">
          <Image
            src={event.coverImage}
            alt={event.name}
            fill
            className="object-cover"
          />
        </div>
      ) : (
        <div className="h-48 w-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
          <CalendarIcon className="h-16 w-16 text-white" />
        </div>
      )}
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <div className="h-12 w-12 flex-shrink-0 rounded-lg bg-blue-100 flex flex-col items-center justify-center mr-3">
              <span className="text-xs font-semibold text-blue-600">{monthAbbr}</span>
              <span className="text-sm font-bold text-blue-800">{day}</span>
            </div>
            <div>
              <Link href={`/events/${event.id}`} className="hover:underline">
                <h3 className="text-lg font-semibold text-gray-900">{event.name}</h3>
              </Link>
              <p className="text-sm text-gray-500">
                {formattedDate} • {formattedTime}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            {getVisibilityIcon()}
            <span className="ml-1 text-xs text-gray-500 capitalize">{event.visibility}</span>
          </div>
        </div>
        
        {event.description && (
          <p className="mt-2 text-sm text-gray-600 line-clamp-3">{event.description}</p>
        )}
        
        <div className="mt-4 flex items-center text-sm text-gray-500">
          {event.location ? (
            <div className="flex items-center mr-4">
              <MapPinIcon className="h-4 w-4 mr-1" />
              <span>{event.location}</span>
            </div>
          ) : event.isOnline ? (
            <div className="flex items-center mr-4">
              <GlobeAltIcon className="h-4 w-4 mr-1" />
              <span>Online Event</span>
            </div>
          ) : null}
          
          {event.host && (
            <div className="flex items-center">
              <UserGroupIcon className="h-4 w-4 mr-1" />
              <span>Hosted by {event.host.name}</span>
            </div>
          )}
        </div>
        
        {event.attendeeCounts && (
          <div className="mt-2 text-sm text-gray-500">
            <span className="font-medium">{event.attendeeCounts.going}</span> going • 
            <span className="font-medium ml-1">{event.attendeeCounts.interested}</span> interested
          </div>
        )}
        
        {onRsvp && (
          <div className="mt-4 flex space-x-2">
            <Button 
              variant={userAttendance === "going" ? "primary" : "outline"}
              disabled={isRsvpLoading}
              onClick={() => handleRsvp("going")}
            >
              {userAttendance === "going" ? "Going" : "Going"}
            </Button>
            <Button 
              variant={userAttendance === "interested" ? "primary" : "outline"}
              disabled={isRsvpLoading}
              onClick={() => handleRsvp("interested")}
            >
              Interested
            </Button>
            {userAttendance && (
              <Button 
                variant="ghost"
                disabled={isRsvpLoading}
                onClick={() => handleRsvp("not_going")}
              >
                Not Going
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
