import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { savedPosts, fanPagePosts } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

type RouteParams = {
  params: Promise<{ postId: string }>;
};

// POST /api/fan-pages/posts/[postId]/bookmark - Toggle bookmark on fan page post
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the fan page post exists
    const post = await db.query.fanPagePosts.findFirst({
      where: eq(fanPagePosts.id, postId),
      columns: { id: true },
    });

    if (!post) {
      return NextResponse.json(
        { error: "Fan page post not found" },
        { status: 404 }
      );
    }

    // Check if user has already bookmarked this post
    const existingBookmark = await db.query.savedPosts.findFirst({
      where: and(
        eq(savedPosts.postId, postId),
        eq(savedPosts.userId, session.user.id)
      ),
    });

    if (existingBookmark) {
      // Remove bookmark
      await db.delete(savedPosts).where(eq(savedPosts.id, existingBookmark.id));
      
      return NextResponse.json({
        message: "Fan page post bookmark removed successfully",
        isBookmarked: false,
      });
    } else {
      // Add bookmark
      const bookmarkId = uuidv4();
      await db.insert(savedPosts).values({
        id: bookmarkId,
        userId: session.user.id,
        postId: postId,
      });

      return NextResponse.json({
        message: "Fan page post bookmarked successfully",
        isBookmarked: true,
      });
    }
  } catch (error) {
    console.error("Error toggling fan page post bookmark:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/fan-pages/posts/[postId]/bookmark - Remove bookmark from fan page post
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Find and remove the bookmark
    const existingBookmark = await db.query.savedPosts.findFirst({
      where: and(
        eq(savedPosts.postId, postId),
        eq(savedPosts.userId, session.user.id)
      ),
    });

    if (existingBookmark) {
      await db.delete(savedPosts).where(eq(savedPosts.id, existingBookmark.id));
      
      return NextResponse.json({
        message: "Fan page post bookmark removed successfully",
        isBookmarked: false,
      });
    } else {
      return NextResponse.json({
        message: "Bookmark not found",
        isBookmarked: false,
      });
    }
  } catch (error) {
    console.error("Error removing fan page post bookmark:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
