// Notification Sound Utility
class NotificationSoundManager {
  private audioContext: AudioContext | null = null;
  private isEnabled: boolean = true;

  constructor() {
    // Initialize audio context on user interaction
    if (typeof window !== 'undefined') {
      this.initializeAudioContext();
    }
  }

  private initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported:', error);
    }
  }

  // Create notification sound using Web Audio API
  private createNotificationSound(frequency: number = 800, duration: number = 200) {
    if (!this.audioContext || !this.isEnabled) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
    oscillator.type = 'sine';

    // Create a pleasant notification sound envelope
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration / 1000);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration / 1000);
  }

  // Different sounds for different notification types
  playNotificationSound(type: string = 'default') {
    if (!this.isEnabled) return;

    switch (type) {
      case 'like':
        this.createNotificationSound(600, 150);
        break;
      case 'comment':
        this.createNotificationSound(700, 200);
        break;
      case 'friend_request':
        this.createNotificationSound(800, 250);
        break;
      case 'message':
        // Double beep for messages
        this.createNotificationSound(900, 100);
        setTimeout(() => this.createNotificationSound(900, 100), 150);
        break;
      case 'important':
        // Triple beep for important notifications
        this.createNotificationSound(1000, 100);
        setTimeout(() => this.createNotificationSound(1200, 100), 150);
        setTimeout(() => this.createNotificationSound(1000, 100), 300);
        break;
      default:
        this.createNotificationSound(800, 200);
    }
  }

  // Enable/disable sounds
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    localStorage.setItem('notificationSoundsEnabled', enabled.toString());
  }

  // Get current enabled state
  isEnabledState(): boolean {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('notificationSoundsEnabled');
      return stored !== null ? stored === 'true' : true;
    }
    return this.isEnabled;
  }

  // Initialize from localStorage
  initializeFromStorage() {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('notificationSoundsEnabled');
      this.isEnabled = stored !== null ? stored === 'true' : true;
    }
  }

  // Play system notification sound (fallback)
  playSystemSound() {
    if (!this.isEnabled) return;

    // Try to play a system notification sound
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    audio.volume = 0.3;
    audio.play().catch(() => {
      // Fallback to Web Audio API sound
      this.createNotificationSound();
    });
  }
}

// Create singleton instance
export const notificationSound = new NotificationSoundManager();

// Initialize on module load
if (typeof window !== 'undefined') {
  notificationSound.initializeFromStorage();
}
