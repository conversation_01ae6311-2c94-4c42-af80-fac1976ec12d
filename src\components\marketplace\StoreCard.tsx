"use client";

import { useState } from "react";
import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { CheckBadgeIcon } from "@heroicons/react/24/solid";

interface StoreCardProps {
  store: {
    id: string;
    name: string;
    slug: string;
    logo: string | null;
    banner: string | null;
    isVerified: boolean | null;
    owner: {
      id: string;
      name: string | null;
    } | null;
  };
}

export function StoreCard({ store }: StoreCardProps) {
  const [bannerError, setBannerError] = useState(false);
  const [logoError, setLogoError] = useState(false);

  return (
    <Link
      href={`/store/${store.slug}`}
      className="group overflow-hidden rounded-lg bg-white shadow transition-all duration-300 hover:shadow-md"
    >
      <div className="relative h-32 overflow-hidden bg-gray-100">
        {store.banner && !bannerError ? (
          <OptimizedImage
            src={store.banner}
            alt={`${store.name} banner`}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 25vw"
            className="object-cover"
            onError={() => setBannerError(true)}
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-blue-50">
            <span className="text-blue-300">{store.name.charAt(0).toUpperCase()}</span>
          </div>
        )}
      </div>

      <div className="p-4">
        <div className="flex items-center mb-2">
          {/* Square logo */}
          <div className="h-12 w-12 overflow-hidden rounded-md border-2 border-white bg-white shadow mr-3">
            {store.logo && !logoError ? (
              <OptimizedImage
                src={store.logo}
                alt={store.name}
                width={48}
                height={48}
                className="h-full w-full object-cover"
                onError={() => setLogoError(true)}
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-blue-100">
                <span className="text-lg font-semibold text-blue-500">
                  {store.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>

          <div>
            <div className="flex items-center">
              <h3 className="mr-1 font-medium text-gray-900">{store.name}</h3>
              {store.isVerified === true && (
                <CheckBadgeIcon className="h-4 w-4 text-blue-500" />
              )}
            </div>

            <div className="text-xs text-gray-500">
              by {store.owner?.name || "Unknown"}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
