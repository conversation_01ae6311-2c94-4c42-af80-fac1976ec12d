"use client";

import React, { useState } from 'react';
import {
  XMarkIcon,
  ClockIcon,
  ArrowUturnLeftIcon,
  EyeIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

interface VersionHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  history: string[];
  currentIndex: number;
  onRestore: (index: number) => void;
}

interface VersionItem {
  content: string;
  timestamp: Date;
  wordCount: number;
  charCount: number;
}

export const VersionHistoryModal: React.FC<VersionHistoryModalProps> = ({
  isOpen,
  onClose,
  history,
  currentIndex,
  onRestore
}) => {
  const [selectedVersion, setSelectedVersion] = useState<number | null>(null);
  const [previewMode, setPreviewMode] = useState<'diff' | 'full'>('diff');

  // Create version items with metadata
  const versions: VersionItem[] = history.map((content, index) => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0);
    return {
      content,
      timestamp: new Date(Date.now() - (history.length - index - 1) * 60000), // Mock timestamps
      wordCount: words.length,
      charCount: content.length
    };
  }).reverse(); // Show newest first

  const handleRestore = (index: number) => {
    const actualIndex = history.length - 1 - index; // Convert back to original index
    onRestore(actualIndex);
    onClose();
  };

  const getDiff = (oldContent: string, newContent: string) => {
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    const maxLines = Math.max(oldLines.length, newLines.length);
    
    const diff = [];
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';
      
      if (oldLine !== newLine) {
        if (oldLine && newLine) {
          diff.push({ type: 'modified', old: oldLine, new: newLine, line: i + 1 });
        } else if (oldLine) {
          diff.push({ type: 'removed', content: oldLine, line: i + 1 });
        } else {
          diff.push({ type: 'added', content: newLine, line: i + 1 });
        }
      }
    }
    
    return diff;
  };

  const formatFileSize = (chars: number) => {
    if (chars < 1024) return `${chars} chars`;
    if (chars < 1024 * 1024) return `${(chars / 1024).toFixed(1)}K chars`;
    return `${(chars / (1024 * 1024)).toFixed(1)}M chars`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <ClockIcon className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Version History</h3>
            <span className="text-sm text-gray-500">({versions.length} versions)</span>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Version List */}
          <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h4 className="font-medium text-gray-700 mb-3">Versions</h4>
              <div className="space-y-2">
                {versions.map((version, index) => {
                  const isCurrentVersion = index === (versions.length - 1 - currentIndex);
                  const isSelected = selectedVersion === index;
                  
                  return (
                    <div
                      key={index}
                      onClick={() => setSelectedVersion(index)}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50'
                          : isCurrentVersion
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">
                          Version {versions.length - index}
                          {isCurrentVersion && (
                            <span className="ml-2 px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded">
                              Current
                            </span>
                          )}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatDistanceToNow(version.timestamp, { addSuffix: true })}
                        </span>
                      </div>
                      
                      <div className="text-xs text-gray-600 space-y-1">
                        <div className="flex items-center justify-between">
                          <span>{version.wordCount} words</span>
                          <span>{formatFileSize(version.charCount)}</span>
                        </div>
                        <div className="text-gray-500 truncate">
                          {version.content.substring(0, 50)}...
                        </div>
                      </div>
                      
                      {!isCurrentVersion && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRestore(index);
                          }}
                          className="mt-2 w-full flex items-center justify-center space-x-1 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                        >
                          <ArrowUturnLeftIcon className="h-3 w-3" />
                          <span>Restore</span>
                        </button>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Preview Panel */}
          <div className="flex-1 flex flex-col">
            {selectedVersion !== null && (
              <>
                {/* Preview Controls */}
                <div className="p-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-700">
                      Version {versions.length - selectedVersion} Preview
                    </h4>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setPreviewMode('diff')}
                        className={`px-3 py-1 text-sm rounded ${
                          previewMode === 'diff'
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        Show Changes
                      </button>
                      <button
                        onClick={() => setPreviewMode('full')}
                        className={`px-3 py-1 text-sm rounded ${
                          previewMode === 'full'
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        Full Content
                      </button>
                    </div>
                  </div>
                </div>

                {/* Preview Content */}
                <div className="flex-1 p-4 overflow-y-auto">
                  {previewMode === 'diff' && selectedVersion > 0 ? (
                    <div>
                      <h5 className="font-medium text-gray-700 mb-3">
                        Changes from Version {versions.length - selectedVersion + 1}
                      </h5>
                      <div className="space-y-2">
                        {getDiff(versions[selectedVersion - 1].content, versions[selectedVersion].content).map((change, index) => (
                          <div key={index} className="font-mono text-sm">
                            {change.type === 'added' && (
                              <div className="bg-green-100 border-l-4 border-green-500 p-2">
                                <span className="text-green-700">+ {change.content}</span>
                              </div>
                            )}
                            {change.type === 'removed' && (
                              <div className="bg-red-100 border-l-4 border-red-500 p-2">
                                <span className="text-red-700">- {change.content}</span>
                              </div>
                            )}
                            {change.type === 'modified' && (
                              <div className="bg-yellow-100 border-l-4 border-yellow-500 p-2">
                                <div className="text-red-700">- {change.old}</div>
                                <div className="text-green-700">+ {change.new}</div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div>
                      <h5 className="font-medium text-gray-700 mb-3">Full Content</h5>
                      <div className="bg-gray-50 border border-gray-200 rounded p-4">
                        <pre className="whitespace-pre-wrap font-mono text-sm text-gray-800">
                          {versions[selectedVersion].content}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}

            {selectedVersion === null && (
              <div className="flex-1 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Select a version to preview</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default VersionHistoryModal;
