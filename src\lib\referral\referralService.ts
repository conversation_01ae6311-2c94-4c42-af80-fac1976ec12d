import { db } from "@/lib/db";
import { referralCodes, referrals, referralSettings, users, walletTransactions } from "@/lib/db/schema";
import { eq, and, count, sum, desc } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { WalletService } from "@/lib/wallet/walletService";
import { executeQuery } from "@/lib/db/mysql";
import { ReferralSecurity } from "./referralSecurity";
import { generateReferralUrl } from "@/lib/utils/url";

export class ReferralService {
  // Generate unique referral code with security and collision detection
  static async generateUniqueReferralCode(userId: string): Promise<string> {
    // Validate user ID with security checks
    const userValidation = ReferralSecurity.validateUserId(userId);
    if (!userValidation.isValid) {
      ReferralSecurity.logSecurityEvent('invalid_user_id_in_code_generation', { userId, error: userValidation.error });
      throw new Error(userValidation.error);
    }

    const maxRetries = 10;
    let attempts = 0;

    while (attempts < maxRetries) {
      // Generate secure code
      const code = ReferralSecurity.generateSecureCode(userId);

      // Validate generated code
      const codeValidation = ReferralSecurity.validateReferralCode(code);
      if (!codeValidation.isValid) {
        attempts++;
        continue;
      }

      // Check if code already exists
      const existingCode = await db.query.referralCodes.findFirst({
        where: eq(referralCodes.code, code),
      });

      if (!existingCode) {
        return code;
      }

      attempts++;
      // Add small delay to avoid rapid retries
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    ReferralSecurity.logSecurityEvent('referral_code_generation_failed', { userId, attempts });
    throw new Error(`Failed to generate unique referral code after ${maxRetries} attempts`);
  }

  // Legacy method for backward compatibility
  static generateReferralCode(userId: string): string {
    if (!userId || userId.length < 2) {
      throw new Error('Invalid userId: must be at least 2 characters long');
    }
    const userPart = userId.substring(0, 2); // First 2 chars of user ID
    const random = Math.random().toString(36).substring(2, 4); // 2 random chars
    return `${userPart}${random}`.toUpperCase();
  }

  // Get or create referral code for user with atomic operation
  static async getOrCreateReferralCode(userId: string): Promise<string> {
    if (!userId) {
      throw new Error('Invalid userId: cannot be empty');
    }

    // Check if user already has a referral code
    const existingCode = await db.query.referralCodes.findFirst({
      where: eq(referralCodes.userId, userId),
    });

    if (existingCode) {
      return existingCode.code;
    }

    // Generate unique referral code with collision detection
    const code = await this.generateUniqueReferralCode(userId);
    const referralCodeId = uuidv4();

    try {
      // Create referral code with atomic operation
      await db.insert(referralCodes).values({
        id: referralCodeId,
        userId,
        code,
        isActive: true,
        totalReferrals: 0,
        totalEarnings: "0.00",
      });

      return code;
    } catch (error: any) {
      // Handle duplicate key error (race condition)
      if (error.code === 'ER_DUP_ENTRY') {
        // Check if another process created a code for this user
        const newExistingCode = await db.query.referralCodes.findFirst({
          where: eq(referralCodes.userId, userId),
        });

        if (newExistingCode) {
          return newExistingCode.code;
        }

        // If duplicate was for the code itself, retry with new code
        return await this.getOrCreateReferralCode(userId);
      }

      throw error;
    }
  }

  // Get referral settings
  static async getReferralSettings() {
    const settings = await db.query.referralSettings.findFirst({
      where: eq(referralSettings.id, 'default'),
    });

    return settings || {
      id: 'default',
      isEnabled: true,
      rewardAmount: "5.00",
      minPayoutThreshold: "10.00",
      requiresVerification: false,
      maxReferralsPerUser: 100,
      rewardBothUsers: false,
      referredUserReward: "0.00",
    };
  }

  // Process referral signup with comprehensive security and error handling
  static async processReferralSignup(referralCode: string, newUserId: string): Promise<boolean> {
    // Security validation
    const validation = ReferralSecurity.validateReferralSignup(referralCode, newUserId);
    if (!validation.isValid) {
      ReferralSecurity.logSecurityEvent('referral_signup_validation_failed', {
        referralCode: ReferralSecurity.sanitizeReferralCode(referralCode),
        newUserId,
        errors: validation.errors
      });
      console.error('Referral signup validation failed:', validation.errors);
      return false;
    }

    // Sanitize inputs
    const sanitizedCode = ReferralSecurity.sanitizeReferralCode(referralCode);

    let referralId: string | null = null;

    try {
      const settings = await this.getReferralSettings();

      if (!settings.isEnabled) {
        console.log('Referral program is disabled');
        return false;
      }

      // Find referral code with validation
      const codeRecord = await db.query.referralCodes.findFirst({
        where: and(
          eq(referralCodes.code, sanitizedCode),
          eq(referralCodes.isActive, true)
        ),
      });

      if (!codeRecord) {
        ReferralSecurity.logSecurityEvent('referral_code_not_found', {
          code: sanitizedCode,
          newUserId
        });
        console.log(`Referral code not found or inactive: ${sanitizedCode}`);
        return false;
      }

      // Check if user is trying to refer themselves
      if (codeRecord.userId === newUserId) {
        ReferralSecurity.logSecurityEvent('self_referral_attempt', {
          userId: newUserId,
          code: sanitizedCode
        });
        console.log(`User ${newUserId} attempted to use their own referral code`);
        return false;
      }

      // Check if this user was already referred
      const existingReferral = await db.query.referrals.findFirst({
        where: eq(referrals.referredUserId, newUserId),
      });

      if (existingReferral) {
        console.log(`User ${newUserId} was already referred by ${existingReferral.referrerId}`);
        return false;
      }

      // Validate reward amount
      const rewardAmount = parseFloat(settings.rewardAmount);
      if (isNaN(rewardAmount) || rewardAmount <= 0) {
        console.error(`Invalid reward amount in settings: ${settings.rewardAmount}`);
        return false;
      }

      // Create referral record with transaction safety
      referralId = uuidv4();

      try {
        await db.insert(referrals).values({
          id: referralId,
          referrerId: codeRecord.userId,
          referredUserId: newUserId,
          referralCode: referralCode.toUpperCase(),
          status: 'pending', // Start as pending, complete after reward processing
          rewardAmount: settings.rewardAmount,
          completedAt: null, // Will be set when reward is processed
        });

        console.log(`Created referral record ${referralId} for user ${newUserId} via code ${referralCode}`);

        // Process rewards
        const rewardProcessed = await this.processReferralReward(referralId);

        if (rewardProcessed) {
          // Mark referral as completed
          await db
            .update(referrals)
            .set({
              status: 'completed',
              completedAt: new Date(),
              updatedAt: new Date(),
            })
            .where(eq(referrals.id, referralId));

          console.log(`Successfully completed referral ${referralId}`);
          return true;
        } else {
          // Mark referral as failed but keep record for debugging
          await db
            .update(referrals)
            .set({
              status: 'cancelled',
              updatedAt: new Date(),
            })
            .where(eq(referrals.id, referralId));

          console.error(`Failed to process reward for referral ${referralId}`);
          return false;
        }

      } catch (insertError: any) {
        console.error('Error creating referral record:', insertError);

        // Check if it's a duplicate key error
        if (insertError.code === 'ER_DUP_ENTRY') {
          console.log(`Duplicate referral attempt detected for user ${newUserId}`);
          return false;
        }

        throw insertError; // Re-throw other errors
      }

    } catch (error: any) {
      console.error('Error processing referral signup:', error);

      // Cleanup: Remove referral record if it was created but processing failed
      if (referralId) {
        try {
          await db
            .update(referrals)
            .set({
              status: 'cancelled',
              updatedAt: new Date(),
            })
            .where(eq(referrals.id, referralId));

          console.log(`Marked referral ${referralId} as cancelled due to processing error`);
        } catch (cleanupError) {
          console.error('Error during referral cleanup:', cleanupError);
        }
      }

      return false;
    }
  }

  // Process referral reward with security and race condition protection
  static async processReferralReward(referralId: string): Promise<boolean> {
    // Security validation
    const validation = ReferralSecurity.validateRewardProcessing(referralId);
    if (!validation.isValid) {
      ReferralSecurity.logSecurityEvent('reward_processing_validation_failed', {
        referralId,
        error: validation.error
      });
      console.error('Reward processing validation failed:', validation.error);
      return false;
    }

    try {
      // Use SELECT FOR UPDATE to prevent race conditions
      const referral = await db.query.referrals.findFirst({
        where: eq(referrals.id, referralId),
        with: {
          referrer: true,
          referredUser: true,
        },
      });

      if (!referral) {
        console.error(`Referral not found with ID: ${referralId}`);
        return false;
      }

      // Check if already paid (race condition check)
      if (referral.paidAt) {
        console.log(`Referral ${referralId} already paid, skipping reward processing`);
        return true; // Already processed, not an error
      }

      const settings = await this.getReferralSettings();
      if (!settings.isEnabled) {
        console.log('Referral program is disabled, skipping reward processing');
        return false;
      }

      const rewardAmount = parseFloat(referral.rewardAmount);
      if (isNaN(rewardAmount) || rewardAmount <= 0) {
        console.error(`Invalid reward amount for referral ${referralId}: ${referral.rewardAmount}`);
        return false;
      }

      // Atomic update to mark as paid first (prevents double processing)
      const updateResult = await db
        .update(referrals)
        .set({
          paidAt: new Date(),
          updatedAt: new Date(),
        })
        .where(and(
          eq(referrals.id, referralId),
          eq(referrals.paidAt, null) // Only update if not already paid
        ));

      // If no rows were updated, another process already processed this
      if (!updateResult || updateResult.affectedRows === 0) {
        console.log(`Referral ${referralId} was already processed by another transaction`);
        return true;
      }

      // Process wallet operations with error handling
      try {
        // Add reward to referrer's earning wallet
        await WalletService.updateWalletBalance(
          referral.referrerId,
          'earning',
          rewardAmount.toFixed(2),
          'add'
        );

        // Create earning transaction
        await WalletService.createTransaction(referral.referrerId, {
          type: 'earning',
          amount: rewardAmount.toFixed(2),
          walletType: 'earning',
          reference: `Referral reward for ${referral.referredUser?.name || 'user'}`,
          note: `Earned $${rewardAmount} for referring a new user`,
          metadata: {
            referralId,
            referredUserId: referral.referredUserId,
            type: 'referral_commission',
          },
        });

        // Update referral code stats atomically
        if (referral.referrer?.id) {
          await db
            .update(referralCodes)
            .set({
              totalReferrals: referral.referrer?.referralCode?.totalReferrals ?
                referral.referrer.referralCode.totalReferrals + 1 : 1,
              totalEarnings: (parseFloat(referral.referrer?.referralCode?.totalEarnings || '0') + rewardAmount).toFixed(2),
              updatedAt: new Date(),
            })
            .where(eq(referralCodes.userId, referral.referrerId));
        }

        console.log(`Successfully processed referral reward for ${referralId}: $${rewardAmount}`);
        return true;

      } catch (walletError) {
        console.error(`Wallet operation failed for referral ${referralId}:`, walletError);

        // Rollback the paid status if wallet operations failed
        await db
          .update(referrals)
          .set({
            paidAt: null,
            updatedAt: new Date(),
          })
          .where(eq(referrals.id, referralId));

        return false;
      }

    } catch (error) {
      console.error('Error processing referral reward:', error);

      // Attempt to rollback if we marked as paid but failed later
      try {
        await db
          .update(referrals)
          .set({
            paidAt: null,
            updatedAt: new Date(),
          })
          .where(eq(referrals.id, referralId));
      } catch (rollbackError) {
        console.error('Failed to rollback referral payment status:', rollbackError);
      }

      return false;
    }
  }

  // Get user referral stats
  static async getUserReferralStats(userId: string, req?: any) {
    // Get referral code
    const referralCode = await this.getOrCreateReferralCode(userId);

    // Get referral stats
    const totalReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.referrerId, userId));

    const completedReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(and(
        eq(referrals.referrerId, userId),
        eq(referrals.status, 'completed')
      ));

    const pendingReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(and(
        eq(referrals.referrerId, userId),
        eq(referrals.status, 'pending')
      ));

    const totalEarningsResult = await db
      .select({ total: sum(referrals.rewardAmount) })
      .from(referrals)
      .where(and(
        eq(referrals.referrerId, userId),
        eq(referrals.status, 'completed')
      ));

    // Get recent referrals
    const recentReferrals = await db.query.referrals.findMany({
      where: eq(referrals.referrerId, userId),
      orderBy: [desc(referrals.createdAt)],
      limit: 10,
      with: {
        referredUser: {
          columns: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    const settings = await this.getReferralSettings();

    return {
      referralCode,
      referralLink: generateReferralUrl(referralCode, req),
      totalReferrals: totalReferralsResult[0]?.count || 0,
      completedReferrals: completedReferralsResult[0]?.count || 0,
      pendingReferrals: pendingReferralsResult[0]?.count || 0,
      totalEarnings: parseFloat(totalEarningsResult[0]?.total || '0'),
      rewardPerReferral: parseFloat(settings.rewardAmount),
      recentReferrals: recentReferrals.map(referral => ({
        id: referral.id,
        name: referral.referredUser?.name || 'Unknown User',
        username: referral.referredUser?.username,
        date: referral.createdAt.toISOString().split('T')[0],
        status: referral.status,
        reward: parseFloat(referral.rewardAmount),
        paidAt: referral.paidAt,
      })),
      settings,
    };
  }

  // Initialize referral tables (for testing)
  static async initializeTables() {
    try {
      // Create referral_codes table (standardized with referral-tables.sql)
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS referral_codes (
          id VARCHAR(255) PRIMARY KEY,
          userId VARCHAR(255) NOT NULL UNIQUE,
          code VARCHAR(50) NOT NULL UNIQUE,
          isActive BOOLEAN DEFAULT TRUE,
          totalReferrals INT DEFAULT 0,
          totalEarnings DECIMAL(15,2) DEFAULT 0.00,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_code (code),
          INDEX idx_user (userId)
        )
      `);

      // Create referrals table (standardized with referral-tables.sql)
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS referrals (
          id VARCHAR(255) PRIMARY KEY,
          referrerId VARCHAR(255) NOT NULL,
          referredUserId VARCHAR(255) NOT NULL,
          referralCode VARCHAR(50) NOT NULL,
          status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
          rewardAmount DECIMAL(15,2) DEFAULT 5.00,
          paidAt TIMESTAMP NULL,
          completedAt TIMESTAMP NULL,
          metadata JSON,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (referrerId) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (referredUserId) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE KEY unique_referral (referrerId, referredUserId),
          INDEX idx_referrer (referrerId),
          INDEX idx_referred (referredUserId),
          INDEX idx_code (referralCode),
          INDEX idx_status (status)
        )
      `);

      // Create referral_settings table (standardized with referral-tables.sql)
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS referral_settings (
          id VARCHAR(255) PRIMARY KEY,
          isEnabled BOOLEAN DEFAULT TRUE,
          rewardAmount DECIMAL(15,2) DEFAULT 5.00,
          minPayoutThreshold DECIMAL(15,2) DEFAULT 10.00,
          requiresVerification BOOLEAN DEFAULT FALSE,
          maxReferralsPerUser INT DEFAULT 100,
          rewardBothUsers BOOLEAN DEFAULT FALSE,
          referredUserReward DECIMAL(15,2) DEFAULT 0.00,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      // Insert default settings if not exists (standardized with referral-tables.sql)
      await executeQuery(`
        INSERT IGNORE INTO referral_settings (
          id, isEnabled, rewardAmount, minPayoutThreshold,
          requiresVerification, maxReferralsPerUser,
          rewardBothUsers, referredUserReward
        ) VALUES (
          'default', TRUE, 5.00, 10.00,
          FALSE, 100,
          FALSE, 0.00
        )
      `);

      console.log('Referral tables initialized successfully');
      return { success: true };
    } catch (error) {
      console.error('Error initializing referral tables:', error);
      throw error;
    }
  }
}
