"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { useDropzone } from "react-dropzone";
import { PhotoIcon, XMarkIcon, CheckCircleIcon } from "@heroicons/react/24/outline";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";

const storeSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(255, "Name must be less than 255 characters"),
  slug: z
    .string()
    .min(3, "Slug must be at least 3 characters")
    .max(100, "Slug must be less than 100 characters")
    .regex(/^[a-zA-Z0-9-]+$/, "Slug can only contain letters, numbers, and hyphens"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  location: z.string().max(255, "Location must be less than 255 characters").optional(),
  phone: z.string().max(50, "Phone number must be less than 50 characters").optional(),
  email: z.string().email("Please enter a valid email address").max(255, "Email must be less than 255 characters").optional(),
  website: z.string().max(255, "Website must be less than 255 characters").optional(),
});

type StoreFormData = z.infer<typeof storeSchema>;

interface StoreEditFormProps {
  store: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    banner: string | null;
    location: string | null;
    phone: string | null;
    email: string | null;
    website: string | null;
  };
}

export function StoreEditForm({ store }: StoreEditFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Logo handling
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(store.logo);

  // Banner handling
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [bannerPreview, setBannerPreview] = useState<string | null>(store.banner);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<StoreFormData>({
    resolver: zodResolver(storeSchema),
    defaultValues: {
      name: store.name,
      slug: store.slug,
      description: store.description || "",
      location: store.location || "",
      phone: store.phone || "",
      email: store.email || "",
      website: store.website || "",
    },
  });

  // Logo dropzone
  const { getRootProps: getLogoRootProps, getInputProps: getLogoInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setLogoFile(file);
        setLogoPreview(URL.createObjectURL(file));
      }
    },
  });

  // Banner dropzone
  const { getRootProps: getBannerRootProps, getInputProps: getBannerInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setBannerFile(file);
        setBannerPreview(URL.createObjectURL(file));
      }
    },
  });

  const removeLogo = () => {
    setLogoFile(null);
    setLogoPreview(null);
  };

  const removeBanner = () => {
    setBannerFile(null);
    setBannerPreview(null);
  };

  const onSubmit = async (data: StoreFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      // Upload images to Cloudinary if provided
      let logoUrl = logoPreview;
      let bannerUrl = bannerPreview;

      if (logoFile) {
        try {
          const logoUrls = await uploadMultipleToCloudinary([logoFile]);
          if (logoUrls.length > 0) {
            logoUrl = logoUrls[0];
          }
        } catch (uploadError) {
          console.error("Error uploading logo:", uploadError);
          setError("Error uploading logo. Please try again.");
          setIsSubmitting(false);
          return;
        }
      }

      if (bannerFile) {
        try {
          const bannerUrls = await uploadMultipleToCloudinary([bannerFile]);
          if (bannerUrls.length > 0) {
            bannerUrl = bannerUrls[0];
          }
        } catch (uploadError) {
          console.error("Error uploading banner:", uploadError);
          setError("Error uploading banner. Please try again.");
          setIsSubmitting(false);
          return;
        }
      }

      // Update store
      const response = await fetch(`/api/marketplace/stores/${store.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          logo: logoUrl,
          banner: bannerUrl,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.message || "Failed to update store");
        setIsSubmitting(false);
        return;
      }

      // Show success message
      setSuccess("Store updated successfully!");

      // Refresh the page to show updated store data
      router.refresh();

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push("/my-store/dashboard");
      }, 1500);
    } catch (err) {
      console.error("Error updating store:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      {success && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
            <div className="text-sm text-green-700">{success}</div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div className="sm:col-span-2">
          <Input
            label="Store Name *"
            placeholder="Enter store name"
            error={errors.name?.message}
            {...register("name")}
          />
        </div>

        <div className="sm:col-span-2">
          <Input
            label="Store Slug *"
            placeholder="your-store-name (used in URL: /store/your-store-name)"
            error={errors.slug?.message}
            {...register("slug")}
          />
          <p className="mt-1 text-xs text-gray-500">
            This will be used in your store URL. Use only letters, numbers, and hyphens.
          </p>
        </div>

        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Store Description
          </label>
          <textarea
            className="w-full rounded-md border border-gray-300 px-3 py-2 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
            placeholder="Describe your store"
            {...register("description")}
          ></textarea>
          {errors.description && (
            <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
          )}
        </div>

        <div className="sm:col-span-2">
          <Input
            label="Location"
            placeholder="City, State, Country"
            error={errors.location?.message}
            {...register("location")}
          />
        </div>

        {/* Contact Information Section */}
        <div className="sm:col-span-2">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
        </div>

        <div className="sm:col-span-1">
          <Input
            label="Phone Number"
            placeholder="+****************"
            error={errors.phone?.message}
            {...register("phone")}
          />
        </div>

        <div className="sm:col-span-1">
          <Input
            label="Email Address"
            type="email"
            placeholder="<EMAIL>"
            error={errors.email?.message}
            {...register("email")}
          />
        </div>

        <div className="sm:col-span-2">
          <Input
            label="Website"
            placeholder="www.yourstore.com"
            error={errors.website?.message}
            {...register("website")}
          />
          <p className="mt-1 text-xs text-gray-500">
            Enter your website URL without http:// or https://
          </p>
        </div>

        {/* Logo Upload */}
        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Store Logo
          </label>

          {logoPreview ? (
            <div className="relative mb-4 h-32 w-32 overflow-hidden rounded-full border border-gray-200">
              <img
                src={logoPreview}
                alt="Store logo"
                className="h-full w-full object-cover"
              />
              <button
                type="button"
                onClick={removeLogo}
                className="absolute right-1 top-1 rounded-full bg-white p-1 shadow hover:bg-gray-100"
              >
                <XMarkIcon className="h-4 w-4 text-gray-600" />
              </button>
            </div>
          ) : (
            <div
              {...getLogoRootProps()}
              className="mb-4 flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-full border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
            >
              <input {...getLogoInputProps()} />
              <PhotoIcon className="h-8 w-8 text-gray-400" />
              <p className="mt-1 text-xs text-gray-500">Upload logo</p>
            </div>
          )}
          <p className="text-xs text-gray-500">
            Recommended: Square image, at least 200x200 pixels
          </p>
        </div>

        {/* Banner Upload */}
        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Store Banner
          </label>

          {bannerPreview ? (
            <div className="relative mb-4 h-48 w-full overflow-hidden rounded-lg border border-gray-200">
              <img
                src={bannerPreview}
                alt="Store banner"
                className="h-full w-full object-cover"
              />
              <button
                type="button"
                onClick={removeBanner}
                className="absolute right-2 top-2 rounded-full bg-white p-1 shadow hover:bg-gray-100"
              >
                <XMarkIcon className="h-4 w-4 text-gray-600" />
              </button>
            </div>
          ) : (
            <div
              {...getBannerRootProps()}
              className="mb-4 flex h-48 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
            >
              <input {...getBannerInputProps()} />
              <PhotoIcon className="h-8 w-8 text-gray-400" />
              <p className="mt-1 text-sm text-gray-500">Upload banner</p>
            </div>
          )}
          <p className="text-xs text-gray-500">
            Recommended: 1200x300 pixels, 4:1 aspect ratio
          </p>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push("/my-store/dashboard")}
        >
          Cancel
        </Button>
        <Button type="submit" isLoading={isSubmitting}>
          Update Store
        </Button>
      </div>
    </form>
  );
}
