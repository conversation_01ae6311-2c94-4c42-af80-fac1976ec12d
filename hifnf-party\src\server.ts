import type * as Party from "partykit/server";

// Types for different message types
interface BaseMessage {
  type: string;
  timestamp: string;
  userId?: string;
}

interface ChatMessage extends BaseMessage {
  type: "chat_message";
  messageId: string;
  senderId: string;
  receiverId: string;
  content: string;
  conversationType: "direct" | "fanpage";
  fanPageId?: string;
}

interface NotificationMessage extends BaseMessage {
  type: "notification";
  notificationId: string;
  recipientId: string;
  notificationType: "like" | "comment" | "message" | "follow" | "fan_page_message" | "fan_page_reply";
  senderId: string;
  data: any;
}

interface TypingMessage extends BaseMessage {
  type: "typing";
  conversationId: string;
  isTyping: boolean;
}

interface PresenceMessage extends BaseMessage {
  type: "presence";
  status: "online" | "offline" | "away";
}

interface AuthMessage extends BaseMessage {
  type: "auth";
  token: string;
}

interface PingMessage extends BaseMessage {
  type: "ping";
}

type WebSocketMessage = ChatMessage | NotificationMessage | TypingMessage | PresenceMessage | AuthMessage | PingMessage;

// Connection metadata
interface ConnectionMeta {
  userId?: string;
  authenticated: boolean;
  lastSeen: number;
  rooms: Set<string>;
}

export default class Server implements Party.Server {
  private connections = new Map<string, ConnectionMeta>();
  private userConnections = new Map<string, Set<string>>(); // userId -> Set of connection IDs

  constructor(readonly room: Party.Room) {}

  onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    console.log(`New connection: ${conn.id} to room: ${this.room.id}`);

    // Initialize connection metadata
    this.connections.set(conn.id, {
      authenticated: false,
      lastSeen: Date.now(),
      rooms: new Set([this.room.id])
    });

    // Send connection acknowledgment
    conn.send(JSON.stringify({
      type: "connection_ack",
      connectionId: conn.id,
      roomId: this.room.id,
      timestamp: new Date().toISOString()
    }));
  }

  onMessage(message: string, sender: Party.Connection) {
    try {
      const data: WebSocketMessage = JSON.parse(message);
      const connMeta = this.connections.get(sender.id);

      if (!connMeta) {
        sender.send(JSON.stringify({ type: "error", message: "Connection not found" }));
        return;
      }

      // Update last seen
      connMeta.lastSeen = Date.now();

      switch (data.type) {
        case "auth":
          this.handleAuth(sender, data as AuthMessage);
          break;

        case "chat_message":
          if (connMeta.authenticated) {
            this.handleChatMessage(sender, data as ChatMessage);
          } else {
            sender.send(JSON.stringify({ type: "error", message: "Authentication required" }));
          }
          break;

        case "notification":
          if (connMeta.authenticated) {
            this.handleNotification(sender, data as NotificationMessage);
          } else {
            sender.send(JSON.stringify({ type: "error", message: "Authentication required" }));
          }
          break;

        case "typing":
          if (connMeta.authenticated) {
            this.handleTyping(sender, data as TypingMessage);
          }
          break;

        case "presence":
          if (connMeta.authenticated) {
            this.handlePresence(sender, data as PresenceMessage);
          }
          break;

        case "ping":
          // Respond to ping with pong
          sender.send(JSON.stringify({
            type: "pong",
            timestamp: new Date().toISOString()
          }));
          break;

        default:
          sender.send(JSON.stringify({ type: "error", message: "Unknown message type" }));
      }
    } catch (error) {
      console.error("Error parsing message:", error);
      sender.send(JSON.stringify({ type: "error", message: "Invalid message format" }));
    }
  }

  onClose(conn: Party.Connection) {
    const connMeta = this.connections.get(conn.id);
    if (connMeta?.userId) {
      // Remove from user connections
      const userConns = this.userConnections.get(connMeta.userId);
      if (userConns) {
        userConns.delete(conn.id);
        if (userConns.size === 0) {
          this.userConnections.delete(connMeta.userId);
          // Broadcast user offline status
          this.broadcastPresence(connMeta.userId, "offline");
        }
      }
    }

    this.connections.delete(conn.id);
    console.log(`Connection closed: ${conn.id}`);
  }

  private async handleAuth(conn: Party.Connection, message: AuthMessage) {
    try {
      // Verify JWT token with your auth system
      const userId = await this.verifyToken(message.token);

      if (userId) {
        const connMeta = this.connections.get(conn.id)!;
        connMeta.authenticated = true;
        connMeta.userId = userId;

        // Track user connections
        if (!this.userConnections.has(userId)) {
          this.userConnections.set(userId, new Set());
        }
        this.userConnections.get(userId)!.add(conn.id);

        conn.send(JSON.stringify({
          type: "auth_success",
          userId,
          timestamp: new Date().toISOString()
        }));

        // Broadcast user online status
        this.broadcastPresence(userId, "online");
      } else {
        conn.send(JSON.stringify({
          type: "auth_error",
          message: "Invalid token"
        }));
      }
    } catch (error) {
      console.error("Auth error:", error);
      conn.send(JSON.stringify({
        type: "auth_error",
        message: "Authentication failed"
      }));
    }
  }

  private handleChatMessage(conn: Party.Connection, message: ChatMessage) {
    const connMeta = this.connections.get(conn.id)!;

    // Broadcast to receiver
    if (message.conversationType === "direct") {
      this.sendToUser(message.receiverId, {
        type: "new_message",
        ...message,
        timestamp: new Date().toISOString()
      });
    } else if (message.conversationType === "fanpage" && message.fanPageId) {
      // Broadcast to fan page room
      this.room.broadcast(JSON.stringify({
        type: "new_fanpage_message",
        ...message,
        timestamp: new Date().toISOString()
      }), [conn.id]);
    }

    // Send delivery confirmation to sender
    conn.send(JSON.stringify({
      type: "message_delivered",
      messageId: message.messageId,
      timestamp: new Date().toISOString()
    }));
  }

  private handleNotification(conn: Party.Connection, message: NotificationMessage) {
    // Send notification to recipient
    this.sendToUser(message.recipientId, {
      type: "new_notification",
      ...message,
      timestamp: new Date().toISOString()
    });
  }

  private handleTyping(conn: Party.Connection, message: TypingMessage) {
    const connMeta = this.connections.get(conn.id)!;

    // Broadcast typing status to conversation participants
    this.room.broadcast(JSON.stringify({
      type: "user_typing",
      userId: connMeta.userId,
      conversationId: message.conversationId,
      isTyping: message.isTyping,
      timestamp: new Date().toISOString()
    }), [conn.id]);
  }

  private handlePresence(conn: Party.Connection, message: PresenceMessage) {
    const connMeta = this.connections.get(conn.id)!;

    if (connMeta.userId) {
      this.broadcastPresence(connMeta.userId, message.status);
    }
  }

  private sendToUser(userId: string, message: any) {
    const userConns = this.userConnections.get(userId);
    if (userConns) {
      userConns.forEach(connId => {
        const conn = [...this.room.getConnections()].find(c => c.id === connId);
        if (conn) {
          conn.send(JSON.stringify(message));
        }
      });
    }
  }

  private broadcastPresence(userId: string, status: string) {
    this.room.broadcast(JSON.stringify({
      type: "user_presence",
      userId,
      status,
      timestamp: new Date().toISOString()
    }));
  }

  private async verifyToken(token: string): Promise<string | null> {
    try {
      // This should integrate with your NextAuth JWT verification
      // For now, we'll implement a basic verification
      // In production, you'd want to verify the JWT signature

      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());

      // Check expiration
      if (payload.exp && payload.exp < Date.now() / 1000) {
        return null;
      }

      return payload.sub || payload.id || null;
    } catch (error) {
      console.error("Token verification error:", error);
      return null;
    }
  }
}

Server satisfies Party.Worker;
