#!/usr/bin/env node

/**
 * Memory Leak Fix Script
 * Automatically fixes common memory leak patterns in the codebase
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Starting Memory Leak Fix Script...');

// Common memory leak patterns to fix
const fixes = [
  {
    name: 'Uncleaned setInterval',
    pattern: /setInterval\s*\(\s*([^,]+),\s*([^)]+)\)/g,
    replacement: (match, callback, delay) => {
      return `safeSetInterval(${callback}, ${delay}, 'Auto-generated interval')`;
    },
    imports: "import { safeSetInterval, safeClearInterval } from '@/lib/utils/memoryLeakDetector';"
  },
  {
    name: 'Uncleaned addEventListener',
    pattern: /(\w+)\.addEventListener\s*\(\s*['"]([^'"]+)['"]\s*,\s*([^,)]+)(?:\s*,\s*([^)]+))?\)/g,
    replacement: (match, target, event, listener, options) => {
      const opts = options ? `, ${options}` : '';
      return `safeAddEventListener(${target}, '${event}', ${listener}${opts})`;
    },
    imports: "import { safeAddEventListener, safeRemoveEventListener } from '@/lib/utils/memoryLeakDetector';"
  }
];

// Files to scan (excluding node_modules, .git, etc.)
const scanDirectories = ['src', 'scripts'];
const excludePatterns = [
  /node_modules/,
  /\.git/,
  /\.next/,
  /dist/,
  /build/,
  /coverage/,
  /\.d\.ts$/,
  /memoryLeakDetector\.ts$/
];

function shouldScanFile(filePath) {
  return (
    (filePath.endsWith('.ts') || filePath.endsWith('.tsx') || filePath.endsWith('.js') || filePath.endsWith('.jsx')) &&
    !excludePatterns.some(pattern => pattern.test(filePath))
  );
}

function scanDirectory(dirPath, files = []) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !excludePatterns.some(pattern => pattern.test(fullPath))) {
      scanDirectory(fullPath, files);
    } else if (stat.isFile() && shouldScanFile(fullPath)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let importsToAdd = new Set();
  
  for (const fix of fixes) {
    const matches = content.match(fix.pattern);
    if (matches) {
      console.log(`  🔍 Found ${matches.length} instances of "${fix.name}" in ${filePath}`);
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
      if (fix.imports) {
        importsToAdd.add(fix.imports);
      }
    }
  }
  
  // Add necessary imports
  if (importsToAdd.size > 0) {
    const imports = Array.from(importsToAdd).join('\n');
    
    // Find existing imports or add at the top
    const importRegex = /^import\s+.*?from\s+['"][^'"]+['"];?\s*$/gm;
    const existingImports = content.match(importRegex);
    
    if (existingImports) {
      // Add after last import
      const lastImport = existingImports[existingImports.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport) + lastImport.length;
      content = content.slice(0, lastImportIndex) + '\n' + imports + content.slice(lastImportIndex);
    } else {
      // Add at the top
      content = imports + '\n\n' + content;
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ Fixed ${filePath}`);
    return true;
  }
  
  return false;
}

function main() {
  let totalFiles = 0;
  let fixedFiles = 0;
  
  for (const dir of scanDirectories) {
    if (fs.existsSync(dir)) {
      console.log(`\n📁 Scanning directory: ${dir}`);
      const files = scanDirectory(dir);
      
      for (const file of files) {
        totalFiles++;
        if (fixFile(file)) {
          fixedFiles++;
        }
      }
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`  Total files scanned: ${totalFiles}`);
  console.log(`  Files fixed: ${fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log(`\n⚠️  Important: Please review the changes and test your application!`);
    console.log(`   Some manual adjustments might be needed.`);
  } else {
    console.log(`\n✅ No memory leaks found or all already fixed!`);
  }
}

// Additional manual fixes
function createManualFixGuide() {
  const guide = `
# Manual Memory Leak Fixes Required

## 1. Check these files for potential issues:

### server.js
- ✅ Fixed: setInterval cleanup added

### src/lib/monitoring/messagePerformance.ts  
- ✅ Fixed: Global setInterval cleanup added

### src/app/blogs/page.tsx
- ✅ Fixed: Cache size limit added

## 2. Additional checks needed:

### Large Arrays/Objects in State
- Check components with large useState arrays
- Implement pagination for large datasets
- Add cleanup in useEffect

### Database Connections
- Ensure all DB connections are properly closed
- Check connection pool settings

### File Uploads
- Clear file references after upload
- Implement file size limits

### WebSocket Connections
- Add proper cleanup for socket connections
- Remove event listeners on disconnect

## 3. Monitoring

Add this to your main layout to monitor memory:

\`\`\`tsx
import { memoryLeakDetector } from '@/lib/utils/memoryLeakDetector';

// In development, log memory stats every 30 seconds
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    console.log('Memory Stats:', memoryLeakDetector.getSummary());
  }, 30000);
}
\`\`\`

## 4. Testing

Run your app with these flags to detect memory issues:

\`\`\`bash
node --expose-gc --max-old-space-size=4096 server.js
\`\`\`
`;

  fs.writeFileSync('MEMORY_LEAK_FIXES.md', guide);
  console.log('\n📝 Created MEMORY_LEAK_FIXES.md with manual fix guide');
}

if (require.main === module) {
  main();
  createManualFixGuide();
}

module.exports = { main, fixFile, scanDirectory };
