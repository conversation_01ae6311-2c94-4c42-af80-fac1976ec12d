"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Select } from "@/components/ui/Select";
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  EyeIcon,
  HeartIcon,
  StarIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
} from "@heroicons/react/24/outline";

// Mock data for demonstration purposes
const generateMockData = (days: number) => {
  const data = [];
  const today = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    data.push({
      date: date.toISOString().split('T')[0],
      views: Math.floor(Math.random() * 100) + 10,
      followers: Math.floor(Math.random() * 5),
      reviews: Math.floor(Math.random() * 2),
      products: i === 0 ? Math.floor(Math.random() * 2) : 0,
      sales: Math.floor(Math.random() * 3),
      revenue: (Math.floor(Math.random() * 300) + 50) * 100, // in cents
    });
  }
  
  return data;
};

interface StoreAnalyticsProps {
  storeId: string;
}

export function StoreAnalytics({ storeId }: StoreAnalyticsProps) {
  const [timeRange, setTimeRange] = useState("30");
  const [analyticsData, setAnalyticsData] = useState(() => generateMockData(30));

  const handleTimeRangeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const days = parseInt(e.target.value);
    setTimeRange(e.target.value);
    setAnalyticsData(generateMockData(days));
  };

  // Calculate totals
  const totals = analyticsData.reduce(
    (acc, day) => {
      acc.views += day.views;
      acc.followers += day.followers;
      acc.reviews += day.reviews;
      acc.products += day.products;
      acc.sales += day.sales;
      acc.revenue += day.revenue;
      return acc;
    },
    { views: 0, followers: 0, reviews: 0, products: 0, sales: 0, revenue: 0 }
  );

  // Calculate trends (comparing first half to second half)
  const halfIndex = Math.floor(analyticsData.length / 2);
  const firstHalf = analyticsData.slice(0, halfIndex);
  const secondHalf = analyticsData.slice(halfIndex);

  const firstHalfTotals = firstHalf.reduce(
    (acc, day) => {
      acc.views += day.views;
      acc.followers += day.followers;
      acc.reviews += day.reviews;
      acc.sales += day.sales;
      return acc;
    },
    { views: 0, followers: 0, reviews: 0, sales: 0 }
  );

  const secondHalfTotals = secondHalf.reduce(
    (acc, day) => {
      acc.views += day.views;
      acc.followers += day.followers;
      acc.reviews += day.reviews;
      acc.sales += day.sales;
      return acc;
    },
    { views: 0, followers: 0, reviews: 0, sales: 0 }
  );

  const trends = {
    views: ((secondHalfTotals.views - firstHalfTotals.views) / (firstHalfTotals.views || 1)) * 100,
    followers: ((secondHalfTotals.followers - firstHalfTotals.followers) / (firstHalfTotals.followers || 1)) * 100,
    reviews: ((secondHalfTotals.reviews - firstHalfTotals.reviews) / (firstHalfTotals.reviews || 1)) * 100,
    sales: ((secondHalfTotals.sales - firstHalfTotals.sales) / (firstHalfTotals.sales || 1)) * 100,
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(cents / 100);
  };

  const renderTrendIndicator = (percentage: number) => {
    const isPositive = percentage >= 0;
    return (
      <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        <ArrowTrendingUpIcon 
          className={`h-4 w-4 ${isPositive ? 'rotate-0' : 'rotate-180'}`} 
        />
        <span className="ml-1 text-xs font-medium">
          {Math.abs(percentage).toFixed(1)}%
        </span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Store Analytics</h2>
        <div className="flex items-center space-x-2">
          <Select
            value={timeRange}
            onChange={handleTimeRangeChange}
            className="w-40"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </Select>
          <Button variant="outline" size="sm">
            Export
          </Button>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Product Views</p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold text-gray-900">{totals.views}</h3>
                <div className="ml-2">{renderTrendIndicator(trends.views)}</div>
              </div>
            </div>
            <div className="rounded-full bg-blue-100 p-3">
              <EyeIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            {totals.views / analyticsData.length} views per day on average
          </p>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">New Followers</p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold text-gray-900">{totals.followers}</h3>
                <div className="ml-2">{renderTrendIndicator(trends.followers)}</div>
              </div>
            </div>
            <div className="rounded-full bg-red-100 p-3">
              <HeartIcon className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            {(totals.followers / analyticsData.length).toFixed(1)} new followers per day
          </p>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">New Reviews</p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold text-gray-900">{totals.reviews}</h3>
                <div className="ml-2">{renderTrendIndicator(trends.reviews)}</div>
              </div>
            </div>
            <div className="rounded-full bg-yellow-100 p-3">
              <StarIcon className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            {(totals.reviews / analyticsData.length).toFixed(1)} new reviews per day
          </p>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">New Products</p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold text-gray-900">{totals.products}</h3>
              </div>
            </div>
            <div className="rounded-full bg-indigo-100 p-3">
              <ShoppingBagIcon className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            {(totals.products / analyticsData.length).toFixed(1)} new products per day
          </p>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Sales</p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold text-gray-900">{totals.sales}</h3>
                <div className="ml-2">{renderTrendIndicator(trends.sales)}</div>
              </div>
            </div>
            <div className="rounded-full bg-green-100 p-3">
              <ShoppingBagIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            {(totals.sales / analyticsData.length).toFixed(1)} sales per day
          </p>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Revenue</p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold text-gray-900">{formatCurrency(totals.revenue)}</h3>
              </div>
            </div>
            <div className="rounded-full bg-green-100 p-3">
              <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            {formatCurrency(totals.revenue / analyticsData.length)} per day
          </p>
        </div>
      </div>

      {/* Chart Placeholder */}
      <div className="rounded-lg border border-gray-200 bg-white p-6">
        <h3 className="mb-4 text-lg font-medium text-gray-900">Performance Over Time</h3>
        <div className="h-64 w-full">
          <div className="flex h-full flex-col items-center justify-center">
            <ChartBarIcon className="h-16 w-16 text-gray-300" />
            <p className="mt-2 text-sm text-gray-500">
              Charts will be displayed here when Chart.js is installed.
            </p>
            <p className="text-xs text-gray-400">
              Run: npm install chart.js react-chartjs-2
            </p>
          </div>
        </div>
      </div>

      {/* Daily Data Table */}
      <div className="rounded-lg border border-gray-200 bg-white">
        <div className="border-b border-gray-200 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Daily Analytics</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Views
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Followers
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Reviews
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Products
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Sales
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Revenue
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {analyticsData.slice().reverse().map((day, index) => (
                <tr key={day.date} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {new Date(day.date).toLocaleDateString()}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {day.views}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {day.followers}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {day.reviews}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {day.products}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {day.sales}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {formatCurrency(day.revenue)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
