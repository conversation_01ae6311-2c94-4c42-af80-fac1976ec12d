import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { SidebarErrorBoundary } from "@/components/error/ErrorBoundary";
import { SavedPostsList } from "@/components/saved/SavedPostsList";

export default async function SavedPage() {
  await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row">
          {/* Left sidebar - 20% */}
          <div className="w-full lg:w-[20%] mb-5 lg:mb-0">
            <SidebarErrorBoundary>
              <LeftSidebar />
            </SidebarErrorBoundary>
            {/* This is an empty div that takes up the same space as the fixed sidebar */}
            <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
          </div>

          {/* Gap between left sidebar and main content - 5% */}
          <div className="hidden lg:block lg:w-[5%]"></div>

          {/* Main content - 70% */}
          <div className="w-full lg:w-[70%] space-y-6">
            {/* Enhanced Header */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg p-6 text-white">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                    </svg>
                  </div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Saved Items</h1>
                  <p className="text-blue-100 mt-1">Your bookmarked posts and content</p>
                </div>
              </div>
            </div>

            <SavedPostsList />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
