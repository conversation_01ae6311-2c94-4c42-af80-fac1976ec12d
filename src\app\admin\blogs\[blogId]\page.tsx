"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Input } from "@/components/ui/Input";
import { Select } from "@/components/ui/Select";
import { Textarea } from "@/components/ui/Textarea";
import RichTextEditor from "@/components/blog/RichTextEditor";
import { toast } from "react-hot-toast";
import {
  ArrowLeftIcon,
  EyeIcon,
  TrashIcon,
  StarIcon,
  CalendarIcon,
  UserIcon,
  ChatBubbleLeftIcon,
  HeartIcon,
} from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import Link from "next/link";
import Image from "next/image";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  content: string;
  coverImage?: string | null;
  status: "draft" | "published" | "archived";
  featured: boolean;
  readTime?: number | null;
  viewCount?: number;
  seoTitle?: string | null;
  seoDescription?: string | null;
  publishedAt?: string | null;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    username?: string | null;
    image?: string | null;
    email?: string | null;
  };
  category?: {
    id: string;
    name: string;
    color: string;
  } | null;
  tags?: string[] | null;
  _count: {
    likes: number;
    comments: number;
  };
  _admin: {
    totalViews: number;
    createdAt: string;
    updatedAt: string;
    publishedAt?: string | null;
  };
  likes: Array<{
    id: string;
    user: {
      id: string;
      name: string;
      image?: string | null;
    };
  }>;
  comments: Array<{
    id: string;
    content: string;
    createdAt: string;
    user: {
      id: string;
      name: string;
      image?: string | null;
    };
  }>;
}

interface BlogCategory {
  id: string;
  name: string;
  color: string;
}

export default function AdminBlogDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [blog, setBlog] = useState<Blog | null>(null);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [isEditing, setIsEditing] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    title: "",
    excerpt: "",
    content: "",
    coverImage: "",
    categoryId: "",
    tags: [] as string[],
    status: "draft" as "draft" | "published" | "archived",
    featured: false,
    readTime: 0,
    seoTitle: "",
    seoDescription: "",
  });

  const fetchBlog = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/blogs/${params?.blogId}`);
      if (!response.ok) throw new Error("Failed to fetch blog");

      const data = await response.json();
      setBlog(data);

      // Set form data
      setFormData({
        title: data.title || "",
        excerpt: data.excerpt || "",
        content: data.content || "",
        coverImage: data.coverImage || "",
        categoryId: data.category?.id || "",
        tags: data.tags || [],
        status: data.status,
        featured: data.featured,
        readTime: data.readTime || 0,
        seoTitle: data.seoTitle || "",
        seoDescription: data.seoDescription || "",
      });
    } catch (error) {
      console.error("Error fetching blog:", error);
      toast.error("Failed to fetch blog");
      router.push("/admin/blogs");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/blogs/categories");
      if (!response.ok) throw new Error("Failed to fetch categories");
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  useEffect(() => {
    if (params?.blogId) {
      fetchBlog();
      fetchCategories();
    }
  }, [params?.blogId]);

  const handleSave = async () => {
    try {
      setIsSaving(true);
      const response = await fetch(`/api/admin/blogs/${params?.blogId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error("Failed to update blog");

      const result = await response.json();
      toast.success("Blog updated successfully");
      setIsEditing(false);
      fetchBlog(); // Refresh data
    } catch (error) {
      console.error("Error updating blog:", error);
      toast.error("Failed to update blog");
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this blog? This action cannot be undone.")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/blogs/${params?.blogId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete blog");

      toast.success("Blog deleted successfully");
      router.push("/admin/blogs");
    } catch (error) {
      console.error("Error deleting blog:", error);
      toast.error("Failed to delete blog");
    }
  };

  const toggleFeatured = async () => {
    try {
      const response = await fetch(`/api/admin/blogs/${params?.blogId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ featured: !blog?.featured }),
      });

      if (!response.ok) throw new Error("Failed to update blog");

      toast.success(`Blog ${!blog?.featured ? "featured" : "unfeatured"} successfully`);
      fetchBlog();
    } catch (error) {
      console.error("Error updating blog:", error);
      toast.error("Failed to update blog");
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      published: "success",
      draft: "warning",
      archived: "default",
    };
    return (
      <Badge variant={variants[status as keyof typeof variants] as any}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!blog) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="mt-2 text-sm font-medium text-gray-900">Blog not found</h3>
          <p className="mt-1 text-sm text-gray-500">
            The blog you're looking for doesn't exist.
          </p>
          <div className="mt-6">
            <Button onClick={() => router.push("/admin/blogs")}>
              Back to Blogs
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="outline"
            className="mr-4"
            onClick={() => router.push("/admin/blogs")}
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Blogs
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{blog.title}</h1>
            <div className="flex items-center space-x-4 mt-1">
              {getStatusBadge(blog.status)}
              {blog.featured && (
                <Badge variant="warning">
                  <StarIconSolid className="mr-1 h-3 w-3" />
                  Featured
                </Badge>
              )}
              {blog.category && (
                <Badge
                  variant="default"
                  style={{ backgroundColor: blog.category.color + "20", color: blog.category.color }}
                >
                  {blog.category.name}
                </Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={toggleFeatured}
            className="text-gray-400 hover:text-yellow-500"
            title={blog.featured ? "Remove from featured" : "Add to featured"}
          >
            {blog.featured ? (
              <StarIconSolid className="h-6 w-6" />
            ) : (
              <StarIcon className="h-6 w-6" />
            )}
          </button>
          <Link
            href={`/blogs/${blog.slug}`}
            target="_blank"
            className="inline-flex items-center"
          >
            <Button variant="outline">
              <EyeIcon className="mr-2 h-5 w-5" />
              View Live
            </Button>
          </Link>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              Edit Blog
            </Button>
          ) : (
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          )}
          <Button variant="danger" onClick={handleDelete}>
            <TrashIcon className="mr-2 h-5 w-5" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Blog Content */}
          <div className="bg-white rounded-lg shadow p-6">
            {isEditing ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title
                  </label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Blog title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Excerpt
                  </label>
                  <Textarea
                    value={formData.excerpt}
                    onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                    placeholder="Brief description of the blog"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cover Image URL
                  </label>
                  <Input
                    value={formData.coverImage}
                    onChange={(e) => setFormData({ ...formData, coverImage: e.target.value })}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Content
                  </label>
                  <RichTextEditor
                    value={formData.content}
                    onChange={(value) => setFormData({ ...formData, content: value })}
                    placeholder="Blog content"
                    rows={15}
                    className="focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            ) : (
              <div>
                {blog.coverImage && (
                  <div className="mb-6">
                    <Image
                      src={blog.coverImage}
                      alt={blog.title}
                      width={800}
                      height={400}
                      className="w-full h-64 object-cover rounded-lg"
                    />
                  </div>
                )}

                {blog.excerpt && (
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Excerpt</h3>
                    <p className="text-gray-600">{blog.excerpt}</p>
                  </div>
                )}

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Content</h3>
                  <div className="prose max-w-none">
                    <div dangerouslySetInnerHTML={{ __html: blog.content.replace(/\n/g, '<br>') }} />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* SEO Settings (when editing) */}
          {isEditing && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">SEO Settings</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SEO Title
                  </label>
                  <Input
                    value={formData.seoTitle}
                    onChange={(e) => setFormData({ ...formData, seoTitle: e.target.value })}
                    placeholder="SEO optimized title"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SEO Description
                  </label>
                  <Textarea
                    value={formData.seoDescription}
                    onChange={(e) => setFormData({ ...formData, seoDescription: e.target.value })}
                    placeholder="SEO meta description"
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Blog Settings */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Blog Settings</h3>
            {isEditing ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <Select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                    <option value="archived">Archived</option>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <Select
                    value={formData.categoryId}
                    onChange={(e) => setFormData({ ...formData, categoryId: e.target.value })}
                  >
                    <option value="">No Category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Read Time (minutes)
                  </label>
                  <Input
                    type="number"
                    value={formData.readTime}
                    onChange={(e) => setFormData({ ...formData, readTime: parseInt(e.target.value) || 0 })}
                    min="0"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={formData.featured}
                    onChange={(e) => setFormData({ ...formData, featured: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="featured" className="ml-2 text-sm text-gray-700">
                    Featured Blog
                  </label>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Status:</span>
                  {getStatusBadge(blog.status)}
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Category:</span>
                  <span className="text-sm text-gray-900">
                    {blog.category?.name || "No Category"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Read Time:</span>
                  <span className="text-sm text-gray-900">{blog.readTime || 0} min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Featured:</span>
                  <span className="text-sm text-gray-900">{blog.featured ? "Yes" : "No"}</span>
                </div>
              </div>
            )}
          </div>

          {/* Author Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Author</h3>
            <div className="flex items-center space-x-3">
              {blog.author.image && (
                <Image
                  src={blog.author.image}
                  alt={blog.author.name}
                  width={40}
                  height={40}
                  className="h-10 w-10 rounded-full"
                />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900">{blog.author.name}</p>
                <p className="text-sm text-gray-500">{blog.author.email}</p>
              </div>
            </div>
          </div>

          {/* Statistics */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <EyeIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-500">Views</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {blog._admin.totalViews}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <HeartIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-500">Likes</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {blog._count.likes}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <ChatBubbleLeftIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-500">Comments</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {blog._count.comments}
                </span>
              </div>
            </div>
          </div>

          {/* Dates */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Dates</h3>
            <div className="space-y-3">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <CalendarIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-500">Created</span>
                </div>
                <p className="text-sm text-gray-900">{formatDate(blog.createdAt)}</p>
              </div>
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <CalendarIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-500">Last Updated</span>
                </div>
                <p className="text-sm text-gray-900">{formatDate(blog.updatedAt)}</p>
              </div>
              {blog.publishedAt && (
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <CalendarIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-500">Published</span>
                  </div>
                  <p className="text-sm text-gray-900">{formatDate(blog.publishedAt)}</p>
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          {blog.tags && blog.tags.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {blog.tags.map((tag, index) => (
                  <Badge key={index} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Recent Comments */}
      {blog.comments.length > 0 && (
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Comments</h3>
          <div className="space-y-4">
            {blog.comments.slice(0, 5).map((comment) => (
              <div key={comment.id} className="flex space-x-3">
                {comment.user.image && (
                  <Image
                    src={comment.user.image}
                    alt={comment.user.name}
                    width={32}
                    height={32}
                    className="h-8 w-8 rounded-full"
                  />
                )}
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {comment.user.name}
                    </span>
                    <span className="text-sm text-gray-500">
                      {formatDate(comment.createdAt)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">{comment.content}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
