import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { siteSettings } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

// Get all site settings
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Try to get settings using raw SQL to avoid schema mismatch issues
    let settings: any[] = [];

    try {
      // Use raw SQL without prepared statements
      const [rows] = await db.execute(
        `SELECT * FROM site_settings ORDER BY group_name ASC, setting_key ASC`
      );

      if (Array.isArray(rows) && rows.length > 0) {
        settings = rows.map((row: any) => ({
          id: row.id,
          key: row.setting_key,
          value: row.value,
          type: row.type,
          groupName: row.group_name,
          label: row.label,
          description: row.description,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
        }));
      }
    } catch (error) {
      console.error('Error fetching settings with raw SQL:', error);
      // Fallback to schema-based query if raw SQL fails
      settings = await db.query.siteSettings.findMany({
        orderBy: (siteSettings, { asc }) => [asc(siteSettings.groupName), asc(siteSettings.key)],
      });
    }

    // If no settings found, return an empty object
    if (settings.length === 0) {
      console.log('No settings found in the database');
    }

    // Group settings by group_name
    const groupedSettings = settings.reduce((acc, setting) => {
      const group = setting.groupName;
      if (!acc[group]) {
        acc[group] = {};
      }
      // Use the key directly as the property name
      const keyName = setting.key;
      acc[group][keyName] = {
        value: setting.value,
        type: setting.type,
        label: setting.label,
        description: setting.description,
      };
      return acc;
    }, {} as Record<string, any>);

    return NextResponse.json(groupedSettings, { status: 200 });
  } catch (error) {
    console.error("Error fetching site settings:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update site settings
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    console.log("Received settings update request:", JSON.stringify(body, null, 2));

    // Validate the request body
    if (!body || typeof body !== "object") {
      return NextResponse.json(
        { message: "Invalid request body" },
        { status: 400 }
      );
    }

    // Flatten the nested settings structure
    const flattenedSettings: Array<{ key: string; value: any; group: string }> = [];

    Object.entries(body).forEach(([group, groupSettings]) => {
      if (groupSettings && typeof groupSettings === 'object') {
        Object.entries(groupSettings as Record<string, any>).forEach(([key, value]) => {
          flattenedSettings.push({ key, value, group });
        });
      }
    });

    console.log("Flattened settings:", flattenedSettings);

    // Process each setting update
    const updateResults = [];

    for (const { key, value, group } of flattenedSettings) {
      try {
        // Find the existing setting
        let existingSetting = null;

        try {
          // Try using Drizzle ORM first
          existingSetting = await db.query.siteSettings.findFirst({
            where: eq(siteSettings.key, key),
          });
        } catch (ormError) {
          console.log(`ORM query failed for key ${key}, trying raw SQL:`, ormError);

          // Fallback to raw SQL
          const escapedKey = key.replace(/'/g, "''");
          const [rows] = await db.execute(
            `SELECT * FROM site_settings WHERE setting_key = '${escapedKey}' LIMIT 1`
          );

          if (Array.isArray(rows) && rows.length > 0) {
            const row = rows[0] as any;
            existingSetting = {
              id: row.id,
              key: row.setting_key,
              value: row.value,
              type: row.type,
              groupName: row.group_name,
              label: row.label,
              description: row.description,
            };
          }
        }

        if (existingSetting) {
          // Convert value to string based on its type
          let stringValue: string;

          if (typeof value === 'boolean') {
            stringValue = value ? 'true' : 'false';
          } else if (value === null || value === undefined) {
            stringValue = '';
          } else {
            stringValue = String(value);
          }

          console.log(`Updating setting ${key}: ${existingSetting.value} -> ${stringValue}`);

          try {
            // Try using Drizzle ORM first
            await db.update(siteSettings)
              .set({ value: stringValue })
              .where(eq(siteSettings.key, key));

            updateResults.push({ key, status: 'success', method: 'orm' });
          } catch (ormError) {
            console.log(`ORM update failed for key ${key}, trying raw SQL:`, ormError);

            // Fallback to raw SQL
            const escapedValue = stringValue.replace(/'/g, "''");
            const escapedKey = key.replace(/'/g, "''");

            await db.execute(
              `UPDATE site_settings SET value = '${escapedValue}', updated_at = NOW() WHERE setting_key = '${escapedKey}'`
            );

            updateResults.push({ key, status: 'success', method: 'raw_sql' });
          }
        } else {
          console.log(`Setting not found: ${key}`);
          updateResults.push({ key, status: 'not_found' });
        }
      } catch (settingError) {
        console.error(`Error updating setting ${key}:`, settingError);
        updateResults.push({ key, status: 'error', error: settingError.message });
      }
    }

    console.log("Update results:", updateResults);

    // Check if any updates failed
    const failedUpdates = updateResults.filter(result => result.status === 'error');
    const notFoundSettings = updateResults.filter(result => result.status === 'not_found');

    if (failedUpdates.length > 0) {
      console.error("Some settings failed to update:", failedUpdates);
      return NextResponse.json(
        {
          message: "Some settings failed to update",
          details: {
            failed: failedUpdates,
            notFound: notFoundSettings,
            successful: updateResults.filter(result => result.status === 'success')
          }
        },
        { status: 207 } // Multi-status
      );
    }

    return NextResponse.json(
      {
        message: "Settings updated successfully",
        details: {
          updated: updateResults.filter(result => result.status === 'success').length,
          notFound: notFoundSettings.length,
          total: updateResults.length
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating site settings:", error);

    // Provide more detailed error information
    let errorMessage = "Internal server error";
    let errorDetails = null;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        name: error.name,
        stack: error.stack,
      };

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string; errno?: number };

        errorDetails = {
          ...errorDetails,
          code: dbError.code,
          errno: dbError.errno,
          sqlMessage: dbError.sqlMessage,
        };

        if (dbError.code === 'ER_NO_SUCH_TABLE') {
          errorMessage = "Database table 'site_settings' does not exist. Please run database migrations.";
        } else if (dbError.code === 'ER_BAD_FIELD_ERROR') {
          errorMessage = "Database schema mismatch. Please check your database structure.";
        } else if (dbError.code === 'ECONNREFUSED') {
          errorMessage = "Cannot connect to database. Please check your database configuration.";
        }
      }
    }

    console.error("Detailed error information:", errorDetails);

    return NextResponse.json(
      {
        message: errorMessage,
        error: process.env.NODE_ENV === 'development' ? errorDetails : undefined
      },
      { status: 500 }
    );
  }
}
