import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPageMessages, fanPages, users, notifications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

const replyMessageSchema = z.object({
  content: z.string().min(1).max(5000),
  recipientId: z.string().min(1), // User ID to reply to
});

// POST - Send a reply from fan page to user
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;
    const body = await request.json();
    const { content, recipientId } = replyMessageSchema.parse(body);

    // Verify fan page exists and user is the owner
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, pageId),
        eq(fanPages.ownerId, session.user.id),
        eq(fanPages.isActive, true)
      ),
    });

    if (!page) {
      return NextResponse.json({ error: "Fan page not found or access denied" }, { status: 404 });
    }

    // Verify recipient exists
    const recipient = await db.query.users.findFirst({
      where: eq(users.id, recipientId),
    });

    if (!recipient) {
      return NextResponse.json({ error: "Recipient not found" }, { status: 404 });
    }

    // Create reply message
    const messageId = uuidv4();
    await db.insert(fanPageMessages).values({
      id: messageId,
      fanPageId: pageId,
      senderId: session.user.id,
      content,
      isFromPage: true, // This is a reply from the page
    });

    // Create notification for the user
    await db.insert(notifications).values({
      id: uuidv4(),
      recipientId: recipientId,
      type: "fan_page_reply",
      senderId: session.user.id,
      fanPageId: pageId,
    });

    // Fetch the created message with sender info
    const createdMessage = await db
      .select({
        id: fanPageMessages.id,
        fanPageId: fanPageMessages.fanPageId,
        senderId: fanPageMessages.senderId,
        content: fanPageMessages.content,
        isFromPage: fanPageMessages.isFromPage,
        read: fanPageMessages.read,
        createdAt: fanPageMessages.createdAt,
        sender: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPageMessages)
      .leftJoin(users, eq(fanPageMessages.senderId, users.id))
      .where(eq(fanPageMessages.id, messageId))
      .limit(1);

    return NextResponse.json({
      success: true,
      message: "Reply sent successfully",
      data: createdMessage[0],
    });

  } catch (error) {
    console.error("Error sending fan page reply:", error);
    return NextResponse.json(
      { error: "Failed to send reply" },
      { status: 500 }
    );
  }
}
