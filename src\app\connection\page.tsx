import { MainLayout } from "@/components/layout/MainLayout";
import { getCurrentUser } from "@/lib/utils/auth";
import { ConnectionContent } from "@/components/connection/ConnectionContent";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { redirect } from "next/navigation";

export const dynamic = 'force-dynamic';

export default async function ConnectionPage() {
  const user = await getCurrentUser();

  if (!user) {
    redirect('/auth/signin');
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-2 py-4 sm:px-4 sm:py-6 lg:px-8">
          <div className="flex flex-col lg:flex-row">
            {/* Left sidebar - 20% */}
            <div className="w-full lg:w-[20%] mb-4 lg:mb-0">
              <LeftSidebar />
              {/* This is an empty div that takes up the same space as the fixed sidebar */}
              <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
            </div>

            {/* Gap between left sidebar and main content - 5% */}
            <div className="hidden lg:block lg:w-[5%]"></div>

            {/* Main content - 75% */}
            <div className="w-full lg:w-[75%]">
              <ConnectionContent />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
