/**
 * Image utility functions for handling aspect ratios and display optimization
 */

export type ImageAspectRatio = 'square' | 'landscape' | 'portrait';

export interface ImageDimensions {
  width: number;
  height: number;
  aspectRatio: number;
  type: ImageAspectRatio;
}

/**
 * Detects the aspect ratio type of an image
 * @param width Image width
 * @param height Image height
 * @returns The aspect ratio type
 */
export function getImageAspectRatioType(width: number, height: number): ImageAspectRatio {
  const ratio = width / height;
  
  // Consider images within 10% of square as square
  if (ratio >= 0.9 && ratio <= 1.1) {
    return 'square';
  }
  
  return ratio > 1 ? 'landscape' : 'portrait';
}

/**
 * Gets image dimensions and aspect ratio information
 * @param width Image width
 * @param height Image height
 * @returns Complete dimension information
 */
export function getImageDimensions(width: number, height: number): ImageDimensions {
  const aspectRatio = width / height;
  const type = getImageAspectRatioType(width, height);
  
  return {
    width,
    height,
    aspectRatio,
    type
  };
}

// Cache for image dimensions to avoid repeated calculations
const dimensionsCache = new Map<string, ImageDimensions>();

/**
 * Loads an image and returns its dimensions with caching
 * @param src Image source URL
 * @returns Promise with image dimensions
 */
export function loadImageDimensions(src: string): Promise<ImageDimensions> {
  // Check cache first
  if (dimensionsCache.has(src)) {
    return Promise.resolve(dimensionsCache.get(src)!);
  }

  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      const dimensions = getImageDimensions(img.naturalWidth, img.naturalHeight);

      // Cache the result
      dimensionsCache.set(src, dimensions);

      resolve(dimensions);

      // Clean up
      img.onload = null;
      img.onerror = null;
    };

    img.onerror = () => {
      reject(new Error(`Failed to load image: ${src}`));

      // Clean up
      img.onload = null;
      img.onerror = null;
    };

    // Set crossOrigin to handle CORS issues
    img.crossOrigin = 'anonymous';
    img.src = src;
  });
}

/**
 * Determines if an image should be cropped based on its dimensions
 * @param dimensions Image dimensions
 * @param maxHeight Maximum allowed height in pixels
 * @returns Whether the image should be cropped
 */
export function shouldCropImage(dimensions: ImageDimensions, maxHeight: number = 1020): boolean {
  return dimensions.type === 'portrait' && dimensions.height > maxHeight;
}

/**
 * Calculates the optimal display height for an image
 * @param dimensions Image dimensions
 * @param containerWidth Container width in pixels
 * @param maxHeight Maximum allowed height in pixels
 * @returns Optimal display height
 */
export function getOptimalDisplayHeight(
  dimensions: ImageDimensions, 
  containerWidth: number, 
  maxHeight: number = 1020
): number {
  const naturalHeight = containerWidth / dimensions.aspectRatio;
  
  if (dimensions.type === 'portrait' && naturalHeight > maxHeight) {
    return maxHeight;
  }
  
  return naturalHeight;
}

/**
 * Gets CSS classes for image display based on aspect ratio
 * @param dimensions Image dimensions
 * @returns CSS class string
 */
export function getImageDisplayClasses(dimensions: ImageDimensions): string {
  const baseClasses = "w-full";

  switch (dimensions.type) {
    case 'square':
      // Square images: maintain aspect ratio, fit perfectly
      return `${baseClasses} object-contain bg-gray-50`;
    case 'landscape':
      // Wide images: maintain aspect ratio, fit perfectly
      return `${baseClasses} object-contain bg-gray-50`;
    case 'portrait':
      // Portrait images: crop at 1020px height
      return `${baseClasses} object-cover max-h-[1020px]`;
    default:
      return `${baseClasses} object-contain bg-gray-50`;
  }
}

/**
 * Determines if a "see more" indicator should be shown
 * @param dimensions Image dimensions
 * @param maxHeight Maximum allowed height
 * @returns Whether to show the indicator
 */
export function shouldShowSeeMoreIndicator(
  dimensions: ImageDimensions,
  maxHeight: number = 1020
): boolean {
  return shouldCropImage(dimensions, maxHeight);
}

/**
 * Clears the image dimensions cache
 * Useful for memory management in long-running applications
 */
export function clearImageDimensionsCache(): void {
  dimensionsCache.clear();
}

/**
 * Gets the current cache size
 * @returns Number of cached image dimensions
 */
export function getImageDimensionsCacheSize(): number {
  return dimensionsCache.size;
}
