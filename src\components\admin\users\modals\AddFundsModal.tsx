"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";
import { XMarkIcon, PlusIcon } from "@heroicons/react/24/outline";

interface AddFundsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
  onSuccess: () => void;
}

export function AddFundsModal({ 
  isOpen, 
  onClose, 
  userId, 
  userName,
  onSuccess 
}: AddFundsModalProps) {
  const [formData, setFormData] = useState({
    amount: "",
    walletType: "general" as "general" | "earning",
    reason: "",
    note: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.amount || !formData.reason) {
      toast.error("Please fill in all required fields");
      return;
    }

    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    if (amount > 100000) {
      toast.error("Maximum amount is ৳100,000");
      return;
    }

    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/admin/users/${userId}/wallet/add-funds`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount,
          walletType: formData.walletType,
          reason: formData.reason,
          note: formData.note || undefined,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to add funds");
      }

      toast.success(`৳${amount.toLocaleString()} added to ${formData.walletType} wallet successfully`);
      onSuccess();
      onClose();
      
      // Reset form
      setFormData({
        amount: "",
        walletType: "general",
        reason: "",
        note: "",
      });
    } catch (error: any) {
      console.error("Error adding funds:", error);
      toast.error(error.message || "Failed to add funds");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-full">
              <PlusIcon className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Add Funds
              </h2>
              <p className="text-sm text-gray-600">
                Add funds to {userName}'s wallet
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Amount <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                ৳
              </span>
              <input
                type="number"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                placeholder="0.00"
                min="1"
                max="100000"
                step="0.01"
                className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Minimum: ৳1, Maximum: ৳100,000
            </p>
          </div>

          {/* Wallet Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Wallet Type <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.walletType}
              onChange={(e) => handleInputChange('walletType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="general">General Wallet</option>
              <option value="earning">Earning Wallet</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              {formData.walletType === 'general' 
                ? 'For deposits and purchases' 
                : 'For earnings and rewards'
              }
            </p>
          </div>

          {/* Reason */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reason <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              placeholder="e.g., Refund for order #12345"
              maxLength={500}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              This will be visible to the user
            </p>
          </div>

          {/* Admin Note */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Admin Note (Optional)
            </label>
            <textarea
              value={formData.note}
              onChange={(e) => handleInputChange('note', e.target.value)}
              placeholder="Internal note for admin reference..."
              rows={3}
              maxLength={1000}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Internal note (not visible to user)
            </p>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Adding...
                </>
              ) : (
                <>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Funds
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
