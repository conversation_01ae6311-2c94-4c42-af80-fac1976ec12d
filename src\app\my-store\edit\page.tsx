import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { stores } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { redirect } from "next/navigation";
import { StoreEditForm } from "@/components/marketplace/StoreEditForm";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { ChevronLeftIcon } from "@heroicons/react/24/outline";

export default async function EditStorePage() {
  const user = await requireAuth();

  // Check if user has a store
  const userStore = await db
    .select({
      id: stores.id,
      name: stores.name,
      slug: stores.slug,
      description: stores.description,
      logo: stores.logo,
      banner: stores.banner,
      location: stores.location,
      phone: stores.phone,
      email: stores.email,
      website: stores.website,
    })
    .from(stores)
    .where(eq(stores.ownerId, user.id))
    .limit(1);

  // If user doesn't have a store, redirect to create store page
  if (userStore.length === 0) {
    redirect("/marketplace/create-store");
  }

  const store = userStore[0];

  return (
    <MainLayout>
      <div className="mx-auto max-w-3xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link href="/my-store/dashboard">
            <Button variant="outline" size="sm">
              <ChevronLeftIcon className="h-4 w-4 mr-1" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
            Edit Store
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Update your store details and appearance
          </p>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-6">
            <StoreEditForm store={store} />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
