import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCommissions, users } from "@/lib/db/schema";
import { eq, desc, sql, inArray } from "drizzle-orm";

// Get user's commission history
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user's commissions with referred user information
    const commissionsResult = await db
      .select({
        id: referralCommissions.id,
        planName: referralCommissions.planName,
        subscriptionAmount: referralCommissions.subscriptionAmount,
        commissionType: referralCommissions.commissionType,
        commissionRate: referralCommissions.commissionRate,
        commissionAmount: referralCommissions.commissionAmount,
        isFirstPurchase: referralCommissions.isFirstPurchase,
        status: referralCommissions.status,
        createdAt: referralCommissions.createdAt,
        approvedAt: referralCommissions.approvedAt,
        paidAt: referralCommissions.paidAt,
        referredUserId: referralCommissions.referredUserId,
      })
      .from(referralCommissions)
      .where(eq(referralCommissions.referrerId, session.user.id))
      .orderBy(desc(referralCommissions.createdAt));

    // Get referred user information
    const referredUserIds = commissionsResult
      .map(c => c.referredUserId)
      .filter(id => id && id.trim() !== ''); // Filter out null/empty IDs

    const referredUsers = referredUserIds.length > 0
      ? await db
          .select({
            id: users.id,
            name: users.name,
            email: users.email,
          })
          .from(users)
          .where(inArray(users.id, referredUserIds))
      : [];

    // Create a map for referred users
    const referredUsersMap = new Map(
      referredUsers.map(user => [user.id, user])
    );

    // Combine data
    const commissions = commissionsResult.map(commission => {
      const referredUser = referredUsersMap.get(commission.referredUserId);
      
      return {
        id: commission.id,
        planName: commission.planName,
        subscriptionAmount: parseFloat(commission.subscriptionAmount),
        commissionType: commission.commissionType,
        commissionRate: parseFloat(commission.commissionRate),
        commissionAmount: parseFloat(commission.commissionAmount),
        isFirstPurchase: commission.isFirstPurchase,
        status: commission.status,
        createdAt: commission.createdAt.toISOString(),
        approvedAt: commission.approvedAt?.toISOString(),
        paidAt: commission.paidAt?.toISOString(),
        referredUserName: referredUser?.name || 'Unknown User',
        referredUserEmail: referredUser?.email || '',
      };
    });

    return NextResponse.json({
      success: true,
      data: commissions,
    });

  } catch (error) {
    console.error("Error fetching user commissions:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch commission history"
      },
      { status: 500 }
    );
  }
}
