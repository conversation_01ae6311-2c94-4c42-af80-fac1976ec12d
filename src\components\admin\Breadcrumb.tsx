"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { HomeIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

export function Breadcrumb() {
  const pathname = usePathname();

  if (!pathname?.startsWith("/admin")) {
    return null;
  }

  const pathSegments = pathname.split("/").filter(Boolean);

  // Create breadcrumb items
  const breadcrumbs = pathSegments.map((segment, index) => {
    const href = `/${pathSegments.slice(0, index + 1).join("/")}`;
    const isLast = index === pathSegments.length - 1;

    // Format the segment for display (capitalize first letter, replace hyphens with spaces)
    const formattedSegment = segment
      .replace(/-/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());

    return {
      href,
      label: formattedSegment,
      isLast,
    };
  });

  return (
    <nav className="mb-6 flex" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        <li className="inline-flex items-center">
          <Link
            href="/admin/dashboard"
            className="inline-flex items-center rounded-md px-2 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-600"
          >
            <HomeIcon className="mr-2 h-4 w-4" aria-hidden="true" />
            Dashboard
          </Link>
        </li>

        {breadcrumbs.slice(1).map((breadcrumb, index) => (
          <li key={breadcrumb.href}>
            <div className="flex items-center">
              <ChevronRightIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              {breadcrumb.isLast ? (
                <span className="ml-1 rounded-md bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 md:ml-2">
                  {breadcrumb.label}
                </span>
              ) : (
                <Link
                  href={breadcrumb.href}
                  className="ml-1 rounded-md px-2 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-600 md:ml-2"
                >
                  {breadcrumb.label}
                </Link>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
}
