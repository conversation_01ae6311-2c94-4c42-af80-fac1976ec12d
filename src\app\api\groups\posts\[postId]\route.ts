import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, groupMembers } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";
import { deleteMediaFiles } from "@/lib/storage/imageStorage";

const groupPostUpdateSchema = z.object({
  content: z.string().max(5000).optional(),
  images: z.array(z.string()).optional(),
  videos: z.array(z.string()).optional(),
  privacy: z.enum(["public", "subscribers", "private"]).optional(),
  backgroundColor: z.string().nullable().optional(),
  feeling: z.string().nullable().optional(),
  activity: z.string().nullable().optional(),
  location: z.string().nullable().optional(),
  formattedContent: z.boolean().optional(),
}).refine(data => {
  // At least one field must be provided for update
  return Object.keys(data).some(key => data[key as keyof typeof data] !== undefined);
}, {
  message: "At least one field must be provided for update",
}).refine(data => {
  // If content is being updated, it should not be empty (unless there are images/videos)
  if (data.content !== undefined) {
    return data.content.trim().length > 0 ||
           (data.images && data.images.length > 0) ||
           (data.videos && data.videos.length > 0);
  }
  return true;
}, {
  message: "Post must contain either text content, images, or videos",
  path: ["content"],
});

// Delete a group post
export async function DELETE(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { postId } = params;

    // Get the post to check ownership and group
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Check if user is the post owner or group admin/moderator
    const isPostOwner = post.userId === session.user.id;
    
    let isGroupAdmin = false;
    if (post.groupId) {
      const membership = await db.query.groupMembers.findFirst({
        where: and(
          eq(groupMembers.groupId, post.groupId),
          eq(groupMembers.userId, session.user.id)
        ),
      });
      
      isGroupAdmin = membership && (membership.role === 'admin' || membership.role === 'moderator');
    }

    if (!isPostOwner && !isGroupAdmin) {
      return NextResponse.json(
        { message: "Forbidden: You can only delete your own posts or posts in groups you moderate" },
        { status: 403 }
      );
    }

    // Delete associated media files from storage before deleting the post
    let mediaDeleteResult = null;
    if (post.images || post.videos) {
      try {
        console.log(`🗑️ Deleting media files for group post ${postId}...`);
        mediaDeleteResult = await deleteMediaFiles(
          post.images || [],
          post.videos || []
        );

        if (process.env.NODE_ENV === 'development') {
          console.log('Group post media deletion result:', mediaDeleteResult);
        }
      } catch (error) {
        console.error('Error deleting group post media files:', error);
        // Continue with post deletion even if media deletion fails
      }
    }

    // Delete the post from database
    await db.delete(posts).where(eq(posts.id, postId));

    // Prepare response with media deletion info
    const response: any = {
      message: "Post deleted successfully",
      postId: postId
    };

    if (mediaDeleteResult) {
      response.mediaDeleted = {
        success: mediaDeleteResult.success,
        deletedCount: mediaDeleteResult.deletedImages.length,
        failedCount: mediaDeleteResult.failedImages.length,
      };

      if (mediaDeleteResult.failedImages.length > 0) {
        console.warn(`⚠️ Some group post media files could not be deleted:`, mediaDeleteResult.failedImages);
        response.mediaDeleted.warnings = mediaDeleteResult.errors;
      }
    }

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error("Error deleting group post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a group post
export async function PATCH(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { postId } = params;
    const body = await req.json();

    if (process.env.NODE_ENV === 'development') {
      console.log('Group post update - Received body:', JSON.stringify(body, null, 2));
    }

    // Validate the request body
    const validatedData = groupPostUpdateSchema.parse(body);

    if (process.env.NODE_ENV === 'development') {
      console.log('Group post update - Validated data:', JSON.stringify(validatedData, null, 2));
    }

    // Get the post to check ownership
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Check if user is the post owner
    if (post.userId !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden: You can only edit your own posts" },
        { status: 403 }
      );
    }

    // Update the post
    await db
      .update(posts)
      .set({
        content: validatedData.content !== undefined ? validatedData.content : post.content,
        images: validatedData.images !== undefined ? validatedData.images : post.images,
        videos: validatedData.videos !== undefined ? validatedData.videos : post.videos,
        privacy: validatedData.privacy !== undefined ? validatedData.privacy : post.privacy,
        backgroundColor: validatedData.backgroundColor !== undefined ? validatedData.backgroundColor : post.backgroundColor,
        feeling: validatedData.feeling !== undefined ? validatedData.feeling : post.feeling,
        activity: validatedData.activity !== undefined ? validatedData.activity : post.activity,
        location: validatedData.location !== undefined ? validatedData.location : post.location,
        formattedContent: validatedData.formattedContent !== undefined ? validatedData.formattedContent : post.formattedContent,
        updatedAt: new Date(),
      })
      .where(eq(posts.id, postId));

    return NextResponse.json(
      { message: "Post updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating group post:", error);

    if (error instanceof z.ZodError) {
      if (process.env.NODE_ENV === 'development') {
        console.log("Group post validation errors:", error.errors);
      }
      return NextResponse.json(
        {
          message: "Invalid input data",
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
