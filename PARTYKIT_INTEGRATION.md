# PartyKit Real-Time Integration

This document describes the PartyKit real-time functionality implementation for the HiFnF social media application.

## Overview

The PartyKit integration replaces the existing 30-second polling system with real-time WebSocket connections, providing sub-100ms latency for messaging and notifications.

## Features Implemented

### ✅ Real-Time Messaging
- Instant message delivery via WebSocket
- Message delivery status (sent, delivered, read)
- Optimistic UI updates with fallback
- Support for direct messages and fan page messaging

### ✅ Real-Time Notifications
- Instant notifications for likes, comments, follows
- Browser notifications with sound
- Priority-based notification handling
- Automatic fallback to polling

### ✅ Online Presence & Typing Indicators
- User online/offline/away status tracking
- Real-time typing indicators in conversations
- Activity-based presence detection
- Automatic away status after inactivity

### ✅ Performance Monitoring
- Connection latency tracking
- Message delivery metrics
- Error rate monitoring
- Performance scoring system

### ✅ Feature Flag System
- Environment-based feature toggles
- Gradual rollout capabilities
- User group targeting
- Automatic fallback mechanisms

## Architecture

### PartyKit Servers

1. **Main Server** (`src/server.ts`)
   - General purpose WebSocket handling
   - Authentication management
   - Connection lifecycle

2. **Chat Server** (`src/chat.ts`)
   - Message routing and delivery
   - Typing indicators
   - Conversation management
   - Message queuing for offline users

3. **Notifications Server** (`src/notifications.ts`)
   - Real-time notification delivery
   - Notification read status
   - Priority handling

### Client-Side Components

1. **PartyKit Client** (`src/lib/partykit/client.ts`)
   - WebSocket connection management
   - Automatic reconnection
   - Message queuing
   - Latency tracking

2. **Real-Time Hooks**
   - `useRealtimeMessaging` - Message handling
   - `useRealtimeNotifications` - Notification management
   - `useOnlinePresence` - Presence and typing
   - `usePerformanceMonitoring` - Metrics tracking

3. **Enhanced Components**
   - Updated `TypingIndicator` with real-time support
   - Enhanced `MessageInput` with typing detection
   - Real-time `PerformanceDashboard`

## Configuration

### Environment Variables

```bash
# PartyKit Configuration
NEXT_PUBLIC_PARTYKIT_HOST=localhost:1999
PARTYKIT_API_TOKEN=your-api-token

# Feature Flags
NEXT_PUBLIC_FEATURE_REALTIME_MESSAGING=false
NEXT_PUBLIC_FEATURE_REALTIME_NOTIFICATIONS=false
NEXT_PUBLIC_FEATURE_TYPING_INDICATORS=false
NEXT_PUBLIC_FEATURE_ONLINE_PRESENCE=false
NEXT_PUBLIC_FEATURE_MESSAGE_DELIVERY_STATUS=false
NEXT_PUBLIC_FEATURE_POLLING_FALLBACK=true
NEXT_PUBLIC_FEATURE_HYBRID_MODE=false
NEXT_PUBLIC_FEATURE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_FEATURE_ROLLOUT_PERCENTAGE=0
```

### Development Setup

1. **Start PartyKit Server**
   ```bash
   cd hifnf-party
   npm install
   npm run dev
   ```

2. **Configure Environment**
   ```bash
   # Enable real-time features for development
   NEXT_PUBLIC_FEATURE_REALTIME_MESSAGING=true
   NEXT_PUBLIC_FEATURE_REALTIME_NOTIFICATIONS=true
   NEXT_PUBLIC_FEATURE_TYPING_INDICATORS=true
   NEXT_PUBLIC_FEATURE_ONLINE_PRESENCE=true
   NEXT_PUBLIC_FEATURE_ROLLOUT_PERCENTAGE=100
   ```

3. **Start Next.js Application**
   ```bash
   npm run dev
   ```

## Migration Strategy

### Phase 1: Development (Current)
- All real-time features enabled
- 100% rollout for testing
- Hybrid mode for comparison
- Full performance monitoring

### Phase 2: Staging
- 50% rollout to test users
- Core features enabled (messaging, notifications)
- Polling fallback active
- Performance validation

### Phase 3: Production Rollout
- Start with 10% rollout
- Monitor performance metrics
- Gradually increase to 100%
- Maintain polling fallback

## Performance Targets

- **Message Latency**: < 100ms average
- **Connection Uptime**: > 99%
- **Delivery Success Rate**: > 99.5%
- **Reconnection Time**: < 5 seconds
- **Memory Usage**: < 50MB per connection

## Monitoring & Debugging

### Performance Dashboard
Access the real-time performance dashboard in the bottom-right corner of the application to monitor:
- Connection status
- Latency metrics
- Message delivery rates
- Error counts
- Performance scores

### Debug Information
```javascript
// Get feature flag status
const { getDebugInfo } = useFeatureFlags(userId);
console.log(getDebugInfo());

// Get performance metrics
const { metrics, getPerformanceSummary } = usePerformanceMonitoring();
console.log(getPerformanceSummary());
```

## API Integration

### Sending Real-Time Updates from API Endpoints

```typescript
import { emitNotification, emitMessage } from '@/lib/partykit/server-utils';

// In your API route
await emitNotification({
  id: notificationId,
  type: "like",
  recipientId: userId,
  senderId: currentUserId,
  timestamp: new Date().toISOString()
});
```

### Updated API Endpoints
- `src/app/api/messages/route.ts` - Real-time message emission
- `src/app/api/fan-pages/[pageId]/messages/route.ts` - Fan page messaging
- `src/lib/utils/notifications.ts` - Enhanced notification system

## Cost Optimization

### Connection Management
- Smart connection pooling
- Automatic connection cleanup
- Room optimization strategies
- Message batching for efficiency

### Rate Limiting
- Built-in rate limiting for PartyKit requests
- Intelligent message queuing
- Bandwidth optimization

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check PartyKit server status
   - Verify environment variables
   - Ensure firewall allows WebSocket connections

2. **High Latency**
   - Monitor network conditions
   - Check server load
   - Verify geographic proximity to PartyKit servers

3. **Message Delivery Issues**
   - Check authentication status
   - Verify user permissions
   - Monitor error logs

### Fallback Behavior
The system automatically falls back to polling when:
- PartyKit connection fails
- Authentication errors occur
- Network connectivity issues
- Feature flags disable real-time features

## Security Considerations

- JWT token verification for all connections
- Rate limiting on all endpoints
- Input validation and sanitization
- CORS configuration for WebSocket connections
- Automatic session cleanup on disconnect

## Future Enhancements

- Voice/video call integration
- File sharing with real-time progress
- Advanced presence features (location, status)
- Message encryption for sensitive content
- Advanced analytics and insights

## Support

For issues or questions regarding the PartyKit integration:
1. Check the performance dashboard for connection status
2. Review the browser console for error messages
3. Verify environment configuration
4. Test with feature flags disabled to isolate issues

## Contributing

When contributing to the real-time features:
1. Test with both real-time and polling modes
2. Ensure backward compatibility
3. Add appropriate performance monitoring
4. Update feature flag documentation
5. Test fallback scenarios
