import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  referralCommissions,
  referrals,
  walletTransactions
} from "@/lib/db/schema";
import { eq, and, sum, count, sql } from "drizzle-orm";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get referral earnings from referralCommissions table (primary source)
    // Only count paid commissions
    const referralEarningsFromCommissionsResult = await db
      .select({ total: sum(referralCommissions.commissionAmount) })
      .from(referralCommissions)
      .where(
        and(
          eq(referralCommissions.referrerId, userId),
          eq(referralCommissions.status, 'paid')
        )
      );

    // Get referral earnings from wallet transactions (backup method)
    const referralEarningsFromWalletResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.userId, userId),
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          sql`JSON_EXTRACT(metadata, '$.type') = 'referral_commission'`
        )
      );

    // Get referral statistics
    const totalReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.referrerId, userId));

    const activeReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(
        and(
          eq(referrals.referrerId, userId),
          eq(referrals.status, 'completed')
        )
      );

    // Calculate referral earnings from commissions table (primary source)
    const referralEarningsFromCommissions = parseFloat(referralEarningsFromCommissionsResult[0]?.total || '0');
    const referralEarningsFromWallet = parseFloat(referralEarningsFromWalletResult[0]?.total || '0');

    // Use commission table data as primary source for referral earnings
    const referralEarnings = referralEarningsFromCommissions > 0 ? referralEarningsFromCommissions : referralEarningsFromWallet;

    // Subscription commissions are the same as referral commissions in this system
    const subscriptionCommissions = referralEarningsFromCommissions;

    const totalReferrals = totalReferralsResult[0]?.count || 0;
    const activeReferrals = activeReferralsResult[0]?.count || 0;

    const data = {
      referralEarnings,
      subscriptionCommissions,
      totalReferrals,
      activeReferrals,
      breakdown: {
        fromCommissionsTable: referralEarningsFromCommissions,
        fromWalletTransactions: referralEarningsFromWallet,
      }
    };

    return NextResponse.json({
      success: true,
      data,
    });

  } catch (error) {
    console.error("Error fetching referral earnings:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch referral earnings",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
