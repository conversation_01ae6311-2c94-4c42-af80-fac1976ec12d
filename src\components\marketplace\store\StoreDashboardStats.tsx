"use client";

import {
  ShoppingBagIcon,
  UsersIcon,
  StarIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  EyeIcon
} from "@heroicons/react/24/outline";

interface StoreDashboardStatsProps {
  store: {
    slug: string;
    productCount: number;
    followerCount: number;
    reviewCount: number;
    averageRating: number | string | null;
  };
  totalViews?: number;
  totalRevenue?: number;
}

export function StoreDashboardStats({
  store,
  totalViews = 0,
  totalRevenue = 0
}: StoreDashboardStatsProps) {
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount); // Amount is already in correct format
  };

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-blue-100 p-3">
            <ShoppingBagIcon className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Products</p>
            <p className="text-xl font-semibold text-gray-900">{store.productCount}</p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-green-100 p-3">
            <UsersIcon className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Followers</p>
            <p className="text-xl font-semibold text-gray-900">{store.followerCount}</p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-yellow-100 p-3">
            <StarIcon className="h-5 w-5 text-yellow-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Rating</p>
            <p className="text-xl font-semibold text-gray-900">
              {typeof store.averageRating === 'number'
                ? store.averageRating.toFixed(1)
                : store.averageRating || "N/A"}
              <span className="ml-1 text-xs text-gray-500">
                ({store.reviewCount})
              </span>
            </p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-purple-100 p-3">
            <ChartBarIcon className="h-5 w-5 text-purple-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Store URL</p>
            <p className="text-sm font-semibold text-gray-900 truncate">
              /store/{store.slug}
            </p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-indigo-100 p-3">
            <EyeIcon className="h-5 w-5 text-indigo-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Total Views</p>
            <p className="text-xl font-semibold text-gray-900">{totalViews}</p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-emerald-100 p-3">
            <CurrencyDollarIcon className="h-5 w-5 text-emerald-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Revenue</p>
            <p className="text-xl font-semibold text-gray-900">{formatCurrency(totalRevenue)}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
