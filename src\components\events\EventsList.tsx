"use client";

import { useState, useEffect } from "react";
import { EventCard } from "./EventCard";
import { Spinner } from "@/components/ui/Spinner";
import { CalendarIcon } from "@heroicons/react/24/outline";

interface Event {
  id: string;
  name: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  isOnline: boolean | null;
  coverImage: string | null;
  visibility: string;
  category: string | null;
  hostId: string;
  host?: {
    id: string;
    name: string;
    username: string | null;
    image: string | null;
  };
  attendeeCounts?: {
    going: number;
    interested: number;
    not_going: number;
  };
  userAttendance?: "going" | "interested" | "not_going" | null;
}

interface EventsListProps {
  title: string;
  emptyMessage?: string;
  filter?: {
    category?: string;
    visibility?: string;
    startDate?: string;
    endDate?: string;
    hostId?: string;
    search?: string;
  };
  variant?: "compact" | "full";
}

export function EventsList({ title, emptyMessage, filter, variant = "compact" }: EventsListProps) {
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Build query string from filter
        const queryParams = new URLSearchParams();
        if (filter?.category) queryParams.append("category", filter.category);
        if (filter?.visibility) queryParams.append("visibility", filter.visibility);
        if (filter?.startDate) queryParams.append("startDate", filter.startDate);
        if (filter?.endDate) queryParams.append("endDate", filter.endDate);
        if (filter?.hostId) queryParams.append("hostId", filter.hostId);
        if (filter?.search) queryParams.append("search", filter.search);
        
        const queryString = queryParams.toString();
        const url = `/api/events${queryString ? `?${queryString}` : ''}`;
        
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error("Failed to fetch events");
        }
        
        const data = await response.json();
        setEvents(data);
      } catch (err) {
        console.error("Error fetching events:", err);
        setError("Failed to load events. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchEvents();
  }, [filter]);

  const handleRsvp = async (eventId: string, status: "going" | "interested" | "not_going") => {
    try {
      const response = await fetch(`/api/events/${eventId}/attendees`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to update RSVP");
      }
      
      // Update the local state
      setEvents(prevEvents => 
        prevEvents.map(event => 
          event.id === eventId 
            ? { 
                ...event, 
                userAttendance: status,
                attendeeCounts: {
                  ...event.attendeeCounts!,
                  [status]: (event.attendeeCounts?.[status] || 0) + 1,
                  ...(event.userAttendance && {
                    [event.userAttendance]: Math.max(0, (event.attendeeCounts?.[event.userAttendance] || 0) - 1)
                  })
                }
              } 
            : event
        )
      );
    } catch (err) {
      console.error("Error updating RSVP:", err);
      // Show error message to user
    }
  };

  if (isLoading) {
    return (
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900">{title}</h2>
          <div className="flex justify-center py-10">
            <Spinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900">{title}</h2>
          <div className="rounded-lg bg-red-50 p-4 text-red-800">
            <p>{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900">{title}</h2>
          <div className="rounded-lg border border-gray-200 p-8 text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
              <CalendarIcon className="h-8 w-8 text-blue-600" />
            </div>
            <p className="mt-4 text-sm text-gray-500">
              {emptyMessage || "No events found."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow">
      <div className="p-6">
        <h2 className="mb-4 text-lg font-semibold text-gray-900">{title}</h2>
        <div className="space-y-4">
          {events.map(event => (
            <EventCard
              key={event.id}
              event={event}
              onRsvp={handleRsvp}
              userAttendance={event.userAttendance}
              variant={variant}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
