import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventInvites, users, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, or, inArray } from "drizzle-orm";

const inviteSchema = z.object({
  userIds: z.array(z.string()),
});

// Get all invites for an event
export async function GET(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the user is the host or has been invited
    const isHost = event.hostId === session.user.id;
    const isInvited = await db.query.eventInvites.findFirst({
      where: and(
        eq(eventInvites.eventId, eventId),
        eq(eventInvites.toUserId, session.user.id)
      ),
    });

    if (!isHost && !isInvited) {
      return NextResponse.json(
        { message: "You don't have permission to view invites for this event" },
        { status: 403 }
      );
    }

    // Get the status filter from query params
    const url = new URL(req.url);
    const status = url.searchParams.get("status") as "pending" | "accepted" | "declined" | null;

    // Fetch invites with user information
    let query = db.select({
      id: eventInvites.id,
      status: eventInvites.status,
      createdAt: eventInvites.createdAt,
      fromUser: {
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
      },
    })
    .from(eventInvites)
    .innerJoin(users, eq(eventInvites.fromUserId, users.id))
    .where(
      and(
        eq(eventInvites.eventId, eventId),
        eq(eventInvites.toUserId, session.user.id)
      )
    );

    // Apply status filter if provided
    if (status) {
      query = db.select({
        id: eventInvites.id,
        status: eventInvites.status,
        createdAt: eventInvites.createdAt,
        fromUser: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
      })
      .from(eventInvites)
      .innerJoin(users, eq(eventInvites.fromUserId, users.id))
      .where(and(
        eq(eventInvites.eventId, eventId),
        eq(eventInvites.toUserId, session.user.id),
        eq(eventInvites.status, status)
      ));
    }

    const invites = await query;

    return NextResponse.json(invites);
  } catch (error) {
    console.error("Error fetching event invites:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Invite users to an event
export async function POST(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;
    const body = await req.json();
    const validatedData = inviteSchema.parse(body);

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the user is the host or a member
    if (event.hostId !== session.user.id) {
      return NextResponse.json(
        { message: "Only the event host can send invites" },
        { status: 403 }
      );
    }

    // Check if users exist
    const usersToInvite = await db.query.users.findMany({
      where: inArray(users.id, validatedData.userIds),
    });

    if (usersToInvite.length !== validatedData.userIds.length) {
      return NextResponse.json(
        { message: "One or more users not found" },
        { status: 404 }
      );
    }

    // Check if any users are already invited
    const existingInvites = await db.query.eventInvites.findMany({
      where: and(
        eq(eventInvites.eventId, eventId),
        inArray(eventInvites.toUserId, validatedData.userIds)
      ),
    });

    const alreadyInvitedUserIds = existingInvites.map(invite => invite.toUserId);
    const newUserIds = validatedData.userIds.filter(id => !alreadyInvitedUserIds.includes(id));

    // Create invites for new users
    if (newUserIds.length > 0) {
      const invitesToCreate = newUserIds.map(userId => ({
        id: uuidv4(),
        eventId,
        fromUserId: session.user.id,
        toUserId: userId,
        status: "pending" as const,
      }));

      await db.insert(eventInvites).values(invitesToCreate);

      // Create notifications for invited users
      const notificationsToCreate = newUserIds.map(userId => ({
        id: uuidv4(),
        recipientId: userId,
        type: "event_invite" as const,
        senderId: session.user.id,
        eventId,
        read: false,
      }));

      await db.insert(notifications).values(notificationsToCreate);
    }

    return NextResponse.json({
      message: "Invites sent successfully",
      invited: newUserIds.length,
      alreadyInvited: alreadyInvitedUserIds.length
    });
  } catch (error) {
    console.error("Error sending event invites:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
