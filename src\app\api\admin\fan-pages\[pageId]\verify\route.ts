import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, users, notifications } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const verifySchema = z.object({
  isVerified: z.boolean(),
});

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// PATCH /api/admin/fan-pages/[pageId]/verify - Update verification status
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0 || !user[0].isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = verifySchema.parse(body);

    // Check if fan page exists
    const pageResult = await db
      .select({
        id: fanPages.id,
        ownerId: fanPages.ownerId,
        name: fanPages.name,
        isVerified: fanPages.isVerified,
      })
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];

    // Update verification status
    await db
      .update(fanPages)
      .set({
        isVerified: validatedData.isVerified,
        updatedAt: new Date(),
      })
      .where(eq(fanPages.id, pageId));

    // Create notification for page owner
    const notificationId = uuidv4();
    await db.insert(notifications).values({
      id: notificationId,
      recipientId: page.ownerId,
      type: "fan_page_role_added", // Using existing type as placeholder for verification
      fanPageId: pageId,
    });

    return NextResponse.json({
      message: `Fan page ${validatedData.isVerified ? "verified" : "unverified"} successfully`,
      isVerified: validatedData.isVerified,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating verification status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
