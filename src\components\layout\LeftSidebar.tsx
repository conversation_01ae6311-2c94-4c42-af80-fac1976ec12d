"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import { useMobileView } from "@/hooks/useClientSide";
import {
  HomeIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  BellIcon,
  UserCircleIcon,
  BookmarkIcon,
  ClockIcon,
  UsersIcon,
  CalendarIcon,
  PhotoIcon,
  ShoppingBagIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BuildingStorefrontIcon,
  UserIcon,
  NewspaperIcon,
  BanknotesIcon,
  GlobeAltIcon,
  DocumentTextIcon
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";
import { useWallet } from "@/contexts/WalletContext";

export function LeftSidebar() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [showMore, setShowMore] = useState(false);
  const { balance, loading: walletLoading } = useWallet();
  const isMobile = useMobileView();

  // Format balance for display
  const formatBalance = (amount: string | undefined) => {
    if (!amount) return "0.00";
    return parseFloat(amount).toFixed(2);
  };

  const sidebarItems = [
    { name: "News Feed", href: "/", icon: HomeIcon },
    { name: "Connections", href: "/connection", icon: UsersIcon },
    { name: "Saved", href: "/saved", icon: BookmarkIcon },
    { name: "Memories", href: "/memories", icon: ClockIcon },
    { name: "Groups", href: "/groups", icon: UsersIcon },
    { name: "Events", href: "/events", icon: CalendarIcon },
    { name: "Pages", href: "/pages", icon: NewspaperIcon },
    { name: "Marketplace", href: "/marketplace", icon: BuildingStorefrontIcon },
    { name: "Blogs", href: "/blogs", icon: DocumentTextIcon },
    { name: "Photos", href: "/photos", icon: PhotoIcon },
    {
      name: walletLoading ? "Wallet (...)" : `Wallet ($${formatBalance(balance?.generalBalance)})`,
      href: "/wallet",
      icon: BanknotesIcon
    },
    {
      name: walletLoading ? "Earning World (...)" : `Earning World ($${formatBalance(balance?.earningBalance)})`,
      href: "/earning-world",
      icon: GlobeAltIcon
    },
  ];

  const moreItems: any[] = [];

  // Don't render on mobile - navigation is handled by mobile menu
  if (isMobile) {
    return null;
  }

  return (
    <div className="block w-full">
      <div className="relative lg:fixed lg:top-[5rem] lg:w-[calc(20%-1rem)] space-y-3 overflow-y-auto pr-2 lg:h-[calc(100vh-5rem)] pt-4 scrollbar-none">
        {/* Profile section removed */}

        {/* Main Navigation */}
        <nav className="space-y-1">
          {sidebarItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center rounded-xl px-2 py-2 text-sm font-medium transition-all duration-200",
                pathname === item.href
                  ? "bg-blue-50 text-blue-600 shadow-sm"
                  : "text-gray-700 hover:bg-gray-100"
              )}
            >
              <div className={cn(
                "flex items-center justify-center h-7 w-7 rounded-full mr-2",
                pathname === item.href
                  ? "bg-blue-100 text-blue-600"
                  : "text-gray-600"
              )}>
                <item.icon className="h-4 w-4" />
              </div>
              <span>{item.name}</span>
            </Link>
          ))}

          {/* See More Button */}
          {!showMore && (
            <button
              onClick={() => setShowMore(true)}
              className="flex w-full items-center rounded-xl px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 transition-all duration-200 mt-1"
            >
              <div className="flex items-center justify-center h-7 w-7 rounded-full bg-gray-200 mr-2">
                <ChevronDownIcon className="h-4 w-4 text-gray-600" />
              </div>
              <span>See More</span>
            </button>
          )}

          {/* More Items (expandable) */}
          {showMore && (
            <div className="space-y-1 mt-1 animate-fadeIn">
              {moreItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center rounded-xl px-2 py-2 text-sm font-medium transition-all duration-200",
                    pathname === item.href
                      ? "bg-blue-50 text-blue-600 shadow-sm"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  <div className="flex items-center justify-center h-7 w-7 rounded-full bg-gray-100 text-gray-600 mr-2">
                    <item.icon className="h-4 w-4" />
                  </div>
                  <span>{item.name}</span>
                </Link>
              ))}

              {/* See Less Button */}
              <button
                onClick={() => setShowMore(false)}
                className="flex w-full items-center rounded-xl px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 transition-all duration-200 mt-1"
              >
                <div className="flex items-center justify-center h-7 w-7 rounded-full bg-gray-200 mr-2">
                  <ChevronUpIcon className="h-4 w-4 text-gray-600" />
                </div>
                <span>See Less</span>
              </button>
            </div>
          )}
        </nav>

        {/* Footer */}
        <div className="px-2 pt-2 text-[10px] text-gray-500 space-y-1">
          <div className="flex flex-wrap gap-x-1">
            <Link href="/privacy" className="hover:underline">Privacy</Link>
            <span>·</span>
            <Link href="/terms" className="hover:underline">Terms</Link>
            <span>·</span>
            <Link href="/cookies" className="hover:underline">Cookies</Link>
          </div>
          <p>© 2024 HIFNF</p>
        </div>
      </div>
    </div>
  );
}
