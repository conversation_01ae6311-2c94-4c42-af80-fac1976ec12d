import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { SettingsSidebar } from "@/components/settings/SettingsSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { SettingsLayout } from "@/components/settings/SettingsLayout";
import { Button } from "@/components/ui/Button";
import { ComputerDesktopIcon, DevicePhoneMobileIcon, DeviceTabletIcon } from "@heroicons/react/24/outline";

export default async function SessionsSettingsPage() {
  const user = await requireAuth();

  // Mock data for active sessions
  const activeSessions = [
    {
      id: "1",
      device: "Desktop",
      browser: "Chrome",
      location: "Dhaka, Bangladesh",
      ip: "***********",
      lastActive: "Just now",
      isCurrent: true,
    },
    {
      id: "2",
      device: "Mobile",
      browser: "Safari",
      location: "Dhaka, Bangladesh",
      ip: "***********",
      lastActive: "2 hours ago",
      isCurrent: false,
    },
    {
      id: "3",
      device: "Tablet",
      browser: "Firefox",
      location: "Chittagong, Bangladesh",
      ip: "***********",
      lastActive: "Yesterday",
      isCurrent: false,
    },
  ];

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case "Desktop":
        return <ComputerDesktopIcon className="h-5 w-5 text-gray-500" />;
      case "Mobile":
        return <DevicePhoneMobileIcon className="h-5 w-5 text-gray-500" />;
      case "Tablet":
        return <DeviceTabletIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <ComputerDesktopIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Settings sidebar */}
          <div className="lg:col-span-1">
            <SettingsSidebar />
          </div>

          {/* Main content */}
          <div className="lg:col-span-2">
            <SettingsLayout 
              title="Manage Sessions" 
              description="View and manage your active sessions across devices."
            >
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-4">Active Sessions</h4>
                
                <div className="space-y-4">
                  {activeSessions.map((session) => (
                    <div 
                      key={session.id} 
                      className="flex items-start justify-between p-4 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 mt-1">
                          {getDeviceIcon(session.device)}
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h5 className="text-sm font-medium text-gray-900">
                              {session.browser} on {session.device}
                            </h5>
                            {session.isCurrent && (
                              <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                Current
                              </span>
                            )}
                          </div>
                          <p className="mt-1 text-xs text-gray-500">
                            {session.location} • {session.ip}
                          </p>
                          <p className="mt-1 text-xs text-gray-500">
                            Last active: {session.lastActive}
                          </p>
                        </div>
                      </div>
                      
                      {!session.isCurrent && (
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          Logout
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="pt-5">
                <div className="flex justify-end">
                  <Button variant="danger">
                    Logout from all devices
                  </Button>
                </div>
              </div>
            </SettingsLayout>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}
