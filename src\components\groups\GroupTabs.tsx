"use client";

import {
  ChatBubbleLeftRightIcon,
  UsersIcon,
  PhotoIcon,
  InformationCircleIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";

interface GroupTabsProps {
  groupId: string;
  activeTab: 'posts' | 'members' | 'media' | 'about' | 'settings';
  showSettings?: boolean;
}

export function GroupTabs({ groupId, activeTab, showSettings = false }: GroupTabsProps) {
  return (
    <div className="border-b border-gray-200 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex space-x-8 overflow-x-auto">
          <a 
            href={`/groups/${groupId}`} 
            className={`border-b-2 py-4 px-1 text-sm font-medium ${
              activeTab === 'posts' 
                ? 'border-blue-500 text-blue-600' 
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center">
              <ChatBubbleLeftRightIcon className="mr-2 h-5 w-5" />
              <span>Posts</span>
            </div>
          </a>
          <a 
            href={`/groups/${groupId}/members`} 
            className={`border-b-2 py-4 px-1 text-sm font-medium ${
              activeTab === 'members' 
                ? 'border-blue-500 text-blue-600' 
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center">
              <UsersIcon className="mr-2 h-5 w-5" />
              <span>Members</span>
            </div>
          </a>
          <a 
            href={`/groups/${groupId}/media`} 
            className={`border-b-2 py-4 px-1 text-sm font-medium ${
              activeTab === 'media' 
                ? 'border-blue-500 text-blue-600' 
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center">
              <PhotoIcon className="mr-2 h-5 w-5" />
              <span>Media</span>
            </div>
          </a>
          <a
            href={`/groups/${groupId}/about`}
            className={`border-b-2 py-4 px-1 text-sm font-medium ${
              activeTab === 'about'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center">
              <InformationCircleIcon className="mr-2 h-5 w-5" />
              <span>About</span>
            </div>
          </a>
          {showSettings && (
            <a
              href={`/groups/${groupId}/settings`}
              className={`border-b-2 py-4 px-1 text-sm font-medium ${
                activeTab === 'settings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
              }`}
            >
              <div className="flex items-center">
                <Cog6ToothIcon className="mr-2 h-5 w-5" />
                <span>Settings</span>
              </div>
            </a>
          )}
        </div>
      </div>
    </div>
  );
}
