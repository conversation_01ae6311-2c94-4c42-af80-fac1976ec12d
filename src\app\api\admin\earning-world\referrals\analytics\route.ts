import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { 
  referralCommissions, 
  packageCommissionSettings, 
  subscriptionPlans,
  referrals 
} from "@/lib/db/schema";
import { sql, desc, eq, and, gte } from "drizzle-orm";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get total commissions and referrals
    const totalStats = await db
      .select({
        totalCommissions: sql<number>`COALESCE(SUM(${referralCommissions.commissionAmount}), 0)`,
        totalReferrals: sql<number>`COUNT(*)`,
        averageCommission: sql<number>`COALESCE(AVG(${referralCommissions.commissionAmount}), 0)`
      })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'approved'));

    // Get top performing plans
    const topPerformingPlans = await db
      .select({
        planName: referralCommissions.planName,
        totalCommissions: sql<number>`SUM(${referralCommissions.commissionAmount})`,
        referralCount: sql<number>`COUNT(*)`,
        averageCommission: sql<number>`AVG(${referralCommissions.commissionAmount})`
      })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'approved'))
      .groupBy(referralCommissions.planId, referralCommissions.planName)
      .orderBy(desc(sql`SUM(${referralCommissions.commissionAmount})`))
      .limit(5);

    // Get commission by type
    const commissionsByType = await db
      .select({
        commissionType: referralCommissions.commissionType,
        totalAmount: sql<number>`SUM(${referralCommissions.commissionAmount})`
      })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'approved'))
      .groupBy(referralCommissions.commissionType);

    // Get monthly trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyTrends = await db
      .select({
        month: sql<string>`DATE_FORMAT(${referralCommissions.createdAt}, '%Y-%m')`,
        commissions: sql<number>`SUM(${referralCommissions.commissionAmount})`,
        referrals: sql<number>`COUNT(*)`
      })
      .from(referralCommissions)
      .where(
        and(
          eq(referralCommissions.status, 'approved'),
          gte(referralCommissions.createdAt, sixMonthsAgo)
        )
      )
      .groupBy(sql`DATE_FORMAT(${referralCommissions.createdAt}, '%Y-%m')`)
      .orderBy(sql`DATE_FORMAT(${referralCommissions.createdAt}, '%Y-%m')`);

    // Format commission types data
    const commissionTypeData = {
      percentage: 0,
      fixed: 0
    };

    commissionsByType.forEach(item => {
      if (item.commissionType === 'percentage') {
        commissionTypeData.percentage = item.totalAmount;
      } else if (item.commissionType === 'fixed') {
        commissionTypeData.fixed = item.totalAmount;
      }
    });

    // Format monthly trends with month names
    const formattedMonthlyTrends = monthlyTrends.map(trend => ({
      month: new Date(trend.month + '-01').toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short' 
      }),
      commissions: trend.commissions,
      referrals: trend.referrals
    }));

    const analytics = {
      totalCommissions: totalStats[0]?.totalCommissions || 0,
      totalReferrals: totalStats[0]?.totalReferrals || 0,
      averageCommission: totalStats[0]?.averageCommission || 0,
      topPerformingPlans: topPerformingPlans.map(plan => ({
        planName: plan.planName,
        totalCommissions: plan.totalCommissions,
        referralCount: plan.referralCount,
        averageCommission: plan.averageCommission
      })),
      monthlyTrends: formattedMonthlyTrends,
      commissionsByType: commissionTypeData
    };

    return NextResponse.json({
      success: true,
      data: analytics,
    });

  } catch (error) {
    console.error("Error fetching commission analytics:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch commission analytics"
      },
      { status: 500 }
    );
  }
}

// Get detailed commission report
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { startDate, endDate, planId, status } = body;

    let whereConditions = [];

    if (startDate) {
      whereConditions.push(gte(referralCommissions.createdAt, new Date(startDate)));
    }

    if (endDate) {
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      whereConditions.push(sql`${referralCommissions.createdAt} <= ${endDateTime}`);
    }

    if (planId) {
      whereConditions.push(eq(referralCommissions.planId, planId));
    }

    if (status) {
      whereConditions.push(eq(referralCommissions.status, status));
    }

    const whereClause = whereConditions.length > 0 
      ? and(...whereConditions)
      : undefined;

    const detailedReport = await db
      .select({
        id: referralCommissions.id,
        referrerId: referralCommissions.referrerId,
        referredUserId: referralCommissions.referredUserId,
        planName: referralCommissions.planName,
        subscriptionAmount: referralCommissions.subscriptionAmount,
        commissionType: referralCommissions.commissionType,
        commissionRate: referralCommissions.commissionRate,
        commissionAmount: referralCommissions.commissionAmount,
        status: referralCommissions.status,
        isFirstPurchase: referralCommissions.isFirstPurchase,
        createdAt: referralCommissions.createdAt,
        processedAt: referralCommissions.processedAt
      })
      .from(referralCommissions)
      .where(whereClause)
      .orderBy(desc(referralCommissions.createdAt))
      .limit(1000); // Limit to prevent large responses

    // Calculate summary for the filtered data
    const summary = {
      totalCommissions: detailedReport.reduce((sum, item) => sum + parseFloat(item.commissionAmount.toString()), 0),
      totalReferrals: detailedReport.length,
      averageCommission: detailedReport.length > 0 
        ? detailedReport.reduce((sum, item) => sum + parseFloat(item.commissionAmount.toString()), 0) / detailedReport.length
        : 0,
      statusBreakdown: detailedReport.reduce((acc, item) => {
        acc[item.status] = (acc[item.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return NextResponse.json({
      success: true,
      data: {
        report: detailedReport,
        summary
      },
    });

  } catch (error) {
    console.error("Error generating commission report:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to generate commission report"
      },
      { status: 500 }
    );
  }
}
