/**
 * Utility functions for messaging system
 */

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image?: string;
  };
  receiver?: {
    id: string;
    name: string;
    image?: string;
  };
}

export interface Conversation {
  user: {
    id: string;
    name: string;
    image?: string;
  };
  lastMessage: Message;
  unreadCount: number;
  isPinned?: boolean;
  isArchived?: boolean;
  type?: 'user' | 'fanpage';
  fanPageId?: string;
}

/**
 * Validates message data structure
 */
export function isValidMessage(message: any): message is Message {
  return (
    message &&
    typeof message.id === 'string' &&
    typeof message.senderId === 'string' &&
    typeof message.receiverId === 'string' &&
    typeof message.content === 'string' &&
    typeof message.read === 'boolean' &&
    typeof message.createdAt === 'string'
  );
}

/**
 * Validates conversation data structure
 */
export function isValidConversation(conversation: any): conversation is Conversation {
  return (
    conversation &&
    conversation.user &&
    typeof conversation.user.id === 'string' &&
    typeof conversation.user.name === 'string' &&
    conversation.lastMessage &&
    isValidMessage(conversation.lastMessage) &&
    typeof conversation.unreadCount === 'number'
  );
}

/**
 * Filters out invalid messages from an array
 */
export function filterValidMessages(messages: any[]): Message[] {
  if (!Array.isArray(messages)) {
    console.warn('filterValidMessages: input is not an array');
    return [];
  }
  
  return messages.filter(isValidMessage);
}

/**
 * Filters out invalid conversations from an array
 */
export function filterValidConversations(conversations: any[]): Conversation[] {
  if (!Array.isArray(conversations)) {
    console.warn('filterValidConversations: input is not an array');
    return [];
  }
  
  return conversations.filter(isValidConversation);
}

/**
 * Removes duplicate conversations based on user ID
 */
export function removeDuplicateConversations(conversations: Conversation[]): Conversation[] {
  const conversationMap = new Map<string, Conversation>();
  
  conversations.forEach(conv => {
    const key = conv.user.id;
    const existing = conversationMap.get(key);
    
    if (!existing) {
      conversationMap.set(key, conv);
    } else {
      // Keep the conversation with the most recent message
      const existingTime = new Date(existing.lastMessage.createdAt).getTime();
      const currentTime = new Date(conv.lastMessage.createdAt).getTime();
      
      if (currentTime > existingTime) {
        // Update with newer message but preserve important properties
        conversationMap.set(key, {
          ...conv,
          unreadCount: Math.max(existing.unreadCount, conv.unreadCount),
          isPinned: existing.isPinned || conv.isPinned,
          isArchived: existing.isArchived || conv.isArchived
        });
      } else {
        // Keep existing but update unread count if needed
        conversationMap.set(key, {
          ...existing,
          unreadCount: Math.max(existing.unreadCount, conv.unreadCount)
        });
      }
    }
  });
  
  return Array.from(conversationMap.values());
}

/**
 * Sorts conversations by last message time (most recent first)
 */
export function sortConversationsByTime(conversations: Conversation[]): Conversation[] {
  return conversations.sort((a, b) => {
    // Sort pinned conversations first
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    
    // Then sort by last message time
    const timeA = new Date(a.lastMessage.createdAt).getTime();
    const timeB = new Date(b.lastMessage.createdAt).getTime();
    return timeB - timeA;
  });
}

/**
 * Checks if a conversation ID represents a fan page
 */
export function isFanPageConversation(conversationId: string): boolean {
  return conversationId.startsWith('fanpage_');
}

/**
 * Extracts fan page ID from conversation ID
 */
export function extractFanPageId(conversationId: string): string {
  return conversationId.replace('fanpage_', '');
}

/**
 * Creates a fan page conversation ID
 */
export function createFanPageConversationId(fanPageId: string): string {
  return `fanpage_${fanPageId}`;
}

/**
 * Sanitizes message content
 */
export function sanitizeMessageContent(content: string): string {
  if (typeof content !== 'string') {
    return '';
  }
  
  return content.trim().slice(0, 5000); // Max 5000 characters
}

/**
 * Formats message timestamp for display
 */
export function formatMessageTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } else if (diffInHours < 48) {
      return 'Yesterday ' + date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    }
  } catch (error) {
    console.error('Error formatting message time:', error);
    return 'Invalid date';
  }
}

/**
 * Groups messages by date
 */
export function groupMessagesByDate(messages: Message[]): Record<string, Message[]> {
  const groups: Record<string, Message[]> = {};
  
  messages.forEach(message => {
    try {
      const date = new Date(message.createdAt);
      const dateKey = date.toDateString();
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      
      groups[dateKey].push(message);
    } catch (error) {
      console.error('Error grouping message by date:', error);
    }
  });
  
  return groups;
}

/**
 * Checks if two messages are from the same sender and close in time
 */
export function shouldGroupMessages(msg1: Message, msg2: Message, maxMinutes: number = 5): boolean {
  if (msg1.senderId !== msg2.senderId) {
    return false;
  }
  
  try {
    const time1 = new Date(msg1.createdAt).getTime();
    const time2 = new Date(msg2.createdAt).getTime();
    const diffInMinutes = Math.abs(time2 - time1) / (1000 * 60);
    
    return diffInMinutes <= maxMinutes;
  } catch (error) {
    console.error('Error comparing message times:', error);
    return false;
  }
}
