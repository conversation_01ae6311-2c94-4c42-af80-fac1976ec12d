"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  DocumentArrowDownIcon,
  ArrowPathIcon,
  CurrencyDollarIcon,
  UsersIcon,
  UserGroupIcon,
  BanknotesIcon,
  TrophyIcon,
  StarIcon
} from "@heroicons/react/24/outline";

interface AnalyticsData {
  overview: {
    totalEarnings: number;
    totalUsers: number;
    activeEarners: number;
    totalPayouts: number;
    growthRate: number;
    conversionRate: number;
  };
  trends: {
    earningsTrend: Array<{
      date: string;
      amount: number;
      referrals: number;
      blogs: number;
    }>;
    userGrowth: Array<{
      date: string;
      newUsers: number;
      activeUsers: number;
    }>;
  };
  performance: {
    topEarners: Array<{
      id: string;
      name: string;
      username: string;
      totalEarnings: number;
      earningType: string;
    }>;
    topReferrers: Array<{
      id: string;
      name: string;
      username: string;
      referrals: number;
      earnings: number;
    }>;
    topBlogs: Array<{
      id: string;
      title: string;
      author: string;
      reads: number;
      earnings: number;
    }>;
  };
  metrics: {
    averageEarningPerUser: number;
    averageReferralReward: number;
    averageBlogEarning: number;
    payoutSuccessRate: number;
    userRetentionRate: number;
    referralConversionRate: number;
  };
}

type TimeRange = '7d' | '30d' | '90d' | '1y';

export default function AdminEarningWorldAnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');
  const [exporting, setExporting] = useState(false);

  const fetchAnalytics = async () => {
    try {
      const response = await fetch(`/api/admin/earning-world/analytics?range=${timeRange}`);
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [timeRange]);

  const fetchData = () => {
    setLoading(true);
    fetchAnalytics();
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchAnalytics();
  };

  const handleExport = async () => {
    setExporting(true);
    try {
      const response = await fetch(`/api/admin/earning-world/analytics/export?range=${timeRange}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `earning-world-analytics-${timeRange}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting analytics:', error);
    } finally {
      setExporting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const getTimeRangeLabel = (range: TimeRange) => {
    const labels = {
      '7d': 'Last 7 Days',
      '30d': 'Last 30 Days',
      '90d': 'Last 90 Days',
      '1y': 'Last Year'
    };
    return labels[range];
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Earning World Analytics</h1>
            <p className="text-gray-600 mt-1">
              Detailed analytics and performance metrics
            </p>
          </div>
          <div className="flex space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as TimeRange)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
            <Button
              variant="outline"
              onClick={handleExport}
              disabled={exporting}
            >
              <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
              {exporting ? 'Exporting...' : 'Export'}
            </Button>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {analytics && (
          <>
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm opacity-90">Total Earnings</p>
                    <p className="text-2xl font-bold">{formatCurrency(analytics.overview.totalEarnings)}</p>
                    <div className="flex items-center mt-2">
                      {analytics.overview.growthRate >= 0 ? (
                        <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
                      ) : (
                        <ArrowTrendingDownIcon className="h-4 w-4 mr-1" />
                      )}
                      <span className="text-sm">
                        {formatPercentage(Math.abs(analytics.overview.growthRate))} vs last period
                      </span>
                    </div>
                  </div>
                  <CurrencyDollarIcon className="h-12 w-12 opacity-50" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm opacity-90">Active Earners</p>
                    <p className="text-2xl font-bold">{formatNumber(analytics.overview.activeEarners)}</p>
                    <p className="text-sm opacity-75">
                      of {formatNumber(analytics.overview.totalUsers)} users
                    </p>
                  </div>
                  <UsersIcon className="h-12 w-12 opacity-50" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm opacity-90">Conversion Rate</p>
                    <p className="text-2xl font-bold">{formatPercentage(analytics.overview.conversionRate)}</p>
                    <p className="text-sm opacity-75">
                      Users to earners
                    </p>
                  </div>
                  <TrophyIcon className="h-12 w-12 opacity-50" />
                </div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>
                    Key performance indicators for {getTimeRangeLabel(timeRange)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">Avg Earning Per User</span>
                      <span className="text-lg font-bold text-gray-900">
                        {formatCurrency(analytics.metrics.averageEarningPerUser)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">Avg Referral Reward</span>
                      <span className="text-lg font-bold text-gray-900">
                        {formatCurrency(analytics.metrics.averageReferralReward)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">Avg Blog Earning</span>
                      <span className="text-lg font-bold text-gray-900">
                        {formatCurrency(analytics.metrics.averageBlogEarning)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">Payout Success Rate</span>
                      <span className="text-lg font-bold text-gray-900">
                        {formatPercentage(analytics.metrics.payoutSuccessRate)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">User Retention Rate</span>
                      <span className="text-lg font-bold text-gray-900">
                        {formatPercentage(analytics.metrics.userRetentionRate)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">Referral Conversion</span>
                      <span className="text-lg font-bold text-gray-900">
                        {formatPercentage(analytics.metrics.referralConversionRate)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Performers</CardTitle>
                  <CardDescription>
                    Highest earning users in {getTimeRangeLabel(timeRange)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.performance.topEarners.slice(0, 5).map((earner, index) => (
                      <div key={earner.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                            <span className="text-sm font-medium">#{index + 1}</span>
                          </div>
                          <div>
                            <p className="text-sm font-medium">{earner.name}</p>
                            <p className="text-xs text-gray-500">@{earner.username}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold">{formatCurrency(earner.totalEarnings)}</p>
                          <p className="text-xs text-gray-500">{earner.earningType}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts Placeholder */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Earnings Trend</CardTitle>
                  <CardDescription>
                    Daily earnings over {getTimeRangeLabel(timeRange)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <ChartBarIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Chart Coming Soon
                    </h3>
                    <p className="text-gray-500">
                      Interactive earnings trend chart will be displayed here.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>User Growth</CardTitle>
                  <CardDescription>
                    New and active users over {getTimeRangeLabel(timeRange)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <UsersIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Chart Coming Soon
                    </h3>
                    <p className="text-gray-500">
                      User growth trend chart will be displayed here.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Referrers and Blogs */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Top Referrers</CardTitle>
                  <CardDescription>
                    Most successful referrers in {getTimeRangeLabel(timeRange)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.performance.topReferrers.map((referrer, index) => (
                      <div key={referrer.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <UserGroupIcon className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">{referrer.name}</p>
                            <p className="text-xs text-gray-500">@{referrer.username}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold">{referrer.referrals} referrals</p>
                          <p className="text-xs text-gray-500">{formatCurrency(referrer.earnings)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Earning Blogs</CardTitle>
                  <CardDescription>
                    Highest earning blog posts in {getTimeRangeLabel(timeRange)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.performance.topBlogs.map((blog, index) => (
                      <div key={blog.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <StarIcon className="h-4 w-4 text-green-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{blog.title}</p>
                            <p className="text-xs text-gray-500">by {blog.author}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold">{formatNumber(blog.reads)} reads</p>
                          <p className="text-xs text-gray-500">{formatCurrency(blog.earnings)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
