"use client";

import React, { useState } from 'react';
import {
  XMarkIcon,
  PlusIcon,
  MinusIcon,
  TableCellsIcon
} from '@heroicons/react/24/outline';

interface TableEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (tableMarkdown: string) => void;
}

interface TableCell {
  content: string;
}

interface TableRow {
  cells: TableCell[];
}

export const TableEditorModal: React.FC<TableEditorModalProps> = ({
  isOpen,
  onClose,
  onInsert
}) => {
  const [rows, setRows] = useState<TableRow[]>([
    { cells: [{ content: 'Header 1' }, { content: 'Header 2' }, { content: 'Header 3' }] },
    { cells: [{ content: 'Cell 1' }, { content: 'Cell 2' }, { content: 'Cell 3' }] },
    { cells: [{ content: 'Cell 4' }, { content: 'Cell 5' }, { content: 'Cell 6' }] }
  ]);
  const [hasHeader, setHasHeader] = useState(true);
  const [alignment, setAlignment] = useState<'left' | 'center' | 'right'>('left');

  const addRow = () => {
    const newRow: TableRow = {
      cells: Array(rows[0]?.cells.length || 3).fill(null).map(() => ({ content: '' }))
    };
    setRows([...rows, newRow]);
  };

  const removeRow = (index: number) => {
    if (rows.length > 1) {
      setRows(rows.filter((_, i) => i !== index));
    }
  };

  const addColumn = () => {
    setRows(rows.map(row => ({
      ...row,
      cells: [...row.cells, { content: '' }]
    })));
  };

  const removeColumn = (columnIndex: number) => {
    if (rows[0]?.cells.length > 1) {
      setRows(rows.map(row => ({
        ...row,
        cells: row.cells.filter((_, i) => i !== columnIndex)
      })));
    }
  };

  const updateCell = (rowIndex: number, cellIndex: number, content: string) => {
    setRows(rows.map((row, rIndex) => 
      rIndex === rowIndex 
        ? {
            ...row,
            cells: row.cells.map((cell, cIndex) => 
              cIndex === cellIndex ? { content } : cell
            )
          }
        : row
    ));
  };

  const generateTableMarkdown = () => {
    if (rows.length === 0) return '';

    const alignmentChar = alignment === 'center' ? ':---:' : alignment === 'right' ? '---:' : '---';
    
    let markdown = '';
    
    // Add rows
    rows.forEach((row, rowIndex) => {
      const rowContent = row.cells.map(cell => cell.content || ' ').join(' | ');
      markdown += `| ${rowContent} |\n`;
      
      // Add separator after header row
      if (rowIndex === 0 && hasHeader) {
        const separator = row.cells.map(() => alignmentChar).join(' | ');
        markdown += `| ${separator} |\n`;
      }
    });

    return markdown;
  };

  const handleInsert = () => {
    const tableMarkdown = generateTableMarkdown();
    onInsert(tableMarkdown);
    onClose();
  };

  const previewMarkdown = generateTableMarkdown();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <TableCellsIcon className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Table Editor</h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Editor Panel */}
          <div className="flex-1 p-4 overflow-y-auto border-r border-gray-200">
            {/* Controls */}
            <div className="mb-4 space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-700">Table Structure</h4>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={addRow}
                    className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                  >
                    <PlusIcon className="h-3 w-3" />
                    <span>Row</span>
                  </button>
                  <button
                    onClick={addColumn}
                    className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                  >
                    <PlusIcon className="h-3 w-3" />
                    <span>Column</span>
                  </button>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={hasHeader}
                    onChange={(e) => setHasHeader(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm text-gray-700">First row as header</span>
                </label>

                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">Alignment:</span>
                  <select
                    value={alignment}
                    onChange={(e) => setAlignment(e.target.value as 'left' | 'center' | 'right')}
                    className="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Table Editor */}
            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <table className="w-full">
                <tbody>
                  {rows.map((row, rowIndex) => (
                    <tr key={rowIndex} className={hasHeader && rowIndex === 0 ? 'bg-gray-50' : ''}>
                      {row.cells.map((cell, cellIndex) => (
                        <td key={cellIndex} className="border border-gray-200 p-0 relative group">
                          <input
                            type="text"
                            value={cell.content}
                            onChange={(e) => updateCell(rowIndex, cellIndex, e.target.value)}
                            className="w-full px-3 py-2 border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                            placeholder={hasHeader && rowIndex === 0 ? `Header ${cellIndex + 1}` : `Cell ${rowIndex + 1}-${cellIndex + 1}`}
                          />
                          {/* Column controls */}
                          {rowIndex === 0 && (
                            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                              <button
                                onClick={() => removeColumn(cellIndex)}
                                className="p-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
                                disabled={rows[0]?.cells.length <= 1}
                              >
                                <MinusIcon className="h-3 w-3" />
                              </button>
                            </div>
                          )}
                        </td>
                      ))}
                      {/* Row controls */}
                      <td className="w-8 border-0 p-0">
                        <div className="flex items-center justify-center h-full">
                          <button
                            onClick={() => removeRow(rowIndex)}
                            className="p-1 bg-red-500 text-white rounded text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                            disabled={rows.length <= 1}
                          >
                            <MinusIcon className="h-3 w-3" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Preview Panel */}
          <div className="w-1/3 p-4 bg-gray-50">
            <h4 className="font-medium text-gray-700 mb-3">Markdown Preview</h4>
            <div className="bg-white border border-gray-200 rounded p-3 mb-4">
              <pre className="text-xs text-gray-600 whitespace-pre-wrap font-mono">
                {previewMarkdown}
              </pre>
            </div>
            
            <h4 className="font-medium text-gray-700 mb-3">Rendered Preview</h4>
            <div className="bg-white border border-gray-200 rounded p-3 overflow-auto">
              <div 
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: previewMarkdown
                    .split('\n')
                    .map(line => {
                      if (line.startsWith('|') && line.endsWith('|')) {
                        const cells = line.slice(1, -1).split('|').map(cell => cell.trim());
                        if (cells.every(cell => cell.match(/^:?---:?$/))) {
                          return ''; // Skip separator row
                        }
                        return `<tr>${cells.map(cell => `<td class="border border-gray-300 px-2 py-1">${cell}</td>`).join('')}</tr>`;
                      }
                      return '';
                    })
                    .filter(Boolean)
                    .join('')
                    .replace(/^/, '<table class="border-collapse border border-gray-300">')
                    .replace(/$/, '</table>')
                }}
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={handleInsert}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Insert Table
          </button>
        </div>
      </div>
    </div>
  );
};

export default TableEditorModal;
