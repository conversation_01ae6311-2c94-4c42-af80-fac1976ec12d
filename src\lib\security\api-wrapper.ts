import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import Jo<PERSON> from 'joi';
import { validateRequestBody, sanitizeInput, detectSQLInjection, detectXSS } from './validation';

interface ApiHandlerOptions {
  requireAuth?: boolean;
  requireAdmin?: boolean;
  validationSchema?: Joi.ObjectSchema;
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
  allowedMethods?: string[];
  skipCSRF?: boolean;
}

interface ApiContext {
  user?: any;
  isAdmin?: boolean;
  validatedData?: any;
  request: NextRequest;
}

type ApiHandler = (context: ApiContext) => Promise<NextResponse>;

/**
 * Secure API route wrapper with comprehensive security features
 */
export function withSecurity(
  handler: ApiHandler,
  options: ApiHandlerOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const {
        requireAuth = false,
        requireAdmin = false,
        validationSchema,
        allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        skipCSRF = false,
      } = options;

      // Check allowed methods
      if (!allowedMethods.includes(request.method)) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Method not allowed',
            allowedMethods 
          },
          { 
            status: 405,
            headers: { 'Allow': allowedMethods.join(', ') }
          }
        );
      }

      // Get session if auth is required
      let session = null;
      let user = null;
      let isAdmin = false;

      if (requireAuth || requireAdmin) {
        session = await getServerSession(authOptions);
        
        if (!session?.user?.id) {
          return NextResponse.json(
            { 
              success: false,
              error: 'Authentication required' 
            },
            { status: 401 }
          );
        }

        user = session.user;
        isAdmin = user.isAdmin === true;

        // Check admin requirement
        if (requireAdmin && !isAdmin) {
          return NextResponse.json(
            { 
              success: false,
              error: 'Admin access required' 
            },
            { status: 403 }
          );
        }

        // Check user status
        if (user.status !== 'active' || !user.isActive) {
          return NextResponse.json(
            { 
              success: false,
              error: 'Account disabled or suspended',
              code: 'ACCOUNT_DISABLED'
            },
            { status: 401 }
          );
        }
      }

      // Validate and sanitize request body
      let validatedData = null;
      if (validationSchema && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json();
          
          // Check for SQL injection and XSS
          const bodyString = JSON.stringify(body);
          if (detectSQLInjection(bodyString)) {
            return NextResponse.json(
              { 
                success: false,
                error: 'SQL injection attempt detected' 
              },
              { status: 400 }
            );
          }

          if (detectXSS(bodyString)) {
            return NextResponse.json(
              { 
                success: false,
                error: 'XSS attempt detected' 
              },
              { status: 400 }
            );
          }

          const validation = validateRequestBody(body, validationSchema);
          if (!validation.isValid) {
            return NextResponse.json(
              { 
                success: false,
                error: 'Validation failed',
                details: validation.errors 
              },
              { status: 400 }
            );
          }

          validatedData = validation.data;
        } catch (error) {
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid JSON in request body' 
            },
            { status: 400 }
          );
        }
      }

      // CSRF protection for state-changing requests
      if (!skipCSRF && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
        const origin = request.headers.get('origin');
        const host = request.headers.get('host');
        
        if (origin && !origin.includes(host || '')) {
          return NextResponse.json(
            { 
              success: false,
              error: 'CSRF protection: Invalid origin' 
            },
            { status: 403 }
          );
        }
      }

      // Create context for handler
      const context: ApiContext = {
        user,
        isAdmin,
        validatedData,
        request,
      };

      // Call the actual handler
      const response = await handler(context);

      // Add security headers to response
      const securityHeaders = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'origin-when-cross-origin',
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      };

      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;

    } catch (error) {
      console.error('API Security Wrapper Error:', error);
      
      return NextResponse.json(
        { 
          success: false,
          error: 'Internal server error',
          ...(process.env.NODE_ENV === 'development' && {
            details: error instanceof Error ? error.message : 'Unknown error'
          })
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Create a secure API handler with validation
 */
export function createSecureHandler(
  handler: ApiHandler,
  options: ApiHandlerOptions = {}
) {
  return {
    GET: options.allowedMethods?.includes('GET') ? withSecurity(handler, options) : undefined,
    POST: options.allowedMethods?.includes('POST') ? withSecurity(handler, options) : undefined,
    PUT: options.allowedMethods?.includes('PUT') ? withSecurity(handler, options) : undefined,
    DELETE: options.allowedMethods?.includes('DELETE') ? withSecurity(handler, options) : undefined,
    PATCH: options.allowedMethods?.includes('PATCH') ? withSecurity(handler, options) : undefined,
  };
}

/**
 * Middleware for input sanitization
 */
export function sanitizeApiInput(data: any): any {
  return sanitizeInput(data);
}

/**
 * Rate limiting for API routes
 */
const apiRateLimits = new Map<string, { count: number; resetTime: number }>();

export function checkApiRateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000
): { allowed: boolean; retryAfter?: number } {
  const now = Date.now();
  const key = identifier;
  
  let entry = apiRateLimits.get(key);
  
  if (!entry || entry.resetTime <= now) {
    entry = { count: 0, resetTime: now + windowMs };
    apiRateLimits.set(key, entry);
  }
  
  entry.count++;
  
  if (entry.count > maxRequests) {
    return {
      allowed: false,
      retryAfter: Math.ceil((entry.resetTime - now) / 1000),
    };
  }
  
  return { allowed: true };
}

/**
 * Security audit logging
 */
export function logSecurityEvent(
  event: string,
  details: any,
  severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    severity,
    details,
    userAgent: details.userAgent || 'unknown',
    ip: details.ip || 'unknown',
  };
  
  // In production, send to security monitoring service
  console.warn('Security Event:', logEntry);
  
  // Store in database for audit trail
  // await db.insert(securityLogs).values(logEntry);
}

/**
 * Check for suspicious activity patterns
 */
export function detectSuspiciousActivity(
  userId: string,
  action: string,
  metadata: any = {}
): boolean {
  // Implement pattern detection logic
  // - Multiple failed login attempts
  // - Unusual API usage patterns
  // - Rapid successive requests
  // - Access from unusual locations
  
  const suspiciousPatterns = [
    // Multiple failed attempts
    metadata.failedAttempts > 5,
    // Rapid requests
    metadata.requestsPerMinute > 60,
    // Unusual user agent
    metadata.userAgent?.includes('bot') || metadata.userAgent?.includes('crawler'),
  ];
  
  return suspiciousPatterns.some(pattern => pattern);
}

/**
 * Generate CSRF token
 */
export function generateCSRFToken(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(token: string, sessionToken: string): boolean {
  // In production, implement proper CSRF token validation
  // This is a simplified version
  return token && token.length > 10;
}
