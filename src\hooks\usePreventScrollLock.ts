"use client";

import { useEffect } from 'react';

/**
 * Hook to prevent HeadlessUI from locking body scroll when dropdowns are opened
 * This should be used in Menu components but NOT in Dialog components
 */
export function usePreventScrollLock(isOpen: boolean) {
  useEffect(() => {
    if (!isOpen) return;

    // Store original styles
    const originalHtmlOverflow = document.documentElement.style.overflow;
    const originalHtmlPaddingRight = document.documentElement.style.paddingRight;
    const originalBodyOverflow = document.body.style.overflow;

    // Function to restore scroll
    const restoreScroll = () => {
      // Only restore if HeadlessUI has set overflow to hidden
      if (document.documentElement.style.overflow === 'hidden') {
        document.documentElement.style.overflow = originalHtmlOverflow || 'auto';
        document.documentElement.style.paddingRight = originalHtmlPaddingRight || '';
      }
      if (document.body.style.overflow === 'hidden') {
        document.body.style.overflow = originalBodyOverflow || '';
      }
    };

    // Use MutationObserver to watch for HeadlessUI changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'style' || mutation.attributeName === 'data-headlessui-state')) {
          // Small delay to let HeadlessUI finish its changes
          setTimeout(restoreScroll, 0);
        }
      });
    });

    // Observe changes to html and body elements
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style', 'data-headlessui-state']
    });
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['style']
    });

    // Initial restore in case HeadlessUI has already made changes
    setTimeout(restoreScroll, 0);

    return () => {
      observer.disconnect();
    };
  }, [isOpen]);
}
