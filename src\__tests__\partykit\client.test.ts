/**
 * @jest-environment jsdom
 */

import { PartyKitClient, PartyKitManager } from '@/lib/partykit/client';

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState = MockWebSocket.CONNECTING;
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(public url: string, public protocols?: string | string[]) {
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      this.onopen?.(new Event('open'));
    }, 10);
  }

  send(data: string) {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    // Echo back for testing
    setTimeout(() => {
      this.onmessage?.(new MessageEvent('message', { data }));
    }, 5);
  }

  close() {
    this.readyState = MockWebSocket.CLOSED;
    this.onclose?.(new CloseEvent('close'));
  }
}

// Mock PartySocket
jest.mock('partysocket', () => {
  return jest.fn().mockImplementation((config) => {
    return new MockWebSocket(`ws://${config.host}/parties/${config.party}/${config.room}`);
  });
});

// Mock next-auth
jest.mock('next-auth/react', () => ({
  getSession: jest.fn().mockResolvedValue({
    user: { id: 'test-user-id', name: 'Test User' },
    accessToken: 'mock-token'
  })
}));

describe('PartyKitClient', () => {
  let client: PartyKitClient;

  beforeEach(() => {
    client = new PartyKitClient({
      host: 'localhost:1999',
      party: 'test',
      room: 'test-room',
      enableFallback: true
    });
  });

  afterEach(() => {
    client.disconnect();
  });

  describe('Connection Management', () => {
    test('should connect successfully', async () => {
      const connected = await client.connect();
      expect(connected).toBe(true);
      expect(client.isConnected()).toBe(true);
    });

    test('should handle connection timeout', async () => {
      // Mock a slow connection
      jest.spyOn(global, 'setTimeout').mockImplementation((callback, delay) => {
        if (delay === 10000) {
          // Don't call the timeout callback
          return 123 as any;
        }
        return setTimeout(callback as any, delay);
      });

      await expect(client.connect()).rejects.toThrow('Connection timeout');
    });

    test('should reconnect after disconnection', async () => {
      await client.connect();
      expect(client.isConnected()).toBe(true);

      // Simulate disconnection
      const socket = (client as any).socket;
      socket.readyState = MockWebSocket.CLOSED;
      socket.onclose(new CloseEvent('close'));

      // Wait for reconnection attempt
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(client.getStatus().reconnectAttempts).toBeGreaterThan(0);
    });
  });

  describe('Message Handling', () => {
    beforeEach(async () => {
      await client.connect();
    });

    test('should send messages successfully', () => {
      const message = { type: 'test', content: 'Hello World' };
      const result = client.send(message);
      expect(result).toBe(true);
    });

    test('should queue messages when disconnected', () => {
      client.disconnect();
      
      const message = { type: 'test', content: 'Queued message' };
      const result = client.send(message);
      expect(result).toBe(false);
    });

    test('should emit events for received messages', (done) => {
      const testMessage = { type: 'test_response', data: 'test data' };
      
      client.on('test_response', (data) => {
        expect(data.data).toBe('test data');
        done();
      });

      // Simulate receiving a message
      const socket = (client as any).socket;
      socket.onmessage(new MessageEvent('message', { 
        data: JSON.stringify(testMessage) 
      }));
    });
  });

  describe('Latency Tracking', () => {
    beforeEach(async () => {
      await client.connect();
    });

    test('should track ping latency', (done) => {
      client.on('latency_update', (latency) => {
        expect(typeof latency).toBe('number');
        expect(latency).toBeGreaterThanOrEqual(0);
        done();
      });

      // Trigger ping
      client.send({ type: 'ping', timestamp: Date.now() });
      
      // Simulate pong response
      setTimeout(() => {
        const socket = (client as any).socket;
        socket.onmessage(new MessageEvent('message', { 
          data: JSON.stringify({ type: 'pong' }) 
        }));
      }, 10);
    });
  });

  describe('Error Handling', () => {
    test('should handle authentication errors', (done) => {
      client.on('auth_error', (error) => {
        expect(error.message).toBe('Invalid token');
        done();
      });

      // Simulate auth error response
      const socket = (client as any).socket;
      socket.onmessage(new MessageEvent('message', { 
        data: JSON.stringify({ 
          type: 'auth_error', 
          message: 'Invalid token' 
        }) 
      }));
    });

    test('should handle connection errors', (done) => {
      client.on('error', (error) => {
        expect(error).toBeDefined();
        done();
      });

      // Simulate connection error
      const socket = (client as any).socket;
      socket.onerror(new Event('error'));
    });
  });
});

describe('PartyKitManager', () => {
  afterEach(() => {
    PartyKitManager.disconnectAll();
  });

  test('should create and reuse client instances', () => {
    const client1 = PartyKitManager.getClient('chat', 'room1');
    const client2 = PartyKitManager.getClient('chat', 'room1');
    const client3 = PartyKitManager.getClient('chat', 'room2');

    expect(client1).toBe(client2); // Same instance for same room
    expect(client1).not.toBe(client3); // Different instance for different room
  });

  test('should disconnect all clients', () => {
    const client1 = PartyKitManager.getClient('chat', 'room1');
    const client2 = PartyKitManager.getClient('notifications', 'room2');

    const disconnectSpy1 = jest.spyOn(client1, 'disconnect');
    const disconnectSpy2 = jest.spyOn(client2, 'disconnect');

    PartyKitManager.disconnectAll();

    expect(disconnectSpy1).toHaveBeenCalled();
    expect(disconnectSpy2).toHaveBeenCalled();
  });

  test('should provide optimization stats', () => {
    const stats = PartyKitManager.getOptimizationStats();
    
    expect(stats).toHaveProperty('timestamp');
    expect(stats).toHaveProperty('connectionPool');
    expect(stats).toHaveProperty('roomOptimizer');
    expect(stats).toHaveProperty('messageBatcher');
    expect(stats).toHaveProperty('recommendations');
  });
});

describe('Performance Requirements', () => {
  let client: PartyKitClient;
  let startTime: number;

  beforeEach(async () => {
    client = new PartyKitClient({
      host: 'localhost:1999',
      party: 'test',
      room: 'performance-test',
      enableFallback: true
    });
    await client.connect();
  });

  afterEach(() => {
    client.disconnect();
  });

  test('should achieve sub-100ms message latency', (done) => {
    startTime = Date.now();
    
    client.on('message', () => {
      const latency = Date.now() - startTime;
      expect(latency).toBeLessThan(100); // Sub-100ms requirement
      done();
    });

    client.send({ type: 'latency_test', timestamp: startTime });
  });

  test('should handle connection within 5 seconds', async () => {
    const connectionStart = Date.now();
    const connected = await client.connect();
    const connectionTime = Date.now() - connectionStart;
    
    expect(connected).toBe(true);
    expect(connectionTime).toBeLessThan(5000); // 5 second requirement
  });

  test('should maintain connection stability', async () => {
    let disconnectionCount = 0;
    
    client.on('disconnected', () => {
      disconnectionCount++;
    });

    // Simulate network instability
    for (let i = 0; i < 5; i++) {
      await new Promise(resolve => setTimeout(resolve, 100));
      // Force reconnection
      const socket = (client as any).socket;
      socket.readyState = MockWebSocket.CLOSED;
      socket.onclose(new CloseEvent('close'));
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Should handle reconnections gracefully
    expect(disconnectionCount).toBeLessThanOrEqual(5);
    expect(client.getStatus().reconnectAttempts).toBeGreaterThan(0);
  });
});

describe('Fallback Mechanisms', () => {
  test('should fallback to polling when WebSocket fails', async () => {
    // Mock WebSocket to always fail
    jest.spyOn(global, 'WebSocket').mockImplementation(() => {
      throw new Error('WebSocket not supported');
    });

    const client = new PartyKitClient({
      host: 'localhost:1999',
      party: 'test',
      room: 'fallback-test',
      enableFallback: true
    });

    let fallbackTriggered = false;
    client.on('max_reconnect_attempts', () => {
      fallbackTriggered = true;
    });

    try {
      await client.connect();
    } catch (error) {
      // Connection should fail but fallback should be triggered
    }

    expect(fallbackTriggered).toBe(true);
  });

  test('should queue messages during connection issues', () => {
    const client = new PartyKitClient({
      host: 'localhost:1999',
      party: 'test',
      room: 'queue-test',
      enableFallback: true
    });

    // Send messages while disconnected
    const message1 = { type: 'test1', content: 'Message 1' };
    const message2 = { type: 'test2', content: 'Message 2' };
    
    const result1 = client.send(message1);
    const result2 = client.send(message2);
    
    expect(result1).toBe(false); // Should be queued
    expect(result2).toBe(false); // Should be queued
    
    // Check that messages are queued
    const messageQueue = (client as any).messageQueue;
    expect(messageQueue).toHaveLength(2);
    expect(messageQueue[0]).toEqual(message1);
    expect(messageQueue[1]).toEqual(message2);
  });
});
