import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { ViewTrackingService } from "@/lib/analytics/viewTrackingService";

// Get blog analytics
export async function GET(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;
    const session = await getServerSession(authOptions);

    // Get blog
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: {
        id: true,
        title: true,
        authorId: true,
        status: true,
        viewCount: true,
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check permissions - only author or admin can view detailed analytics
    const canViewAnalytics = session?.user && (
      session.user.id === blog.authorId || 
      session.user.isAdmin
    );

    if (!canViewAnalytics) {
      // Return basic analytics for public
      return NextResponse.json({
        totalViews: blog.viewCount || 0,
        isPublic: true,
      });
    }

    // Get comprehensive analytics for authorized users
    const analytics = await ViewTrackingService.getBlogAnalytics(blog.id);

    return NextResponse.json({
      ...analytics,
      blogTitle: blog.title,
      isPublic: false,
    });

  } catch (error) {
    console.error("Error fetching blog analytics:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
