"use client";

import {
  UsersIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from "@heroicons/react/24/outline";

interface DashboardStatsProps {
  stats: {
    overview: {
      totalFollowers: number;
      totalPosts: number;
      totalMessages: number;
      unreadMessages: number;
      totalLikes: number;
      totalComments: number;
      engagementRate: number;
    };
    growth: {
      newFollowersThisMonth: number;
      newFollowersLastMonth: number;
      followerGrowthRate: number;
      postsThisMonth: number;
    };
    engagement: {
      likesThisWeek: number;
      commentsThisWeek: number;
      totalEngagement: number;
    };
  };
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  const statCards = [
    {
      title: "Total Followers",
      value: stats.overview.totalFollowers.toLocaleString(),
      icon: <UsersIcon className="h-6 w-6" />,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      change: {
        value: stats.growth.followerGrowthRate,
        isPositive: stats.growth.followerGrowthRate >= 0,
        label: "vs last month"
      },
    },
    {
      title: "Total Posts",
      value: stats.overview.totalPosts.toLocaleString(),
      icon: <DocumentTextIcon className="h-6 w-6" />,
      color: "text-green-600",
      bgColor: "bg-green-100",
      change: {
        value: stats.growth.postsThisMonth,
        isPositive: true,
        label: "this month"
      },
    },
    {
      title: "Messages",
      value: stats.overview.totalMessages.toLocaleString(),
      icon: <ChatBubbleLeftRightIcon className="h-6 w-6" />,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      badge: stats.overview.unreadMessages > 0 ? {
        value: stats.overview.unreadMessages,
        label: "unread"
      } : null,
    },
    {
      title: "Total Likes",
      value: stats.overview.totalLikes.toLocaleString(),
      icon: <HeartIcon className="h-6 w-6" />,
      color: "text-red-600",
      bgColor: "bg-red-100",
      change: {
        value: stats.engagement.likesThisWeek,
        isPositive: true,
        label: "this week"
      },
    },
    {
      title: "Total Comments",
      value: stats.overview.totalComments.toLocaleString(),
      icon: <ChatBubbleOvalLeftIcon className="h-6 w-6" />,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
      change: {
        value: stats.engagement.commentsThisWeek,
        isPositive: true,
        label: "this week"
      },
    },
    {
      title: "Engagement Rate",
      value: `${stats.overview.engagementRate}%`,
      icon: <ArrowTrendingUpIcon className="h-6 w-6" />,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
      change: {
        value: stats.engagement.totalEngagement,
        isPositive: true,
        label: "total engagement"
      },
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {statCards.map((card, index) => (
        <div
          key={index}
          className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`rounded-lg p-3 ${card.bgColor}`}>
                <div className={card.color}>
                  {card.icon}
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <div className="flex items-center space-x-2">
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                  {card.badge && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      {card.badge.value} {card.badge.label}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {card.change && (
            <div className="mt-4 flex items-center">
              <div className={`flex items-center ${
                card.change.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {card.change.isPositive ? (
                  <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowTrendingDownIcon className="h-4 w-4 mr-1" />
                )}
                <span className="text-sm font-medium">
                  {card.change.isPositive && card.change.value > 0 ? '+' : ''}
                  {typeof card.change.value === 'number' && card.change.value % 1 !== 0
                    ? card.change.value.toFixed(1)
                    : card.change.value}
                  {card.title === "Engagement Rate" ? '' : ''}
                </span>
              </div>
              <span className="ml-2 text-sm text-gray-500">{card.change.label}</span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
