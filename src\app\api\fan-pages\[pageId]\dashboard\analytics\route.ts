import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  fanPages,
  fanPageFollowers,
  fanPagePosts,
  fanPagePostLikes,
  fanPagePostComments,
  fanPageRoles
} from "@/lib/db/schema";
import { eq, and, sql, count } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId]/dashboard/analytics - Get analytics data
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;
    const { searchParams } = new URL(request.url);
    const range = searchParams.get("range") || "30d"; // 7d, 30d, 90d

    // Verify user owns this fan page or has admin role
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, pageId),
        eq(fanPages.isActive, true)
      ),
    });

    if (!page) {
      return NextResponse.json({ error: "Fan page not found" }, { status: 404 });
    }

    const isOwner = page.ownerId === session.user.id;
    let hasAdminRole = false;

    if (!isOwner) {
      const userRole = await db.query.fanPageRoles.findFirst({
        where: and(
          eq(fanPageRoles.fanPageId, pageId),
          eq(fanPageRoles.userId, session.user.id),
          eq(fanPageRoles.role, 'admin')
        ),
      });
      hasAdminRole = !!userRole;
    }

    if (!isOwner && !hasAdminRole) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    let days = 30;

    switch (range) {
      case "7d":
        startDate.setDate(now.getDate() - 7);
        days = 7;
        break;
      case "30d":
        startDate.setDate(now.getDate() - 30);
        days = 30;
        break;
      case "90d":
        startDate.setDate(now.getDate() - 90);
        days = 90;
        break;
      default:
        startDate.setDate(now.getDate() - 30);
        days = 30;
    }

    // Generate date array for the range
    const dateArray = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(now.getDate() - i);
      dateArray.push({
        date: date.toISOString().split('T')[0],
        displayDate: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      });
    }

    // Get follower growth data
    const followerGrowthData = await Promise.all(
      dateArray.map(async ({ date }) => {
        const nextDate = new Date(date);
        nextDate.setDate(nextDate.getDate() + 1);

        const followersResult = await db
          .select({ count: count() })
          .from(fanPageFollowers)
          .where(and(
            eq(fanPageFollowers.fanPageId, pageId),
            sql`DATE(${fanPageFollowers.createdAt}) = ${date}`
          ));

        return {
          date,
          newFollowers: followersResult[0]?.count || 0,
        };
      })
    );

    // Calculate cumulative followers
    let cumulativeFollowers = 0;
    const followersBeforeRange = await db
      .select({ count: count() })
      .from(fanPageFollowers)
      .where(and(
        eq(fanPageFollowers.fanPageId, pageId),
        sql`${fanPageFollowers.createdAt} < ${startDate.toISOString()}`
      ));

    cumulativeFollowers = followersBeforeRange[0]?.count || 0;

    const followerGrowth = followerGrowthData.map(item => {
      cumulativeFollowers += item.newFollowers;
      return {
        ...item,
        totalFollowers: cumulativeFollowers,
      };
    });

    // Get post engagement data
    const postEngagementData = await Promise.all(
      dateArray.map(async ({ date }) => {
        const nextDate = new Date(date);
        nextDate.setDate(nextDate.getDate() + 1);

        // Posts created on this date
        const postsResult = await db
          .select({ count: count() })
          .from(fanPagePosts)
          .where(and(
            eq(fanPagePosts.fanPageId, pageId),
            sql`DATE(${fanPagePosts.createdAt}) = ${date}`
          ));

        // Likes on posts created on this date
        const likesResult = await db
          .select({ count: count() })
          .from(fanPagePostLikes)
          .innerJoin(fanPagePosts, eq(fanPagePostLikes.fanPagePostId, fanPagePosts.id))
          .where(and(
            eq(fanPagePosts.fanPageId, pageId),
            sql`DATE(${fanPagePostLikes.createdAt}) = ${date}`
          ));

        // Comments on posts created on this date
        const commentsResult = await db
          .select({ count: count() })
          .from(fanPagePostComments)
          .innerJoin(fanPagePosts, eq(fanPagePostComments.postId, fanPagePosts.id))
          .where(and(
            eq(fanPagePosts.fanPageId, pageId),
            sql`DATE(${fanPagePostComments.createdAt}) = ${date}`
          ));

        return {
          date,
          posts: postsResult[0]?.count || 0,
          likes: likesResult[0]?.count || 0,
          comments: commentsResult[0]?.count || 0,
          engagement: (likesResult[0]?.count || 0) + (commentsResult[0]?.count || 0),
        };
      })
    );

    // Get top performing posts
    const topPosts = await db
      .select({
        id: fanPagePosts.id,
        content: fanPagePosts.content,
        createdAt: fanPagePosts.createdAt,
        likesCount: sql<number>`(
          SELECT COUNT(*) FROM ${fanPagePostLikes}
          WHERE ${fanPagePostLikes.fanPagePostId} = ${fanPagePosts.id}
        )`,
        commentsCount: sql<number>`(
          SELECT COUNT(*) FROM ${fanPagePostComments}
          WHERE ${fanPagePostComments.fanPagePostId} = ${fanPagePosts.id}
        )`,
      })
      .from(fanPagePosts)
      .where(and(
        eq(fanPagePosts.fanPageId, pageId),
        sql`${fanPagePosts.createdAt} >= ${startDate.toISOString()}`
      ))
      .orderBy(sql`(
        (SELECT COUNT(*) FROM ${fanPagePostLikes} WHERE ${fanPagePostLikes.postId} = ${fanPagePosts.id}) +
        (SELECT COUNT(*) FROM ${fanPagePostComments} WHERE ${fanPagePostComments.postId} = ${fanPagePosts.id})
      ) DESC`)
      .limit(5);

    const analytics = {
      followerGrowth: followerGrowth.map(item => ({
        date: item.date,
        displayDate: dateArray.find(d => d.date === item.date)?.displayDate || item.date,
        newFollowers: item.newFollowers,
        totalFollowers: item.totalFollowers,
      })),
      postEngagement: postEngagementData.map(item => ({
        date: item.date,
        displayDate: dateArray.find(d => d.date === item.date)?.displayDate || item.date,
        posts: item.posts,
        likes: item.likes,
        comments: item.comments,
        engagement: item.engagement,
      })),
      topPosts: topPosts.map(post => ({
        id: post.id,
        content: post.content.substring(0, 100) + (post.content.length > 100 ? '...' : ''),
        createdAt: post.createdAt,
        likes: Number(post.likesCount) || 0,
        comments: Number(post.commentsCount) || 0,
        totalEngagement: (Number(post.likesCount) || 0) + (Number(post.commentsCount) || 0),
      })),
      summary: {
        totalNewFollowers: followerGrowthData.reduce((sum, item) => sum + item.newFollowers, 0),
        totalPosts: postEngagementData.reduce((sum, item) => sum + item.posts, 0),
        totalLikes: postEngagementData.reduce((sum, item) => sum + item.likes, 0),
        totalComments: postEngagementData.reduce((sum, item) => sum + item.comments, 0),
        totalEngagement: postEngagementData.reduce((sum, item) => sum + item.engagement, 0),
        averageEngagementPerPost: postEngagementData.reduce((sum, item) => sum + item.posts, 0) > 0
          ? Math.round((postEngagementData.reduce((sum, item) => sum + item.engagement, 0) / postEngagementData.reduce((sum, item) => sum + item.posts, 0)) * 10) / 10
          : 0,
      },
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error("Error fetching analytics data:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
