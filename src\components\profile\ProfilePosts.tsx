"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import dynamic from "next/dynamic";
import { PostCard } from "@/components/feed/PostCard";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import eventBus from "@/lib/eventBus";

// Dynamic import of ModernPostForm to prevent SSR issues
const ModernPostForm = dynamic(
  () => import("@/components/feed/ModernPostForm").then(mod => ({ default: mod.ModernPostForm })),
  {
    ssr: false,
    loading: () => (
      <div className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
        <div className="h-20 bg-gray-200 rounded-lg"></div>
      </div>
    )
  }
);

interface Post {
  id: string;
  content: string | null;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  type: 'user_post' | 'fan_page_post' | 'group_post';
  user: {
    id: string;
    name: string;
    image: string | null;
  } | null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  } | null;
  group: {
    id: string;
    name: string;
    slug: string;
    profileImage: string | null;
    isPrivate: boolean;
  } | null;
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
  sharedPost?: {
    id: string;
    content: string;
    images: string[] | null;
    videos: string[] | null;
    backgroundColor: string | null;
    feeling: string | null;
    activity: string | null;
    location: string | null;
    formattedContent: boolean | null;
    createdAt: string;
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  } | null;
}

interface ProfilePostsProps {
  userId: string;
  isOwnProfile?: boolean;
}

export function ProfilePosts({ userId, isOwnProfile = false }: ProfilePostsProps) {
  const { data: session } = useSession();
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Create a memoized fetchPosts function that can be reused
  const fetchPosts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch posts from the API
      const response = await fetch(`/api/users/${userId}/posts`);
      if (!response.ok) throw new Error("Failed to fetch posts");
      const data = await response.json();

      // Add type field to posts for proper edit/delete functionality
      const postsWithType = data.map((post: any) => ({
        ...post,
        type: 'user_post'
      }));

      setPosts(postsWithType);
    } catch (err) {
      console.error("Error fetching posts:", err);
      setError("Failed to load posts. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Initial fetch when component mounts
  useEffect(() => {
    if (session?.user) {
      fetchPosts();
    }
  }, [session, fetchPosts]);

  // Listen for post-created, post-updated, and post-deleted events
  useEffect(() => {
    // Handler for post-created event
    const handlePostCreated = () => {
      fetchPosts();
    };

    // Handler for post-updated event
    const handlePostUpdated = (postId: string) => {
      // Refresh all posts to get the updated content
      fetchPosts();
    };

    // Handler for post-deleted event
    const handlePostDeleted = (data: { postId: string } | string) => {
      // Handle both event formats for compatibility
      const postId = typeof data === 'string' ? data : data.postId;
      // Remove the deleted post from state
      setPosts((prevPosts) => prevPosts.filter((post) => post.id !== postId));
    };

    // Subscribe to events
    eventBus.on("post-created", handlePostCreated);
    eventBus.on("post-updated", handlePostUpdated);
    eventBus.on("post-deleted", handlePostDeleted);

    // Cleanup function to unsubscribe
    return () => {
      eventBus.off("post-created", handlePostCreated);
      eventBus.off("post-updated", handlePostUpdated);
      eventBus.off("post-deleted", handlePostDeleted);
    };
  }, [fetchPosts]);

  const handleLike = async (postId: string) => {
    try {
      // Call the API to like/unlike the post
      const response = await fetch(`/api/posts/${postId}/like`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to like post");
      }

      // Update the state optimistically
      setPosts((prevPosts) =>
        prevPosts.map((post) => {
          if (post.id === postId) {
            const newLiked = !post.liked;
            // If post was disliked and now being liked, remove the dislike
            const wasDisliked = post.disliked;
            return {
              ...post,
              liked: newLiked,
              disliked: wasDisliked ? false : post.disliked,
              _count: {
                ...post._count,
                likes: post._count.likes + (newLiked ? 1 : -1),
                dislikes: wasDisliked ? post._count.dislikes - 1 : post._count.dislikes,
              },
            };
          }
          return post;
        })
      );
    } catch (error) {
      console.error("Error liking post:", error);
      // Could add a toast notification here
    }
  };

  const handleDislike = async (postId: string) => {
    try {
      // Call the API to dislike/undislike the post
      const response = await fetch(`/api/posts/${postId}/dislike`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to dislike post");
      }

      // Update the state optimistically
      setPosts((prevPosts) =>
        prevPosts.map((post) => {
          if (post.id === postId) {
            const newDisliked = !post.disliked;
            // If post was liked and now being disliked, remove the like
            const wasLiked = post.liked;
            return {
              ...post,
              disliked: newDisliked,
              liked: wasLiked ? false : post.liked,
              _count: {
                ...post._count,
                dislikes: post._count.dislikes + (newDisliked ? 1 : -1),
                likes: wasLiked ? post._count.likes - 1 : post._count.likes,
              },
            };
          }
          return post;
        })
      );
    } catch (error) {
      console.error("Error disliking post:", error);
      // Could add a toast notification here
    }
  };

  if (!session?.user) {
    return (
      <div className="rounded-lg p-8 text-center shadow">
        <p className="text-gray-500">
          Please sign in to view posts.
        </p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-6 text-center">
        <p className="text-red-700">{error}</p>
        <button
          className="mt-4 rounded-md bg-red-100 px-4 py-2 text-sm font-medium text-red-700 hover:bg-red-200"
          onClick={() => fetchPosts()}
        >
          Try Again
        </button>
      </div>
    );
  }

  if (posts.length === 0) {
    return (
      <div className="space-y-6">
        {/* Post creation form - only show for own profile */}
        {isOwnProfile && (
          <ModernPostForm />
        )}

        <div className="rounded-xl border border-gray-200 p-8 text-center">
          <p className="text-sm text-gray-500">
            No posts yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Post creation form - only show for own profile */}
      {isOwnProfile && (
        <ModernPostForm />
      )}

      <div className="space-y-5">
        {posts.map((post) => (
          <div key={post.id} className="transform transition-all duration-300 hover:translate-y-[-2px]">
            <PostCard
              post={post}
              onLike={() => handleLike(post.id)}
              onDislike={() => handleDislike(post.id)}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
