"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Select } from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { toast } from "react-hot-toast";
import { ArrowLeftIcon, PlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { PlanBadge, BADGE_TYPE_OPTIONS, BADGE_COLOR_OPTIONS } from "@/components/ui/PlanBadge";

interface PlanFormData {
  name: string;
  displayName: string;
  description: string;
  price: string;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  // Custom billing cycle fields
  customBillingMonths: number;
  customBillingYears: number;
  features: string[];
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
  grantsVerification: boolean;
  // Badge system fields
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor: string;
  customBadgeUrl: string;
  badgePriority: number;
  isActive: boolean;
}

export default function NewSubscriptionPlanPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newFeature, setNewFeature] = useState("");
  const [formData, setFormData] = useState<PlanFormData>({
    name: "",
    displayName: "",
    description: "",
    price: "",
    currency: "USD",
    billingCycle: "monthly",
    // Custom billing cycle defaults
    customBillingMonths: 0,
    customBillingYears: 0,
    features: [],
    maxPosts: -1,
    maxStorage: -1,
    maxGroups: -1,
    canCreateFanPages: false,
    canCreateStores: false,
    canMonetizeBlogs: false,
    prioritySupport: false,
    grantsVerification: false,
    // Badge system defaults
    badgeType: 'none',
    badgeColor: '#3B82F6',
    customBadgeUrl: '',
    badgePriority: 0,
    isActive: true,
  });

  const handleInputChange = (field: keyof PlanFormData, value: any) => {
    console.log('Form field changed:', field, value); // Debug log
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature("");
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.displayName.trim() || !formData.price.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (!/^\d+(\.\d{1,2})?$/.test(formData.price)) {
      toast.error("Please enter a valid price (e.g., 9.99)");
      return;
    }

    try {
      setIsSubmitting(true);
      
      const response = await fetch('/api/admin/subscriptions/plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create subscription plan');
      }

      toast.success('Subscription plan created successfully');
      router.push('/admin/subscriptions/plans');
    } catch (error) {
      console.error('Error creating plan:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create subscription plan');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/subscriptions/plans">
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Back to Plans
            </Button>
          </Link>
        </div>
        <div className="mt-4 flex items-center space-x-3">
          <h1 className="text-2xl font-bold text-gray-900">Create Subscription Plan</h1>
          <PlanBadge
            badgeType={formData.badgeType}
            badgeColor={formData.badgeColor}
            customBadgeUrl={formData.customBadgeUrl || null}
            size="md"
          />
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Set up a new subscription plan with pricing and features
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Plan Name (Internal) *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., basic, premium, pro"
                required
              />
              <p className="mt-1 text-xs text-gray-500">
                Used internally for identification (lowercase, no spaces)
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Display Name *
              </label>
              <Input
                type="text"
                value={formData.displayName}
                onChange={(e) => handleInputChange('displayName', e.target.value)}
                placeholder="e.g., Basic Plan, Premium Plan"
                required
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe what this plan offers..."
              rows={3}
            />
          </div>
        </Card>

        {/* Pricing */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Pricing</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price *
              </label>
              <Input
                type="text"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="9.99"
                pattern="^\d+(\.\d{1,2})?$"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Currency
              </label>
              <Select
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="BDT">BDT</option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Billing Cycle
              </label>
              <Select
                value={formData.billingCycle}
                onChange={(e) => handleInputChange('billingCycle', e.target.value as 'monthly' | 'yearly')}
              >
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </Select>
            </div>
          </div>

          {/* Custom Billing Cycle Fields - Separate section */}
          <div className="mt-6 p-4 bg-blue-50 border-2 border-blue-200 rounded-lg">
            <div className="flex items-center mb-2">
              <span className="text-blue-600 font-bold mr-2">🆕</span>
              <label className="block text-sm font-medium text-gray-700">
                Custom Billing Period (Optional)
              </label>
            </div>
            <p className="text-xs text-gray-500 mb-3">
              Leave both fields as 0 to use the default billing cycle above. If you set custom values, they will override the default cycle.
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Custom Months (0-120)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="120"
                  value={formData.customBillingMonths}
                  onChange={(e) => handleInputChange('customBillingMonths', parseInt(e.target.value) || 0)}
                  placeholder="0"
                  className="border-blue-300 focus:border-blue-500"
                />
                <p className="text-xs text-gray-400 mt-1">Current: {formData.customBillingMonths}</p>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Custom Years (0-10)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="10"
                  value={formData.customBillingYears}
                  onChange={(e) => handleInputChange('customBillingYears', parseInt(e.target.value) || 0)}
                  placeholder="0"
                  className="border-blue-300 focus:border-blue-500"
                />
                <p className="text-xs text-gray-400 mt-1">Current: {formData.customBillingYears}</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Features */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Features</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Add Feature
              </label>
              <div className="flex space-x-2">
                <Input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Enter a feature..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                />
                <Button type="button" onClick={addFeature} variant="outline">
                  <PlusIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {formData.features.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Current Features
                </label>
                <div className="space-y-2">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm">{feature}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFeature(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Limits */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Limits</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Posts
              </label>
              <Input
                type="number"
                value={formData.maxPosts}
                onChange={(e) => handleInputChange('maxPosts', isNaN(parseInt(e.target.value)) ? -1 : parseInt(e.target.value))}
                placeholder="-1 for unlimited"
              />
              <p className="mt-1 text-xs text-gray-500">-1 for unlimited</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Storage (MB)
              </label>
              <Input
                type="number"
                value={formData.maxStorage}
                onChange={(e) => handleInputChange('maxStorage', isNaN(parseInt(e.target.value)) ? -1 : parseInt(e.target.value))}
                placeholder="-1 for unlimited"
              />
              <p className="mt-1 text-xs text-gray-500">-1 for unlimited</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Groups
              </label>
              <Input
                type="number"
                value={formData.maxGroups}
                onChange={(e) => handleInputChange('maxGroups', isNaN(parseInt(e.target.value)) ? -1 : parseInt(e.target.value))}
                placeholder="-1 for unlimited"
              />
              <p className="mt-1 text-xs text-gray-500">-1 for unlimited</p>
            </div>
          </div>
        </Card>

        {/* Permissions */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Permissions</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <Checkbox
              checked={formData.canCreateFanPages}
              onChange={(checked) => handleInputChange('canCreateFanPages', checked)}
              label="Can Create Fan Pages"
            />
            <Checkbox
              checked={formData.canCreateStores}
              onChange={(checked) => handleInputChange('canCreateStores', checked)}
              label="Can Create Stores"
            />
            <Checkbox
              checked={formData.canMonetizeBlogs}
              onChange={(checked) => handleInputChange('canMonetizeBlogs', checked)}
              label="Can Monetize Blogs"
            />
            <Checkbox
              checked={formData.prioritySupport}
              onChange={(checked) => handleInputChange('prioritySupport', checked)}
              label="Priority Support"
            />
            <div>
              <Checkbox
                checked={formData.grantsVerification}
                onChange={(checked) => handleInputChange('grantsVerification', checked)}
                label="Grants User Verification"
              />
              <p className="text-xs text-gray-500 mt-1 ml-6">
                Subscribers will automatically get a verified badge
              </p>
            </div>
          </div>
        </Card>

        {/* Badge System */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Badge System</h2>
          <p className="text-sm text-gray-600 mb-4">
            Configure the badge that will be displayed next to subscribers' names
          </p>

          <div className="space-y-6">
            {/* Badge Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Badge Type
              </label>
              <Select
                value={formData.badgeType}
                onChange={(value) => handleInputChange('badgeType', value)}
                options={BADGE_TYPE_OPTIONS.map(option => ({
                  value: option.value,
                  label: option.label
                }))}
              />
            </div>

            {/* Badge Color */}
            {formData.badgeType !== 'none' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Badge Color
                </label>
                <div className="flex space-x-2">
                  <input
                    type="color"
                    value={formData.badgeColor}
                    onChange={(e) => handleInputChange('badgeColor', e.target.value)}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <Select
                    value={formData.badgeColor}
                    onChange={(value) => handleInputChange('badgeColor', value)}
                    options={BADGE_COLOR_OPTIONS.map(option => ({
                      value: option.value,
                      label: option.label
                    }))}
                    className="flex-1"
                  />
                </div>
              </div>
            )}

            {/* Custom Badge URL */}
            {formData.badgeType === 'custom' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Badge URL
                </label>
                <Input
                  type="url"
                  value={formData.customBadgeUrl}
                  onChange={(e) => handleInputChange('customBadgeUrl', e.target.value)}
                  placeholder="https://example.com/badge.png"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Enter the URL of your custom badge image (16x16px recommended)
                </p>
              </div>
            )}

            {/* Badge Priority */}
            {formData.badgeType !== 'none' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Badge Priority
                </label>
                <Input
                  type="number"
                  value={formData.badgePriority}
                  onChange={(e) => handleInputChange('badgePriority', parseInt(e.target.value) || 0)}
                  placeholder="0"
                  min="0"
                  max="100"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Higher priority badges will be displayed when users have multiple subscriptions (0-100)
                </p>
              </div>
            )}
          </div>
        </Card>

        {/* Status */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Status</h2>
          <Checkbox
            checked={formData.isActive}
            onChange={(checked) => handleInputChange('isActive', checked)}
            label="Plan is Active"
            description="Inactive plans won't be available for new subscriptions"
          />
        </Card>

        {/* Submit */}
        <div className="flex justify-end space-x-4">
          <Link href="/admin/subscriptions/plans">
            <Button variant="outline" type="button">
              Cancel
            </Button>
          </Link>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Plan"}
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
