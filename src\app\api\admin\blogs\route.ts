import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogCategories, users, blogLikes, blogComments } from "@/lib/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, desc, asc, count, sql, and, like, or } from "drizzle-orm";

const blogSchema = z.object({
  title: z.string().min(1).max(500),
  content: z.string().min(1),
  excerpt: z.string().max(1000).optional(),
  coverImage: z.string().url().optional().nullable(),
  categoryId: z.string().optional().nullable(),
  tags: z.array(z.string()).optional(),
  status: z.enum(["draft", "published", "archived"]).default("draft"),
  readTime: z.number().optional(),
  featured: z.boolean().optional().default(false),
  seoTitle: z.string().max(255).optional(),
  seoDescription: z.string().max(1000).optional(),
  authorId: z.string().optional(), // Allow admin to create blog as another user
});

const querySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  search: z.string().optional(),
  status: z.string().optional(),
  categoryId: z.string().optional(),
  authorId: z.string().optional(),
  featured: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});

// Get all blogs with admin filtering capabilities
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams);
    const validatedQuery = querySchema.parse(queryParams);

    const page = parseInt(validatedQuery.page || "1");
    const limit = parseInt(validatedQuery.limit || "10");
    const search = validatedQuery.search || "";
    const status = validatedQuery.status || "";
    const categoryId = validatedQuery.categoryId || "";
    const authorId = validatedQuery.authorId || "";
    const featured = validatedQuery.featured === "true";
    const dateFrom = validatedQuery.dateFrom || "";
    const dateTo = validatedQuery.dateTo || "";
    const sortBy = validatedQuery.sortBy || "createdAt";
    const sortOrder = validatedQuery.sortOrder || "desc";

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        or(
          like(blogs.title, `%${search}%`),
          like(blogs.content, `%${search}%`),
          like(blogs.excerpt, `%${search}%`)
        )
      );
    }

    if (status) {
      conditions.push(eq(blogs.status, status as any));
    }

    if (categoryId) {
      conditions.push(eq(blogs.categoryId, categoryId));
    }

    if (authorId) {
      conditions.push(eq(blogs.authorId, authorId));
    }

    if (featured) {
      conditions.push(eq(blogs.featured, true));
    }

    if (dateFrom) {
      conditions.push(sql`${blogs.createdAt} >= ${new Date(dateFrom)}`);
    }

    if (dateTo) {
      conditions.push(sql`${blogs.createdAt} <= ${new Date(dateTo)}`);
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const totalCountResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(whereClause);

    const totalCount = totalCountResult[0]?.count || 0;

    // Get blogs with pagination
    const sortColumn = (() => {
      switch (sortBy) {
        case 'title': return blogs.title;
        case 'status': return blogs.status;
        case 'createdAt': return blogs.createdAt;
        case 'updatedAt': return blogs.updatedAt;
        default: return blogs.createdAt;
      }
    })();

    const orderByClause = sortOrder === "asc"
      ? asc(sortColumn)
      : desc(sortColumn);

    const blogsList = await db.query.blogs.findMany({
      where: whereClause,
      with: {
        author: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        category: true,
        likes: {
          columns: {
            id: true,
          },
        },
        comments: {
          columns: {
            id: true,
          },
        },
      },
      orderBy: orderByClause,
      limit,
      offset,
    });

    // Format response with counts
    const formattedBlogs = blogsList.map(blog => ({
      ...blog,
      _count: {
        likes: blog.likes.length,
        comments: blog.comments.length,
      },
      likes: undefined,
      comments: undefined,
    }));

    return NextResponse.json({
      blogs: formattedBlogs,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new blog as admin
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = blogSchema.parse(body);

    // Use provided authorId or default to current admin user
    const authorId = validatedData.authorId || session.user.id;

    // Verify the author exists if different from current user
    if (authorId !== session.user.id) {
      const author = await db.query.users.findFirst({
        where: eq(users.id, authorId),
        columns: { id: true },
      });

      if (!author) {
        return NextResponse.json(
          { message: "Author not found" },
          { status: 404 }
        );
      }
    }

    // Generate slug from title
    const slug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");

    // Check if slug already exists
    const existingBlog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
    });

    let finalSlug = slug;
    if (existingBlog) {
      // Add timestamp to make it unique
      finalSlug = `${slug}-${Date.now()}`;
    }

    // Calculate read time (rough estimate: 200 words per minute)
    const wordCount = validatedData.content.split(/\s+/).length;
    const readTime = validatedData.readTime || Math.ceil(wordCount / 200);

    const blogId = uuidv4();

    // Insert blog into database
    await db.insert(blogs).values({
      id: blogId,
      title: validatedData.title,
      slug: finalSlug,
      content: validatedData.content,
      excerpt: validatedData.excerpt || null,
      coverImage: validatedData.coverImage || null,
      authorId,
      categoryId: validatedData.categoryId || null,
      tags: validatedData.tags || null,
      status: validatedData.status,
      readTime,
      featured: validatedData.featured || false,
      seoTitle: validatedData.seoTitle || null,
      seoDescription: validatedData.seoDescription || null,
      publishedAt: validatedData.status === "published" ? new Date() : null,
    });

    return NextResponse.json(
      {
        message: "Blog created successfully",
        id: blogId,
        slug: finalSlug
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating blog:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
