"use client";

import { useState, useEffect } from "react";
import {
  UsersIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  ClockIcon
} from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";
import { NotificationTime } from "@/components/ui/TimeDisplay";

interface Activity {
  id: string;
  type: 'new_follower' | 'new_message' | 'post_like' | 'post_comment';
  createdAt: string;
  user: {
    id: string;
    name: string | null;
    image: string | null;
  };
  message: string;
  content?: string;
  postContent?: string;
  postId?: string;
  isRead?: boolean;
}

interface RecentActivityProps {
  pageId: string;
  limit?: number;
  showHeader?: boolean;
}

export function RecentActivity({ pageId, limit = 20, showHeader = true }: RecentActivityProps) {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchRecentActivity();
  }, [pageId, limit]);

  const fetchRecentActivity = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/fan-pages/${pageId}/dashboard/recent-activity?limit=${limit}`);

      if (!response.ok) {
        throw new Error('Failed to fetch recent activity');
      }

      const data = await response.json();
      setActivities(data.activities || []);
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      setError('Failed to load recent activity');
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'new_follower':
        return <UsersIcon className="h-5 w-5 text-blue-600" />;
      case 'new_message':
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-green-600" />;
      case 'post_like':
        return <HeartIcon className="h-5 w-5 text-red-600" />;
      case 'post_comment':
        return <ChatBubbleOvalLeftIcon className="h-5 w-5 text-purple-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getActivityBgColor = (type: Activity['type']) => {
    switch (type) {
      case 'new_follower':
        return 'bg-blue-100';
      case 'new_message':
        return 'bg-green-100';
      case 'post_like':
        return 'bg-red-100';
      case 'post_comment':
        return 'bg-purple-100';
      default:
        return 'bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        {showHeader && <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>}
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3 animate-pulse">
              <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        {showHeader && <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>}
        <div className="text-center py-8">
          <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {showHeader && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          <span className="text-sm text-gray-500">{activities.length} activities</span>
        </div>
      )}

      {activities.length === 0 ? (
        <div className="text-center py-8">
          <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No recent activity</p>
        </div>
      ) : (
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              <div className={`p-2 rounded-full ${getActivityBgColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  {activity.user.image ? (
                    <img
                      src={activity.user.image}
                      alt={activity.user.name || 'User'}
                      className="h-6 w-6 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        {(activity.user.name || 'U').charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <p className="text-sm text-gray-900 truncate">{activity.message}</p>
                  {activity.type === 'new_message' && !activity.isRead && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      New
                    </span>
                  )}
                </div>

                {activity.content && (
                  <p className="text-sm text-gray-600 mt-1 truncate">"{activity.content}"</p>
                )}

                {activity.postContent && (
                  <p className="text-sm text-gray-500 mt-1 truncate">Post: "{activity.postContent}"</p>
                )}

                <NotificationTime
                  date={activity.createdAt}
                  className="text-xs text-gray-400 mt-1"
                  autoUpdate={true}
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
