"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Select } from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { toast } from "react-hot-toast";
import { EditProductModal } from "@/components/admin/marketplace/EditProductModal";
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BuildingStorefrontIcon,
  TagIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
} from "@heroicons/react/24/outline";
import { formatTimeAgo, formatCurrency } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";

interface Product {
  id: string;
  title: string;
  description: string | null;
  price: number;
  condition: "new" | "like_new" | "good" | "fair" | "poor";
  category: string;
  location: string | null;
  photos: string[] | null;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  store: {
    id: string;
    name: string;
    logo: string | null;
    ownerId: string;
  };
  owner?: {
    id: string;
    name: string;
    image: string | null;
  };
}

export default function AdminMarketplacePage() {
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    condition: "all",
    category: "all",
    priceRange: "all",
    dateRange: "all",
  });
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [bulkAction, setBulkAction] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Fetch products data
  useEffect(() => {
    fetchProducts();
  }, [currentPage, sortBy, sortOrder]);

  // Apply filters and search
  useEffect(() => {
    if (products.length > 0) {
      let result = [...products];

      // Apply search
      if (searchTerm) {
        result = result.filter(
          (product) =>
            product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (product.description &&
              product.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
            product.store.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      // Apply filters
      if (filters.condition !== "all") {
        result = result.filter((product) => product.condition === filters.condition);
      }

      if (filters.category !== "all") {
        result = result.filter((product) => product.category === filters.category);
      }

      // Apply price range filter
      if (filters.priceRange !== "all") {
        switch (filters.priceRange) {
          case "under50":
            result = result.filter((product) => product.price < 5000); // Under $50 (in cents)
            break;
          case "50to100":
            result = result.filter((product) => product.price >= 5000 && product.price <= 10000);
            break;
          case "100to500":
            result = result.filter((product) => product.price > 10000 && product.price <= 50000);
            break;
          case "over500":
            result = result.filter((product) => product.price > 50000);
            break;
        }
      }

      // Apply date range filter
      if (filters.dateRange !== "all") {
        const now = new Date();
        let dateLimit;

        switch (filters.dateRange) {
          case "today":
            dateLimit = new Date(now.setDate(now.getDate() - 1));
            break;
          case "week":
            dateLimit = new Date(now.setDate(now.getDate() - 7));
            break;
          case "month":
            dateLimit = new Date(now.setMonth(now.getMonth() - 1));
            break;
          case "year":
            dateLimit = new Date(now.setFullYear(now.getFullYear() - 1));
            break;
          default:
            dateLimit = null;
        }

        if (dateLimit) {
          result = result.filter(
            (product) => new Date(product.createdAt) > dateLimit
          );
        }
      }

      setFilteredProducts(result);
    }
  }, [products, searchTerm, filters]);

  const fetchProducts = async () => {
    setIsLoading(true);
    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        sort: sortBy,
        order: sortOrder,
      });

      // Add search term if present
      if (searchTerm) {
        queryParams.append("search", searchTerm);
      }

      // Add filters if they're not set to 'all'
      if (filters.condition !== "all") {
        queryParams.append("condition", filters.condition);
      }

      if (filters.category !== "all") {
        queryParams.append("category", filters.category);
      }

      // Add price range filters
      if (filters.priceRange !== "all") {
        switch (filters.priceRange) {
          case "under50":
            queryParams.append("priceMax", "5000"); // $50 in cents
            break;
          case "50to100":
            queryParams.append("priceMin", "5000");
            queryParams.append("priceMax", "10000");
            break;
          case "100to500":
            queryParams.append("priceMin", "10000");
            queryParams.append("priceMax", "50000");
            break;
          case "over500":
            queryParams.append("priceMin", "50000");
            break;
        }
      }

      // Add date range filter
      if (filters.dateRange !== "all") {
        const now = new Date();
        let dateFrom;

        switch (filters.dateRange) {
          case "today":
            dateFrom = new Date(now);
            dateFrom.setDate(now.getDate() - 1);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
          case "week":
            dateFrom = new Date(now);
            dateFrom.setDate(now.getDate() - 7);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
          case "month":
            dateFrom = new Date(now);
            dateFrom.setMonth(now.getMonth() - 1);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
          case "year":
            dateFrom = new Date(now);
            dateFrom.setFullYear(now.getFullYear() - 1);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
        }
      }

      // Make API request
      const response = await fetch(`/api/admin/marketplace/products?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch products: ${response.status}`);
      }

      const data = await response.json();

      if (data && Array.isArray(data.products)) {
        setProducts(data.products);
        setFilteredProducts(data.products);
        setTotalPages(data.pagination.totalPages || 1);
      } else {
        console.error("Invalid response format from admin marketplace API:", data);
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");

      // Fallback to regular marketplace API if admin API is not available yet
      try {
        const response = await fetch(`/api/marketplace/products?limit=10&page=${currentPage}`);
        if (response.ok) {
          const data = await response.json();
          if (data && Array.isArray(data)) {
            setProducts(data);
            setFilteredProducts(data);
            setTotalPages(Math.ceil(data.length / 10) || 1);
          } else {
            // Set empty arrays as a fallback
            setProducts([]);
            setFilteredProducts([]);
            setTotalPages(1);
          }
        } else {
          // Set empty arrays if the fallback request fails
          setProducts([]);
          setFilteredProducts([]);
          setTotalPages(1);
        }
      } catch (fallbackError) {
        console.error("Fallback error:", fallbackError);
        // Set empty arrays if the fallback request throws an error
        setProducts([]);
        setFilteredProducts([]);
        setTotalPages(1);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  const handleSelectProduct = (productId: string) => {
    setSelectedProducts((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAllProducts = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map((product) => product.id));
    }
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setIsEditModalOpen(true);
  };

  const handleProductUpdated = () => {
    // Refresh the products list after a product is updated
    fetchProducts();
  };

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm("Are you sure you want to delete this product?")) return;

    try {
      const response = await fetch(`/api/admin/marketplace/products/${productId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete product: ${response.status}`);
      }

      // Update UI after successful deletion
      setProducts(products.filter(product => product.id !== productId));
      setFilteredProducts(filteredProducts.filter(product => product.id !== productId));
      toast.success("Product deleted successfully");
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedProducts.length === 0) return;

    try {
      if (bulkAction === "delete") {
        const confirmed = window.confirm(
          `Are you sure you want to delete ${selectedProducts.length} products? This action cannot be undone.`
        );

        if (!confirmed) return;

        const response = await fetch(`/api/admin/marketplace/products`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ productIds: selectedProducts }),
        });

        if (!response.ok) {
          throw new Error(`Failed to delete products: ${response.status}`);
        }

        // Update UI after successful deletion
        setProducts(products.filter(product => !selectedProducts.includes(product.id)));
        setFilteredProducts(filteredProducts.filter(product => !selectedProducts.includes(product.id)));
        toast.success(`${selectedProducts.length} products deleted successfully`);
        setSelectedProducts([]);
      }
    } catch (error) {
      console.error("Error performing bulk action:", error);
      toast.error("Failed to perform bulk action");
    }
  };

  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "new":
        return (
          <Badge variant="success">New</Badge>
        );
      case "like_new":
        return (
          <Badge variant="success">Like New</Badge>
        );
      case "good":
        return (
          <Badge variant="warning">Good</Badge>
        );
      case "fair":
        return (
          <Badge variant="warning">Fair</Badge>
        );
      case "poor":
        return (
          <Badge variant="danger">Poor</Badge>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Marketplace Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage products, categories, and marketplace settings
          </p>
        </div>
        <div className="mt-4 flex space-x-2 sm:mt-0">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <FunnelIcon className="mr-2 h-5 w-5" />
            Filters
          </Button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="mb-6">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
          <form onSubmit={handleSearch} className="relative flex-grow">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search products..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </form>
          <Button
            onClick={fetchProducts}
            variant="outline"
            className="flex-shrink-0"
          >
            <ArrowPathIcon className="mr-2 h-5 w-5" />
            Refresh
          </Button>
        </div>

        {showFilters && (
          <div className="mt-4 rounded-lg border border-gray-200 bg-white p-4">
            <h3 className="mb-3 font-medium text-gray-700">Filter Products</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Condition
                </label>
                <Select
                  value={filters.condition}
                  onChange={(e) => handleFilterChange("condition", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Conditions</option>
                  <option value="new">New</option>
                  <option value="like_new">Like New</option>
                  <option value="good">Good</option>
                  <option value="fair">Fair</option>
                  <option value="poor">Poor</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Category
                </label>
                <Select
                  value={filters.category}
                  onChange={(e) => handleFilterChange("category", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Categories</option>
                  <option value="Electronics">Electronics</option>
                  <option value="Clothing & Accessories">Clothing & Accessories</option>
                  <option value="Home & Garden">Home & Garden</option>
                  <option value="Toys & Games">Toys & Games</option>
                  <option value="Sports & Outdoors">Sports & Outdoors</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Price Range
                </label>
                <Select
                  value={filters.priceRange}
                  onChange={(e) => handleFilterChange("priceRange", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Prices</option>
                  <option value="under50">Under $50</option>
                  <option value="50to100">$50 - $100</option>
                  <option value="100to500">$100 - $500</option>
                  <option value="over500">Over $500</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Listed
                </label>
                <Select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange("dateRange", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="year">This Year</option>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk actions */}
      {selectedProducts.length > 0 && (
        <div className="mb-4 flex items-center justify-between rounded-lg bg-blue-50 p-3">
          <span className="text-sm font-medium text-blue-700">
            {selectedProducts.length} products selected
          </span>
          <div className="flex items-center space-x-2">
            <Select
              value={bulkAction}
              onChange={(e) => setBulkAction(e.target.value)}
              className="text-sm"
            >
              <option value="">Bulk Action</option>
              <option value="delete">Delete Selected</option>
            </Select>
            <Button
              onClick={handleBulkAction}
              disabled={!bulkAction}
              size="sm"
              variant={bulkAction === "delete" ? "danger" : "primary"}
            >
              Apply
            </Button>
          </div>
        </div>
      )}

      {/* Products table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Spinner size="lg" />
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center p-4 text-center">
            <ShoppingBagIcon className="mb-2 h-12 w-12 text-gray-400" />
            <h3 className="mb-1 text-lg font-medium text-gray-900">No products found</h3>
            <p className="text-gray-500">
              {searchTerm || Object.values(filters).some((v) => v !== "all")
                ? "Try adjusting your search or filters"
                : "Add your first product to get started"}
            </p>
            {(searchTerm || Object.values(filters).some((v) => v !== "all")) && (
              <Button
                onClick={() => {
                  setSearchTerm("");
                  setFilters({
                    condition: "all",
                    category: "all",
                    priceRange: "all",
                    dateRange: "all",
                  });
                }}
                variant="outline"
                className="mt-4"
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="w-12 px-3 py-3 text-left">
                    <Checkbox
                      checked={
                        selectedProducts.length > 0 &&
                        selectedProducts.length === filteredProducts.length
                      }
                      onChange={handleSelectAllProducts}
                      aria-label="Select all products"
                    />
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("title")}
                    >
                      Product
                      {sortBy === "title" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("store.name")}
                    >
                      Store
                      {sortBy === "store.name" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("price")}
                    >
                      Price
                      {sortBy === "price" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("condition")}
                    >
                      Condition
                      {sortBy === "condition" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("category")}
                    >
                      Category
                      {sortBy === "category" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("createdAt")}
                    >
                      Listed
                      {sortBy === "createdAt" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-3 py-4">
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onChange={() => handleSelectProduct(product.id)}
                        aria-label={`Select ${product.title}`}
                      />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          {product.photos && product.photos.length > 0 ? (
                            <Image
                              src={product.photos[0]}
                              alt={product.title}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          ) : (
                            <div className="flex h-10 w-10 items-center justify-center rounded-md bg-gray-100 text-gray-500">
                              <ShoppingBagIcon className="h-6 w-6" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {product.title}
                          </div>
                          <div className="text-xs text-gray-500">
                            ID: {product.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="h-6 w-6 flex-shrink-0">
                          {product.store.logo ? (
                            <Image
                              src={product.store.logo}
                              alt={product.store.name}
                              width={24}
                              height={24}
                              className="h-6 w-6 rounded-full"
                            />
                          ) : (
                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200">
                              <BuildingStorefrontIcon className="h-4 w-4 text-gray-500" />
                            </div>
                          )}
                        </div>
                        <div className="ml-2 text-sm">{product.store.name}</div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      <div className="font-medium text-gray-900">
                        {formatCurrency(product.price)}
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {getConditionBadge(product.condition)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                        <TagIcon className="mr-1 h-3 w-3" />
                        {product.category}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <div>{new Date(product.createdAt).toLocaleDateString()}</div>
                      <div className="text-xs">{formatTimeAgo(product.createdAt)}</div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <a
                          href={`/marketplace/product/${product.id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                        </a>
                        <Button
                          onClick={() => handleEditProduct(product)}
                          variant="outline"
                          size="sm"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleDeleteProduct(product.id)}
                          variant="danger"
                          size="sm"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && filteredProducts.length > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{filteredProducts.length}</span>{" "}
            products
          </div>
          <div className="flex space-x-1">
            <Button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            <Button
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Edit Product Modal */}
      <EditProductModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        product={selectedProduct}
        onProductUpdated={handleProductUpdated}
      />
    </AdminLayout>
  );
}
