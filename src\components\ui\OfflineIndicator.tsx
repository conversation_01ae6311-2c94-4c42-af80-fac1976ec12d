"use client";

import { useEffect, useState } from 'react';
import { WifiIcon, CloudArrowUpIcon } from '@heroicons/react/24/outline';
import { useOfflineSupport } from '@/hooks/useOfflineSupport';
import { cn } from '@/lib/utils';

export function OfflineIndicator() {
  const { isOnline, hasPendingActions, syncPendingActions } = useOfflineSupport();
  const [showIndicator, setShowIndicator] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  // Show indicator when offline or has pending actions
  useEffect(() => {
    setShowIndicator(!isOnline || hasPendingActions);
  }, [isOnline, hasPendingActions]);

  const handleSync = async () => {
    if (isSyncing || !isOnline) return;
    
    setIsSyncing(true);
    try {
      await syncPendingActions();
    } finally {
      setIsSyncing(false);
    }
  };

  if (!showIndicator) return null;

  return (
    <div className={cn(
      "fixed top-16 left-4 right-4 z-40 mx-auto max-w-sm",
      "transform transition-all duration-300 ease-in-out"
    )}>
      <div className={cn(
        "flex items-center justify-between p-3 rounded-lg shadow-lg border",
        !isOnline 
          ? "bg-red-50 border-red-200 text-red-800"
          : "bg-yellow-50 border-yellow-200 text-yellow-800"
      )}>
        <div className="flex items-center space-x-2">
          <div className={cn(
            "w-2 h-2 rounded-full",
            !isOnline ? "bg-red-500" : "bg-yellow-500"
          )} />
          
          <div className="flex items-center space-x-1">
            <WifiIcon className={cn(
              "w-4 h-4",
              !isOnline && "opacity-50"
            )} />
            <span className="text-sm font-medium">
              {!isOnline ? "Offline" : "Syncing..."}
            </span>
          </div>
        </div>

        {isOnline && hasPendingActions && (
          <button
            onClick={handleSync}
            disabled={isSyncing}
            className={cn(
              "flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium",
              "bg-yellow-100 hover:bg-yellow-200 transition-colors",
              "disabled:opacity-50 disabled:cursor-not-allowed"
            )}
          >
            <CloudArrowUpIcon className={cn(
              "w-3 h-3",
              isSyncing && "animate-bounce"
            )} />
            <span>{isSyncing ? "Syncing" : "Sync"}</span>
          </button>
        )}
      </div>
    </div>
  );
}
