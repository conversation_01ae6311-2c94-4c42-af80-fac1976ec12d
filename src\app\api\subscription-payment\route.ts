import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { SubscriptionService } from "@/lib/subscription/subscriptionService";
import { PaymentService } from "@/lib/payment/paymentService";
import { z } from "zod";

const subscriptionPaymentSchema = z.object({
  subscriptionId: z.string(),
  paymentGatewayId: z.string(),
  returnUrl: z.string().optional(),
  cancelUrl: z.string().optional(),
});

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { subscriptionId, paymentGatewayId, returnUrl, cancelUrl } = subscriptionPaymentSchema.parse(body);

    // Get the subscription details
    const subscription = await SubscriptionService.getUserSubscription(session.user.id);
    
    if (!subscription || subscription.id !== subscriptionId) {
      return NextResponse.json(
        { message: "Subscription not found or access denied" },
        { status: 404 }
      );
    }

    if (subscription.status !== 'pending') {
      return NextResponse.json(
        { message: "Subscription is not in pending status" },
        { status: 400 }
      );
    }

    // Get the plan details
    const plan = await SubscriptionService.getSubscriptionPlan(subscription.planId);
    
    if (!plan) {
      return NextResponse.json(
        { message: "Subscription plan not found" },
        { status: 404 }
      );
    }

    // Create transaction record
    const transactionId = await SubscriptionService.createTransaction({
      subscriptionId,
      userId: session.user.id,
      planId: subscription.planId,
      type: 'payment',
      amount: plan.price,
      currency: plan.currency,
      paymentGateway: paymentGatewayId,
      description: `Payment for ${plan.displayName} subscription`,
    });

    // Process payment through payment service
    const paymentResult = await PaymentService.processPayment({
      amount: plan.price,
      currency: plan.currency,
      gatewayId: paymentGatewayId,
      userId: session.user.id,
      metadata: {
        subscriptionId,
        transactionId,
        planId: subscription.planId,
        type: 'subscription_payment',
        returnUrl,
        cancelUrl,
      },
    });

    if (!paymentResult.success) {
      // Update transaction status to failed
      await SubscriptionService.updateTransactionStatus(transactionId, 'failed');

      return NextResponse.json(
        {
          success: false,
          message: paymentResult.error || "Payment processing failed"
        },
        { status: 400 }
      );
    }

    // Handle wallet payments differently (they complete immediately)
    if (paymentResult.walletPayment) {
      // Update transaction status to completed for wallet payments
      await SubscriptionService.updateTransactionStatus(
        transactionId,
        'completed',
        {
          gatewayTransactionId: paymentResult.transactionId,
          walletPayment: true,
        }
      );

      return NextResponse.json({
        success: true,
        data: {
          transactionId,
          gatewayTransactionId: paymentResult.transactionId,
          message: paymentResult.message || "Payment completed successfully using wallet balance",
          walletPayment: true,
          subscriptionActivated: true,
        },
      });
    }

    // Update transaction with gateway details for external payments
    await SubscriptionService.updateTransactionStatus(
      transactionId,
      'pending',
      {
        gatewayTransactionId: paymentResult.transactionId,
        paymentUrl: paymentResult.paymentUrl,
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        transactionId,
        paymentUrl: paymentResult.paymentUrl,
        gatewayTransactionId: paymentResult.transactionId,
        message: paymentResult.message || "Payment initiated successfully",
      },
    });

  } catch (error) {
    console.error("Error processing subscription payment:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
