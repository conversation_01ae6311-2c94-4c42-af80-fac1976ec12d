import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { desc, eq, and, or, like, gte, lte } from "drizzle-orm";

const eventSchema = z.object({
  name: z.string().min(2).max(255),
  description: z.string().max(5000).optional().nullable(),
  startTime: z.string().datetime(),
  endTime: z.string().datetime(),
  location: z.string().max(255).optional().nullable(),
  isOnline: z.boolean().optional().default(false),
  onlineLink: z.string().url().optional().nullable(),
  visibility: z.enum(["public", "private", "friends"]).default("public"),
  category: z.string().max(100).optional().nullable(),
  coverImage: z.string().url().optional().nullable(),
});

// Get all events with filtering options
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const category = url.searchParams.get("category");
    const visibility = url.searchParams.get("visibility") as "public" | "private" | "friends" | null;
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const hostId = url.searchParams.get("hostId");
    const search = url.searchParams.get("search");

    // Build the query with all conditions
    const conditions = [];

    if (category) conditions.push(eq(events.category, category));
    if (visibility) conditions.push(eq(events.visibility, visibility));
    if (startDate) conditions.push(gte(events.startTime, new Date(startDate)));
    if (endDate) conditions.push(lte(events.endTime, new Date(endDate)));
    if (hostId) conditions.push(eq(events.hostId, hostId));
    if (search) {
      conditions.push(
        or(
          like(events.name, `%${search}%`),
          like(events.description || "", `%${search}%`)
        )
      );
    }

    // Execute the query
    const allEvents = await db.query.events.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: desc(events.startTime),
    });

    return NextResponse.json(allEvents);
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new event
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = eventSchema.parse(body);

    // Generate a new UUID for the event
    const eventId = uuidv4();

    // Create the event
    await db.insert(events).values({
      id: eventId,
      name: validatedData.name,
      description: validatedData.description || null,
      startTime: new Date(validatedData.startTime),
      endTime: new Date(validatedData.endTime),
      location: validatedData.location || null,
      isOnline: validatedData.isOnline || false,
      onlineLink: validatedData.onlineLink || null,
      visibility: validatedData.visibility,
      category: validatedData.category || null,
      coverImage: validatedData.coverImage || null,
      hostId: session.user.id,
    });

    // Fetch the created event
    const newEventData = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!newEventData) {
      throw new Error("Failed to create event");
    }

    // Fetch host data separately to avoid collation issues
    const hostData = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
      columns: {
        id: true,
        name: true,
        username: true,
        image: true,
      },
    });

    // Combine event and host data
    const newEvent = {
      ...newEventData,
      host: hostData,
    };

    return NextResponse.json(newEvent);
  } catch (error) {
    console.error("Error creating event:", error);

    if (error instanceof z.ZodError) {
      // Format Zod errors to be more user-friendly
      const formattedErrors = error.errors.map(err => {
        const field = err.path.join('.') || 'unknown';
        return `${field}: ${err.message}`;
      });

      return NextResponse.json(
        {
          message: "Validation error",
          errors: error.errors,
          formattedErrors
        },
        { status: 400 }
      );
    }

    // Provide more detailed error message
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string };

        if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
          console.error("Collation mismatch error:", dbError);
        } else if (dbError.sqlMessage) {
          // Log the SQL error but don't expose it to the client
          console.error("SQL Error:", dbError.sqlMessage);
        }
      }
    }

    return NextResponse.json(
      { message: errorMessage },
      { status: statusCode }
    );
  }
}
