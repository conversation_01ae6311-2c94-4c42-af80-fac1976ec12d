import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, users, fanPageFollowers, fanPagePosts, fanPageRoles } from "@/lib/db/schema";
import { eq, and, count } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/admin/fan-pages/[pageId] - Get specific fan page details for admin
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0 || !user[0].isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get fan page with owner info
    const pageResult = await db
      .select({
        id: fanPages.id,
        ownerId: fanPages.ownerId,
        name: fanPages.name,
        username: fanPages.username,
        category: fanPages.category,
        description: fanPages.description,
        profileImage: fanPages.profileImage,
        coverImage: fanPages.coverImage,
        website: fanPages.website,
        email: fanPages.email,
        phone: fanPages.phone,
        location: fanPages.location,
        isVerified: fanPages.isVerified,
        isActive: fanPages.isActive,
        followerCount: fanPages.followerCount,
        postCount: fanPages.postCount,
        createdAt: fanPages.createdAt,
        updatedAt: fanPages.updatedAt,
        owner: {
          id: users.id,
          name: users.name,
          email: users.email,
          image: users.image,
          isActive: users.isActive,
          createdAt: users.createdAt,
        },
      })
      .from(fanPages)
      .leftJoin(users, eq(fanPages.ownerId, users.id))
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    // Get follower count
    const followerCountResult = await db
      .select({ count: count() })
      .from(fanPageFollowers)
      .where(eq(fanPageFollowers.fanPageId, pageId));

    // Get post count
    const postCountResult = await db
      .select({ count: count() })
      .from(fanPagePosts)
      .where(eq(fanPagePosts.fanPageId, pageId));

    // Get roles count
    const rolesCountResult = await db
      .select({ count: count() })
      .from(fanPageRoles)
      .where(eq(fanPageRoles.fanPageId, pageId));

    const page = pageResult[0];
    const stats = {
      followers: followerCountResult[0]?.count || 0,
      posts: postCountResult[0]?.count || 0,
      roles: rolesCountResult[0]?.count || 0,
    };

    return NextResponse.json({
      page,
      stats,
    });
  } catch (error) {
    console.error("Error fetching admin fan page details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/fan-pages/[pageId] - Delete fan page (admin only)
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0 || !user[0].isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if fan page exists
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    // Delete the fan page (cascade will handle related records)
    await db.delete(fanPages).where(eq(fanPages.id, pageId));

    return NextResponse.json({ message: "Fan page deleted successfully" });
  } catch (error) {
    console.error("Error deleting fan page:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
