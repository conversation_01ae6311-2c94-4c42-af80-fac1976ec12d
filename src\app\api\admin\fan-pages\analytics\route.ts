import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, users, fanPageFollowers, fanPagePosts } from "@/lib/db/schema";
import { eq, count, sum, gte, and, sql } from "drizzle-orm";

// GET /api/admin/fan-pages/analytics - Get fan pages analytics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0 || !user[0].isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get("range") || "30d";

    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    
    switch (range) {
      case "7d":
        startDate.setDate(now.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(now.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(now.getDate() - 90);
        break;
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get overview stats
    const totalPagesResult = await db
      .select({ count: count() })
      .from(fanPages);

    const activePagesResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(eq(fanPages.isActive, true));

    const verifiedPagesResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(eq(fanPages.isVerified, true));

    const totalFollowersResult = await db
      .select({ total: sum(fanPages.followerCount) })
      .from(fanPages);

    const totalPostsResult = await db
      .select({ total: sum(fanPages.postCount) })
      .from(fanPages);

    // Get new pages in the time range
    const newPagesResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(gte(fanPages.createdAt, startDate));

    // Calculate growth rate (simplified)
    const previousPeriodStart = new Date(startDate);
    previousPeriodStart.setTime(previousPeriodStart.getTime() - (now.getTime() - startDate.getTime()));
    
    const previousNewPagesResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(and(
        gte(fanPages.createdAt, previousPeriodStart),
        gte(fanPages.createdAt, startDate)
      ));

    const currentNewPages = newPagesResult[0]?.count || 0;
    const previousNewPages = previousNewPagesResult[0]?.count || 1;
    const growthRate = ((currentNewPages - previousNewPages) / previousNewPages) * 100;

    // Get category breakdown
    const categoryBreakdownResult = await db
      .select({
        category: fanPages.category,
        count: count(),
      })
      .from(fanPages)
      .groupBy(fanPages.category);

    const totalPages = totalPagesResult[0]?.count || 0;
    const categoryBreakdown = categoryBreakdownResult.map(item => ({
      category: item.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count: item.count,
      percentage: totalPages > 0 ? Math.round((item.count / totalPages) * 100 * 10) / 10 : 0,
    }));

    // Get monthly growth (last 5 months)
    const monthlyGrowth = [];
    for (let i = 4; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
      
      const monthNewPagesResult = await db
        .select({ count: count() })
        .from(fanPages)
        .where(and(
          gte(fanPages.createdAt, monthStart),
          gte(fanPages.createdAt, monthEnd)
        ));

      const monthTotalPagesResult = await db
        .select({ count: count() })
        .from(fanPages)
        .where(gte(fanPages.createdAt, monthEnd));

      monthlyGrowth.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        newPages: monthNewPagesResult[0]?.count || 0,
        totalPages: monthTotalPagesResult[0]?.count || 0,
      });
    }

    // Get top pages by followers
    const topPagesResult = await db
      .select({
        id: fanPages.id,
        name: fanPages.name,
        username: fanPages.username,
        followerCount: fanPages.followerCount,
        postCount: fanPages.postCount,
        isVerified: fanPages.isVerified,
      })
      .from(fanPages)
      .where(eq(fanPages.isActive, true))
      .orderBy(sql`${fanPages.followerCount} DESC`)
      .limit(10);

    const analytics = {
      overview: {
        totalPages: totalPages,
        activePages: activePagesResult[0]?.count || 0,
        verifiedPages: verifiedPagesResult[0]?.count || 0,
        totalFollowers: Number(totalFollowersResult[0]?.total) || 0,
        totalPosts: Number(totalPostsResult[0]?.total) || 0,
        growthRate: Math.round(growthRate * 10) / 10,
      },
      categoryBreakdown,
      monthlyGrowth,
      topPages: topPagesResult,
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error("Error fetching fan pages analytics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
