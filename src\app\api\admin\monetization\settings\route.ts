import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { MonetizationService } from "@/lib/monetization/monetizationService";
import { z } from "zod";

const settingsSchema = z.object({
  cprRate: z.number().min(0).max(100).optional(),
  minPayoutThreshold: z.number().min(0).max(10000).optional(),
  minReadDuration: z.number().min(10).max(3600).optional(), // 10 seconds to 1 hour
  isEnabled: z.boolean().optional(),
});

// Get monetization settings
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const config = await MonetizationService.getMonetizationConfig();

    return NextResponse.json({
      success: true,
      data: config,
    });
  } catch (error: any) {
    console.error("Error fetching monetization settings:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to fetch monetization settings" 
      },
      { status: 500 }
    );
  }
}

// Update monetization settings
export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = settingsSchema.parse(body);

    await MonetizationService.updateMonetizationConfig(validatedData);

    return NextResponse.json({
      success: true,
      message: "Monetization settings updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating monetization settings:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to update monetization settings" 
      },
      { status: 500 }
    );
  }
}
