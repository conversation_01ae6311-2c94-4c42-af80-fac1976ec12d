// Memory monitoring utility for development
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    console.log('🔍 Memory monitoring started...');

    this.intervalId = setInterval(() => {
      this.logMemoryUsage();
    }, intervalMs);

    // Log initial memory usage
    this.logMemoryUsage();
  }

  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isMonitoring = false;
    console.log('🛑 Memory monitoring stopped');
  }

  logMemoryUsage(): void {
    const usage = process.memoryUsage();
    const formatBytes = (bytes: number): string => {
      return (bytes / 1024 / 1024).toFixed(2) + ' MB';
    };

    console.log('📊 Memory Usage:', {
      rss: formatBytes(usage.rss), // Resident Set Size
      heapTotal: formatBytes(usage.heapTotal), // Total heap size
      heapUsed: formatBytes(usage.heapUsed), // Used heap size
      external: formatBytes(usage.external), // External memory
      arrayBuffers: formatBytes(usage.arrayBuffers), // ArrayBuffer memory
    });

    // Warning if memory usage is high
    const heapUsedMB = usage.heapUsed / 1024 / 1024;
    if (heapUsedMB > 1500) {
      console.warn('⚠️  High memory usage detected:', formatBytes(usage.heapUsed));
    }
  }

  forceGarbageCollection(): void {
    if (global.gc) {
      console.log('🗑️  Forcing garbage collection...');
      global.gc();
      this.logMemoryUsage();
    } else {
      console.warn('⚠️  Garbage collection not available. Run with --expose-gc flag.');
    }
  }

  getMemoryStats() {
    const usage = process.memoryUsage();
    return {
      rss: usage.rss,
      heapTotal: usage.heapTotal,
      heapUsed: usage.heapUsed,
      external: usage.external,
      arrayBuffers: usage.arrayBuffers,
      heapUsedPercentage: (usage.heapUsed / usage.heapTotal) * 100,
    };
  }
}

// Auto-start monitoring in development
if (process.env.NODE_ENV === 'development' && process.env.MEMORY_MONITORING === 'true') {
  const monitor = MemoryMonitor.getInstance();
  monitor.startMonitoring(60000); // Monitor every minute

  // Cleanup on process exit
  process.on('SIGINT', () => {
    monitor.stopMonitoring();
    process.exit(0);
  });
}
