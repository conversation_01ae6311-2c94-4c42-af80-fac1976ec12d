"use client";

import { useState, useEffect, Fragment, useRef } from "react";
import { Menu, Transition } from "@headlessui/react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import {
  ChatBubbleLeftRightIcon,
  UserCircleIcon,
  ChevronDownIcon,
  EyeIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";
import { MessageTime } from "@/components/ui/TimeDisplay";
import { usePreventScrollLock } from "@/hooks/usePreventScrollLock";

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  read: boolean;
  createdAt: string;
  sender: {
    id: string;
    name: string;
    image: string;
  };
  receiver: {
    id: string;
    name: string;
    image: string;
  };
}

interface Conversation {
  user: {
    id: string;
    name: string;
    image: string;
  };
  lastMessage: Message;
  unreadCount: number;
  type?: 'user' | 'fanpage';
  fanPageId?: string;
  isPinned?: boolean;
}

export function MessageDropdown() {
  const { data: session } = useSession();
  const router = useRouter();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalUnreadCount, setTotalUnreadCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const openStateRef = useRef(false);

  // Prevent scroll lock when dropdown is open
  usePreventScrollLock(isOpen);

  // Update isOpen state when menu state changes (using useEffect to avoid setState during render)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (openStateRef.current !== isOpen) {
        setIsOpen(openStateRef.current);
      }
    }, 0);

    return () => clearTimeout(timer);
  });

  // Fetch recent conversations
  const fetchRecentConversations = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch("/api/messages");

      if (response.ok) {
        const data = await response.json();
        // Get only the first 10 conversations
        const recentConversations = data.slice(0, 10);
        setConversations(recentConversations);

        // Calculate total unread count
        const unreadCount = recentConversations.reduce(
          (total: number, conv: Conversation) => total + conv.unreadCount,
          0
        );
        setTotalUnreadCount(unreadCount);
      } else {
        setError('Failed to load conversations');
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
      setError('Network error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchRecentConversations();

      // Refresh every 30 seconds
      const interval = setInterval(fetchRecentConversations, 30000);
      return () => clearInterval(interval);
    }
  }, [session]);

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes < 1 ? "now" : `${diffInMinutes}m`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d`;
    }
  };

  const truncateMessage = (content: string, maxLength: number = 40) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + "...";
  };

  const handleConversationClick = (userId: string) => {
    router.push(`/messages?userId=${userId}`);
  };

  if (!session?.user) {
    return null;
  }

  return (
    <Menu as="div" className="relative">
      {({ open }) => {
        // Track open state in ref to avoid setState during render
        openStateRef.current = open;

        return (
          <>
      <Menu.Button className="flex items-center justify-center p-2 rounded-xl w-10 h-10 transition-all duration-200 text-gray-700 hover:text-blue-600 hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-md relative group active:scale-95">
        <ChatBubbleLeftRightIcon className="h-6 w-6 transition-transform duration-200 group-hover:scale-110" />
        {totalUnreadCount > 0 && (
          <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-gradient-to-r from-red-500 to-red-600 text-xs font-bold text-white shadow-lg animate-pulse">
            {totalUnreadCount > 99 ? "99+" : totalUnreadCount}
          </span>
        )}
        <span className="sr-only">Messages</span>
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-300"
        enterFrom="transform opacity-0 scale-95 translate-y-2"
        enterTo="transform opacity-100 scale-100 translate-y-0"
        leave="transition ease-in duration-200"
        leaveFrom="transform opacity-100 scale-100 translate-y-0"
        leaveTo="transform opacity-0 scale-95 translate-y-2"
      >
        <Menu.Items className="absolute right-0 z-50 mt-3 w-80 sm:w-96 origin-top-right rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-100 backdrop-blur-sm overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-white/20 rounded-full p-2">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">Messages</h3>
                  <p className="text-blue-100 text-sm">Stay connected with friends</p>
                </div>
              </div>
              {totalUnreadCount > 0 && (
                <span className="bg-white/20 backdrop-blur-sm text-white text-sm font-medium px-3 py-1 rounded-full">
                  {totalUnreadCount} unread
                </span>
              )}
            </div>
          </div>

          {/* Conversations List */}
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="p-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3 p-2 animate-pulse">
                    <div className="w-10 h-10 bg-gray-200 rounded-full" />
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-24 mb-1" />
                      <div className="h-3 bg-gray-200 rounded w-32" />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="p-6 text-center">
                <div className="text-red-500 mb-2">
                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">{error}</p>
                <button
                  onClick={fetchRecentConversations}
                  className="mt-2 text-xs text-blue-600 hover:text-blue-800"
                >
                  Try again
                </button>
              </div>
            ) : conversations.length === 0 ? (
              <div className="p-6 text-center">
                <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">No conversations yet</p>
                <p className="text-xs text-gray-400">Start messaging with friends</p>
              </div>
            ) : (
              conversations.map((conversation) => (
                <Menu.Item key={conversation.user.id}>
                  {({ active }) => (
                    <button
                      onClick={() => handleConversationClick(conversation.user.id)}
                      className={cn(
                        "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors",
                        active && "bg-gray-50",
                        conversation.unreadCount > 0 && "bg-blue-50"
                      )}
                    >
                      {/* Avatar */}
                      <div className="relative flex-shrink-0">
                        {conversation.user.image ? (
                          <Image
                            src={conversation.user.image}
                            alt={conversation.user.name}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                        ) : (
                          <UserCircleIcon className="h-10 w-10 text-gray-400" />
                        )}
                        {conversation.unreadCount > 0 && (
                          <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white">
                            {conversation.unreadCount > 9 ? "9+" : conversation.unreadCount}
                          </span>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className={cn(
                            "text-sm truncate",
                            conversation.unreadCount > 0 ? "font-semibold text-gray-900" : "font-medium text-gray-700"
                          )}>
                            {conversation.user.name}
                          </p>
                          <MessageTime
                            date={conversation.lastMessage.createdAt}
                            className="text-xs text-gray-500 flex-shrink-0 ml-2"
                            autoUpdate={true}
                          />
                        </div>
                        <div className="flex items-center space-x-1">
                          {conversation.lastMessage.senderId === session.user.id && (
                            <div className="flex-shrink-0">
                              {conversation.lastMessage.read ? (
                                <EyeIcon className="h-3 w-3 text-blue-500" />
                              ) : (
                                <ClockIcon className="h-3 w-3 text-gray-400" />
                              )}
                            </div>
                          )}
                          <p className={cn(
                            "text-sm truncate",
                            conversation.unreadCount > 0 ? "text-gray-900 font-medium" : "text-gray-500"
                          )}>
                            {truncateMessage(conversation.lastMessage.content)}
                          </p>
                        </div>
                      </div>
                    </button>
                  )}
                </Menu.Item>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200">
            <Menu.Item>
              {({ active }) => (
                <Link
                  href="/messages"
                  className={cn(
                    "flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-blue-600 hover:bg-gray-50 transition-colors",
                    active && "bg-gray-50"
                  )}
                >
                  See All Messages
                </Link>
              )}
            </Menu.Item>
          </div>
        </Menu.Items>
      </Transition>
          </>
        );
      }}
    </Menu>
  );
}
