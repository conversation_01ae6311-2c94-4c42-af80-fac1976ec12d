"use client";

import { useState, useRef, useEffect } from "react";
import { format, isToday, isYesterday } from "date-fns";
import { MessageTime } from "@/components/ui/TimeDisplay";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  PaperAirplaneIcon,
  FaceSmileIcon,
  PaperClipIcon,
  InformationCircleIcon,
  CheckIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

interface FanPageMessage {
  id: string;
  fanPageId: string;
  senderId: string;
  content: string;
  isFromPage?: boolean; // true if message is from page owner
  read: boolean;
  createdAt: string;
  sender: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface FanPageChatAreaProps {
  messages: FanPageMessage[];
  selectedUser: {
    id: string;
    name: string | null;
    image: string | null;
  } | null;
  currentUserId: string;
  onSendMessage: (content: string) => Promise<boolean>;
  onSendReply?: (content: string, recipientId: string) => Promise<boolean>; // New reply function
  isSending?: boolean;
  className?: string;
}

export function FanPageChatArea({
  messages,
  selectedUser,
  currentUserId,
  onSendMessage,
  onSendReply,
  isSending = false,
  className
}: FanPageChatAreaProps) {
  const [messageInput, setMessageInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!messageInput.trim() || isSending || !selectedUser) return;

    const content = messageInput.trim();
    setMessageInput("");

    // Use reply function if available, otherwise use regular send
    const success = onSendReply
      ? await onSendReply(content, selectedUser.id)
      : await onSendMessage(content);

    if (!success) {
      setMessageInput(content); // Restore message on failure
    }
  };

  // Removed formatMessageTime function as we'll use MessageTime component

  const groupMessagesByDate = (messages: FanPageMessage[]) => {
    const groups: { [date: string]: FanPageMessage[] } = {};

    messages.forEach(message => {
      const date = format(new Date(message.createdAt), "yyyy-MM-dd");
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return groups;
  };

  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString);
    if (isToday(date)) {
      return "Today";
    } else if (isYesterday(date)) {
      return "Yesterday";
    } else {
      return format(date, "MMMM d, yyyy");
    }
  };

  if (!selectedUser) {
    return (
      <div className={cn("flex items-center justify-center h-full bg-gray-50", className)}>
        <div className="text-center">
          <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No conversation selected</h3>
          <p className="mt-1 text-sm text-gray-500">
            Choose a conversation from the list to start messaging
          </p>
        </div>
      </div>
    );
  }

  const messageGroups = groupMessagesByDate(messages);

  return (
    <div className={cn("flex flex-col h-full bg-white", className)}>
      {/* Chat header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center space-x-3">
          {selectedUser.image ? (
            <OptimizedImage
              src={selectedUser.image}
              alt={selectedUser.name || "User"}
              width={40}
              height={40}
              className="h-10 w-10 rounded-full object-cover"
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <span className="text-sm font-semibold text-white">
                {(selectedUser.name || "U").charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          <div>
            <h3 className="text-sm font-medium text-gray-900">
              {selectedUser.name || "Unknown User"}
            </h3>
            <p className="text-xs text-gray-500">
              Fan page conversation
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button size="sm" variant="ghost">
            <InformationCircleIcon className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {Object.entries(messageGroups).map(([date, dayMessages]) => (
          <div key={date}>
            {/* Date header */}
            <div className="flex justify-center mb-4">
              <span className="px-3 py-1 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">
                {formatDateHeader(date)}
              </span>
            </div>

            {/* Messages for this date */}
            <div className="space-y-3">
              {dayMessages.map((message, index) => {
                // Check if message is from page (page owner reply) or from user
                const isFromPage = message.isFromPage || false;
                const isFromCurrentUser = message.senderId === currentUserId;
                const showAvatar = !isFromCurrentUser && (
                  index === 0 ||
                  dayMessages[index - 1]?.senderId !== message.senderId
                );

                return (
                  <div
                    key={message.id}
                    className={cn(
                      "flex items-end space-x-2",
                      isFromPage ? "justify-end" : "justify-start"
                    )}
                  >
                    {!isFromPage && (
                      <div className="w-8 h-8 flex-shrink-0">
                        {showAvatar && (
                          selectedUser.image ? (
                            <OptimizedImage
                              src={selectedUser.image}
                              alt={selectedUser.name || "User"}
                              width={32}
                              height={32}
                              className="h-8 w-8 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                              <span className="text-xs font-semibold text-white">
                                {(selectedUser.name || "U").charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    )}

                    <div
                      className={cn(
                        "max-w-xs lg:max-w-md px-4 py-2 rounded-lg",
                        isFromPage
                          ? "bg-blue-600 text-white" // Page replies in blue
                          : "bg-gray-100 text-gray-900" // User messages in gray
                      )}
                    >
                      <p className="text-sm">{message.content}</p>
                      <div className={cn(
                        "flex items-center justify-end mt-1 space-x-1",
                        isFromPage ? "text-blue-100" : "text-gray-500"
                      )}>
                        <MessageTime
                          date={message.createdAt}
                          className="text-xs"
                          autoUpdate={false}
                        />
                        {isFromPage && (
                          message.read ? (
                            <CheckCircleIcon className="h-3 w-3" />
                          ) : (
                            <CheckIcon className="h-3 w-3" />
                          )
                        )}
                      </div>
                    </div>

                    {isFromPage && <div className="w-8 h-8 flex-shrink-0" />}
                  </div>
                );
              })}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <form onSubmit={handleSendMessage} className="flex items-end space-x-2">
          <div className="flex-1">
            <div className="relative">
              <Input
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                placeholder="Type a message..."
                className="pr-20"
                disabled={isSending}
                maxLength={5000}
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                <Button
                  type="button"
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  disabled={isSending}
                >
                  <PaperClipIcon className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  disabled={isSending}
                >
                  <FaceSmileIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <Button
            type="submit"
            disabled={!messageInput.trim() || isSending}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSending ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
            ) : (
              <PaperAirplaneIcon className="h-4 w-4" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
