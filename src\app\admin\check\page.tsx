"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";

export default function AdminCheckPage() {
  const { data: session, status } = useSession();
  const [serverSession, setServerSession] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function checkServerSession() {
      try {
        const response = await fetch('/api/debug/session');
        const data = await response.json();
        setServerSession(data);
      } catch (error) {
        console.error('Error fetching server session:', error);
      } finally {
        setIsLoading(false);
      }
    }

    checkServerSession();
  }, []);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-3xl rounded-lg bg-white p-6 shadow-md">
        <h1 className="mb-6 text-2xl font-bold text-gray-900">Admin Session Check</h1>
        
        <div className="mb-6">
          <h2 className="mb-2 text-xl font-semibold text-gray-800">Client Session Status</h2>
          <div className="rounded-md bg-gray-50 p-4">
            <p><strong>Status:</strong> {status}</p>
            <p><strong>Is Admin:</strong> {session?.user?.isAdmin ? 'Yes' : 'No'}</p>
            <p><strong>User ID:</strong> {session?.user?.id || 'Not available'}</p>
            <p><strong>Email:</strong> {session?.user?.email || 'Not available'}</p>
            <p><strong>Role:</strong> {session?.user?.role || 'Not available'}</p>
          </div>
        </div>
        
        <div className="mb-6">
          <h2 className="mb-2 text-xl font-semibold text-gray-800">Server Session Status</h2>
          {isLoading ? (
            <p>Loading server session data...</p>
          ) : (
            <div className="rounded-md bg-gray-50 p-4">
              <p><strong>Has Session:</strong> {serverSession?.hasSession ? 'Yes' : 'No'}</p>
              <p><strong>Is Admin:</strong> {serverSession?.isAdmin ? 'Yes' : 'No'}</p>
              <pre className="mt-4 max-h-60 overflow-auto rounded-md bg-gray-100 p-2 text-xs">
                {JSON.stringify(serverSession, null, 2)}
              </pre>
            </div>
          )}
        </div>
        
        <div className="flex flex-wrap gap-4">
          <Link 
            href="/admin/login" 
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Go to Admin Login
          </Link>
          <Link 
            href="/admin/dashboard" 
            className="rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700"
          >
            Go to Admin Dashboard
          </Link>
          <Link 
            href="/" 
            className="rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
          >
            Go to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
