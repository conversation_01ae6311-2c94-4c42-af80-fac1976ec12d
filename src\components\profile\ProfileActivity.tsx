"use client";

import { useState, useEffect, useCallback } from "react";
import {
  ClockIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  ShareIcon,
  UserPlusIcon,
  PhotoIcon,
  DocumentTextIcon
} from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

interface Activity {
  id: string;
  type: 'post_created' | 'post_liked' | 'comment_created' | 'post_shared' | 'user_subscribed' | 'photo_uploaded';
  createdAt: string;
  user: {
    id: string;
    name: string;
    username: string;
    image: string | null;
  };
  post?: {
    id: string;
    content: string;
    user: {
      name: string;
      username: string;
    };
  };
  targetUser?: {
    id: string;
    name: string;
    username: string;
  };
  photo?: {
    id: string;
    url: string;
  };
}

interface ProfileActivityProps {
  userId: string;
  isOwnProfile: boolean;
}

export function ProfileActivity({ userId, isOwnProfile }: ProfileActivityProps) {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'posts' | 'likes' | 'comments' | 'subscriptions'>('all');

  const fetchActivities = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/users/${userId}/activity?filter=${filter}`);

      if (response.ok) {
        const data = await response.json();
        setActivities(data.activities || []);
      } else {
        console.error('Failed to fetch activities');
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setIsLoading(false);
    }
  }, [userId, filter]);

  useEffect(() => {
    fetchActivities();
  }, [fetchActivities]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'post_created':
        return <DocumentTextIcon className="h-5 w-5 text-blue-500" />;
      case 'post_liked':
        return <HeartIcon className="h-5 w-5 text-red-500" />;
      case 'comment_created':
        return <ChatBubbleLeftIcon className="h-5 w-5 text-green-500" />;
      case 'post_shared':
        return <ShareIcon className="h-5 w-5 text-purple-500" />;
      case 'user_subscribed':
        return <UserPlusIcon className="h-5 w-5 text-indigo-500" />;
      case 'photo_uploaded':
        return <PhotoIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getActivityMessage = (activity: Activity) => {
    switch (activity.type) {
      case 'post_created':
        return 'created a new post';
      case 'post_liked':
        return `liked ${activity.post?.user.name}'s post`;
      case 'comment_created':
        return `commented on ${activity.post?.user.name}'s post`;
      case 'post_shared':
        return `shared ${activity.post?.user.name}'s post`;
      case 'user_subscribed':
        return `subscribed to ${activity.targetUser?.name}`;
      case 'photo_uploaded':
        return 'uploaded a new photo';
      default:
        return 'had some activity';
    }
  };

  const ActivityItem = ({ activity }: { activity: Activity }) => (
    <div className="flex items-start space-x-3 p-4 hover:bg-gray-50 rounded-lg transition-colors duration-200">
      <div className="flex-shrink-0">
        <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200">
          {activity.user.image ? (
            <Image
              src={activity.user.image}
              alt={activity.user.name}
              width={40}
              height={40}
              className="h-full w-full object-cover"
            />
          ) : (
            <div className="h-full w-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold">
              {activity.user.name.charAt(0).toUpperCase()}
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          {getActivityIcon(activity.type)}
          <p className="text-sm text-gray-900">
            <Link href={`/user/${activity.user.username}`} className="font-medium hover:text-blue-600">
              {activity.user.name}
            </Link>
            {' '}
            {getActivityMessage(activity)}
          </p>
        </div>

        {activity.post && (
          <div className="mt-2 p-3 bg-gray-100 rounded-lg">
            <p className="text-sm text-gray-700 line-clamp-2">
              {activity.post.content}
            </p>
          </div>
        )}

        {activity.photo && (
          <div className="mt-2">
            <Image
              src={activity.photo.url}
              alt="Uploaded photo"
              width={200}
              height={150}
              className="rounded-lg object-cover"
            />
          </div>
        )}

        <p className="text-xs text-gray-500 mt-2">
          {formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
        </p>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-start space-x-3">
                  <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header with Filters */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Recent Activity</h2>

        <div className="flex flex-wrap gap-2">
          {[
            { key: 'all', label: 'All Activity' },
            { key: 'posts', label: 'Posts' },
            { key: 'likes', label: 'Likes' },
            { key: 'comments', label: 'Comments' },
            { key: 'subscriptions', label: 'Subscriptions' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key as any)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${
                filter === filterOption.key
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {filterOption.label}
            </button>
          ))}
        </div>
      </div>

      {/* Activity Feed */}
      <div className="bg-white rounded-xl shadow-sm">
        {activities.length > 0 ? (
          <div className="divide-y divide-gray-100">
            {activities.map((activity) => (
              <ActivityItem key={activity.id} activity={activity} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <ClockIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No activity yet</h3>
            <p className="text-gray-500">
              {isOwnProfile
                ? "Your activity will appear here as you interact with posts and users."
                : "This user's activity will appear here."
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
