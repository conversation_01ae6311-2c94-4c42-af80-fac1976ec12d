import { db } from "@/lib/db";
import {
  wallets,
  walletTransactions,
  agents,
  paymentGateways,
  walletSettings,
  pinCodes,
  cashoutRequests,
  userPaymentMethods,
  users
} from "@/lib/db/schema";
import { eq, and, desc, sum, count, gte, lte, or, like } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import bcrypt from "bcryptjs";

export interface WalletBalance {
  generalBalance: string;
  earningBalance: string;
  totalDeposited: string;
  totalWithdrawn: string;
  totalSent: string;
  totalReceived: string;
}

export interface TransactionData {
  type: 'deposit' | 'send' | 'receive' | 'cashout' | 'internal_transfer' | 'earning' | 'withdraw';
  amount: string;
  fee?: string;
  walletType: 'general' | 'earning';
  toUserId?: string;
  toAgentId?: string;
  fromWalletType?: 'general' | 'earning';
  toWalletType?: 'general' | 'earning';
  paymentGateway?: string;
  gatewayTransactionId?: string;
  reference?: string;
  note?: string;
  metadata?: any;
}

export interface DepositData {
  amount: string;
  paymentGateway: string;
  gatewayTransactionId?: string;
  metadata?: any;
}

export interface SendMoneyData {
  toUserId: string;
  amount: string;
  note?: string;
  pin: string;
}

export interface CashoutData {
  agentId: string;
  amount: string;
  note?: string;
  pin: string;
}

export interface InternalTransferData {
  amount: string;
  fromWalletType: 'earning';
  toWalletType: 'general';
  pin: string;
}

export interface WithdrawData {
  amount: string;
  accountType: string;
  accountNumber: string;
  accountName: string;
  note?: string;
  pin: string;
}

export class WalletService {
  // Get or create wallet for user
  static async getOrCreateWallet(userId: string): Promise<WalletBalance> {
    let wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (!wallet) {
      const walletId = uuidv4();
      await db.insert(wallets).values({
        id: walletId,
        userId,
        generalBalance: "0.00",
        earningBalance: "0.00",
        totalDeposited: "0.00",
        totalWithdrawn: "0.00",
        totalSent: "0.00",
        totalReceived: "0.00",
        isActive: true,
      });

      wallet = await db.query.wallets.findFirst({
        where: eq(wallets.userId, userId),
      });
    }

    return {
      generalBalance: wallet!.generalBalance,
      earningBalance: wallet!.earningBalance,
      totalDeposited: wallet!.totalDeposited,
      totalWithdrawn: wallet!.totalWithdrawn,
      totalSent: wallet!.totalSent,
      totalReceived: wallet!.totalReceived,
    };
  }

  // Create transaction record
  static async createTransaction(userId: string, data: TransactionData): Promise<string> {
    const transactionId = uuidv4();
    const netAmount = data.fee
      ? (parseFloat(data.amount) - parseFloat(data.fee)).toFixed(2)
      : data.amount;

    await db.insert(walletTransactions).values({
      id: transactionId,
      userId,
      type: data.type,
      amount: data.amount,
      fee: data.fee || "0.00",
      netAmount,
      walletType: data.walletType,
      toUserId: data.toUserId,
      toAgentId: data.toAgentId,
      fromWalletType: data.fromWalletType,
      toWalletType: data.toWalletType,
      status: 'pending',
      paymentGateway: data.paymentGateway,
      gatewayTransactionId: data.gatewayTransactionId,
      reference: data.reference,
      note: data.note,
      metadata: data.metadata,
    });

    return transactionId;
  }

  // Update transaction status
  static async updateTransactionStatus(
    transactionId: string,
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled',
    gatewayTransactionId?: string
  ): Promise<void> {
    const updateData: any = { status };
    if (gatewayTransactionId) {
      updateData.gatewayTransactionId = gatewayTransactionId;
    }

    await db.update(walletTransactions)
      .set(updateData)
      .where(eq(walletTransactions.id, transactionId));
  }

  // Update wallet balance
  static async updateWalletBalance(
    userId: string,
    walletType: 'general' | 'earning',
    amount: string,
    operation: 'add' | 'subtract'
  ): Promise<void> {
    const wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (!wallet) {
      throw new Error('Wallet not found');
    }

    const currentBalance = parseFloat(
      walletType === 'general' ? wallet.generalBalance : wallet.earningBalance
    );
    const changeAmount = parseFloat(amount);
    const newBalance = operation === 'add'
      ? currentBalance + changeAmount
      : currentBalance - changeAmount;

    if (newBalance < 0) {
      throw new Error('Insufficient balance');
    }

    const updateData = walletType === 'general'
      ? { generalBalance: newBalance.toFixed(2) }
      : { earningBalance: newBalance.toFixed(2) };

    await db.update(wallets)
      .set(updateData)
      .where(eq(wallets.userId, userId));
  }

  // Process deposit
  static async processDeposit(userId: string, data: DepositData): Promise<string> {
    // Check if deposits are enabled
    const depositEnabled = await this.getWalletSetting('deposit_enabled');
    if (depositEnabled === 'false') {
      throw new Error('Deposits are currently disabled');
    }

    // Get gateway settings for payment processing
    const gateway = await db.query.paymentGateways.findFirst({
      where: and(
        eq(paymentGateways.name, data.paymentGateway),
        eq(paymentGateways.isActive, true)
      ),
    });

    if (!gateway) {
      throw new Error('Payment gateway not available');
    }

    // Get admin-configured deposit fee settings
    const depositFeePercentage = parseFloat(await this.getWalletSetting('deposit_fee_percentage') || '0.00');
    const depositFeeFixed = parseFloat(await this.getWalletSetting('deposit_fee_fixed') || '0.00');
    const depositMinAmount = parseFloat(await this.getWalletSetting('deposit_min_amount') || '1.00');
    const depositMaxAmount = parseFloat(await this.getWalletSetting('deposit_max_amount') || '10000.00');

    const amount = parseFloat(data.amount);

    // Validate deposit limits
    if (amount < depositMinAmount) {
      throw new Error(`Minimum deposit amount is $${depositMinAmount.toFixed(2)}`);
    }
    if (amount > depositMaxAmount) {
      throw new Error(`Maximum deposit amount is $${depositMaxAmount.toFixed(2)}`);
    }

    // Calculate fees using admin settings
    const percentageFee = (amount * depositFeePercentage) / 100;
    const totalFee = percentageFee + depositFeeFixed;
    const netAmount = amount - totalFee;

    // Create transaction
    const transactionId = await this.createTransaction(userId, {
      type: 'deposit',
      amount: data.amount,
      fee: totalFee.toFixed(2),
      walletType: 'general',
      paymentGateway: data.paymentGateway,
      gatewayTransactionId: data.gatewayTransactionId,
      metadata: data.metadata,
    });

    return transactionId;
  }

  // Complete deposit (called after payment gateway confirmation)
  static async completeDeposit(transactionId: string): Promise<void> {
    const transaction = await db.query.walletTransactions.findFirst({
      where: eq(walletTransactions.id, transactionId),
    });

    if (!transaction || transaction.type !== 'deposit') {
      throw new Error('Invalid deposit transaction');
    }

    // Update wallet balance
    await this.updateWalletBalance(
      transaction.userId,
      'general',
      transaction.netAmount,
      'add'
    );

    // Update transaction status
    await this.updateTransactionStatus(transactionId, 'completed');

    // Update total deposited
    await db.update(wallets)
      .set({
        totalDeposited: sql`totalDeposited + ${transaction.netAmount}`
      })
      .where(eq(wallets.userId, transaction.userId));
  }

  // Verify PIN
  static async verifyPin(userId: string, pin: string): Promise<boolean> {
    const pinRecord = await db.query.pinCodes.findFirst({
      where: and(
        eq(pinCodes.userId, userId),
        eq(pinCodes.isActive, true)
      ),
    });

    if (!pinRecord) {
      throw new Error('PIN not set');
    }

    // Check if account is locked
    if (pinRecord.lockedUntil && new Date() < pinRecord.lockedUntil) {
      throw new Error('Account temporarily locked due to failed attempts');
    }

    const isValid = await bcrypt.compare(pin, pinRecord.pinHash);

    if (!isValid) {
      // Increment failed attempts
      const newFailedAttempts = pinRecord.failedAttempts + 1;
      const updateData: any = { failedAttempts: newFailedAttempts };

      // Lock account after 3 failed attempts
      if (newFailedAttempts >= 3) {
        updateData.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      }

      await db.update(pinCodes)
        .set(updateData)
        .where(eq(pinCodes.id, pinRecord.id));

      throw new Error('Invalid PIN');
    }

    // Reset failed attempts on successful verification
    await db.update(pinCodes)
      .set({
        failedAttempts: 0,
        lockedUntil: null,
        lastUsedAt: new Date()
      })
      .where(eq(pinCodes.id, pinRecord.id));

    return true;
  }

  // Send money to another user
  static async sendMoney(userId: string, data: SendMoneyData): Promise<string> {
    // Verify PIN
    await this.verifyPin(userId, data.pin);

    // Check if sending is enabled
    const sendEnabled = await this.getWalletSetting('send_enabled');
    if (sendEnabled === 'false') {
      throw new Error('Money transfers are currently disabled');
    }

    // Check if recipient exists
    const recipient = await db.query.users.findFirst({
      where: eq(users.id, data.toUserId),
    });

    if (!recipient) {
      throw new Error('Recipient not found');
    }

    // Get admin-configured send money fee settings
    const sendFeePercentage = parseFloat(await this.getWalletSetting('send_fee_percentage') || '1.00');
    const sendFeeFixed = parseFloat(await this.getWalletSetting('send_fee_fixed') || '0.00');
    const sendMinAmount = parseFloat(await this.getWalletSetting('send_min_amount') || '1.00');
    const sendMaxAmount = parseFloat(await this.getWalletSetting('send_max_amount') || '5000.00');

    const amount = parseFloat(data.amount);

    // Validate send money limits
    if (amount < sendMinAmount) {
      throw new Error(`Minimum send amount is $${sendMinAmount.toFixed(2)}`);
    }
    if (amount > sendMaxAmount) {
      throw new Error(`Maximum send amount is $${sendMaxAmount.toFixed(2)}`);
    }

    // Calculate fees using admin settings
    const percentageFee = (amount * sendFeePercentage) / 100;
    const totalFee = percentageFee + sendFeeFixed;
    const totalAmountNeeded = amount + totalFee;

    // Check sender balance (amount + fees)
    const senderWallet = await this.getOrCreateWallet(userId);
    if (parseFloat(senderWallet.generalBalance) < totalAmountNeeded) {
      throw new Error(`Insufficient balance. You need $${totalAmountNeeded.toFixed(2)} (including $${totalFee.toFixed(2)} fee)`);
    }

    // Create send transaction
    const sendTransactionId = await this.createTransaction(userId, {
      type: 'send',
      amount: data.amount,
      fee: totalFee.toFixed(2),
      walletType: 'general',
      toUserId: data.toUserId,
      note: data.note,
      metadata: {
        feeBreakdown: {
          percentage: sendFeePercentage,
          percentageFee: percentageFee.toFixed(2),
          fixedFee: sendFeeFixed.toFixed(2),
          totalFee: totalFee.toFixed(2),
        },
      },
    });

    // Create receive transaction
    const receiveTransactionId = await this.createTransaction(data.toUserId, {
      type: 'receive',
      amount: data.amount,
      fee: "0.00",
      walletType: 'general',
      reference: sendTransactionId,
    });

    // Update balances (deduct amount + fee from sender, add only amount to receiver)
    await this.updateWalletBalance(userId, 'general', totalAmountNeeded.toFixed(2), 'subtract');
    await this.updateWalletBalance(data.toUserId, 'general', data.amount, 'add');

    // Update transaction statuses
    await this.updateTransactionStatus(sendTransactionId, 'completed');
    await this.updateTransactionStatus(receiveTransactionId, 'completed');

    // Update wallet statistics
    await db.update(wallets)
      .set({
        totalSent: sql`totalSent + ${data.amount}`
      })
      .where(eq(wallets.userId, userId));

    await db.update(wallets)
      .set({
        totalReceived: sql`totalReceived + ${data.amount}`
      })
      .where(eq(wallets.userId, data.toUserId));

    return sendTransactionId;
  }

  // Request cashout to agent
  static async requestCashout(userId: string, data: CashoutData): Promise<string> {
    // Verify PIN
    await this.verifyPin(userId, data.pin);

    // Check if agent exists and is active
    const agent = await db.query.agents.findFirst({
      where: and(
        eq(agents.id, data.agentId),
        eq(agents.isActive, true),
        eq(agents.isVerified, true)
      ),
    });

    if (!agent) {
      throw new Error('Agent not available');
    }

    // Check user earning balance
    const userWallet = await this.getOrCreateWallet(userId);
    if (parseFloat(userWallet.earningBalance) < parseFloat(data.amount)) {
      throw new Error('Insufficient earning balance');
    }

    // Calculate agent commission
    const commission = (parseFloat(data.amount) * parseFloat(agent.commission)) / 100;
    const netAmount = parseFloat(data.amount) - commission;

    // Create cashout request
    const requestId = uuidv4();
    await db.insert(cashoutRequests).values({
      id: requestId,
      userId,
      agentId: data.agentId,
      amount: data.amount,
      fee: commission.toFixed(2),
      netAmount: netAmount.toFixed(2),
      status: 'pending',
      note: data.note,
    });

    return requestId;
  }

  // Internal transfer (earning to general)
  static async internalTransfer(userId: string, data: InternalTransferData): Promise<string> {
    // Verify PIN
    await this.verifyPin(userId, data.pin);

    // Check earning balance
    const userWallet = await this.getOrCreateWallet(userId);
    if (parseFloat(userWallet.earningBalance) < parseFloat(data.amount)) {
      throw new Error('Insufficient earning balance');
    }

    // Create transaction
    const transactionId = await this.createTransaction(userId, {
      type: 'internal_transfer',
      amount: data.amount,
      fee: "0.00",
      walletType: 'earning',
      fromWalletType: 'earning',
      toWalletType: 'general',
    });

    // Update balances
    await this.updateWalletBalance(userId, 'earning', data.amount, 'subtract');
    await this.updateWalletBalance(userId, 'general', data.amount, 'add');

    // Update transaction status
    await this.updateTransactionStatus(transactionId, 'completed');

    return transactionId;
  }

  // Get wallet setting
  static async getWalletSetting(key: string): Promise<string | null> {
    const setting = await db.query.walletSettings.findFirst({
      where: eq(walletSettings.key, key),
    });

    return setting?.value || null;
  }

  // Get user transactions
  static async getUserTransactions(
    userId: string,
    page: number = 1,
    limit: number = 20,
    type?: string,
    dateFrom?: Date,
    dateTo?: Date
  ) {
    const offset = (page - 1) * limit;

    let whereConditions = [eq(walletTransactions.userId, userId)];

    if (type) {
      whereConditions.push(eq(walletTransactions.type, type as any));
    }

    if (dateFrom) {
      whereConditions.push(gte(walletTransactions.createdAt, dateFrom));
    }

    if (dateTo) {
      whereConditions.push(lte(walletTransactions.createdAt, dateTo));
    }

    const transactions = await db.query.walletTransactions.findMany({
      where: and(...whereConditions),
      orderBy: [desc(walletTransactions.createdAt)],
      limit,
      offset,
      with: {
        toUser: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        toAgent: {
          columns: {
            id: true,
            name: true,
            serviceType: true,
          },
        },
      },
    });

    const totalCount = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(and(...whereConditions));

    return {
      transactions,
      totalCount: totalCount[0].count,
      totalPages: Math.ceil(totalCount[0].count / limit),
      currentPage: page,
    };
  }

  // Get user pending payouts (withdraw/cashout requests)
  static async getUserPendingPayouts(userId: string) {
    const transactions = await db.query.walletTransactions.findMany({
      where: and(
        eq(walletTransactions.userId, userId),
        eq(walletTransactions.walletType, 'earning'),
        or(
          eq(walletTransactions.type, 'withdraw'),
          eq(walletTransactions.type, 'cashout')
        ),
        eq(walletTransactions.status, 'pending')
      ),
      orderBy: [desc(walletTransactions.createdAt)],
    });

    // Calculate total pending amount
    const totalAmount = transactions.reduce((total, transaction) => {
      return total + Math.abs(parseFloat(transaction.amount));
    }, 0);

    return {
      transactions,
      totalAmount,
      count: transactions.length,
    };
  }

  // Set or update PIN
  static async setPin(userId: string, pin: string): Promise<void> {
    const pinHash = await bcrypt.hash(pin, 12);

    // Check if PIN already exists
    const existingPin = await db.query.pinCodes.findFirst({
      where: eq(pinCodes.userId, userId),
    });

    if (existingPin) {
      // Update existing PIN
      await db.update(pinCodes)
        .set({
          pinHash,
          isActive: true,
          failedAttempts: 0,
          lockedUntil: null
        })
        .where(eq(pinCodes.userId, userId));
    } else {
      // Create new PIN
      const pinId = uuidv4();
      await db.insert(pinCodes).values({
        id: pinId,
        userId,
        pinHash,
        isActive: true,
        failedAttempts: 0,
      });
    }
  }

  // Get active payment gateways
  static async getActivePaymentGateways() {
    return await db.query.paymentGateways.findMany({
      where: eq(paymentGateways.isActive, true),
      orderBy: [paymentGateways.sortOrder],
    });
  }

  // Get all payment gateways (admin only)
  static async getAllPaymentGateways() {
    return await db.query.paymentGateways.findMany({
      orderBy: [paymentGateways.sortOrder, paymentGateways.name],
    });
  }

  // Get payment gateway by ID
  static async getPaymentGatewayById(id: string) {
    return await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.id, id),
    });
  }

  // Create payment gateway
  static async createPaymentGateway(data: any) {
    const gatewayId = uuidv4();
    await db.insert(paymentGateways).values({
      id: gatewayId,
      ...data,
    });
    return gatewayId;
  }

  // Update payment gateway
  static async updatePaymentGateway(id: string, data: any) {
    await db.update(paymentGateways)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(paymentGateways.id, id));
  }

  // Delete payment gateway
  static async deletePaymentGateway(id: string) {
    await db.delete(paymentGateways)
      .where(eq(paymentGateways.id, id));
  }

  // Toggle gateway status
  static async toggleGatewayStatus(id: string, isActive: boolean) {
    await db.update(paymentGateways)
      .set({
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(paymentGateways.id, id));
  }

  // Get active agents
  static async getActiveAgents() {
    return await db.query.agents.findMany({
      where: and(
        eq(agents.isActive, true),
        eq(agents.isVerified, true)
      ),
      orderBy: [desc(agents.rating), agents.name],
    });
  }

  // Request withdraw from earning wallet
  static async requestWithdraw(userId: string, data: WithdrawData): Promise<string> {
    // Verify PIN
    await this.verifyPin(userId, data.pin);

    // Check if withdrawals are enabled
    const withdrawEnabled = await this.getWalletSetting('withdraw_enabled');
    if (withdrawEnabled === 'false') {
      throw new Error('Withdrawals are currently disabled');
    }

    // Check withdrawal limits
    const minAmount = parseFloat(await this.getWalletSetting('withdraw_min_amount') || '10.00');
    const maxAmount = parseFloat(await this.getWalletSetting('withdraw_max_amount') || '10000.00');
    const amount = parseFloat(data.amount);

    if (amount < minAmount) {
      throw new Error(`Minimum withdrawal amount is $${minAmount.toFixed(2)}`);
    }

    if (amount > maxAmount) {
      throw new Error(`Maximum withdrawal amount is $${maxAmount.toFixed(2)}`);
    }

    // Check user earning balance
    const userWallet = await this.getOrCreateWallet(userId);
    if (parseFloat(userWallet.earningBalance) < amount) {
      throw new Error('Insufficient earning balance');
    }

    // Calculate withdrawal fees
    const feePercentage = parseFloat(await this.getWalletSetting('withdraw_fee_percentage') || '2.00');
    const feeFixed = parseFloat(await this.getWalletSetting('withdraw_fee_fixed') || '0.00');

    const percentageFee = (amount * feePercentage) / 100;
    const totalFee = percentageFee + feeFixed;
    const netAmount = amount - totalFee;

    // Check if user has enough balance including fees
    if (parseFloat(userWallet.earningBalance) < amount) {
      throw new Error('Insufficient balance to cover withdrawal amount and fees');
    }

    // Create withdraw transaction
    const transactionId = await this.createTransaction(userId, {
      type: 'withdraw',
      amount: data.amount,
      fee: totalFee.toFixed(2),
      walletType: 'earning',
      metadata: {
        accountType: data.accountType,
        accountNumber: data.accountNumber,
        accountName: data.accountName,
        netAmount: netAmount.toFixed(2),
        feeBreakdown: {
          percentage: feePercentage,
          percentageFee: percentageFee.toFixed(2),
          fixedFee: feeFixed.toFixed(2),
          totalFee: totalFee.toFixed(2),
        },
      },
      note: data.note,
    });

    // Deduct amount from earning balance
    await this.updateWalletBalance(
      userId,
      'earning',
      data.amount,
      'subtract'
    );

    // Update total withdrawn
    await db.update(wallets)
      .set({
        totalWithdrawn: sql`totalWithdrawn + ${data.amount}`
      })
      .where(eq(wallets.userId, userId));

    return transactionId;
  }

  // Get user's withdraw requests
  static async getUserWithdrawRequests(userId: string) {
    return await db.query.walletTransactions.findMany({
      where: and(
        eq(walletTransactions.userId, userId),
        eq(walletTransactions.type, 'withdraw')
      ),
      orderBy: [desc(walletTransactions.createdAt)],
      limit: 50,
    });
  }

  // Payment Methods Management

  // Get user's payment methods
  static async getUserPaymentMethods(userId: string, methodType?: 'payment' | 'payout') {
    const whereConditions = [
      eq(userPaymentMethods.userId, userId),
      eq(userPaymentMethods.isActive, true)
    ];

    if (methodType) {
      whereConditions.push(eq(userPaymentMethods.methodType, methodType));
    }

    return await db.query.userPaymentMethods.findMany({
      where: and(...whereConditions),
      orderBy: [desc(userPaymentMethods.isDefault), desc(userPaymentMethods.createdAt)],
    });
  }

  // Add payment method
  static async addPaymentMethod(
    userId: string,
    type: 'bank' | 'mobile_banking' | 'card',
    methodType: 'payment' | 'payout',
    name: string,
    details: any
  ): Promise<string> {
    const methodId = uuidv4();

    // Check if this is the first payment method of this type (make it default)
    const existingMethods = await this.getUserPaymentMethods(userId, methodType);
    const isDefault = existingMethods.length === 0;

    await db.insert(userPaymentMethods).values({
      id: methodId,
      userId,
      type,
      methodType,
      name,
      details,
      isDefault,
      isActive: true,
    });

    return methodId;
  }

  // Get payment method by ID
  static async getPaymentMethodById(id: string) {
    return await db.query.userPaymentMethods.findFirst({
      where: eq(userPaymentMethods.id, id),
    });
  }

  // Set default payment method
  static async setDefaultPaymentMethod(userId: string, methodId: string): Promise<void> {
    // First, remove default from all user's methods
    await db.update(userPaymentMethods)
      .set({ isDefault: false })
      .where(eq(userPaymentMethods.userId, userId));

    // Then set the selected method as default
    await db.update(userPaymentMethods)
      .set({ isDefault: true })
      .where(and(
        eq(userPaymentMethods.id, methodId),
        eq(userPaymentMethods.userId, userId)
      ));
  }

  // Delete payment method
  static async deletePaymentMethod(id: string): Promise<void> {
    console.log('WalletService: Deleting payment method with ID:', id);

    const result = await db.update(userPaymentMethods)
      .set({ isActive: false })
      .where(eq(userPaymentMethods.id, id));

    console.log('WalletService: Delete result:', result);
  }

  // PIN Management

  // Check if user has PIN
  static async hasPin(userId: string): Promise<boolean> {
    const pinRecord = await db.query.pinCodes.findFirst({
      where: and(
        eq(pinCodes.userId, userId),
        eq(pinCodes.isActive, true)
      ),
    });
    return !!pinRecord;
  }

  // Get PIN info
  static async getPinInfo(userId: string) {
    return await db.query.pinCodes.findFirst({
      where: and(
        eq(pinCodes.userId, userId),
        eq(pinCodes.isActive, true)
      ),
    });
  }

  // Get daily limits from settings
  static async getDailyLimits() {
    const settings = await db.query.walletSettings.findMany({
      where: or(
        eq(walletSettings.key, 'daily_deposit_limit'),
        eq(walletSettings.key, 'daily_send_limit'),
        eq(walletSettings.key, 'daily_cashout_limit')
      ),
    });

    const limits: any = {};
    settings.forEach(setting => {
      if (setting.key === 'daily_deposit_limit') {
        limits.dailyDepositLimit = setting.value;
      } else if (setting.key === 'daily_send_limit') {
        limits.dailySendLimit = setting.value;
      } else if (setting.key === 'daily_cashout_limit') {
        limits.dailyCashoutLimit = setting.value;
      }
    });

    return limits;
  }
}

// Import sql function for raw SQL operations
import { sql } from "drizzle-orm";
