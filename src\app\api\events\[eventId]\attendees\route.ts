import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventAttendees, users, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and } from "drizzle-orm";

const attendeeSchema = z.object({
  status: z.enum(["going", "interested", "not_going"]),
});

// Get all attendees for an event
export async function GET(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Get the status filter from query params
    const url = new URL(req.url);
    const status = url.searchParams.get("status") as "going" | "interested" | "not_going" | null;

    // Fetch attendees with user information
    let query = db.select({
      id: eventAttendees.id,
      status: eventAttendees.status,
      createdAt: eventAttendees.createdAt,
      user: {
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
      },
    })
    .from(eventAttendees)
    .innerJoin(users, eq(eventAttendees.userId, users.id))
    .where(eq(eventAttendees.eventId, eventId));

    // Apply status filter if provided
    if (status) {
      query = db.select({
        id: eventAttendees.id,
        status: eventAttendees.status,
        createdAt: eventAttendees.createdAt,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
      })
      .from(eventAttendees)
      .innerJoin(users, eq(eventAttendees.userId, users.id))
      .where(and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.status, status)
      ));
    }

    const attendees = await query;

    return NextResponse.json(attendees);
  } catch (error) {
    console.error("Error fetching event attendees:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// RSVP to an event
export async function POST(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;
    const body = await req.json();
    const validatedData = attendeeSchema.parse(body);

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the user is already attending
    const existingAttendee = await db.query.eventAttendees.findFirst({
      where: and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.userId, session.user.id)
      ),
    });

    if (existingAttendee) {
      // Update the existing attendance
      await db
        .update(eventAttendees)
        .set({ status: validatedData.status })
        .where(eq(eventAttendees.id, existingAttendee.id));

      // Fetch the updated attendee
      const updatedAttendee = await db.query.eventAttendees.findFirst({
        where: eq(eventAttendees.id, existingAttendee.id),
      });

      if (!updatedAttendee) {
        return NextResponse.json(
          { message: "Failed to update attendance" },
          { status: 500 }
        );
      }

      return NextResponse.json(updatedAttendee);
    } else {
      // Generate a new UUID for the attendance
      const attendeeId = uuidv4();

      // Create a new attendance
      await db
        .insert(eventAttendees)
        .values({
          id: attendeeId,
          eventId,
          userId: session.user.id,
          status: validatedData.status,
        });

      // Fetch the new attendee
      const newAttendee = await db.query.eventAttendees.findFirst({
        where: eq(eventAttendees.id, attendeeId),
      });

      if (!newAttendee) {
        return NextResponse.json(
          { message: "Failed to create attendance" },
          { status: 500 }
        );
      }

      // If the user is going or interested, notify the event host
      if (validatedData.status !== "not_going" && event.hostId !== session.user.id) {
        await db.insert(notifications).values({
          id: uuidv4(),
          recipientId: event.hostId,
          type: "event_update",
          senderId: session.user.id,
          eventId,
          read: false,
        });
      }

      return NextResponse.json(newAttendee);
    }
  } catch (error) {
    console.error("Error updating event attendance:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete attendance (cancel RSVP)
export async function DELETE(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Delete the attendance
    await db
      .delete(eventAttendees)
      .where(
        and(
          eq(eventAttendees.eventId, eventId),
          eq(eventAttendees.userId, session.user.id)
        )
      );

    return NextResponse.json({ message: "Attendance removed successfully" });
  } catch (error) {
    console.error("Error removing event attendance:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
