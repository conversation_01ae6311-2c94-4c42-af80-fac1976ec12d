"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/Tabs";
import { toast } from "react-hot-toast";

export default function AdminUIControlsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("layout");
  const [settings, setSettings] = useState({
    ui: {
      // Layout settings
      layout_width: "1280px",
      sidebar_width: "280px",
      enable_right_sidebar: "true",
      content_padding: "16px",
      
      // Colors settings
      primary_color: "#3b82f6",
      secondary_color: "#10b981",
      background_color: "#f3f4f6",
      text_color: "#111827",
      link_color: "#2563eb",
      
      // Typography settings
      base_font_size: "16px",
      heading_font: "Inter, sans-serif",
      body_font: "Inter, sans-serif",
      
      // Components settings
      button_radius: "6px",
      card_radius: "8px",
      input_radius: "6px",
      shadow_intensity: "medium",
    }
  });
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    async function fetchSettings() {
      try {
        setIsLoading(true);
        const response = await fetch('/api/admin/settings');

        if (!response.ok) {
          throw new Error('Failed to fetch settings');
        }

        const data = await response.json();
        
        // If UI settings exist in the response, use them
        if (data.ui) {
          setSettings(prevSettings => ({
            ...prevSettings,
            ui: {
              ...prevSettings.ui,
              ...data.ui
            }
          }));
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching UI settings:', error);
        setErrorMessage('Failed to load UI settings. Please try again.');
        setIsLoading(false);
      }
    }

    fetchSettings();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setSettings(prevSettings => ({
      ...prevSettings,
      ui: {
        ...prevSettings.ui,
        [name]: value
      }
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    
    setSettings(prevSettings => ({
      ...prevSettings,
      ui: {
        ...prevSettings.ui,
        [name]: checked.toString()
      }
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSuccessMessage("");
      setErrorMessage("");

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ui: settings.ui }),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      setSuccessMessage('UI settings saved successfully!');
      toast.success('UI settings saved successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage("");
      }, 3000);
    } catch (error) {
      console.error('Error saving UI settings:', error);
      setErrorMessage('Failed to save UI settings. Please try again.');
      toast.error('Failed to save UI settings');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">UI Controls</h1>
        <p className="mt-1 text-sm text-gray-500">
          Customize the appearance and layout of your site
        </p>
      </div>

      {successMessage && (
        <div className="mb-4 rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="mb-4 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      <div className="overflow-hidden rounded-lg bg-white shadow">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="border-b border-gray-200">
            <TabsList className="flex">
              <TabsTrigger
                value="layout"
                className="px-4 py-2 text-sm font-medium"
              >
                Layout
              </TabsTrigger>
              <TabsTrigger
                value="colors"
                className="px-4 py-2 text-sm font-medium"
              >
                Colors
              </TabsTrigger>
              <TabsTrigger
                value="typography"
                className="px-4 py-2 text-sm font-medium"
              >
                Typography
              </TabsTrigger>
              <TabsTrigger
                value="components"
                className="px-4 py-2 text-sm font-medium"
              >
                Components
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="p-6">
            <TabsContent value="layout" className="mt-0">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Layout Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Configure the layout dimensions and structure of your site
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="layout_width" className="block text-sm font-medium text-gray-700">
                      Layout Width
                    </label>
                    <Input
                      id="layout_width"
                      name="layout_width"
                      type="text"
                      value={settings.ui.layout_width}
                      onChange={handleInputChange}
                      className="mt-1"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Maximum width of the main content (e.g., 1280px)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="sidebar_width" className="block text-sm font-medium text-gray-700">
                      Sidebar Width
                    </label>
                    <Input
                      id="sidebar_width"
                      name="sidebar_width"
                      type="text"
                      value={settings.ui.sidebar_width}
                      onChange={handleInputChange}
                      className="mt-1"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Width of the sidebar (e.g., 280px)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="content_padding" className="block text-sm font-medium text-gray-700">
                      Content Padding
                    </label>
                    <Input
                      id="content_padding"
                      name="content_padding"
                      type="text"
                      value={settings.ui.content_padding}
                      onChange={handleInputChange}
                      className="mt-1"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Padding around content areas (e.g., 16px)
                    </p>
                  </div>

                  <div className="flex items-start pt-4">
                    <div className="flex h-5 items-center">
                      <input
                        id="enable_right_sidebar"
                        name="enable_right_sidebar"
                        type="checkbox"
                        checked={settings.ui.enable_right_sidebar === "true"}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="enable_right_sidebar" className="font-medium text-gray-700">
                        Enable Right Sidebar
                      </label>
                      <p className="text-gray-500">
                        Show the right sidebar on applicable pages
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="colors" className="mt-0">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Color Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Customize the color scheme of your site
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="primary_color" className="block text-sm font-medium text-gray-700">
                      Primary Color
                    </label>
                    <div className="mt-1 flex items-center">
                      <input
                        type="color"
                        id="primary_color"
                        name="primary_color"
                        value={settings.ui.primary_color}
                        onChange={handleInputChange}
                        className="h-8 w-8 rounded border-gray-300 p-0"
                      />
                      <Input
                        type="text"
                        value={settings.ui.primary_color}
                        onChange={handleInputChange}
                        name="primary_color"
                        className="ml-2"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Main brand color used for buttons, links, and accents
                    </p>
                  </div>

                  <div>
                    <label htmlFor="secondary_color" className="block text-sm font-medium text-gray-700">
                      Secondary Color
                    </label>
                    <div className="mt-1 flex items-center">
                      <input
                        type="color"
                        id="secondary_color"
                        name="secondary_color"
                        value={settings.ui.secondary_color}
                        onChange={handleInputChange}
                        className="h-8 w-8 rounded border-gray-300 p-0"
                      />
                      <Input
                        type="text"
                        value={settings.ui.secondary_color}
                        onChange={handleInputChange}
                        name="secondary_color"
                        className="ml-2"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Secondary brand color used for complementary elements
                    </p>
                  </div>

                  <div>
                    <label htmlFor="background_color" className="block text-sm font-medium text-gray-700">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center">
                      <input
                        type="color"
                        id="background_color"
                        name="background_color"
                        value={settings.ui.background_color}
                        onChange={handleInputChange}
                        className="h-8 w-8 rounded border-gray-300 p-0"
                      />
                      <Input
                        type="text"
                        value={settings.ui.background_color}
                        onChange={handleInputChange}
                        name="background_color"
                        className="ml-2"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Main background color for the site
                    </p>
                  </div>

                  <div>
                    <label htmlFor="text_color" className="block text-sm font-medium text-gray-700">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center">
                      <input
                        type="color"
                        id="text_color"
                        name="text_color"
                        value={settings.ui.text_color}
                        onChange={handleInputChange}
                        className="h-8 w-8 rounded border-gray-300 p-0"
                      />
                      <Input
                        type="text"
                        value={settings.ui.text_color}
                        onChange={handleInputChange}
                        name="text_color"
                        className="ml-2"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Main text color for the site
                    </p>
                  </div>

                  <div>
                    <label htmlFor="link_color" className="block text-sm font-medium text-gray-700">
                      Link Color
                    </label>
                    <div className="mt-1 flex items-center">
                      <input
                        type="color"
                        id="link_color"
                        name="link_color"
                        value={settings.ui.link_color}
                        onChange={handleInputChange}
                        className="h-8 w-8 rounded border-gray-300 p-0"
                      />
                      <Input
                        type="text"
                        value={settings.ui.link_color}
                        onChange={handleInputChange}
                        name="link_color"
                        className="ml-2"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Color used for links and interactive elements
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="typography" className="mt-0">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Typography Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Configure fonts and text styles for your site
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="base_font_size" className="block text-sm font-medium text-gray-700">
                      Base Font Size
                    </label>
                    <Input
                      id="base_font_size"
                      name="base_font_size"
                      type="text"
                      value={settings.ui.base_font_size}
                      onChange={handleInputChange}
                      className="mt-1"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Base font size for the site (e.g., 16px)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="heading_font" className="block text-sm font-medium text-gray-700">
                      Heading Font
                    </label>
                    <select
                      id="heading_font"
                      name="heading_font"
                      value={settings.ui.heading_font}
                      onChange={handleInputChange}
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="Inter, sans-serif">Inter</option>
                      <option value="Roboto, sans-serif">Roboto</option>
                      <option value="Open Sans, sans-serif">Open Sans</option>
                      <option value="Montserrat, sans-serif">Montserrat</option>
                      <option value="Poppins, sans-serif">Poppins</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      Font used for headings
                    </p>
                  </div>

                  <div>
                    <label htmlFor="body_font" className="block text-sm font-medium text-gray-700">
                      Body Font
                    </label>
                    <select
                      id="body_font"
                      name="body_font"
                      value={settings.ui.body_font}
                      onChange={handleInputChange}
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="Inter, sans-serif">Inter</option>
                      <option value="Roboto, sans-serif">Roboto</option>
                      <option value="Open Sans, sans-serif">Open Sans</option>
                      <option value="Montserrat, sans-serif">Montserrat</option>
                      <option value="Poppins, sans-serif">Poppins</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      Font used for body text
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="components" className="mt-0">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Component Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Customize the appearance of UI components
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="button_radius" className="block text-sm font-medium text-gray-700">
                      Button Border Radius
                    </label>
                    <Input
                      id="button_radius"
                      name="button_radius"
                      type="text"
                      value={settings.ui.button_radius}
                      onChange={handleInputChange}
                      className="mt-1"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Border radius for buttons (e.g., 6px)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="card_radius" className="block text-sm font-medium text-gray-700">
                      Card Border Radius
                    </label>
                    <Input
                      id="card_radius"
                      name="card_radius"
                      type="text"
                      value={settings.ui.card_radius}
                      onChange={handleInputChange}
                      className="mt-1"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Border radius for cards (e.g., 8px)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="input_radius" className="block text-sm font-medium text-gray-700">
                      Input Border Radius
                    </label>
                    <Input
                      id="input_radius"
                      name="input_radius"
                      type="text"
                      value={settings.ui.input_radius}
                      onChange={handleInputChange}
                      className="mt-1"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Border radius for input fields (e.g., 6px)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="shadow_intensity" className="block text-sm font-medium text-gray-700">
                      Shadow Intensity
                    </label>
                    <select
                      id="shadow_intensity"
                      name="shadow_intensity"
                      value={settings.ui.shadow_intensity}
                      onChange={handleInputChange}
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="none">None</option>
                      <option value="light">Light</option>
                      <option value="medium">Medium</option>
                      <option value="heavy">Heavy</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      Intensity of shadows on cards and components
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>

          <div className="bg-gray-50 px-6 py-4">
            <div className="flex items-center justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                disabled={isSaving}
              >
                Reset
              </Button>
              <Button
                onClick={handleSaveSettings}
                disabled={isSaving}
                isLoading={isSaving}
              >
                {isSaving ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          </div>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
