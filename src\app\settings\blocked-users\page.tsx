import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { SettingsSidebar } from "@/components/settings/SettingsSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { SettingsLayout } from "@/components/settings/SettingsLayout";
import { Button } from "@/components/ui/Button";
import Image from "next/image";
import { Input } from "@/components/ui/Input";

export default async function BlockedUsersPage() {
  const user = await requireAuth();

  // Mock data for blocked users
  const blockedUsers = [
    {
      id: "1",
      name: "<PERSON>",
      username: "johndo<PERSON>",
      image: "/placeholder-user.jpg",
      blockedDate: "2023-12-15",
    },
    {
      id: "2",
      name: "<PERSON>",
      username: "jane<PERSON>",
      image: "/placeholder-user.jpg",
      blockedDate: "2023-11-20",
    },
  ];

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Settings sidebar */}
          <div className="lg:col-span-1">
            <SettingsSidebar />
          </div>

          {/* Main content */}
          <div className="lg:col-span-2">
            <SettingsLayout 
              title="Blocked Users" 
              description="Manage the users you've blocked from interacting with you."
            >
              <div className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Input
                    type="search"
                    placeholder="Search blocked users..."
                    className="max-w-md"
                  />
                </div>
                
                {blockedUsers.length > 0 ? (
                  <div className="space-y-4">
                    {blockedUsers.map((user) => (
                      <div 
                        key={user.id} 
                        className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className="relative h-10 w-10 rounded-full overflow-hidden">
                              <Image
                                src={user.image}
                                alt={user.name}
                                fill
                                className="object-cover"
                              />
                            </div>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-gray-900">{user.name}</h5>
                            <p className="text-xs text-gray-500">@{user.username}</p>
                            <p className="text-xs text-gray-500">
                              Blocked on {new Date(user.blockedDate).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        
                        <Button variant="outline" size="sm">
                          Unblock
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">You haven't blocked any users yet.</p>
                  </div>
                )}
              </div>
            </SettingsLayout>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}
