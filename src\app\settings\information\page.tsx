import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { SettingsSidebar } from "@/components/settings/SettingsSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { SettingsLayout } from "@/components/settings/SettingsLayout";
import { Button } from "@/components/ui/Button";

export default async function MyInformationPage() {
  const user = await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Settings sidebar */}
          <div className="lg:col-span-1">
            <SettingsSidebar />
          </div>

          {/* Main content */}
          <div className="lg:col-span-2">
            <SettingsLayout 
              title="My Information" 
              description="View and download your personal information."
            >
              <div className="space-y-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800">Your Data</h4>
                  <p className="mt-1 text-sm text-blue-700">
                    You can download a copy of your data at any time. This includes posts, photos, comments, and other information associated with your account.
                  </p>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900">Download Your Information</h4>
                  <p className="mt-1 text-sm text-gray-500">
                    You can request a download of your information in an HTML format that is easy to view, or in a JSON format, which could allow you to import your information to another service.
                  </p>
                  
                  <div className="mt-4 space-y-4">
                    <div>
                      <label htmlFor="data-format" className="block text-sm font-medium text-gray-700">
                        Format
                      </label>
                      <div className="mt-1">
                        <select
                          id="data-format"
                          name="data-format"
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="html">HTML format</option>
                          <option value="json">JSON format</option>
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="data-range" className="block text-sm font-medium text-gray-700">
                        Date Range
                      </label>
                      <div className="mt-1">
                        <select
                          id="data-range"
                          name="data-range"
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="all-time">All time</option>
                          <option value="last-year">Last year</option>
                          <option value="last-month">Last month</option>
                          <option value="last-week">Last week</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="pt-2">
                      <Button>
                        Request Download
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900">Data Usage and Privacy</h4>
                  <p className="mt-1 text-sm text-gray-500">
                    Learn more about how we use your data and how to control your privacy settings.
                  </p>
                  
                  <div className="mt-4">
                    <Button variant="outline">
                      View Privacy Policy
                    </Button>
                  </div>
                </div>
              </div>
            </SettingsLayout>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}
