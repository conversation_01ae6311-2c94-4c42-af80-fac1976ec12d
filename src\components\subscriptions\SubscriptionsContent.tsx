"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  UserGroupIcon,
  UserPlusIcon,
  UsersIcon,
  XMarkIcon
} from "@heroicons/react/24/outline";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { SubscriptionsList } from "./SubscriptionsList";
import { SubscribersList } from "./SubscribersList";
import { PeopleSuggestionsPreview } from "../friends/PeopleSuggestionsPreview";
import { cn } from "@/lib/utils";

type TabType = 'following' | 'followers' | 'suggestions';

export function SubscriptionsContent() {
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<TabType>('following');

  useEffect(() => {
    const tab = searchParams?.get('tab') as TabType;
    if (tab && ['following', 'followers', 'suggestions'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('tab', tab);
    router.push(`/subscriptions?${params.toString()}`);
  };

  const clearSearch = () => {
    setSearchQuery("");
  };

  if (!session?.user) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Please sign in</h3>
        <p className="text-gray-600">You need to be signed in to view subscriptions.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Subscriptions</h1>
            <p className="text-sm text-gray-600 mt-1">
              Manage your subscriptions and discover new people
            </p>
          </div>

          {/* Search */}
          <div className="relative max-w-md w-full sm:w-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search people..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="mt-6 border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => handleTabChange('following')}
              className={cn(
                "py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200",
                activeTab === 'following'
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              <div className="flex items-center space-x-2">
                <UserPlusIcon className="h-4 w-4" />
                <span>Following</span>
              </div>
            </button>
            <button
              onClick={() => handleTabChange('followers')}
              className={cn(
                "py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200",
                activeTab === 'followers'
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              <div className="flex items-center space-x-2">
                <UsersIcon className="h-4 w-4" />
                <span>Followers</span>
              </div>
            </button>
            <button
              onClick={() => handleTabChange('suggestions')}
              className={cn(
                "py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200",
                activeTab === 'suggestions'
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="h-4 w-4" />
                <span>Discover</span>
              </div>
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-6">
        {activeTab === 'following' && (
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Following</h2>
              <p className="text-sm text-gray-600 mt-1">
                People you are subscribed to
              </p>
            </div>
            <div className="p-6">
              <SubscriptionsList searchQuery={searchQuery} />
            </div>
          </div>
        )}

        {activeTab === 'followers' && (
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Followers</h2>
              <p className="text-sm text-gray-600 mt-1">
                People who are subscribed to you
              </p>
            </div>
            <div className="p-6">
              <SubscribersList searchQuery={searchQuery} />
            </div>
          </div>
        )}

        {activeTab === 'suggestions' && (
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Discover People</h2>
              <p className="text-sm text-gray-600 mt-1">
                Find new people to subscribe to
              </p>
            </div>
            <div className="p-6">
              <PeopleSuggestionsPreview showAll={true} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
