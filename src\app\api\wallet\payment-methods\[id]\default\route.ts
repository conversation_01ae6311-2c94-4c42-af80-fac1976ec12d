import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

// Set payment method as default
export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Check if payment method belongs to user
    const paymentMethod = await WalletService.getPaymentMethodById(id);

    if (!paymentMethod || paymentMethod.userId !== session.user.id) {
      return NextResponse.json(
        {
          success: false,
          message: "Payment method not found"
        },
        { status: 404 }
      );
    }

    await WalletService.setDefaultPaymentMethod(session.user.id, id);

    return NextResponse.json({
      success: true,
      message: "Default payment method updated successfully",
    });
  } catch (error: any) {
    console.error("Error setting default payment method:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update default payment method"
      },
      { status: 500 }
    );
  }
}
