"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Select } from "@/components/ui/Select";
import { toast } from "react-hot-toast";
import Image from "next/image";
import Link from "next/link";
import { formatTimeAgo } from "@/lib/utils";
import {
  MagnifyingGlassIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  StarIcon,
  UserIcon,
  FlagIcon,
  FunnelIcon,
} from "@heroicons/react/24/outline";

interface Review {
  id: string;
  userId: string;
  storeId: string;
  rating: number;
  comment: string | null;
  isApproved: boolean;
  isReported: boolean;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface StoreReviewsProps {
  storeId: string;
}

export function StoreReviews({ storeId }: StoreReviewsProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [filteredReviews, setFilteredReviews] = useState<Review[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    rating: "all",
    status: "all",
  });
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    fetchReviews();
  }, [storeId]);

  useEffect(() => {
    if (reviews.length > 0) {
      let result = [...reviews];

      // Apply search
      if (searchTerm) {
        result = result.filter(
          (review) =>
            (review.comment &&
              review.comment.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (review.user.name &&
              review.user.name.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply rating filter
      if (filters.rating !== "all") {
        const ratingValue = parseInt(filters.rating);
        result = result.filter((review) => review.rating === ratingValue);
      }

      // Apply status filter
      if (filters.status !== "all") {
        if (filters.status === "approved") {
          result = result.filter((review) => review.isApproved);
        } else if (filters.status === "pending") {
          result = result.filter((review) => !review.isApproved);
        } else if (filters.status === "reported") {
          result = result.filter((review) => review.isReported);
        }
      }

      setFilteredReviews(result);
    }
  }, [reviews, searchTerm, filters]);

  const fetchReviews = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/stores/${storeId}/reviews`);

      if (!response.ok) {
        throw new Error(`Failed to fetch store reviews: ${response.status}`);
      }

      const data = await response.json();

      // Add mock approval and reported status for demonstration
      const reviewsWithStatus = data.reviews.map((review: any) => ({
        ...review,
        isApproved: Math.random() > 0.3, // 70% chance of being approved
        isReported: Math.random() > 0.8, // 20% chance of being reported
      }));

      setReviews(reviewsWithStatus);
      setFilteredReviews(reviewsWithStatus);
    } catch (error) {
      console.error("Error fetching store reviews:", error);
      toast.error("Failed to load store reviews");
      setReviews([]);
      setFilteredReviews([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleApproveReview = async (reviewId: string) => {
    setActionLoading(reviewId);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Update local state
      setReviews(reviews.map(review =>
        review.id === reviewId
          ? { ...review, isApproved: true }
          : review
      ));

      toast.success("Review approved successfully");
    } catch (error) {
      console.error("Error approving review:", error);
      toast.error("Failed to approve review");
    } finally {
      setActionLoading(null);
    }
  };

  const handleRejectReview = async (reviewId: string) => {
    setActionLoading(reviewId);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Update local state
      setReviews(reviews.map(review =>
        review.id === reviewId
          ? { ...review, isApproved: false }
          : review
      ));

      toast.success("Review rejected successfully");
    } catch (error) {
      console.error("Error rejecting review:", error);
      toast.error("Failed to reject review");
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm("Are you sure you want to delete this review? This action cannot be undone.")) {
      return;
    }

    setActionLoading(reviewId);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Update local state
      setReviews(reviews.filter(review => review.id !== reviewId));

      toast.success("Review deleted successfully");
    } catch (error) {
      console.error("Error deleting review:", error);
      toast.error("Failed to delete review");
    } finally {
      setActionLoading(null);
    }
  };

  const renderStarRating = (rating: number) => {
    const stars = [];

    for (let i = 0; i < 5; i++) {
      stars.push(
        <StarIcon
          key={i}
          className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
        />
      );
    }

    return (
      <div className="flex">
        {stars}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Store Reviews</h2>
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
        >
          <FunnelIcon className="mr-2 h-5 w-5" />
          Filters
        </Button>
      </div>

      {/* Search and filters */}
      <div className="space-y-4">
        <form onSubmit={handleSearch} className="relative">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="Search reviews..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </form>

        {showFilters && (
          <div className="rounded-lg border border-gray-200 bg-white p-4">
            <h3 className="mb-3 font-medium text-gray-700">Filter Reviews</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Rating
                </label>
                <Select
                  value={filters.rating}
                  onChange={(e) => handleFilterChange("rating", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Ratings</option>
                  <option value="5">5 Stars</option>
                  <option value="4">4 Stars</option>
                  <option value="3">3 Stars</option>
                  <option value="2">2 Stars</option>
                  <option value="1">1 Star</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Status
                </label>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange("status", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Reviews</option>
                  <option value="approved">Approved</option>
                  <option value="pending">Pending</option>
                  <option value="reported">Reported</option>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Reviews list */}
      <div className="rounded-lg border border-gray-200 bg-white">
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Spinner size="lg" />
          </div>
        ) : filteredReviews.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center p-4 text-center">
            <StarIcon className="mb-2 h-12 w-12 text-gray-400" />
            <h3 className="mb-1 text-lg font-medium text-gray-900">No reviews found</h3>
            <p className="text-gray-500">
              {searchTerm || Object.values(filters).some((v) => v !== "all")
                ? "Try adjusting your search or filters"
                : "This store doesn't have any reviews yet"}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Reviewer
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Rating
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Comment
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {filteredReviews.map((review) => (
                  <tr key={review.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          {review.user.image ? (
                            <Image
                              src={review.user.image}
                              alt={review.user.name || "User"}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                              <UserIcon className="h-6 w-6 text-gray-500" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {review.user.name || "Anonymous User"}
                          </div>
                          <Link href={`/admin/users/${review.userId}`} className="text-xs text-blue-600 hover:underline">
                            View Profile
                          </Link>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      {renderStarRating(review.rating)}
                    </td>
                    <td className="px-6 py-4">
                      <div className="max-w-xs overflow-hidden text-sm text-gray-900">
                        {review.comment || <span className="text-gray-500 italic">No comment provided</span>}
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      {review.isReported ? (
                        <Badge variant="danger" className="flex items-center">
                          <FlagIcon className="mr-1 h-3 w-3" />
                          Reported
                        </Badge>
                      ) : review.isApproved ? (
                        <Badge variant="success" className="flex items-center">
                          <CheckIcon className="mr-1 h-3 w-3" />
                          Approved
                        </Badge>
                      ) : (
                        <Badge variant="default" className="flex items-center">
                          Pending
                        </Badge>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <div>{new Date(review.createdAt).toLocaleDateString()}</div>
                      <div className="text-xs">{formatTimeAgo(review.createdAt)}</div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {!review.isApproved ? (
                          <Button
                            onClick={() => handleApproveReview(review.id)}
                            variant="primary"
                            size="sm"
                            disabled={actionLoading === review.id}
                          >
                            {actionLoading === review.id ? (
                              <Spinner size="sm" className="h-4 w-4" />
                            ) : (
                              <CheckIcon className="h-4 w-4" />
                            )}
                          </Button>
                        ) : (
                          <Button
                            onClick={() => handleRejectReview(review.id)}
                            variant="warning"
                            size="sm"
                            disabled={actionLoading === review.id}
                          >
                            {actionLoading === review.id ? (
                              <Spinner size="sm" className="h-4 w-4" />
                            ) : (
                              <XMarkIcon className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                        <Button
                          onClick={() => handleDeleteReview(review.id)}
                          variant="danger"
                          size="sm"
                          disabled={actionLoading === review.id}
                        >
                          {actionLoading === review.id ? (
                            <Spinner size="sm" className="h-4 w-4" />
                          ) : (
                            <TrashIcon className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
