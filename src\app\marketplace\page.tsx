import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { BuildingStorefrontIcon, PlusIcon, ShoppingBagIcon, TagIcon, StarIcon, UserGroupIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { db } from "@/lib/db";
import { products, stores, users } from "@/lib/db/schema";
import { desc, eq } from "drizzle-orm";
import { ProductCard } from "@/components/marketplace/ProductCard";
import { StoreCard } from "@/components/marketplace/StoreCard";

export default async function MarketplacePage() {
  await requireAuth();

  // Fetch latest products
  const latestProducts = await db
    .select({
      id: products.id,
      title: products.title,
      price: products.price,
      condition: products.item_condition,
      photos: products.photos,
      createdAt: products.createdAt,
      store: {
        id: stores.id,
        name: stores.name,
        logo: stores.logo,
      },
    })
    .from(products)
    .leftJoin(stores, eq(products.storeId, stores.id))
    .orderBy(desc(products.createdAt))
    .limit(8);

  // Fetch popular stores
  const popularStores = await db
    .select({
      id: stores.id,
      name: stores.name,
      slug: stores.slug,
      logo: stores.logo,
      banner: stores.banner,
      isVerified: stores.isVerified,
      owner: {
        id: users.id,
        name: users.name,
      },
    })
    .from(stores)
    .leftJoin(users, eq(stores.ownerId, users.id))
    .orderBy(desc(stores.createdAt))
    .limit(4);

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Marketplace
          </h1>
          <div className="flex space-x-2">
            <Link href="/marketplace/following">
              <Button variant="outline">
                <StarIcon className="h-5 w-5 mr-1" />
                Following
              </Button>
            </Link>
            <Link href="/marketplace/create-store">
              <Button>
                <PlusIcon className="h-5 w-5 mr-1" />
                Create Store
              </Button>
            </Link>
          </div>
        </div>

        <div className="mb-8 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="flex-1 sm:max-w-xs">
            <Input
              type="search"
              placeholder="Search marketplace..."
              className="w-full"
            />
          </div>
          <div className="flex space-x-2 overflow-x-auto">
            <Link href="/marketplace">
              <Button variant="outline">All Products</Button>
            </Link>
            <Link href="/marketplace?view=stores">
              <Button variant="outline">Stores</Button>
            </Link>
            <Link href="/my-store/dashboard">
              <Button variant="outline">My Store</Button>
            </Link>
          </div>
        </div>

        {/* Categories section */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Categories
          </h2>

          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
            <div className="flex flex-col items-center rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
              <div className="mb-2 rounded-full bg-blue-100 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                </svg>
              </div>
              <span className="text-center text-sm font-medium">Electronics</span>
            </div>

            <div className="flex flex-col items-center rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
              <div className="mb-2 rounded-full bg-green-100 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <span className="text-center text-sm font-medium">Clothing</span>
            </div>

            <div className="flex flex-col items-center rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
              <div className="mb-2 rounded-full bg-purple-100 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <span className="text-center text-sm font-medium">Home</span>
            </div>

            <div className="flex flex-col items-center rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
              <div className="mb-2 rounded-full bg-yellow-100 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="text-center text-sm font-medium">Toys</span>
            </div>

            <div className="flex flex-col items-center rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
              <div className="mb-2 rounded-full bg-red-100 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                </svg>
              </div>
              <span className="text-center text-sm font-medium">Gifts</span>
            </div>

            <div className="flex flex-col items-center rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
              <div className="mb-2 rounded-full bg-indigo-100 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
              </div>
              <span className="text-center text-sm font-medium">More</span>
            </div>
          </div>
        </div>

        {/* Latest Products */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Latest Products
            </h2>
            <Link href="/marketplace?sort=newest" className="text-sm text-blue-600 hover:text-blue-800">
              View all
            </Link>
          </div>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {latestProducts && latestProducts.length > 0 ? (
              latestProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))
            ) : (
              <div className="col-span-full overflow-hidden rounded-lg bg-white shadow">
                <div className="p-8 text-center">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <TagIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="mt-4 text-lg font-medium text-gray-900">
                    No products available
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Create a store first, then add products to your store.
                  </p>
                  <div className="mt-6">
                    <Link href="/marketplace/create-store">
                      <Button>
                        Create Store
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Popular Stores */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Popular Stores
            </h2>
            <Link href="/marketplace?view=stores" className="text-sm text-blue-600 hover:text-blue-800">
              View all stores
            </Link>
          </div>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {popularStores && popularStores.length > 0 ? (
              popularStores.map((store) => (
                <StoreCard key={store.id} store={store} />
              ))
            ) : (
              <div className="col-span-full overflow-hidden rounded-lg bg-white shadow">
                <div className="p-8 text-center">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <BuildingStorefrontIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="mt-4 text-lg font-medium text-gray-900">
                    No stores available
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Be the first to create a store in the marketplace.
                  </p>
                  <div className="mt-6">
                    <Link href="/marketplace/create-store">
                      <Button>
                        Create Store
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
