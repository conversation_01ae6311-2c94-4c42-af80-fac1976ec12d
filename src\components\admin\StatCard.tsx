"use client";

import { ReactNode } from "react";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  color: string;
  bgColor: string;
  change?: {
    value: number;
    isPositive: boolean;
  };
  onClick?: () => void;
}

export function StatCard({
  title,
  value,
  icon,
  color,
  bgColor,
  change,
  onClick,
}: StatCardProps) {
  return (
    <div 
      className={`relative overflow-hidden rounded-lg bg-white shadow transition-all duration-300 hover:shadow-md ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      <div className="p-5">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <p className={`mt-2 text-3xl font-semibold ${color}`}>{value}</p>
            
            {change && (
              <div className="mt-2 flex items-center text-sm">
                <span
                  className={`flex items-center ${
                    change.isPositive ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {change.isPositive ? (
                    <svg
                      className="mr-1 h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 10l7-7m0 0l7 7m-7-7v18"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="mr-1 h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 14l-7 7m0 0l-7-7m7 7V3"
                      />
                    </svg>
                  )}
                  {Math.abs(change.value)}%
                </span>
                <span className="ml-2 text-gray-500">from last month</span>
              </div>
            )}
          </div>
          
          <div className={`rounded-full p-3 ${bgColor}`}>
            {icon}
          </div>
        </div>
      </div>
      
      {/* Decorative element */}
      <div
        className={`absolute bottom-0 left-0 h-1 w-full ${bgColor}`}
      ></div>
    </div>
  );
}
