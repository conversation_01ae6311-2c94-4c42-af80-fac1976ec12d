import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { CreateFanPageForm } from "@/components/fan-pages/CreateFanPageForm";

export const dynamic = 'force-dynamic';

export default async function CreateFanPagePage() {
  // Require authentication
  await requireAuth();

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Create a Fan Page</h1>
            <p className="mt-2 text-lg text-gray-600">
              Connect with your audience and share your passion
            </p>
          </div>

          <CreateFanPageForm />
        </div>
      </div>
    </MainLayout>
  );
}

export const metadata = {
  title: "Create Fan Page | HIFNF",
  description: "Create your own fan page to connect with your audience and share your content.",
};
