import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { z } from "zod";

const paymentMethodSchema = z.object({
  type: z.enum(['bank', 'mobile_banking', 'card']),
  methodType: z.enum(['payment', 'payout']),
  name: z.string().min(1, "Name is required"),
  details: z.object({
    // Bank fields
    accountNumber: z.string().optional(),
    accountName: z.string().optional(),
    bankName: z.string().optional(),
    branchName: z.string().optional(),
    routingNumber: z.string().optional(),
    // Mobile banking fields
    mobileNumber: z.string().optional(),
    provider: z.string().optional(),
    // Card fields
    cardNumber: z.string().optional(),
    cardHolderName: z.string().optional(),
  }),
});

// Get user's payment methods
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const paymentMethods = await WalletService.getUserPaymentMethods(session.user.id);

    return NextResponse.json({
      success: true,
      paymentMethods: paymentMethods || [],
    });
  } catch (error: any) {
    console.error("Error fetching payment methods:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch payment methods"
      },
      { status: 500 }
    );
  }
}

// Add new payment method
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = paymentMethodSchema.parse(body);

    // Validate required fields based on type
    if (validatedData.type === 'bank') {
      if (!validatedData.details.bankName || !validatedData.details.accountNumber || !validatedData.details.accountName) {
        return NextResponse.json(
          {
            success: false,
            message: "Bank name, account number, and account name are required for bank accounts"
          },
          { status: 400 }
        );
      }
    } else if (validatedData.type === 'mobile_banking') {
      if (!validatedData.details.provider || !validatedData.details.mobileNumber) {
        return NextResponse.json(
          {
            success: false,
            message: "Provider and mobile number are required for mobile banking"
          },
          { status: 400 }
        );
      }
    } else if (validatedData.type === 'card') {
      if (!validatedData.details.cardNumber || !validatedData.details.cardHolderName) {
        return NextResponse.json(
          {
            success: false,
            message: "Card number and card holder name are required for cards"
          },
          { status: 400 }
        );
      }
    }

    const paymentMethodId = await WalletService.addPaymentMethod(
      session.user.id,
      validatedData.type,
      validatedData.methodType,
      validatedData.name,
      validatedData.details
    );

    return NextResponse.json({
      success: true,
      message: "Payment method added successfully",
      paymentMethodId,
    });
  } catch (error: any) {
    console.error("Error adding payment method:", error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid input data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to add payment method"
      },
      { status: 500 }
    );
  }
}
