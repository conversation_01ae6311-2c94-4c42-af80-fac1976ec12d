import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, subscriptions } from "@/lib/db/schema";
import { eq, and, or, sql, isNotNull, inArray } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const type = url.searchParams.get('type') || 'today'; // 'today', 'upcoming', 'all'
    const limit = parseInt(url.searchParams.get('limit') || '10');

    // Define today's date
    const today = new Date();

    // Get users that the current user is subscribed to
    const subscriptionsQuery = await db
      .select({
        targetUserId: subscriptions.targetUserId
      })
      .from(subscriptions)
      .where(eq(subscriptions.subscriberId, session.user.id));

    const subscribedUserIds = subscriptionsQuery.map(s => s.targetUserId);

    // Define today's date components
    const todayMonth = today.getMonth() + 1; // JavaScript months are 0-indexed
    const todayDay = today.getDate();

    if (subscribedUserIds.length === 0) {
      return NextResponse.json({
        birthdays: [],
        type,
        count: 0,
        today: {
          month: todayMonth,
          day: todayDay
        }
      });
    }

    // Build date conditions based on type
    let dateCondition;

    if (type === 'today') {
      // Today's birthdays
      dateCondition = and(
        sql`MONTH(${users.birthday}) = ${todayMonth}`,
        sql`DAY(${users.birthday}) = ${todayDay}`
      );
    } else if (type === 'upcoming') {
      // Next 30 days birthdays (excluding today)
      const nextMonth = new Date(today);
      nextMonth.setDate(today.getDate() + 30);

      if (today.getMonth() === nextMonth.getMonth()) {
        // Same month
        dateCondition = and(
          sql`MONTH(${users.birthday}) = ${todayMonth}`,
          sql`DAY(${users.birthday}) > ${todayDay}`,
          sql`DAY(${users.birthday}) <= ${nextMonth.getDate()}`
        );
      } else {
        // Cross month boundary
        dateCondition = or(
          and(
            sql`MONTH(${users.birthday}) = ${todayMonth}`,
            sql`DAY(${users.birthday}) > ${todayDay}`
          ),
          and(
            sql`MONTH(${users.birthday}) = ${nextMonth.getMonth() + 1}`,
            sql`DAY(${users.birthday}) <= ${nextMonth.getDate()}`
          )
        );
      }
    } else {
      // All birthdays (today + upcoming 30 days)
      const nextMonth = new Date(today);
      nextMonth.setDate(today.getDate() + 30);

      if (today.getMonth() === nextMonth.getMonth()) {
        // Same month
        dateCondition = and(
          sql`MONTH(${users.birthday}) = ${todayMonth}`,
          sql`DAY(${users.birthday}) >= ${todayDay}`,
          sql`DAY(${users.birthday}) <= ${nextMonth.getDate()}`
        );
      } else {
        // Cross month boundary
        dateCondition = or(
          and(
            sql`MONTH(${users.birthday}) = ${todayMonth}`,
            sql`DAY(${users.birthday}) >= ${todayDay}`
          ),
          and(
            sql`MONTH(${users.birthday}) = ${nextMonth.getMonth() + 1}`,
            sql`DAY(${users.birthday}) <= ${nextMonth.getDate()}`
          )
        );
      }
    }

    // Fetch friends with birthdays (simplified query)
    let birthdayFriends: any[] = [];

    if (subscribedUserIds.length > 0) {
      birthdayFriends = await db
        .select({
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
          birthday: users.birthday,
        })
        .from(users)
        .where(
          and(
            inArray(users.id, subscribedUserIds),
            isNotNull(users.birthday),
            dateCondition
          )
        )
        .limit(limit);
    }

    // Format the response with JavaScript calculations
    const formattedBirthdays = birthdayFriends.map(friend => {
      const birthdayDate = new Date(friend.birthday!);
      const birthdayMonth = birthdayDate.getMonth() + 1;
      const birthdayDay = birthdayDate.getDate();
      const isToday = birthdayMonth === todayMonth && birthdayDay === todayDay;

      // Calculate days until birthday
      let daysUntil = 0;
      if (isToday) {
        daysUntil = 0;
      } else {
        const thisYear = today.getFullYear();
        let nextBirthday = new Date(thisYear, birthdayMonth - 1, birthdayDay);

        if (nextBirthday < today) {
          nextBirthday = new Date(thisYear + 1, birthdayMonth - 1, birthdayDay);
        }

        daysUntil = Math.ceil((nextBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      }

      return {
        id: friend.id,
        name: friend.name,
        username: friend.username,
        image: friend.image,
        birthday: friend.birthday,
        birthdayMonth,
        birthdayDay,
        isToday,
        daysUntil,
        age: friend.birthday ? new Date().getFullYear() - birthdayDate.getFullYear() : null
      };
    }).sort((a, b) => {
      // Sort by: today's birthdays first, then by days until birthday
      if (a.isToday && !b.isToday) return -1;
      if (!a.isToday && b.isToday) return 1;
      if (a.daysUntil !== b.daysUntil) return a.daysUntil - b.daysUntil;
      return a.name.localeCompare(b.name);
    });

    return NextResponse.json({
      birthdays: formattedBirthdays,
      type,
      count: formattedBirthdays.length,
      today: {
        month: todayMonth,
        day: todayDay
      }
    });

  } catch (error) {
    console.error("Error fetching birthdays:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
