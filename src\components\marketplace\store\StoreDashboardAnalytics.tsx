"use client";

import { 
  ChartBarIcon, 
  ArrowTrendingUpIcon, 
  ArrowTrendingDownIcon 
} from "@heroicons/react/24/outline";

interface StoreDashboardAnalyticsProps {
  // Mock data for analytics
  analytics?: {
    viewsThisWeek: number;
    viewsLastWeek: number;
    followersThisWeek: number;
    followersLastWeek: number;
    salesThisWeek: number;
    salesLastWeek: number;
  };
}

export function StoreDashboardAnalytics({ 
  analytics = {
    viewsThisWeek: 245,
    viewsLastWeek: 210,
    followersThisWeek: 12,
    followersLastWeek: 8,
    salesThisWeek: 8,
    salesLastWeek: 5
  }
}: StoreDashboardAnalyticsProps) {
  
  // Calculate percentage changes
  const viewsChange = calculatePercentageChange(analytics.viewsThisWeek, analytics.viewsLastWeek);
  const followersChange = calculatePercentageChange(analytics.followersThisWeek, analytics.followersLastWeek);
  const salesChange = calculatePercentageChange(analytics.salesThisWeek, analytics.salesLastWeek);
  
  function calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return Math.round(((current - previous) / previous) * 100);
  }

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center">
          <ChartBarIcon className="h-5 w-5 text-gray-500 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Analytics Overview</h3>
        </div>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
          {/* Views */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-500">Views</h4>
              <div className={`flex items-center text-xs font-medium ${viewsChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {viewsChange >= 0 ? (
                  <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
                ) : (
                  <ArrowTrendingDownIcon className="h-3 w-3 mr-1" />
                )}
                {Math.abs(viewsChange)}%
              </div>
            </div>
            <p className="mt-2 text-2xl font-semibold text-gray-900">{analytics.viewsThisWeek}</p>
            <p className="mt-1 text-xs text-gray-500">Compared to {analytics.viewsLastWeek} last week</p>
          </div>
          
          {/* Followers */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-500">New Followers</h4>
              <div className={`flex items-center text-xs font-medium ${followersChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {followersChange >= 0 ? (
                  <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
                ) : (
                  <ArrowTrendingDownIcon className="h-3 w-3 mr-1" />
                )}
                {Math.abs(followersChange)}%
              </div>
            </div>
            <p className="mt-2 text-2xl font-semibold text-gray-900">{analytics.followersThisWeek}</p>
            <p className="mt-1 text-xs text-gray-500">Compared to {analytics.followersLastWeek} last week</p>
          </div>
          
          {/* Sales */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-500">Sales</h4>
              <div className={`flex items-center text-xs font-medium ${salesChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {salesChange >= 0 ? (
                  <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
                ) : (
                  <ArrowTrendingDownIcon className="h-3 w-3 mr-1" />
                )}
                {Math.abs(salesChange)}%
              </div>
            </div>
            <p className="mt-2 text-2xl font-semibold text-gray-900">{analytics.salesThisWeek}</p>
            <p className="mt-1 text-xs text-gray-500">Compared to {analytics.salesLastWeek} last week</p>
          </div>
        </div>
        
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            Analytics data is updated daily. Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>
      </div>
    </div>
  );
}
