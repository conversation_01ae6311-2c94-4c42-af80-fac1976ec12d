import mysql from 'mysql2/promise';
import * as fs from 'fs';
import * as path from 'path';
import { config } from 'dotenv';

// Load environment variables
config();

async function runViewTrackingMigration() {
  console.log('🚀 Starting blog views table migration...');

  // Get database credentials from environment variables
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Database config: ${dbHost}:${dbPort} as ${dbUser} (password: ${dbPassword ? 'set' : 'not set'})`);
  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  let connection: mysql.Connection;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: dbHost,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      port: dbPort,
      multipleStatements: true,
    });

    console.log('✅ Connected to database');

    // Check if blog_views table already exists
    const [existingTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'blog_views'
    `, [dbName]);

    if ((existingTables as any[]).length > 0) {
      console.log('⚠️  blog_views table already exists');
      return;
    }

    // Create the blog_views table directly
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS blog_views (
        id VARCHAR(255) PRIMARY KEY,
        blogId VARCHAR(255) NOT NULL,
        userId VARCHAR(255),
        ipAddress VARCHAR(45) NOT NULL,
        userAgent TEXT,
        sessionId VARCHAR(255) NOT NULL,
        fingerprint VARCHAR(255),
        referrer VARCHAR(500),
        country VARCHAR(100),
        city VARCHAR(100),
        device VARCHAR(50),
        browser VARCHAR(50),
        os VARCHAR(50),
        isBot BOOLEAN DEFAULT FALSE,
        isUnique BOOLEAN DEFAULT TRUE,
        viewDuration INT DEFAULT 0,
        scrollDepth INT DEFAULT 0,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_blog_views_blogId (blogId),
        INDEX idx_blog_views_userId (userId),
        INDEX idx_blog_views_sessionId (sessionId),
        INDEX idx_blog_views_createdAt (createdAt),
        INDEX idx_blog_views_isBot (isBot),
        INDEX idx_blog_views_isUnique (isUnique),
        INDEX idx_blog_views_country (country),
        INDEX idx_blog_views_device (device),
        INDEX idx_blog_views_composite (blogId, isBot, isUnique, createdAt)
      )
    `;

    // Execute the migration
    console.log('🔄 Creating blog_views table...');
    await connection.execute(createTableSQL);
    console.log('✅ Table created successfully');

    // Verify the table was created
    const [newTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'blog_views'
    `, [dbName]);

    if ((newTables as any[]).length > 0) {
      console.log('✅ blog_views table created successfully');
      
      // Check table structure
      const [columns] = await connection.execute(`
        DESCRIBE blog_views
      `);
      
      console.log('📊 Table structure:');
      (columns as any[]).forEach((col: any) => {
        console.log(`  - ${col.Field}: ${col.Type}`);
      });
    } else {
      console.error('❌ Failed to create blog_views table');
      process.exit(1);
    }

    console.log('🎉 Blog views tracking migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection!) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
runViewTrackingMigration().catch(console.error);
