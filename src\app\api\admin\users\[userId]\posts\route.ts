import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, users, groups } from "@/lib/db/schema";
import { eq, desc, count } from "drizzle-orm";

// Get user's posts
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user's posts with related data
    const userPosts = await db.select({
      id: posts.id,
      content: posts.content,
      images: posts.images,
      videos: posts.videos,
      privacy: posts.privacy,
      feeling: posts.feeling,
      activity: posts.activity,
      location: posts.location,
      backgroundColor: posts.backgroundColor,
      sharedPostId: posts.sharedPostId,
      groupId: posts.groupId,
      createdAt: posts.createdAt,
      updatedAt: posts.updatedAt,
      // Related data
      groupName: groups.name,
    })
    .from(posts)
    .leftJoin(groups, eq(posts.groupId, groups.id))
    .where(eq(posts.userId, userId))
    .orderBy(desc(posts.createdAt))
    .limit(limit)
    .offset(offset);

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(posts)
      .where(eq(posts.userId, userId));

    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      posts: userPosts,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
