import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, users, storeSettings } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { desc, eq, and, or, like, count } from "drizzle-orm";

const storeSchema = z.object({
  name: z.string().min(2).max(255),
  slug: z.string().min(3).max(100).regex(/^[a-zA-Z0-9-]+$/),
  description: z.string().max(1000).optional(),
  location: z.string().max(255).optional(),
  logo: z.string().url().optional().nullable(),
  banner: z.string().url().optional().nullable(),
});

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const offset = (page - 1) * limit;

    // Get all stores with search filter
    const allStores = await db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        description: stores.description,
        logo: stores.logo,
        banner: stores.banner,
        location: stores.location,
        isVerified: stores.isVerified,
        createdAt: stores.createdAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(stores)
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(
        search
          ? or(
              like(stores.name, `%${search}%`),
              like(stores.description || "", `%${search}%`),
              like(stores.location || "", `%${search}%`)
            )
          : undefined
      )
      .orderBy(desc(stores.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(stores)
      .where(
        search
          ? or(
              like(stores.name, `%${search}%`),
              like(stores.description || "", `%${search}%`),
              like(stores.location || "", `%${search}%`)
            )
          : undefined
      );

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      stores: allStores,
      pagination: {
        total: totalCount,
        totalPages,
        currentPage: page,
        limit,
      },
    });
  } catch (error) {
    console.error("Error fetching stores:", error);
    return NextResponse.json(
      { message: "Error fetching stores" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = storeSchema.parse(body);

    // Check if slug is already taken
    const existingStore = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.slug, validatedData.slug))
      .limit(1);

    if (existingStore.length > 0) {
      return NextResponse.json(
        { message: "Store slug already exists" },
        { status: 400 }
      );
    }

    // Create new store
    const storeId = uuidv4();
    await db.insert(stores).values({
      id: storeId,
      name: validatedData.name,
      slug: validatedData.slug,
      description: validatedData.description || null,
      location: validatedData.location || null,
      logo: validatedData.logo || null,
      banner: validatedData.banner || null,
      ownerId: session.user.id,
      isVerified: false,
    });

    // Create default store settings
    await db.insert(storeSettings).values({
      id: uuidv4(),
      storeId: storeId,
      visibility: "public",
      showOutOfStock: true,
      showProductViews: true,
      emailNotifications: true,
      productViewNotifications: false,
    });

    // Get the created store
    const createdStore = await db
      .select()
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    return NextResponse.json(
      { message: "Store created successfully", store: createdStore[0] },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating store:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Error creating store" },
      { status: 500 }
    );
  }
}
