CREATE TABLE `admin_permissions` (
	`id` varchar(255) NOT NULL,
	`name` varchar(100) NOT NULL,
	`code` varchar(100) NOT NULL,
	`description` text,
	`module` varchar(100) NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `admin_permissions_id` PRIMARY KEY(`id`),
	CONSTRAINT `admin_permissions_name_unique` UNIQUE(`name`),
	CONSTRAINT `admin_permissions_code_unique` UNIQUE(`code`)
);
--> statement-breakpoint
CREATE TABLE `admin_role_permissions` (
	`id` varchar(255) NOT NULL,
	`role_id` varchar(255) NOT NULL,
	`permission_id` varchar(255) NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `admin_role_permissions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `admin_roles` (
	`id` varchar(255) NOT NULL,
	`name` varchar(100) NOT NULL,
	`description` text,
	`is_system` boolean DEFAULT false,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `admin_roles_id` PRIMARY KEY(`id`),
	CONSTRAINT `admin_roles_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `agents` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`phone` varchar(50) NOT NULL,
	`email` varchar(255),
	`location` varchar(255),
	`serviceType` varchar(100) NOT NULL,
	`accountNumber` varchar(100) NOT NULL,
	`accountName` varchar(255) NOT NULL,
	`dailyLimit` decimal(15,2) NOT NULL DEFAULT '10000.00',
	`currentDailyAmount` decimal(15,2) NOT NULL DEFAULT '0.00',
	`commission` decimal(5,2) NOT NULL DEFAULT '2.00',
	`isActive` boolean NOT NULL DEFAULT true,
	`isVerified` boolean NOT NULL DEFAULT false,
	`rating` decimal(3,2) DEFAULT '0.00',
	`totalTransactions` int DEFAULT 0,
	`totalAmount` decimal(15,2) DEFAULT '0.00',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `agents_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `blogBookmarks` (
	`id` varchar(255) NOT NULL,
	`blogId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `blogBookmarks_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `blogCategories` (
	`id` varchar(255) NOT NULL,
	`name` varchar(100) NOT NULL,
	`slug` varchar(100) NOT NULL,
	`description` text,
	`color` varchar(7) DEFAULT '#3b82f6',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `blogCategories_id` PRIMARY KEY(`id`),
	CONSTRAINT `blogCategories_slug_unique` UNIQUE(`slug`)
);
--> statement-breakpoint
CREATE TABLE `blogComments` (
	`id` varchar(255) NOT NULL,
	`blogId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`content` text NOT NULL,
	`parentId` varchar(255),
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `blogComments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `blogLikes` (
	`id` varchar(255) NOT NULL,
	`blogId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `blogLikes_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `blogs` (
	`id` varchar(255) NOT NULL,
	`title` varchar(500) NOT NULL,
	`slug` varchar(500) NOT NULL,
	`excerpt` text,
	`content` text NOT NULL,
	`coverImage` varchar(255),
	`authorId` varchar(255) NOT NULL,
	`categoryId` varchar(255),
	`tags` json,
	`status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
	`readTime` int,
	`viewCount` int DEFAULT 0,
	`featured` boolean DEFAULT false,
	`seoTitle` varchar(255),
	`seoDescription` text,
	`publishedAt` timestamp,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `blogs_id` PRIMARY KEY(`id`),
	CONSTRAINT `blogs_slug_unique` UNIQUE(`slug`)
);
--> statement-breakpoint
CREATE TABLE `cashout_requests` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`agentId` varchar(255) NOT NULL,
	`amount` decimal(15,2) NOT NULL,
	`fee` decimal(15,2) NOT NULL DEFAULT '0.00',
	`netAmount` decimal(15,2) NOT NULL,
	`status` enum('pending','accepted','processing','completed','declined','cancelled') NOT NULL DEFAULT 'pending',
	`note` text,
	`agentNote` text,
	`transactionId` varchar(255),
	`processedAt` timestamp,
	`completedAt` timestamp,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `cashout_requests_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `event_attendees` (
	`id` varchar(255) NOT NULL,
	`eventId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`status` enum('going','interested','not_going') NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `event_attendees_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `event_comments` (
	`id` varchar(255) NOT NULL,
	`eventId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`content` text NOT NULL,
	`images` json,
	`videos` json,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `event_comments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `event_invites` (
	`id` varchar(255) NOT NULL,
	`eventId` varchar(255) NOT NULL,
	`fromUserId` varchar(255) NOT NULL,
	`toUserId` varchar(255) NOT NULL,
	`status` enum('pending','accepted','declined') NOT NULL DEFAULT 'pending',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `event_invites_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `events` (
	`id` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`description` text,
	`startTime` timestamp NOT NULL,
	`endTime` timestamp NOT NULL,
	`location` varchar(255),
	`isOnline` boolean DEFAULT false,
	`onlineLink` varchar(255),
	`coverImage` varchar(255),
	`hostId` varchar(255) NOT NULL,
	`visibility` enum('public','private','friends') NOT NULL DEFAULT 'public',
	`category` varchar(100),
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `events_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `group_members` (
	`id` varchar(255) NOT NULL,
	`groupId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`role` enum('admin','moderator','member','pending') NOT NULL DEFAULT 'member',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `group_members_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `group_reports` (
	`id` varchar(255) NOT NULL,
	`groupId` varchar(255) NOT NULL,
	`reporterId` varchar(255) NOT NULL,
	`reportedUserId` varchar(255),
	`postId` varchar(255),
	`reason` enum('spam','harassment','inappropriate_content','violation','other') NOT NULL,
	`description` text,
	`status` enum('pending','reviewed') NOT NULL DEFAULT 'pending',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `group_reports_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `payment_gateways` (
	`id` varchar(255) NOT NULL,
	`name` varchar(100) NOT NULL,
	`displayName` varchar(100) NOT NULL,
	`type` enum('stripe','paypal','sslcommerz','bkash','nagad','rocket','bank') NOT NULL,
	`isActive` boolean NOT NULL DEFAULT false,
	`config` json NOT NULL,
	`depositFee` decimal(5,2) DEFAULT '0.00',
	`depositFixedFee` decimal(10,2) DEFAULT '0.00',
	`minDeposit` decimal(10,2) DEFAULT '1.00',
	`maxDeposit` decimal(15,2) DEFAULT '10000.00',
	`currency` varchar(10) NOT NULL DEFAULT 'USD',
	`sortOrder` int DEFAULT 0,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `payment_gateways_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `pin_codes` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`pinHash` varchar(255) NOT NULL,
	`isActive` boolean NOT NULL DEFAULT true,
	`failedAttempts` int NOT NULL DEFAULT 0,
	`lockedUntil` timestamp,
	`lastUsedAt` timestamp,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `pin_codes_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `product_reports` (
	`id` varchar(255) NOT NULL,
	`productId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`reason` enum('counterfeit','inappropriate','fraud','prohibited','other') NOT NULL,
	`description` text,
	`status` enum('pending','reviewed','resolved','dismissed') NOT NULL DEFAULT 'pending',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `product_reports_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `products` (
	`id` varchar(255) NOT NULL,
	`title` varchar(255) NOT NULL,
	`description` text,
	`price` int NOT NULL,
	`item_condition` enum('new','like_new','good','fair','poor') NOT NULL,
	`category` varchar(100) NOT NULL,
	`location` varchar(255),
	`photos` json,
	`storeId` varchar(255) NOT NULL,
	`viewCount` int DEFAULT 0,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `products_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `site_settings` (
	`id` varchar(255) NOT NULL,
	`setting_key` varchar(100) NOT NULL,
	`value` text,
	`type` varchar(50) NOT NULL,
	`group_name` varchar(100) NOT NULL,
	`label` varchar(255) NOT NULL,
	`description` text,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `site_settings_id` PRIMARY KEY(`id`),
	CONSTRAINT `site_settings_setting_key_unique` UNIQUE(`setting_key`)
);
--> statement-breakpoint
CREATE TABLE `store_follows` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`storeId` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `store_follows_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `store_reviews` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`storeId` varchar(255) NOT NULL,
	`rating` int NOT NULL,
	`comment` text,
	`isApproved` boolean DEFAULT true,
	`isReported` boolean DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `store_reviews_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `store_settings` (
	`id` varchar(255) NOT NULL,
	`storeId` varchar(255) NOT NULL,
	`visibility` enum('public','private') NOT NULL DEFAULT 'public',
	`showOutOfStock` boolean DEFAULT true,
	`showProductViews` boolean DEFAULT true,
	`emailNotifications` boolean DEFAULT true,
	`productViewNotifications` boolean DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `store_settings_id` PRIMARY KEY(`id`),
	CONSTRAINT `store_settings_storeId_unique` UNIQUE(`storeId`)
);
--> statement-breakpoint
CREATE TABLE `stores` (
	`id` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`slug` varchar(100) NOT NULL,
	`description` text,
	`logo` varchar(255),
	`banner` varchar(255),
	`location` varchar(255),
	`phone` varchar(50),
	`email` varchar(255),
	`website` varchar(255),
	`ownerId` varchar(255) NOT NULL,
	`isVerified` boolean DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `stores_id` PRIMARY KEY(`id`),
	CONSTRAINT `stores_slug_unique` UNIQUE(`slug`)
);
--> statement-breakpoint
CREATE TABLE `wallet_settings` (
	`id` varchar(255) NOT NULL,
	`key` varchar(100) NOT NULL,
	`value` text NOT NULL,
	`type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
	`description` text,
	`category` varchar(100) DEFAULT 'general',
	`isSystem` boolean NOT NULL DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `wallet_settings_id` PRIMARY KEY(`id`),
	CONSTRAINT `wallet_settings_key_unique` UNIQUE(`key`)
);
--> statement-breakpoint
CREATE TABLE `wallet_transactions` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`type` enum('deposit','send','receive','cashout','internal_transfer','earning','withdraw') NOT NULL,
	`amount` decimal(15,2) NOT NULL,
	`fee` decimal(15,2) NOT NULL DEFAULT '0.00',
	`netAmount` decimal(15,2) NOT NULL,
	`walletType` enum('general','earning') NOT NULL,
	`toUserId` varchar(255),
	`toAgentId` varchar(255),
	`fromWalletType` enum('general','earning'),
	`toWalletType` enum('general','earning'),
	`status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
	`paymentGateway` varchar(100),
	`gatewayTransactionId` varchar(255),
	`reference` varchar(255),
	`note` text,
	`metadata` json,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `wallet_transactions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `wallets` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`generalBalance` decimal(15,2) NOT NULL DEFAULT '0.00',
	`earningBalance` decimal(15,2) NOT NULL DEFAULT '0.00',
	`totalDeposited` decimal(15,2) NOT NULL DEFAULT '0.00',
	`totalWithdrawn` decimal(15,2) NOT NULL DEFAULT '0.00',
	`totalSent` decimal(15,2) NOT NULL DEFAULT '0.00',
	`totalReceived` decimal(15,2) NOT NULL DEFAULT '0.00',
	`isActive` boolean NOT NULL DEFAULT true,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `wallets_id` PRIMARY KEY(`id`),
	CONSTRAINT `wallets_userId_unique` UNIQUE(`userId`)
);
--> statement-breakpoint
DROP TABLE `groupMembers`;--> statement-breakpoint
DROP TABLE `groupReports`;--> statement-breakpoint
DROP TABLE `groupRules`;--> statement-breakpoint
ALTER TABLE `notifications` MODIFY COLUMN `type` enum('like','comment','friend_request','friend_accept','message','group_invite','group_join_request','group_join_approved','group_post','group_announcement','event_invite','event_reminder','event_update','event_comment','store_follow','store_review','product_new','product_report') NOT NULL;--> statement-breakpoint
ALTER TABLE `groups` ADD `rules` text;--> statement-breakpoint
ALTER TABLE `groups` ADD `postPermission` enum('all-members','admin-only') DEFAULT 'all-members' NOT NULL;--> statement-breakpoint
ALTER TABLE `notifications` ADD `eventId` varchar(255);--> statement-breakpoint
ALTER TABLE `notifications` ADD `storeId` varchar(255);--> statement-breakpoint
ALTER TABLE `notifications` ADD `productId` varchar(255);--> statement-breakpoint
ALTER TABLE `users` ADD `role` varchar(50) DEFAULT 'user';--> statement-breakpoint
ALTER TABLE `users` ADD `is_admin` boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE `users` ADD `admin_role_id` varchar(255);--> statement-breakpoint
ALTER TABLE `groups` DROP COLUMN `profileImage`;--> statement-breakpoint
ALTER TABLE `groups` DROP COLUMN `postingPermission`;