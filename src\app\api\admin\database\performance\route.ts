import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { dbPerformanceMonitor } from "@/lib/db/performance-monitor";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // For now, allow any authenticated user. In production, add admin check
    // const user = await getUserById(session.user.id);
    // if (user?.role !== 'admin') {
    //   return NextResponse.json(
    //     { message: "Admin access required" },
    //     { status: 403 }
    //   );
    // }

    // Get comprehensive performance metrics
    const [
      performanceMetrics,
      serverStats,
      healthCheck,
      queryStats
    ] = await Promise.all([
      dbPerformanceMonitor.getPerformanceMetrics(),
      dbPerformanceMonitor.getServerStats(),
      dbPerformanceMonitor.checkHealth(),
      dbPerformanceMonitor.getQueryStats()
    ]);

    const response = {
      success: true,
      data: {
        overview: {
          status: healthCheck.status,
          timestamp: new Date().toISOString(),
          uptime: serverStats.uptime,
          uptimeFormatted: formatUptime(serverStats.uptime),
        },
        connections: {
          current: serverStats.connections.current,
          max: serverStats.connections.max,
          total: serverStats.connections.total,
          usage: ((serverStats.connections.current / serverStats.connections.max) * 100).toFixed(1),
          poolLimit: performanceMetrics.poolStats.connectionLimit,
          poolMaxIdle: performanceMetrics.poolStats.maxIdle,
        },
        queries: {
          total: serverStats.queries.total,
          slow: serverStats.queries.slow,
          qps: serverStats.queries.qps,
          recentStats: {
            total: queryStats.totalQueries,
            slow: queryStats.slowQueries,
            avgTime: queryStats.avgQueryTime,
            minTime: queryStats.minQueryTime,
            maxTime: queryStats.maxQueryTime,
          }
        },
        memory: {
          used: serverStats.memory.used,
          max: serverStats.memory.max,
          usageFormatted: formatBytes(serverStats.memory.used),
          maxFormatted: formatBytes(serverStats.memory.max),
        },
        health: {
          status: healthCheck.status,
          issues: healthCheck.issues,
          recommendations: healthCheck.recommendations,
        },
        performance: {
          connectionUsage: ((serverStats.connections.current / serverStats.connections.max) * 100).toFixed(1),
          slowQueryRatio: serverStats.queries.total > 0 
            ? ((serverStats.queries.slow / serverStats.queries.total) * 100).toFixed(2)
            : '0.00',
          avgQueryTime: queryStats.avgQueryTime,
          queriesPerSecond: serverStats.queries.qps,
        }
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error("Error fetching database performance metrics:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch database performance metrics",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// Helper function to format uptime
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

// Helper function to format bytes
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
