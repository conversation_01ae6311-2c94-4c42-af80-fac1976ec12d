"use client";

import { useSession } from "next-auth/react";
import { useAdminSessionValidation } from "@/hooks/useSessionValidation";
import { CheckCircleIcon, ExclamationTriangleIcon, XCircleIcon } from "@heroicons/react/24/outline";

export function SessionStatusIndicator() {
  const { data: session, status } = useSession();
  const { isValid } = useAdminSessionValidation({
    checkInterval: 10000, // Check every 10 seconds for demo
    redirectOnInvalid: false, // Don't redirect for this indicator
    showToast: false // Don't show toast for this indicator
  });

  if (status === "loading") {
    return (
      <div className="flex items-center space-x-2 text-gray-500">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
        <span className="text-sm">Checking session...</span>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="flex items-center space-x-2 text-red-600">
        <XCircleIcon className="h-4 w-4" />
        <span className="text-sm">Not authenticated</span>
      </div>
    );
  }

  if (!isValid) {
    return (
      <div className="flex items-center space-x-2 text-red-600">
        <XCircleIcon className="h-4 w-4" />
        <span className="text-sm">Session invalid</span>
      </div>
    );
  }

  const userStatus = session?.user?.status;
  const isActive = session?.user?.isActive;

  if (userStatus !== 'active' || !isActive) {
    return (
      <div className="flex items-center space-x-2 text-yellow-600">
        <ExclamationTriangleIcon className="h-4 w-4" />
        <span className="text-sm">Account {userStatus}</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 text-green-600">
      <CheckCircleIcon className="h-4 w-4" />
      <span className="text-sm">Session active</span>
    </div>
  );
}
