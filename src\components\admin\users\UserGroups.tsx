"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Card } from "@/components/ui/Card";
import { toast } from "react-hot-toast";
import {
  MagnifyingGlassIcon,
  EyeIcon,
  TrashIcon,
  UserGroupIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

interface Group {
  id: string;
  name: string;
  description: string | null;
  visibility: string;
  category: string | null;
  rules: string | null;
  postPermission: string;
  coverImage: string | null;
  createdAt: string;
  updatedAt: string;
  memberCount: number;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface UserGroupsProps {
  userId: string;
}

export function UserGroups({ userId }: UserGroupsProps) {
  const [groups, setGroups] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });

  useEffect(() => {
    fetchGroups();
  }, [userId, pagination.page]);

  const fetchGroups = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      const response = await fetch(`/api/admin/users/${userId}/groups?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch groups");
      }

      const data = await response.json();
      setGroups(data.groups);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching groups:", error);
      toast.error("Failed to fetch groups");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchGroups();
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const getVisibilityBadge = (visibility: string) => {
    const variants = {
      public: "success",
      "private-visible": "warning",
      "private-hidden": "default",
    } as const;

    const labels = {
      public: "Public",
      "private-visible": "Private (Visible)",
      "private-hidden": "Private (Hidden)",
    } as const;

    return (
      <Badge variant={variants[visibility as keyof typeof variants] || "default"}>
        {labels[visibility as keyof typeof labels] || visibility}
      </Badge>
    );
  };

  const getPostPermissionBadge = (permission: string) => {
    return (
      <Badge variant={permission === "all-members" ? "primary" : "warning"}>
        {permission === "all-members" ? "All Members" : "Admin Only"}
      </Badge>
    );
  };

  if (isLoading && groups.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Created Groups</h3>
          <p className="text-sm text-gray-600">
            Total: {pagination.total} groups
          </p>
        </div>

        {/* Search */}
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              className="pl-10 w-64"
            />
          </div>
          <Button onClick={handleSearch}>Search</Button>
        </div>
      </div>

      {/* Groups List */}
      {groups.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-gray-500">
            <UserGroupIcon className="mx-auto h-12 w-12 mb-4" />
            <h3 className="text-lg font-medium mb-2">No groups found</h3>
            <p>This user hasn't created any groups yet.</p>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {groups.map((group) => (
            <Card key={group.id} className="p-6">
              {/* Group Cover */}
              <div className="mb-4">
                {group.coverImage ? (
                  <img
                    src={group.coverImage}
                    alt={group.name}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-full h-32 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <UserGroupIcon className="h-12 w-12 text-white" />
                  </div>
                )}
              </div>

              {/* Group Info */}
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <h4 className="text-lg font-semibold text-gray-900">
                    {group.name}
                  </h4>
                </div>

                {group.description && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {group.description}
                  </p>
                )}

                {/* Badges */}
                <div className="flex flex-wrap gap-2">
                  {getVisibilityBadge(group.visibility)}
                  {getPostPermissionBadge(group.postPermission)}
                  {group.category && (
                    <Badge variant="outline">{group.category}</Badge>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>{group.memberCount} members</span>
                  <span>{new Date(group.createdAt).toLocaleDateString()}</span>
                </div>

                {/* Rules Preview */}
                {group.rules && (
                  <div className="text-xs text-gray-500">
                    <span className="font-medium">Rules:</span>{" "}
                    {group.rules.length > 100
                      ? group.rules.substring(0, 100) + "..."
                      : group.rules
                    }
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
            {pagination.total} results
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={pagination.page === page ? "primary" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              Next
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
