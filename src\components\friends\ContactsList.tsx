"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { 
  UserIcon, 
  ChatBubbleLeftRightIcon, 
  VideoCameraIcon, 
  ArrowLeftIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
  ClockIcon
} from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { toast } from "react-hot-toast";

interface Contact {
  id: string;
  name: string;
  username: string;
  image: string | null;
  onlineStatus: 'online' | 'away' | 'offline';
  lastSeenText: string;
  lastSeen: string | null;
  showOnlineStatus: boolean;
  createdAt: string;
}

interface ContactsResponse {
  contacts: Contact[];
  stats: {
    total: number;
    online: number;
    offline: number;
  };
  pagination: {
    limit: number;
    hasMore: boolean;
  };
}

export function ContactsList() {
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [stats, setStats] = useState({ total: 0, online: 0, offline: 0 });
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all'); // 'all', 'online', 'offline'
  const [pagination, setPagination] = useState({ limit: 20, hasMore: true });

  useEffect(() => {
    if (session?.user) {
      fetchContacts();
      
      // Update status every 30 seconds
      const interval = setInterval(() => {
        updateUserStatus();
        // Refresh contacts every minute to update online status
        if (Date.now() % 60000 < 30000) {
          fetchContacts(false);
        }
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [session, searchQuery, activeFilter]);

  const fetchContacts = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(activeFilter === 'online' && { onlineOnly: 'true' })
      });

      const response = await fetch(`/api/friends/contacts?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch contacts');
      }

      const data: ContactsResponse = await response.json();
      setContacts(data.contacts || []);
      setStats(data.stats);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching contacts:', error);
      toast.error('Failed to load contacts');
    } finally {
      setLoading(false);
    }
  };

  const loadMoreContacts = async () => {
    if (!pagination.hasMore || loadingMore) return;

    try {
      setLoadingMore(true);
      
      const params = new URLSearchParams({
        limit: '20',
        offset: contacts.length.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(activeFilter === 'online' && { onlineOnly: 'true' })
      });

      const response = await fetch(`/api/friends/contacts?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to load more contacts');
      }

      const data: ContactsResponse = await response.json();
      setContacts(prev => [...prev, ...data.contacts]);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error loading more contacts:', error);
      toast.error('Failed to load more contacts');
    } finally {
      setLoadingMore(false);
    }
  };

  const updateUserStatus = async () => {
    try {
      await fetch('/api/users/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'online' }),
      });
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleStartChat = (contact: Contact) => {
    // Navigate to messages page with the contact
    window.location.href = `/messages/${contact.username}`;
  };

  const handleVideoCall = (contact: Contact) => {
    // For now, show a toast. In future, implement video call
    toast.success(`Video call feature coming soon for ${contact.name}!`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'offline':
      default:
        return 'Offline';
    }
  };

  const filteredContacts = contacts.filter(contact => {
    if (activeFilter === 'online') {
      return contact.onlineStatus === 'online';
    } else if (activeFilter === 'offline') {
      return contact.onlineStatus === 'offline';
    }
    return true;
  });

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm">
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center space-x-4">
            <Link 
              href="/"
              className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
            >
              <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
            </Link>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Contacts</h2>
              <p className="text-sm text-gray-600 mt-1">Loading contacts...</p>
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="flex items-center space-x-3 p-4 border border-gray-100 rounded-lg animate-pulse">
                <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center space-x-4 mb-4">
          <Link 
            href="/"
            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
          </Link>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Contacts</h2>
            <p className="text-sm text-gray-600 mt-1">
              {stats.online} online • {stats.total} total contacts
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative mb-4">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search contacts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          {[
            { key: 'all', label: 'All', icon: UserGroupIcon, count: stats.total },
            { key: 'online', label: 'Online', icon: UserIcon, count: stats.online },
            { key: 'offline', label: 'Offline', icon: ClockIcon, count: stats.offline }
          ].map((filter) => (
            <button
              key={filter.key}
              onClick={() => setActiveFilter(filter.key)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                activeFilter === filter.key
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <filter.icon className="h-4 w-4 mr-2" />
              {filter.label} ({filter.count})
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {filteredContacts.length === 0 ? (
          <div className="text-center py-12">
            <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No contacts found' : 'No contacts yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery 
                ? `No contacts match "${searchQuery}"`
                : "Start connecting with people to see them here."
              }
            </p>
            {!searchQuery && (
              <Link
                href="/connection?tab=suggestions"
                className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
              >
                <UserIcon className="h-4 w-4 mr-2" />
                Find Friends
              </Link>
            )}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredContacts.map((contact) => (
                <div 
                  key={contact.id} 
                  className="flex items-center space-x-3 p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow duration-200 cursor-pointer group"
                  onClick={() => handleStartChat(contact)}
                >
                  <div className="relative">
                    <div className="h-12 w-12 flex-shrink-0 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 flex items-center justify-center shadow-sm">
                      {contact.image ? (
                        <OptimizedImage
                          src={contact.image}
                          alt={contact.name}
                          width={48}
                          height={48}
                          className="rounded-full object-cover"
                        />
                      ) : (
                        <UserIcon className="h-6 w-6 text-gray-500" />
                      )}
                    </div>
                    {contact.showOnlineStatus && (
                      <span 
                        className={`absolute bottom-0 right-0 block h-3 w-3 rounded-full ring-2 ring-white ${getStatusColor(contact.onlineStatus)}`}
                        title={getStatusText(contact.onlineStatus)}
                      ></span>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {contact.name}
                      </h3>
                      <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStartChat(contact);
                          }}
                          className="p-1.5 rounded-full hover:bg-blue-100 text-blue-500"
                          title="Send Message"
                        >
                          <ChatBubbleLeftRightIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleVideoCall(contact);
                          }}
                          className="p-1.5 rounded-full hover:bg-green-100 text-green-500"
                          title="Video Call"
                        >
                          <VideoCameraIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mb-1">@{contact.username}</p>
                    <p className="text-xs text-gray-500">
                      {contact.showOnlineStatus ? contact.lastSeenText : 'Status hidden'}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More Button */}
            {pagination.hasMore && (
              <div className="mt-8 text-center">
                <button
                  onClick={loadMoreContacts}
                  disabled={loadingMore}
                  className="px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingMore ? (
                    <>
                      <div className="inline-block h-4 w-4 border border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
