import { MainLayout } from "@/components/layout/MainLayout";
import { getCurrentUser } from "@/lib/utils/auth";
import { FanPagesDirectory } from "@/components/fan-pages/FanPagesDirectory";

export const dynamic = 'force-dynamic';

export default async function FanPagesPage() {
  const user = await getCurrentUser();

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 pointer-events-none opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.02'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '40px 40px'
        }}></div>
        {/* Main Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <FanPagesDirectory currentUserId={user?.id} />
        </div>
      </div>
    </MainLayout>
  );
}

export const metadata = {
  title: "Fan Pages | HIFNF",
  description: "Discover and follow fan pages from your favorite creators, brands, and public figures.",
};
