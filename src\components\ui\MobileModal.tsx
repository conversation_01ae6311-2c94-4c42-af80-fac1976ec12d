"use client";

import { Fragment, ReactNode, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { useMobileView } from '@/hooks/useClientSide';

interface MobileModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  className?: string;
  showCloseButton?: boolean;
  fullScreen?: boolean;
  slideFrom?: 'bottom' | 'top' | 'left' | 'right';
}

export function MobileModal({
  isOpen,
  onClose,
  title,
  children,
  className,
  showCloseButton = true,
  fullScreen = false,
  slideFrom = 'bottom'
}: MobileModalProps) {
  const isMobile = useMobileView();

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const getTransitionClasses = () => {
    switch (slideFrom) {
      case 'bottom':
        return {
          enter: 'translate-y-full',
          enterTo: 'translate-y-0',
          leave: 'translate-y-0',
          leaveTo: 'translate-y-full'
        };
      case 'top':
        return {
          enter: '-translate-y-full',
          enterTo: 'translate-y-0',
          leave: 'translate-y-0',
          leaveTo: '-translate-y-full'
        };
      case 'left':
        return {
          enter: '-translate-x-full',
          enterTo: 'translate-x-0',
          leave: 'translate-x-0',
          leaveTo: '-translate-x-full'
        };
      case 'right':
        return {
          enter: 'translate-x-full',
          enterTo: 'translate-x-0',
          leave: 'translate-x-0',
          leaveTo: 'translate-x-full'
        };
      default:
        return {
          enter: 'translate-y-full',
          enterTo: 'translate-y-0',
          leave: 'translate-y-0',
          leaveTo: 'translate-y-full'
        };
    }
  };

  const transitions = getTransitionClasses();

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        {/* Backdrop */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className={cn(
            "flex min-h-full items-end justify-center",
            !isMobile && "items-center p-4"
          )}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom={cn("opacity-0", transitions.enter)}
              enterTo={cn("opacity-100", transitions.enterTo)}
              leave="ease-in duration-200"
              leaveFrom={cn("opacity-100", transitions.leave)}
              leaveTo={cn("opacity-0", transitions.leaveTo)}
            >
              <Dialog.Panel className={cn(
                "w-full transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all",
                isMobile ? (
                  fullScreen 
                    ? "h-full rounded-none" 
                    : "max-h-[90vh] rounded-t-2xl"
                ) : "max-w-md rounded-2xl",
                className
              )}>
                {/* Header */}
                {(title || showCloseButton) && (
                  <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    {title && (
                      <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                        {title}
                      </Dialog.Title>
                    )}
                    {showCloseButton && (
                      <button
                        type="button"
                        className="rounded-full p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
                        onClick={onClose}
                      >
                        <span className="sr-only">Close</span>
                        <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                      </button>
                    )}
                  </div>
                )}

                {/* Content */}
                <div className={cn(
                  "overflow-y-auto",
                  fullScreen && isMobile ? "h-full" : "max-h-[70vh]"
                )}>
                  {children}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
