import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { VideoCameraIcon, PlusIcon } from "@heroicons/react/24/outline";

export default async function VideosPage() {
  await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Videos
          </h1>
          <Button>
            <PlusIcon className="h-5 w-5 mr-1" />
            Upload Video
          </Button>
        </div>

        <div className="mb-8 flex space-x-2 overflow-x-auto pb-2">
          <Button variant="outline">Your Videos</Button>
          <Button variant="outline">Live Videos</Button>
          <Button variant="outline">Saved</Button>
        </div>

        {/* Your videos section */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Your Videos
          </h2>
          
          {/* Empty state */}
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="p-8 text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                <VideoCameraIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                No videos yet
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Videos you upload or create will appear here.
              </p>
              <div className="mt-6">
                <Button>
                  Upload Video
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Recommended videos section */}
        <div>
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Recommended Videos
          </h2>
          
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {/* Video cards */}
            <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
              <div className="aspect-video bg-gray-200 relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-12 w-12 rounded-full bg-black/50 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900">How to Use HIFNF Features</h3>
                <p className="text-xs text-gray-500 mt-1">HIFNF Official • 5.2K views • 2 days ago</p>
              </div>
            </div>
            
            <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
              <div className="aspect-video bg-gray-200 relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-12 w-12 rounded-full bg-black/50 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900">Community Highlights</h3>
                <p className="text-xs text-gray-500 mt-1">HIFNF Community • 3.7K views • 1 week ago</p>
              </div>
            </div>
            
            <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
              <div className="aspect-video bg-gray-200 relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-12 w-12 rounded-full bg-black/50 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900">Tips & Tricks for Better Posts</h3>
                <p className="text-xs text-gray-500 mt-1">HIFNF Tips • 8.1K views • 2 weeks ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
