"use client";

import { Badge } from "@/components/ui/Badge";
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon
} from "@heroicons/react/24/outline";

interface MonetizationStatusBadgeProps {
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export function MonetizationStatusBadge({ 
  status, 
  size = 'sm', 
  showIcon = true,
  className = ""
}: MonetizationStatusBadgeProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          variant: 'warning' as const,
          icon: ClockIcon,
          label: 'Monetization Pending',
          shortLabel: 'Pending'
        };
      case 'approved':
        return {
          variant: 'success' as const,
          icon: CheckCircleIcon,
          label: 'Monetized',
          shortLabel: 'Monetized'
        };
      case 'rejected':
        return {
          variant: 'destructive' as const,
          icon: XCircleIcon,
          label: 'Monetization Rejected',
          shortLabel: 'Rejected'
        };
      case 'suspended':
        return {
          variant: 'secondary' as const,
          icon: ExclamationTriangleIcon,
          label: 'Monetization Suspended',
          shortLabel: 'Suspended'
        };
      default:
        return {
          variant: 'secondary' as const,
          icon: CurrencyDollarIcon,
          label: status,
          shortLabel: status
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;
  
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <Badge 
      variant={config.variant} 
      className={`flex items-center gap-1 ${sizeClasses[size]} ${className}`}
    >
      {showIcon && <IconComponent className={iconSizes[size]} />}
      <span>{size === 'sm' ? config.shortLabel : config.label}</span>
    </Badge>
  );
}
