import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { z } from "zod";

const calculateFeeSchema = z.object({
  amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Amount must be a positive number"),
});

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = calculateFeeSchema.parse(body);

    // Check if withdrawals are enabled
    const withdrawEnabled = await WalletService.getWalletSetting('withdraw_enabled');
    if (withdrawEnabled === 'false') {
      return NextResponse.json(
        { 
          success: false,
          message: "Withdrawals are currently disabled" 
        },
        { status: 400 }
      );
    }

    // Get withdrawal settings
    const minAmount = parseFloat(await WalletService.getWalletSetting('withdraw_min_amount') || '10.00');
    const maxAmount = parseFloat(await WalletService.getWalletSetting('withdraw_max_amount') || '10000.00');
    const feePercentage = parseFloat(await WalletService.getWalletSetting('withdraw_fee_percentage') || '2.00');
    const feeFixed = parseFloat(await WalletService.getWalletSetting('withdraw_fee_fixed') || '0.00');

    const amount = parseFloat(validatedData.amount);

    // Validate amount limits
    if (amount < minAmount) {
      return NextResponse.json(
        { 
          success: false,
          message: `Minimum withdrawal amount is $${minAmount.toFixed(2)}` 
        },
        { status: 400 }
      );
    }

    if (amount > maxAmount) {
      return NextResponse.json(
        { 
          success: false,
          message: `Maximum withdrawal amount is $${maxAmount.toFixed(2)}` 
        },
        { status: 400 }
      );
    }

    // Calculate fees
    const percentageFee = (amount * feePercentage) / 100;
    const totalFee = percentageFee + feeFixed;
    const netAmount = amount - totalFee;

    // Get user's earning balance
    const userWallet = await WalletService.getOrCreateWallet(session.user.id);
    const earningBalance = parseFloat(userWallet.earningBalance);
    const hasSufficientBalance = earningBalance >= amount;

    return NextResponse.json({
      success: true,
      data: {
        amount: amount.toFixed(2),
        feeBreakdown: {
          percentage: feePercentage,
          percentageFee: percentageFee.toFixed(2),
          fixedFee: feeFixed.toFixed(2),
          totalFee: totalFee.toFixed(2),
        },
        netAmount: netAmount.toFixed(2),
        limits: {
          minimum: minAmount.toFixed(2),
          maximum: maxAmount.toFixed(2),
        },
        balance: {
          earning: earningBalance.toFixed(2),
          hasSufficientBalance,
        },
      },
    });
  } catch (error: any) {
    console.error("Error calculating withdrawal fee:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid amount",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: "Failed to calculate withdrawal fee" 
      },
      { status: 500 }
    );
  }
}
