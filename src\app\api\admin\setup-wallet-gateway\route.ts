import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { paymentGateways } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export async function POST() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Check if wallet gateway already exists
    const existingWallet = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.name, 'wallet')
    });

    if (existingWallet) {
      return NextResponse.json({
        success: true,
        message: "Wallet payment gateway already exists",
        data: existingWallet
      });
    }

    // Add wallet payment gateway
    const walletGateway = {
      id: uuidv4(),
      name: 'wallet',
      displayName: 'General Wallet Balance',
      type: 'wallet' as const,
      isActive: true,
      config: {
        description: 'Pay using your general wallet balance',
        instantPayment: true,
        requiresBalance: true,
      },
      depositFee: '0.00',
      depositFixedFee: '0.00',
      minDeposit: '0.01',
      maxDeposit: '999999.99',
      currency: 'USD',
      sortOrder: 1
    };

    await db.insert(paymentGateways).values(walletGateway);

    return NextResponse.json({
      success: true,
      message: "Wallet payment gateway added successfully",
      data: walletGateway
    });

  } catch (error) {
    console.error("Error setting up wallet gateway:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to setup wallet gateway",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
