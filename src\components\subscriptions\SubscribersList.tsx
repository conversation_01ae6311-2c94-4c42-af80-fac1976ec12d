"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { 
  UserIcon, 
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  UserPlusIcon,
  UserMinusIcon
} from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";
import { formatDistanceToNow } from "date-fns";
import { subscribeToUser, unsubscribeFromUser, getSubscriptionStatus } from "@/app/actions/subscription";

interface Subscriber {
  id: string;
  subscriberId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
  isSubscribedBack?: boolean;
}

interface SubscribersResponse {
  subscribers: Subscriber[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

interface SubscribersListProps {
  searchQuery?: string;
}

export function SubscribersList({ searchQuery = "" }: SubscribersListProps) {
  const { data: session } = useSession();
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [pagination, setPagination] = useState({
    page: 1,
    hasMore: true
  });
  const [processingUsers, setProcessingUsers] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (session?.user) {
      fetchSubscribers();
    }
  }, [session]);

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
    if (searchQuery !== localSearchQuery) {
      fetchSubscribers(1, false);
    }
  }, [searchQuery]);

  const fetchSubscribers = async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const params = new URLSearchParams({
        type: 'subscribers',
        limit: '20',
        offset: ((page - 1) * 20).toString(),
        ...(localSearchQuery && { search: localSearchQuery })
      });

      const response = await fetch(`/api/subscriptions?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscribers');
      }

      const data: SubscribersResponse = await response.json();
      
      // Check subscription status for each subscriber
      const subscribersWithStatus = await Promise.all(
        data.subscribers.map(async (subscriber) => {
          try {
            const statusResult = await getSubscriptionStatus(subscriber.subscriberId);
            return {
              ...subscriber,
              isSubscribedBack: statusResult.success ? statusResult.isSubscribed : false
            };
          } catch (error) {
            console.error('Error checking subscription status:', error);
            return { ...subscriber, isSubscribedBack: false };
          }
        })
      );
      
      if (append) {
        setSubscribers(prev => [...prev, ...subscribersWithStatus]);
      } else {
        setSubscribers(subscribersWithStatus);
      }
      
      setPagination({
        page: data.pagination.page,
        hasMore: data.pagination.hasMore
      });
    } catch (error) {
      console.error('Error fetching subscribers:', error);
      toast.error('Failed to load subscribers');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreSubscribers = () => {
    if (pagination.hasMore && !loadingMore) {
      fetchSubscribers(pagination.page + 1, true);
    }
  };

  const handleSubscribeBack = async (subscriberId: string, name: string) => {
    if (!session?.user) return;

    setProcessingUsers(prev => new Set(prev).add(subscriberId));

    try {
      const result = await subscribeToUser(subscriberId);

      if (result.success) {
        // Update local state
        setSubscribers(prev => prev.map(sub => 
          sub.subscriberId === subscriberId 
            ? { ...sub, isSubscribedBack: true }
            : sub
        ));
        toast.success(`Subscribed to ${name}`);
      } else {
        toast.error(result.message || 'Failed to subscribe');
      }
    } catch (error) {
      console.error('Error subscribing:', error);
      toast.error('Failed to subscribe');
    } finally {
      setProcessingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(subscriberId);
        return newSet;
      });
    }
  };

  const handleUnsubscribeBack = async (subscriberId: string, name: string) => {
    if (!session?.user) return;

    setProcessingUsers(prev => new Set(prev).add(subscriberId));

    try {
      const result = await unsubscribeFromUser(subscriberId);

      if (result.success) {
        // Update local state
        setSubscribers(prev => prev.map(sub => 
          sub.subscriberId === subscriberId 
            ? { ...sub, isSubscribedBack: false }
            : sub
        ));
        toast.success(`Unsubscribed from ${name}`);
      } else {
        toast.error(result.message || 'Failed to unsubscribe');
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
      toast.error('Failed to unsubscribe');
    } finally {
      setProcessingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(subscriberId);
        return newSet;
      });
    }
  };

  const handleStartChat = (subscriber: Subscriber) => {
    // This will be implemented when chat functionality is added
    toast.success(`Starting chat with ${subscriber.name}`);
  };

  // Filter subscribers based on search query
  const filteredSubscribers = subscribers.filter(subscriber =>
    subscriber.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
    subscriber.username.toLowerCase().includes(localSearchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg animate-pulse">
            <div className="h-16 w-16 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
            <div className="flex space-x-2">
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (filteredSubscribers.length === 0) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {localSearchQuery ? 'No subscribers found' : 'No subscribers yet'}
        </h3>
        <p className="text-gray-600 mb-4">
          {localSearchQuery 
            ? `No subscribers match "${localSearchQuery}"`
            : "When people subscribe to you, they'll appear here."
          }
        </p>
        {!localSearchQuery && (
          <Link
            href="/subscriptions?tab=suggestions"
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <UserIcon className="h-4 w-4 mr-2" />
            Discover People
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {filteredSubscribers.map((subscriber) => (
        <div key={subscriber.id} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors duration-200">
          <div className="relative h-16 w-16 flex-shrink-0">
            {subscriber.image ? (
              <OptimizedImage
                src={subscriber.image}
                alt={subscriber.name}
                width={64}
                height={64}
                className="rounded-full object-cover"
              />
            ) : (
              <div className="h-16 w-16 bg-gray-200 rounded-full flex items-center justify-center">
                <UserIcon className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div>
                <Link 
                  href={`/user/${subscriber.username}`}
                  className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200"
                >
                  {subscriber.name}
                </Link>
                <p className="text-sm text-gray-500">@{subscriber.username}</p>
                <p className="text-xs text-gray-400 mt-1">
                  Subscribed {formatDistanceToNow(new Date(subscriber.createdAt), { addSuffix: true })}
                </p>
                {subscriber.isSubscribedBack && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                    Mutual subscription
                  </span>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2 mt-3">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleStartChat(subscriber)}
                className="flex-1 flex items-center justify-center space-x-1"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
                <span>Message</span>
              </Button>
              <Link
                href={`/user/${subscriber.username}`}
                className="flex-1"
              >
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full flex items-center justify-center space-x-1"
                >
                  <UserIcon className="h-4 w-4" />
                  <span>Profile</span>
                </Button>
              </Link>
              {subscriber.isSubscribedBack ? (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleUnsubscribeBack(subscriber.subscriberId, subscriber.name)}
                  disabled={processingUsers.has(subscriber.subscriberId)}
                  className="flex items-center justify-center space-x-1 bg-blue-50 text-blue-600 hover:bg-blue-100"
                >
                  <UserMinusIcon className="h-4 w-4" />
                  <span>{processingUsers.has(subscriber.subscriberId) ? 'Processing...' : 'Subscribed'}</span>
                </Button>
              ) : (
                <Button
                  size="sm"
                  variant="primary"
                  onClick={() => handleSubscribeBack(subscriber.subscriberId, subscriber.name)}
                  disabled={processingUsers.has(subscriber.subscriberId)}
                  className="flex items-center justify-center space-x-1"
                >
                  <UserPlusIcon className="h-4 w-4" />
                  <span>{processingUsers.has(subscriber.subscriberId) ? 'Processing...' : 'Subscribe Back'}</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      ))}

      {/* Load More Button */}
      {pagination.hasMore && (
        <div className="text-center pt-4">
          <Button
            variant="outline"
            onClick={loadMoreSubscribers}
            disabled={loadingMore}
            className="px-8"
          >
            {loadingMore ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
}
