"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { formatTimeAgo } from "@/lib/utils";
import {
  UserIcon,
  ShieldCheckIcon,
  UserPlusIcon,
  UserMinusIcon,
  ChevronUpIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/outline";

interface Member {
  id: string;
  userId: string;
  groupId: string;
  role: "admin" | "moderator" | "member" | "pending";
  createdAt: Date;
  user: {
    id: string;
    name: string | null;
    username: string | null;
    image: string | null;
  };
}

interface GroupMembersListProps {
  groupId: string;
  isAdmin: boolean;
  isModerator: boolean;
  isCreator: boolean;
  currentUserId: string;
}

export function GroupMembersList({
  groupId,
  isAdmin,
  isModerator,
  isCreator,
  currentUserId,
}: GroupMembersListProps) {
  const [members, setMembers] = useState<Member[]>([]);
  const [pendingMembers, setPendingMembers] = useState<Member[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showPendingRequests, setShowPendingRequests] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    fetchMembers();
  }, []);

  const fetchMembers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch regular members
      const membersResponse = await fetch(`/api/groups/${groupId}/members?role=member`);
      if (!membersResponse.ok) {
        throw new Error("Failed to fetch members");
      }
      const membersData = await membersResponse.json();
      setMembers(membersData);

      // Fetch admins and moderators
      const adminsResponse = await fetch(`/api/groups/${groupId}/members?role=admin`);
      const modsResponse = await fetch(`/api/groups/${groupId}/members?role=moderator`);
      
      if (adminsResponse.ok && modsResponse.ok) {
        const adminsData = await adminsResponse.json();
        const modsData = await modsResponse.json();
        setMembers(prev => [...prev, ...adminsData, ...modsData]);
      }

      // Fetch pending members if admin or moderator
      if (isAdmin || isModerator) {
        const pendingResponse = await fetch(`/api/groups/${groupId}/members?role=pending`);
        if (pendingResponse.ok) {
          const pendingData = await pendingResponse.json();
          setPendingMembers(pendingData);
        }
      }
    } catch (error) {
      console.error("Error fetching members:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleMemberAction = async (userId: string, action: string) => {
    try {
      setActionLoading(userId);
      setError(null);

      const response = await fetch(`/api/groups/${groupId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          action,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${action} member`);
      }

      // Refresh the member list
      await fetchMembers();
    } catch (error) {
      console.error(`Error performing ${action}:`, error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setActionLoading(null);
    }
  };

  const filteredMembers = members.filter(member => 
    member.user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.user.username?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredPendingMembers = pendingMembers.filter(member => 
    member.user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.user.username?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "admin":
        return (
          <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            <ShieldCheckIcon className="mr-1 h-3 w-3" />
            Admin
          </span>
        );
      case "moderator":
        return (
          <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            <ShieldCheckIcon className="mr-1 h-3 w-3" />
            Moderator
          </span>
        );
      case "pending":
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
            Pending
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <h2 className="text-xl font-semibold text-gray-900">
          Group Members ({members.length})
        </h2>
        <div className="w-full sm:w-64">
          <Input
            type="search"
            placeholder="Search members..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pending requests section (for admins and moderators) */}
      {(isAdmin || isModerator) && pendingMembers.length > 0 && (
        <div className="rounded-lg bg-white shadow overflow-hidden">
          <div 
            className="flex items-center justify-between px-4 py-3 bg-gray-50 cursor-pointer"
            onClick={() => setShowPendingRequests(!showPendingRequests)}
          >
            <h3 className="text-sm font-medium text-gray-900">
              Pending Requests ({pendingMembers.length})
            </h3>
            <button className="text-gray-500 hover:text-gray-700">
              {showPendingRequests ? (
                <ChevronUpIcon className="h-5 w-5" />
              ) : (
                <ChevronDownIcon className="h-5 w-5" />
              )}
            </button>
          </div>
          
          {showPendingRequests && (
            <div className="divide-y divide-gray-200">
              {filteredPendingMembers.length > 0 ? (
                filteredPendingMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0">
                        {member.user.image ? (
                          <Image
                            src={member.user.image}
                            alt={member.user.name || "User"}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded-full"
                          />
                        ) : (
                          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                            {member.user.name?.charAt(0).toUpperCase() || "U"}
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          <a href={`/user/${member.user.username || member.user.id}`}>
                            {member.user.name}
                          </a>
                        </p>
                        <p className="text-xs text-gray-500">
                          Requested {formatTimeAgo(member.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        onClick={() => handleMemberAction(member.userId, "approve")}
                        isLoading={actionLoading === member.userId}
                        disabled={actionLoading === member.userId}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleMemberAction(member.userId, "reject")}
                        disabled={actionLoading === member.userId}
                      >
                        Reject
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-sm text-gray-500">
                  No pending requests match your search.
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Members list */}
      <div className="rounded-lg bg-white shadow overflow-hidden">
        <div className="divide-y divide-gray-200">
          {isLoading ? (
            <div className="p-4 text-center">
              <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
              <p className="mt-2 text-sm text-gray-500">Loading members...</p>
            </div>
          ) : filteredMembers.length > 0 ? (
            filteredMembers.map((member) => (
              <div key={member.id} className="flex items-center justify-between p-4">
                <div className="flex items-center">
                  <div className="h-10 w-10 flex-shrink-0">
                    {member.user.image ? (
                      <Image
                        src={member.user.image}
                        alt={member.user.name || "User"}
                        width={40}
                        height={40}
                        className="h-10 w-10 rounded-full"
                      />
                    ) : (
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                        {member.user.name?.charAt(0).toUpperCase() || "U"}
                      </div>
                    )}
                  </div>
                  <div className="ml-3">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-gray-900">
                        <a href={`/user/${member.user.username || member.user.id}`}>
                          {member.user.name}
                        </a>
                      </p>
                      <div className="ml-2">
                        {getRoleBadge(member.role)}
                      </div>
                    </div>
                    <p className="text-xs text-gray-500">
                      Joined {formatTimeAgo(member.createdAt)}
                    </p>
                  </div>
                </div>
                
                {/* Admin actions */}
                {(isAdmin || (isModerator && member.role === "member")) && 
                 member.userId !== currentUserId && (
                  <div className="flex space-x-2">
                    {isAdmin && member.role === "member" && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleMemberAction(member.userId, "promote")}
                        isLoading={actionLoading === member.userId}
                        disabled={actionLoading === member.userId}
                      >
                        Make Moderator
                      </Button>
                    )}
                    {isAdmin && member.role === "moderator" && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleMemberAction(member.userId, "demote")}
                        isLoading={actionLoading === member.userId}
                        disabled={actionLoading === member.userId}
                      >
                        Remove Moderator
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleMemberAction(member.userId, "remove")}
                      disabled={actionLoading === member.userId}
                    >
                      <UserMinusIcon className="h-4 w-4 mr-1" />
                      Remove
                    </Button>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-sm text-gray-500">
              No members found matching your search.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
