"use client";

import { useState, useEffect } from "react";
import { HeartIcon } from "@heroicons/react/24/outline";
import { PostCard } from "@/components/feed/PostCard";

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  type: 'user_post' | 'fan_page_post';
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  } | null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  } | null;
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
  sharedPost?: {
    id: string;
    content: string;
    images: string[] | null;
    videos: string[] | null;
    backgroundColor: string | null;
    feeling: string | null;
    activity: string | null;
    location: string | null;
    formattedContent: boolean | null;
    createdAt: string;
    user: {
      id: string;
      name: string;
      username?: string | null;
      image: string | null;
    };
  } | null;
}

interface ProfileLikedPostsProps {
  userId: string;
  isOwnProfile: boolean;
}

export function ProfileLikedPosts({ userId, isOwnProfile }: ProfileLikedPostsProps) {
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    fetchLikedPosts();
  }, [userId]);

  const fetchLikedPosts = async (pageNum = 1) => {
    try {
      setIsLoading(pageNum === 1);
      setError(null);
      const response = await fetch(`/api/users/${userId}/liked-posts?page=${pageNum}&limit=10`);

      if (response.ok) {
        const data = await response.json();
        if (pageNum === 1) {
          setPosts(data.posts || []);
        } else {
          setPosts(prev => [...prev, ...(data.posts || [])]);
        }
        setHasMore(data.hasMore || false);
      } else {
        setError('Failed to fetch liked posts');
        console.error('Failed to fetch liked posts');
      }
    } catch (error) {
      setError('Error fetching liked posts');
      console.error('Error fetching liked posts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMore = () => {
    if (hasMore && !isLoading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchLikedPosts(nextPage);
    }
  };

  const handleLike = async (postId: string) => {
    try {
      const response = await fetch(`/api/posts/${postId}/like`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to like post");
      }

      // Update the state optimistically
      setPosts((prevPosts) =>
        prevPosts.map((post) => {
          if (post.id === postId) {
            const newLiked = !post.liked;
            const wasDisliked = post.disliked;
            return {
              ...post,
              liked: newLiked,
              disliked: wasDisliked ? false : post.disliked,
              _count: {
                ...post._count,
                likes: post._count.likes + (newLiked ? 1 : -1),
                dislikes: wasDisliked ? post._count.dislikes - 1 : post._count.dislikes,
              },
            };
          }
          return post;
        })
      );

      // If the post was unliked, remove it from the liked posts list
      if (posts.find(p => p.id === postId)?.liked) {
        setPosts(prev => prev.filter(p => p.id !== postId));
      }
    } catch (error) {
      console.error('Error liking post:', error);
    }
  };

  const handleDislike = async (postId: string) => {
    try {
      const response = await fetch(`/api/posts/${postId}/dislike`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to dislike post");
      }

      // Update the state optimistically
      setPosts((prevPosts) =>
        prevPosts.map((post) => {
          if (post.id === postId) {
            const newDisliked = !post.disliked;
            const wasLiked = post.liked;
            return {
              ...post,
              disliked: newDisliked,
              liked: wasLiked ? false : post.liked,
              _count: {
                ...post._count,
                dislikes: post._count.dislikes + (newDisliked ? 1 : -1),
                likes: wasLiked ? post._count.likes - 1 : post._count.likes,
              },
            };
          }
          return post;
        })
      );

      // If the post was liked and now disliked, remove it from the liked posts list
      if (posts.find(p => p.id === postId)?.liked) {
        setPosts(prev => prev.filter(p => p.id !== postId));
      }
    } catch (error) {
      console.error('Error disliking post:', error);
    }
  };

  if (isLoading && posts.length === 0) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center space-x-3">
          <HeartIcon className="h-6 w-6 text-red-500" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Liked Posts</h2>
            <p className="text-sm text-gray-500 mt-1">
              {posts.length} {posts.length === 1 ? 'post' : 'posts'} liked
            </p>
          </div>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <p className="text-red-700">{error}</p>
          <button
            className="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
            onClick={() => fetchLikedPosts(1)}
          >
            Try Again
          </button>
        </div>
      )}

      {/* Posts */}
      {!error && posts.length > 0 ? (
        <div className="space-y-6">
          {posts.map((post) => (
            <div key={post.id} className="transform transition-all duration-300 hover:translate-y-[-2px]">
              <PostCard
                post={post}
                onLike={() => handleLike(post.id)}
                onDislike={() => handleDislike(post.id)}
              />
            </div>
          ))}

          {/* Load More Button */}
          {hasMore && (
            <div className="text-center py-6">
              <button
                onClick={loadMore}
                disabled={isLoading}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isLoading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </div>
      ) : !error ? (
        <div className="bg-white rounded-xl shadow-sm p-12">
          <div className="text-center">
            <HeartIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No liked posts yet</h3>
            <p className="text-gray-500">
              {isOwnProfile
                ? "Posts you like will appear here. Start exploring and liking posts!"
                : "This user hasn't liked any posts yet."
              }
            </p>
          </div>
        </div>
      ) : null}
    </div>
  );
}
