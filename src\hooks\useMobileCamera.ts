"use client";

import { useState, useRef, useCallback } from 'react';
import { useMobileView } from './useClientSide';

interface UseMobileCameraOptions {
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  acceptedTypes?: string[];
}

interface CameraResult {
  file: File;
  dataUrl: string;
  width: number;
  height: number;
}

export function useMobileCamera({
  quality = 0.8,
  maxWidth = 1920,
  maxHeight = 1080,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp']
}: UseMobileCameraOptions = {}) {
  const isMobile = useMobileView();
  const [isCapturing, setIsCapturing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const compressImage = useCallback((file: File): Promise<CameraResult> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now()
              });
              
              const reader = new FileReader();
              reader.onload = () => {
                resolve({
                  file: compressedFile,
                  dataUrl: reader.result as string,
                  width,
                  height
                });
              };
              reader.readAsDataURL(compressedFile);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }, [quality, maxWidth, maxHeight]);

  const captureFromCamera = useCallback(async (): Promise<CameraResult | null> => {
    if (!isMobile) {
      setError('Camera capture is only available on mobile devices');
      return null;
    }

    setIsCapturing(true);
    setError(null);

    try {
      // Create a file input that accepts camera
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = acceptedTypes.join(',');
      input.capture = 'environment'; // Use back camera

      return new Promise((resolve, reject) => {
        input.onchange = async (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            try {
              const result = await compressImage(file);
              resolve(result);
            } catch (error) {
              reject(error);
            }
          } else {
            resolve(null);
          }
          setIsCapturing(false);
        };

        input.oncancel = () => {
          setIsCapturing(false);
          resolve(null);
        };

        input.click();
      });
    } catch (error) {
      setIsCapturing(false);
      setError(error instanceof Error ? error.message : 'Failed to capture image');
      return null;
    }
  }, [isMobile, acceptedTypes, compressImage]);

  const selectFromGallery = useCallback(async (): Promise<CameraResult | null> => {
    setIsCapturing(true);
    setError(null);

    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = acceptedTypes.join(',');
      input.multiple = false;

      return new Promise((resolve, reject) => {
        input.onchange = async (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            try {
              const result = await compressImage(file);
              resolve(result);
            } catch (error) {
              reject(error);
            }
          } else {
            resolve(null);
          }
          setIsCapturing(false);
        };

        input.oncancel = () => {
          setIsCapturing(false);
          resolve(null);
        };

        input.click();
      });
    } catch (error) {
      setIsCapturing(false);
      setError(error instanceof Error ? error.message : 'Failed to select image');
      return null;
    }
  }, [acceptedTypes, compressImage]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    captureFromCamera,
    selectFromGallery,
    isCapturing,
    error,
    clearError,
    isMobileSupported: isMobile
  };
}
