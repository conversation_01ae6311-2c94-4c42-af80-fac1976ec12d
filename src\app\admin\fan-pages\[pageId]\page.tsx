"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger, TabsContent } from "@/components/ui/Tabs";
import {
  ArrowLeftIcon,
  CheckBadgeIcon,
  XMarkIcon,
  PencilIcon,
  TrashIcon,
  UsersIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CalendarIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  EyeIcon,
  EyeSlashIcon,
} from "@heroicons/react/24/outline";
import { CheckBadgeIcon as CheckBadgeIconSolid } from "@heroicons/react/24/solid";
import { toast } from "react-hot-toast";

interface FanPageData {
  id: string;
  ownerId: string;
  name: string;
  username: string;
  category: string;
  description: string | null;
  profileImage: string | null;
  coverImage: string | null;
  website: string | null;
  email: string | null;
  phone: string | null;
  location: string | null;
  isVerified: boolean;
  isActive: boolean;
  followerCount: number;
  postCount: number;
  createdAt: string;
  updatedAt: string;
  owner: {
    id: string;
    name: string | null;
    email: string | null;
    image: string | null;
    isActive: boolean;
    createdAt: string;
  };
}

interface FanPageStats {
  followers: number;
  posts: number;
  roles: number;
}

export default function AdminFanPageDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const pageId = params?.pageId as string;

  const [isLoading, setIsLoading] = useState(true);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [fanPage, setFanPage] = useState<FanPageData | null>(null);
  const [stats, setStats] = useState<FanPageStats>({
    followers: 0,
    posts: 0,
    roles: 0,
  });
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (pageId) {
      fetchFanPageDetails();
    }
  }, [pageId]);

  const fetchFanPageDetails = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/fan-pages/${pageId}`);

      if (!response.ok) {
        if (response.status === 404) {
          toast.error("Fan page not found");
          router.push("/admin/fan-pages");
          return;
        }
        throw new Error("Failed to fetch fan page details");
      }

      const data = await response.json();
      setFanPage(data.page);
      setStats(data.stats);
    } catch (error) {
      console.error("Error fetching fan page details:", error);
      toast.error("Failed to load fan page details");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyPage = async (verify: boolean) => {
    try {
      setIsActionLoading(true);
      const response = await fetch(`/api/admin/fan-pages/${pageId}/verify`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isVerified: verify }),
      });

      if (!response.ok) throw new Error("Failed to update verification");

      toast.success(`Page ${verify ? "verified" : "unverified"} successfully`);
      fetchFanPageDetails();
    } catch (error) {
      console.error("Error updating verification:", error);
      toast.error("Failed to update verification");
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleToggleStatus = async (isActive: boolean) => {
    try {
      setIsActionLoading(true);
      const response = await fetch(`/api/admin/fan-pages/${pageId}/status`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) throw new Error("Failed to update status");

      toast.success(`Page ${isActive ? "activated" : "deactivated"} successfully`);
      fetchFanPageDetails();
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleDeletePage = async () => {
    if (!confirm("Are you sure you want to delete this fan page? This action cannot be undone.")) {
      return;
    }

    try {
      setIsActionLoading(true);
      const response = await fetch(`/api/admin/fan-pages/${pageId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete page");

      toast.success("Fan page deleted successfully");
      router.push("/admin/fan-pages");
    } catch (error) {
      console.error("Error deleting page:", error);
      toast.error("Failed to delete page");
    } finally {
      setIsActionLoading(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!fanPage) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Fan page not found</h3>
          <p className="mt-1 text-sm text-gray-500">
            The fan page you're looking for doesn't exist.
          </p>
          <Button
            onClick={() => router.push("/admin/fan-pages")}
            className="mt-4"
          >
            Back to Fan Pages
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push("/admin/fan-pages")}
              className="flex items-center"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Fan Pages
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Fan Page Details</h1>
              <p className="text-gray-600">
                Manage fan page information and settings
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => handleVerifyPage(!fanPage.isVerified)}
              variant={fanPage.isVerified ? "destructive" : "primary"}
              disabled={isActionLoading}
            >
              {fanPage.isVerified ? (
                <>
                  <XMarkIcon className="h-4 w-4 mr-2" />
                  Remove Verification
                </>
              ) : (
                <>
                  <CheckBadgeIcon className="h-4 w-4 mr-2" />
                  Verify Page
                </>
              )}
            </Button>
            <Button
              onClick={() => handleToggleStatus(!fanPage.isActive)}
              variant={fanPage.isActive ? "destructive" : "primary"}
              disabled={isActionLoading}
            >
              {fanPage.isActive ? (
                <>
                  <EyeSlashIcon className="h-4 w-4 mr-2" />
                  Deactivate
                </>
              ) : (
                <>
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Activate
                </>
              )}
            </Button>
            <Button
              onClick={handleDeletePage}
              variant="destructive"
              disabled={isActionLoading}
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete Page
            </Button>
          </div>
        </div>

        {/* Fan Page Header */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Cover Image */}
          <div className="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            {fanPage.coverImage && (
              <OptimizedImage
                src={fanPage.coverImage}
                alt="Cover"
                fill
                className="object-cover"
              />
            )}
          </div>

          {/* Profile Info */}
          <div className="px-6 py-4">
            <div className="flex items-start space-x-4">
              <div className="relative -mt-16">
                <OptimizedImage
                  src={fanPage.profileImage || "/images/default-avatar.png"}
                  alt={fanPage.name}
                  width={120}
                  height={120}
                  className="w-30 h-30 rounded-full border-4 border-white object-cover"
                />
              </div>
              <div className="flex-1 pt-4">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold text-gray-900">{fanPage.name}</h2>
                  {fanPage.isVerified && (
                    <CheckBadgeIconSolid className="h-6 w-6 text-blue-500" />
                  )}
                  <Badge variant={fanPage.isActive ? "success" : "danger"}>
                    {fanPage.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <p className="text-gray-600">@{fanPage.username}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <Badge variant="outline">
                    {fanPage.category.replace("_", " ").toUpperCase()}
                  </Badge>
                  <div className="flex items-center text-sm text-gray-500">
                    <UsersIcon className="h-4 w-4 mr-1" />
                    {stats.followers.toLocaleString()} followers
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    {stats.posts.toLocaleString()} posts
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="owner">Owner Info</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <p className="mt-1 text-sm text-gray-900">{fanPage.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Username</label>
                    <p className="mt-1 text-sm text-gray-900">@{fanPage.username}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Category</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {fanPage.category.replace("_", " ").toUpperCase()}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {fanPage.description || "No description provided"}
                    </p>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div className="space-y-4">
                  {fanPage.website && (
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <a
                        href={fanPage.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {fanPage.website}
                      </a>
                    </div>
                  )}
                  {fanPage.email && (
                    <div className="flex items-center">
                      <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <span className="text-gray-900">{fanPage.email}</span>
                    </div>
                  )}
                  {fanPage.phone && (
                    <div className="flex items-center">
                      <PhoneIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <span className="text-gray-900">{fanPage.phone}</span>
                    </div>
                  )}
                  {fanPage.location && (
                    <div className="flex items-center">
                      <MapPinIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <span className="text-gray-900">{fanPage.location}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Statistics */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{stats.followers}</div>
                    <div className="text-sm text-gray-500">Followers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.posts}</div>
                    <div className="text-sm text-gray-500">Posts</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{stats.roles}</div>
                    <div className="text-sm text-gray-500">Team Members</div>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Timestamps</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(fanPage.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(fanPage.updatedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="owner" className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Page Owner Information</h3>
              <div className="flex items-start space-x-4">
                <OptimizedImage
                  src={fanPage.owner.image || "/images/default-avatar.png"}
                  alt={fanPage.owner.name || "Owner"}
                  width={64}
                  height={64}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Name</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {fanPage.owner.name || "Unknown"}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {fanPage.owner.email || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Status</label>
                      <Badge variant={fanPage.owner.isActive ? "success" : "danger"}>
                        {fanPage.owner.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Member Since</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {new Date(fanPage.owner.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button
                      onClick={() => router.push(`/admin/users/${fanPage.ownerId}`)}
                      variant="outline"
                    >
                      View User Profile
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
              <p className="text-gray-500">Activity tracking will be implemented here.</p>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Page Settings</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Verification Status</h4>
                    <p className="text-sm text-gray-500">
                      Verified pages get a blue checkmark badge
                    </p>
                  </div>
                  <Badge variant={fanPage.isVerified ? "success" : "outline"}>
                    {fanPage.isVerified ? "Verified" : "Not Verified"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Active Status</h4>
                    <p className="text-sm text-gray-500">
                      Inactive pages are hidden from public view
                    </p>
                  </div>
                  <Badge variant={fanPage.isActive ? "success" : "danger"}>
                    {fanPage.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
