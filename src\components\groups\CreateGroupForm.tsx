"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, PhotoIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";

const groupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(255, "Name must be less than 255 characters"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  visibility: z.enum(["public", "private-visible", "private-hidden"]),
  category: z.string().max(100, "Category must be less than 100 characters").optional(),
  rules: z.string().max(1000, "Rules must be less than 1000 characters").optional(),
  postPermission: z.enum(["all-members", "admin-only"]),
});

type GroupFormData = z.infer<typeof groupSchema>;

interface CreateGroupFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateGroupForm({ isOpen, onClose }: CreateGroupFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<GroupFormData>({
    resolver: zodResolver(groupSchema),
    defaultValues: {
      visibility: "public",
      postPermission: "all-members",
    },
  });

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setCoverImage(file);
      setCoverImagePreview(URL.createObjectURL(file));
    }
  };

  const removeCoverImage = () => {
    setCoverImage(null);
    setCoverImagePreview(null);
  };

  const onSubmit = async (data: GroupFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      let coverImageUrl = null;

      // Upload cover image if provided
      if (coverImage) {
        const uploadedImages = await uploadMultipleToCloudinary([coverImage]);
        if (uploadedImages && uploadedImages.length > 0) {
          coverImageUrl = uploadedImages[0];
        }
      }

      // Create the group
      const response = await fetch("/api/groups", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          coverImage: coverImageUrl,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create group");
      }

      const result = await response.json();

      // Reset form and close modal
      reset();
      setCoverImage(null);
      setCoverImagePreview(null);
      onClose();

      // Navigate to the new group page
      router.push(`/groups/${result.id}`);
      router.refresh();
    } catch (error) {
      console.error("Error creating group:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onClose={() => !isLoading && onClose()} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <DialogTitle className="text-lg font-medium">Create Group</DialogTitle>
            <button
              onClick={() => !isLoading && onClose()}
              className="rounded-full p-1 hover:bg-gray-200"
              disabled={isLoading}
            >
              <XMarkIcon className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          {error && (
            <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-600">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Group Name*
              </label>
              <Input
                {...register("name")}
                placeholder="Enter group name"
                error={errors.name?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                {...register("description")}
                className="w-full rounded-md border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                rows={3}
                placeholder="What is this group about?"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cover Image
              </label>
              <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6">
                {coverImagePreview ? (
                  <div className="space-y-2">
                    <div className="relative h-32 w-full overflow-hidden rounded-md">
                      <img
                        src={coverImagePreview}
                        alt="Cover preview"
                        className="h-full w-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={removeCoverImage}
                        className="absolute top-2 right-2 rounded-full bg-gray-800 bg-opacity-75 p-1 text-white hover:bg-opacity-100"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="flex text-sm text-gray-600">
                      <label
                        htmlFor="cover-image-upload"
                        className="cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500"
                      >
                        Change cover image
                        <input
                          id="cover-image-upload"
                          name="cover-image-upload"
                          type="file"
                          className="sr-only"
                          accept="image/*"
                          onChange={handleCoverImageChange}
                        />
                      </label>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-1 text-center">
                    <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                      <PhotoIcon className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="flex text-sm text-gray-600">
                      <label
                        htmlFor="cover-image-upload"
                        className="cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500"
                      >
                        <span>Upload a cover image</span>
                        <input
                          id="cover-image-upload"
                          name="cover-image-upload"
                          type="file"
                          className="sr-only"
                          accept="image/*"
                          onChange={handleCoverImageChange}
                        />
                      </label>
                    </div>
                    <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                  </div>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <Input
                {...register("category")}
                placeholder="E.g., Technology, Sports, Art"
                error={errors.category?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Privacy*
              </label>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    id="public"
                    type="radio"
                    value="public"
                    {...register("visibility")}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="public" className="ml-2 block text-sm text-gray-700">
                    Public - Anyone can see the group, its members and their posts
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="private-visible"
                    type="radio"
                    value="private-visible"
                    {...register("visibility")}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="private-visible" className="ml-2 block text-sm text-gray-700">
                    Private (Visible) - Anyone can find the group, but only members can see posts
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="private-hidden"
                    type="radio"
                    value="private-hidden"
                    {...register("visibility")}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="private-hidden" className="ml-2 block text-sm text-gray-700">
                    Private (Hidden) - Only members can find the group and see posts
                  </label>
                </div>
              </div>
              {errors.visibility && (
                <p className="mt-1 text-sm text-red-500">{errors.visibility.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Who can post?*
              </label>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    id="all-members"
                    type="radio"
                    value="all-members"
                    {...register("postPermission")}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="all-members" className="ml-2 block text-sm text-gray-700">
                    All members
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="admin-only"
                    type="radio"
                    value="admin-only"
                    {...register("postPermission")}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="admin-only" className="ml-2 block text-sm text-gray-700">
                    Admins only
                  </label>
                </div>
              </div>
              {errors.postPermission && (
                <p className="mt-1 text-sm text-red-500">{errors.postPermission.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Group Rules
              </label>
              <textarea
                {...register("rules")}
                className="w-full rounded-md border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                rows={3}
                placeholder="Set rules for your group members to follow"
              />
              {errors.rules && (
                <p className="mt-1 text-sm text-red-500">{errors.rules.message}</p>
              )}
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => !isLoading && onClose()}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" isLoading={isLoading} disabled={isLoading}>
                Create Group
              </Button>
            </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
