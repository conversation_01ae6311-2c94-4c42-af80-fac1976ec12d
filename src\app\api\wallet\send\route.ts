import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { sendMoneySchema } from "@/lib/wallet/validation";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq, or } from "drizzle-orm";

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = sendMoneySchema.parse(body);

    // Check if sending is enabled
    const sendEnabled = await WalletService.getWalletSetting('send_enabled');
    if (sendEnabled === 'false') {
      return NextResponse.json(
        { 
          success: false,
          message: "Money transfers are currently disabled" 
        },
        { status: 400 }
      );
    }

    // Find recipient by username, email, or phone
    const recipient = await db.query.users.findFirst({
      where: or(
        eq(users.username, validatedData.recipient),
        eq(users.email, validatedData.recipient),
        eq(users.phone, validatedData.recipient)
      ),
    });

    if (!recipient) {
      return NextResponse.json(
        { 
          success: false,
          message: "Recipient not found" 
        },
        { status: 404 }
      );
    }

    if (recipient.id === session.user.id) {
      return NextResponse.json(
        { 
          success: false,
          message: "You cannot send money to yourself" 
        },
        { status: 400 }
      );
    }

    // Process money transfer
    const transactionId = await WalletService.sendMoney(session.user.id, {
      toUserId: recipient.id,
      amount: validatedData.amount,
      note: validatedData.note,
      pin: validatedData.pin,
    });

    return NextResponse.json({
      success: true,
      data: {
        transactionId,
        recipient: {
          id: recipient.id,
          name: recipient.name,
          username: recipient.username,
          image: recipient.image,
        },
        message: "Money sent successfully",
      },
    });
  } catch (error: any) {
    console.error("Error sending money:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid input data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to send money" 
      },
      { status: 500 }
    );
  }
}
