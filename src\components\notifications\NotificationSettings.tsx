"use client";

import { useState, useEffect } from 'react';
import { Switch } from '@headlessui/react';
import { 
  BellI<PERSON>, 
  SpeakerWaveIcon, 
  SpeakerXMarkIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { notificationSound } from '@/lib/utils/notificationSound';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';

interface NotificationSettingsProps {
  className?: string;
}

export function NotificationSettings({ className }: NotificationSettingsProps) {
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [browserNotifications, setBrowserNotifications] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>('default');

  useEffect(() => {
    // Load settings from localStorage
    setSoundEnabled(notificationSound.isEnabledState());
    setEmailNotifications(localStorage.getItem('emailNotifications') !== 'false');
    
    // Check browser notification permission
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission);
      setBrowserNotifications(Notification.permission === 'granted');
    }

    // Check if push notifications are supported
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      navigator.serviceWorker.ready.then(registration => {
        registration.pushManager.getSubscription().then(subscription => {
          setPushNotifications(!!subscription);
        });
      });
    }
  }, []);

  const handleSoundToggle = (enabled: boolean) => {
    setSoundEnabled(enabled);
    notificationSound.setEnabled(enabled);
    
    if (enabled) {
      // Play test sound
      notificationSound.playNotificationSound('default');
      toast.success('Notification sounds enabled');
    } else {
      toast.success('Notification sounds disabled');
    }
  };

  const handleBrowserNotificationToggle = async (enabled: boolean) => {
    if (enabled && notificationPermission !== 'granted') {
      try {
        const permission = await Notification.requestPermission();
        setNotificationPermission(permission);
        
        if (permission === 'granted') {
          setBrowserNotifications(true);
          toast.success('Browser notifications enabled');
          
          // Show test notification
          new Notification('Test Notification', {
            body: 'Browser notifications are now enabled!',
            icon: '/favicon.ico',
            badge: '/favicon.ico',
          });
        } else {
          setBrowserNotifications(false);
          toast.error('Browser notification permission denied');
        }
      } catch (error) {
        console.error('Error requesting notification permission:', error);
        toast.error('Failed to enable browser notifications');
      }
    } else {
      setBrowserNotifications(enabled);
      localStorage.setItem('browserNotifications', enabled.toString());
      toast.success(enabled ? 'Browser notifications enabled' : 'Browser notifications disabled');
    }
  };

  const handleEmailToggle = (enabled: boolean) => {
    setEmailNotifications(enabled);
    localStorage.setItem('emailNotifications', enabled.toString());
    toast.success(enabled ? 'Email notifications enabled' : 'Email notifications disabled');
  };

  const handlePushToggle = async (enabled: boolean) => {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      toast.error('Push notifications are not supported in this browser');
      return;
    }

    try {
      if (enabled) {
        const registration = await navigator.serviceWorker.ready;
        const subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
        });
        
        // Send subscription to server
        await fetch('/api/push/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(subscription),
        });
        
        setPushNotifications(true);
        toast.success('Push notifications enabled');
      } else {
        const registration = await navigator.serviceWorker.ready;
        const subscription = await registration.pushManager.getSubscription();
        
        if (subscription) {
          await subscription.unsubscribe();
          
          // Remove subscription from server
          await fetch('/api/push/unsubscribe', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ endpoint: subscription.endpoint }),
          });
        }
        
        setPushNotifications(false);
        toast.success('Push notifications disabled');
      }
    } catch (error) {
      console.error('Error managing push notifications:', error);
      toast.error('Failed to update push notification settings');
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-blue-100 rounded-lg">
            <BellIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Notification Preferences</h3>
            <p className="text-sm text-gray-500">Customize how you receive notifications</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Sound Notifications */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              {soundEnabled ? (
                <SpeakerWaveIcon className="h-5 w-5 text-green-600" />
              ) : (
                <SpeakerXMarkIcon className="h-5 w-5 text-gray-400" />
              )}
              <div>
                <h4 className="text-sm font-medium text-gray-900">Sound Notifications</h4>
                <p className="text-xs text-gray-500">Play sounds when you receive notifications</p>
              </div>
            </div>
            <Switch
              checked={soundEnabled}
              onChange={handleSoundToggle}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                soundEnabled ? "bg-blue-600" : "bg-gray-200"
              )}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  soundEnabled ? "translate-x-6" : "translate-x-1"
                )}
              />
            </Switch>
          </div>

          {/* Browser Notifications */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <ComputerDesktopIcon className={cn(
                "h-5 w-5",
                browserNotifications ? "text-green-600" : "text-gray-400"
              )} />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Browser Notifications</h4>
                <p className="text-xs text-gray-500">Show desktop notifications in your browser</p>
                {notificationPermission === 'denied' && (
                  <p className="text-xs text-red-500 mt-1">Permission denied. Enable in browser settings.</p>
                )}
              </div>
            </div>
            <Switch
              checked={browserNotifications}
              onChange={handleBrowserNotificationToggle}
              disabled={notificationPermission === 'denied'}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                browserNotifications ? "bg-blue-600" : "bg-gray-200",
                notificationPermission === 'denied' && "opacity-50 cursor-not-allowed"
              )}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  browserNotifications ? "translate-x-6" : "translate-x-1"
                )}
              />
            </Switch>
          </div>

          {/* Push Notifications */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <DevicePhoneMobileIcon className={cn(
                "h-5 w-5",
                pushNotifications ? "text-green-600" : "text-gray-400"
              )} />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Push Notifications</h4>
                <p className="text-xs text-gray-500">Receive notifications even when the app is closed</p>
              </div>
            </div>
            <Switch
              checked={pushNotifications}
              onChange={handlePushToggle}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                pushNotifications ? "bg-blue-600" : "bg-gray-200"
              )}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  pushNotifications ? "translate-x-6" : "translate-x-1"
                )}
              />
            </Switch>
          </div>

          {/* Email Notifications */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <CheckCircleIcon className={cn(
                "h-5 w-5",
                emailNotifications ? "text-green-600" : "text-gray-400"
              )} />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                <p className="text-xs text-gray-500">Receive important notifications via email</p>
              </div>
            </div>
            <Switch
              checked={emailNotifications}
              onChange={handleEmailToggle}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                emailNotifications ? "bg-blue-600" : "bg-gray-200"
              )}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  emailNotifications ? "translate-x-6" : "translate-x-1"
                )}
              />
            </Switch>
          </div>
        </div>

        {/* Test Notifications */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Test Notifications</h4>
          <div className="flex space-x-3">
            <button
              onClick={() => {
                notificationSound.playNotificationSound('like');
                toast.success('Test notification sound played!');
              }}
              className="px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              Test Sound
            </button>
            
            {browserNotifications && (
              <button
                onClick={() => {
                  new Notification('Test Notification', {
                    body: 'This is a test browser notification!',
                    icon: '/favicon.ico',
                  });
                }}
                className="px-3 py-2 text-xs font-medium text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
              >
                Test Browser
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
