import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { MonetizationService } from "@/lib/monetization/monetizationService";

// Get author's monetization stats
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const stats = await MonetizationService.getAuthorStats(session.user.id);

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error: any) {
    console.error("Error fetching author monetization stats:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to fetch monetization stats" 
      },
      { status: 500 }
    );
  }
}
