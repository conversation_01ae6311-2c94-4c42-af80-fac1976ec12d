"use client";

import { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Select } from "@/components/ui/Select";
import { toast } from "react-hot-toast";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface EditGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (group: any) => void;
  group: any;
}

export function EditGroupModal({ isOpen, onClose, onSuccess, group }: EditGroupModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    visibility: "public",
    category: "",
    rules: "",
    postPermission: "all-members",
  });

  useEffect(() => {
    if (group) {
      setFormData({
        name: group.name || "",
        description: group.description || "",
        visibility: group.visibility || "public",
        category: group.category || "",
        rules: group.rules || "",
        postPermission: group.postPermission || "all-members",
      });
    }
  }, [group]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/admin/groups/${group.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update group");
      }

      const updatedGroup = await response.json();
      onSuccess(updatedGroup);
    } catch (error) {
      console.error("Error updating group:", error);
      toast.error((error as Error).message || "Failed to update group");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="sm:flex sm:items-start">
        <div className="mt-3 w-full text-center sm:mt-0 sm:text-left">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium leading-6 text-gray-900">
              Edit Group
            </h3>
            <button
              type="button"
              className="rounded-md bg-white text-gray-400 hover:text-gray-500"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-4">
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Group Name <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="mt-1"
                  />
                </div>

                <div>
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Description
                  </label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={3}
                    className="mt-1"
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label
                      htmlFor="visibility"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Visibility
                    </label>
                    <Select
                      id="visibility"
                      name="visibility"
                      value={formData.visibility}
                      onChange={handleChange}
                      className="mt-1"
                    >
                      <option value="public">Public</option>
                      <option value="private-visible">Private (Visible in search)</option>
                      <option value="private-hidden">Private (Hidden)</option>
                    </Select>
                  </div>

                  <div>
                    <label
                      htmlFor="category"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Category
                    </label>
                    <Select
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      className="mt-1"
                    >
                      <option value="">Select a category</option>
                      <option value="social">Social</option>
                      <option value="business">Business</option>
                      <option value="education">Education</option>
                      <option value="entertainment">Entertainment</option>
                      <option value="gaming">Gaming</option>
                      <option value="technology">Technology</option>
                      <option value="health">Health</option>
                      <option value="other">Other</option>
                    </Select>
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="rules"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Group Rules
                  </label>
                  <Textarea
                    id="rules"
                    name="rules"
                    value={formData.rules}
                    onChange={handleChange}
                    rows={3}
                    className="mt-1"
                    placeholder="Enter group rules and guidelines..."
                  />
                </div>

                <div>
                  <label
                    htmlFor="postPermission"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Post Permission
                  </label>
                  <Select
                    id="postPermission"
                    name="postPermission"
                    value={formData.postPermission}
                    onChange={handleChange}
                    className="mt-1"
                  >
                    <option value="all-members">All Members</option>
                    <option value="admin-only">Admins Only</option>
                  </Select>
                </div>
              </div>

              <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full sm:ml-3 sm:w-auto"
                >
                  {isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  className="mt-3 w-full sm:mt-0 sm:w-auto"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Modal>
  );
}
