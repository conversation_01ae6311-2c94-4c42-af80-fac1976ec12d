import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { 
  userSubscriptions, 
  subscriptionTransactions, 
  subscriptionPlans,
  users 
} from "@/lib/db/schema";
import { eq, sql, desc, gte, lte, and } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session?.user?.id || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get subscription statistics
    const [
      totalSubscriptions,
      activeSubscriptions,
      recentTransactions,
      planDistribution,
      revenueData,
      churnData
    ] = await Promise.all([
      // Total subscriptions
      db.select({ count: sql<number>`count(*)` })
        .from(userSubscriptions),

      // Active subscriptions
      db.select({ count: sql<number>`count(*)` })
        .from(userSubscriptions)
        .where(eq(userSubscriptions.status, 'active')),

      // Recent transactions
      db.select({
        count: sql<number>`count(*)`,
        revenue: sql<number>`sum(${subscriptionTransactions.amount})`
      })
        .from(subscriptionTransactions)
        .where(
          and(
            eq(subscriptionTransactions.status, 'completed'),
            gte(subscriptionTransactions.createdAt, startDate)
          )
        ),

      // Plan distribution
      db.select({
        planId: userSubscriptions.planId,
        planName: subscriptionPlans.displayName,
        count: sql<number>`count(*)`
      })
        .from(userSubscriptions)
        .leftJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id))
        .where(eq(userSubscriptions.status, 'active'))
        .groupBy(userSubscriptions.planId, subscriptionPlans.displayName),

      // Revenue by plan
      db.select({
        planId: subscriptionTransactions.planId,
        planName: subscriptionPlans.displayName,
        revenue: sql<number>`sum(${subscriptionTransactions.amount})`,
        transactions: sql<number>`count(*)`
      })
        .from(subscriptionTransactions)
        .leftJoin(subscriptionPlans, eq(subscriptionTransactions.planId, subscriptionPlans.id))
        .where(
          and(
            eq(subscriptionTransactions.status, 'completed'),
            gte(subscriptionTransactions.createdAt, startDate)
          )
        )
        .groupBy(subscriptionTransactions.planId, subscriptionPlans.displayName),

      // Churn data (cancelled subscriptions)
      db.select({
        count: sql<number>`count(*)`,
        date: sql<string>`date(${userSubscriptions.cancelledAt})`
      })
        .from(userSubscriptions)
        .where(
          and(
            eq(userSubscriptions.status, 'cancelled'),
            gte(userSubscriptions.cancelledAt, startDate)
          )
        )
        .groupBy(sql`date(${userSubscriptions.cancelledAt})`)
        .orderBy(sql`date(${userSubscriptions.cancelledAt})`)
    ]);

    // Calculate metrics
    const totalSubs = totalSubscriptions[0]?.count || 0;
    const activeSubs = activeSubscriptions[0]?.count || 0;
    const recentRevenue = recentTransactions[0]?.revenue || 0;
    const recentTransactionCount = recentTransactions[0]?.count || 0;

    // Calculate conversion rate (mock data for now)
    const conversionRate = totalSubs > 0 ? (activeSubs / totalSubs) * 100 : 0;

    // Calculate average revenue per user
    const arpu = activeSubs > 0 ? revenueData.reduce((sum, plan) => sum + (plan.revenue || 0), 0) / activeSubs : 0;

    const analytics = {
      overview: {
        totalSubscriptions: totalSubs,
        activeSubscriptions: activeSubs,
        recentRevenue: parseFloat(recentRevenue.toFixed(2)),
        recentTransactions: recentTransactionCount,
        conversionRate: parseFloat(conversionRate.toFixed(2)),
        arpu: parseFloat(arpu.toFixed(2)),
      },
      planDistribution: planDistribution.map(plan => ({
        planId: plan.planId,
        planName: plan.planName || 'Unknown',
        subscribers: plan.count,
        percentage: totalSubs > 0 ? parseFloat(((plan.count / totalSubs) * 100).toFixed(2)) : 0,
      })),
      revenueByPlan: revenueData.map(plan => ({
        planId: plan.planId,
        planName: plan.planName || 'Unknown',
        revenue: parseFloat((plan.revenue || 0).toFixed(2)),
        transactions: plan.transactions,
      })),
      churnData: churnData.map(item => ({
        date: item.date,
        cancellations: item.count,
      })),
      period: parseInt(period),
    };

    return NextResponse.json({
      success: true,
      data: analytics,
    });

  } catch (error) {
    console.error("Error fetching subscription analytics:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
