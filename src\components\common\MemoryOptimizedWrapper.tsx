'use client';

import React, { 
  Suspense, 
  ErrorBoundary, 
  Component, 
  ReactNode, 
  useEffect, 
  useRef,
  useState,
  useMemo
} from 'react';
import { memoryMonitor } from '@/lib/utils/memory-optimization';

interface MemoryOptimizedWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
  enableMemoryMonitoring?: boolean;
  cleanupOnUnmount?: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class MemoryErrorBoundary extends Component<
  { children: ReactNode; fallback?: ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Memory Optimized Component Error:', error, errorInfo);
    
    // Log memory usage when error occurs
    if (process.env.NODE_ENV === 'development') {
      memoryMonitor.measure();
      console.log('Memory usage at error:', memoryMonitor.getLatestUsage().toFixed(1) + '%');
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-800 font-medium">Something went wrong</h3>
          <p className="text-red-600 text-sm mt-1">
            Please refresh the page or try again later.
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

const DefaultFallback = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span className="ml-2 text-gray-600">Loading...</span>
  </div>
);

export default function MemoryOptimizedWrapper({
  children,
  fallback = <DefaultFallback />,
  errorFallback,
  enableMemoryMonitoring = process.env.NODE_ENV === 'development',
  cleanupOnUnmount = true,
}: MemoryOptimizedWrapperProps) {
  const [isVisible, setIsVisible] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const cleanupFunctions = useRef<Array<() => void>>([]);

  // Intersection Observer for lazy rendering
  useEffect(() => {
    if (!wrapperRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(wrapperRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Memory monitoring
  useEffect(() => {
    if (!enableMemoryMonitoring) return;

    const interval = setInterval(() => {
      memoryMonitor.measure();
    }, 5000);

    cleanupFunctions.current.push(() => clearInterval(interval));

    return () => clearInterval(interval);
  }, [enableMemoryMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupOnUnmount) {
        cleanupFunctions.current.forEach(cleanup => cleanup());
        cleanupFunctions.current = [];
      }
    };
  }, [cleanupOnUnmount]);

  // Memoized content to prevent unnecessary re-renders
  const memoizedContent = useMemo(() => {
    if (!isVisible) {
      return fallback;
    }

    return (
      <MemoryErrorBoundary fallback={errorFallback}>
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      </MemoryErrorBoundary>
    );
  }, [isVisible, children, fallback, errorFallback]);

  return (
    <div ref={wrapperRef} className="memory-optimized-wrapper">
      {memoizedContent}
    </div>
  );
}

// Higher-order component for memory optimization
export function withMemoryOptimization<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: {
    fallback?: ReactNode;
    errorFallback?: ReactNode;
    enableMemoryMonitoring?: boolean;
  }
) {
  const MemoryOptimizedComponent = (props: P) => {
    return (
      <MemoryOptimizedWrapper
        fallback={options?.fallback}
        errorFallback={options?.errorFallback}
        enableMemoryMonitoring={options?.enableMemoryMonitoring}
      >
        <WrappedComponent {...props} />
      </MemoryOptimizedWrapper>
    );
  };

  MemoryOptimizedComponent.displayName = `withMemoryOptimization(${WrappedComponent.displayName || WrappedComponent.name})`;

  return MemoryOptimizedComponent;
}

// Hook for memory-efficient state management
export function useMemoryEfficientState<T>(
  initialValue: T,
  options?: {
    debounceMs?: number;
    maxHistorySize?: number;
  }
) {
  const [state, setState] = useState<T>(initialValue);
  const [history, setHistory] = useState<T[]>([initialValue]);
  const debounceMs = options?.debounceMs || 0;
  const maxHistorySize = options?.maxHistorySize || 10;

  const debouncedSetState = useMemo(() => {
    if (debounceMs === 0) {
      return (newValue: T | ((prev: T) => T)) => {
        setState(prev => {
          const nextValue = typeof newValue === 'function' ? (newValue as (prev: T) => T)(prev) : newValue;
          
          setHistory(prevHistory => {
            const newHistory = [...prevHistory, nextValue];
            return newHistory.length > maxHistorySize 
              ? newHistory.slice(-maxHistorySize)
              : newHistory;
          });
          
          return nextValue;
        });
      };
    }

    let timeoutId: NodeJS.Timeout;
    return (newValue: T | ((prev: T) => T)) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setState(prev => {
          const nextValue = typeof newValue === 'function' ? (newValue as (prev: T) => T)(prev) : newValue;
          
          setHistory(prevHistory => {
            const newHistory = [...prevHistory, nextValue];
            return newHistory.length > maxHistorySize 
              ? newHistory.slice(-maxHistorySize)
              : newHistory;
          });
          
          return nextValue;
        });
      }, debounceMs);
    };
  }, [debounceMs, maxHistorySize]);

  const undo = () => {
    if (history.length > 1) {
      const newHistory = history.slice(0, -1);
      const previousValue = newHistory[newHistory.length - 1];
      setHistory(newHistory);
      setState(previousValue);
    }
  };

  const clearHistory = () => {
    setHistory([state]);
  };

  return {
    state,
    setState: debouncedSetState,
    history,
    undo,
    clearHistory,
    canUndo: history.length > 1,
  };
}
