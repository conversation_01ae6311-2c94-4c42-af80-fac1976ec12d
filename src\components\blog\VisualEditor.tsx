"use client";

import React, { useRef, useEffect, useState, useCallback } from 'react';

interface VisualEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const VisualEditor: React.FC<VisualEditorProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  className = ""
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUserInputRef = useRef(false);

  // Convert markdown to HTML for display
  const markdownToHtml = (markdown: string): string => {
    let html = markdown;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mb-2 mt-4 outline-none" contenteditable="true" data-type="heading" data-level="3">$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mb-3 mt-5 outline-none" contenteditable="true" data-type="heading" data-level="2">$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mb-4 mt-6 outline-none" contenteditable="true" data-type="heading" data-level="1">$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold" contenteditable="true" data-type="bold">$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em class="italic" contenteditable="true" data-type="italic">$1</em>');

    // Code
    html = html.replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" contenteditable="true" data-type="code">$1</code>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" contenteditable="true" data-type="link" data-url="$2" rel="nofollow noopener noreferrer" target="_blank">$1</a>');

    // Images
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg my-4" data-type="image" data-alt="$1" />');

    // Lists
    html = html.replace(/^\- (.*$)/gim, '<li class="ml-4 outline-none" contenteditable="true" data-type="list-item">• $1</li>');
    html = html.replace(/(<li.*<\/li>)/s, '<ul class="mb-4" data-type="list">$1</ul>');

    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4 outline-none" contenteditable="true" data-type="blockquote">$1</blockquote>');

    // Line breaks and paragraphs
    html = html.replace(/\n\n/g, '</p><p class="mb-4 outline-none" contenteditable="true" data-type="paragraph">');
    html = html.replace(/\n/g, '<br />');

    // Wrap in paragraphs if not already wrapped
    if (html && !html.startsWith('<')) {
      html = '<p class="mb-4 outline-none" contenteditable="true" data-type="paragraph">' + html + '</p>';
    }

    return html;
  };

  // Convert HTML back to markdown
  const htmlToMarkdown = (html: string): string => {
    let markdown = html;

    // Headers
    markdown = markdown.replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1');
    markdown = markdown.replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1');
    markdown = markdown.replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1');

    // Bold
    markdown = markdown.replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**');

    // Italic
    markdown = markdown.replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*');

    // Code
    markdown = markdown.replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`');

    // Links
    markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)');

    // Images
    markdown = markdown.replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*\/?>/gi, '![$2]($1)');

    // Lists
    markdown = markdown.replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1');
    markdown = markdown.replace(/<ul[^>]*>(.*?)<\/ul>/gi, '$1');

    // Blockquotes
    markdown = markdown.replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1');

    // Paragraphs
    markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n');

    // Clean up extra line breaks
    markdown = markdown.replace(/\n\n+/g, '\n\n');
    markdown = markdown.replace(/<br\s*\/?>/gi, '\n');

    // Remove HTML tags
    markdown = markdown.replace(/<[^>]*>/g, '');

    // Clean up
    markdown = markdown.trim();

    return markdown;
  };

  // Advanced cursor position management
  const getCursorPosition = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !editorRef.current) return null;

    const range = selection.getRangeAt(0);
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(editorRef.current);
    preCaretRange.setEnd(range.startContainer, range.startOffset);

    return {
      offset: preCaretRange.toString().length,
      isCollapsed: range.collapsed,
      selectedText: range.toString(),
      startContainer: range.startContainer,
      startOffset: range.startOffset,
      endContainer: range.endContainer,
      endOffset: range.endOffset
    };
  };

  const setCursorPosition = (position: any) => {
    if (!position || !editorRef.current) return;

    try {
      const selection = window.getSelection();
      if (!selection) return;

      // Try offset-based positioning
      const walker = document.createTreeWalker(
        editorRef.current,
        NodeFilter.SHOW_TEXT,
        null
      );

      let currentOffset = 0;
      let targetNode = null;
      let targetOffset = 0;

      while (walker.nextNode()) {
        const node = walker.currentNode;
        const nodeLength = node.textContent?.length || 0;

        if (currentOffset + nodeLength >= position.offset) {
          targetNode = node;
          targetOffset = position.offset - currentOffset;
          break;
        }
        currentOffset += nodeLength;
      }

      if (targetNode) {
        const range = document.createRange();
        const safeOffset = Math.min(targetOffset, targetNode.textContent?.length || 0);
        range.setStart(targetNode, safeOffset);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } catch (error) {
      console.warn('Could not restore cursor position:', error);
    }
  };

  // Update editor content when value changes (only for external changes)
  useEffect(() => {
    if (editorRef.current && !isUserInputRef.current) {
      const html = markdownToHtml(value);

      // Only update if content actually changed
      if (editorRef.current.innerHTML !== html) {
        const currentPosition = getCursorPosition();
        editorRef.current.innerHTML = html;

        // Restore cursor position after DOM update only if we had a position
        if (currentPosition) {
          requestAnimationFrame(() => {
            setCursorPosition(currentPosition);
          });
        }
      }
    }
  }, [value]);

  // Simple input handler
  const handleInput = useCallback(() => {
    if (!editorRef.current) return;

    // Mark as user input
    isUserInputRef.current = true;

    // Clear existing timeout
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    // Debounce the update
    updateTimeoutRef.current = setTimeout(() => {
      if (editorRef.current) {
        const html = editorRef.current.innerHTML;
        const markdown = htmlToMarkdown(html);

        // Only trigger onChange if content actually changed
        if (markdown !== value) {
          onChange(markdown);
        }

        // Reset user input flag after a delay
        setTimeout(() => {
          isUserInputRef.current = false;
        }, 100);
      }
    }, 300); // Increased debounce for stability
  }, [value, onChange]);



  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Modern formatting functions (replacing deprecated execCommand)
  const applyFormatting = useCallback((tag: string, className?: string, attributes?: Record<string, string>) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = range.toString();

    if (selectedText) {
      const element = document.createElement(tag);
      if (className) element.className = className;
      if (attributes) {
        Object.entries(attributes).forEach(([key, value]) => {
          element.setAttribute(key, value);
        });
      }
      element.setAttribute('contenteditable', 'true');
      element.textContent = selectedText;

      range.deleteContents();
      range.insertNode(element);

      // Place cursor after the inserted element
      range.setStartAfter(element);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);

      handleInput();
    }
  }, [handleInput]);

  const createLink = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const selectedText = selection.toString();
    if (selectedText) {
      const url = prompt('Enter URL:');
      if (url) {
        const link = document.createElement('a');
        link.href = url;
        link.className = 'text-blue-600 hover:text-blue-800 underline';
        link.setAttribute('contenteditable', 'true');
        link.setAttribute('data-type', 'link');
        link.setAttribute('data-url', url);
        link.textContent = selectedText;

        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(link);

        // Place cursor after the link
        range.setStartAfter(link);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);

        handleInput();
      }
    }
  }, [handleInput]);



  // Simple Enter key handling - let browser handle most cases
  const handleEnterKey = useCallback((e: React.KeyboardEvent) => {
    if (e.key !== 'Enter') return;

    // Mark as user input to prevent cursor jumping
    isUserInputRef.current = true;

    // Let browser handle Enter key naturally for most cases
    // Only handle specific cases if needed

    // Trigger input handling after a short delay
    setTimeout(() => {
      handleInput();
    }, 10);
  }, [handleInput]);

  // Combined keydown handler
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Handle Enter key first
    handleEnterKey(e);

    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          applyFormatting('strong', 'font-semibold', { 'data-type': 'bold' });
          break;
        case 'i':
          e.preventDefault();
          applyFormatting('em', 'italic', { 'data-type': 'italic' });
          break;
        case 'u':
          e.preventDefault();
          applyFormatting('span', 'underline', { 'data-type': 'underline' });
          break;
        case 'k':
          e.preventDefault();
          createLink();
          break;
        case 'z':
          // Let browser handle undo/redo
          break;
        case 'y':
          // Let browser handle redo
          break;
      }
    }
  }, [applyFormatting, createLink, handleEnterKey]);

  // Enhanced paste handling
  const handlePaste = useCallback((e: React.ClipboardEvent) => {
    e.preventDefault();

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const text = e.clipboardData.getData('text/plain');
    const html = e.clipboardData.getData('text/html');

    const range = selection.getRangeAt(0);
    range.deleteContents();

    // If HTML is available and contains formatting, use it
    if (html && html.includes('<')) {
      // Advanced HTML sanitization
      const cleanHtml = html
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/<style[^>]*>.*?<\/style>/gi, '')
        .replace(/<link[^>]*>/gi, '')
        .replace(/<meta[^>]*>/gi, '')
        .replace(/on\w+="[^"]*"/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/data:/gi, '')
        .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
        .replace(/<object[^>]*>.*?<\/object>/gi, '')
        .replace(/<embed[^>]*>/gi, '');

      // Create a temporary container to parse and clean HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = cleanHtml;

      // Convert to our markdown-compatible format
      const processedContent = convertPastedContent(tempDiv);

      // Insert the processed content
      if (processedContent) {
        range.insertNode(processedContent);

        // Move cursor to end of inserted content
        range.setStartAfter(processedContent);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } else {
      // Insert as plain text with proper formatting
      const lines = text.split('\n');
      const fragment = document.createDocumentFragment();

      lines.forEach((line, index) => {
        if (line.trim()) {
          const textNode = document.createTextNode(line);
          fragment.appendChild(textNode);
        }

        if (index < lines.length - 1) {
          fragment.appendChild(document.createElement('br'));
        }
      });

      range.insertNode(fragment);

      // Move cursor to end
      range.setStartAfter(fragment);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    }

    handleInput();
  }, [handleInput]);

  // Convert pasted HTML content to our format
  const convertPastedContent = (container: HTMLElement): DocumentFragment => {
    const fragment = document.createDocumentFragment();

    Array.from(container.childNodes).forEach(node => {
      if (node.nodeType === Node.TEXT_NODE) {
        fragment.appendChild(node.cloneNode(true));
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement;

        switch (element.tagName.toLowerCase()) {
          case 'p':
            const p = document.createElement('p');
            p.className = 'mb-4 outline-none';
            p.setAttribute('contenteditable', 'true');
            p.setAttribute('data-type', 'paragraph');
            p.innerHTML = element.innerHTML;
            fragment.appendChild(p);
            break;

          case 'h1':
          case 'h2':
          case 'h3':
          case 'h4':
          case 'h5':
          case 'h6':
            const heading = document.createElement(element.tagName.toLowerCase());
            const level = element.tagName.charAt(1);
            heading.className = level === '1' ? 'text-2xl font-bold mb-4 mt-6 outline-none' :
                               level === '2' ? 'text-xl font-semibold mb-3 mt-5 outline-none' :
                               'text-lg font-semibold mb-2 mt-4 outline-none';
            heading.setAttribute('contenteditable', 'true');
            heading.setAttribute('data-type', 'heading');
            heading.setAttribute('data-level', level);
            heading.textContent = element.textContent || '';
            fragment.appendChild(heading);
            break;

          case 'strong':
          case 'b':
            const strong = document.createElement('strong');
            strong.className = 'font-semibold';
            strong.setAttribute('contenteditable', 'true');
            strong.setAttribute('data-type', 'bold');
            strong.textContent = element.textContent || '';
            fragment.appendChild(strong);
            break;

          case 'em':
          case 'i':
            const em = document.createElement('em');
            em.className = 'italic';
            em.setAttribute('contenteditable', 'true');
            em.setAttribute('data-type', 'italic');
            em.textContent = element.textContent || '';
            fragment.appendChild(em);
            break;

          case 'a':
            const link = document.createElement('a');
            link.href = element.getAttribute('href') || '';
            link.className = 'text-blue-600 hover:text-blue-800 underline';
            link.setAttribute('contenteditable', 'true');
            link.setAttribute('data-type', 'link');
            link.textContent = element.textContent || '';
            fragment.appendChild(link);
            break;

          case 'code':
            const code = document.createElement('code');
            code.className = 'bg-gray-100 px-1 py-0.5 rounded text-sm font-mono';
            code.setAttribute('contenteditable', 'true');
            code.setAttribute('data-type', 'code');
            code.textContent = element.textContent || '';
            fragment.appendChild(code);
            break;

          default:
            // For other elements, just extract text content
            if (element.textContent) {
              fragment.appendChild(document.createTextNode(element.textContent));
            }
            break;
        }
      }
    });

    return fragment;
  };

  // Enhanced click handling for interactive elements
  const handleClick = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;

    // Handle link clicks in edit mode
    if (target.tagName === 'A') {
      e.preventDefault();
      e.stopPropagation();

      const currentUrl = target.getAttribute('href') || '';
      const currentText = target.textContent || '';

      // Show a more user-friendly dialog
      const action = confirm(`Edit link "${currentText}"?\nCurrent URL: ${currentUrl}\n\nClick OK to edit, Cancel to remove link.`);

      if (action) {
        const newUrl = prompt('Enter new URL:', currentUrl);
        if (newUrl !== null && newUrl.trim() !== '') {
          target.setAttribute('href', newUrl.trim());
          target.setAttribute('data-url', newUrl.trim());
          handleInput();
        }
      } else {
        // Remove link but keep text
        const textNode = document.createTextNode(currentText);
        target.parentNode?.replaceChild(textNode, target);
        handleInput();
      }
    }

    // Handle image clicks
    if (target.tagName === 'IMG') {
      e.preventDefault();
      e.stopPropagation();

      const currentSrc = target.getAttribute('src') || '';
      const currentAlt = target.getAttribute('alt') || '';

      const action = confirm(`Edit image?\nCurrent: ${currentAlt || 'No alt text'}\n\nClick OK to edit, Cancel to remove image.`);

      if (action) {
        const newSrc = prompt('Enter image URL:', currentSrc);
        if (newSrc !== null && newSrc.trim() !== '') {
          target.setAttribute('src', newSrc.trim());

          const newAlt = prompt('Enter alt text (optional):', currentAlt);
          if (newAlt !== null) {
            target.setAttribute('alt', newAlt.trim());
            target.setAttribute('data-alt', newAlt.trim());
          }
          handleInput();
        }
      } else {
        // Remove image
        target.remove();
        handleInput();
      }
    }

    // Handle code block clicks for syntax highlighting
    if (target.tagName === 'CODE' && target.parentElement?.tagName === 'PRE') {
      e.preventDefault();
      const currentCode = target.textContent || '';
      const language = prompt('Enter programming language (optional):', '');

      if (language !== null) {
        target.setAttribute('data-language', language);
        target.className = `text-sm font-mono language-${language}`;
        handleInput();
      }
    }
  }, [handleInput]);

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        onClick={handleClick}
        className="w-full px-4 py-3 min-h-[400px] bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 prose prose-sm max-w-none"
        style={{ whiteSpace: 'pre-wrap' }}
        suppressContentEditableWarning={true}
        data-placeholder={placeholder}
        spellCheck={true}
      />
      
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
          font-style: italic;
        }

        [contenteditable] h1:focus,
        [contenteditable] h2:focus,
        [contenteditable] h3:focus,
        [contenteditable] p:focus,
        [contenteditable] blockquote:focus,
        [contenteditable] li:focus {
          outline: 2px solid #3b82f6;
          outline-offset: 2px;
          border-radius: 4px;
          background-color: rgba(59, 130, 246, 0.05);
        }

        [contenteditable] a {
          cursor: pointer;
          position: relative;
          text-decoration: underline;
          text-decoration-color: #3b82f6;
        }

        [contenteditable] a:hover {
          background-color: rgba(59, 130, 246, 0.1);
          border-radius: 2px;
        }

        [contenteditable] a:hover:after {
          content: "Click to edit link";
          position: absolute;
          bottom: -25px;
          left: 0;
          background: #1f2937;
          color: white;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 11px;
          white-space: nowrap;
          z-index: 10;
        }

        [contenteditable] img {
          cursor: pointer;
          border: 2px solid transparent;
          border-radius: 8px;
          transition: all 0.2s ease;
        }

        [contenteditable] img:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
          transform: scale(1.02);
        }

        [contenteditable] code {
          background-color: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        [contenteditable] blockquote {
          border-left: 4px solid #3b82f6;
          background-color: rgba(59, 130, 246, 0.05);
          border-radius: 0 4px 4px 0;
        }

        [contenteditable] ul {
          list-style: none;
        }

        [contenteditable] li {
          position: relative;
          padding-left: 20px;
        }

        [contenteditable] li:before {
          content: "•";
          color: #3b82f6;
          font-weight: bold;
          position: absolute;
          left: 0;
        }

        /* Selection styling */
        [contenteditable] ::selection {
          background-color: rgba(59, 130, 246, 0.2);
        }

        /* Focus ring for the entire editor */
        [contenteditable]:focus {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      `}</style>
    </div>
  );
};

export default VisualEditor;
