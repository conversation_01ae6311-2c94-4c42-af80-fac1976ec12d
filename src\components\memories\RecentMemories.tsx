"use client";

import { useState, useEffect } from "react";
import { MemoryCard } from "./MemoryCard";
import { Spinner } from "@/components/ui/Spinner";
import { ClockIcon } from "@heroicons/react/24/outline";

interface Memory {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  };
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
  sharedPost?: {
    id: string;
    content: string;
    images: string[] | null;
    videos: string[] | null;
    backgroundColor: string | null;
    feeling: string | null;
    activity: string | null;
    location: string | null;
    formattedContent: boolean | null;
    createdAt: string;
    user: {
      id: string;
      name: string;
      username?: string | null;
      image: string | null;
    };
  } | null;
}

export function RecentMemories() {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMemories = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/memories?type=recent");

        if (!response.ok) {
          throw new Error("Failed to fetch memories");
        }

        const data = await response.json();
        setMemories(data.memories);
      } catch (error) {
        console.error("Error fetching memories:", error);
        setError("Failed to load memories. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchMemories();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center py-10">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-4 text-red-800">
        <p>{error}</p>
      </div>
    );
  }

  if (memories.length === 0) {
    return (
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="p-6 text-center">
          <p className="text-sm text-gray-500">
            You don't have any recent memories to display.
          </p>
          <p className="mt-2 text-sm text-gray-500">
            As you create more posts and content, they'll appear here as memories in the future.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
      {memories.map((memory) => (
        <div key={memory.id} className="transform transition-all duration-300 hover:translate-y-[-2px] hover:shadow-lg">
          <div className="h-full">
            <MemoryCard memory={memory} type="recent" />
          </div>
        </div>
      ))}
    </div>
  );
}
