const { drizzle } = require('drizzle-orm/mysql2');
const mysql = require('mysql2/promise');
const { nanoid } = require('nanoid');

// Database schema imports
const { blogs, users } = require('../src/lib/db/schema');

async function seedBlogPosts() {
  try {
    // Create database connection
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'hifnf',
    });

    const db = drizzle(connection);

    // Get first user from database
    const firstUser = await db.select().from(users).limit(1);
    
    if (firstUser.length === 0) {
      console.log('No users found. Please create a user first.');
      return;
    }

    const authorId = firstUser[0].id;

    // Sample blog posts
    const sampleBlogs = [
      {
        id: nanoid(),
        title: "Getting Started with Next.js 15",
        slug: "getting-started-with-nextjs-15",
        excerpt: "Learn how to build modern web applications with the latest features in Next.js 15, including improved performance and developer experience.",
        content: `# Getting Started with Next.js 15

Next.js 15 brings exciting new features and improvements that make building web applications even more enjoyable. In this comprehensive guide, we'll explore the key features and how to get started.

## What's New in Next.js 15

- **Improved Performance**: Faster build times and optimized runtime performance
- **Enhanced Developer Experience**: Better error messages and debugging tools
- **New App Router Features**: More powerful routing capabilities
- **Server Components**: Better server-side rendering with React Server Components

## Installation

To get started with Next.js 15, run the following command:

\`\`\`bash
npx create-next-app@latest my-app
cd my-app
npm run dev
\`\`\`

## Key Features

### 1. App Router
The App Router provides a more intuitive way to structure your application with file-based routing.

### 2. Server Components
Server Components allow you to render components on the server, reducing the JavaScript bundle size sent to the client.

### 3. Streaming
Next.js 15 supports streaming, allowing you to send parts of your page to the client as they become ready.

## Conclusion

Next.js 15 is a significant step forward in web development, offering improved performance, better developer experience, and powerful new features. Start building your next project with Next.js 15 today!`,
        coverImage: "https://images.unsplash.com/photo-**********-4365d14bab8c?w=800&h=400&fit=crop",
        authorId: authorId,
        categoryId: null,
        tags: ["nextjs", "react", "web-development", "javascript"],
        status: "published",
        readTime: 8,
        viewCount: 0,
        featured: true,
        seoTitle: "Getting Started with Next.js 15 - Complete Guide",
        seoDescription: "Learn how to build modern web applications with Next.js 15. Complete guide with examples and best practices.",
        publishedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        title: "Building Responsive UIs with Tailwind CSS",
        slug: "building-responsive-uis-with-tailwind-css",
        excerpt: "Master the art of creating beautiful, responsive user interfaces using Tailwind CSS utility classes and modern design principles.",
        content: `# Building Responsive UIs with Tailwind CSS

Tailwind CSS has revolutionized how we approach styling in modern web development. This utility-first framework allows developers to build custom designs without leaving their HTML.

## Why Tailwind CSS?

- **Utility-First**: Build complex components from simple utility classes
- **Responsive Design**: Built-in responsive design utilities
- **Customizable**: Easily customize your design system
- **Performance**: Only ship the CSS you actually use

## Getting Started

Install Tailwind CSS in your project:

\`\`\`bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
\`\`\`

## Responsive Design

Tailwind makes responsive design simple with its mobile-first approach:

\`\`\`html
<div class="w-full md:w-1/2 lg:w-1/3">
  <!-- Content -->
</div>
\`\`\`

## Best Practices

1. **Use Semantic HTML**: Start with proper HTML structure
2. **Mobile First**: Design for mobile devices first
3. **Component Extraction**: Extract repeated patterns into components
4. **Custom Utilities**: Create custom utilities for your design system

## Conclusion

Tailwind CSS empowers developers to create beautiful, responsive designs quickly and efficiently. Its utility-first approach and excellent documentation make it an excellent choice for modern web development.`,
        coverImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop",
        authorId: authorId,
        categoryId: null,
        tags: ["tailwindcss", "css", "responsive-design", "ui-ux"],
        status: "published",
        readTime: 6,
        viewCount: 0,
        featured: false,
        seoTitle: "Building Responsive UIs with Tailwind CSS",
        seoDescription: "Learn how to create responsive user interfaces with Tailwind CSS utility classes and modern design principles.",
        publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      },
      {
        id: nanoid(),
        title: "The Future of Web Development in 2024",
        slug: "future-of-web-development-2024",
        excerpt: "Explore the emerging trends, technologies, and frameworks that will shape web development in 2024 and beyond.",
        content: `# The Future of Web Development in 2024

As we move through 2024, the web development landscape continues to evolve at a rapid pace. Let's explore the key trends and technologies that are shaping the future of web development.

## Emerging Technologies

### 1. WebAssembly (WASM)
WebAssembly is enabling near-native performance in web browsers, opening up new possibilities for complex applications.

### 2. Edge Computing
Moving computation closer to users for improved performance and reduced latency.

### 3. AI Integration
Artificial Intelligence is becoming more integrated into web development workflows and user experiences.

## Framework Evolution

- **React**: Continued focus on concurrent features and server components
- **Vue.js**: Enhanced composition API and improved TypeScript support
- **Svelte**: Growing adoption with SvelteKit gaining momentum
- **Next.js**: Leading the way in full-stack React development

## Development Trends

1. **Jamstack Architecture**: Static site generation with dynamic functionality
2. **Micro-frontends**: Breaking down large applications into smaller, manageable pieces
3. **Progressive Web Apps**: Bridging the gap between web and native applications
4. **Serverless Functions**: Event-driven, scalable backend solutions

## Tools and Workflow

- **Vite**: Fast build tool gaining widespread adoption
- **TypeScript**: Becoming the standard for large-scale applications
- **Docker**: Containerization for consistent development environments
- **GitHub Actions**: Automated CI/CD workflows

## Conclusion

The future of web development is exciting, with new technologies and approaches constantly emerging. Staying updated with these trends will help developers build better, faster, and more user-friendly applications.`,
        coverImage: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop",
        authorId: authorId,
        categoryId: null,
        tags: ["web-development", "trends", "technology", "future"],
        status: "published",
        readTime: 10,
        viewCount: 0,
        featured: true,
        seoTitle: "The Future of Web Development in 2024 - Trends and Technologies",
        seoDescription: "Explore emerging trends, technologies, and frameworks shaping web development in 2024 and beyond.",
        publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      }
    ];

    // Insert blog posts
    for (const blog of sampleBlogs) {
      await db.insert(blogs).values(blog);
      console.log(`Created blog: ${blog.title}`);
    }

    console.log('✅ Blog posts seeded successfully!');
    await connection.end();
  } catch (error) {
    console.error('❌ Error seeding blog posts:', error);
  }
}

// Run the seed function
seedBlogPosts();
