import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptionTransactions, subscriptionPlans, users } from "@/lib/db/schema";
import { eq, and, desc, like, gte, lte, count, isNotNull } from "drizzle-orm";
import { z } from "zod";

const querySchema = z.object({
  search: z.string().optional(),
  adminUserId: z.string().optional(),
  action: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  page: z.string().transform(val => parseInt(val) || 1).default('1'),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).default('20'),
});

// GET - Fetch audit logs for subscription changes
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    // Build where conditions for audit logs
    // We'll look for transactions that have admin metadata
    const whereConditions = [
      // Only get transactions that have metadata indicating admin actions
      isNotNull(subscriptionTransactions.metadata),
    ];

    if (query.dateFrom) {
      whereConditions.push(gte(subscriptionTransactions.createdAt, new Date(query.dateFrom)));
    }

    if (query.dateTo) {
      const dateTo = new Date(query.dateTo);
      dateTo.setHours(23, 59, 59, 999);
      whereConditions.push(lte(subscriptionTransactions.createdAt, dateTo));
    }

    // Get total count for pagination
    const totalCountQuery = db
      .select({ count: count() })
      .from(subscriptionTransactions)
      .innerJoin(users, eq(subscriptionTransactions.userId, users.id))
      .innerJoin(subscriptionPlans, eq(subscriptionTransactions.planId, subscriptionPlans.id))
      .where(and(...whereConditions));

    const [{ count: totalCount }] = await totalCountQuery;

    // Get audit logs with user and plan data
    const auditLogsQuery = db
      .select({
        id: subscriptionTransactions.id,
        subscriptionId: subscriptionTransactions.subscriptionId,
        userId: subscriptionTransactions.userId,
        planId: subscriptionTransactions.planId,
        type: subscriptionTransactions.type,
        amount: subscriptionTransactions.amount,
        currency: subscriptionTransactions.currency,
        status: subscriptionTransactions.status,
        paymentGateway: subscriptionTransactions.paymentGateway,
        gatewayTransactionId: subscriptionTransactions.gatewayTransactionId,
        description: subscriptionTransactions.description,
        metadata: subscriptionTransactions.metadata,
        processedAt: subscriptionTransactions.processedAt,
        createdAt: subscriptionTransactions.createdAt,
        user: {
          name: users.name,
          email: users.email,
        },
        plan: {
          displayName: subscriptionPlans.displayName,
        },
      })
      .from(subscriptionTransactions)
      .innerJoin(users, eq(subscriptionTransactions.userId, users.id))
      .innerJoin(subscriptionPlans, eq(subscriptionTransactions.planId, subscriptionPlans.id))
      .where(and(...whereConditions))
      .orderBy(desc(subscriptionTransactions.createdAt))
      .limit(query.limit)
      .offset((query.page - 1) * query.limit);

    const auditLogs = await auditLogsQuery;

    // Parse metadata and filter admin actions
    const adminAuditLogs = auditLogs
      .map(log => {
        let metadata = null;
        try {
          metadata = log.metadata ? JSON.parse(log.metadata as string) : null;
        } catch (e) {
          // Invalid JSON, skip
          return null;
        }

        // Only include logs that are admin actions
        if (!metadata?.adminAction) {
          return null;
        }

        return {
          ...log,
          parsedMetadata: metadata,
        };
      })
      .filter(log => log !== null);

    // Apply additional filters on parsed data
    let filteredLogs = adminAuditLogs;

    if (query.search) {
      filteredLogs = filteredLogs.filter(log => 
        log.user.name.toLowerCase().includes(query.search!.toLowerCase()) ||
        log.user.email.toLowerCase().includes(query.search!.toLowerCase()) ||
        log.description?.toLowerCase().includes(query.search!.toLowerCase())
      );
    }

    if (query.adminUserId) {
      filteredLogs = filteredLogs.filter(log => 
        log.parsedMetadata?.adminUserId === query.adminUserId
      );
    }

    if (query.action) {
      filteredLogs = filteredLogs.filter(log => 
        log.type === query.action ||
        log.description?.toLowerCase().includes(query.action.toLowerCase())
      );
    }

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / query.limit);
    const hasNextPage = query.page < totalPages;
    const hasPrevPage = query.page > 1;

    // Get admin user details for the logs
    const adminUserIds = [...new Set(filteredLogs.map(log => log.parsedMetadata?.adminUserId).filter(Boolean))];
    const adminUsers = adminUserIds.length > 0 ? await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
      })
      .from(users)
      .where(eq(users.id, adminUserIds[0])) // This would need to be improved for multiple admin users
      : [];

    // Enhance logs with admin user info
    const enhancedLogs = filteredLogs.map(log => ({
      ...log,
      adminUser: adminUsers.find(admin => admin.id === log.parsedMetadata?.adminUserId) || null,
    }));

    return NextResponse.json({
      success: true,
      auditLogs: enhancedLogs,
      pagination: {
        currentPage: query.page,
        totalPages,
        totalCount: filteredLogs.length,
        hasNextPage,
        hasPrevPage,
        limit: query.limit,
      },
    });
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid query parameters",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch audit logs",
      },
      { status: 500 }
    );
  }
}
