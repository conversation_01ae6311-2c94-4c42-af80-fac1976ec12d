"use client";

import Link from "next/link";
import Image from "next/image";
import { UsersIcon, LockClosedIcon, EyeIcon } from "@heroicons/react/24/outline";

interface Group {
  id: string;
  name: string;
  description: string | null;
  category: string | null;
  visibility: string;
  coverImage: string | null;
  createdAt: string;
}

interface GroupCardProps {
  group: Group;
}

export function GroupCard({ group }: GroupCardProps) {
  const getVisibilityIcon = () => {
    switch (group.visibility) {
      case "private-hidden":
        return <LockClosedIcon className="w-4 h-4 text-gray-500" />;
      case "private-visible":
        return <EyeIcon className="w-4 h-4 text-gray-500" />;
      default:
        return <UsersIcon className="w-4 h-4 text-green-500" />;
    }
  };

  const getVisibilityText = () => {
    switch (group.visibility) {
      case "private-hidden":
        return "Private";
      case "private-visible":
        return "Private";
      default:
        return "Public";
    }
  };

  return (
    <Link href={`/groups/${group.id}`}>
      <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 overflow-hidden">
        {/* Cover Image */}
        <div className="h-24 bg-gradient-to-r from-blue-500 to-purple-600 relative">
          {group.coverImage ? (
            <Image
              src={group.coverImage}
              alt={group.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600" />
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-sm font-semibold text-gray-900 line-clamp-1">
              {group.name}
            </h3>
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              {getVisibilityIcon()}
              <span>{getVisibilityText()}</span>
            </div>
          </div>

          {group.description && (
            <p className="text-xs text-gray-600 line-clamp-2 mb-2">
              {group.description}
            </p>
          )}

          {group.category && (
            <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              {group.category}
            </span>
          )}
        </div>
      </div>
    </Link>
  );
}
