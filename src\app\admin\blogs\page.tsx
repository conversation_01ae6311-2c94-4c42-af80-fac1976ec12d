"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Input } from "@/components/ui/Input";
import { Select } from "@/components/ui/Select";
import { toast } from "react-hot-toast";
import {
  PlusIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import Link from "next/link";
import Image from "next/image";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  coverImage?: string | null;
  status: "draft" | "published" | "archived";
  featured: boolean;
  readTime?: number | null;
  viewCount?: number;
  publishedAt?: string | null;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    username?: string | null;
    image?: string | null;
  };
  category?: {
    id: string;
    name: string;
    color: string;
  } | null;
  tags?: string[] | null;
  _count: {
    likes: number;
    comments: number;
  };
}

interface BlogCategory {
  id: string;
  name: string;
  color: string;
}

export default function AdminBlogsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedBlogs, setSelectedBlogs] = useState<string[]>([]);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Filters
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    categoryId: "",
    featured: "",
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  const fetchBlogs = async (page = 1) => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
        ...filters,
      });

      const response = await fetch(`/api/admin/blogs?${params}`);
      if (!response.ok) throw new Error("Failed to fetch blogs");

      const data = await response.json();
      setBlogs(data.blogs);
      setCurrentPage(data.pagination.page);
      setTotalPages(data.pagination.totalPages);
      setTotalCount(data.pagination.totalCount);
    } catch (error) {
      console.error("Error fetching blogs:", error);
      toast.error("Failed to fetch blogs");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/blogs/categories");
      if (!response.ok) throw new Error("Failed to fetch categories");
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  useEffect(() => {
    fetchBlogs();
    fetchCategories();
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchBlogs(1);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const handleBulkAction = async (action: string) => {
    if (selectedBlogs.length === 0) {
      toast.error("Please select blogs to perform bulk action");
      return;
    }

    try {
      // Implement bulk actions here
      switch (action) {
        case "publish":
          // Bulk publish logic
          break;
        case "draft":
          // Bulk draft logic
          break;
        case "delete":
          if (confirm(`Are you sure you want to delete ${selectedBlogs.length} blogs?`)) {
            // Bulk delete logic
          }
          break;
      }
    } catch (error) {
      console.error("Error performing bulk action:", error);
      toast.error("Failed to perform bulk action");
    }
  };

  const handleDeleteBlog = async (blogId: string) => {
    if (!confirm("Are you sure you want to delete this blog?")) return;

    try {
      const response = await fetch(`/api/admin/blogs/${blogId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete blog");

      toast.success("Blog deleted successfully");
      fetchBlogs(currentPage);
    } catch (error) {
      console.error("Error deleting blog:", error);
      toast.error("Failed to delete blog");
    }
  };

  const toggleFeatured = async (blogId: string, featured: boolean) => {
    try {
      const response = await fetch(`/api/admin/blogs/${blogId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ featured: !featured }),
      });

      if (!response.ok) throw new Error("Failed to update blog");

      toast.success(`Blog ${!featured ? "featured" : "unfeatured"} successfully`);
      fetchBlogs(currentPage);
    } catch (error) {
      console.error("Error updating blog:", error);
      toast.error("Failed to update blog");
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      published: "success",
      draft: "warning",
      archived: "default",
    };
    return (
      <Badge variant={variants[status as keyof typeof variants] as any}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Blog Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage all blogs, categories, and content
          </p>
        </div>
        <div className="mt-4 flex space-x-2 sm:mt-0">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <FunnelIcon className="mr-2 h-5 w-5" />
            Filters
          </Button>
          <Button onClick={() => router.push("/admin/blogs/categories")}>
            Categories
          </Button>
          <Button onClick={() => router.push("/admin/blogs/new")}>
            <PlusIcon className="mr-2 h-5 w-5" />
            Create Blog
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="mb-6 rounded-lg bg-gray-50 p-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search blogs..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange("status", e.target.value)}
              >
                <option value="">All Statuses</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <Select
                value={filters.categoryId}
                onChange={(e) => handleFilterChange("categoryId", e.target.value)}
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Featured
              </label>
              <Select
                value={filters.featured}
                onChange={(e) => handleFilterChange("featured", e.target.value)}
              >
                <option value="">All Blogs</option>
                <option value="true">Featured Only</option>
                <option value="false">Non-Featured</option>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedBlogs.length > 0 && (
        <div className="mb-4 flex items-center justify-between rounded-lg bg-blue-50 p-4">
          <span className="text-sm text-blue-700">
            {selectedBlogs.length} blog(s) selected
          </span>
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkAction("publish")}
            >
              Publish
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkAction("draft")}
            >
              Draft
            </Button>
            <Button
              size="sm"
              variant="danger"
              onClick={() => handleBulkAction("delete")}
            >
              Delete
            </Button>
          </div>
        </div>
      )}

      {/* Blogs Table */}
      {isLoading ? (
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      ) : (
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedBlogs.length === blogs.length && blogs.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedBlogs(blogs.map(blog => blog.id));
                        } else {
                          setSelectedBlogs([]);
                        }
                      }}
                      className="rounded border-gray-300"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Blog
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Author
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Stats
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {blogs.map((blog) => (
                  <tr key={blog.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedBlogs.includes(blog.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedBlogs([...selectedBlogs, blog.id]);
                          } else {
                            setSelectedBlogs(selectedBlogs.filter(id => id !== blog.id));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-start space-x-3">
                        {blog.coverImage && (
                          <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg">
                            <Image
                              src={blog.coverImage}
                              alt={blog.title}
                              width={48}
                              height={48}
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <Link
                              href={`/admin/blogs/${blog.id}`}
                              className="text-sm font-medium text-gray-900 hover:text-blue-600 truncate"
                            >
                              {blog.title}
                            </Link>
                            {blog.featured && (
                              <StarIconSolid className="h-4 w-4 text-yellow-400" />
                            )}
                          </div>
                          <div className="flex items-center space-x-2 mt-1">
                            {blog.category && (
                              <Badge
                                variant="default"
                                style={{ backgroundColor: blog.category.color + "20", color: blog.category.color }}
                              >
                                {blog.category.name}
                              </Badge>
                            )}
                            {blog.readTime && (
                              <span className="text-xs text-gray-500">
                                {blog.readTime} min read
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        {blog.author.image && (
                          <Image
                            src={blog.author.image}
                            alt={blog.author.name}
                            width={24}
                            height={24}
                            className="h-6 w-6 rounded-full"
                          />
                        )}
                        <span className="text-sm text-gray-900">{blog.author.name}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {getStatusBadge(blog.status)}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500">
                        <div>{blog._count.likes} likes</div>
                        <div>{blog._count.comments} comments</div>
                        <div>{blog.viewCount || 0} views</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div>Created: {formatDate(blog.createdAt)}</div>
                      {blog.publishedAt && (
                        <div>Published: {formatDate(blog.publishedAt)}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => toggleFeatured(blog.id, blog.featured)}
                          className="text-gray-400 hover:text-yellow-500"
                          title={blog.featured ? "Remove from featured" : "Add to featured"}
                        >
                          {blog.featured ? (
                            <StarIconSolid className="h-5 w-5" />
                          ) : (
                            <StarIcon className="h-5 w-5" />
                          )}
                        </button>
                        <Link
                          href={`/blogs/${blog.slug}`}
                          target="_blank"
                          className="text-gray-400 hover:text-blue-500"
                          title="View blog"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </Link>
                        <Link
                          href={`/admin/blogs/${blog.id}`}
                          className="text-gray-400 hover:text-blue-500"
                          title="Edit blog"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </Link>
                        <button
                          onClick={() => handleDeleteBlog(blog.id)}
                          className="text-gray-400 hover:text-red-500"
                          title="Delete blog"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
              <div className="flex flex-1 justify-between sm:hidden">
                <Button
                  variant="outline"
                  onClick={() => fetchBlogs(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => fetchBlogs(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{" "}
                    <span className="font-medium">
                      {(currentPage - 1) * 10 + 1}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium">
                      {Math.min(currentPage * 10, totalCount)}
                    </span>{" "}
                    of <span className="font-medium">{totalCount}</span> results
                  </p>
                </div>
                <div>
                  <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                    <Button
                      variant="outline"
                      onClick={() => fetchBlogs(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="rounded-r-none"
                    >
                      Previous
                    </Button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? "primary" : "outline"}
                          onClick={() => fetchBlogs(page)}
                          className="rounded-none"
                        >
                          {page}
                        </Button>
                      );
                    })}
                    <Button
                      variant="outline"
                      onClick={() => fetchBlogs(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="rounded-l-none"
                    >
                      Next
                    </Button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {blogs.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <h3 className="mt-2 text-sm font-medium text-gray-900">No blogs found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new blog.
          </p>
          <div className="mt-6">
            <Button onClick={() => router.push("/admin/blogs/new")}>
              <PlusIcon className="mr-2 h-5 w-5" />
              Create Blog
            </Button>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
