"use client";

import {
  EnvelopeIcon
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

interface MessageStats {
  totalMessages: number;
  unreadMessages: number;
  totalConversations: number;
  averageResponseTime: number; // in minutes
  messagesLast24h: number;
}

interface MessageStatisticsProps {
  stats: MessageStats;
  className?: string;
}

export function MessageStatistics({ stats, className }: MessageStatisticsProps) {

  const statItems = [
    {
      label: "Unread Messages",
      value: stats.unreadMessages.toLocaleString(),
      icon: EnvelopeIcon,
      color: "text-red-600",
      bgColor: "bg-red-50",
      highlight: stats.unreadMessages > 0,
    },
  ];

  return (
    <div className={cn("flex justify-center", className)}>
      <div className="w-full max-w-sm">
      {statItems.map((item) => {
        const Icon = item.icon;
        return (
          <div
            key={item.label}
            className={cn(
              "relative overflow-hidden rounded-lg bg-white p-4 shadow-sm border border-gray-200",
              item.highlight && "ring-2 ring-red-500 ring-opacity-50"
            )}
          >
            <div className="flex items-center">
              <div className={cn("rounded-md p-2", item.bgColor)}>
                <Icon className={cn("h-5 w-5", item.color)} />
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-500 truncate">
                  {item.label}
                </p>
                <p className={cn(
                  "text-lg font-semibold",
                  item.highlight ? "text-red-600" : "text-gray-900"
                )}>
                  {item.value}
                </p>
              </div>
            </div>

            {item.highlight && (
              <div className="absolute top-0 right-0">
                <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
              </div>
            )}
          </div>
        );
      })}
      </div>
    </div>
  );
}
