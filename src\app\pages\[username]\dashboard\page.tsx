import { MainLayout } from "@/components/layout/MainLayout";
import { getCurrentUser } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageRoles } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { notFound, redirect } from "next/navigation";
import { FanPageDashboard } from "@/components/fan-pages/dashboard/FanPageDashboard";

interface FanPageDashboardPageProps {
  params: Promise<{
    username: string;
  }>;
}

export const dynamic = 'force-dynamic';

export default async function FanPageDashboardPage({ params }: FanPageDashboardPageProps) {
  try {
    const { username } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect('/auth/signin');
    }

    // Find the fan page by username
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.username, username),
        eq(fanPages.isActive, true)
      ),
      with: {
        owner: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    if (!page) {
      notFound();
    }

    // Check if current user is the owner or has admin role
    const isOwner = page.ownerId === user.id;
    let hasAdminRole = false;

    if (!isOwner) {
      const userRole = await db.query.fanPageRoles.findFirst({
        where: and(
          eq(fanPageRoles.fanPageId, page.id),
          eq(fanPageRoles.userId, user.id),
          eq(fanPageRoles.role, 'admin')
        ),
      });
      hasAdminRole = !!userRole;
    }

    // If user is not owner and doesn't have admin role, redirect to page
    if (!isOwner && !hasAdminRole) {
      redirect(`/pages/${username}`);
    }

    return (
      <MainLayout>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
          <FanPageDashboard
            page={{
              ...page,
              isVerified: Boolean(page.isVerified),
              followerCount: page.followerCount || 0
            }}
            isOwner={isOwner}
            hasAdminRole={hasAdminRole}
            currentUserId={user.id}
          />
        </div>
      </MainLayout>
    );
  } catch (error) {
    console.error('Error loading fan page dashboard:', error);
    notFound();
  }
}

export const metadata = {
  title: "Fan Page Dashboard | HIFNF",
  description: "Manage your fan page with comprehensive analytics and tools.",
};
