"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";
import { toast } from "react-hot-toast";

export function FeatureTest() {
  const [testResults, setTestResults] = useState<{
    imageUpload: string;
    groupJoin: string;
  }>({
    imageUpload: "Not tested",
    groupJoin: "Not tested",
  });

  const [isTestingImageUpload, setIsTestingImageUpload] = useState(false);
  const [isTestingGroupJoin, setIsTestingGroupJoin] = useState(false);

  const testImageUpload = async () => {
    setIsTestingImageUpload(true);
    try {
      // Create a test file
      const canvas = document.createElement('canvas');
      canvas.width = 100;
      canvas.height = 100;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#3b82f6';
        ctx.fillRect(0, 0, 100, 100);
        ctx.fillStyle = '#ffffff';
        ctx.font = '16px Arial';
        ctx.fillText('Test', 30, 55);
      }

      // Convert canvas to blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob!);
        }, 'image/png');
      });

      const testFile = new File([blob], 'test-image.png', { type: 'image/png' });

      // Test upload
      const urls = await uploadMultipleToCloudinary([testFile]);
      
      if (urls && urls.length > 0) {
        setTestResults(prev => ({ ...prev, imageUpload: "✅ Success" }));
        toast.success("Image upload test passed!");
      } else {
        throw new Error("No URLs returned");
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, imageUpload: "❌ Failed: " + (error instanceof Error ? error.message : "Unknown error") }));
      toast.error("Image upload test failed!");
    } finally {
      setIsTestingImageUpload(false);
    }
  };

  const testGroupJoin = async () => {
    setIsTestingGroupJoin(true);
    try {
      // Test API endpoint availability
      const response = await fetch('/api/groups/test-group-id/members', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'test-user-id', // This is a test, so we use a dummy ID
          action: 'request',
        }),
      });

      // We expect this to fail with 404 or 401, but not with a network error
      if (response.status === 404 || response.status === 401) {
        setTestResults(prev => ({ ...prev, groupJoin: "✅ API Available" }));
        toast.success("Group join API test passed!");
      } else if (response.ok) {
        setTestResults(prev => ({ ...prev, groupJoin: "✅ Success" }));
        toast.success("Group join test passed!");
      } else {
        throw new Error(`Unexpected status: ${response.status}`);
      }
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setTestResults(prev => ({ ...prev, groupJoin: "❌ Network Error" }));
      } else {
        setTestResults(prev => ({ ...prev, groupJoin: "❌ Failed: " + (error instanceof Error ? error.message : "Unknown error") }));
      }
      toast.error("Group join test failed!");
    } finally {
      setIsTestingGroupJoin(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Feature Testing Dashboard</h2>
      
      <div className="space-y-6">
        {/* Image Upload Test */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Image Upload Test</h3>
          <p className="text-sm text-gray-600 mb-4">
            Tests the Cloudinary image upload functionality with a generated test image.
          </p>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status: {testResults.imageUpload}</span>
            <Button
              onClick={testImageUpload}
              disabled={isTestingImageUpload}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isTestingImageUpload ? "Testing..." : "Test Image Upload"}
            </Button>
          </div>
        </div>

        {/* Group Join Test */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Group Join Test</h3>
          <p className="text-sm text-gray-600 mb-4">
            Tests the group join API endpoint availability and functionality.
          </p>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status: {testResults.groupJoin}</span>
            <Button
              onClick={testGroupJoin}
              disabled={isTestingGroupJoin}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isTestingGroupJoin ? "Testing..." : "Test Group Join"}
            </Button>
          </div>
        </div>

        {/* Overall Status */}
        <div className="border-t border-gray-200 pt-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Overall Status</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Image Upload:</span>
              <span className={`ml-2 ${testResults.imageUpload.includes('✅') ? 'text-green-600' : testResults.imageUpload.includes('❌') ? 'text-red-600' : 'text-gray-600'}`}>
                {testResults.imageUpload}
              </span>
            </div>
            <div>
              <span className="font-medium">Group Join:</span>
              <span className={`ml-2 ${testResults.groupJoin.includes('✅') ? 'text-green-600' : testResults.groupJoin.includes('❌') ? 'text-red-600' : 'text-gray-600'}`}>
                {testResults.groupJoin}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
