import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";

export interface SessionValidationResult {
  isValid: boolean;
  user?: any;
  error?: string;
  statusCode?: number;
}

/**
 * Enhanced session validation that checks user status in real-time
 */
export async function validateSession(requireAdmin = false): Promise<SessionValidationResult> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return {
        isValid: false,
        error: "Unauthorized - No session found",
        statusCode: 401
      };
    }

    // Fetch current user data from database for real-time status check
    const currentUser = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
      with: {
        adminRole: true,
      },
    });

    if (!currentUser) {
      return {
        isValid: false,
        error: "User not found",
        statusCode: 404
      };
    }

    // Check if user account is active
    if (currentUser.status !== 'active' || !currentUser.isActive) {
      let errorMessage = `Account ${currentUser.status}`;

      if (currentUser.status === 'suspended' && currentUser.suspendedReason) {
        errorMessage += `: ${currentUser.suspendedReason}`;
      } else if (currentUser.status === 'deleted' && currentUser.deletedReason) {
        errorMessage += `: ${currentUser.deletedReason}`;
      } else if (currentUser.status === 'disabled') {
        errorMessage += '. Please contact support for assistance.';
      }

      return {
        isValid: false,
        error: errorMessage,
        statusCode: 403
      };
    }

    // Check admin requirement
    if (requireAdmin && !currentUser.isAdmin) {
      return {
        isValid: false,
        error: "Admin access required",
        statusCode: 403
      };
    }

    return {
      isValid: true,
      user: {
        ...currentUser,
        adminRoleName: currentUser.adminRole?.name || null
      }
    };
  } catch (error) {
    console.error("Session validation error:", error);
    return {
      isValid: false,
      error: "Internal server error during session validation",
      statusCode: 500
    };
  }
}

/**
 * Middleware helper for API routes
 */
export async function validateApiSession(requireAdmin = false) {
  const validation = await validateSession(requireAdmin);

  if (!validation.isValid) {
    return {
      response: NextResponse.json(
        {
          message: validation.error,
          code: validation.statusCode === 403 ? "ACCOUNT_DISABLED" : "UNAUTHORIZED"
        },
        { status: validation.statusCode }
      ),
      user: null
    };
  }

  return {
    response: null,
    user: validation.user
  };
}

/**
 * Check if user status has changed and invalidate session if needed
 */
export async function checkUserStatusChange(userId: string, currentStatus: string, currentIsActive: boolean) {
  try {
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return { statusChanged: true, newStatus: 'deleted' };
    }

    const statusChanged = user.status !== currentStatus || user.isActive !== currentIsActive;

    return {
      statusChanged,
      newStatus: user.status,
      newIsActive: user.isActive
    };
  } catch (error) {
    console.error("Error checking user status change:", error);
    return { statusChanged: false };
  }
}
