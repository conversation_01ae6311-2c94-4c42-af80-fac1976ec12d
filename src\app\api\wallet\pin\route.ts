import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { pinSetupSchema } from "@/lib/wallet/validation";
import { db } from "@/lib/db";
import { pinCodes } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Set or update PIN
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = pinSetupSchema.parse(body);

    // Set PIN
    await WalletService.setPin(session.user.id, validatedData.pin);

    return NextResponse.json({
      success: true,
      message: "PIN set successfully",
    });
  } catch (error: any) {
    console.error("Error setting PIN:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid input data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to set PIN" 
      },
      { status: 500 }
    );
  }
}

// Check if PIN exists
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const existingPin = await db.query.pinCodes.findFirst({
      where: eq(pinCodes.userId, session.user.id),
    });

    return NextResponse.json({
      success: true,
      data: {
        hasPin: !!existingPin,
        isLocked: existingPin?.lockedUntil ? new Date() < existingPin.lockedUntil : false,
        failedAttempts: existingPin?.failedAttempts || 0,
      },
    });
  } catch (error: any) {
    console.error("Error checking PIN status:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to check PIN status" 
      },
      { status: 500 }
    );
  }
}
