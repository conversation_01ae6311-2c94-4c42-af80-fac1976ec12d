import React from 'react';
import {
  StarIcon,
  SparklesIcon,
  ShieldCheckIcon,
  UserIcon,
  TrophyIcon
} from '@heroicons/react/24/solid';

interface PlanBadgeProps {
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor?: string;
  customBadgeUrl?: string | null;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function PlanBadge({ 
  badgeType, 
  badgeColor = '#3B82F6', 
  customBadgeUrl, 
  size = 'md',
  className = '' 
}: PlanBadgeProps) {
  if (badgeType === 'none') {
    return null;
  }

  const sizeClasses = {
    sm: 'w-5 h-5',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const iconSize = sizeClasses[size];

  // Custom badge with uploaded image
  if (badgeType === 'custom' && customBadgeUrl) {
    return (
      <img
        src={customBadgeUrl}
        alt="Custom Badge"
        className={`${iconSize} object-contain ${className}`}
        style={{ filter: `hue-rotate(${badgeColor})` }}
      />
    );
  }

  // Predefined badge icons
  const getBadgeIcon = () => {
    switch (badgeType) {
      case 'crown':
        return <TrophyIcon className={iconSize} style={{ color: badgeColor }} />;
      case 'star':
        return <StarIcon className={iconSize} style={{ color: badgeColor }} />;
      case 'diamond':
        return <SparklesIcon className={iconSize} style={{ color: badgeColor }} />;
      case 'vip':
        return <ShieldCheckIcon className={iconSize} style={{ color: badgeColor }} />;
      default:
        return <UserIcon className={iconSize} style={{ color: badgeColor }} />;
    }
  };

  return (
    <span className={`inline-flex items-center ${className}`} title={`${badgeType.toUpperCase()} Badge`}>
      {getBadgeIcon()}
    </span>
  );
}

// Badge type options for forms
export const BADGE_TYPE_OPTIONS = [
  { value: 'none', label: 'No Badge', icon: null },
  { value: 'crown', label: 'Crown', icon: TrophyIcon },
  { value: 'star', label: 'Star', icon: StarIcon },
  { value: 'diamond', label: 'Diamond', icon: SparklesIcon },
  { value: 'vip', label: 'VIP', icon: ShieldCheckIcon },
  { value: 'custom', label: 'Custom', icon: UserIcon },
] as const;

// Predefined color options
export const BADGE_COLOR_OPTIONS = [
  { value: '#3B82F6', label: 'Blue', color: '#3B82F6' },
  { value: '#EF4444', label: 'Red', color: '#EF4444' },
  { value: '#10B981', label: 'Green', color: '#10B981' },
  { value: '#F59E0B', label: 'Yellow', color: '#F59E0B' },
  { value: '#8B5CF6', label: 'Purple', color: '#8B5CF6' },
  { value: '#F97316', label: 'Orange', color: '#F97316' },
  { value: '#06B6D4', label: 'Cyan', color: '#06B6D4' },
  { value: '#84CC16', label: 'Lime', color: '#84CC16' },
  { value: '#EC4899', label: 'Pink', color: '#EC4899' },
  { value: '#6B7280', label: 'Gray', color: '#6B7280' },
] as const;
