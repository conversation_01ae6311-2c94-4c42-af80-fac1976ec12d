import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralSettings, monetizationSettings } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Get earning world settings
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get referral settings
    const referralSettingsData = await db.query.referralSettings.findFirst({
      where: eq(referralSettings.id, 'default'),
    });

    // Get monetization settings
    const monetizationSettingsData = await db.query.monetizationSettings.findFirst({
      where: eq(monetizationSettings.id, 'default'),
    });

    // Default settings structure
    const defaultSettings = {
      // General Settings
      isEnabled: true,
      maintenanceMode: false,
      
      // Referral Settings
      referralEnabled: referralSettingsData?.isEnabled ?? true,
      referralRewardAmount: referralSettingsData?.rewardAmount ?? "5.00",
      referralMinPayoutThreshold: referralSettingsData?.minPayoutThreshold ?? "10.00",
      referralMaxPerUser: referralSettingsData?.maxReferralsPerUser ?? 100,
      referralRequiresVerification: referralSettingsData?.requiresVerification ?? false,
      referralRewardBothUsers: referralSettingsData?.rewardBothUsers ?? false,
      referralReferredUserReward: referralSettingsData?.referredUserReward ?? "0.00",
      
      // Blog Monetization Settings
      blogMonetizationEnabled: monetizationSettingsData?.isEnabled ?? true,
      blogCprRate: monetizationSettingsData?.cprRate ?? "1.00",
      blogMinPayoutThreshold: monetizationSettingsData?.minPayoutThreshold ?? "10.00",
      blogMinReadDuration: 120, // 2 minutes in seconds
      blogRequiresApproval: true,
      
      // Payout Settings
      payoutEnabled: true,
      payoutMinAmount: "10.00",
      payoutMaxAmount: "1000.00",
      payoutProcessingFee: "0.00",
      payoutAutoApproval: false,
      payoutAutoApprovalLimit: "50.00",
      
      // Security Settings
      requireEmailVerification: true,
      requirePhoneVerification: false,
      enableTwoFactorAuth: false,
      maxDailyWithdrawals: 3,
      
      // System Limits
      maxEarningWalletBalance: "10000.00",
      dailyEarningLimit: "100.00",
      monthlyEarningLimit: "1000.00",
      
      // Notification Settings
      notifyAdminOnPayouts: true,
      notifyUsersOnEarnings: true,
      emailNotificationsEnabled: true,
    };

    return NextResponse.json({
      success: true,
      data: defaultSettings,
    });

  } catch (error) {
    console.error("Error fetching earning world settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch earning world settings"
      },
      { status: 500 }
    );
  }
}

// Update earning world settings
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    const requiredFields = [
      'isEnabled',
      'referralEnabled',
      'referralRewardAmount',
      'referralMinPayoutThreshold',
      'blogMonetizationEnabled',
      'blogCprRate',
      'blogMinPayoutThreshold',
      'payoutEnabled',
      'payoutMinAmount',
      'payoutMaxAmount'
    ];

    for (const field of requiredFields) {
      if (body[field] === undefined || body[field] === null) {
        return NextResponse.json(
          { message: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate numeric fields
    const numericFields = [
      'referralRewardAmount',
      'referralMinPayoutThreshold',
      'blogCprRate',
      'blogMinPayoutThreshold',
      'payoutMinAmount',
      'payoutMaxAmount',
      'payoutProcessingFee',
      'payoutAutoApprovalLimit',
      'maxEarningWalletBalance',
      'dailyEarningLimit',
      'monthlyEarningLimit'
    ];

    for (const field of numericFields) {
      if (body[field] && (isNaN(parseFloat(body[field])) || parseFloat(body[field]) < 0)) {
        return NextResponse.json(
          { message: `Invalid value for ${field}` },
          { status: 400 }
        );
      }
    }

    // Update referral settings
    const referralSettingsData = {
      isEnabled: body.referralEnabled,
      rewardAmount: parseFloat(body.referralRewardAmount).toFixed(2),
      minPayoutThreshold: parseFloat(body.referralMinPayoutThreshold).toFixed(2),
      requiresVerification: body.referralRequiresVerification || false,
      maxReferralsPerUser: parseInt(body.referralMaxPerUser) || 100,
      rewardBothUsers: body.referralRewardBothUsers || false,
      referredUserReward: body.referralRewardBothUsers 
        ? parseFloat(body.referralReferredUserReward || '0').toFixed(2) 
        : '0.00',
      updatedAt: new Date(),
    };

    const existingReferralSettings = await db.query.referralSettings.findFirst({
      where: eq(referralSettings.id, 'default'),
    });

    if (existingReferralSettings) {
      await db
        .update(referralSettings)
        .set(referralSettingsData)
        .where(eq(referralSettings.id, 'default'));
    } else {
      await db.insert(referralSettings).values({
        id: 'default',
        ...referralSettingsData,
        createdAt: new Date(),
      });
    }

    // Update monetization settings
    const monetizationSettingsData = {
      isEnabled: body.blogMonetizationEnabled,
      cprRate: parseFloat(body.blogCprRate).toFixed(2),
      minPayoutThreshold: parseFloat(body.blogMinPayoutThreshold).toFixed(2),
      minReadDuration: parseInt(body.blogMinReadDuration) || 120,
      updatedAt: new Date(),
    };

    const existingMonetizationSettings = await db.query.monetizationSettings.findFirst({
      where: eq(monetizationSettings.id, 'default'),
    });

    if (existingMonetizationSettings) {
      await db
        .update(monetizationSettings)
        .set(monetizationSettingsData)
        .where(eq(monetizationSettings.id, 'default'));
    } else {
      await db.insert(monetizationSettings).values({
        id: 'default',
        ...monetizationSettingsData,
        createdAt: new Date(),
      });
    }

    return NextResponse.json({
      success: true,
      message: "Earning world settings updated successfully",
    });

  } catch (error) {
    console.error("Error updating earning world settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update earning world settings"
      },
      { status: 500 }
    );
  }
}
