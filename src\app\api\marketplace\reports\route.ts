import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { products, productReports, stores, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and } from "drizzle-orm";

const reportSchema = z.object({
  productId: z.string().min(1),
  reason: z.enum(['counterfeit', 'inappropriate', 'fraud', 'prohibited', 'other']),
  description: z.string().max(1000).optional(),
});

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = reportSchema.parse(body);

    // Check if product exists
    const existingProduct = await db
      .select({
        id: products.id,
        storeId: products.storeId,
      })
      .from(products)
      .where(eq(products.id, validatedData.productId))
      .limit(1);

    if (existingProduct.length === 0) {
      return NextResponse.json(
        { message: "Product not found" },
        { status: 404 }
      );
    }

    // Check if user is reporting their own product
    const storeCheck = await db
      .select()
      .from(stores)
      .where(
        and(
          eq(stores.id, existingProduct[0].storeId),
          eq(stores.ownerId, session.user.id)
        )
      )
      .limit(1);

    if (storeCheck.length > 0) {
      return NextResponse.json(
        { message: "You cannot report your own product" },
        { status: 400 }
      );
    }

    // Check if user already reported this product
    const existingReport = await db
      .select()
      .from(productReports)
      .where(
        and(
          eq(productReports.productId, validatedData.productId),
          eq(productReports.userId, session.user.id)
        )
      )
      .limit(1);

    if (existingReport.length > 0) {
      return NextResponse.json(
        { message: "You have already reported this product" },
        { status: 400 }
      );
    }

    // Create report
    const reportId = uuidv4();
    await db.insert(productReports).values({
      id: reportId,
      productId: validatedData.productId,
      userId: session.user.id,
      reason: validatedData.reason,
      description: validatedData.description || null,
      status: 'pending',
    });

    // Get store owner for notification (in a real app, you'd notify admins)
    const storeOwner = await db
      .select({
        ownerId: stores.ownerId,
      })
      .from(stores)
      .where(eq(stores.id, existingProduct[0].storeId))
      .limit(1);

    if (storeOwner.length > 0) {
      // Create notification for store owner
      const notificationId = uuidv4();
      await db.insert(notifications).values({
        id: notificationId,
        recipientId: storeOwner[0].ownerId,
        type: "product_report" as const,
        senderId: session.user.id,
        productId: validatedData.productId,
        read: false,
      });
    }

    return NextResponse.json(
      { message: "Report submitted successfully" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error submitting report:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Error submitting report" },
      { status: 500 }
    );
  }
}
