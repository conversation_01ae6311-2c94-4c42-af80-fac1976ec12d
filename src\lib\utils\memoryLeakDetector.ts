/**
 * Memory Leak Detection Utility
 * Helps identify and prevent common memory leaks in React applications
 */

import React from 'react';

interface MemoryLeakReport {
  timestamp: number;
  type: 'interval' | 'listener' | 'observer' | 'cache' | 'component';
  description: string;
  severity: 'low' | 'medium' | 'high';
  location?: string;
}

class MemoryLeakDetector {
  private static instance: MemoryLeakDetector;
  private reports: MemoryLeakReport[] = [];
  private intervals: Set<NodeJS.Timeout> = new Set();
  private listeners: Map<EventTarget, Map<string, EventListener[]>> = new Map();
  private observers: Set<IntersectionObserver | MutationObserver | ResizeObserver> = new Set();
  private maxReports = 100;

  static getInstance(): MemoryLeakDetector {
    if (!MemoryLeakDetector.instance) {
      MemoryLeakDetector.instance = new MemoryLeakDetector();
    }
    return MemoryLeakDetector.instance;
  }

  // Track intervals
  trackInterval(intervalId: NodeJS.Timeout, description: string, location?: string): void {
    this.intervals.add(intervalId);
    this.addReport({
      type: 'interval',
      description: `Interval created: ${description}`,
      severity: 'medium',
      location
    });
  }

  clearInterval(intervalId: NodeJS.Timeout): void {
    if (this.intervals.has(intervalId)) {
      clearInterval(intervalId);
      this.intervals.delete(intervalId);
    }
  }

  // Track event listeners
  trackEventListener(
    target: EventTarget, 
    event: string, 
    listener: EventListener, 
    location?: string
  ): void {
    if (!this.listeners.has(target)) {
      this.listeners.set(target, new Map());
    }
    
    const targetListeners = this.listeners.get(target)!;
    if (!targetListeners.has(event)) {
      targetListeners.set(event, []);
    }
    
    targetListeners.get(event)!.push(listener);
    
    this.addReport({
      type: 'listener',
      description: `Event listener added: ${event}`,
      severity: 'low',
      location
    });
  }

  removeEventListener(target: EventTarget, event: string, listener: EventListener): void {
    const targetListeners = this.listeners.get(target);
    if (targetListeners) {
      const eventListeners = targetListeners.get(event);
      if (eventListeners) {
        const index = eventListeners.indexOf(listener);
        if (index > -1) {
          eventListeners.splice(index, 1);
          target.removeEventListener(event, listener);
        }
      }
    }
  }

  // Track observers
  trackObserver(
    observer: IntersectionObserver | MutationObserver | ResizeObserver, 
    description: string,
    location?: string
  ): void {
    this.observers.add(observer);
    this.addReport({
      type: 'observer',
      description: `Observer created: ${description}`,
      severity: 'medium',
      location
    });
  }

  disconnectObserver(observer: IntersectionObserver | MutationObserver | ResizeObserver): void {
    if (this.observers.has(observer)) {
      observer.disconnect();
      this.observers.delete(observer);
    }
  }

  // Track cache growth
  trackCacheGrowth(cacheName: string, size: number, maxSize: number, location?: string): void {
    if (size > maxSize * 0.8) {
      this.addReport({
        type: 'cache',
        description: `Cache ${cacheName} is ${((size / maxSize) * 100).toFixed(1)}% full (${size}/${maxSize})`,
        severity: size > maxSize * 0.95 ? 'high' : 'medium',
        location
      });
    }
  }

  // Add report
  private addReport(report: Omit<MemoryLeakReport, 'timestamp'>): void {
    this.reports.push({
      ...report,
      timestamp: Date.now()
    });

    // Keep only recent reports
    if (this.reports.length > this.maxReports) {
      this.reports = this.reports.slice(-this.maxReports);
    }

    // Log high severity issues immediately
    if (report.severity === 'high') {
      console.warn('🚨 Memory Leak Warning:', report);
    }
  }

  // Get reports
  getReports(severity?: 'low' | 'medium' | 'high'): MemoryLeakReport[] {
    if (severity) {
      return this.reports.filter(report => report.severity === severity);
    }
    return [...this.reports];
  }

  // Get summary
  getSummary(): {
    totalReports: number;
    activeIntervals: number;
    activeListeners: number;
    activeObservers: number;
    highSeverityCount: number;
  } {
    return {
      totalReports: this.reports.length,
      activeIntervals: this.intervals.size,
      activeListeners: Array.from(this.listeners.values()).reduce(
        (total, targetListeners) => total + Array.from(targetListeners.values()).reduce(
          (sum, listeners) => sum + listeners.length, 0
        ), 0
      ),
      activeObservers: this.observers.size,
      highSeverityCount: this.reports.filter(r => r.severity === 'high').length
    };
  }

  // Cleanup all tracked resources
  cleanup(): void {
    // Clear all intervals
    this.intervals.forEach(intervalId => clearInterval(intervalId));
    this.intervals.clear();

    // Remove all event listeners
    this.listeners.forEach((targetListeners, target) => {
      targetListeners.forEach((listeners, event) => {
        listeners.forEach(listener => {
          target.removeEventListener(event, listener);
        });
      });
    });
    this.listeners.clear();

    // Disconnect all observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();

    this.addReport({
      type: 'component',
      description: 'Memory leak detector cleanup completed',
      severity: 'low'
    });
  }

  // Clear reports
  clearReports(): void {
    this.reports = [];
  }
}

// Export singleton instance
export const memoryLeakDetector = MemoryLeakDetector.getInstance();

// Enhanced setInterval wrapper
export function safeSetInterval(
  callback: () => void, 
  delay: number, 
  description: string = 'Unknown',
  location?: string
): NodeJS.Timeout {
  const intervalId = setInterval(callback, delay);
  memoryLeakDetector.trackInterval(intervalId, description, location);
  return intervalId;
}

// Enhanced clearInterval wrapper
export function safeClearInterval(intervalId: NodeJS.Timeout): void {
  memoryLeakDetector.clearInterval(intervalId);
}

// Enhanced addEventListener wrapper
export function safeAddEventListener(
  target: EventTarget,
  event: string,
  listener: EventListener,
  options?: boolean | AddEventListenerOptions,
  location?: string
): void {
  target.addEventListener(event, listener, options);
  memoryLeakDetector.trackEventListener(target, event, listener, location);
}

// Enhanced removeEventListener wrapper
export function safeRemoveEventListener(
  target: EventTarget,
  event: string,
  listener: EventListener
): void {
  memoryLeakDetector.removeEventListener(target, event, listener);
}

// React hook for automatic cleanup
export function useMemoryLeakDetection(componentName: string) {
  const detectorRef = React.useRef(memoryLeakDetector);

  React.useEffect(() => {
    return () => {
      // Component-specific cleanup can be added here
      console.log(`🧹 Cleaning up memory for component: ${componentName}`);
    };
  }, [componentName]);

  return {
    trackInterval: (intervalId: NodeJS.Timeout, description: string) => 
      detectorRef.current.trackInterval(intervalId, description, componentName),
    trackObserver: (observer: any, description: string) => 
      detectorRef.current.trackObserver(observer, description, componentName),
    trackCacheGrowth: (cacheName: string, size: number, maxSize: number) =>
      detectorRef.current.trackCacheGrowth(cacheName, size, maxSize, componentName)
  };
}

// Auto-cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    memoryLeakDetector.cleanup();
  });
}
