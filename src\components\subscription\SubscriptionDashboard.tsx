"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
  CreditCardIcon,
  CheckIcon,
  SparklesIcon,
  CalendarIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { PlanBadge } from "@/components/ui/PlanBadge";
import { toast } from "react-hot-toast";
import { format } from "date-fns";

// Utility function to get billing cycle display text
const getBillingCycleDisplay = (plan: any): string => {
  // If custom billing is set, use that
  if (plan.customBillingYears > 0) {
    return plan.customBillingYears === 1 ? 'year' : `${plan.customBillingYears} years`;
  }
  if (plan.customBillingMonths > 0) {
    return plan.customBillingMonths === 1 ? 'month' : `${plan.customBillingMonths} months`;
  }
  // Otherwise use default billing cycle
  return plan.billingCycle === 'yearly' ? 'year' : 'month';
};

interface SubscriptionStatus {
  hasSubscription: boolean;
  isActive: boolean;
  plan: {
    id: string;
    name: string;
    displayName: string;
    description: string;
    price: string;
    currency: string;
    billingCycle: string;
    // Custom billing cycle fields
    customBillingMonths: number;
    customBillingYears: number;
    features: string[];
    badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
    badgeColor: string;
    customBadgeUrl: string | null;
    badgePriority: number;
  } | null;
  subscription: {
    id: string;
    status: string;
    startDate: string;
    endDate: string;
    nextBillingDate: string | null;
    autoRenew: boolean;
  } | null;
  daysRemaining: number | null;
  nextBillingDate: string | null;
}

interface SubscriptionDashboardProps {
  onUpgrade?: () => void;
  onCancel?: () => void;
}

export function SubscriptionDashboard({ onUpgrade, onCancel }: SubscriptionDashboardProps) {
  const { data: session } = useSession();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const fetchSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/user-subscription');
      const result = await response.json();

      if (result.success) {
        setSubscriptionStatus(result.data);
      } else {
        toast.error('Failed to fetch subscription status');
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error);
      toast.error('Failed to fetch subscription status');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscriptionStatus?.subscription) return;

    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    try {
      const response = await fetch('/api/user-subscription', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscriptionStatus.subscription.id,
          reason: 'User requested cancellation',
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Subscription cancelled successfully');
        fetchSubscriptionStatus();
        onCancel?.();
      } else {
        toast.error(result.message || 'Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Failed to cancel subscription');
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        {/* Header Skeleton */}
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>

        {/* Main Card Skeleton */}
        <div className="animate-pulse bg-gray-50 rounded-xl p-6 space-y-4">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded w-32"></div>
              <div className="h-4 bg-gray-200 rounded w-48"></div>
              <div className="h-8 bg-gray-200 rounded w-24"></div>
            </div>
            <div className="h-6 bg-gray-200 rounded w-16"></div>
          </div>

          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-2">
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded flex-1"></div>
              </div>
            ))}
          </div>

          <div className="flex space-x-3 pt-4">
            <div className="h-10 bg-gray-200 rounded w-32"></div>
            <div className="h-10 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!subscriptionStatus) {
    return (
      <div className="text-center py-8">
        <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">Unable to load subscription information</p>
      </div>
    );
  }

  const { plan, subscription, isActive, daysRemaining, nextBillingDate } = subscriptionStatus;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Subscription Dashboard</h2>
        <p className="text-gray-600 mt-1">
          Manage your subscription plan and billing information
        </p>
      </div>

      {/* Current Plan Status */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100 overflow-hidden">
        <div className="p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <SparklesIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h3 className="text-xl font-bold text-gray-900">
                      {plan?.displayName || 'Free Plan'}
                    </h3>
                    {plan && plan.badgeType !== 'none' && (
                      <PlanBadge
                        badgeType={plan.badgeType}
                        badgeColor={plan.badgeColor}
                        customBadgeUrl={plan.customBadgeUrl}
                        size="md"
                      />
                    )}
                  </div>
                  <p className="text-sm text-gray-600">
                    {plan?.description || 'Basic features included'}
                  </p>
                </div>
              </div>

              {plan && parseFloat(plan.price) > 0 ? (
                <div className="flex items-baseline space-x-2">
                  <span className="text-3xl font-bold text-gray-900">
                    ${plan.price}
                  </span>
                  <span className="text-lg text-gray-500">
                    /{getBillingCycleDisplay(plan)}
                  </span>
                </div>
              ) : (
                <div className="flex items-baseline space-x-2">
                  <span className="text-3xl font-bold text-green-600">Free</span>
                  <span className="text-lg text-gray-500">forever</span>
                </div>
              )}
            </div>

            <div className="text-right">
              <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${
                isActive
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-red-100 text-red-800 border border-red-200'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  isActive ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                {isActive ? 'Active' : 'Inactive'}
              </div>

              {subscription && isActive && daysRemaining !== null && (
                <div className="mt-3 text-right">
                  <p className="text-sm font-medium text-gray-700">
                    {daysRemaining > 0
                      ? `${daysRemaining} days remaining`
                      : 'Expires today'
                    }
                  </p>
                  <p className="text-xs text-gray-500">
                    Until next billing
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Plan Features */}
          {plan?.features && plan.features.length > 0 && (
            <div className="bg-white rounded-xl p-4 border border-gray-100">
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                Plan Features
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <CheckIcon className="h-3 w-3 text-green-600" />
                    </div>
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Subscription Details */}
      {subscription && (
        <div className="bg-white rounded-2xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CalendarIcon className="h-5 w-5 text-blue-600 mr-2" />
            Subscription Details
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center text-sm font-medium text-gray-600 mb-2">
                <CalendarIcon className="h-4 w-4 mr-2 text-green-500" />
                Start Date
              </div>
              <p className="text-lg font-semibold text-gray-900">
                {format(new Date(subscription.startDate), 'MMM dd, yyyy')}
              </p>
            </div>

            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center text-sm font-medium text-gray-600 mb-2">
                <ClockIcon className="h-4 w-4 mr-2 text-orange-500" />
                End Date
              </div>
              <p className="text-lg font-semibold text-gray-900">
                {format(new Date(subscription.endDate), 'MMM dd, yyyy')}
              </p>
            </div>

            {nextBillingDate && subscription.autoRenew && (
              <div className="bg-blue-50 rounded-xl p-4">
                <div className="flex items-center text-sm font-medium text-blue-600 mb-2">
                  <CreditCardIcon className="h-4 w-4 mr-2" />
                  Next Billing
                </div>
                <p className="text-lg font-semibold text-blue-900">
                  {format(new Date(nextBillingDate), 'MMM dd, yyyy')}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="bg-white rounded-2xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

        <div className="flex flex-col sm:flex-row gap-4">
          {plan?.name !== 'pro' && (
            <Button
              onClick={onUpgrade}
              className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <SparklesIcon className="h-5 w-5 mr-2" />
              Upgrade to Premium
            </Button>
          )}

          {subscription && isActive && subscription.status !== 'cancelled' && (
            <Button
              onClick={handleCancelSubscription}
              variant="outline"
              className="flex-1 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 py-3 px-6 rounded-xl transition-all duration-200"
            >
              <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
              Cancel Subscription
            </Button>
          )}
        </div>

        {subscription && subscription.status === 'cancelled' && (
          <div className="mt-4 bg-amber-50 border border-amber-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <ExclamationTriangleIcon className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-amber-800">Subscription Cancelled</h4>
                <p className="text-sm text-amber-700 mt-1">
                  Your subscription will end on {format(new Date(subscription.endDate), 'MMM dd, yyyy')}.
                  You'll continue to have access until then.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
