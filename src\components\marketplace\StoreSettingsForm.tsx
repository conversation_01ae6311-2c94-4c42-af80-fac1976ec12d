"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/Button";
import { SettingsLayout } from "@/components/settings/SettingsLayout";
import { ExclamationTriangleIcon, CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/outline";

interface StoreSettingsFormProps {
  storeId: string;
  initialSettings?: {
    visibility?: "public" | "private";
    showOutOfStock?: boolean | null;
    showProductViews?: boolean | null;
    emailNotifications?: boolean | null;
    productViewNotifications?: boolean | null;
  };
  productCount: number;
}

export function StoreSettingsForm({ storeId, initialSettings, productCount }: StoreSettingsFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [visibility, setVisibility] = useState<"public" | "private">(
    initialSettings?.visibility || "public"
  );
  const [showOutOfStock, setShowOutOfStock] = useState(
    initialSettings?.showOutOfStock !== undefined && initialSettings.showOutOfStock !== null ? initialSettings.showOutOfStock : true
  );
  const [showProductViews, setShowProductViews] = useState(
    initialSettings?.showProductViews !== undefined && initialSettings.showProductViews !== null ? initialSettings.showProductViews : true
  );
  const [emailNotifications, setEmailNotifications] = useState(
    initialSettings?.emailNotifications !== undefined && initialSettings.emailNotifications !== null ? initialSettings.emailNotifications : true
  );
  const [productViewNotifications, setProductViewNotifications] = useState(
    initialSettings?.productViewNotifications !== undefined && initialSettings.productViewNotifications !== null ? initialSettings.productViewNotifications : false
  );

  // Fetch settings if not provided
  useEffect(() => {
    if (!initialSettings) {
      fetchSettings();
    }
  }, [initialSettings, storeId]);

  const fetchSettings = async () => {
    try {
      const response = await fetch(`/api/marketplace/stores/${storeId}/settings`);
      if (response.ok) {
        const data = await response.json();
        setVisibility(data.visibility || "public");
        setShowOutOfStock(data.showOutOfStock !== undefined ? data.showOutOfStock : true);
        setShowProductViews(data.showProductViews !== undefined ? data.showProductViews : true);
        setEmailNotifications(data.emailNotifications !== undefined ? data.emailNotifications : true);
        setProductViewNotifications(data.productViewNotifications !== undefined ? data.productViewNotifications : false);
      }
    } catch (err) {
      console.error("Error fetching store settings:", err);
    }
  };

  const saveVisibilitySettings = async () => {
    await saveSettings({ visibility });
  };

  const saveDisplaySettings = async () => {
    await saveSettings({ showOutOfStock, showProductViews });
  };

  const saveNotificationSettings = async () => {
    await saveSettings({ emailNotifications, productViewNotifications });
  };

  const saveSettings = async (settingsToUpdate: any) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/marketplace/stores/${storeId}/settings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settingsToUpdate),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.message || "Failed to save settings");
        return;
      }

      setSuccess("Settings saved successfully");

      // Refresh the page data
      router.refresh();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error("Error saving settings:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteStore = async () => {
    if (!window.confirm("Are you sure you want to delete your store? This action cannot be undone.")) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/marketplace/stores/${storeId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const data = await response.json();
        setError(data.message || "Failed to delete store");
        setIsLoading(false);
        return;
      }

      // Redirect to marketplace
      router.push("/marketplace");
      router.refresh();
    } catch (err) {
      console.error("Error deleting store:", err);
      setError("An unexpected error occurred. Please try again.");
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-5 w-5 text-green-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Success</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>{success}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Store Visibility */}
      <SettingsLayout
        title="Store Visibility"
        description="Control who can see your store and products"
      >
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="public-store"
                name="visibility"
                type="radio"
                className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={visibility === "public"}
                onChange={() => setVisibility("public")}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="public-store" className="font-medium text-gray-700">
                Public Store
              </label>
              <p className="text-gray-500">
                Your store is visible to everyone and can be found in search results.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="private-store"
                name="visibility"
                type="radio"
                className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={visibility === "private"}
                onChange={() => setVisibility("private")}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="private-store" className="font-medium text-gray-700">
                Private Store
              </label>
              <p className="text-gray-500">
                Your store is only visible to people with the direct link.
              </p>
            </div>
          </div>

          <div className="pt-4">
            <Button
              onClick={saveVisibilitySettings}
              isLoading={isLoading}
            >
              Save Visibility Settings
            </Button>
          </div>
        </div>
      </SettingsLayout>

      {/* Product Display Settings */}
      <SettingsLayout
        title="Product Display Settings"
        description="Customize how your products appear in your store"
      >
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="show-out-of-stock"
                name="show-out-of-stock"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={showOutOfStock}
                onChange={(e) => setShowOutOfStock(e.target.checked)}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="show-out-of-stock" className="font-medium text-gray-700">
                Show Out of Stock Products
              </label>
              <p className="text-gray-500">
                Display products that are out of stock in your store.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="show-product-views"
                name="show-product-views"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={showProductViews}
                onChange={(e) => setShowProductViews(e.target.checked)}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="show-product-views" className="font-medium text-gray-700">
                Show Product View Count
              </label>
              <p className="text-gray-500">
                Display how many times a product has been viewed.
              </p>
            </div>
          </div>

          <div className="pt-4">
            <Button
              onClick={saveDisplaySettings}
              isLoading={isLoading}
            >
              Save Display Settings
            </Button>
          </div>
        </div>
      </SettingsLayout>

      {/* Notification Settings */}
      <SettingsLayout
        title="Notification Settings"
        description="Manage how you receive notifications about your store"
      >
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="email-notifications"
                name="email-notifications"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={emailNotifications}
                onChange={(e) => setEmailNotifications(e.target.checked)}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="email-notifications" className="font-medium text-gray-700">
                Email Notifications
              </label>
              <p className="text-gray-500">
                Receive email notifications about your store activity.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="product-view-notifications"
                name="product-view-notifications"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={productViewNotifications}
                onChange={(e) => setProductViewNotifications(e.target.checked)}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="product-view-notifications" className="font-medium text-gray-700">
                Product View Notifications
              </label>
              <p className="text-gray-500">
                Get notified when someone views your products.
              </p>
            </div>
          </div>

          <div className="pt-4">
            <Button
              onClick={saveNotificationSettings}
              isLoading={isLoading}
            >
              Save Notification Settings
            </Button>
          </div>
        </div>
      </SettingsLayout>

      {/* Delete Store */}
      <SettingsLayout
        title="Delete Store"
        description="Permanently delete your store and all its data"
      >
        <div className="space-y-4">
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Warning</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    Deleting your store is permanent and cannot be undone. All your products, reviews, and store data will be permanently deleted.
                  </p>
                  {productCount > 0 && (
                    <p className="mt-2">
                      You currently have <span className="font-semibold">{productCount} product{productCount !== 1 ? 's' : ''}</span> in your store that will be deleted.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <Button
              variant="destructive"
              onClick={handleDeleteStore}
              isLoading={isLoading}
            >
              Delete Store
            </Button>
          </div>
        </div>
      </SettingsLayout>
    </div>
  );
}
