"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { cn } from "@/lib/utils";

interface PinInputProps {
  length?: number;
  value: string;
  onChange: (value: string) => void;
  onComplete?: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  className?: string;
  autoFocus?: boolean;
  placeholder?: string;
  showValue?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'modern' | 'minimal';
}

export function PinInput({
  length = 4,
  value,
  onChange,
  onComplete,
  disabled = false,
  error = false,
  className,
  autoFocus = false,
  placeholder = "•",
  showValue = false,
  size = 'md',
  variant = 'modern'
}: PinInputProps) {
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  useEffect(() => {
    if (autoFocus && inputRefs.current[0] && !disabled) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus, disabled]);

  useEffect(() => {
    const complete = value.length === length;
    setIsComplete(complete);
    if (complete && onComplete) {
      onComplete(value);
    }
  }, [value, length, onComplete]);

  const handleChange = (index: number, digit: string) => {
    if (disabled) return;

    // Only allow single digits
    if (digit.length > 1) {
      digit = digit.slice(-1);
    }

    // Only allow numbers
    if (digit && !/^\d$/.test(digit)) {
      return;
    }

    const newValue = value.split('');
    newValue[index] = digit;

    // Fill array to correct length
    while (newValue.length < length) {
      newValue.push('');
    }

    const newPin = newValue.join('');
    onChange(newPin);

    // Move to next input if digit was entered
    if (digit && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    // Call onComplete if all digits are filled
    if (newPin.length === length && !newPin.includes('')) {
      onComplete?.(newPin);
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (disabled) return;

    if (e.key === 'Backspace') {
      e.preventDefault();

      if (value[index]) {
        // Clear current digit
        handleChange(index, '');
      } else if (index > 0) {
        // Move to previous input and clear it
        inputRefs.current[index - 1]?.focus();
        handleChange(index - 1, '');
      }
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    if (disabled) return;

    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').slice(0, length);

    if (digits) {
      onChange(digits.padEnd(length, ''));

      // Focus the next empty input or the last input
      const nextIndex = Math.min(digits.length, length - 1);
      inputRefs.current[nextIndex]?.focus();

      // Call onComplete if all digits are filled
      if (digits.length === length) {
        onComplete?.(digits);
      }
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'w-10 h-10 text-sm';
      case 'lg': return 'w-16 h-16 text-2xl';
      default: return 'w-14 h-14 text-xl';
    }
  };

  // Get variant classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'minimal':
        return 'border-0 border-b-2 rounded-none bg-transparent';
      case 'default':
        return 'border rounded-lg';
      default:
        return 'border-2 rounded-xl';
    }
  };

  return (
    <div className={cn(
      "flex justify-center",
      variant === 'minimal' ? 'space-x-4' : 'space-x-2',
      className
    )}>
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={(el) => {
            inputRefs.current[index] = el;
          }}
          type={showValue ? "text" : "password"}
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={value[index] || ''}
          placeholder={!value[index] ? placeholder : ''}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          onFocus={() => setFocusedIndex(index)}
          onBlur={() => setFocusedIndex(null)}
          disabled={disabled}
          className={cn(
            "text-center font-bold",
            getSizeClasses(),
            getVariantClasses(),
            "focus:outline-none transition-all duration-200 ease-in-out",
            variant !== 'minimal' && "hover:border-gray-400",

            // Focus states
            variant === 'minimal'
              ? "focus:border-blue-500 focus:bg-blue-50/30"
              : "focus:ring-3 focus:ring-blue-500/20 focus:border-blue-500",

            // Error states
            error
              ? variant === 'minimal'
                ? "border-red-500 bg-red-50/30 text-red-900"
                : "border-red-400 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500/20"
              : focusedIndex === index
              ? variant === 'minimal'
                ? "border-blue-500 bg-blue-50/30"
                : "border-blue-500 bg-blue-50 shadow-lg scale-105"
              : value[index]
              ? variant === 'minimal'
                ? "border-green-500 text-green-900"
                : "border-green-400 bg-green-50 text-green-900"
              : variant === 'minimal'
              ? "border-gray-300"
              : "border-gray-300 bg-white",

            // Disabled state
            disabled && "bg-gray-100 text-gray-500 cursor-not-allowed opacity-60",

            // Complete state
            isComplete && !error && (
              variant === 'minimal'
                ? "border-green-500"
                : "border-green-500 bg-green-50"
            )
          )}
          autoComplete="off"
        />
      ))}
    </div>
  );
}

interface PinInputFieldProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  length?: number;
  value: string;
  onChange: (value: string) => void;
  onComplete?: (value: string) => void;
  disabled?: boolean;
  className?: string;
  autoFocus?: boolean;
  placeholder?: string;
  showValue?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'modern' | 'minimal';
  showToggle?: boolean;
}

export function PinInputField({
  label,
  description,
  error,
  required = false,
  length = 4,
  value,
  onChange,
  onComplete,
  disabled = false,
  className,
  autoFocus = false,
  placeholder,
  showValue: initialShowValue = false,
  size = 'md',
  variant = 'modern',
  showToggle = false
}: PinInputFieldProps) {
  const [showValue, setShowValue] = useState(initialShowValue);
  return (
    <div className={cn("space-y-3", className)}>
      {label && (
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
          {showToggle && (
            <button
              type="button"
              onClick={() => setShowValue(!showValue)}
              className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
              disabled={disabled}
            >
              {showValue ? 'লুকান' : 'দেখান'}
            </button>
          )}
        </div>
      )}

      {description && (
        <p className="text-sm text-gray-600">{description}</p>
      )}

      <PinInput
        length={length}
        value={value}
        onChange={onChange}
        onComplete={onComplete}
        disabled={disabled}
        error={!!error}
        autoFocus={autoFocus}
        placeholder={placeholder}
        showValue={showValue}
        size={size}
        variant={variant}
      />

      {error && (
        <p className="text-sm text-red-600 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{error}</span>
        </p>
      )}
    </div>
  );
}
