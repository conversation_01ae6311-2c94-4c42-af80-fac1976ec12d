import { MainLayout } from "@/components/layout/MainLayout";
import { getCurrentUser } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageFollowers, fanPageRoles, users } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { notFound } from "next/navigation";
import { FanPageProfile } from "@/components/fan-pages/FanPageProfile";

interface FanPagePageProps {
  params: Promise<{
    username: string;
  }>;
}

export const dynamic = 'force-dynamic';

export default async function FanPagePage({ params }: FanPagePageProps) {
  try {
    const { username } = await params;
    const user = await getCurrentUser();

    // Get fan page with owner info
    const pageResult = await db
      .select({
        id: fanPages.id,
        ownerId: fanPages.ownerId,
        name: fanPages.name,
        username: fanPages.username,
        category: fanPages.category,
        description: fanPages.description,
        profileImage: fanPages.profileImage,
        coverImage: fanPages.coverImage,
        website: fanPages.website,
        email: fanPages.email,
        phone: fanPages.phone,
        location: fanPages.location,
        isVerified: fanPages.isVerified,
        isActive: fanPages.isActive,
        followerCount: fanPages.followerCount,
        postCount: fanPages.postCount,
        createdAt: fanPages.createdAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPages)
      .leftJoin(users, eq(fanPages.ownerId, users.id))
      .where(eq(fanPages.username, username))
      .limit(1);

    if (pageResult.length === 0) {
      notFound();
    }

    const page = pageResult[0];

    // Check if page is active
    if (!page.isActive) {
      notFound();
    }

    // Check user's relationship with this page
    let isFollowing = false;
    let userRole = null;

    if (user) {
      // Check if following
      const followResult = await db
        .select()
        .from(fanPageFollowers)
        .where(
          and(
            eq(fanPageFollowers.userId, user.id),
            eq(fanPageFollowers.fanPageId, page.id)
          )
        )
        .limit(1);

      isFollowing = followResult.length > 0;

      // Check if user has a role in this page
      const roleResult = await db
        .select()
        .from(fanPageRoles)
        .where(
          and(
            eq(fanPageRoles.userId, user.id),
            eq(fanPageRoles.fanPageId, page.id)
          )
        )
        .limit(1);

      userRole = roleResult[0]?.role || null;
    }

    // Check if user is the owner
    const isOwner = user?.id === page.ownerId;

    const pageData = {
      ...page,
      isVerified: Boolean(page.isVerified),
      isFollowing,
      isOwner,
      userRole,
    };

    return (
      <MainLayout>
        <FanPageProfile page={{
          ...pageData,
          followerCount: pageData.followerCount || 0
        }} />
      </MainLayout>
    );

  } catch (error) {
    console.error("Error loading fan page:", error);
    notFound();
  }
}

// Generate metadata for the page
export async function generateMetadata({ params }: FanPagePageProps) {
  try {
    const { username } = await params;

    const pageResult = await db
      .select({
        name: fanPages.name,
        description: fanPages.description,
        profileImage: fanPages.profileImage,
      })
      .from(fanPages)
      .where(eq(fanPages.username, username))
      .limit(1);

    if (pageResult.length === 0) {
      return {
        title: "Page Not Found",
      };
    }

    const page = pageResult[0];

    return {
      title: `${page.name} (@${username}) | HIFNF`,
      description: page.description || `Follow ${page.name} on HIFNF`,
      openGraph: {
        title: page.name,
        description: page.description || `Follow ${page.name} on HIFNF`,
        images: page.profileImage ? [page.profileImage] : [],
      },
    };
  } catch (error) {
    return {
      title: "Fan Page | HIFNF",
    };
  }
}
