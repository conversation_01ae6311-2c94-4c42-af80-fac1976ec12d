"use client";

import { memo } from "react";
import Link from "next/link";
import { UserBadge } from "./UserBadge";
import { VerifiedBadge } from "./VerifiedBadge";
import { useUserBadgeWithCache } from "@/hooks/useUserBadge";

interface UserWithBadgeProps {
  user: {
    id: string;
    name: string;
    username?: string | null;
    image?: string | null;
    isVerified?: boolean;
  };
  showBadge?: boolean;
  badgeSize?: 'sm' | 'md' | 'lg';
  linkToProfile?: boolean;
  className?: string;
  nameClassName?: string;
  usernameClassName?: string;
  showUsername?: boolean;
}

export const UserWithBadge = memo(function UserWithBadge({
  user,
  showBadge = true,
  badgeSize = 'sm',
  linkToProfile = true,
  className = '',
  nameClassName = '',
  usernameClassName = '',
  showUsername = false
}: UserWithBadgeProps) {
  const { badgeData, loading } = useUserBadgeWithCache({
    userId: user.id,
    enabled: showBadge
  });

  const UserContent = () => (
    <div className={`flex items-center space-x-1 ${className}`}>
      <span className={`font-medium ${nameClassName}`}>
        {user.name}
      </span>
      
      {showUsername && user.username && (
        <span className={`text-gray-500 ${usernameClassName}`}>
          @{user.username}
        </span>
      )}
      
      {user.isVerified && (
        <VerifiedBadge size={badgeSize} />
      )}

      {showBadge && !loading && badgeData.badgeType !== 'none' && (
        <UserBadge
          badgeType={badgeData.badgeType}
          badgeColor={badgeData.badgeColor}
          customBadgeUrl={badgeData.customBadgeUrl}
          planName={badgeData.planName}
          size={badgeSize}
        />
      )}
    </div>
  );

  if (linkToProfile) {
    return (
      <Link
        href={`/user/${user.username || user.id}`}
        className="hover:underline"
      >
        <UserContent />
      </Link>
    );
  }

  return <UserContent />;
});

// Simplified version for when you already have badge data
export const UserWithBadgeStatic = memo(function UserWithBadgeStatic({
  user,
  badgeData,
  badgeSize = 'sm',
  linkToProfile = true,
  className = '',
  nameClassName = '',
  usernameClassName = '',
  showUsername = false
}: {
  user: {
    id: string;
    name: string;
    username?: string | null;
  };
  badgeData?: {
    badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
    badgeColor?: string;
    customBadgeUrl?: string;
    planName?: string;
  };
  badgeSize?: 'sm' | 'md' | 'lg';
  linkToProfile?: boolean;
  className?: string;
  nameClassName?: string;
  usernameClassName?: string;
  showUsername?: boolean;
}) {
  const UserContent = () => (
    <div className={`flex items-center space-x-1 ${className}`}>
      <span className={`font-medium ${nameClassName}`}>
        {user.name}
      </span>
      
      {showUsername && user.username && (
        <span className={`text-gray-500 ${usernameClassName}`}>
          @{user.username}
        </span>
      )}
      
      {badgeData && badgeData.badgeType !== 'none' && (
        <UserBadge
          badgeType={badgeData.badgeType}
          badgeColor={badgeData.badgeColor}
          customBadgeUrl={badgeData.customBadgeUrl}
          planName={badgeData.planName}
          size={badgeSize}
        />
      )}
    </div>
  );

  if (linkToProfile) {
    return (
      <Link
        href={`/user/${user.username || user.id}`}
        className="hover:underline"
      >
        <UserContent />
      </Link>
    );
  }

  return <UserContent />;
});

// For use in lists where you want to batch load badges
export function UserListWithBadges({
  users,
  badgeSize = 'sm',
  linkToProfile = true,
  className = '',
  itemClassName = ''
}: {
  users: Array<{
    id: string;
    name: string;
    username?: string | null;
  }>;
  badgeSize?: 'sm' | 'md' | 'lg';
  linkToProfile?: boolean;
  className?: string;
  itemClassName?: string;
}) {
  return (
    <div className={className}>
      {users.map((user) => (
        <div key={user.id} className={itemClassName}>
          <UserWithBadge
            user={user}
            badgeSize={badgeSize}
            linkToProfile={linkToProfile}
          />
        </div>
      ))}
    </div>
  );
}
