import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { SettingsSidebar } from "@/components/settings/SettingsSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { SettingsLayout } from "@/components/settings/SettingsLayout";
import { Button } from "@/components/ui/Button";
import Image from "next/image";

export default async function TwoFactorAuthPage() {
  const user = await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Settings sidebar */}
          <div className="lg:col-span-1">
            <SettingsSidebar />
          </div>

          {/* Main content */}
          <div className="lg:col-span-2">
            <SettingsLayout 
              title="Two-Factor Authentication" 
              description="Add an extra layer of security to your account."
            >
              <div className="space-y-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800">Enhanced Security</h4>
                  <p className="mt-1 text-sm text-blue-700">
                    Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in.
                  </p>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Authenticator App</h4>
                      <p className="mt-1 text-sm text-gray-500">
                        Use an authenticator app like Google Authenticator, Microsoft Authenticator, or Authy to get verification codes.
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Set up
                    </Button>
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Text Message (SMS)</h4>
                      <p className="mt-1 text-sm text-gray-500">
                        Get verification codes sent to your phone via SMS.
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Set up
                    </Button>
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Backup Codes</h4>
                      <p className="mt-1 text-sm text-gray-500">
                        Generate backup codes to use when you don't have access to your phone.
                      </p>
                    </div>
                    <Button variant="outline" size="sm" disabled>
                      Generate
                    </Button>
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    You need to set up either Authenticator App or SMS verification first.
                  </p>
                </div>
              </div>
              
              <div className="pt-5">
                <div className="flex justify-end">
                  <Button variant="outline" className="mr-3">
                    Cancel
                  </Button>
                  <Button disabled>
                    Save Changes
                  </Button>
                </div>
              </div>
            </SettingsLayout>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}
