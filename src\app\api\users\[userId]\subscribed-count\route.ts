import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptions, users } from "@/lib/db/schema";
import { eq, count } from "drizzle-orm";

// Get count of users that this user is subscribed to
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get count of users this user is subscribed to
    const subscribedCount = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.subscriberId, userId));

    return NextResponse.json({
      count: subscribedCount[0]?.count || 0,
    });
  } catch (error) {
    console.error("Error fetching subscribed count:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
