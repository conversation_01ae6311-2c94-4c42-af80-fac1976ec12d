import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { packageCommissionSettings } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";

// Get package commission settings
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const settings = await db.query.packageCommissionSettings.findMany({
      orderBy: (packageCommissionSettings, { asc }) => [asc(packageCommissionSettings.planName)],
    });

    return NextResponse.json({
      success: true,
      data: settings,
    });

  } catch (error) {
    console.error("Error fetching package commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch package commission settings"
      },
      { status: 500 }
    );
  }
}

// Create package commission settings
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      planId,
      planName,
      isEnabled,
      commissionType,
      firstPurchaseCommission,
      recurringCommission,
      maxCommissionAmount
    } = body;

    // Enhanced validation
    const validationErrors: string[] = [];

    if (!planId || !planName) {
      validationErrors.push("Plan ID and name are required");
    }

    if (!['fixed', 'percentage'].includes(commissionType)) {
      validationErrors.push("Commission type must be 'fixed' or 'percentage'");
    }

    if (!firstPurchaseCommission || isNaN(parseFloat(firstPurchaseCommission)) || parseFloat(firstPurchaseCommission) < 0) {
      validationErrors.push("First purchase commission must be a positive number");
    }

    if (commissionType === 'percentage') {
      if (parseFloat(firstPurchaseCommission) > 100) {
        validationErrors.push("First purchase commission percentage cannot exceed 100%");
      }
      if (recurringCommission && parseFloat(recurringCommission) > 100) {
        validationErrors.push("Recurring commission percentage cannot exceed 100%");
      }
    }

    if (recurringCommission && (isNaN(parseFloat(recurringCommission)) || parseFloat(recurringCommission) < 0)) {
      validationErrors.push("Recurring commission must be a positive number");
    }

    if (maxCommissionAmount && (isNaN(parseFloat(maxCommissionAmount)) || parseFloat(maxCommissionAmount) <= 0)) {
      validationErrors.push("Maximum commission amount must be a positive number");
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: validationErrors
        },
        { status: 400 }
      );
    }

    // Check if settings already exist for this plan
    const existingSettings = await db.query.packageCommissionSettings.findFirst({
      where: (packageCommissionSettings, { eq }) => eq(packageCommissionSettings.planId, planId),
    });

    if (existingSettings) {
      return NextResponse.json(
        { message: "Commission settings already exist for this plan" },
        { status: 400 }
      );
    }

    // Create new package commission settings
    await db.insert(packageCommissionSettings).values({
      id: uuidv4(),
      planId,
      planName,
      isEnabled: isEnabled ?? true,
      commissionType,
      firstPurchaseCommission,
      recurringCommission: recurringCommission || '0.00',
      maxCommissionAmount: maxCommissionAmount || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: "Package commission settings created successfully",
    });

  } catch (error) {
    console.error("Error creating package commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to create package commission settings"
      },
      { status: 500 }
    );
  }
}

// Update package commission settings
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      id,
      planId,
      planName,
      isEnabled,
      commissionType,
      firstPurchaseCommission,
      recurringCommission,
      maxCommissionAmount
    } = body;

    // Enhanced validation
    const validationErrors: string[] = [];

    if (!id) {
      validationErrors.push("Settings ID is required");
    }

    if (!['fixed', 'percentage'].includes(commissionType)) {
      validationErrors.push("Commission type must be 'fixed' or 'percentage'");
    }

    if (!firstPurchaseCommission || isNaN(parseFloat(firstPurchaseCommission)) || parseFloat(firstPurchaseCommission) < 0) {
      validationErrors.push("First purchase commission must be a positive number");
    }

    if (commissionType === 'percentage') {
      if (parseFloat(firstPurchaseCommission) > 100) {
        validationErrors.push("First purchase commission percentage cannot exceed 100%");
      }
      if (recurringCommission && parseFloat(recurringCommission) > 100) {
        validationErrors.push("Recurring commission percentage cannot exceed 100%");
      }
    }

    if (recurringCommission && (isNaN(parseFloat(recurringCommission)) || parseFloat(recurringCommission) < 0)) {
      validationErrors.push("Recurring commission must be a positive number");
    }

    if (maxCommissionAmount && (isNaN(parseFloat(maxCommissionAmount)) || parseFloat(maxCommissionAmount) <= 0)) {
      validationErrors.push("Maximum commission amount must be a positive number");
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: validationErrors
        },
        { status: 400 }
      );
    }

    // Check if settings exist
    const existingSettings = await db.query.packageCommissionSettings.findFirst({
      where: (packageCommissionSettings, { eq }) => eq(packageCommissionSettings.id, id),
    });

    if (!existingSettings) {
      return NextResponse.json(
        { message: "Package commission settings not found" },
        { status: 404 }
      );
    }

    // Update package commission settings
    await db
      .update(packageCommissionSettings)
      .set({
        planName,
        isEnabled: isEnabled ?? true,
        commissionType,
        firstPurchaseCommission,
        recurringCommission: recurringCommission || '0.00',
        maxCommissionAmount: maxCommissionAmount || null,
        updatedAt: new Date(),
      })
      .where((packageCommissionSettings, { eq }) => eq(packageCommissionSettings.id, id));

    return NextResponse.json({
      success: true,
      message: "Package commission settings updated successfully",
    });

  } catch (error) {
    console.error("Error updating package commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update package commission settings"
      },
      { status: 500 }
    );
  }
}
