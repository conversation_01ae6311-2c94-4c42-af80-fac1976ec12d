"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { 
  ShoppingBagIcon, 
  EyeIcon, 
  PlusIcon,
  CalendarIcon,
  MapPinIcon,
  TagIcon
} from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";

interface Product {
  id: string;
  title: string;
  description: string | null;
  price: number;
  condition: 'new' | 'like_new' | 'good' | 'fair' | 'poor';
  category: string;
  location: string | null;
  photos: string[];
  images: string[];
  viewCount: number;
  views: number;
  createdAt: string;
  updatedAt: string;
  store: {
    id: string;
    name: string;
    slug: string;
  } | null;
}

interface ProfileProductsProps {
  userId: string;
  isOwnProfile: boolean;
}

export function ProfileProducts({ userId, isOwnProfile }: ProfileProductsProps) {
  const { data: session } = useSession();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: '1',
        limit: '20',
      });

      const response = await fetch(`/api/users/${userId}/products?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch products");
      }
      
      const data = await response.json();
      setProducts(data.products);
    } catch (err) {
      console.error("Error fetching products:", err);
      setError(err instanceof Error ? err.message : "Failed to load products. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (session?.user) {
      fetchProducts();
    }
  }, [session, fetchProducts]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price / 100); // Assuming price is stored in cents
  };

  const getConditionBadge = (condition: string) => {
    const conditionStyles = {
      new: 'bg-green-100 text-green-800',
      like_new: 'bg-blue-100 text-blue-800',
      good: 'bg-yellow-100 text-yellow-800',
      fair: 'bg-orange-100 text-orange-800',
      poor: 'bg-red-100 text-red-800'
    };

    const conditionLabels = {
      new: 'New',
      like_new: 'Like New',
      good: 'Good',
      fair: 'Fair',
      poor: 'Poor'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${conditionStyles[condition as keyof typeof conditionStyles]}`}>
        {conditionLabels[condition as keyof typeof conditionLabels]}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white rounded-xl border border-gray-200 p-4 animate-pulse">
            <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
              <div className="flex justify-between">
                <div className="h-3 bg-gray-200 rounded w-16"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
        <div className="text-red-500 mb-4">
          <ShoppingBagIcon className="mx-auto h-12 w-12" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Products</h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <Button onClick={fetchProducts} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with create button */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {isOwnProfile ? 'Your Products' : 'Products'}
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {products.length} product{products.length !== 1 ? 's' : ''} found
            </p>
          </div>
          
          {isOwnProfile && (
            <Link href="/marketplace/product/create">
              <Button className="flex items-center gap-2">
                <PlusIcon className="h-4 w-4" />
                Add Product
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Products grid */}
      {products.length === 0 ? (
        <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
          <div className="text-gray-400 mb-4">
            <ShoppingBagIcon className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products yet</h3>
          <p className="text-gray-500 mb-4">
            {isOwnProfile 
              ? "You haven't listed any products yet. Start selling your items!"
              : "This user hasn't listed any products yet."
            }
          </p>
          {isOwnProfile && (
            <Link href="/marketplace/product/create">
              <Button>
                <PlusIcon className="h-4 w-4 mr-2" />
                List Your First Product
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <Link
              key={product.id}
              href={`/marketplace/product/${product.id}`}
              className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-300 hover:scale-[1.02] cursor-pointer group"
            >
              {/* Product image */}
              <div className="aspect-square relative">
                {product.photos && product.photos.length > 0 ? (
                  <img
                    src={product.photos[0]}
                    alt={product.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <ShoppingBagIcon className="h-16 w-16 text-gray-400 group-hover:text-gray-500 transition-colors" />
                  </div>
                )}

                {/* Price overlay */}
                <div className="absolute top-3 left-3">
                  <span className="bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg text-sm font-semibold text-gray-900 group-hover:bg-blue-600 group-hover:text-white transition-colors">
                    {formatPrice(product.price)}
                  </span>
                </div>

                {/* Condition badge */}
                <div className="absolute top-3 right-3">
                  {getConditionBadge(product.condition)}
                </div>
              </div>

              {/* Product details */}
              <div className="p-4">
                <div className="mb-2">
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-1 mb-1 group-hover:text-blue-600 transition-colors">
                    {product.title}
                  </h3>

                  {product.description && (
                    <p className="text-gray-600 text-sm line-clamp-2 mb-2">
                      {product.description}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <TagIcon className="h-3 w-3" />
                      {product.category}
                    </div>
                    <div className="flex items-center gap-1">
                      <EyeIcon className="h-3 w-3" />
                      {product.viewCount} views
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <CalendarIcon className="h-3 w-3" />
                      {formatDate(product.createdAt)}
                    </div>

                    {product.location && (
                      <div className="flex items-center gap-1">
                        <MapPinIcon className="h-3 w-3" />
                        {product.location}
                      </div>
                    )}
                  </div>

                  {product.store && (
                    <div className="pt-2 border-t border-gray-100">
                      <span
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          window.open(`/marketplace/stores/${product.store!.slug}`, '_blank');
                        }}
                        className="text-xs text-blue-600 hover:text-blue-800 transition-colors cursor-pointer hover:underline"
                      >
                        Store: {product.store.name}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}
