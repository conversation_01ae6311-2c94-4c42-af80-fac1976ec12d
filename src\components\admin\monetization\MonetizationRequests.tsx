"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Spinner } from "@/components/ui/Spinner";
import { Modal } from "@/components/ui/Modal";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Textarea } from "@/components/ui/Textarea";
import { toast } from "react-hot-toast";
import {
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserIcon,
  DocumentTextIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";
import Image from "next/image";

interface MonetizationRequest {
  id: string;
  blogId: string;
  isEnabled: boolean;
  isApproved: boolean;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  cprRate: string;
  totalReads: number;
  uniqueReads: number;
  totalEarnings: string;
  createdAt: string;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  blog: {
    id: string;
    title: string;
    slug: string;
    viewCount: number;
    publishedAt: string;
    author: {
      id: string;
      name: string;
      email: string;
      username: string;
      image?: string;
    };
  };
  approver?: {
    id: string;
    name: string;
    email: string;
  };
  rejector?: {
    id: string;
    name: string;
    email: string;
  };
}

interface ApprovalModalData {
  requestId: string;
  blogTitle: string;
  authorName: string;
  approved: boolean;
}

export function MonetizationRequests() {
  const [requests, setRequests] = useState<MonetizationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('pending');
  const [approvalModal, setApprovalModal] = useState<ApprovalModalData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');

  useEffect(() => {
    fetchRequests();
  }, [selectedStatus]);

  const fetchRequests = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (selectedStatus !== 'all') {
        params.append('status', selectedStatus);
      }

      const response = await fetch(`/api/admin/monetization/requests?${params}`);
      const data = await response.json();

      if (data.success) {
        setRequests(data.data);
      } else {
        toast.error(data.message || 'Failed to fetch requests');
      }
    } catch (error) {
      console.error('Error fetching monetization requests:', error);
      toast.error('Failed to fetch requests');
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproval = async (approved: boolean) => {
    if (!approvalModal) return;

    setIsProcessing(true);
    try {
      const response = await fetch('/api/admin/monetization/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          monetizationId: approvalModal.requestId,
          approved,
          reason: approved ? undefined : rejectionReason,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        setApprovalModal(null);
        setRejectionReason('');
        fetchRequests();
      } else {
        toast.error(data.message || 'Failed to process request');
      }
    } catch (error) {
      console.error('Error processing monetization request:', error);
      toast.error('Failed to process request');
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      case 'approved':
        return <Badge variant="success">Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      case 'suspended':
        return <Badge variant="secondary">Suspended</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DocumentTextIcon className="h-5 w-5" />
            Monetization Requests
          </CardTitle>
          <CardDescription>
            Review and manage blog monetization requests from authors
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Status Filter */}
          <div className="flex gap-2 mb-6">
            {['all', 'pending', 'approved', 'rejected', 'suspended'].map((status) => (
              <Button
                key={status}
                variant={selectedStatus === status ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStatus(status)}
                className="capitalize"
              >
                {status}
              </Button>
            ))}
          </div>

          {/* Requests List */}
          {requests.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No monetization requests found for {selectedStatus} status.
            </div>
          ) : (
            <div className="space-y-4">
              {requests.map((request) => (
                <Card key={request.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        {/* Blog Info */}
                        <div className="flex items-start gap-4 mb-4">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg mb-2">
                              {request.blog?.slug ? (
                                <Link
                                  href={`/blogs/${request.blog.slug}`}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  {request.blog.title}
                                </Link>
                              ) : (
                                <span className="text-gray-800">
                                  {request.blog?.title || 'Blog not found'}
                                </span>
                              )}
                            </h3>

                            {/* Author Info */}
                            <div className="flex items-center gap-2 mb-2">
                              {request.blog?.author?.image ? (
                                <Image
                                  src={request.blog.author.image}
                                  alt={request.blog.author.name}
                                  width={24}
                                  height={24}
                                  className="rounded-full"
                                />
                              ) : (
                                <UserIcon className="h-6 w-6 text-gray-400" />
                              )}
                              <span className="text-sm text-gray-600">
                                {request.blog?.author?.name || 'Unknown Author'} {request.blog?.author?.username ? `(@${request.blog.author.username})` : ''}
                              </span>
                            </div>

                            {/* Stats */}
                            <div className="flex items-center gap-4 text-sm text-gray-600">
                              <div className="flex items-center gap-1">
                                <EyeIcon className="h-4 w-4" />
                                {request.blog?.viewCount || 0} views
                              </div>
                              <div className="flex items-center gap-1">
                                <ClockIcon className="h-4 w-4" />
                                {formatDate(request.createdAt)}
                              </div>
                              <div className="flex items-center gap-1">
                                <CurrencyDollarIcon className="h-4 w-4" />
                                ${request.cprRate} CPR
                              </div>
                            </div>
                          </div>

                          <div className="flex flex-col items-end gap-2">
                            {getStatusBadge(request.status)}

                            {request.status === 'pending' && (
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-green-600 border-green-600 hover:bg-green-50"
                                  onClick={() => setApprovalModal({
                                    requestId: request.id,
                                    blogTitle: request.blog?.title || 'Unknown Blog',
                                    authorName: request.blog?.author?.name || 'Unknown Author',
                                    approved: true,
                                  })}
                                >
                                  <CheckCircleIcon className="h-4 w-4 mr-1" />
                                  Approve
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-red-600 border-red-600 hover:bg-red-50"
                                  onClick={() => setApprovalModal({
                                    requestId: request.id,
                                    blogTitle: request.blog?.title || 'Unknown Blog',
                                    authorName: request.blog?.author?.name || 'Unknown Author',
                                    approved: false,
                                  })}
                                >
                                  <XCircleIcon className="h-4 w-4 mr-1" />
                                  Reject
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Additional Info for Processed Requests */}
                        {request.status !== 'pending' && (
                          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                            {request.status === 'approved' && request.approver && (
                              <p className="text-sm text-green-700">
                                Approved by {request.approver.name} on {formatDate(request.approvedAt!)}
                              </p>
                            )}
                            {request.status === 'rejected' && request.rejector && (
                              <div className="text-sm text-red-700">
                                <p>Rejected by {request.rejector.name} on {formatDate(request.rejectedAt!)}</p>
                                {request.rejectionReason && (
                                  <p className="mt-1 italic">Reason: {request.rejectionReason}</p>
                                )}
                              </div>
                            )}
                          </div>
                        )}

                        {/* Earnings Info for Approved */}
                        {request.status === 'approved' && (
                          <div className="mt-4 grid grid-cols-3 gap-4">
                            <div className="text-center p-3 bg-blue-50 rounded-lg">
                              <div className="text-lg font-semibold text-blue-600">
                                {request.uniqueReads.toLocaleString()}
                              </div>
                              <div className="text-sm text-blue-700">Qualified Reads</div>
                            </div>
                            <div className="text-center p-3 bg-green-50 rounded-lg">
                              <div className="text-lg font-semibold text-green-600">
                                ${parseFloat(request.totalEarnings).toFixed(2)}
                              </div>
                              <div className="text-sm text-green-700">Total Earnings</div>
                            </div>
                            <div className="text-center p-3 bg-purple-50 rounded-lg">
                              <div className="text-lg font-semibold text-purple-600">
                                ${request.cprRate}
                              </div>
                              <div className="text-sm text-purple-700">CPR Rate</div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Approval/Rejection Modal */}
      {approvalModal && (
        <Modal
          isOpen={true}
          onClose={() => {
            setApprovalModal(null);
            setRejectionReason('');
          }}
          title={approvalModal.approved ? 'Approve Monetization' : 'Reject Monetization'}
        >
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium">{approvalModal.blogTitle}</h4>
              <p className="text-sm text-gray-600">by {approvalModal.authorName}</p>
            </div>

            {approvalModal.approved ? (
              <p className="text-sm text-gray-600">
                Are you sure you want to approve this blog for monetization?
                The author will be able to earn money from qualified reads.
              </p>
            ) : (
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  Please provide a reason for rejecting this monetization request:
                </p>
                <div>
                  <Label htmlFor="rejectionReason">Rejection Reason</Label>
                  <Textarea
                    id="rejectionReason"
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Explain why this request is being rejected..."
                    rows={3}
                    required
                  />
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  setApprovalModal(null);
                  setRejectionReason('');
                }}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleApproval(approvalModal.approved)}
                disabled={isProcessing || (!approvalModal.approved && !rejectionReason.trim())}
                className={approvalModal.approved ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
              >
                {isProcessing ? (
                  <Spinner size="sm" />
                ) : approvalModal.approved ? (
                  'Approve'
                ) : (
                  'Reject'
                )}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
