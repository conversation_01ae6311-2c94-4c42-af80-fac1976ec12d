"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, PhotoIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { useDropzone } from "react-dropzone";
import { uploadToCloudinary } from "@/lib/cloudinary";

interface ProfilePhotoUploaderProps {
  isOpen: boolean;
  onClose: () => void;
  currentPhotoUrl?: string | null;
  onSuccess?: () => void;
}

export function ProfilePhotoUploader({
  isOpen,
  onClose,
  currentPhotoUrl,
  onSuccess,
}: ProfilePhotoUploaderProps) {
  const { data: session, update } = useSession();
  const router = useRouter();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentPhotoUrl || null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setSelectedFile(file);
        setPreviewUrl(URL.createObjectURL(file));
      }
    },
  });

  const handleSave = async () => {
    if (!selectedFile || !session?.user?.id) return;

    setIsUploading(true);
    setError(null);

    try {
      // Upload image to Cloudinary
      const imageUrl = await uploadToCloudinary(selectedFile);
      console.log('Uploaded profile image URL:', imageUrl);

      // Update user profile in the database
      const response = await fetch(`/api/users/${session.user.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          image: imageUrl,
        }),
      });

      const result = await response.json();
      console.log('Profile update response:', result);

      if (!response.ok) {
        throw new Error("Failed to update profile photo");
      }

      // Update the session with the new image
      await update({
        ...session,
        user: {
          ...session.user,
          image: imageUrl,
        },
      });

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page to show the updated profile
      router.refresh();
      onClose();
    } catch (err) {
      console.error("Error updating profile photo:", err);
      setError("Failed to update profile photo. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        if (!isUploading) onClose();
      }}
      className="relative z-50"
    >
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="w-full max-w-md rounded-xl bg-white p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <DialogTitle className="text-lg font-medium">
              Update Profile Photo
            </DialogTitle>
            <button
              type="button"
              onClick={onClose}
              disabled={isUploading}
              className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="space-y-4">
            {/* Preview area */}
            {previewUrl ? (
              <div className="relative mx-auto h-40 w-40 overflow-hidden rounded-full border-4 border-white bg-gray-100 shadow-md">
                <OptimizedImage
                  src={previewUrl}
                  alt="Profile preview"
                  fill
                  className="object-cover"
                  loadingClassName="bg-gray-200 animate-pulse"
                />
              </div>
            ) : (
              <div className="mx-auto h-40 w-40 rounded-full bg-gray-200 flex items-center justify-center">
                <PhotoIcon className="h-16 w-16 text-gray-400" />
              </div>
            )}

            {/* Dropzone */}
            <div
              {...getRootProps()}
              className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
            >
              <input {...getInputProps()} />
              <PhotoIcon className="h-8 w-8 mx-auto text-gray-400" />
              <p className="mt-1 text-sm text-gray-500">
                Drag & drop an image here, or click to select
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Recommended: Square image, at least 400x400 pixels
              </p>
            </div>

            {error && (
              <div className="text-red-500 text-sm text-center">{error}</div>
            )}

            <div className="flex justify-end space-x-3 mt-4">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={!selectedFile || isUploading}
                isLoading={isUploading}
              >
                Save
              </Button>
            </div>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
