"use client";

import { ReactNode, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface NotificationBadgeProps {
  count: number;
  maxCount?: number;
  children: ReactNode;
  variant?: 'default' | 'messages' | 'high-priority' | 'dot';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  animate?: boolean;
  onClick?: () => void;
  className?: string;
  showZero?: boolean;
}

export function NotificationBadge({
  count,
  maxCount = 99,
  children,
  variant = 'default',
  position = 'top-right',
  animate = true,
  onClick,
  className,
  showZero = false
}: NotificationBadgeProps) {
  const [isAnimating, setIsAnimating] = useState(false);
  const [prevCount, setPrevCount] = useState(count);

  // Trigger animation when count changes
  useEffect(() => {
    if (count !== prevCount && animate) {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 300);
      setPrevCount(count);
      return () => clearTimeout(timer);
    }
  }, [count, prevCount, animate]);

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();
  const shouldShow = count > 0 || showZero;

  const getVariantStyles = () => {
    switch (variant) {
      case 'messages':
        return 'bg-blue-500 text-white border-2 border-white';
      case 'high-priority':
        return 'bg-red-500 text-white border-2 border-white animate-pulse';
      case 'dot':
        return 'bg-red-500 w-3 h-3 border-2 border-white';
      default:
        return 'bg-red-500 text-white border-2 border-white';
    }
  };

  const getPositionStyles = () => {
    switch (position) {
      case 'top-left':
        return '-top-2 -left-2';
      case 'bottom-right':
        return '-bottom-2 -right-2';
      case 'bottom-left':
        return '-bottom-2 -left-2';
      default:
        return '-top-2 -right-2';
    }
  };

  const getBadgeSize = () => {
    if (variant === 'dot') return 'w-3 h-3';
    if (count > 99) return 'min-w-[24px] h-6 px-1';
    if (count > 9) return 'min-w-[20px] h-5 px-1';
    return 'w-5 h-5';
  };

  return (
    <div className={cn("relative inline-block", className)}>
      {children}
      
      {shouldShow && (
        <div
          className={cn(
            "absolute flex items-center justify-center rounded-full text-xs font-bold transition-all duration-200",
            getVariantStyles(),
            getPositionStyles(),
            getBadgeSize(),
            isAnimating && "scale-125",
            onClick && "cursor-pointer hover:scale-110 active:scale-95"
          )}
          onClick={onClick}
        >
          {variant !== 'dot' && (
            <span className={cn(
              "transition-all duration-200",
              isAnimating && "animate-bounce"
            )}>
              {displayCount}
            </span>
          )}
        </div>
      )}

      {/* Pulse effect for new notifications */}
      {shouldShow && isAnimating && (
        <div
          className={cn(
            "absolute rounded-full animate-ping",
            getPositionStyles(),
            getBadgeSize(),
            variant === 'high-priority' ? 'bg-red-400' : 'bg-red-400'
          )}
        />
      )}
    </div>
  );
}
