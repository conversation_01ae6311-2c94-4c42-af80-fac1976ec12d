"use client";

import { Button } from "@/components/ui/Button";
import { useFanPageFollow } from "@/hooks/useFanPageFollow";

interface FollowButtonProps {
  pageId: string;
  pageName: string;
  initialIsFollowing: boolean;
  initialFollowerCount: number;
  variant?: "default" | "compact" | "card";
  className?: string;
  onUpdate?: (data: { isFollowing: boolean; followerCount: number }) => void;
  useQuickApi?: boolean;
}

export function FollowButton({
  pageId,
  pageName,
  initialIsFollowing,
  initialFollowerCount,
  variant = "default",
  className = "",
  onUpdate,
  useQuickApi = true,
}: FollowButtonProps) {
  const {
    isFollowing,
    isLoading,
    toggleFollow,
  } = useFanPageFollow({
    pageId,
    pageName,
    initialIsFollowing,
    initialFollowerCount,
    onUpdate,
    useQuickApi,
  });

  const getButtonStyles = () => {
    const baseStyles = "transition-all duration-200 font-medium";

    switch (variant) {
      case "compact":
        return `${baseStyles} px-3 py-1 text-xs rounded-lg ${
          isFollowing
            ? 'bg-blue-100 text-blue-700 hover:bg-blue-200 border border-blue-300'
            : 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md'
        }`;

      case "card":
        return `${baseStyles} w-full rounded-xl border-2 text-xs sm:text-sm py-2 ${
          isFollowing
            ? 'border-blue-500 text-blue-600 bg-blue-50 hover:bg-blue-100'
            : 'border-purple-500 text-purple-600 bg-purple-50 hover:bg-purple-100'
        }`;

      default:
        return `${baseStyles} min-w-[120px] h-11 font-semibold transform hover:scale-105 ${
          isFollowing
            ? 'border-2 border-blue-500 text-blue-600 hover:bg-blue-50 hover:border-blue-600'
            : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl text-white'
        } ${isLoading ? 'cursor-not-allowed opacity-75' : 'cursor-pointer'}`;
    }
  };

  const getButtonContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span>{variant === "compact" ? "..." : (isFollowing ? 'Unfollowing...' : 'Following...')}</span>
        </div>
      );
    }

    if (isFollowing) {
      return (
        <div className="flex items-center space-x-2 group">
          <svg className="w-3 h-3 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          {variant === "compact" ? (
            <span>Following</span>
          ) : (
            <>
              <span className="group-hover:hidden">Following</span>
              <span className="hidden group-hover:inline">Unfollow</span>
            </>
          )}
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-2 group">
        <svg className="w-3 h-3 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
        </svg>
        <span>{variant === "card" ? 'Follow Page' : 'Follow'}</span>
      </div>
    );
  };

  return (
    <Button
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        toggleFollow();
      }}
      disabled={isLoading}
      variant={isFollowing ? "outline" : "primary"}
      className={`${getButtonStyles()} ${className}`}
    >
      {getButtonContent()}
    </Button>
  );
}
