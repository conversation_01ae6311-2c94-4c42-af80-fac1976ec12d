"use client";

import { memo } from "react";
import {
  HandThumbUpIcon,
  HandThumbDownIcon,
  ChatBubbleOvalLeftIcon,
  ShareIcon,
} from "@heroicons/react/24/outline";
import {
  HandThumbUpIcon as HandThumbUpSolidIcon,
  HandThumbDownIcon as HandThumbDownSolidIcon,
} from "@heroicons/react/24/solid";
import { cn } from "@/lib/utils";

interface PostActionButtonsProps {
  // Like functionality
  liked: boolean;
  onLike: () => void;
  isLikeAnimating?: boolean;

  // Dislike functionality
  disliked: boolean;
  onDislike: () => void;
  isDislikeAnimating?: boolean;

  // Comment functionality
  showComments?: boolean;
  onToggleComments?: () => void;
  showCommentsButton?: boolean;

  // Share functionality
  onShare?: () => void;

  // Post type specific props
  postType?: 'user_post' | 'fan_page_post' | 'group_post' | 'blog_post';

  // General props
  disabled?: boolean;
  className?: string;
}

export const PostActionButtons = memo(function PostActionButtons({
  liked,
  onLike,
  isLikeAnimating = false,
  disliked,
  onDislike,
  isDislikeAnimating = false,
  showComments = false,
  onToggleComments,
  showCommentsButton = true,
  onShare,
  postType = 'user_post',
  disabled = false,
  className = ""
}: PostActionButtonsProps) {
  // All post types support dislike functionality
  const showDislikeButton = true;
  const isAnimating = isLikeAnimating || isDislikeAnimating;

  return (
    <>
      {/* Divider */}
      <hr className="my-2 border-gray-200" />

      {/* Action Buttons */}
      <div className={cn(
        "grid gap-1",
        showCommentsButton ? "grid-cols-4" : "grid-cols-3",
        className
      )}>
        {/* Like button */}
        <button
          className={cn(
            "flex items-center justify-center py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-lg sm:rounded-xl relative overflow-hidden min-h-[44px]",
            liked
              ? "text-blue-600 bg-blue-50"
              : "text-black hover:bg-gray-50",
            (isAnimating || disabled) && "pointer-events-none",
            isLikeAnimating && "like-animation bg-blue-100"
          )}
          onClick={onLike}
          disabled={isAnimating || disabled}
          aria-label={liked ? "Unlike post" : "Like post"}
        >
          <div className={cn(
            "flex items-center space-x-1 sm:space-x-2",
            isLikeAnimating && "scale-110"
          )}>
            {liked ? (
              <HandThumbUpSolidIcon className={cn(
                "h-4 w-4 sm:h-5 sm:w-5",
                isLikeAnimating ? "scale-125 text-blue-700" : ""
              )} />
            ) : (
              <HandThumbUpIcon className={cn(
                "h-4 w-4 sm:h-5 sm:w-5",
                isLikeAnimating ? "scale-125 text-blue-600" : ""
              )} />
            )}
            <span className="hidden sm:inline">Like</span>
          </div>
          {/* Enhanced ripple effect */}
          {isLikeAnimating && (
            <>
              <div className="absolute inset-0 bg-blue-400 opacity-20 rounded-xl ripple-effect" />
              <div className="absolute inset-0 bg-blue-300 opacity-10 rounded-xl animate-pulse" />
            </>
          )}
        </button>

        {/* Dislike button - Hidden for blog posts */}
        {showDislikeButton && (
          <button
            className={cn(
              "flex items-center justify-center py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-lg sm:rounded-xl relative overflow-hidden min-h-[44px]",
              disliked
                ? "text-red-600 bg-red-50"
                : "text-black hover:bg-gray-50",
              (isAnimating || disabled) && "pointer-events-none",
              isDislikeAnimating && "dislike-animation bg-red-100"
            )}
            onClick={onDislike}
            disabled={isAnimating || disabled}
            aria-label={disliked ? "Remove dislike" : "Dislike post"}
          >
          <div className={cn(
            "flex items-center space-x-1 sm:space-x-2",
            isDislikeAnimating && "scale-110"
          )}>
            {disliked ? (
              <HandThumbDownSolidIcon className={cn(
                "h-4 w-4 sm:h-5 sm:w-5",
                isDislikeAnimating ? "scale-125 text-red-700" : ""
              )} />
            ) : (
              <HandThumbDownIcon className={cn(
                "h-4 w-4 sm:h-5 sm:w-5",
                isDislikeAnimating ? "scale-125 text-red-600" : ""
              )} />
            )}
            <span className="hidden sm:inline">Dislike</span>
          </div>
          {/* Enhanced ripple effect */}
          {isDislikeAnimating && (
            <>
              <div className="absolute inset-0 bg-red-400 opacity-20 rounded-xl ripple-effect" />
              <div className="absolute inset-0 bg-red-300 opacity-10 rounded-xl animate-pulse" />
            </>
          )}
          </button>
        )}

        {/* Comment button */}
        {showCommentsButton && (
          <button
            className="flex items-center justify-center py-2 sm:py-3 text-xs sm:text-sm font-medium text-black hover:bg-gray-50 rounded-lg sm:rounded-xl min-h-[44px]"
            onClick={onToggleComments}
            aria-label="Toggle comments"
            disabled={disabled}
          >
            <div className="flex items-center space-x-1 sm:space-x-2">
              <ChatBubbleOvalLeftIcon className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden sm:inline">Comment</span>
            </div>
          </button>
        )}

        {/* Share button */}
        <button
          className="flex items-center justify-center py-2 sm:py-3 text-xs sm:text-sm font-medium text-black hover:bg-gray-50 rounded-lg sm:rounded-xl min-h-[44px]"
          onClick={onShare}
          aria-label="Share post"
          disabled={disabled}
        >
          <div className="flex items-center space-x-1 sm:space-x-2">
            <ShareIcon className="h-4 w-4 sm:h-5 sm:w-5" />
            <span className="hidden sm:inline">Share</span>
          </div>
        </button>
      </div>
    </>
  );
});
