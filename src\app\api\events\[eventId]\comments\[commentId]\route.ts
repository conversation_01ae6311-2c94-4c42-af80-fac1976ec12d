import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventComments } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and } from "drizzle-orm";

const commentUpdateSchema = z.object({
  content: z.string().min(1).max(5000),
});

// Update a comment
export async function PUT(
  req: Request,
  context: { params: Promise<{ eventId: string; commentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId, commentId } = params;
    const body = await req.json();
    const validatedData = commentUpdateSchema.parse(body);

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the comment exists and belongs to the user
    const comment = await db.query.eventComments.findFirst({
      where: and(
        eq(eventComments.id, commentId),
        eq(eventComments.eventId, eventId)
      ),
    });

    if (!comment) {
      return NextResponse.json(
        { message: "Comment not found" },
        { status: 404 }
      );
    }

    if (comment.userId !== session.user.id && event.hostId !== session.user.id) {
      return NextResponse.json(
        { message: "You don't have permission to update this comment" },
        { status: 403 }
      );
    }

    // Update the comment
    await db
      .update(eventComments)
      .set({ content: validatedData.content })
      .where(eq(eventComments.id, commentId));

    // Fetch the updated comment
    const updatedComment = await db.query.eventComments.findFirst({
      where: eq(eventComments.id, commentId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
    });

    if (!updatedComment) {
      return NextResponse.json(
        { message: "Failed to update comment" },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedComment);
  } catch (error) {
    console.error("Error updating event comment:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a comment
export async function DELETE(
  req: Request,
  context: { params: Promise<{ eventId: string; commentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId, commentId } = params;

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the comment exists
    const comment = await db.query.eventComments.findFirst({
      where: and(
        eq(eventComments.id, commentId),
        eq(eventComments.eventId, eventId)
      ),
    });

    if (!comment) {
      return NextResponse.json(
        { message: "Comment not found" },
        { status: 404 }
      );
    }

    // Check if the user is the comment author or the event host
    if (comment.userId !== session.user.id && event.hostId !== session.user.id) {
      return NextResponse.json(
        { message: "You don't have permission to delete this comment" },
        { status: 403 }
      );
    }

    // Delete the comment
    await db.delete(eventComments).where(eq(eventComments.id, commentId));

    return NextResponse.json({ message: "Comment deleted successfully" });
  } catch (error) {
    console.error("Error deleting event comment:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
