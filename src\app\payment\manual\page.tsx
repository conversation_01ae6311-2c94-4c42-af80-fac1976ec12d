"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/Button";
import {
  BanknotesIcon,
  ClipboardDocumentIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  config: {
    instructions?: string;
    accountDetails?: string;
  };
}

// Component that uses useSearchParams
function ManualPaymentContent() {
  const searchParams = useSearchParams();
  const [gateway, setGateway] = useState<PaymentGateway | null>(null);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState<string | null>(null);

  const gatewayId = searchParams?.get('gateway');
  const amount = searchParams?.get('amount');

  useEffect(() => {
    if (gatewayId) {
      fetchGatewayDetails();
    }
  }, [gatewayId]);

  const fetchGatewayDetails = async () => {
    try {
      const response = await fetch(`/api/admin/wallet/gateways/${gatewayId}`);
      const data = await response.json();

      if (data.success) {
        setGateway(data.data);
      } else {
        toast.error('Failed to fetch payment details');
      }
    } catch (error) {
      console.error('Error fetching gateway details:', error);
      toast.error('Failed to fetch payment details');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(label);
      toast.success(`${label} copied to clipboard`);
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!gateway) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Payment Gateway Not Found</h1>
          <p className="text-gray-600 mb-6">The requested payment gateway could not be found.</p>
          <Button onClick={() => window.history.back()}>
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-blue-600 px-6 py-8 text-white text-center">
            <BanknotesIcon className="h-12 w-12 mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">Manual Payment Instructions</h1>
            <p className="text-blue-100">
              Complete your payment of <span className="font-bold">${amount}</span> using the details below
            </p>
          </div>

          {/* Content */}
          <div className="px-6 py-8 space-y-6">
            {/* Payment Instructions */}
            {gateway.config.instructions && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-3">Payment Instructions</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700 whitespace-pre-line">
                    {gateway.config.instructions}
                  </p>
                </div>
              </div>
            )}

            {/* Account Details */}
            {gateway.config.accountDetails && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-3">Account Details</h2>
                <div className="bg-gray-50 rounded-lg p-4 relative">
                  <p className="text-gray-700 whitespace-pre-line mb-3">
                    {gateway.config.accountDetails}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(gateway.config.accountDetails || '', 'Account Details')}
                    className="absolute top-3 right-3"
                  >
                    {copied === 'Account Details' ? (
                      <CheckCircleIcon className="h-4 w-4" />
                    ) : (
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Payment Amount */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Payment Amount</h2>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="text-green-800 font-medium">Amount to Pay:</span>
                  <span className="text-2xl font-bold text-green-900">${amount}</span>
                </div>
              </div>
            </div>

            {/* Important Notes */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-yellow-800 font-medium mb-2">Important Notes:</h3>
              <ul className="text-yellow-700 text-sm space-y-1">
                <li>• Please include your transaction reference in the payment description</li>
                <li>• Payment processing may take 1-3 business days</li>
                <li>• Keep your payment receipt for verification</li>
                <li>• Contact support if you need assistance</li>
              </ul>
            </div>

            {/* Actions */}
            <div className="flex space-x-4">
              <Button
                onClick={() => window.history.back()}
                variant="outline"
                className="flex-1"
              >
                Go Back
              </Button>
              <Button
                onClick={() => {
                  toast.success('Payment instructions noted. Please complete your payment and contact support for verification.');
                  window.location.href = '/wallet';
                }}
                className="flex-1"
              >
                I've Made the Payment
              </Button>
            </div>
          </div>
        </div>

        {/* Support Contact */}
        <div className="mt-8 text-center">
          <p className="text-gray-600 mb-2">Need help with your payment?</p>
          <Button variant="outline" size="sm">
            Contact Support
          </Button>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function ManualPaymentPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <ManualPaymentContent />
    </Suspense>
  );
}
