import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { products, stores, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { notFound } from "next/navigation";
import { ProductDetail } from "@/components/marketplace/ProductDetail";

export default async function ProductDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  await requireAuth();
  const resolvedParams = await params;

  // Fetch product details
  const productDetails = await db
    .select({
      id: products.id,
      title: products.title,
      description: products.description,
      price: products.price,
      condition: products.item_condition,
      category: products.category,
      location: products.location,
      photos: products.photos,
      viewCount: products.viewCount,
      createdAt: products.createdAt,
      updatedAt: products.updatedAt,
      store: {
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        logo: stores.logo,
        isVerified: stores.isVerified,
      },
      owner: {
        id: users.id,
        name: users.name,
        image: users.image,
      },
    })
    .from(products)
    .leftJoin(stores, eq(products.storeId, stores.id))
    .leftJoin(users, eq(stores.ownerId, users.id))
    .where(eq(products.id, resolvedParams.id))
    .limit(1);

  if (productDetails.length === 0) {
    notFound();
  }

  const product = productDetails[0];

  // Increment view count (in a real app, you might want to do this client-side)
  await db
    .update(products)
    .set({
      viewCount: (product.viewCount || 0) + 1,
    })
    .where(eq(products.id, resolvedParams.id));

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <ProductDetail product={product} />
      </div>
    </MainLayout>
  );
}
