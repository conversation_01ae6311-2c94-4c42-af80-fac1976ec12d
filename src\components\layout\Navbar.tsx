"use client";

import { useState, Fragment, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { signOut, useSession } from "next-auth/react";
import { Menu, Transition } from "@headlessui/react";
import {
  HomeIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  BellIcon,
  UserCircleIcon,
  Bars3Icon,
  XMarkIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  BanknotesIcon,
  CreditCardIcon,
  CommandLineIcon,
  UsersIcon,
  ShieldCheckIcon,
  QuestionMarkCircleIcon,
  HeartIcon
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { SearchSuggestions } from "@/components/search/SearchSuggestions";
import { MessageDropdown } from "@/components/messages/MessageDropdown";
import { ConnectionDropdown } from "@/components/connection/ConnectionDropdown";
import { NotificationDropdown } from "@/components/notifications/NotificationDropdown";



export function Navbar() {
  const pathname = usePathname();
  const router = useRouter();
  const { data: session } = useSession();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [username, setUsername] = useState("");
  const [searchInputRef, setSearchInputRef] = useState<HTMLInputElement | null>(null);

  // Navigation items for mobile menu
  const navigation = [
    { name: "Groups", href: "/groups", icon: UserGroupIcon },
    { name: "Events", href: "/events", icon: BellIcon },
  ];

  // Keyboard shortcut for search (Ctrl/Cmd + K)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        searchInputRef?.focus();
      }
      // ESC to close search suggestions
      if (event.key === 'Escape') {
        setShowSuggestions(false);
        searchInputRef?.blur();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [searchInputRef]);

  useEffect(() => {
    async function fetchUserUsername() {
      if (!session?.user?.id) return;

      try {
        const response = await fetch(`/api/users/${session.user.id}`);
        if (response.ok) {
          const userData = await response.json();
          if (userData.username) {
            setUsername(userData.username);
          }
        }
      } catch (error) {
        console.error('Error fetching user username:', error);
      }
    }

    fetchUserUsername();
  }, [session]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setShowSuggestions(false);
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleSearchFromSuggestions = (query: string) => {
    setSearchQuery(query);
    router.push(`/search?q=${encodeURIComponent(query)}`);
  };

  return (
    <header className="bg-gradient-to-r from-white via-blue-50/30 to-white backdrop-blur-md shadow-sm border-b border-blue-100/50 fixed top-0 left-0 right-0 z-50">
      <nav className="mx-auto flex max-w-7xl items-center justify-between p-2 sm:p-4 lg:px-8" aria-label="Global">
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1 p-1 sm:-m-1.5 sm:p-1.5 group transition-all duration-300 hover:scale-105">
            <div className="relative">
              <Image
                src="/logo.png"
                alt="HIFNF Logo"
                width={90}
                height={30}
                priority
                className="h-6 sm:h-8 w-auto transition-all duration-300 group-hover:brightness-110"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
            </div>
          </Link>
        </div>
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2 inline-flex items-center justify-center rounded-xl p-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 active:scale-95"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Bars3Icon className="h-5 w-5 sm:h-6 sm:w-6 transition-transform duration-200 hover:scale-110" aria-hidden="true" />
          </button>
        </div>

        {/* Home button */}
        <div className="hidden lg:flex lg:items-center lg:mr-4">
          <Link
            href="/"
            className={cn(
              "flex items-center justify-center p-2 rounded-xl w-10 h-10 transition-all duration-200 group relative",
              pathname === "/"
                ? "text-blue-600 bg-gradient-to-br from-blue-50 to-blue-100 shadow-sm"
                : "text-gray-700 hover:text-blue-600 hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm"
            )}
            title="Home"
          >
            <HomeIcon className="h-6 w-6 transition-transform duration-200 group-hover:scale-110" />
            <span className="sr-only">Home</span>
            {pathname === "/" && (
              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"></div>
            )}
          </Link>
        </div>

        {/* Search bar */}
        <div className="hidden lg:flex lg:flex-1 lg:justify-center">
          <form onSubmit={handleSearch} className="w-full max-w-lg">
            <div className="relative group">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200" aria-hidden="true" />
              </div>
              <input
                ref={setSearchInputRef}
                type="text"
                className="block w-full rounded-xl border-0 bg-gradient-to-r from-gray-50 to-blue-50/30 py-2.5 pl-11 pr-20 text-gray-900 ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500 focus:bg-white sm:text-sm sm:leading-6 transition-all duration-200 shadow-sm hover:shadow-md focus:shadow-lg"
                placeholder="🔍 Search people, groups, pages, events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setShowSuggestions(true)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              />
              {/* Keyboard shortcut indicator */}
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <kbd className="hidden sm:inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-sans text-gray-400 bg-white/50">
                  <span className="text-xs">⌘</span>K
                </kbd>
              </div>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-purple-500/0 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              {showSuggestions && (
                <SearchSuggestions
                  query={searchQuery}
                  onSelect={() => setShowSuggestions(false)}
                  onSearch={handleSearchFromSuggestions}
                />
              )}
            </div>
          </form>
        </div>

        {/* Navigation links */}
        <div className="hidden lg:flex lg:gap-x-8 lg:ml-8">
          {/* Connections Dropdown */}
          <ConnectionDropdown />

          {/* Notification Dropdown */}
          <NotificationDropdown />
          {/* Message Dropdown */}
          <MessageDropdown />
        </div>
        <div className="hidden lg:flex lg:flex-1 lg:justify-end">
          {session ? (
            <Menu as="div" className="relative ml-3">
              <div>
                <Menu.Button className="flex items-center gap-2 rounded-xl bg-gradient-to-r from-white to-blue-50/50 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:shadow-md transition-all duration-200 border border-gray-200/50 group">
                  <span className="sr-only">Open user menu</span>
                  <div className="relative">
                    {session.user.image ? (
                      <Image
                        src={session.user.image}
                        alt={session.user.name || "Profile"}
                        width={32}
                        height={32}
                        className="rounded-full ring-2 ring-blue-200/50 group-hover:ring-blue-300 transition-all duration-200"
                      />
                    ) : (
                      <UserCircleIcon className="h-8 w-8 text-gray-400 group-hover:text-blue-500 transition-colors duration-200" />
                    )}
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                  </div>
                  <span className="hidden md:block text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-200">
                    {session.user.name}
                  </span>
                  <ChevronDownIcon className="h-4 w-4 text-gray-500 group-hover:text-blue-500 transition-all duration-200 group-hover:rotate-180" aria-hidden="true" />
                </Menu.Button>
              </div>
              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="transform opacity-0 scale-95 translate-y-1"
                enterTo="transform opacity-100 scale-100 translate-y-0"
                leave="transition ease-in duration-150"
                leaveFrom="transform opacity-100 scale-100 translate-y-0"
                leaveTo="transform opacity-0 scale-95 translate-y-1"
              >
                <Menu.Items className="absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-xl bg-white py-0 shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-100 backdrop-blur-sm overflow-hidden">
                  {/* User Profile Header */}
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-4">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        {session.user.image ? (
                          <Image
                            src={session.user.image}
                            alt={session.user.name || "Profile"}
                            width={48}
                            height={48}
                            className="rounded-full ring-2 ring-white/50"
                          />
                        ) : (
                          <UserCircleIcon className="h-12 w-12 text-white" />
                        )}
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-semibold text-sm truncate">
                          {session.user.name}
                        </h3>
                        <p className="text-blue-100 text-xs">
                          {session.user.email}
                        </p>
                        <div className="flex items-center mt-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                          <span className="text-blue-100 text-xs">Online</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="py-2">
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href={username ? `/user/${username}` : "/profile"}
                        className={cn(
                          active ? 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-blue-700 rounded-lg mx-2 group'
                        )}
                      >
                        <UserCircleIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-blue-500" : "text-gray-500 group-hover:text-blue-500"
                        )} aria-hidden="true" />
                        Your Profile
                      </Link>
                    )}
                  </Menu.Item>

                  <div className="border-t border-gray-100 my-2 mx-2"></div>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/manage-subscription"
                        className={cn(
                          active ? 'bg-gradient-to-r from-green-50 to-green-100 text-green-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 hover:text-green-700 rounded-lg mx-2 group'
                        )}
                      >
                        <CreditCardIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-green-500" : "text-gray-500 group-hover:text-green-500"
                        )} aria-hidden="true" />
                        Manage Subscription
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/wallet"
                        className={cn(
                          active ? 'bg-gradient-to-r from-yellow-50 to-yellow-100 text-yellow-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-yellow-50 hover:to-yellow-100 hover:text-yellow-700 rounded-lg mx-2 group'
                        )}
                      >
                        <BanknotesIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-yellow-500" : "text-gray-500 group-hover:text-yellow-500"
                        )} aria-hidden="true" />
                        Wallet
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/pages/create"
                        className={cn(
                          active ? 'bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-purple-50 hover:to-purple-100 hover:text-purple-700 rounded-lg mx-2 group'
                        )}
                      >
                        <UsersIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-purple-500" : "text-gray-500 group-hover:text-purple-500"
                        )} aria-hidden="true" />
                        Create Fan Page
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/privacy"
                        className={cn(
                          active ? 'bg-gradient-to-r from-teal-50 to-teal-100 text-teal-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-teal-50 hover:to-teal-100 hover:text-teal-700 rounded-lg mx-2 group'
                        )}
                      >
                        <ShieldCheckIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-teal-500" : "text-gray-500 group-hover:text-teal-500"
                        )} aria-hidden="true" />
                        Privacy & Security
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/help"
                        className={cn(
                          active ? 'bg-gradient-to-r from-orange-50 to-orange-100 text-orange-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-100 hover:text-orange-700 rounded-lg mx-2 group'
                        )}
                      >
                        <QuestionMarkCircleIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-orange-500" : "text-gray-500 group-hover:text-orange-500"
                        )} aria-hidden="true" />
                        Help & Support
                      </Link>
                    )}
                  </Menu.Item>
                  <div className="border-t border-gray-100 my-2 mx-2"></div>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/settings"
                        className={cn(
                          active ? 'bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg mx-2 group'
                        )}
                      >
                        <Cog6ToothIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-gray-600" : "text-gray-500 group-hover:text-gray-600"
                        )} aria-hidden="true" />
                        Settings
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/keyboard-shortcuts"
                        className={cn(
                          active ? 'bg-gradient-to-r from-indigo-50 to-indigo-100 text-indigo-700' : 'text-gray-700',
                          'flex items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-indigo-100 hover:text-indigo-700 rounded-lg mx-2 group'
                        )}
                      >
                        <CommandLineIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-indigo-500" : "text-gray-500 group-hover:text-indigo-500"
                        )} aria-hidden="true" />
                        Keyboard Shortcuts
                      </Link>
                    )}
                  </Menu.Item>
                  <div className="border-t border-gray-100 my-2"></div>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => signOut()}
                        className={cn(
                          active ? 'bg-gradient-to-r from-red-50 to-red-100 text-red-700' : 'text-gray-700',
                          'flex w-full items-center px-4 py-3 text-sm transition-all duration-200 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 hover:text-red-700 rounded-lg mx-2 group'
                        )}
                      >
                        <ArrowRightOnRectangleIcon className={cn(
                          "mr-3 h-5 w-5 transition-colors duration-200",
                          active ? "text-red-500" : "text-gray-500 group-hover:text-red-500"
                        )} aria-hidden="true" />
                        Sign out
                      </button>
                    )}
                  </Menu.Item>
                  </div>
                </Menu.Items>
              </Transition>
            </Menu>
          ) : (
            <Link
              href="/login"
              className="inline-flex items-center gap-2 rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 px-4 py-2 text-sm font-semibold text-white shadow-lg hover:from-blue-700 hover:to-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-all duration-200 hover:shadow-xl hover:scale-105"
            >
              Log in
              <span aria-hidden="true" className="transition-transform duration-200 group-hover:translate-x-1">&rarr;</span>
            </Link>
          )}
        </div>
      </nav>
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300" onClick={() => setMobileMenuOpen(false)} />
          <div className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-gradient-to-b from-white to-blue-50/30 px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <Link href="/" className="-m-1.5 p-1.5 group">
                <Image
                  src="/logo.png"
                  alt="HIFNF Logo"
                  width={75}
                  height={25}
                  priority
                  className="h-7 w-auto transition-transform duration-200 group-hover:scale-105"
                />
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-xl p-2.5 text-gray-700 hover:text-red-600 hover:bg-red-50 transition-all duration-200 active:scale-95"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="sr-only">Close menu</span>
                <XMarkIcon className="h-6 w-6 transition-transform duration-200 hover:rotate-90" aria-hidden="true" />
              </button>
            </div>
            {/* Mobile search */}
            <div className="mb-6">
              <form onSubmit={handleSearch} className="w-full">
                <div className="relative group">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200" aria-hidden="true" />
                  </div>
                  <input
                    ref={(el) => !searchInputRef && setSearchInputRef(el)}
                    type="text"
                    className="block w-full rounded-xl border-0 bg-gradient-to-r from-gray-50 to-blue-50/30 py-3 pl-11 pr-4 text-gray-900 ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500 focus:bg-white sm:text-sm sm:leading-6 transition-all duration-200 shadow-sm hover:shadow-md focus:shadow-lg"
                    placeholder="🔍 Search people, groups, events..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-purple-500/0 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </form>
            </div>

            <div className="flow-root">
              <div className="-my-6 divide-y divide-gray-200/50">
                {/* Mobile Search Bar */}
                <div className="py-4">
                  <form onSubmit={handleSearch} className="w-full">
                    <div className="relative">
                      <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </div>
                      <input
                        ref={setSearchInputRef}
                        type="search"
                        name="search"
                        id="mobile-search"
                        className="block w-full rounded-xl border-0 bg-gray-50 py-3 pl-10 pr-3 text-gray-900 placeholder:text-gray-400 focus:bg-white focus:ring-2 focus:ring-blue-500 sm:text-sm"
                        placeholder="Search HIFNF..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                  </form>
                </div>
                {/* Mobile Navigation Grid */}
                <div className="py-4 sm:py-6">
                  <div className="grid grid-cols-4 gap-2 sm:gap-4">
                    <Link
                      href="/"
                      className={cn(
                        "flex flex-col items-center justify-center p-3 sm:p-4 rounded-xl transition-all duration-200 active:scale-95",
                        pathname === "/"
                          ? "text-blue-600 bg-gradient-to-br from-blue-50 to-blue-100 shadow-sm"
                          : "text-gray-700 hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:text-blue-600 hover:shadow-sm"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                      title="Home"
                    >
                      <HomeIcon className="h-6 w-6 sm:h-7 sm:w-7 mb-1 sm:mb-2 transition-transform duration-200 hover:scale-110" />
                      <span className="text-xs font-medium">Home</span>
                    </Link>

                    {/* Connections Link for Mobile */}
                    <Link
                      href="/connection"
                      className={cn(
                        "flex flex-col items-center justify-center p-3 sm:p-4 rounded-xl transition-all duration-200 active:scale-95",
                        pathname?.startsWith("/connection")
                          ? "text-purple-600 bg-gradient-to-br from-purple-50 to-purple-100 shadow-sm"
                          : "text-gray-700 hover:bg-gradient-to-br hover:from-purple-50 hover:to-purple-100 hover:text-purple-600 hover:shadow-sm"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                      title="Connections"
                    >
                      <UserGroupIcon className="h-6 w-6 sm:h-7 sm:w-7 mb-1 sm:mb-2 transition-transform duration-200 hover:scale-110" />
                      <span className="text-xs font-medium">Connections</span>
                    </Link>

                    {navigation.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "flex flex-col items-center justify-center p-3 sm:p-4 rounded-xl transition-all duration-200 active:scale-95",
                          pathname === item.href
                            ? "text-green-600 bg-gradient-to-br from-green-50 to-green-100 shadow-sm"
                            : "text-gray-700 hover:bg-gradient-to-br hover:from-green-50 hover:to-green-100 hover:text-green-600 hover:shadow-sm"
                        )}
                        onClick={() => setMobileMenuOpen(false)}
                        title={item.name}
                      >
                        <item.icon className="h-6 w-6 sm:h-7 sm:w-7 mb-1 sm:mb-2 transition-transform duration-200 hover:scale-110" />
                        <span className="text-xs font-medium">{item.name}</span>
                      </Link>
                    ))}
                    {/* Messages Link for Mobile */}
                    <Link
                      href="/messages"
                      className={cn(
                        "flex flex-col items-center justify-center p-3 sm:p-4 rounded-xl transition-all duration-200 active:scale-95",
                        pathname === "/messages"
                          ? "text-indigo-600 bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-sm"
                          : "text-gray-700 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-indigo-100 hover:text-indigo-600 hover:shadow-sm"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                      title="Messages"
                    >
                      <ChatBubbleLeftRightIcon className="h-6 w-6 sm:h-7 sm:w-7 mb-1 sm:mb-2 transition-transform duration-200 hover:scale-110" />
                      <span className="text-xs font-medium">Messages</span>
                    </Link>
                  </div>
                </div>
                {/* User Profile Section */}
                <div className="py-4 sm:py-6 border-t border-gray-200/50">
                  {session ? (
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 px-3 sm:px-4 py-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
                        <div className="relative">
                          {session.user.image ? (
                            <Image
                              src={session.user.image}
                              alt={session.user.name || "Profile"}
                              width={36}
                              height={36}
                              className="rounded-full ring-2 ring-blue-200"
                            />
                          ) : (
                            <UserCircleIcon className="h-9 w-9 text-gray-400" />
                          )}
                          <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-400 border-2 border-white rounded-full"></div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <span className="text-sm font-semibold text-gray-900 truncate block">
                            {session.user.name}
                          </span>
                          <p className="text-xs text-gray-500">Online</p>
                        </div>
                      </div>

                      <Link
                        href={username ? `/user/${username}` : "/profile"}
                        className="flex items-center rounded-xl px-4 py-3 text-base font-medium leading-7 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-blue-700 transition-all duration-200 group"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <UserCircleIcon className="mr-3 h-6 w-6 text-gray-500 group-hover:text-blue-500 transition-colors duration-200" />
                        Your Profile
                      </Link>

                      <Link
                        href="/manage-subscription"
                        className="flex items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <CreditCardIcon className="mr-3 h-6 w-6 text-gray-500" />
                        Manage Subscription
                      </Link>

                      <Link
                        href="/wallet"
                        className="flex items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <BanknotesIcon className="mr-3 h-6 w-6 text-gray-500" />
                        Wallet
                      </Link>

                      <Link
                        href="/settings"
                        className="flex items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <Cog6ToothIcon className="mr-3 h-6 w-6 text-gray-500" />
                        Settings
                      </Link>

                      <Link
                        href="/keyboard-shortcuts"
                        className="flex items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <CommandLineIcon className="mr-3 h-6 w-6 text-gray-500" />
                        Keyboard Shortcuts
                      </Link>

                      <button
                        onClick={() => signOut()}
                        className="flex w-full items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                      >
                        <ArrowRightOnRectangleIcon className="mr-3 h-6 w-6 text-gray-500" />
                        Sign out
                      </button>
                    </div>
                  ) : (
                    <Link
                      href="/login"
                      className="block w-full rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Log in
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
