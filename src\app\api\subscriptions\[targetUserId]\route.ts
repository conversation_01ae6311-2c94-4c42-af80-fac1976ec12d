import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptions, notifications } from "@/lib/db/schema";
import { and, eq } from "drizzle-orm";

interface RouteContext {
  params: Promise<{
    targetUserId: string;
  }>;
}

export async function GET(
  request: Request,
  context: RouteContext
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const targetUserId = params.targetUserId;

    // Check if current user is subscribed to target user
    const isSubscribed = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, session.user.id),
        eq(subscriptions.targetUserId, targetUserId)
      ),
    });

    // Check if target user is subscribed to current user (mutual subscription)
    const isSubscribedBack = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, targetUserId),
        eq(subscriptions.targetUserId, session.user.id)
      ),
    });

    return NextResponse.json({
      isSubscribed: !!isSubscribed,
      isSubscribedBack: !!isSubscribedBack,
      subscriptionId: isSubscribed?.id || null,
    });
  } catch (error) {
    console.error("Error getting subscription status:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: RouteContext
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const targetUserId = params.targetUserId;

    // Find the subscription
    const subscription = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, session.user.id),
        eq(subscriptions.targetUserId, targetUserId)
      ),
    });

    if (!subscription) {
      return NextResponse.json(
        { message: "Not subscribed to this user" },
        { status: 404 }
      );
    }

    // Delete the subscription
    await db.delete(subscriptions).where(eq(subscriptions.id, subscription.id));

    // Delete any related notifications
    await db
      .delete(notifications)
      .where(eq(notifications.subscriptionId, subscription.id));

    return NextResponse.json(
      { message: "Successfully unsubscribed" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error unsubscribing:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
