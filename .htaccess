RewriteEngine On

# If the request is not for a valid file or directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Proxy all requests to your Node.js server
RewriteRule ^(.*)$ http://127.0.0.1:3000/$1 [P,L]

# Set headers for proxy
<IfModule mod_headers.c>
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-Port "443"
</IfModule>

# Enable proxy module
<IfModule mod_proxy.c>
    ProxyRequests Off
    ProxyPreserveHost On
</IfModule>
