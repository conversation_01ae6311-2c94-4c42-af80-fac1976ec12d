"use client";

import { useState, useEffect } from "react";
import { getSupportedCurrencies, getCurrencySymbol, getCurrencySettings } from "@/lib/utils/currency";

interface CurrencySelectorProps {
  value: string;
  onChange: (currency: string) => void;
  className?: string;
  disabled?: boolean;
}

export function CurrencySelector({ 
  value, 
  onChange, 
  className = "",
  disabled = false 
}: CurrencySelectorProps) {
  const [currencies, setCurrencies] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadCurrencies() {
      try {
        const settings = await getCurrencySettings();
        const supportedCurrencies = getSupportedCurrencies(settings);
        setCurrencies(supportedCurrencies);
      } catch (error) {
        console.error("Error loading currencies:", error);
        // Fallback currencies
        setCurrencies(['USD', 'EUR', 'GBP', 'BDT', 'INR']);
      } finally {
        setIsLoading(false);
      }
    }

    loadCurrencies();
  }, []);

  if (isLoading) {
    return (
      <select className={`${className} opacity-50`} disabled>
        <option>Loading...</option>
      </select>
    );
  }

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={className}
      disabled={disabled}
    >
      {currencies.map((currency) => (
        <option key={currency} value={currency}>
          {getCurrencySymbol(currency)} {currency}
        </option>
      ))}
    </select>
  );
}

// Currency info component
interface CurrencyInfoProps {
  currencyCode: string;
  className?: string;
}

export function CurrencyInfo({ currencyCode, className = "" }: CurrencyInfoProps) {
  const symbol = getCurrencySymbol(currencyCode);
  
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <span className="text-lg font-medium">{symbol}</span>
      <span className="text-sm text-gray-600">{currencyCode}</span>
    </div>
  );
}
