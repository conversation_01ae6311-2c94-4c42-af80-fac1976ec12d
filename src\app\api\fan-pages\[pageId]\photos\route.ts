import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePosts, fanPages } from "@/lib/db/schema";
import { eq, and, desc, sql, isNotNull } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId]/photos - Get fan page photos
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = (page - 1) * limit;

    // Check if fan page exists
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    // Get posts with images
    const postsWithImages = await db
      .select({
        id: fanPagePosts.id,
        content: fanPagePosts.content,
        images: fanPagePosts.images,
        createdAt: fanPagePosts.createdAt,
        likeCount: fanPagePosts.likeCount,
        commentCount: fanPagePosts.commentCount,
        fanPage: {
          id: fanPages.id,
          ownerId: fanPages.ownerId,
          name: fanPages.name,
          username: fanPages.username,
          profileImage: fanPages.profileImage,
          isVerified: fanPages.isVerified,
        },
      })
      .from(fanPagePosts)
      .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
      .where(
        and(
          eq(fanPagePosts.fanPageId, pageId),
          eq(fanPagePosts.isPublished, true),
          isNotNull(fanPagePosts.images),
          sql`JSON_LENGTH(${fanPagePosts.images}) > 0`
        )
      )
      .orderBy(desc(fanPagePosts.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(fanPagePosts)
      .where(
        and(
          eq(fanPagePosts.fanPageId, pageId),
          eq(fanPagePosts.isPublished, true),
          isNotNull(fanPagePosts.images),
          sql`JSON_LENGTH(${fanPagePosts.images}) > 0`
        )
      );

    const total = totalResult[0]?.count || 0;

    // Extract individual photos with metadata
    const photos: Array<{
      id: string;
      url: string;
      postId: string;
      postContent: string;
      createdAt: string;
      likeCount: number;
      commentCount: number;
      fanPage: {
        id: string;
        name: string;
        username: string;
        profileImage: string | null;
        isVerified: boolean;
      };
    }> = [];

    postsWithImages.forEach(post => {
      if (post.images && Array.isArray(post.images)) {
        post.images.forEach((imageUrl: string, index: number) => {
          photos.push({
            id: `${post.id}-${index}`,
            url: imageUrl,
            postId: post.id,
            postContent: post.content || '',
            createdAt: post.createdAt,
            likeCount: post.likeCount,
            commentCount: post.commentCount,
            fanPage: {
              id: post.fanPage?.id || pageId,
              name: post.fanPage?.name || 'Unknown Page',
              username: post.fanPage?.username || 'unknown',
              profileImage: post.fanPage?.profileImage || null,
              isVerified: post.fanPage?.isVerified || false,
            },
          });
        });
      }
    });

    // Sort photos by creation date (newest first)
    photos.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({
      photos,
      posts: postsWithImages,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error("Error fetching fan page photos:", error);
    return NextResponse.json(
      { error: "Failed to fetch photos" },
      { status: 500 }
    );
  }
}
