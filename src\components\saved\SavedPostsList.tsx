"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { PostCard } from "@/components/feed/PostCard";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { BookmarkIcon, MagnifyingGlassIcon, FunnelIcon } from "@heroicons/react/24/outline";
import { Post } from "@/types/post";
import { useToast } from "@/contexts/ToastContext";

interface SavedPostsResponse {
  posts: Post[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

interface SavedPostsListProps {
  initialFilter?: string;
}

export function SavedPostsList({ initialFilter = "all" }: SavedPostsListProps) {
  const { data: session } = useSession();
  const { showError, showSuccess } = useToast();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(1);
  const [filter, setFilter] = useState(initialFilter);
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);

  const fetchSavedPosts = useCallback(async (pageNum: number = 1, reset: boolean = false) => {
    if (!session?.user) return;

    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await fetch(`/api/saved-posts?page=${pageNum}&limit=10`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch saved posts');
      }

      const data: SavedPostsResponse = await response.json();
      
      if (reset || pageNum === 1) {
        setPosts(data.posts);
      } else {
        setPosts(prev => [...prev, ...data.posts]);
      }
      
      setHasMore(data.pagination.hasMore);
      setPage(pageNum);
    } catch (error) {
      console.error('Error fetching saved posts:', error);
      showError('Error', 'Failed to load saved posts');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [session?.user, showError]);

  useEffect(() => {
    fetchSavedPosts(1, true);
  }, [fetchSavedPosts]);

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      fetchSavedPosts(page + 1);
    }
  };

  const handleFilterChange = (newFilter: string) => {
    setFilter(newFilter);
    // For now, we'll just change the active filter
    // In the future, you can implement actual filtering logic
  };

  // Handle unsave functionality
  const handleUnsave = useCallback(async (postId: string) => {
    try {
      // Find the post to determine its type
      const post = posts.find(p => p.id === postId);
      if (!post) {
        showError('Error', 'Post not found');
        return;
      }

      // Determine the correct API endpoint based on post type
      let apiUrl = '';

      if (post.type === 'fan_page_post') {
        apiUrl = `/api/fan-pages/posts/${postId}/bookmark`;
      } else if (post.type === 'blog_post') {
        // For blog posts, we need the slug instead of ID
        const blogSlug = post.blog?.slug;
        if (blogSlug) {
          apiUrl = `/api/blogs/${blogSlug}/save`;
        } else {
          showError('Error', 'Blog slug not found');
          return;
        }
      } else {
        // Regular user posts and group posts
        apiUrl = `/api/posts/${postId}/bookmark`;
      }

      const response = await fetch(apiUrl, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Remove the post from the local state
        setPosts(prev => prev.filter(post => post.id !== postId));
        showSuccess('Success', 'Item removed from saved items');
      } else {
        throw new Error('Failed to unsave item');
      }
    } catch (error) {
      console.error('Error unsaving item:', error);
      showError('Error', 'Failed to remove item from saved items');
    }
  }, [posts, showError, showSuccess]);

  // Remove duplicates and filter posts
  const uniquePosts = posts.filter((post, index, self) =>
    index === self.findIndex(p => p.id === post.id)
  );

  const filteredPosts = uniquePosts.filter(post => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesContent = post.content?.toLowerCase().includes(query);
      const matchesUser = post.user?.name?.toLowerCase().includes(query);
      const matchesUsername = post.user?.username?.toLowerCase().includes(query);

      if (!matchesContent && !matchesUser && !matchesUsername) {
        return false;
      }
    }

    // Type filter
    switch (filter) {
      case 'posts':
        return !post.images?.length && !post.videos?.length;
      case 'photos':
        return post.images?.length > 0;
      case 'videos':
        return post.videos?.length > 0;
      case 'links':
        // Check if content contains URLs
        return post.content?.includes('http') || false;
      default:
        return true;
    }
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (uniquePosts.length === 0) {
    return (
      <div className="space-y-6">
        {/* Enhanced Header with Search */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex flex-col space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search saved items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-3 w-full border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Toggle */}
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2"
              >
                <FunnelIcon className="h-4 w-4" />
                <span>Filters</span>
              </Button>
              <span className="text-sm text-gray-500">0 items</span>
            </div>

            {/* Filter Buttons */}
            {showFilters && (
              <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-100">
                <Button
                  variant={filter === 'all' ? 'primary' : 'outline'}
                  onClick={() => handleFilterChange('all')}
                  size="sm"
                >
                  All Items
                </Button>
                <Button
                  variant={filter === 'posts' ? 'primary' : 'outline'}
                  onClick={() => handleFilterChange('posts')}
                  size="sm"
                >
                  Posts
                </Button>
                <Button
                  variant={filter === 'links' ? 'primary' : 'outline'}
                  onClick={() => handleFilterChange('links')}
                  size="sm"
                >
                  Links
                </Button>
                <Button
                  variant={filter === 'photos' ? 'primary' : 'outline'}
                  onClick={() => handleFilterChange('photos')}
                  size="sm"
                >
                  Photos
                </Button>
                <Button
                  variant={filter === 'videos' ? 'primary' : 'outline'}
                  onClick={() => handleFilterChange('videos')}
                  size="sm"
                >
                  Videos
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Empty state */}
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-8 text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
              <BookmarkIcon className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              No saved items yet
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              When you save items, they'll appear here for easy access.
            </p>
            <div className="mt-6">
              <Button onClick={() => window.location.href = '/'}>
                Browse News Feed
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search saved items..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-3 w-full border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </Button>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                {filteredPosts.length} of {uniquePosts.length} items
              </span>
              {searchQuery && (
                <Button
                  variant="ghost"
                  onClick={() => setSearchQuery("")}
                  size="sm"
                  className="text-gray-500 hover:text-gray-700"
                >
                  Clear search
                </Button>
              )}
            </div>
          </div>

          {/* Filter Buttons */}
          {showFilters && (
            <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-100">
              <Button
                variant={filter === 'all' ? 'primary' : 'outline'}
                onClick={() => handleFilterChange('all')}
                size="sm"
              >
                All Items ({uniquePosts.length})
              </Button>
              <Button
                variant={filter === 'posts' ? 'primary' : 'outline'}
                onClick={() => handleFilterChange('posts')}
                size="sm"
              >
                Posts ({uniquePosts.filter(p => !p.images?.length && !p.videos?.length).length})
              </Button>
              <Button
                variant={filter === 'links' ? 'primary' : 'outline'}
                onClick={() => handleFilterChange('links')}
                size="sm"
              >
                Links ({uniquePosts.filter(p => p.content?.includes('http')).length})
              </Button>
              <Button
                variant={filter === 'photos' ? 'primary' : 'outline'}
                onClick={() => handleFilterChange('photos')}
                size="sm"
              >
                Photos ({uniquePosts.filter(p => p.images?.length > 0).length})
              </Button>
              <Button
                variant={filter === 'videos' ? 'primary' : 'outline'}
                onClick={() => handleFilterChange('videos')}
                size="sm"
              >
                Videos ({uniquePosts.filter(p => p.videos?.length > 0).length})
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Posts list or No Results */}
      {filteredPosts.length > 0 ? (
        <div className="space-y-3 sm:space-y-5">
          {filteredPosts.map((post, index) => (
            <div key={`saved-post-${post.id}-${index}`} className="content-block transform transition-all duration-300 hover:translate-y-[-2px]">
              <PostCard
                post={{
                  ...post,
                  // Since these are saved posts, they should always show as bookmarked
                  isBookmarked: true
                }}
                onLike={() => {}} // Saved posts don't need like functionality
                onDislike={() => {}} // Saved posts don't need dislike functionality
                variant="saved" // Add a variant to indicate this is a saved post
                showActions={true}
                onUnsave={() => handleUnsave(post.id)} // Add unsave functionality
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
            <MagnifyingGlassIcon className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            {searchQuery ? 'No results found' : 'No saved items match your filters'}
          </h3>
          <p className="mt-2 text-sm text-gray-500">
            {searchQuery
              ? `No saved items match "${searchQuery}". Try a different search term.`
              : 'Try adjusting your filters to see more saved items.'
            }
          </p>
          {(searchQuery || filter !== 'all') && (
            <div className="mt-6 flex justify-center space-x-3">
              {searchQuery && (
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                >
                  Clear search
                </Button>
              )}
              {filter !== 'all' && (
                <Button
                  variant="outline"
                  onClick={() => handleFilterChange('all')}
                >
                  Show all items
                </Button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Load more button */}
      {hasMore && (
        <div className="flex justify-center py-6">
          <Button
            onClick={handleLoadMore}
            disabled={loadingMore}
            variant="outline"
          >
            {loadingMore ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
