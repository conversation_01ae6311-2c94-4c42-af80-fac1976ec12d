"use client";

import { cn } from "@/lib/utils";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowRightIcon,
  PlusIcon,
  BanknotesIcon,
  UserIcon
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import Image from "next/image";

interface Transaction {
  id: string;
  type: 'deposit' | 'send' | 'receive' | 'cashout' | 'internal_transfer' | 'earning' | 'withdraw';
  amount: string;
  fee: string;
  netAmount: string;
  walletType: 'general' | 'earning';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  paymentGateway?: string;
  reference?: string;
  note?: string;
  createdAt: string;
  toUser?: {
    id: string;
    name: string;
    username: string;
    image?: string;
  };
  toAgent?: {
    id: string;
    name: string;
    serviceType: string;
  };
  fromWalletType?: 'general' | 'earning';
  toWalletType?: 'general' | 'earning';
}

interface TransactionCardProps {
  transaction: Transaction;
  className?: string;
}

export function TransactionCard({ transaction, className }: TransactionCardProps) {
  const getTransactionIcon = () => {
    switch (transaction.type) {
      case 'deposit':
        return <PlusIcon className="h-5 w-5 text-green-600" />;
      case 'send':
        return <ArrowUpIcon className="h-5 w-5 text-red-600" />;
      case 'receive':
        return <ArrowDownIcon className="h-5 w-5 text-green-600" />;
      case 'cashout':
        return <BanknotesIcon className="h-5 w-5 text-orange-600" />;
      case 'internal_transfer':
        return <ArrowRightIcon className="h-5 w-5 text-blue-600" />;
      case 'earning':
        return <PlusIcon className="h-5 w-5 text-green-600" />;
      case 'withdraw':
        return <ArrowUpIcon className="h-5 w-5 text-red-600" />;
      default:
        return <BanknotesIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTransactionTitle = () => {
    switch (transaction.type) {
      case 'deposit':
        return `Deposit via ${transaction.paymentGateway || 'Payment Gateway'}`;
      case 'send':
        return `Sent to ${transaction.toUser?.name || 'User'}`;
      case 'receive':
        return `Received from ${transaction.toUser?.name || 'User'}`;
      case 'cashout':
        return `Cashout via ${transaction.toAgent?.name || 'Agent'}`;
      case 'internal_transfer':
        return `Transfer: ${transaction.fromWalletType} → ${transaction.toWalletType}`;
      case 'earning':
        return 'Earning Credit';
      case 'withdraw':
        return 'Withdrawal';
      default:
        return 'Transaction';
    }
  };

  const getStatusColor = () => {
    switch (transaction.status) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'processing':
        return 'text-blue-600 bg-blue-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
      case 'cancelled':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getAmountDisplay = () => {
    const isDebit = ['send', 'cashout', 'withdraw'].includes(transaction.type);
    const amount = parseFloat(transaction.amount);
    const fee = parseFloat(transaction.fee);

    return {
      isDebit,
      amount: amount.toFixed(2),
      fee: fee > 0 ? fee.toFixed(2) : null,
      netAmount: parseFloat(transaction.netAmount).toFixed(2),
    };
  };

  const { isDebit, amount, fee, netAmount } = getAmountDisplay();

  return (
    <div className={cn(
      "bg-white border border-gray-200 rounded-lg p-3 sm:p-4 hover:shadow-md transition-shadow",
      className
    )}>
      <div className="flex items-start sm:items-center justify-between gap-3">
        <div className="flex items-start sm:items-center space-x-3 min-w-0 flex-1">
          <div className="flex-shrink-0 mt-0.5 sm:mt-0">
            {getTransactionIcon()}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {getTransactionTitle()}
              </h4>
              {transaction.toUser && (
                <div className="flex items-center space-x-1">
                  {transaction.toUser.image ? (
                    <Image
                      src={transaction.toUser.image}
                      alt={transaction.toUser.name}
                      width={16}
                      height={16}
                      className="rounded-full"
                    />
                  ) : (
                    <UserIcon className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mt-1">
              <span className={cn(
                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium w-fit",
                getStatusColor()
              )}>
                {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
              </span>

              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                <span className="text-xs text-gray-500">
                  {format(new Date(transaction.createdAt), 'MMM dd, yyyy HH:mm')}
                </span>

                <span className="text-xs text-gray-500 capitalize">
                  {transaction.walletType} wallet
                </span>
              </div>
            </div>

            {transaction.note && (
              <p className="text-xs text-gray-600 mt-1 truncate">
                {transaction.note}
              </p>
            )}

            {transaction.reference && (
              <p className="text-xs text-gray-500 mt-1">
                Ref: {transaction.reference}
              </p>
            )}
          </div>
        </div>

        <div className="text-right flex-shrink-0">
          <div className={cn(
            "text-base sm:text-lg font-semibold",
            isDebit ? "text-red-600" : "text-green-600"
          )}>
            {isDebit ? '-' : '+'}${parseFloat(amount).toFixed(2)}
          </div>

          {fee && (
            <div className="text-xs text-gray-500">
              Fee: ${parseFloat(fee).toFixed(2)}
            </div>
          )}

          {fee && (
            <div className="text-xs sm:text-sm text-gray-700">
              Net: ${parseFloat(netAmount).toFixed(2)}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface TransactionListProps {
  transactions: Transaction[];
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
}

export function TransactionList({
  transactions,
  loading = false,
  emptyMessage = "No transactions found",
  className
}: TransactionListProps) {
  if (loading) {
    return (
      <div className={cn("space-y-3 sm:space-y-4", className)}>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="h-5 w-5 bg-gray-300 rounded" />
              <div className="flex-1">
                <div className="h-4 bg-gray-300 rounded w-1/2 mb-2" />
                <div className="h-3 bg-gray-300 rounded w-1/4" />
              </div>
              <div className="h-6 bg-gray-300 rounded w-16" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className={cn(
        "text-center py-8 sm:py-12 bg-white border border-gray-200 rounded-lg",
        className
      )}>
        <BanknotesIcon className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
        <p className="text-sm sm:text-base text-gray-500 px-4">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2 sm:space-y-3", className)}>
      {transactions.map((transaction) => (
        <TransactionCard key={transaction.id} transaction={transaction} />
      ))}
    </div>
  );
}
