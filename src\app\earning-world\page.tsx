"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import {
  GlobeAltIcon,
  PlusIcon,
  BanknotesIcon,
  UserGroupIcon,
  PencilIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";
import { ReferralSection } from "@/components/earning-world/ReferralSection";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useWallet } from "@/contexts/WalletContext";
import { Badge } from "@/components/ui/Badge";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";

export const dynamic = 'force-dynamic';

// Global helper function for safe number formatting
const safeToFixed = (value: any, decimals: number = 2) => {
  if (value === null || value === undefined) return "0.00";
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return isNaN(numValue) ? "0.00" : numValue.toFixed(decimals);
};

function EarningOpportunityCard({ opportunity }: any) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'coming_soon': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 hover:shadow-lg transition-all duration-300 hover:border-blue-300">
      <div className="flex items-start justify-between mb-3 sm:mb-4">
        <div className={`w-10 h-10 sm:w-12 sm:h-12 ${opportunity.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
          <opportunity.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
        </div>
        <div className="flex flex-col sm:flex-row gap-1 sm:gap-2 ml-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getDifficultyColor(opportunity.difficulty)} text-center`}>
            {opportunity.difficulty}
          </span>
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(opportunity.status)} text-center`}>
            {opportunity.status === 'coming_soon' ? 'Coming Soon' : 'Active'}
          </span>
        </div>
      </div>

      <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">
        {opportunity.title}
      </h3>

      <p className="text-gray-600 text-sm mb-3 sm:mb-4 line-clamp-2 sm:h-10 overflow-hidden">
        {opportunity.description}
      </p>

      <div className="space-y-2 mb-3 sm:mb-4">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Earning:</span>
          <span className="font-semibold text-green-600">{opportunity.earning}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Time Required:</span>
          <span className="text-gray-900">{opportunity.timeRequired}</span>
        </div>
      </div>

      <Button
        className="w-full text-sm sm:text-base"
        disabled={opportunity.status === 'coming_soon'}
        variant={opportunity.status === 'coming_soon' ? 'outline' : 'default'}
      >
        {opportunity.status === 'coming_soon' ? 'Coming Soon' : 'Start Earning'}
      </Button>
    </div>
  );
}

function EarningWorldContent() {
  const [activeTab, setActiveTab] = useState('overview');
  const { balance, loading: walletLoading } = useWallet();

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'opportunities', name: 'Opportunities', icon: GlobeAltIcon },
    { id: 'blog-monetization', name: 'Blog Monetization', icon: PencilIcon },
    { id: 'referrals', name: 'Refer & Earn', icon: UserGroupIcon },
    { id: 'commissions', name: 'Commission History', icon: CurrencyDollarIcon },
  ];

  const earningOpportunities = [
    {
      id: 1,
      title: "Refer & Earn",
      description: "Invite friends and earn $5 for each successful referral",
      icon: UserGroupIcon,
      color: "bg-blue-500",
      earning: "$5 per referral",
      difficulty: "Easy",
      timeRequired: "5 minutes",
      status: "active"
    },
    {
      id: 2,
      title: "Blog Monetization",
      description: "Write quality blog posts and earn money based on reads",
      icon: PencilIcon,
      color: "bg-green-500",
      earning: "$1 per 1000 reads",
      difficulty: "Medium",
      timeRequired: "2-4 hours",
      status: "active"
    },
    {
      id: 3,
      title: "Content Creation",
      description: "Create engaging content for our platform",
      icon: StarIcon,
      color: "bg-purple-500",
      earning: "$10-50 per content",
      difficulty: "Medium",
      timeRequired: "1-3 hours",
      status: "coming_soon"
    },
    {
      id: 4,
      title: "Community Moderation",
      description: "Help maintain community standards and guidelines",
      icon: CheckCircleIcon,
      color: "bg-orange-500",
      earning: "$15 per hour",
      difficulty: "Easy",
      timeRequired: "Flexible",
      status: "coming_soon"
    },
    {
      id: 5,
      title: "Survey Participation",
      description: "Complete surveys and market research studies",
      icon: ClockIcon,
      color: "bg-indigo-500",
      earning: "$2-10 per survey",
      difficulty: "Easy",
      timeRequired: "10-30 minutes",
      status: "coming_soon"
    },
    {
      id: 6,
      title: "Affiliate Marketing",
      description: "Promote products and earn commission on sales",
      icon: ArrowTrendingUpIcon,
      color: "bg-red-500",
      earning: "5-20% commission",
      difficulty: "Hard",
      timeRequired: "Ongoing",
      status: "coming_soon"
    }
  ];

  return (
    <div className="mx-auto max-w-7xl px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
      {/* Header */}
      <div className="mb-4 sm:mb-6 lg:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">
          Earning World
        </h1>
        <p className="text-sm sm:text-base text-gray-600">
          Discover various ways to earn money and grow your income
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-4 sm:mb-6 lg:mb-8">
        <nav className="-mb-px flex overflow-x-auto scrollbar-hide" aria-label="Tabs">
          <div className="flex space-x-4 sm:space-x-8 min-w-max px-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-xs sm:text-sm flex items-center flex-shrink-0`}
              >
                <tab.icon className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{tab.name}</span>
                <span className="sm:hidden">
                  {tab.name.split(' ')[0]}
                </span>
              </button>
            ))}
          </div>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <OverviewTab
          balance={balance}
          walletLoading={walletLoading}
        />
      )}

      {activeTab === 'opportunities' && (
        <OpportunitiesTab earningOpportunities={earningOpportunities} />
      )}

      {activeTab === 'blog-monetization' && (
        <BlogMonetizationTab />
      )}

      {activeTab === 'referrals' && (
        <ReferralSection />
      )}

      {activeTab === 'commissions' && (
        <CommissionHistoryTab />
      )}
    </div>
  );
}

function OverviewTab({ balance, walletLoading }: any) {
  const router = useRouter();
  const [overviewData, setOverviewData] = useState({
    totalEarnings: 0,
    pendingPayouts: 0,
    availableBalance: 0,
    blogEarnings: 0,
    referralEarnings: 0,
    subscriptionCommissions: 0,
    totalBlogs: 0,
    monetizedBlogs: 0,
    totalReferrals: 0,
    activeReferrals: 0,
    currency: "USD",
  });

  const [recentTransactions, setRecentTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const formatBalance = (amount: string | number | undefined) => {
    if (!amount) return "0.00";
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return isNaN(numAmount) ? "0.00" : numAmount.toFixed(2);
  };

  // Fetch earning overview data
  const fetchOverviewData = async () => {
    try {
      setLoading(true);

      // Fetch comprehensive earning overview data
      const overviewResponse = await fetch('/api/earning/overview');
      if (overviewResponse.ok) {
        const overviewResult = await overviewResponse.json();
        if (overviewResult.success && overviewResult.data) {
          setOverviewData(overviewResult.data.overview);
          setRecentTransactions(overviewResult.data.recentTransactions || []);
        } else {
          // Fallback if API returns success: false
          await fetchOverviewDataFallback();
        }
      } else {
        // Fallback to individual API calls if overview endpoint fails
        await fetchOverviewDataFallback();
      }

    } catch (error) {
      console.error('Error fetching overview data:', error);
      // Try fallback method
      await fetchOverviewDataFallback();
    } finally {
      setLoading(false);
    }
  };

  // Fallback method for fetching data from individual endpoints
  const fetchOverviewDataFallback = async () => {
    try {
      // Fetch wallet balance
      const walletResponse = await fetch('/api/wallet/balance');
      let walletData = null;
      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        walletData = walletResult.data;
      }

      // Fetch blog stats
      const blogResponse = await fetch('/api/author/blog-stats');
      let blogData = null;
      if (blogResponse.ok) {
        const blogResult = await blogResponse.json();
        blogData = blogResult;
      }

      // Fetch earning transactions (recent)
      const transactionsResponse = await fetch('/api/wallet/transactions?type=earning&limit=10&page=1');
      let transactionsData = [];
      if (transactionsResponse.ok) {
        const transactionsResult = await transactionsResponse.json();
        transactionsData = transactionsResult.data?.transactions || [];
      }

      // Fetch pending payouts (withdraw/cashout requests)
      const pendingPayoutsResponse = await fetch('/api/wallet/pending-payouts');
      let pendingPayoutsAmount = 0;
      if (pendingPayoutsResponse.ok) {
        const pendingPayoutsResult = await pendingPayoutsResponse.json();
        pendingPayoutsAmount = pendingPayoutsResult.data?.totalAmount || 0;
      }

      // Set overview data using fallback API responses
      setOverviewData({
        totalEarnings: walletData?.earningBalance || 0,
        pendingPayouts: pendingPayoutsAmount || 0,
        availableBalance: walletData?.earningBalance || 0,
        blogEarnings: blogData?.totalEarnings || 0,
        referralEarnings: 0, // Will be calculated from referral API if available
        subscriptionCommissions: 0, // Will be calculated from subscription API if available
        totalBlogs: blogData?.totalBlogs || 0,
        monetizedBlogs: blogData?.monetizedBlogs || 0,
        totalReferrals: 0, // Will be fetched from referral API if available
        activeReferrals: 0, // Will be fetched from referral API if available
        currency: "USD",
      });

      // Format recent transactions
      const formattedTransactions = transactionsData.map((transaction: any) => {
        const amount = parseFloat(transaction.amount || '0');
        return {
          id: transaction.id,
          type: amount > 0 ? 'credit' : 'debit',
          amount: Math.abs(amount),
          description: transaction.description || getTransactionDescription(transaction.type, transaction.source),
          date: transaction.createdAt ? new Date(transaction.createdAt).toLocaleDateString() : 'N/A',
          status: transaction.status || 'unknown',
        };
      });

      setRecentTransactions(formattedTransactions);

    } catch (error) {
      console.error('Error in fallback data fetch:', error);
    }
  };

  // Get transaction description based on type and source
  const getTransactionDescription = (type: string, source?: string) => {
    switch (type) {
      case 'earning':
        return 'Earning transaction';
      case 'withdraw':
        return 'Withdrawal to bank account';
      case 'internal_transfer':
        return 'Transfer to general wallet';
      case 'deposit':
        return 'Wallet deposit';
      case 'send':
        return 'Money sent';
      case 'receive':
        return 'Money received';
      default:
        return 'Wallet transaction';
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchOverviewData();
    setRefreshing(false);
  };

  const handleWithdrawFunds = () => {
    // Redirect to wallet page with withdraw modal parameter
    router.push('/wallet?action=withdraw');
  };

  useEffect(() => {
    fetchOverviewData();
  }, []);

  return (
    <div className="space-y-4 sm:space-y-6 lg:space-y-8">
      {/* Earning Wallet Balance */}
      <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl p-4 sm:p-6 lg:p-8 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex-1">
            <h2 className="text-base sm:text-lg font-medium text-green-100 mb-2">Earning Wallet Balance</h2>
            <div className="flex items-baseline">
              <span className="text-2xl sm:text-3xl lg:text-4xl font-bold">
                ${loading || walletLoading ? "..." : formatBalance(overviewData.availableBalance)}
              </span>
              <span className="text-green-100 ml-2 text-sm sm:text-base">{overviewData.currency}</span>
            </div>
            <p className="text-green-100 mt-1 sm:mt-2 text-sm sm:text-base">Available for withdrawal</p>
          </div>
          <div className="text-right hidden sm:block">
            <BanknotesIcon className="h-12 w-12 sm:h-16 sm:w-16 text-green-200 opacity-50" />
          </div>
        </div>
        <div className="mt-4 sm:mt-6 flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Button
            className="bg-white text-green-600 hover:bg-green-50 w-full sm:w-auto"
            onClick={handleWithdrawFunds}
          >
            <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Withdraw Funds</span>
            <span className="sm:hidden">Withdraw</span>
          </Button>
          <Button
            className="bg-green-300 text-white hover:bg-green-200 w-full sm:w-auto"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <ArrowTrendingUpIcon className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Earnings Summary */}
      {loading ? (
        <div className="grid grid-cols-1 gap-3 sm:gap-4 sm:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white overflow-hidden rounded-lg border border-gray-200 p-3 sm:p-4 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-16"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-3 sm:gap-4 sm:grid-cols-3">
          <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-3 sm:p-4">
            <dt className="text-xs sm:text-sm font-medium text-gray-500 truncate">Total Earnings</dt>
            <dd className="mt-1 text-lg sm:text-xl font-semibold text-gray-900">
              ${safeToFixed(overviewData.totalEarnings)}
            </dd>
          </div>

          <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-3 sm:p-4">
            <dt className="text-xs sm:text-sm font-medium text-gray-500 truncate">Pending Payouts</dt>
            <dd className="mt-1 text-lg sm:text-xl font-semibold text-gray-900">
              ${safeToFixed(overviewData.pendingPayouts)}
            </dd>
          </div>

          <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-3 sm:p-4">
            <dt className="text-xs sm:text-sm font-medium text-gray-500 truncate">Available Balance</dt>
            <dd className="mt-1 text-lg sm:text-xl font-semibold text-gray-900">
              ${safeToFixed(overviewData.availableBalance)}
            </dd>
          </div>
        </div>
      )}





      {/* Recent Transactions */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-3 sm:p-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0">
            <h4 className="text-sm sm:text-base font-medium text-gray-900">Recent Transactions</h4>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="w-full sm:w-auto text-xs sm:text-sm"
            >
              {refreshing ? (
                <ArrowTrendingUpIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin" />
              ) : (
                <ArrowTrendingUpIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        <div>
          {loading ? (
            <div className="p-6 sm:p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
              <p className="mt-4 text-sm sm:text-base text-gray-600">Loading transactions...</p>
            </div>
          ) : recentTransactions.length === 0 ? (
            <div className="p-6 sm:p-8 text-center">
              <BanknotesIcon className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
              <p className="text-sm sm:text-base text-gray-600">Your earning transactions will appear here</p>
            </div>
          ) : (
            <>
              {/* Mobile Card Layout */}
              <div className="block sm:hidden">
                <div className="divide-y divide-gray-200">
                  {recentTransactions.map((transaction: any) => (
                    <div key={transaction.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 flex-1">
                          <div className="flex-shrink-0 mt-0.5">
                            {transaction.type === 'credit' ? (
                              <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <ArrowTrendingUpIcon className="h-4 w-4 text-red-500 rotate-180" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {transaction.description}
                              </p>
                              <span className={`text-sm font-semibold ${
                                transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {transaction.type === 'credit' ? '+' : '-'}
                                ${safeToFixed(transaction.amount)}
                              </span>
                            </div>
                            <div className="flex items-center justify-between mt-1">
                              <p className="text-xs text-gray-500 capitalize">
                                {transaction.type} • {transaction.date}
                              </p>
                              <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                                transaction.status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  : transaction.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {transaction.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Desktop Table Layout */}
              <div className="hidden sm:block overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentTransactions.map((transaction: any) => (
                      <tr key={transaction.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <div className="flex items-center">
                            {transaction.type === 'credit' ? (
                              <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-2" />
                            ) : (
                              <ArrowTrendingUpIcon className="h-4 w-4 text-red-500 mr-2 rotate-180" />
                            )}
                            <span className="capitalize">{transaction.type}</span>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {transaction.description}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {transaction.date}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                          <span className={`font-medium ${
                            transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.type === 'credit' ? '+' : '-'}
                            ${safeToFixed(transaction.amount)}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                          <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                            transaction.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : transaction.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {transaction.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>

        {!loading && recentTransactions.length > 0 && (
          <div className="p-4 border-t border-gray-200 flex justify-center">
            <Button variant="outline" size="sm">
              View All Transactions
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

function BlogMonetizationTab() {
  const [blogStats, setBlogStats] = useState({
    totalBlogs: 0,
    publishedBlogs: 0,
    draftBlogs: 0,
    totalViews: 0,
    monetizedBlogs: 0,
    pendingMonetization: 0,
    totalEarnings: "0.00",
  });

  const [monetizationConfig, setMonetizationConfig] = useState({
    isEnabled: false,
    cprRate: 1.00,
    minPayoutThreshold: 10.00,
    minReadDuration: 120,
  });

  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch blog monetization data
  const fetchBlogData = async () => {
    try {
      setLoading(true);

      // Fetch comprehensive blog monetization data
      const blogsResponse = await fetch('/api/author/blogs-monetization?limit=50');
      if (blogsResponse.ok) {
        const blogsData = await blogsResponse.json();
        if (blogsData.success && blogsData.data) {
          setBlogStats(blogsData.data.stats || {});
          setBlogPosts(blogsData.data.blogs || []);
        } else {
          // Fallback if API returns success: false
          await fetchBlogDataFallback();
        }
      } else {
        // Fallback to individual API calls
        await fetchBlogDataFallback();
      }

      // Fetch monetization config
      const configResponse = await fetch('/api/monetization/status');
      if (configResponse.ok) {
        const configData = await configResponse.json();
        if (configData.success) {
          setMonetizationConfig({
            isEnabled: configData.data.isEnabled,
            cprRate: configData.data.cprRate,
            minPayoutThreshold: configData.data.minPayoutThreshold,
            minReadDuration: configData.data.minReadDuration,
          });
        }
      }

    } catch (error) {
      console.error('Error fetching blog data:', error);
      // Try fallback method
      await fetchBlogDataFallback();
    } finally {
      setLoading(false);
    }
  };

  // Fallback method for fetching data from individual endpoints
  const fetchBlogDataFallback = async () => {
    try {
      // Fetch blog stats from author API
      const statsResponse = await fetch('/api/author/blog-stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setBlogStats({
          totalBlogs: statsData.totalBlogs || 0,
          publishedBlogs: statsData.publishedBlogs || 0,
          draftBlogs: statsData.draftBlogs || 0,
          totalViews: statsData.totalViews || 0,
          monetizedBlogs: statsData.monetizedBlogs || 0,
          pendingMonetization: statsData.pendingMonetization || 0,
          totalEarnings: statsData.totalEarnings || "0.00",
        });

        // Set blog posts from recent blogs
        setBlogPosts(statsData.recentBlogs || []);
      }
    } catch (error) {
      console.error('Error in fallback blog data fetch:', error);
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchBlogData();
    setRefreshing(false);
  };

  // Request monetization for a blog
  const requestMonetization = async (blogSlug: string) => {
    try {
      const response = await fetch(`/api/blogs/${blogSlug}/monetization`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Refresh data after successful request
        await fetchBlogData();
        console.log('Monetization requested successfully');
      } else {
        console.error('Failed to request monetization:', result.message);
      }
    } catch (error) {
      console.error('Error requesting monetization:', error);
    }
  };

  // Navigate to blog creation
  const handleCreateBlog = () => {
    window.location.href = '/blogs/create';
  };

  // Navigate to blog analytics
  const handleViewAnalytics = () => {
    window.location.href = '/blogs/analytics';
  };

  // Navigate to specific blog
  const handleViewBlog = (blogSlug: string) => {
    window.location.href = `/blogs/${blogSlug}`;
  };

  // Navigate to edit blog
  const handleEditBlog = (blogSlug: string) => {
    window.location.href = `/blogs/${blogSlug}/edit`;
  };

  useEffect(() => {
    fetchBlogData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'monetized': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getApprovalStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'not_requested': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 lg:space-y-8">
      {/* Blog Monetization Overview */}
      <div className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl p-4 sm:p-6 lg:p-8 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex-1">
            <h2 className="text-base sm:text-lg font-medium text-purple-100 mb-2">Blog Monetization</h2>
            <div className="flex items-baseline">
              <span className="text-2xl sm:text-3xl lg:text-4xl font-bold">
                ${loading ? "..." : safeToFixed(parseFloat(blogStats.totalEarnings || "0"))}
              </span>
              <span className="text-purple-100 ml-2 text-sm sm:text-base">Total Earned</span>
            </div>
            <div className="text-purple-100 mt-2 text-sm sm:text-base">
              {loading ? "Loading..." : (
                <div className="space-y-1">
                  <p>
                    ${safeToFixed(monetizationConfig.cprRate)} per 1000 reads
                  </p>
                  <p>
                    Min payout: ${safeToFixed(monetizationConfig.minPayoutThreshold)}
                  </p>
                  {!monetizationConfig.isEnabled && (
                    <span className="block text-yellow-200 text-sm mt-1">⚠️ Monetization is currently disabled</span>
                  )}
                </div>
              )}
            </div>
          </div>
          <div className="text-right hidden sm:block">
            <PencilIcon className="h-12 w-12 sm:h-16 sm:w-16 text-purple-200 opacity-50" />
          </div>
        </div>
        <div className="mt-4 sm:mt-6 flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Button
            className="bg-white text-purple-600 hover:bg-purple-50 w-full sm:w-auto"
            onClick={handleCreateBlog}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Write New Blog</span>
            <span className="sm:hidden">New Blog</span>
          </Button>
          <Button
            className="bg-purple-400 text-white hover:bg-purple-300 w-full sm:w-auto"
            onClick={handleViewAnalytics}
          >
            <ChartBarIcon className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">View Analytics</span>
            <span className="sm:hidden">Analytics</span>
          </Button>
          <Button
            className="bg-purple-300 text-white hover:bg-purple-200 w-full sm:w-auto"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <ArrowTrendingUpIcon className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Blog Stats */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div className="ml-4 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <PencilIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Blogs</p>
                <p className="text-2xl font-bold text-gray-900">{blogStats.totalBlogs}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Monetized</p>
                <p className="text-2xl font-bold text-gray-900">{blogStats.monetizedBlogs}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <StarIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">{blogStats.totalViews.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <ClockIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Approval</p>
                <p className="text-2xl font-bold text-gray-900">{blogStats.pendingMonetization}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Blog Posts Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-medium text-gray-900">My Blog Posts</h4>
            <div className="flex space-x-2">
              <Button size="sm" variant="outline" onClick={handleRefresh} disabled={refreshing}>
                {refreshing ? (
                  <ArrowTrendingUpIcon className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
              <Button size="sm" onClick={handleCreateBlog}>
                <PlusIcon className="h-4 w-4 mr-2" />
                Write New Blog
              </Button>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading blog posts...</p>
            </div>
          ) : blogPosts.length === 0 ? (
            <div className="p-8 text-center">
              <PencilIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No blog posts yet</h3>
              <p className="text-gray-600 mb-4">Start writing your first blog post to begin earning!</p>
              <Button onClick={handleCreateBlog}>
                <PlusIcon className="h-4 w-4 mr-2" />
                Write Your First Blog
              </Button>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Blog Title
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reads
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Earnings
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Published
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {blogPosts.map((blog: any) => {
                  const monetizationStatus = blog.monetization?.status || 'not_requested';
                  const totalEarnings = blog.monetization?.totalEarnings || "0.00";

                  return (
                    <tr key={blog.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div>
                          <p className="text-sm font-medium text-gray-900 hover:text-purple-600 cursor-pointer"
                             onClick={() => handleViewBlog(blog.slug)}>
                            {blog.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            Status: <span className="font-medium">{blog.status}</span>
                          </p>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getApprovalStatusColor(monetizationStatus)}`}>
                          {monetizationStatus === 'approved' ? 'Approved' :
                           monetizationStatus === 'pending' ? 'Pending' :
                           monetizationStatus === 'rejected' ? 'Rejected' : 'Not Requested'}
                        </span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                        {blog.viewCount?.toLocaleString() || '0'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-green-600">
                        ${parseFloat(totalEarnings).toFixed(2)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {new Date(blog.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-right text-sm space-x-2">
                        <Button variant="outline" size="sm" onClick={() => handleViewBlog(blog.slug)}>
                          View
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleEditBlog(blog.slug)}>
                          Edit
                        </Button>
                        {monetizationConfig.isEnabled && blog.status === 'published' &&
                         (monetizationStatus === 'not_requested' || monetizationStatus === 'rejected') && (
                          <Button
                            size="sm"
                            onClick={() => requestMonetization(blog.slug)}
                            className="bg-purple-600 hover:bg-purple-700 text-white"
                          >
                            Request Monetization
                          </Button>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>

        {!loading && blogPosts.length > 0 && (
          <div className="p-4 border-t border-gray-200 flex justify-center">
            <Button variant="outline" size="sm" onClick={() => window.location.href = '/blogs/my-blogs'}>
              View All Blog Posts
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

function OpportunitiesTab({ earningOpportunities }: any) {
  const [filter, setFilter] = useState('all');

  const filteredOpportunities = earningOpportunities.filter((opp: any) => {
    if (filter === 'all') return true;
    if (filter === 'active') return opp.status === 'active';
    if (filter === 'coming_soon') return opp.status === 'coming_soon';
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1 max-w-md">
          <Input
            type="search"
            placeholder="Search opportunities..."
            className="w-full"
          />
        </div>
        <div className="flex space-x-2">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            All
          </Button>
          <Button
            variant={filter === 'active' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('active')}
          >
            Active
          </Button>
          <Button
            variant={filter === 'coming_soon' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('coming_soon')}
          >
            Coming Soon
          </Button>
        </div>
      </div>

      {/* Opportunities Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {filteredOpportunities.map((opportunity: any) => (
          <EarningOpportunityCard key={opportunity.id} opportunity={opportunity} />
        ))}
      </div>
    </div>
  );
}

export default function EarningWorldPage() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="mt-4 text-gray-600">Loading...</p>
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4 text-center">
        <h1 className="mb-4 text-2xl font-bold text-red-600">Authentication Required</h1>
        <p className="mb-6 max-w-md text-gray-600">
          Please log in to access this page.
        </p>
        <div className="rounded-lg bg-white p-6 shadow">
          <a href="/login" className="text-blue-600 hover:underline">Go to Login</a>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <EarningWorldContent />
    </MainLayout>
  );
}

// Commission History Tab Component
function CommissionHistoryTab() {
  const { data: session } = useSession();
  const [commissions, setCommissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);

  useEffect(() => {
    if (session?.user?.id) {
      fetchCommissionData();
    }
  }, [session]);

  const fetchCommissionData = async () => {
    try {
      const [commissionsResponse, statsResponse] = await Promise.all([
        fetch('/api/user/commissions'),
        fetch('/api/user/commission-stats')
      ]);

      if (commissionsResponse.ok) {
        const commissionsData = await commissionsResponse.json();
        setCommissions(commissionsData.data || []);
      }

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }
    } catch (error) {
      console.error('Error fetching commission data:', error);
      toast.error('Failed to load commission data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-blue-100 text-blue-800',
      paid: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800'
    };
    return variants[status] || variants.pending;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Commission Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Earned</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(stats.totalEarned || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Commissions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalCommissions || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ClockIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Amount</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(stats.pendingAmount || 0)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Commission History */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Commission History</h3>
          <p className="text-sm text-gray-500">Track your referral commission earnings</p>
        </div>

        <div className="p-6">
          {commissions.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Referred User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Commission
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {commissions.map((commission) => (
                    <tr key={commission.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {commission.referredUserName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {commission.referredUserEmail}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {commission.planName}
                        </div>
                        {commission.isFirstPurchase && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            First Purchase
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(commission.commissionAmount)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {commission.commissionType === 'percentage'
                            ? `${commission.commissionRate}%`
                            : 'Fixed'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge className={getStatusBadge(commission.status)}>
                          {commission.status}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(commission.createdAt)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <CurrencyDollarIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Commission History
              </h3>
              <p className="text-gray-500">
                Start referring users to earn commissions when they purchase subscription plans.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
