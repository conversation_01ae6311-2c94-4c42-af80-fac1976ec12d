"use client";

import { useState } from 'react';
import { NotificationItem } from './NotificationItem';
import { useNotifications } from '@/hooks/useNotifications';
import { Button } from '@/components/ui/Button';
import { BellIcon, CheckIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

interface NotificationsListProps {
  className?: string;
}

export function NotificationsList({ className }: NotificationsListProps) {
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications,
  } = useNotifications();

  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  const filteredNotifications = filter === 'unread' 
    ? notifications.filter(n => !n.read)
    : notifications;

  if (isLoading && notifications.length === 0) {
    return (
      <div className={cn("space-y-4", className)}>
        {/* Loading skeleton */}
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-start space-x-4 p-4 animate-pulse">
            <div className="w-10 h-10 bg-gray-200 rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4" />
              <div className="h-3 bg-gray-200 rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("text-center py-8", className)}>
        <div className="text-red-500 mb-4">
          <BellIcon className="h-12 w-12 mx-auto mb-2" />
          <p className="text-sm">Failed to load notifications</p>
          <p className="text-xs text-gray-500">{error}</p>
        </div>
        <Button onClick={refreshNotifications} variant="outline" size="sm">
          Try Again
        </Button>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className={cn("text-center py-12", className)}>
        <BellIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
        <p className="text-gray-500">You're all caught up! Check back later for new notifications.</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with filters and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Notifications
            {unreadCount > 0 && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {unreadCount} new
              </span>
            )}
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          {/* Filter buttons */}
          <div className="flex rounded-md shadow-sm">
            <button
              onClick={() => setFilter('all')}
              className={cn(
                "px-3 py-1 text-sm font-medium rounded-l-md border",
                filter === 'all'
                  ? "bg-blue-50 border-blue-200 text-blue-700"
                  : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
              )}
            >
              All
            </button>
            <button
              onClick={() => setFilter('unread')}
              className={cn(
                "px-3 py-1 text-sm font-medium rounded-r-md border-t border-r border-b",
                filter === 'unread'
                  ? "bg-blue-50 border-blue-200 text-blue-700"
                  : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
              )}
            >
              Unread ({unreadCount})
            </button>
          </div>

          {/* Mark all as read button */}
          {unreadCount > 0 && (
            <Button
              onClick={markAllAsRead}
              variant="outline"
              size="sm"
              className="flex items-center space-x-1"
            >
              <CheckIcon className="h-4 w-4" />
              <span>Mark all read</span>
            </Button>
          )}
        </div>
      </div>

      {/* Notifications list */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-8">
            <BellIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">
              {filter === 'unread' ? 'No unread notifications' : 'No notifications to show'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredNotifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={markAsRead}
                onDelete={deleteNotification}
              />
            ))}
          </div>
        )}
      </div>

      {/* Load more button (if needed) */}
      {filteredNotifications.length >= 50 && (
        <div className="text-center">
          <Button
            onClick={refreshNotifications}
            variant="outline"
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
}
