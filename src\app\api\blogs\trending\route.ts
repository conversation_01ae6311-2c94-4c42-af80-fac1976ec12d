import { NextResponse } from "next/server";
import { ViewTrackingService } from "@/lib/analytics/viewTrackingService";

// Get trending blogs
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const maxLimit = 50;
    const actualLimit = Math.min(limit, maxLimit);

    const trendingBlogs = await ViewTrackingService.getTrendingBlogs(actualLimit);

    return NextResponse.json({
      success: true,
      data: trendingBlogs,
      meta: {
        total: trendingBlogs.length,
        limit: actualLimit,
      },
    });

  } catch (error) {
    console.error("Error fetching trending blogs:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
