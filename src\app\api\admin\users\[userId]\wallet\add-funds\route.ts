import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { wallets, walletTransactions, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const addFundsSchema = z.object({
  amount: z.number().positive().min(1).max(100000),
  walletType: z.enum(['general', 'earning']).default('general'),
  note: z.string().optional(),
  reason: z.string().min(1).max(500),
});

// Add funds to user wallet (Admin only)
export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const body = await req.json();
    const validatedData = addFundsSchema.parse(body);

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get or create user wallet
    let wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (!wallet) {
      // Create wallet if doesn't exist
      const walletId = uuidv4();
      await db.insert(wallets).values({
        id: walletId,
        userId: userId,
        generalBalance: "0.00",
        earningBalance: "0.00",
        totalDeposited: "0.00",
        totalWithdrawn: "0.00",
        totalSent: "0.00",
        totalReceived: "0.00",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      wallet = await db.query.wallets.findFirst({
        where: eq(wallets.userId, userId),
      });
    }

    if (!wallet) {
      return NextResponse.json(
        { message: "Failed to create wallet" },
        { status: 500 }
      );
    }

    // Calculate new balance
    const currentBalance = parseFloat(
      validatedData.walletType === 'earning' 
        ? wallet.earningBalance 
        : wallet.generalBalance
    );
    const newBalance = currentBalance + validatedData.amount;

    // Update wallet balance
    const updateData = validatedData.walletType === 'earning' 
      ? { 
          earningBalance: newBalance.toFixed(2),
          updatedAt: new Date(),
        }
      : { 
          generalBalance: newBalance.toFixed(2),
          totalDeposited: (parseFloat(wallet.totalDeposited) + validatedData.amount).toFixed(2),
          updatedAt: new Date(),
        };

    await db
      .update(wallets)
      .set(updateData)
      .where(eq(wallets.id, wallet.id));

    // Create transaction record
    const transactionId = uuidv4();
    await db.insert(walletTransactions).values({
      id: transactionId,
      userId: userId,
      type: 'admin_credit',
      amount: validatedData.amount.toFixed(2),
      walletType: validatedData.walletType,
      status: 'completed',
      description: `Admin added funds: ${validatedData.reason}`,
      adminNote: validatedData.note,
      adminId: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Log the action
    console.log(`Admin ${session.user.id} added ৳${validatedData.amount} to ${validatedData.walletType} wallet of user ${userId}`);

    return NextResponse.json({
      success: true,
      message: "Funds added successfully",
      data: {
        transactionId,
        amount: validatedData.amount,
        walletType: validatedData.walletType,
        newBalance: newBalance.toFixed(2),
      },
    });
  } catch (error: any) {
    console.error("Error adding funds to user wallet:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to add funds" 
      },
      { status: 500 }
    );
  }
}
