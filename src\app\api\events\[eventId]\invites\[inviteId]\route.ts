import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventInvites, eventAttendees, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and } from "drizzle-orm";

const inviteResponseSchema = z.object({
  status: z.enum(["accepted", "declined"]),
});

// Update an invite (accept/decline)
export async function PUT(
  req: Request,
  context: { params: Promise<{ eventId: string; inviteId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId, inviteId } = params;
    const body = await req.json();
    const validatedData = inviteResponseSchema.parse(body);

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the invite exists and belongs to the user
    const invite = await db.query.eventInvites.findFirst({
      where: and(
        eq(eventInvites.id, inviteId),
        eq(eventInvites.eventId, eventId),
        eq(eventInvites.toUserId, session.user.id)
      ),
    });

    if (!invite) {
      return NextResponse.json(
        { message: "Invite not found" },
        { status: 404 }
      );
    }

    // Update the invite status
    await db
      .update(eventInvites)
      .set({ status: validatedData.status })
      .where(eq(eventInvites.id, inviteId));

    // Fetch the updated invite
    const updatedInvite = await db.query.eventInvites.findFirst({
      where: eq(eventInvites.id, inviteId),
    });

    if (!updatedInvite) {
      return NextResponse.json(
        { message: "Failed to update invite" },
        { status: 500 }
      );
    }

    // If accepted, add the user as an attendee
    if (validatedData.status === "accepted") {
      // Check if the user is already an attendee
      const existingAttendee = await db.query.eventAttendees.findFirst({
        where: and(
          eq(eventAttendees.eventId, eventId),
          eq(eventAttendees.userId, session.user.id)
        ),
      });

      if (existingAttendee) {
        // Update the existing attendance
        await db
          .update(eventAttendees)
          .set({ status: "going" })
          .where(eq(eventAttendees.id, existingAttendee.id));
      } else {
        // Create a new attendance
        await db
          .insert(eventAttendees)
          .values({
            id: uuidv4(),
            eventId,
            userId: session.user.id,
            status: "going",
          });
      }

      // Notify the event host
      await db.insert(notifications).values({
        id: uuidv4(),
        recipientId: event.hostId,
        type: "event_update",
        senderId: session.user.id,
        eventId,
        read: false,
      });
    }

    return NextResponse.json(updatedInvite);
  } catch (error) {
    console.error("Error updating event invite:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete an invite
export async function DELETE(
  req: Request,
  context: { params: Promise<{ eventId: string; inviteId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId, inviteId } = params;

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the invite exists
    const invite = await db.query.eventInvites.findFirst({
      where: eq(eventInvites.id, inviteId),
    });

    if (!invite) {
      return NextResponse.json(
        { message: "Invite not found" },
        { status: 404 }
      );
    }

    // Check if the user is the host or the recipient of the invite
    if (event.hostId !== session.user.id && invite.toUserId !== session.user.id) {
      return NextResponse.json(
        { message: "You don't have permission to delete this invite" },
        { status: 403 }
      );
    }

    // Delete the invite
    await db.delete(eventInvites).where(eq(eventInvites.id, inviteId));

    return NextResponse.json({ message: "Invite deleted successfully" });
  } catch (error) {
    console.error("Error deleting event invite:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
