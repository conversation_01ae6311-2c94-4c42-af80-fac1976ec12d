import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { z } from "zod";

const withdrawSchema = z.object({
  amount: z.string().min(1, "Amount is required"),
  accountType: z.string().min(1, "Account type is required"),
  accountNumber: z.string().min(1, "Account number is required"),
  accountName: z.string().min(1, "Account name is required"),
  note: z.string().optional(),
  pin: z.string().min(4, "PIN must be at least 4 digits"),
});

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = withdrawSchema.parse(body);

    // Check if withdraw is enabled
    const withdrawEnabled = await WalletService.getWalletSetting('withdraw_enabled');
    if (withdrawEnabled === 'false') {
      return NextResponse.json(
        { 
          success: false,
          message: "Withdraw is currently disabled" 
        },
        { status: 400 }
      );
    }

    // Process withdraw request
    const requestId = await WalletService.requestWithdraw(session.user.id, {
      amount: validatedData.amount,
      accountType: validatedData.accountType,
      accountNumber: validatedData.accountNumber,
      accountName: validatedData.accountName,
      note: validatedData.note,
      pin: validatedData.pin,
    });

    return NextResponse.json({
      success: true,
      data: {
        requestId,
        message: "Withdraw request submitted successfully. It will be processed within 24 hours.",
      },
    });
  } catch (error: any) {
    console.error("Error processing withdraw:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid input data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to process withdraw request" 
      },
      { status: 500 }
    );
  }
}

// Get user's withdraw requests
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const withdrawRequests = await WalletService.getUserWithdrawRequests(session.user.id);

    return NextResponse.json({
      success: true,
      data: withdrawRequests,
    });
  } catch (error: any) {
    console.error("Error fetching withdraw requests:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch withdraw requests" 
      },
      { status: 500 }
    );
  }
}
