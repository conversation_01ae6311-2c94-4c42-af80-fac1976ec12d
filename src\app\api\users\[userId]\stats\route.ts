import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, subscriptions, users, blogs, products, stores } from "@/lib/db/schema";
import { eq, and, count } from "drizzle-orm";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;

    // Get user posts count
    const postsCount = await db
      .select({ count: count() })
      .from(posts)
      .where(eq(posts.userId, userId));

    // Get subscribers count (people who subscribed to this user)
    const subscribersCount = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.targetUserId, userId));

    // Get photos count (posts with images)
    const photosCount = await db
      .select({ count: count() })
      .from(posts)
      .where(
        and(
          eq(posts.userId, userId),
          // Check if images field is not null and not empty
          // This is a simplified check - you might need to adjust based on your schema
        )
      );

    // For now, let's just count all posts as potential photos
    // You can refine this later based on your actual image storage logic
    const allUserPosts = await db
      .select({ images: posts.images })
      .from(posts)
      .where(eq(posts.userId, userId));

    const actualPhotosCount = allUserPosts.filter(post =>
      post.images && Array.isArray(post.images) && post.images.length > 0
    ).length;

    // Get blogs count
    const isOwnProfile = userId === session.user.id;
    const blogsCount = await db
      .select({ count: count() })
      .from(blogs)
      .where(
        isOwnProfile
          ? eq(blogs.authorId, userId)
          : and(eq(blogs.authorId, userId), eq(blogs.status, "published"))
      );

    // Get products count (through stores owned by the user)
    const productsCount = await db
      .select({ count: count() })
      .from(products)
      .leftJoin(stores, eq(products.storeId, stores.id))
      .where(eq(stores.ownerId, userId));

    return NextResponse.json({
      stats: {
        posts: postsCount[0]?.count || 0,
        subscribers: subscribersCount[0]?.count || 0,
        photos: actualPhotosCount || 0,
        blogs: blogsCount[0]?.count || 0,
        products: productsCount[0]?.count || 0,
      }
    });

  } catch (error) {
    console.error("Error fetching user stats:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
