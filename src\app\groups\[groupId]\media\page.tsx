import Link from "next/link";
import Image from "next/image";
import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { db } from "@/lib/db";
import { groups, groupMembers, posts } from "@/lib/db/schema";
import { eq, and, desc, isNotNull, count } from "drizzle-orm";
import { formatTimeAgo } from "@/lib/utils";
import {
  UsersIcon,
  ChevronLeftIcon,
  PhotoIcon,
  VideoCameraIcon,
  ArrowPathIcon
} from "@heroicons/react/24/outline";

// Import components
import { GroupHeader } from "@/components/groups/GroupHeader";
import { GroupTabs } from "@/components/groups/GroupTabs";
import { GroupNotFound } from "@/components/groups/GroupNotFound";

interface GroupMediaPageProps {
  params: Promise<{
    groupId: string;
  }>;
}

export default async function GroupMediaPage({ params }: GroupMediaPageProps) {
  const user = await requireAuth();
  const resolvedParams = await params;
  const { groupId } = resolvedParams;

  // Fetch the group
  const group = await db.select({
    id: groups.id,
    name: groups.name,
    description: groups.description,
    visibility: groups.visibility,
    coverImage: groups.coverImage,
    category: groups.category,
    creatorId: groups.creatorId,
    createdAt: groups.createdAt,
    updatedAt: groups.updatedAt
  })
  .from(groups)
  .where(eq(groups.id, groupId))
  .limit(1)
  .then(results => results[0]);

  if (!group) {
    // If group doesn't exist, show a nice error message
    return (
      <MainLayout>
        <GroupNotFound />
      </MainLayout>
    );
  }

  // Get user's role in the group
  const userMembership = await db.query.groupMembers.findFirst({
    where: and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.userId, user.id)
    ),
  });

  // Check if user has access to the group
  const isPublic = group.visibility === "public";
  const isPrivateVisible = group.visibility === "private-visible";
  const isMember = userMembership && (userMembership.role === "admin" || userMembership.role === "moderator" || userMembership.role === "member");
  const isAdmin = userMembership && userMembership.role === "admin";
  const isModerator = userMembership && userMembership.role === "moderator";
  const isCreator = group && group.creatorId === user.id;

  // Get member count
  const membersCount = await db
    .select({ count: count() })
    .from(groupMembers)
    .where(eq(groupMembers.groupId, groupId));

  // For private groups, only members can see the media
  if ((group.visibility === "private-hidden" || group.visibility === "private-visible") && !isMember && !isCreator) {
    return (
      <MainLayout>
        <GroupHeader
          group={group}
          membersCount={membersCount[0]?.count || 0}
          isCreator={isCreator}
          isAdmin={isAdmin || false}
          isMember={isMember || false}
          isPending={false}
          isPublic={isPublic}
          isPrivateVisible={isPrivateVisible}
        />

        <GroupTabs groupId={groupId} activeTab="media" />

        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-white p-8 text-center shadow">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
              <UsersIcon className="h-8 w-8 text-yellow-600" />
            </div>
            <h1 className="mt-4 text-xl font-bold text-gray-900">
              Private Group
            </h1>
            <p className="mt-4 text-sm text-gray-500">
              This is a private group. You need to be a member to view its media.
            </p>
            <div className="mt-4">
              <Link href={`/groups/${groupId}`}>
                <Button>
                  View Group
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Fetch posts with images
  const postsWithImages = await db.query.posts.findMany({
    where: and(
      eq(posts.groupId, groupId),
      isNotNull(posts.images),
      eq(posts.isPublished, true)
    ),
    orderBy: [desc(posts.createdAt)],
    with: {
      user: {
        columns: {
          id: true,
          name: true,
          username: true,
          image: true,
        },
      },
    },
  });

  // Fetch posts with videos
  const postsWithVideos = await db.query.posts.findMany({
    where: and(
      eq(posts.groupId, groupId),
      isNotNull(posts.videos),
      eq(posts.isPublished, true)
    ),
    orderBy: [desc(posts.createdAt)],
    with: {
      user: {
        columns: {
          id: true,
          name: true,
          username: true,
          image: true,
        },
      },
    },
  });

  // Extract all images and videos
  const allImages: { url: string; postId: string; createdAt: Date; user: any }[] = [];
  const allVideos: { url: string; postId: string; createdAt: Date; user: any }[] = [];

  postsWithImages.forEach(post => {
    if (post.images && Array.isArray(post.images)) {
      post.images.forEach(imageUrl => {
        allImages.push({
          url: imageUrl,
          postId: post.id,
          createdAt: post.createdAt,
          user: post.user
        });
      });
    }
  });

  postsWithVideos.forEach(post => {
    if (post.videos && Array.isArray(post.videos)) {
      post.videos.forEach(videoUrl => {
        allVideos.push({
          url: videoUrl,
          postId: post.id,
          createdAt: post.createdAt,
          user: post.user
        });
      });
    }
  });

  return (
    <MainLayout>
      {/* Hero section with cover image */}
      <GroupHeader
        group={group}
        membersCount={membersCount[0]?.count || 0}
        isCreator={isCreator}
        isAdmin={isAdmin || false}
        isMember={isMember || false}
        isPending={false}
        isPublic={isPublic}
        isPrivateVisible={isPrivateVisible}
      />

      {/* Tabs navigation */}
      <GroupTabs groupId={groupId} activeTab="media" showSettings={isCreator || isAdmin} />

      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Page title and stats */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Media
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            {allImages.length} photos • {allVideos.length} videos
          </p>
        </div>

        {/* Media type tabs */}
        <div className="mb-6 border-b border-gray-200">
          <div className="flex space-x-8">
            <a
              href="#photos"
              className="border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600"
            >
              <div className="flex items-center">
                <PhotoIcon className="mr-2 h-5 w-5" />
                <span>Photos</span>
              </div>
            </a>
            <a
              href="#videos"
              className="border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700"
            >
              <div className="flex items-center">
                <VideoCameraIcon className="mr-2 h-5 w-5" />
                <span>Videos</span>
              </div>
            </a>
          </div>
        </div>

        {/* Photos section */}
        <div id="photos" className="mb-12">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Photos</h2>
          </div>

          {allImages.length > 0 ? (
            <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
              {allImages.map((image, index) => (
                <div key={`${image.postId}-${index}`} className="group relative aspect-square overflow-hidden rounded-lg bg-gray-100">
                  <Image
                    src={image.url}
                    alt={`Photo ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    <div className="absolute bottom-0 left-0 right-0 p-3">
                      <div className="flex items-center">
                        <div className="h-6 w-6 rounded-full overflow-hidden bg-gray-200">
                          {image.user.image ? (
                            <Image
                              src={image.user.image}
                              alt={image.user.name || "User"}
                              width={24}
                              height={24}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="flex h-full w-full items-center justify-center bg-gray-300 text-gray-600 text-xs">
                              {image.user.name?.charAt(0).toUpperCase() || "U"}
                            </div>
                          )}
                        </div>
                        <span className="ml-2 text-xs text-white">
                          {formatTimeAgo(image.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-lg bg-white p-8 text-center shadow">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                <PhotoIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h2 className="mt-4 text-lg font-medium text-gray-900">
                No photos yet
              </h2>
              <p className="mt-2 text-sm text-gray-500">
                No photos have been shared in this group yet.
              </p>
              {isMember && (
                <div className="mt-4">
                  <Link href={`/groups/${groupId}`}>
                    <Button>
                      Share Photos
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Videos section */}
        <div id="videos">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Videos</h2>
          </div>

          {allVideos.length > 0 ? (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {allVideos.map((video, index) => (
                <div key={`${video.postId}-${index}`} className="group relative overflow-hidden rounded-lg bg-gray-100">
                  <div className="aspect-video">
                    <video
                      src={video.url}
                      className="h-full w-full object-cover"
                      controls
                    />
                  </div>
                  <div className="p-3 bg-white border-t border-gray-100">
                    <div className="flex items-center">
                      <div className="h-6 w-6 rounded-full overflow-hidden bg-gray-200">
                        {video.user.image ? (
                          <Image
                            src={video.user.image}
                            alt={video.user.name || "User"}
                            width={24}
                            height={24}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center bg-gray-300 text-gray-600 text-xs">
                            {video.user.name?.charAt(0).toUpperCase() || "U"}
                          </div>
                        )}
                      </div>
                      <span className="ml-2 text-xs text-gray-500">
                        {video.user.name} • {formatTimeAgo(video.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-lg bg-white p-8 text-center shadow">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                <VideoCameraIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h2 className="mt-4 text-lg font-medium text-gray-900">
                No videos yet
              </h2>
              <p className="mt-2 text-sm text-gray-500">
                No videos have been shared in this group yet.
              </p>
              {isMember && (
                <div className="mt-4">
                  <Link href={`/groups/${groupId}`}>
                    <Button>
                      Share Videos
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
