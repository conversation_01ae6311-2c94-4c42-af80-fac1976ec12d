"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import {
  CreditCardIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  Cog6ToothIcon,
  BanknotesIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { GatewayConfigModal } from "@/components/admin/GatewayConfigModal";

interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  type: string;
  isActive: boolean;
  config: any;
  depositFee: string;
  depositFixedFee: string;
  minDeposit: string;
  maxDeposit: string;
  currency: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export default function AdminGatewaysPage() {
  const [gateways, setGateways] = useState<PaymentGateway[]>([]);
  const [loading, setLoading] = useState(true);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchGateways();
  }, []);

  const fetchGateways = async () => {
    try {
      const response = await fetch('/api/admin/wallet/gateways');
      const data = await response.json();

      if (data.success) {
        setGateways(data.data);
      } else {
        toast.error('Failed to fetch payment gateways');
      }
    } catch (error) {
      console.error('Error fetching gateways:', error);
      toast.error('Failed to fetch payment gateways');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGateway = () => {
    setSelectedGateway(null);
    setModalMode('create');
    setIsConfigModalOpen(true);
  };

  const handleEditGateway = (gateway: PaymentGateway) => {
    setSelectedGateway(gateway);
    setModalMode('edit');
    setIsConfigModalOpen(true);
  };

  const handleToggleStatus = async (gatewayId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/wallet/gateways/${gatewayId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !currentStatus }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        fetchGateways();
      } else {
        toast.error(data.message || 'Failed to toggle gateway status');
      }
    } catch (error) {
      console.error('Error toggling gateway status:', error);
      toast.error('Failed to toggle gateway status');
    }
  };

  const handleDeleteGateway = async (gatewayId: string, gatewayName: string) => {
    if (!confirm(`Are you sure you want to delete the ${gatewayName} gateway? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/wallet/gateways/${gatewayId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        fetchGateways();
      } else {
        toast.error(data.message || 'Failed to delete gateway');
      }
    } catch (error) {
      console.error('Error deleting gateway:', error);
      toast.error('Failed to delete gateway');
    }
  };

  const getGatewayIcon = (type: string) => {
    switch (type) {
      case 'stripe':
        return <CreditCardIcon className="h-6 w-6" />;
      case 'paypal':
        return <BanknotesIcon className="h-6 w-6" />;
      case 'uddoktapay':
        return <Cog6ToothIcon className="h-6 w-6" />;
      case 'manual':
        return <BanknotesIcon className="h-6 w-6" />;
      default:
        return <CreditCardIcon className="h-6 w-6" />;
    }
  };

  const getGatewayTypeColor = (type: string) => {
    switch (type) {
      case 'stripe':
        return 'bg-purple-100 text-purple-800';
      case 'paypal':
        return 'bg-blue-100 text-blue-800';
      case 'uddoktapay':
        return 'bg-green-100 text-green-800';
      case 'manual':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment Gateways</h1>
            <p className="text-gray-600 mt-1">
              Manage payment gateways and their configurations
            </p>
          </div>
          <Button onClick={handleCreateGateway}>
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Gateway
          </Button>
        </div>

        {/* Gateway Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CreditCardIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Gateways</p>
                <p className="text-2xl font-bold text-gray-900">{gateways.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <EyeIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Gateways</p>
                <p className="text-2xl font-bold text-gray-900">
                  {gateways.filter(g => g.isActive).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <EyeSlashIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Inactive Gateways</p>
                <p className="text-2xl font-bold text-gray-900">
                  {gateways.filter(g => !g.isActive).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Cog6ToothIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Configured</p>
                <p className="text-2xl font-bold text-gray-900">
                  {gateways.filter(g => g.config && Object.keys(g.config).length > 0).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Gateways List */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">All Payment Gateways</h2>
          </div>

          {gateways.length === 0 ? (
            <div className="text-center py-12">
              <CreditCardIcon className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500 mb-4">No payment gateways configured</p>
              <Button onClick={handleCreateGateway}>
                <PlusIcon className="h-5 w-5 mr-2" />
                Add Your First Gateway
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Gateway
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fees
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Limits
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {gateways.map((gateway) => (
                    <tr key={gateway.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="p-2 bg-gray-100 rounded-lg mr-3">
                            {getGatewayIcon(gateway.type)}
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {gateway.displayName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {gateway.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGatewayTypeColor(gateway.type)}`}>
                          {gateway.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          gateway.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {gateway.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {gateway.depositFee}% + ${gateway.depositFixedFee}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${gateway.minDeposit} - ${gateway.maxDeposit}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditGateway(gateway)}
                          >
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          <Button
                            variant={gateway.isActive ? "outline" : "secondary"}
                            size="sm"
                            onClick={() => handleToggleStatus(gateway.id, gateway.isActive)}
                          >
                            {gateway.isActive ? (
                              <EyeSlashIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={() => handleDeleteGateway(gateway.id, gateway.displayName)}
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Gateway Configuration Modal */}
      <GatewayConfigModal
        isOpen={isConfigModalOpen}
        onClose={() => setIsConfigModalOpen(false)}
        onSuccess={() => {
          setIsConfigModalOpen(false);
          fetchGateways();
        }}
        gateway={selectedGateway}
        mode={modalMode}
      />
    </AdminLayout>
  );
}
