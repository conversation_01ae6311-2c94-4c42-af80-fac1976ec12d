"use client";

import { useState } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import { PhotoIcon, PencilIcon } from "@heroicons/react/24/outline";
import { GroupPostForm } from "./GroupPostForm";

interface GroupPostFormPlaceholderProps {
  groupId: string;
  groupName: string;
  userImage: string | null;
  userName: string | null;
  isAdmin: boolean;
  isModerator: boolean;
}

export function GroupPostFormPlaceholder({
  groupId,
  groupName,
  userImage,
  userName,
  isAdmin,
  isModerator,
}: GroupPostFormPlaceholderProps) {
  const [showFullForm, setShowFullForm] = useState(false);

  if (showFullForm) {
    return (
      <GroupPostForm
        groupId={groupId}
        groupName={groupName}
        userImage={userImage}
        isAdmin={isAdmin}
        isModerator={isModerator}
        onPostCreated={() => setShowFullForm(false)}
      />
    );
  }

  return (
    <div className="rounded-lg bg-white p-4 shadow">
      <div className="flex space-x-3">
        <div className="flex-shrink-0">
          {userImage ? (
            <Image
              src={userImage}
              alt={userName || "User"}
              width={40}
              height={40}
              className="h-10 w-10 rounded-full"
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500 text-sm font-bold">{userName?.charAt(0).toUpperCase() || "U"}</span>
            </div>
          )}
        </div>
        <div className="min-w-0 flex-1">
          <div 
            className="rounded-full bg-gray-100 px-4 py-2 text-gray-500 hover:bg-gray-200 cursor-pointer"
            onClick={() => setShowFullForm(true)}
          >
            <span>Write something to {groupName}...</span>
          </div>
          <div className="mt-3 flex items-center justify-between">
            <div className="flex space-x-4">
              <button 
                className="flex items-center text-gray-500 hover:text-gray-700"
                onClick={() => setShowFullForm(true)}
              >
                <PhotoIcon className="h-5 w-5 mr-1" />
                <span className="text-xs">Photo/Video</span>
              </button>
              <button 
                className="flex items-center text-gray-500 hover:text-gray-700"
                onClick={() => setShowFullForm(true)}
              >
                <PencilIcon className="h-5 w-5 mr-1" />
                <span className="text-xs">Write Post</span>
              </button>
            </div>
            <Button 
              size="sm"
              onClick={() => setShowFullForm(true)}
            >
              Post
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
