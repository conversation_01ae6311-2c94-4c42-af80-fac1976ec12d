"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Select } from "@/components/ui/Select";
import { toast } from "react-hot-toast";
import {
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";

export function StoreImportExport() {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [exportFormat, setExportFormat] = useState("json");
  const [importFormat, setImportFormat] = useState("json");
  const [importFile, setImportFile] = useState<File | null>(null);
  const [validationResults, setValidationResults] = useState<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    validStores: number;
  } | null>(null);

  const handleExport = async () => {
    setIsExporting(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));
      
      // Create a mock export file
      const mockData = {
        stores: [
          {
            name: "Example Store 1",
            slug: "example-store-1",
            description: "This is an example store",
            location: "New York, NY",
            isVerified: true,
            ownerId: "user-123",
          },
          {
            name: "Example Store 2",
            slug: "example-store-2",
            description: "Another example store",
            location: "Los Angeles, CA",
            isVerified: false,
            ownerId: "user-456",
          },
        ],
      };
      
      let fileContent;
      let fileName;
      let mimeType;
      
      if (exportFormat === "json") {
        fileContent = JSON.stringify(mockData, null, 2);
        fileName = "stores-export.json";
        mimeType = "application/json";
      } else {
        // Create CSV content
        const headers = "name,slug,description,location,isVerified,ownerId\n";
        const rows = mockData.stores.map(store => 
          `"${store.name}","${store.slug}","${store.description}","${store.location}",${store.isVerified},"${store.ownerId}"`
        ).join("\n");
        fileContent = headers + rows;
        fileName = "stores-export.csv";
        mimeType = "text/csv";
      }
      
      // Create and download the file
      const blob = new Blob([fileContent], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast.success(`Stores exported successfully as ${exportFormat.toUpperCase()}`);
    } catch (error) {
      console.error("Error exporting stores:", error);
      toast.error("Failed to export stores");
    } finally {
      setIsExporting(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0]);
      setValidationResults(null);
    }
  };

  const validateImport = async () => {
    if (!importFile) {
      toast.error("Please select a file to import");
      return;
    }

    setIsImporting(true);
    try {
      // Simulate file reading and validation
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      // Mock validation results
      const mockValidation = {
        isValid: Math.random() > 0.3, // 70% chance of being valid
        errors: [] as string[],
        warnings: [] as string[],
        validStores: Math.floor(Math.random() * 10) + 1,
      };
      
      // Add some mock errors or warnings
      if (!mockValidation.isValid) {
        mockValidation.errors.push("Some stores are missing required fields");
        mockValidation.errors.push("Duplicate store slugs found");
      } else if (Math.random() > 0.5) {
        mockValidation.warnings.push("Some stores have missing optional fields");
        mockValidation.warnings.push("Some owner IDs may not exist in the system");
      }
      
      setValidationResults(mockValidation);
      
      if (mockValidation.isValid) {
        toast.success("File validated successfully");
      } else {
        toast.error("Validation failed. Please fix the errors and try again.");
      }
    } catch (error) {
      console.error("Error validating import file:", error);
      toast.error("Failed to validate import file");
      setValidationResults(null);
    } finally {
      setIsImporting(false);
    }
  };

  const handleImport = async () => {
    if (!importFile || !validationResults || !validationResults.isValid) {
      toast.error("Please validate the file before importing");
      return;
    }

    setIsImporting(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      
      toast.success(`${validationResults.validStores} stores imported successfully`);
      setImportFile(null);
      setValidationResults(null);
    } catch (error) {
      console.error("Error importing stores:", error);
      toast.error("Failed to import stores");
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Import & Export</h2>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Export Section */}
        <div className="rounded-lg border border-gray-200 bg-white p-6">
          <h3 className="mb-4 text-base font-medium text-gray-900">Export Stores</h3>
          <p className="mb-4 text-sm text-gray-500">
            Export all stores to a file for backup or migration purposes.
          </p>
          
          <div className="mb-4">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Export Format
            </label>
            <Select
              value={exportFormat}
              onChange={(e) => setExportFormat(e.target.value)}
              className="w-full"
            >
              <option value="json">JSON</option>
              <option value="csv">CSV</option>
            </Select>
          </div>
          
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="w-full"
          >
            {isExporting ? (
              <Spinner size="sm" className="mr-2" />
            ) : (
              <ArrowDownTrayIcon className="mr-2 h-5 w-5" />
            )}
            {isExporting ? "Exporting..." : "Export Stores"}
          </Button>
        </div>

        {/* Import Section */}
        <div className="rounded-lg border border-gray-200 bg-white p-6">
          <h3 className="mb-4 text-base font-medium text-gray-900">Import Stores</h3>
          <p className="mb-4 text-sm text-gray-500">
            Import stores from a file. Supported formats: JSON, CSV.
          </p>
          
          <div className="mb-4">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Import Format
            </label>
            <Select
              value={importFormat}
              onChange={(e) => setImportFormat(e.target.value)}
              className="w-full"
            >
              <option value="json">JSON</option>
              <option value="csv">CSV</option>
            </Select>
          </div>
          
          <div className="mb-4">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Select File
            </label>
            <Input
              type="file"
              accept={importFormat === "json" ? ".json" : ".csv"}
              onChange={handleFileChange}
              className="w-full"
            />
            {importFile && (
              <p className="mt-1 text-xs text-gray-500">
                Selected file: {importFile.name} ({Math.round(importFile.size / 1024)} KB)
              </p>
            )}
          </div>
          
          <div className="flex space-x-2">
            <Button
              onClick={validateImport}
              disabled={!importFile || isImporting}
              variant="outline"
              className="flex-1"
            >
              {isImporting && !validationResults ? (
                <Spinner size="sm" className="mr-2" />
              ) : (
                <DocumentTextIcon className="mr-2 h-5 w-5" />
              )}
              Validate
            </Button>
            <Button
              onClick={handleImport}
              disabled={!validationResults?.isValid || isImporting}
              className="flex-1"
            >
              {isImporting && validationResults ? (
                <Spinner size="sm" className="mr-2" />
              ) : (
                <ArrowUpTrayIcon className="mr-2 h-5 w-5" />
              )}
              Import
            </Button>
          </div>
        </div>
      </div>

      {/* Validation Results */}
      {validationResults && (
        <div className={`rounded-lg border ${validationResults.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'} p-6`}>
          <div className="flex items-center">
            {validationResults.isValid ? (
              <CheckCircleIcon className="h-6 w-6 text-green-500" />
            ) : (
              <XCircleIcon className="h-6 w-6 text-red-500" />
            )}
            <h3 className="ml-2 text-base font-medium">
              {validationResults.isValid ? "Validation Successful" : "Validation Failed"}
            </h3>
          </div>
          
          <div className="mt-4">
            <p className="text-sm">
              {validationResults.validStores} valid stores found in the file.
            </p>
            
            {validationResults.errors.length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium text-red-700">Errors:</p>
                <ul className="mt-1 list-inside list-disc text-sm text-red-700">
                  {validationResults.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {validationResults.warnings.length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium text-yellow-700">Warnings:</p>
                <ul className="mt-1 list-inside list-disc text-sm text-yellow-700">
                  {validationResults.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          
          {validationResults.isValid && (
            <div className="mt-4">
              <p className="text-sm text-green-700">
                The file is valid and ready to be imported. Click the Import button to continue.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Template Downloads */}
      <div className="rounded-lg border border-gray-200 bg-white p-6">
        <h3 className="mb-4 text-base font-medium text-gray-900">Download Templates</h3>
        <p className="mb-4 text-sm text-gray-500">
          Download template files to help you format your data correctly for import.
        </p>
        
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm">
            <DocumentTextIcon className="mr-2 h-5 w-5" />
            JSON Template
          </Button>
          <Button variant="outline" size="sm">
            <DocumentTextIcon className="mr-2 h-5 w-5" />
            CSV Template
          </Button>
        </div>
      </div>
    </div>
  );
}
