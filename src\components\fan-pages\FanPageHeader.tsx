"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { useFanPageMessage } from "@/contexts/FanPageMessageContext";
import {
  CheckBadgeIcon,
  MapPinIcon,
  CalendarIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  CameraIcon,
  PencilIcon,
  EllipsisHorizontalIcon,
  ShareIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";
import { CheckBadgeIcon as CheckBadgeIconSolid } from "@heroicons/react/24/solid";
import { formatDistanceToNow } from "date-fns";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";

interface FanPageHeaderProps {
  page: {
    id: string;
    name: string;
    username: string;
    category: string;
    description: string | null;
    profileImage: string | null;
    coverImage: string | null;
    website: string | null;
    email: string | null;
    phone: string | null;
    location: string | null;
    isVerified: boolean;
    followerCount: number;
    postCount: number;
    createdAt: Date;
    owner?: {
      id: string;
      name: string | null;
      image: string | null;
    };
  };
  isFollowing: boolean;
  isOwner: boolean;
  userRole: string | null;
  onFollowToggle: () => Promise<void>;
  onEditPage?: () => void;
  onUploadCover?: () => void;
  onUploadProfile?: () => void;
  isFollowLoading?: boolean;
}

const categoryLabels: Record<string, string> = {
  musician: "Musician",
  actor: "Actor",
  brand: "Brand",
  business: "Business",
  organization: "Organization",
  public_figure: "Public Figure",
  artist: "Artist",
  writer: "Writer",
  athlete: "Athlete",
  politician: "Politician",
  entertainment: "Entertainment",
  media: "Media",
  community: "Community",
  cause: "Cause",
  other: "Other",
};

export function FanPageHeader({
  page,
  isFollowing,
  isOwner,
  userRole,
  onFollowToggle,
  onEditPage,
  onUploadCover,
  onUploadProfile,
  isFollowLoading = false,
}: FanPageHeaderProps) {
  const [coverError, setCoverError] = useState(false);
  const [profileError, setProfileError] = useState(false);
  const { openMessageBox } = useFanPageMessage();

  const handleMessageClick = () => {
    openMessageBox(page.id, page.name, page.profileImage);
  };

  const handleEditClick = () => {
    if (onEditPage) {
      onEditPage();
    }
  };

  const canEdit = isOwner || userRole === 'admin' || userRole === 'editor';

  return (
    <div className="bg-white shadow-xl border-b border-gray-100">
      {/* Cover Photo */}
      <div className="max-w-[1200px] mx-auto">
        <div className="relative h-72 md:h-96 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-500 overflow-hidden rounded-b-3xl">
          {page.coverImage && !coverError ? (
            <>
              <OptimizedImage
                src={page.coverImage}
                alt={`${page.name} cover`}
                fill
                className="object-cover"
                onError={() => setCoverError(true)}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
            </>
          ) : (
            <>
              <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-500" />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
              {/* Decorative Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-10 left-10 w-32 h-32 border border-white rounded-full" />
                <div className="absolute top-20 right-20 w-24 h-24 border border-white rounded-full" />
                <div className="absolute bottom-20 left-1/3 w-16 h-16 border border-white rounded-full" />
              </div>
            </>
          )}

          {/* Cover Photo Upload Button */}
          {canEdit && onUploadCover && (
            <button
              onClick={onUploadCover}
              className="absolute bottom-6 right-6 bg-white/90 backdrop-blur-sm hover:bg-white rounded-xl p-3 transition-all duration-300 shadow-lg hover:shadow-xl group"
            >
              <CameraIcon className="h-6 w-6 text-gray-700 group-hover:text-blue-600 transition-colors" />
            </button>
          )}
        </div>
      </div>

      {/* Profile Section */}
      <div className="relative max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-8 pb-8">
          {/* Profile Picture */}
          <div className="relative -mt-20 sm:-mt-24">
            <div className="relative h-36 w-36 sm:h-44 sm:w-44">
              {page.profileImage && !profileError ? (
                <OptimizedImage
                  src={page.profileImage}
                  alt={page.name}
                  fill
                  className="rounded-full object-cover border-6 border-white shadow-2xl"
                  onError={() => setProfileError(true)}
                />
              ) : (
                <div className="h-full w-full rounded-full bg-gradient-to-br from-gray-300 to-gray-400 border-6 border-white shadow-2xl flex items-center justify-center">
                  <span className="text-3xl sm:text-4xl font-bold text-white">
                    {page.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}

              {/* Profile Photo Upload Button */}
              {canEdit && onUploadProfile && (
                <button
                  onClick={onUploadProfile}
                  className="absolute bottom-3 right-3 bg-white/90 backdrop-blur-sm hover:bg-white rounded-full p-2.5 shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  <CameraIcon className="h-5 w-5 text-gray-700 group-hover:text-blue-600 transition-colors" />
                </button>
              )}
            </div>
          </div>

          {/* Page Info */}
          <div className="flex-1 mt-6 sm:mt-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <div className="flex items-center space-x-3">
                  <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    {page.name}
                  </h1>
                  {page.isVerified && (
                    <div className="relative">
                      <CheckBadgeIconSolid className="h-8 w-8 text-blue-500" />
                      <div className="absolute inset-0 h-8 w-8 text-blue-400 animate-ping opacity-20">
                        <CheckBadgeIconSolid className="h-8 w-8" />
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-3 mt-2">
                  <span className="text-gray-600 font-medium">@{page.username}</span>
                  <span className="text-gray-400">•</span>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 border border-blue-200">
                    {categoryLabels[page.category] || page.category}
                  </span>
                </div>

                {/* Stats */}
                <div className="flex items-center space-x-6 mt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{page.followerCount.toLocaleString()}</div>
                    <div className="text-sm text-gray-600 font-medium">Followers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{page.postCount.toLocaleString()}</div>
                    <div className="text-sm text-gray-600 font-medium">Posts</div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3 mt-6 sm:mt-0">
                {!isOwner && (
                  <>
                    <Button
                      onClick={onFollowToggle}
                      disabled={isFollowLoading}
                      variant={isFollowing ? "outline" : "primary"}
                      className={`min-w-[120px] h-11 font-semibold transition-all duration-200 transform hover:scale-105 ${
                        isFollowing
                          ? 'border-2 border-blue-500 text-blue-600 hover:bg-blue-50 hover:border-blue-600'
                          : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                      } ${isFollowLoading ? 'cursor-not-allowed opacity-75' : 'cursor-pointer'}`}
                    >
                      {isFollowLoading ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                          <span>{isFollowing ? 'Unfollowing...' : 'Following...'}</span>
                        </div>
                      ) : isFollowing ? (
                        <div className="flex items-center space-x-2 group">
                          <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <span className="group-hover:hidden">Following</span>
                          <span className="hidden group-hover:inline">Unfollow</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2 group">
                          <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                          </svg>
                          <span>Follow</span>
                        </div>
                      )}
                    </Button>

                    <Button
                      onClick={handleMessageClick}
                      variant="outline"
                      className="h-11 border-2 border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-all duration-300 font-semibold"
                    >
                      <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2" />
                      Message
                    </Button>
                  </>
                )}

                {canEdit && (
                  <Button
                    onClick={onEditPage}
                    variant="outline"
                    className="h-11 border-2 border-purple-300 text-purple-600 hover:border-purple-500 hover:bg-purple-50 transition-all duration-300 font-semibold"
                  >
                    <PencilIcon className="h-5 w-5 mr-2" />
                    Edit Page
                  </Button>
                )}

                {isOwner && (
                  <Link href={`/pages/${page.username}/dashboard`}>
                    <Button
                      variant="outline"
                      className="h-11 border-2 border-green-300 text-green-600 hover:border-green-500 hover:bg-green-50 transition-all duration-300 font-semibold"
                    >
                      <Cog6ToothIcon className="h-5 w-5 mr-2" />
                      Dashboard
                    </Button>
                  </Link>
                )}

                {/* More Options Menu */}
                <Menu as="div" className="relative">
                  <MenuButton className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                    <EllipsisHorizontalIcon className="h-5 w-5" />
                  </MenuButton>
                  <MenuItems className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                    <MenuItem>
                      {({ active }) => (
                        <button
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } flex items-center w-full px-4 py-2 text-sm text-gray-700`}
                        >
                          <ShareIcon className="h-4 w-4 mr-3" />
                          Share Page
                        </button>
                      )}
                    </MenuItem>
                    {!isOwner && (
                      <MenuItem>
                        {({ active }) => (
                          <button
                            className={`${
                              active ? 'bg-gray-100' : ''
                            } flex items-center w-full px-4 py-2 text-sm text-gray-700`}
                          >
                            Report Page
                          </button>
                        )}
                      </MenuItem>
                    )}
                  </MenuItems>
                </Menu>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
}
