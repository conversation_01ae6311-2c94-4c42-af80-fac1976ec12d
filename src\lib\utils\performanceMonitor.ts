"use client";

import React from 'react';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge';
  tags?: Record<string, string>;
}

interface WebVitalsMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isClient = typeof window !== 'undefined';

  constructor() {
    if (this.isClient) {
      this.initializeObservers();
      this.trackWebVitals();
    }
  }

  private initializeObservers() {
    // Track navigation timing
    if ('PerformanceObserver' in window) {
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.fetchStart, 'timing');
              this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.fetchStart, 'timing');
              this.recordMetric('first_byte', navEntry.responseStart - navEntry.fetchStart, 'timing');
            }
          }
        });
        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);
      } catch (error) {
        console.warn('Navigation observer not supported:', error);
      }

      // Track resource timing
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'resource') {
              const resourceEntry = entry as PerformanceResourceTiming;
              this.recordMetric('resource_load_time', resourceEntry.responseEnd - resourceEntry.fetchStart, 'timing', {
                resource_type: this.getResourceType(resourceEntry.name),
                resource_name: resourceEntry.name
              });
            }
          }
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (error) {
        console.warn('Resource observer not supported:', error);
      }

      // Track long tasks
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'longtask') {
              this.recordMetric('long_task_duration', entry.duration, 'timing');
            }
          }
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      } catch (error) {
        console.warn('Long task observer not supported:', error);
      }
    }
  }

  private trackWebVitals() {
    if (this.isClient) {
      // Track Core Web Vitals using the web-vitals library pattern
      this.trackCLS();
      this.trackFID();
      this.trackLCP();
      this.trackFCP();
      this.trackTTFB();
    }
  }

  private trackCLS() {
    // Cumulative Layout Shift
    if ('PerformanceObserver' in window) {
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          this.recordWebVital('CLS', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (error) {
        console.warn('CLS tracking not supported:', error);
      }
    }
  }

  private trackFID() {
    // First Input Delay
    if ('PerformanceObserver' in window) {
      try {
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordWebVital('FID', (entry as any).processingStart - entry.startTime);
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (error) {
        console.warn('FID tracking not supported:', error);
      }
    }
  }

  private trackLCP() {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.recordWebVital('LCP', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (error) {
        console.warn('LCP tracking not supported:', error);
      }
    }
  }

  private trackFCP() {
    // First Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const fcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.recordWebVital('FCP', entry.startTime);
            }
          }
        });
        fcpObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(fcpObserver);
      } catch (error) {
        console.warn('FCP tracking not supported:', error);
      }
    }
  }

  private trackTTFB() {
    // Time to First Byte
    if (this.isClient && 'performance' in window && 'getEntriesByType' in performance) {
      const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
      if (navigationEntries.length > 0) {
        const ttfb = navigationEntries[0].responseStart - navigationEntries[0].fetchStart;
        this.recordWebVital('TTFB', ttfb);
      }
    }
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
    return 'other';
  }

  private recordWebVital(name: string, value: number) {
    const rating = this.getWebVitalRating(name, value);
    this.recordMetric(`web_vital_${name.toLowerCase()}`, value, 'timing', { rating });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 ${name}: ${value.toFixed(2)}ms (${rating})`);
    }
  }

  private getWebVitalRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = {
      CLS: { good: 0.1, poor: 0.25 },
      FID: { good: 100, poor: 300 },
      LCP: { good: 2500, poor: 4000 },
      FCP: { good: 1800, poor: 3000 },
      TTFB: { good: 800, poor: 1800 }
    };

    const threshold = thresholds[name as keyof typeof thresholds];
    if (!threshold) return 'good';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  recordMetric(name: string, value: number, type: 'timing' | 'counter' | 'gauge', tags?: Record<string, string>) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      tags
    };

    this.metrics.push(metric);

    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`📈 ${name}: ${value}${type === 'timing' ? 'ms' : ''}`, tags || '');
    }
  }

  // Track API call performance
  trackAPICall(url: string, method: string, startTime: number, endTime: number, status: number) {
    const duration = endTime - startTime;
    this.recordMetric('api_call_duration', duration, 'timing', {
      url,
      method,
      status: status.toString()
    });
  }

  // Track component render time
  trackComponentRender(componentName: string, renderTime: number) {
    this.recordMetric('component_render_time', renderTime, 'timing', {
      component: componentName
    });
  }

  // Track user interactions
  trackUserInteraction(action: string, element?: string) {
    this.recordMetric('user_interaction', 1, 'counter', {
      action,
      element: element || 'unknown'
    });
  }

  // Get performance summary
  getPerformanceSummary(): {
    webVitals: Record<string, number>;
    apiCalls: { average: number; count: number };
    componentRenders: Record<string, number>;
    userInteractions: Record<string, number>;
  } {
    const webVitals: Record<string, number> = {};
    const apiCalls: number[] = [];
    const componentRenders: Record<string, number> = {};
    const userInteractions: Record<string, number> = {};

    for (const metric of this.metrics) {
      if (metric.name.startsWith('web_vital_')) {
        const vitalName = metric.name.replace('web_vital_', '').toUpperCase();
        webVitals[vitalName] = metric.value;
      } else if (metric.name === 'api_call_duration') {
        apiCalls.push(metric.value);
      } else if (metric.name === 'component_render_time' && metric.tags?.component) {
        componentRenders[metric.tags.component] = metric.value;
      } else if (metric.name === 'user_interaction' && metric.tags?.action) {
        userInteractions[metric.tags.action] = (userInteractions[metric.tags.action] || 0) + 1;
      }
    }

    return {
      webVitals,
      apiCalls: {
        average: apiCalls.length > 0 ? apiCalls.reduce((a, b) => a + b, 0) / apiCalls.length : 0,
        count: apiCalls.length
      },
      componentRenders,
      userInteractions
    };
  }

  // Export metrics for analysis
  exportMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // Clear metrics
  clearMetrics() {
    this.metrics = [];
  }

  // Cleanup observers
  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for component performance tracking
export function usePerformanceTracking(componentName: string) {
  const trackRender = (renderTime: number) => {
    performanceMonitor.trackComponentRender(componentName, renderTime);
  };

  const trackInteraction = (action: string, element?: string) => {
    performanceMonitor.trackUserInteraction(action, element);
  };

  return { trackRender, trackInteraction };
}

// HOC for automatic component performance tracking
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const WrappedComponent = (props: P) => {
    const startTime = performance.now();

    React.useEffect(() => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      performanceMonitor.trackComponentRender(
        componentName || Component.displayName || Component.name || 'Unknown',
        renderTime
      );
    });

    return React.createElement(Component, props);
  };

  WrappedComponent.displayName = `withPerformanceTracking(${componentName || Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    performanceMonitor.cleanup();
  });
}
