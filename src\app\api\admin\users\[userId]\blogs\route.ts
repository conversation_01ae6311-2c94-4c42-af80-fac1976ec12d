import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, users, blogCategories } from "@/lib/db/schema";
import { eq, desc, count } from "drizzle-orm";

// Get user's blogs
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user's blogs with category data
    const userBlogs = await db.select({
      id: blogs.id,
      title: blogs.title,
      slug: blogs.slug,
      excerpt: blogs.excerpt,
      content: blogs.content,
      coverImage: blogs.coverImage,
      status: blogs.status,
      featured: blogs.featured,
      viewCount: blogs.viewCount,
      readTime: blogs.readTime,
      tags: blogs.tags,
      seoTitle: blogs.seoTitle,
      seoDescription: blogs.seoDescription,
      publishedAt: blogs.publishedAt,
      createdAt: blogs.createdAt,
      updatedAt: blogs.updatedAt,
      // Category data
      categoryName: blogCategories.name,
      categorySlug: blogCategories.slug,
    })
    .from(blogs)
    .leftJoin(blogCategories, eq(blogs.categoryId, blogCategories.id))
    .where(eq(blogs.authorId, userId))
    .orderBy(desc(blogs.createdAt))
    .limit(limit)
    .offset(offset);

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(blogs)
      .where(eq(blogs.authorId, userId));

    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      blogs: userBlogs,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user blogs:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
