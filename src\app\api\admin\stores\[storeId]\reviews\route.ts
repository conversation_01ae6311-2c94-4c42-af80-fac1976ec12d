import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, storeReviews, users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, desc } from "drizzle-orm";

// Get store reviews
export async function GET(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;

    // Check if store exists
    const storeExists = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (storeExists.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Get store reviews with user information
    const reviews = await db
      .select({
        id: storeReviews.id,
        userId: storeReviews.userId,
        storeId: storeReviews.storeId,
        rating: storeReviews.rating,
        comment: storeReviews.comment,
        createdAt: storeReviews.createdAt,
        updatedAt: storeReviews.updatedAt,
        user: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(storeReviews)
      .leftJoin(users, eq(storeReviews.userId, users.id))
      .where(eq(storeReviews.storeId, storeId))
      .orderBy(desc(storeReviews.createdAt));

    return NextResponse.json({
      reviews,
    });
  } catch (error) {
    console.error("Error fetching store reviews:", error);
    return NextResponse.json(
      { message: "Error fetching store reviews" },
      { status: 500 }
    );
  }
}

// Update review status (approve/reject)
export async function PUT(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;

    const body = await request.json();
    const { reviewId, isApproved } = body;

    if (!reviewId) {
      return NextResponse.json(
        { message: "Review ID is required" },
        { status: 400 }
      );
    }

    // Check if review exists and belongs to the store
    const reviewExists = await db
      .select({ id: storeReviews.id })
      .from(storeReviews)
      .where(
        eq(storeReviews.id, reviewId),
        eq(storeReviews.storeId, storeId)
      )
      .limit(1);

    if (reviewExists.length === 0) {
      return NextResponse.json(
        { message: "Review not found or does not belong to this store" },
        { status: 404 }
      );
    }

    // Update review status
    await db
      .update(storeReviews)
      .set({
        isApproved,
        updatedAt: new Date(),
      })
      .where(eq(storeReviews.id, reviewId));

    return NextResponse.json({
      message: `Review ${isApproved ? "approved" : "rejected"} successfully`,
    });
  } catch (error) {
    console.error("Error updating review status:", error);
    return NextResponse.json(
      { message: "Error updating review status" },
      { status: 500 }
    );
  }
}

// Delete review
export async function DELETE(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;

    const { searchParams } = new URL(request.url);
    const reviewId = searchParams.get("reviewId");

    if (!reviewId) {
      return NextResponse.json(
        { message: "Review ID is required" },
        { status: 400 }
      );
    }

    // Check if review exists and belongs to the store
    const reviewExists = await db
      .select({ id: storeReviews.id })
      .from(storeReviews)
      .where(
        eq(storeReviews.id, reviewId),
        eq(storeReviews.storeId, storeId)
      )
      .limit(1);

    if (reviewExists.length === 0) {
      return NextResponse.json(
        { message: "Review not found or does not belong to this store" },
        { status: 404 }
      );
    }

    // Delete review
    await db
      .delete(storeReviews)
      .where(eq(storeReviews.id, reviewId));

    return NextResponse.json({
      message: "Review deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting review:", error);
    return NextResponse.json(
      { message: "Error deleting review" },
      { status: 500 }
    );
  }
}
