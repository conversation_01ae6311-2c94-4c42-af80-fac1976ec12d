"use client";

import { useState, useEffect, useCallback } from "react";
import { UserGroupIcon, UserPlusIcon, UserMinusIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import Image from "next/image";
import Link from "next/link";

interface Connection {
  id: string;
  name: string;
  username: string;
  image: string | null;
  isSubscribed?: boolean;
  isSubscribedBack?: boolean;
}

interface ProfileConnectionsProps {
  userId: string;
  isOwnProfile: boolean;
}

export function ProfileConnections({ userId, isOwnProfile }: ProfileConnectionsProps) {
  const [activeTab, setActiveTab] = useState<'subscribers' | 'subscribed'>('subscribers');
  const [subscribers, setSubscribers] = useState<Connection[]>([]);
  const [subscribed, setSubscribed] = useState<Connection[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchConnections = useCallback(async () => {
    try {
      setIsLoading(true);

      // Fetch subscribers
      const subscribersResponse = await fetch(`/api/users/${userId}/subscribers`);
      if (subscribersResponse.ok) {
        const subscribersData = await subscribersResponse.json();
        setSubscribers(subscribersData.subscribers || []);
      }

      // Fetch subscribed users
      const subscribedResponse = await fetch(`/api/users/${userId}/subscribed`);
      if (subscribedResponse.ok) {
        const subscribedData = await subscribedResponse.json();
        setSubscribed(subscribedData.subscribed || []);
      }
    } catch (error) {
      console.error('Error fetching connections:', error);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchConnections();
  }, [fetchConnections]);

  const handleSubscribe = async (targetUserId: string) => {
    try {
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ targetUserId })
      });

      if (response.ok) {
        // Update the local state
        setSubscribers(prev =>
          prev.map(user =>
            user.id === targetUserId
              ? { ...user, isSubscribedBack: true }
              : user
          )
        );
      }
    } catch (error) {
      console.error('Error subscribing:', error);
    }
  };

  const handleUnsubscribe = async (targetUserId: string) => {
    try {
      const response = await fetch('/api/subscriptions', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ targetUserId })
      });

      if (response.ok) {
        // Update the local state
        setSubscribers(prev =>
          prev.map(user =>
            user.id === targetUserId
              ? { ...user, isSubscribedBack: false }
              : user
          )
        );
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
    }
  };

  const ConnectionCard = ({ connection }: { connection: Connection }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
      <div className="flex items-center space-x-3">
        <Link href={`/user/${connection.username}`} className="flex-shrink-0">
          <div className="h-12 w-12 rounded-full overflow-hidden bg-gray-200">
            {connection.image ? (
              <Image
                src={connection.image}
                alt={connection.name}
                width={48}
                height={48}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold">
                {connection.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
        </Link>

        <div className="flex-1 min-w-0">
          <Link href={`/user/${connection.username}`}>
            <h3 className="text-sm font-medium text-gray-900 hover:text-blue-600 truncate">
              {connection.name}
            </h3>
            <p className="text-sm text-gray-500 truncate">@{connection.username}</p>
          </Link>
        </div>

        {isOwnProfile && activeTab === 'subscribers' && (
          <div className="flex-shrink-0">
            {connection.isSubscribedBack ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleUnsubscribe(connection.id)}
                className="text-blue-600 border-blue-600 hover:bg-blue-50"
              >
                <UserMinusIcon className="h-4 w-4 mr-1" />
                Subscribed
              </Button>
            ) : (
              <Button
                variant="primary"
                size="sm"
                onClick={() => handleSubscribe(connection.id)}
              >
                <UserPlusIcon className="h-4 w-4 mr-1" />
                Subscribe
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const currentConnections = activeTab === 'subscribers' ? subscribers : subscribed;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header with Tabs */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Connections</h2>

        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('subscribers')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${
              activeTab === 'subscribers'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Subscribers ({subscribers.length})
          </button>
          <button
            onClick={() => setActiveTab('subscribed')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${
              activeTab === 'subscribed'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Subscribed ({subscribed.length})
          </button>
        </div>
      </div>

      {/* Connections Grid */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        {currentConnections.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {currentConnections.map((connection) => (
              <ConnectionCard key={connection.id} connection={connection} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No {activeTab === 'subscribers' ? 'subscribers' : 'subscriptions'} yet
            </h3>
            <p className="text-gray-500">
              {activeTab === 'subscribers'
                ? isOwnProfile
                  ? "When people subscribe to you, they'll appear here."
                  : "This user doesn't have any subscribers yet."
                : isOwnProfile
                  ? "Start connecting with people by subscribing to them."
                  : "This user isn't subscribed to anyone yet."
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
