"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { PartyKitManager, PartyKitClient } from "@/lib/partykit/client";

export interface RealtimeNotification {
  id: string;
  type: "like" | "comment" | "message" | "follow" | "fan_page_message" | "fan_page_reply" | "subscription";
  recipientId: string;
  senderId: string;
  title?: string;
  message?: string;
  data?: any;
  timestamp: string;
  read: boolean;
  priority: "low" | "medium" | "high";
  actionUrl?: string;
  metadata?: Record<string, any>;
}

interface NotificationCounts {
  total: number;
  unread: number;
  byType: Record<string, number>;
  priority: {
    high: number;
    medium: number;
    low: number;
  };
}

interface UseRealtimeNotificationsOptions {
  enableSound?: boolean;
  enableBrowserNotifications?: boolean;
  fallbackToPolling?: boolean;
  pollingInterval?: number;
}

export function useRealtimeNotifications(options: UseRealtimeNotificationsOptions = {}) {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<RealtimeNotification[]>([]);
  const [connectionStatus, setConnectionStatus] = useState({
    connected: false,
    authenticated: false,
    latency: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const notificationClient = useRef<PartyKitClient | null>(null);
  const pollingFallbackRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const {
    enableSound = true,
    enableBrowserNotifications = true,
    fallbackToPolling = true,
    pollingInterval = 30000
  } = options;

  // Calculate notification counts
  const notificationCounts: NotificationCounts = {
    total: notifications.length,
    unread: notifications.filter(n => !n.read).length,
    byType: notifications.reduce((acc, n) => {
      acc[n.type] = (acc[n.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    priority: {
      high: notifications.filter(n => !n.read && n.priority === "high").length,
      medium: notifications.filter(n => !n.read && n.priority === "medium").length,
      low: notifications.filter(n => !n.read && n.priority === "low").length,
    }
  };

  // Initialize audio for notifications
  useEffect(() => {
    if (enableSound && typeof window !== "undefined") {
      audioRef.current = new Audio("/sounds/notification.mp3");
      audioRef.current.volume = 0.5;
    }
  }, [enableSound]);

  // Request browser notification permission
  useEffect(() => {
    if (enableBrowserNotifications && typeof window !== "undefined" && "Notification" in window) {
      if (Notification.permission === "default") {
        Notification.requestPermission();
      }
    }
  }, [enableBrowserNotifications]);

  // Initialize PartyKit connection
  useEffect(() => {
    if (!session?.user?.id) return;

    const initializeConnection = async () => {
      try {
        notificationClient.current = PartyKitManager.getClient("notifications", session.user.id);
        
        // Set up event listeners
        notificationClient.current.on("connected", () => {
          setConnectionStatus(prev => ({ ...prev, connected: true }));
          setError(null);
        });

        notificationClient.current.on("authenticated", () => {
          setConnectionStatus(prev => ({ ...prev, authenticated: true }));
          setIsLoading(false);
          // Subscribe to notifications for this user
          notificationClient.current?.send({
            type: "subscribe_notifications",
            userId: session.user.id
          });
        });

        notificationClient.current.on("disconnected", () => {
          setConnectionStatus(prev => ({ ...prev, connected: false, authenticated: false }));
          if (fallbackToPolling) {
            startPollingFallback();
          }
        });

        notificationClient.current.on("error", (error: any) => {
          setError(error.message || "Connection error");
          if (fallbackToPolling) {
            startPollingFallback();
          }
        });

        notificationClient.current.on("latency_update", (latency: number) => {
          setConnectionStatus(prev => ({ ...prev, latency }));
        });

        // Notification event listeners
        notificationClient.current.on("new_notification", handleNewNotification);
        notificationClient.current.on("notification_read", handleNotificationRead);

        // Connect to PartyKit
        await notificationClient.current.connect();

        // Load initial notifications
        await loadNotifications();

      } catch (error) {
        console.error("Failed to initialize PartyKit notifications:", error);
        setError("Failed to connect to real-time notifications");
        setIsLoading(false);
        
        if (fallbackToPolling) {
          startPollingFallback();
        }
      }
    };

    initializeConnection();

    return () => {
      if (notificationClient.current) {
        notificationClient.current.disconnect();
      }
      
      if (pollingFallbackRef.current) {
        clearInterval(pollingFallbackRef.current);
      }
    };
  }, [session?.user?.id]);

  const handleNewNotification = useCallback((data: any) => {
    const notification: RealtimeNotification = {
      ...data.notification,
      read: false,
      priority: data.notification.priority || "medium"
    };
    
    setNotifications(prev => [notification, ...prev]);

    // Play sound notification
    if (enableSound && audioRef.current) {
      audioRef.current.play().catch(console.error);
    }

    // Show browser notification
    if (enableBrowserNotifications && typeof window !== "undefined" && "Notification" in window) {
      if (Notification.permission === "granted") {
        new Notification(notification.title || "New notification", {
          body: notification.message,
          icon: "/icons/notification-icon.png",
          tag: notification.id,
          requireInteraction: notification.priority === "high"
        });
      }
    }

    // Emit custom event for other components
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent("realtime_notification", {
        detail: notification
      }));
    }
  }, [enableSound, enableBrowserNotifications]);

  const handleNotificationRead = useCallback((data: any) => {
    setNotifications(prev => prev.map(notification => 
      notification.id === data.notificationId 
        ? { ...notification, read: true }
        : notification
    ));
  }, []);

  const loadNotifications = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications');
      if (response.ok) {
        const data = await response.json();
        const formattedNotifications: RealtimeNotification[] = data.notifications.map((n: any) => ({
          id: n.id,
          type: n.type,
          recipientId: n.recipientId,
          senderId: n.senderId,
          title: n.title,
          message: n.message,
          data: n.data,
          timestamp: n.createdAt,
          read: n.read,
          priority: n.priority || "medium",
          actionUrl: n.actionUrl,
          metadata: n.metadata
        }));
        setNotifications(formattedNotifications);
      }
    } catch (error) {
      console.error("Failed to load notifications:", error);
    }
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      // Optimistically update UI
      setNotifications(prev => prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      ));

      // Send to PartyKit if connected
      if (notificationClient.current?.isConnected()) {
        notificationClient.current.send({
          type: "mark_read",
          notificationId
        });
      }

      // Also update via API
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ read: true }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
      // Revert optimistic update
      setNotifications(prev => prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: false }
          : notification
      ));
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      // Optimistically update UI
      setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));

      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
      // Reload notifications on error
      loadNotifications();
    }
  }, [loadNotifications]);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      // Optimistically update UI
      setNotifications(prev => prev.filter(n => n.id !== notificationId));

      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }
    } catch (error) {
      console.error("Failed to delete notification:", error);
      // Reload notifications on error
      loadNotifications();
    }
  }, [loadNotifications]);

  const startPollingFallback = useCallback(() => {
    if (pollingFallbackRef.current) return;

    console.log("Starting polling fallback for notifications");
    pollingFallbackRef.current = setInterval(async () => {
      try {
        await loadNotifications();
      } catch (error) {
        console.error("Polling fallback error:", error);
      }
    }, pollingInterval);
  }, [loadNotifications, pollingInterval]);

  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await loadNotifications();
    } finally {
      setIsLoading(false);
    }
  }, [loadNotifications]);

  return {
    // State
    notifications,
    notificationCounts,
    connectionStatus,
    isLoading,
    error,

    // Actions
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,

    // Utilities
    isConnected: connectionStatus.connected && connectionStatus.authenticated,
    latency: connectionStatus.latency
  };
}
