"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

interface WalletBalance {
  generalBalance: string;
  earningBalance: string;
  totalDeposited: string;
  totalWithdrawn: string;
  totalSent: string;
  totalReceived: string;
}

interface UseWalletBalanceReturn {
  balance: WalletBalance | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useWalletBalance(): UseWalletBalanceReturn {
  const { data: session } = useSession();
  const [balance, setBalance] = useState<WalletBalance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBalance = async () => {
    if (!session?.user) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await fetch('/api/wallet/balance');
      const data = await response.json();

      if (data.success) {
        setBalance(data.data);
      } else {
        setError('Failed to fetch wallet balance');
      }
    } catch (err) {
      console.error('Error fetching wallet balance:', err);
      setError('Failed to fetch wallet balance');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBalance();
  }, [session?.user]);

  const refetch = async () => {
    setLoading(true);
    await fetchBalance();
  };

  return {
    balance,
    loading,
    error,
    refetch,
  };
}
