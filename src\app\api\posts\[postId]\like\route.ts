import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { likes, posts, notifications, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { and, eq } from "drizzle-orm";
import { emitNotification } from "@/lib/utils/notifications";

export async function POST(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the post ID from the context params
    const params = await context.params;
    const { postId } = params;

    // Check if the user has already liked the post
    const existingLike = await db.query.likes.findFirst({
      where: and(
        eq(likes.postId, postId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'like')
      ),
    });

    if (existingLike) {
      // Unlike the post
      await db
        .delete(likes)
        .where(
          and(
            eq(likes.postId, postId),
            eq(likes.userId, session.user.id),
            eq(likes.type, 'like')
          )
        );

      return NextResponse.json(
        { message: "Post unliked successfully" },
        { status: 200 }
      );
    }

    // Check if the user has already disliked the post
    const existingDislike = await db.query.likes.findFirst({
      where: and(
        eq(likes.postId, postId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'dislike')
      ),
    });

    // If the user has disliked the post, remove the dislike
    if (existingDislike) {
      await db
        .delete(likes)
        .where(
          and(
            eq(likes.postId, postId),
            eq(likes.userId, session.user.id),
            eq(likes.type, 'dislike')
          )
        );
    }

    // Get the post to check the owner
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
      columns: {
        id: true,
        userId: true,
      },
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Like the post
    await db.insert(likes).values({
      id: uuidv4(),
      userId: session.user.id,
      postId,
      commentId: null,
      type: 'like',
    });

    // Create notification for post owner (if not liking own post)
    if (post.userId !== session.user.id) {
      const notificationId = uuidv4();

      // Get sender info for notification
      const sender = await db.query.users.findFirst({
        where: eq(users.id, session.user.id),
        columns: {
          id: true,
          name: true,
          image: true,
        },
      });

      // Insert notification into database
      await db.insert(notifications).values({
        id: notificationId,
        recipientId: post.userId,
        type: "like",
        senderId: session.user.id,
        postId,
        read: false,
      });

      // Emit real-time notification
      if (sender) {
        emitNotification({
          id: notificationId,
          type: "like",
          recipientId: post.userId,
          senderId: session.user.id,
          read: false,
          createdAt: new Date().toISOString(),
          sender,
          postId,
        });
      }
    }

    return NextResponse.json(
      { message: "Post liked successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error liking post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
