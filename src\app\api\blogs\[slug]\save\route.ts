import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { savedPosts, blogs } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

type RouteParams = {
  params: Promise<{ slug: string }>;
};

// POST /api/blogs/[slug]/save - Toggle save on blog post
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // First, get the blog to find its ID
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true },
    });

    if (!blog) {
      return NextResponse.json(
        { error: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user has already saved this blog
    const existingSave = await db.query.savedPosts.findFirst({
      where: and(
        eq(savedPosts.postId, blog.id),
        eq(savedPosts.userId, session.user.id)
      ),
    });

    if (existingSave) {
      // Remove save
      await db.delete(savedPosts).where(eq(savedPosts.id, existingSave.id));
      
      return NextResponse.json({
        message: "Blog post removed from saved successfully",
        isSaved: false,
      });
    } else {
      // Add save
      const saveId = uuidv4();
      await db.insert(savedPosts).values({
        id: saveId,
        userId: session.user.id,
        postId: blog.id,
      });

      return NextResponse.json({
        message: "Blog post saved successfully",
        isSaved: true,
      });
    }
  } catch (error) {
    console.error("Error toggling blog save:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/blogs/[slug]/save - Remove save from blog post
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // First, get the blog to find its ID
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true },
    });

    if (!blog) {
      return NextResponse.json(
        { error: "Blog not found" },
        { status: 404 }
      );
    }

    // Find and remove the save
    const existingSave = await db.query.savedPosts.findFirst({
      where: and(
        eq(savedPosts.postId, blog.id),
        eq(savedPosts.userId, session.user.id)
      ),
    });

    if (existingSave) {
      await db.delete(savedPosts).where(eq(savedPosts.id, existingSave.id));
      
      return NextResponse.json({
        message: "Blog post removed from saved successfully",
        isSaved: false,
      });
    } else {
      return NextResponse.json({
        message: "Save not found",
        isSaved: false,
      });
    }
  } catch (error) {
    console.error("Error removing blog save:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
