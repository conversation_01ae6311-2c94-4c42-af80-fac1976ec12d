import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptions, users } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;

    // Get all users this user is subscribed to
    const subscribed = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
      })
      .from(subscriptions)
      .innerJoin(users, eq(subscriptions.targetUserId, users.id))
      .where(eq(subscriptions.subscriberId, userId))
      .orderBy(desc(subscriptions.createdAt));

    return NextResponse.json({
      subscribed,
      total: subscribed.length
    });

  } catch (error) {
    console.error("Error fetching subscribed users:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
