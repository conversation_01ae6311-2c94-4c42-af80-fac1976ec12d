import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

// GET /api/fan-pages/owned - Get user's owned fan pages
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all fan pages owned by the current user
    const ownedPages = await db.query.fanPages.findMany({
      where: and(
        eq(fanPages.ownerId, session.user.id),
        eq(fanPages.isActive, true)
      ),
      columns: {
        id: true,
        name: true,
        username: true,
        profileImage: true,
        isVerified: true,
      },
      orderBy: (fanPages, { asc }) => [asc(fanPages.name)],
    });

    return NextResponse.json({
      success: true,
      data: ownedPages,
    });
  } catch (error) {
    console.error("Error fetching owned fan pages:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
