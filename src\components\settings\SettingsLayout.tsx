"use client";

import { ReactNode } from "react";
import { SettingsSidebar } from "./SettingsSidebar";

interface SettingsLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

export function SettingsLayout({ children, title, description }: SettingsLayoutProps) {
  return (
    <div className="rounded-lg bg-white shadow">
      <div className="px-6 py-5 border-b border-gray-200">
        <h3 className="text-lg font-medium leading-6 text-gray-900">{title}</h3>
        {description && (
          <p className="mt-1 text-sm text-gray-500">{description}</p>
        )}
      </div>
      
      <div className="px-6 py-5 space-y-6">
        {children}
      </div>
    </div>
  );
}
