"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Card } from "@/components/ui/Card";
import { toast } from "react-hot-toast";
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PencilIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
} from "@heroicons/react/24/outline";
import { EditPersonalInfoModal } from "./modals/EditPersonalInfoModal";
import { AccountStatusModal } from "./modals/AccountStatusModal";

interface User {
  id: string;
  name: string;
  username: string | null;
  email: string;
  phone: string | null;
  role: string;
  isAdmin: boolean;
  adminRoleId: string | null;
  adminRoleName: string | null;
  image: string | null;
  coverImage: string | null;
  bio: string | null;
  location: string | null;
  birthday: string | null;
  emailVerified: string | null;
  // Status fields
  status: string;
  isActive: boolean;
  suspendedAt: string | null;
  suspendedReason: string | null;
  suspendedBy: string | null;
  deletedAt: string | null;
  deletedReason: string | null;
  deletedBy: string | null;
  createdAt: string;
  updatedAt: string;
  wallet: {
    generalBalance: string;
    earningBalance: string;
    totalDeposited: string;
    totalWithdrawn: string;
    totalSent: string;
    totalReceived: string;
    isActive: boolean;
  };
  isVerified: boolean;
  totalBalance: string;
}

interface UserOverviewProps {
  user: User;
  onUserUpdate: () => void;
}

export function UserOverview({ user, onUserUpdate }: UserOverviewProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  // Modal states
  const [showEditPersonalInfo, setShowEditPersonalInfo] = useState(false);
  const [showAccountStatus, setShowAccountStatus] = useState(false);

  const handleVerifyEmail = async () => {
    try {
      setIsUpdating(true);
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          emailVerified: !user.isVerified,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update verification status");
      }

      toast.success(
        user.isVerified
          ? "Email verification removed"
          : "Email verified successfully"
      );
      onUserUpdate();
    } catch (error) {
      console.error("Error updating verification:", error);
      toast.error("Failed to update verification status");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleToggleAdmin = async () => {
    try {
      setIsUpdating(true);
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isAdmin: !user.isAdmin,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update admin status");
      }

      toast.success(
        user.isAdmin
          ? "Admin privileges removed"
          : "Admin privileges granted"
      );
      onUserUpdate();
    } catch (error) {
      console.error("Error updating admin status:", error);
      toast.error("Failed to update admin status");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Personal Information */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowEditPersonalInfo(true)}
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <UserIcon className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">Full Name</div>
                <div className="text-sm text-gray-600">{user.name}</div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <EnvelopeIcon className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">Email</div>
                <div className="text-sm text-gray-600 flex items-center space-x-2">
                  <span>{user.email}</span>
                  {user.isVerified ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircleIcon className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
            </div>

            {user.phone && (
              <div className="flex items-center space-x-3">
                <PhoneIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Phone</div>
                  <div className="text-sm text-gray-600">{user.phone}</div>
                </div>
              </div>
            )}

            {user.location && (
              <div className="flex items-center space-x-3">
                <MapPinIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Location</div>
                  <div className="text-sm text-gray-600">{user.location}</div>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            {user.username && (
              <div>
                <div className="text-sm font-medium text-gray-900">Username</div>
                <div className="text-sm text-gray-600">@{user.username}</div>
              </div>
            )}

            <div>
              <div className="text-sm font-medium text-gray-900">Role</div>
              <div className="text-sm text-gray-600 flex items-center space-x-2">
                <span className="capitalize">{user.role}</span>
                {user.isAdmin && <Badge variant="primary">Admin</Badge>}
              </div>
            </div>

            {user.adminRoleName && (
              <div>
                <div className="text-sm font-medium text-gray-900">Admin Role</div>
                <div className="text-sm text-gray-600">{user.adminRoleName}</div>
              </div>
            )}

            {user.birthday && (
              <div className="flex items-center space-x-3">
                <CalendarIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Birthday</div>
                  <div className="text-sm text-gray-600">
                    {new Date(user.birthday).toLocaleDateString()}
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-3">
              <ClockIcon className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">Member Since</div>
                <div className="text-sm text-gray-600">
                  {new Date(user.createdAt).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        </div>

        {user.bio && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="text-sm font-medium text-gray-900 mb-2">Bio</div>
            <div className="text-sm text-gray-600">{user.bio}</div>
          </div>
        )}
      </Card>

      {/* Account Status */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Account Status</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAccountStatus(true)}
          >
            <Cog6ToothIcon className="h-4 w-4 mr-2" />
            Manage
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-900">Email Verification</div>
                <div className="text-sm text-gray-600">
                  {user.isVerified ? "Verified" : "Not verified"}
                </div>
              </div>
              <Button
                variant={user.isVerified ? "outline" : "primary"}
                size="sm"
                onClick={handleVerifyEmail}
                disabled={isUpdating}
              >
                {user.isVerified ? "Remove Verification" : "Verify Email"}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-900">Admin Status</div>
                <div className="text-sm text-gray-600">
                  {user.isAdmin ? "Administrator" : "Regular User"}
                </div>
              </div>
              <Button
                variant={user.isAdmin ? "outline" : "primary"}
                size="sm"
                onClick={handleToggleAdmin}
                disabled={isUpdating}
              >
                {user.isAdmin ? "Remove Admin" : "Make Admin"}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-900">Wallet Status</div>
                <div className="text-sm text-gray-600">
                  {user.wallet.isActive ? "Active" : "Inactive"}
                </div>
              </div>
              <Badge variant={user.wallet.isActive ? "success" : "secondary"}>
                {user.wallet.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-900">Last Updated</div>
              <div className="text-sm text-gray-600">
                {new Date(user.updatedAt).toLocaleString()}
              </div>
            </div>

            {user.emailVerified && (
              <div>
                <div className="text-sm font-medium text-gray-900">Email Verified At</div>
                <div className="text-sm text-gray-600">
                  {new Date(user.emailVerified).toLocaleString()}
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Quick Stats */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              ৳{parseFloat(user.totalBalance).toLocaleString()}
            </div>
            <div className="text-sm text-blue-600">Total Balance</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              ৳{parseFloat(user.wallet.totalDeposited).toLocaleString()}
            </div>
            <div className="text-sm text-green-600">Total Deposited</div>
          </div>

          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              ৳{parseFloat(user.wallet.totalWithdrawn).toLocaleString()}
            </div>
            <div className="text-sm text-purple-600">Total Withdrawn</div>
          </div>

          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              ৳{parseFloat(user.wallet.totalSent).toLocaleString()}
            </div>
            <div className="text-sm text-orange-600">Total Sent</div>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <EditPersonalInfoModal
        isOpen={showEditPersonalInfo}
        onClose={() => setShowEditPersonalInfo(false)}
        user={user}
        onSuccess={onUserUpdate}
      />

      <AccountStatusModal
        isOpen={showAccountStatus}
        onClose={() => setShowAccountStatus(false)}
        user={user}
        onSuccess={onUserUpdate}
      />
    </div>
  );
}
