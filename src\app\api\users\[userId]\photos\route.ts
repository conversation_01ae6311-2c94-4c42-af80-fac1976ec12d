import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;

    // Get all posts with images from this user
    const userPosts = await db
      .select({
        id: posts.id,
        images: posts.images,
        content: posts.content,
        createdAt: posts.createdAt,
      })
      .from(posts)
      .where(eq(posts.userId, userId))
      .orderBy(desc(posts.createdAt));

    // Extract photos from posts
    const photos: Array<{
      id: string;
      url: string;
      caption?: string;
      createdAt: string;
    }> = [];

    userPosts.forEach(post => {
      if (post.images && Array.isArray(post.images)) {
        post.images.forEach((imageUrl: string, index: number) => {
          photos.push({
            id: `${post.id}-${index}`,
            url: imageUrl,
            caption: post.content || undefined,
            createdAt: post.createdAt.toISOString(),
          });
        });
      }
    });

    return NextResponse.json({
      photos,
      total: photos.length
    });

  } catch (error) {
    console.error("Error fetching user photos:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
