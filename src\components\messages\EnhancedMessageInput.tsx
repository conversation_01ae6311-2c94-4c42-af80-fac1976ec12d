"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { cn } from "@/lib/utils";
import { useTypingIndicator } from "./TypingIndicator";
import { useRealtimeMessaging } from "@/hooks/useRealtimeMessaging";
import { useFeatureFlags } from "@/lib/features/flags";
import { useSession } from "next-auth/react";
import {
  PaperAirplaneIcon,
  FaceSmileIcon,
  PaperClipIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

interface EnhancedMessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend?: (content: string) => Promise<boolean>; // Made optional for real-time integration
  placeholder?: string;
  disabled?: boolean;
  isSending?: boolean;
  maxLength?: number;
  showEmojiPicker?: boolean;
  onEmojiClick?: () => void;
  showAttachment?: boolean;
  onAttachmentClick?: () => void;
  className?: string;
  // Real-time specific props
  conversationId?: string;
  receiverId?: string;
  fanPageId?: string;
  enableRealtime?: boolean;
}

export function EnhancedMessageInput({
  value,
  onChange,
  onSend,
  placeholder = "Type a message...",
  disabled = false,
  isSending = false,
  maxLength = 5000,
  showEmojiPicker = true,
  onEmojiClick,
  showAttachment = false,
  onAttachmentClick,
  className,
  conversationId,
  receiverId,
  fanPageId,
  enableRealtime = true,
}: EnhancedMessageInputProps) {
  const { data: session } = useSession();
  const { shouldUseRealtime } = useFeatureFlags(session?.user?.id);

  // Real-time messaging hook
  const realtimeMessaging = useRealtimeMessaging({
    conversationId: conversationId || "default",
    enableTypingIndicators: true,
    enableDeliveryStatus: true,
    fallbackToPolling: true
  });

  // Typing indicator hook
  const typingIndicator = useTypingIndicator(
    conversationId || "default",
    session?.user?.id || "",
    (isTyping) => {
      // Handle typing state change if needed
    }
  );

  const useRealtime = enableRealtime && shouldUseRealtime('realtimeMessaging');
  const [sendStatus, setSendStatus] = useState<'idle' | 'sending' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const characterCount = value.length;
  const isOverLimit = characterCount > maxLength;
  const canSend = value.trim().length > 0 && !isOverLimit && !disabled && !isSending;

  const handleSend = async () => {
    if (!canSend) return;

    setSendStatus('sending');
    setErrorMessage("");

    // Stop typing indicator
    typingIndicator.stopTyping();

    try {
      let success = false;

      if (useRealtime && realtimeMessaging.isConnected) {
        // Use real-time messaging
        success = await realtimeMessaging.sendMessage(value.trim(), receiverId, fanPageId);
      } else if (onSend) {
        // Fallback to legacy onSend prop
        success = await onSend(value.trim());
      } else {
        throw new Error("No send method available");
      }

      if (success) {
        setSendStatus('success');
        onChange(""); // Clear input on success

        // Reset status after animation
        timeoutRef.current = setTimeout(() => {
          setSendStatus('idle');
        }, 2000);
      } else {
        setSendStatus('error');
        setErrorMessage("Failed to send message");

        // Reset status after showing error
        timeoutRef.current = setTimeout(() => {
          setSendStatus('idle');
          setErrorMessage("");
        }, 3000);
      }
    } catch (error) {
      setSendStatus('error');
      setErrorMessage("Network error occurred");

      timeoutRef.current = setTimeout(() => {
        setSendStatus('idle');
        setErrorMessage("");
      }, 3000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const getStatusIcon = () => {
    switch (sendStatus) {
      case 'sending':
        return <div className="h-5 w-5 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />;
      case 'success':
        return <CheckIcon className="h-5 w-5 text-green-600" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <PaperAirplaneIcon className="h-5 w-5" />;
    }
  };

  const getButtonColor = () => {
    switch (sendStatus) {
      case 'sending':
        return "bg-blue-500 text-white";
      case 'success':
        return "bg-green-600 text-white";
      case 'error':
        return "bg-red-600 text-white";
      default:
        return canSend
          ? "bg-blue-600 hover:bg-blue-700 text-white"
          : "bg-gray-200 text-gray-400 cursor-not-allowed";
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={cn("space-y-2", className)}>
      {/* Error message */}
      {errorMessage && (
        <div className="flex items-center space-x-2 text-sm text-red-600 bg-red-50 p-2 rounded-lg">
          <ExclamationTriangleIcon className="h-4 w-4 flex-shrink-0" />
          <span>{errorMessage}</span>
          <button
            onClick={() => setErrorMessage("")}
            className="ml-auto text-red-400 hover:text-red-600"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* Input area */}
      <div className="flex items-end space-x-2">
        {/* Attachment button */}
        {showAttachment && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onAttachmentClick}
            disabled={disabled}
            className="p-2 rounded-full"
          >
            <PaperClipIcon className="h-5 w-5" />
          </Button>
        )}

        {/* Input container */}
        <div className="flex-1 relative">
          <Input
            ref={inputRef}
            value={value}
            onChange={(e) => {
              const newValue = e.target.value;
              if (newValue.length <= maxLength) {
                onChange(newValue);

                // Handle typing indicator
                if (useRealtime && typingIndicator.isRealtimeEnabled) {
                  typingIndicator.handleInputChange(newValue);
                }
              }
            }}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled || sendStatus === 'sending'}
            className={cn(
              "pr-12 resize-none transition-all duration-200",
              isOverLimit && "border-red-300 focus:border-red-500",
              sendStatus === 'success' && "border-green-300",
              sendStatus === 'error' && "border-red-300"
            )}
            maxLength={maxLength + 100} // Allow slight overflow for better UX
          />

          {/* Emoji button */}
          {showEmojiPicker && (
            <button
              type="button"
              onClick={onEmojiClick}
              disabled={disabled}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FaceSmileIcon className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Send button */}
        <Button
          onClick={handleSend}
          disabled={!canSend || sendStatus === 'sending'}
          className={cn(
            "rounded-full p-3 transition-all duration-200 transform",
            getButtonColor(),
            sendStatus === 'success' && "scale-110",
            sendStatus === 'error' && "animate-pulse"
          )}
        >
          {getStatusIcon()}
        </Button>
      </div>

      {/* Character count */}
      <div className="flex justify-between items-center text-xs">
        <div className="flex items-center space-x-2">
          {sendStatus === 'success' && (
            <span className="text-green-600 flex items-center space-x-1">
              <CheckIcon className="h-3 w-3" />
              <span>Message sent</span>
            </span>
          )}
          {sendStatus === 'sending' && (
            <span className="text-blue-600 flex items-center space-x-1">
              <div className="h-3 w-3 animate-spin rounded-full border border-blue-600 border-t-transparent" />
              <span>Sending...</span>
            </span>
          )}
        </div>

        <span className={cn(
          "transition-colors",
          isOverLimit ? "text-red-500 font-medium" : "text-gray-500"
        )}>
          {characterCount}/{maxLength}
        </span>
      </div>
    </div>
  );
}
