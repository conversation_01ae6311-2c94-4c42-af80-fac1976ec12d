"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/Tabs";
import {
  ArrowLeftIcon,
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon,
  StarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
} from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";

interface AnalyticsData {
  overview: {
    totalPages: number;
    activePages: number;
    verifiedPages: number;
    totalFollowers: number;
    totalPosts: number;
    growthRate: number;
  };
  categoryBreakdown: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  monthlyGrowth: Array<{
    month: string;
    newPages: number;
    totalPages: number;
  }>;
  topPages: Array<{
    id: string;
    name: string;
    username: string;
    followerCount: number;
    postCount: number;
    isVerified: boolean;
  }>;
}

export default function AdminFanPagesAnalyticsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("30d");

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/fan-pages/analytics?range=${timeRange}`);

      if (!response.ok) {
        throw new Error("Failed to fetch analytics");
      }

      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error("Error fetching analytics:", error);
      toast.error("Failed to load analytics data");
      // Mock data for demonstration
      setAnalytics({
        overview: {
          totalPages: 156,
          activePages: 142,
          verifiedPages: 23,
          totalFollowers: 45678,
          totalPosts: 2341,
          growthRate: 12.5,
        },
        categoryBreakdown: [
          { category: "Musician", count: 45, percentage: 28.8 },
          { category: "Brand", count: 32, percentage: 20.5 },
          { category: "Business", count: 28, percentage: 17.9 },
          { category: "Artist", count: 21, percentage: 13.5 },
          { category: "Public Figure", count: 18, percentage: 11.5 },
          { category: "Other", count: 12, percentage: 7.7 },
        ],
        monthlyGrowth: [
          { month: "Jan", newPages: 12, totalPages: 98 },
          { month: "Feb", newPages: 15, totalPages: 113 },
          { month: "Mar", newPages: 18, totalPages: 131 },
          { month: "Apr", newPages: 14, totalPages: 145 },
          { month: "May", newPages: 11, totalPages: 156 },
        ],
        topPages: [
          {
            id: "1",
            name: "Music World",
            username: "musicworld",
            followerCount: 5432,
            postCount: 234,
            isVerified: true,
          },
          {
            id: "2",
            name: "Tech Brand",
            username: "techbrand",
            followerCount: 4321,
            postCount: 189,
            isVerified: true,
          },
          {
            id: "3",
            name: "Art Gallery",
            username: "artgallery",
            followerCount: 3210,
            postCount: 156,
            isVerified: false,
          },
        ],
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!analytics) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Failed to load analytics</h3>
          <p className="mt-1 text-sm text-gray-500">
            Unable to fetch analytics data. Please try again.
          </p>
          <Button onClick={fetchAnalytics} className="mt-4">
            Retry
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push("/admin/fan-pages")}
              className="flex items-center"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Fan Pages
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Fan Pages Analytics</h1>
              <p className="text-gray-600">
                Insights and statistics for fan pages
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="rounded-md p-3 bg-blue-100">
                  <StarIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Pages
                  </dt>
                  <dd className="text-2xl font-semibold text-blue-600">
                    {analytics.overview.totalPages}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="rounded-md p-3 bg-green-100">
                  <ChartBarIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Pages
                  </dt>
                  <dd className="text-2xl font-semibold text-green-600">
                    {analytics.overview.activePages}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="rounded-md p-3 bg-purple-100">
                  <StarIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Verified Pages
                  </dt>
                  <dd className="text-2xl font-semibold text-purple-600">
                    {analytics.overview.verifiedPages}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="rounded-md p-3 bg-orange-100">
                  <UsersIcon className="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Followers
                  </dt>
                  <dd className="text-2xl font-semibold text-orange-600">
                    {analytics.overview.totalFollowers.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="rounded-md p-3 bg-indigo-100">
                  <DocumentTextIcon className="h-6 w-6 text-indigo-600" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Posts
                  </dt>
                  <dd className="text-2xl font-semibold text-indigo-600">
                    {analytics.overview.totalPosts.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="rounded-md p-3 bg-pink-100">
                  {analytics.overview.growthRate >= 0 ? (
                    <ArrowTrendingUpIcon className="h-6 w-6 text-pink-600" />
                  ) : (
                    <ArrowTrendingDownIcon className="h-6 w-6 text-pink-600" />
                  )}
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Growth Rate
                  </dt>
                  <dd className="text-2xl font-semibold text-pink-600">
                    {analytics.overview.growthRate > 0 ? '+' : ''}{analytics.overview.growthRate}%
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="growth">Growth</TabsTrigger>
            <TabsTrigger value="top-pages">Top Pages</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Page Status Distribution</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Active Pages</span>
                    <span className="text-sm font-medium text-gray-900">
                      {analytics.overview.activePages} ({Math.round((analytics.overview.activePages / analytics.overview.totalPages) * 100)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${(analytics.overview.activePages / analytics.overview.totalPages) * 100}%` }}
                    ></div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Verified Pages</span>
                    <span className="text-sm font-medium text-gray-900">
                      {analytics.overview.verifiedPages} ({Math.round((analytics.overview.verifiedPages / analytics.overview.totalPages) * 100)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{ width: `${(analytics.overview.verifiedPages / analytics.overview.totalPages) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Engagement Metrics</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Avg. Followers per Page</span>
                    <span className="text-sm font-medium text-gray-900">
                      {Math.round(analytics.overview.totalFollowers / analytics.overview.totalPages)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Avg. Posts per Page</span>
                    <span className="text-sm font-medium text-gray-900">
                      {Math.round(analytics.overview.totalPosts / analytics.overview.totalPages)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Verification Rate</span>
                    <span className="text-sm font-medium text-gray-900">
                      {Math.round((analytics.overview.verifiedPages / analytics.overview.totalPages) * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="categories" className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Category Breakdown</h3>
              <div className="space-y-4">
                {analytics.categoryBreakdown.map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 rounded-full bg-blue-500" style={{ backgroundColor: `hsl(${index * 60}, 70%, 50%)` }}></div>
                      <span className="text-sm font-medium text-gray-900">{category.category}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">{category.count} pages</span>
                      <span className="text-sm font-medium text-gray-900">{category.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="growth" className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Growth</h3>
              <div className="space-y-4">
                {analytics.monthlyGrowth.map((month, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">{month.month}</span>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">+{month.newPages} new</span>
                      <span className="text-sm font-medium text-gray-900">{month.totalPages} total</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="top-pages" className="space-y-6">
            <div className="bg-white rounded-lg shadow-md">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Top Performing Pages</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Page
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Followers
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Posts
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {analytics.topPages.map((page, index) => (
                      <tr key={page.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">
                              {page.name}
                            </div>
                            <div className="text-sm text-gray-500 ml-2">@{page.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {page.followerCount.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {page.postCount.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {page.isVerified && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Verified
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
