import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, postReports } from "@/lib/db/schema";
import { z } from "zod";
import { eq } from "drizzle-orm";

const moderationSchema = z.object({
  action: z.enum(["approve", "reject"]),
  reason: z.string().optional(),
});

// Moderate a post (approve/reject)
export async function POST(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { postId } = params;
    const body = await req.json();
    const { action, reason } = moderationSchema.parse(body);

    // Check if the post exists
    const existingPost = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!existingPost) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    if (action === "approve") {
      // Approve the post
      await db.update(posts)
        .set({
          isReported: false,
          moderationStatus: "approved",
          moderatedBy: session.user.id,
          moderatedAt: new Date(),
        })
        .where(eq(posts.id, postId));

      // Update all related reports to resolved
      await db.update(postReports)
        .set({
          status: "resolved",
          reviewedBy: session.user.id,
          reviewedAt: new Date(),
        })
        .where(eq(postReports.postId, postId));

      return NextResponse.json({
        message: "Post approved successfully",
        action: "approved",
      });
    } else if (action === "reject") {
      // Reject the post (mark as rejected but don't delete)
      await db.update(posts)
        .set({
          isReported: true,
          moderationStatus: "rejected",
          moderatedBy: session.user.id,
          moderatedAt: new Date(),
        })
        .where(eq(posts.id, postId));

      // Update all related reports to resolved
      await db.update(postReports)
        .set({
          status: "resolved",
          reviewedBy: session.user.id,
          reviewedAt: new Date(),
        })
        .where(eq(postReports.postId, postId));

      return NextResponse.json({
        message: "Post rejected successfully",
        action: "rejected",
      });
    }

    return NextResponse.json(
      { message: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error moderating post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
