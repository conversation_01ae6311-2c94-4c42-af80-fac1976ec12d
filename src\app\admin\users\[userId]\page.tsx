"use client";

import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/Tabs";
import { toast } from "react-hot-toast";
import { UserOverview } from "@/components/admin/users/UserOverview";
import { UserPosts } from "@/components/admin/users/UserPosts";
import { UserGroups } from "@/components/admin/users/UserGroups";
import { UserBlogs } from "@/components/admin/users/UserBlogs";
import { UserStores } from "@/components/admin/users/UserStores";
import { UserProducts } from "@/components/admin/users/UserProducts";
import { UserPages } from "@/components/admin/users/UserPages";
import { UserWallet } from "@/components/admin/users/UserWallet";
import { UserActions } from "@/components/admin/users/UserActions";
import {
  ArrowLeftIcon,
  UserIcon,
  DocumentTextIcon,
  UserGroupIcon,
  NewspaperIcon,
  BuildingStorefrontIcon,
  CubeIcon,
  DocumentIcon,
  BanknotesIcon,
  Cog6ToothIcon,
  CheckBadgeIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

interface User {
  id: string;
  name: string;
  username: string | null;
  email: string;
  phone: string | null;
  role: string;
  isAdmin: boolean;
  adminRoleId: string | null;
  adminRoleName: string | null;
  image: string | null;
  coverImage: string | null;
  bio: string | null;
  location: string | null;
  birthday: string | null;
  emailVerified: string | null;
  isVerified: boolean;
  // Status fields
  status: string;
  isActive: boolean;
  suspendedAt: string | null;
  suspendedReason: string | null;
  suspendedBy: string | null;
  deletedAt: string | null;
  deletedReason: string | null;
  deletedBy: string | null;
  createdAt: string;
  updatedAt: string;
  wallet: {
    generalBalance: string;
    earningBalance: string;
    totalDeposited: string;
    totalWithdrawn: string;
    totalSent: string;
    totalReceived: string;
    isActive: boolean;
  };
  isVerified: boolean;
  totalBalance: string;
}

export default function UserDetailPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params?.userId as string;

  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const fetchUser = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/users/${userId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch user");
      }

      const userData = await response.json();
      setUser(userData);
    } catch (error) {
      console.error("Error fetching user:", error);
      toast.error("Failed to fetch user details");
      router.push("/admin/users");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserUpdate = () => {
    fetchUser();
  };

  const handleVerifyUser = async (verify: boolean) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/verify`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isVerified: verify }),
      });

      if (!response.ok) throw new Error("Failed to update verification");

      toast.success(`User ${verify ? "verified" : "unverified"} successfully`);
      fetchUser();
    } catch (error) {
      console.error("Error updating verification:", error);
      toast.error("Failed to update verification");
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">User not found</h2>
          <p className="mt-2 text-gray-600">The user you're looking for doesn't exist.</p>
          <Button
            onClick={() => router.push("/admin/users")}
            className="mt-4"
          >
            Back to Users
          </Button>
        </div>
      </AdminLayout>
    );
  }

  const tabs = [
    { id: "overview", name: "Overview", icon: UserIcon },
    { id: "posts", name: "Posts", icon: DocumentTextIcon },
    { id: "groups", name: "Groups", icon: UserGroupIcon },
    { id: "blogs", name: "Blogs", icon: NewspaperIcon },
    { id: "stores", name: "Stores", icon: BuildingStorefrontIcon },
    { id: "products", name: "Products", icon: CubeIcon },
    { id: "pages", name: "Pages", icon: DocumentIcon },
    { id: "wallet", name: "Wallet", icon: BanknotesIcon },
    { id: "actions", name: "Actions", icon: Cog6ToothIcon },
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push("/admin/users")}
              className="flex items-center"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">User Details</h1>
              <p className="text-gray-600">
                Manage user information and activities
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => handleVerifyUser(!user.isVerified)}
              variant={user.isVerified ? "destructive" : "primary"}
              className="flex items-center"
            >
              {user.isVerified ? (
                <>
                  <XMarkIcon className="h-4 w-4 mr-2" />
                  Remove Verification
                </>
              ) : (
                <>
                  <CheckBadgeIcon className="h-4 w-4 mr-2" />
                  Verify User
                </>
              )}
            </Button>
          </div>
        </div>

        {/* User Header Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Cover Image */}
          <div className="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            {user.coverImage && (
              <img
                src={user.coverImage}
                alt="Cover"
                className="w-full h-full object-cover"
              />
            )}
          </div>

          {/* User Info */}
          <div className="px-6 py-4">
            <div className="flex items-start space-x-4">
              {/* Profile Image */}
              <div className="relative -mt-16">
                <div className="w-24 h-24 rounded-full border-4 border-white bg-gray-100 overflow-hidden">
                  {user.image ? (
                    <img
                      src={user.image}
                      alt={user.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-200">
                      <UserIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
              </div>

              {/* User Details */}
              <div className="flex-1 min-w-0 pt-2">
                <div className="flex items-center space-x-2">
                  <h2 className="text-xl font-bold text-gray-900 truncate">
                    {user.name}
                  </h2>
                  {user.isVerified && (
                    <Badge variant="success">Verified</Badge>
                  )}
                  {user.isAdmin && (
                    <Badge variant="primary">Admin</Badge>
                  )}
                </div>

                <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                  {user.username && (
                    <span>@{user.username}</span>
                  )}
                  <span>{user.email}</span>
                  {user.phone && (
                    <span>{user.phone}</span>
                  )}
                </div>

                {user.bio && (
                  <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                    {user.bio}
                  </p>
                )}

                <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                  <span>Role: {user.role}</span>
                  {user.adminRoleName && (
                    <span>Admin Role: {user.adminRoleName}</span>
                  )}
                  <span>Joined: {new Date(user.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Wallet Summary */}
              <div className="text-right">
                <div className="text-sm text-gray-500">Total Balance</div>
                <div className="text-2xl font-bold text-green-600">
                  ৳{parseFloat(user.totalBalance).toLocaleString()}
                </div>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>General: ৳{parseFloat(user.wallet.generalBalance).toLocaleString()}</div>
                  <div>Earning: ৳{parseFloat(user.wallet.earningBalance).toLocaleString()}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-9">
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="flex items-center space-x-2"
              >
                <tab.icon className="h-4 w-4" />
                <span className="hidden sm:inline">{tab.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="mt-6">
            <TabsContent value="overview">
              <UserOverview user={user} onUserUpdate={handleUserUpdate} />
            </TabsContent>

            <TabsContent value="posts">
              <UserPosts userId={userId} />
            </TabsContent>

            <TabsContent value="groups">
              <UserGroups userId={userId} />
            </TabsContent>

            <TabsContent value="blogs">
              <UserBlogs userId={userId} />
            </TabsContent>

            <TabsContent value="stores">
              <UserStores userId={userId} />
            </TabsContent>

            <TabsContent value="products">
              <UserProducts userId={userId} />
            </TabsContent>

            <TabsContent value="pages">
              <UserPages userId={userId} />
            </TabsContent>

            <TabsContent value="wallet">
              <UserWallet user={user} onUserUpdate={handleUserUpdate} />
            </TabsContent>

            <TabsContent value="actions">
              <UserActions user={user} onUserUpdate={handleUserUpdate} />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
