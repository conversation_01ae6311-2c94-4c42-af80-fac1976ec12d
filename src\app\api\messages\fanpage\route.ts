import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPageMessages, fanPages, users, notifications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const sendFanPageMessageSchema = z.object({
  fanPageId: z.string().min(1),
  content: z.string().min(1).max(5000),
});

// POST - Send a message to a fan page from user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        error: "Authentication required",
        message: "Please log in to send messages"
      }, { status: 401 });
    }

    const body = await request.json();

    // Validate input
    const validation = sendFanPageMessageSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: "Invalid input",
        message: "Please check your message content",
        details: validation.error.errors
      }, { status: 400 });
    }

    const { fanPageId, content } = validation.data;

    // Verify fan page exists and is active
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, fanPageId),
        eq(fanPages.isActive, true)
      ),
    });

    if (!page) {
      return NextResponse.json({
        success: false,
        error: "Page not found",
        message: "The fan page you're trying to message doesn't exist or is inactive"
      }, { status: 404 });
    }

    // Prevent page owners from sending messages to their own pages through personal interface
    if (page.ownerId === session.user.id) {
      return NextResponse.json({
        success: false,
        error: "Invalid action",
        message: "Page owners should manage messages through Fan Page Messages section"
      }, { status: 403 });
    }

    // Create message
    const messageId = uuidv4();
    await db.insert(fanPageMessages).values({
      id: messageId,
      fanPageId: fanPageId,
      senderId: session.user.id,
      content,
      isFromPage: false, // This is from user to page
    });

    // Create notification for page owner
    if (page.ownerId !== session.user.id) {
      await db.insert(notifications).values({
        id: uuidv4(),
        userId: page.ownerId,
        type: "fan_page_message",
        title: "New message on your fan page",
        message: `${session.user.name} sent a message to ${page.name}`,
        data: JSON.stringify({
          fanPageId: fanPageId,
          senderId: session.user.id,
          messageId: messageId,
        }),
      });
    }

    // Fetch the created message with sender info
    const createdMessage = await db
      .select({
        id: fanPageMessages.id,
        senderId: fanPageMessages.senderId,
        receiverId: fanPageMessages.fanPageId,
        content: fanPageMessages.content,
        isFromPage: fanPageMessages.isFromPage,
        read: fanPageMessages.read,
        createdAt: fanPageMessages.createdAt,
        sender: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
        receiver: {
          id: fanPages.id,
          name: fanPages.name,
          image: fanPages.profileImage,
        },
      })
      .from(fanPageMessages)
      .leftJoin(users, eq(fanPageMessages.senderId, users.id))
      .leftJoin(fanPages, eq(fanPageMessages.fanPageId, fanPages.id))
      .where(eq(fanPageMessages.id, messageId))
      .limit(1);

    return NextResponse.json({
      success: true,
      message: "Message sent to page successfully",
      data: createdMessage[0],
      timestamp: new Date().toISOString()
    }, { status: 201 });

  } catch (error) {
    console.error("Error sending fan page message:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: "Validation error",
        message: "Invalid input data",
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: "Server error",
      message: "Failed to send message. Please try again.",
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
