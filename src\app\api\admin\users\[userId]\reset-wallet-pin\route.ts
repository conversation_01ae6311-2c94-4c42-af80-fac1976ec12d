import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, pinCodes, wallets } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

const resetWalletPinSchema = z.object({
  newPin: z.string().length(4, "PIN must be 4 digits"),
  confirmPin: z.string().length(4, "Confirm PIN must be 4 digits"),
}).refine((data) => data.newPin === data.confirmPin, {
  message: "PINs don't match",
  path: ["confirmPin"],
});

// Reset user wallet PIN (Admin only)
export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { userId } = params;
    const body = await req.json();
    const validatedData = resetWalletPinSchema.parse(body);

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Check if user has a wallet
    const wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (!wallet) {
      return NextResponse.json(
        { message: "User wallet not found" },
        { status: 404 }
      );
    }

    // Delete existing PIN codes for this user
    await db.delete(pinCodes).where(eq(pinCodes.userId, userId));

    // Create new PIN code
    const bcrypt = require("bcryptjs");
    const hashedPin = await bcrypt.hash(validatedData.newPin, 10);

    await db.insert(pinCodes).values({
      id: require("uuid").v4(),
      userId: userId,
      pinHash: hashedPin,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Log the PIN reset action
    console.log(`Admin ${session.user.id} reset wallet PIN for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: "Wallet PIN reset successfully",
    });
  } catch (error) {
    console.error("Error resetting wallet PIN:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Internal server error"
      },
      { status: 500 }
    );
  }
}

// Remove wallet PIN (Admin only)
export async function DELETE(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { userId } = params;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Delete all PIN codes for this user
    await db.delete(pinCodes).where(eq(pinCodes.userId, userId));

    // Log the PIN removal action
    console.log(`Admin ${session.user.id} removed wallet PIN for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: "Wallet PIN removed successfully",
    });
  } catch (error) {
    console.error("Error removing wallet PIN:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error"
      },
      { status: 500 }
    );
  }
}
