"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { toast } from "react-hot-toast";
import {
  XMarkIcon,
  Cog6ToothIcon,
  CheckCircleIcon,
  XCircleIcon,
  ShieldCheckIcon,
  ShieldExclamationIcon,
  ExclamationTriangleIcon,
  UserIcon,
} from "@heroicons/react/24/outline";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  isAdmin: boolean;
  isVerified: boolean;
  emailVerified: string | null;
  status: string;
  isActive: boolean;
  suspendedAt: string | null;
  suspendedReason: string | null;
  deletedAt: string | null;
  wallet: {
    isActive: boolean;
  };
}

interface AccountStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
  onSuccess: () => void;
}

export function AccountStatusModal({
  isOpen,
  onClose,
  user,
  onSuccess
}: AccountStatusModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [actionType, setActionType] = useState<string | null>(null);

  const handleVerifyEmail = async () => {
    try {
      setIsLoading(true);
      setActionType('email');

      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          emailVerified: !user.isVerified ? new Date().toISOString() : null,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to update email verification");
      }

      toast.success(
        user.isVerified
          ? "Email verification removed"
          : "Email verified successfully"
      );
      onSuccess();
    } catch (error: any) {
      console.error("Error updating email verification:", error);
      toast.error(error.message || "Failed to update email verification");
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleToggleAdmin = async () => {
    try {
      setIsLoading(true);
      setActionType('admin');

      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isAdmin: !user.isAdmin,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to update admin status");
      }

      toast.success(
        user.isAdmin
          ? "Admin privileges removed"
          : "Admin privileges granted"
      );
      onSuccess();
    } catch (error: any) {
      console.error("Error updating admin status:", error);
      toast.error(error.message || "Failed to update admin status");
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleToggleWallet = async () => {
    try {
      setIsLoading(true);
      setActionType('wallet');

      const response = await fetch(`/api/admin/users/${user.id}/wallet/settings`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isActive: !user.wallet.isActive,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to update wallet status");
      }

      toast.success(
        user.wallet.isActive
          ? "Wallet deactivated"
          : "Wallet activated"
      );
      onSuccess();
    } catch (error: any) {
      console.error("Error updating wallet status:", error);
      toast.error(error.message || "Failed to update wallet status");
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleDisableAccount = async () => {
    if (!confirm(`Are you sure you want to disable ${user.name}'s account? They will not be able to log in.`)) {
      return;
    }

    try {
      setIsLoading(true);
      setActionType('disable');

      const response = await fetch(`/api/admin/users/${user.id}/disable`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to disable account");
      }

      toast.success("Account disabled successfully");
      onSuccess();
    } catch (error: any) {
      console.error("Error disabling account:", error);
      toast.error(error.message || "Failed to disable account");
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleSuspendAccount = async () => {
    const reason = prompt(`Enter reason for suspending ${user.name}'s account:`);
    if (!reason || !reason.trim()) {
      toast.error("Suspension reason is required");
      return;
    }

    if (!confirm(`Are you sure you want to suspend ${user.name}'s account?`)) {
      return;
    }

    try {
      setIsLoading(true);
      setActionType('suspend');

      const response = await fetch(`/api/admin/users/${user.id}/suspend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: reason.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to suspend account");
      }

      toast.success("Account suspended successfully");
      onSuccess();
    } catch (error: any) {
      console.error("Error suspending account:", error);
      toast.error(error.message || "Failed to suspend account");
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleDeleteAccount = async () => {
    const reason = prompt(`Enter reason for PERMANENTLY deleting ${user.name}'s account:`);
    if (!reason || !reason.trim()) {
      toast.error("Deletion reason is required");
      return;
    }

    if (!confirm(`🚨 CRITICAL WARNING: Are you sure you want to PERMANENTLY DELETE ${user.name}'s account?\n\n⚠️ This will PERMANENTLY remove:\n• User profile and all personal data\n• All posts, comments, and likes\n• Wallet and transaction history\n• All friendships and messages\n• Group memberships and activities\n• ALL related data from the database\n\n❌ THIS ACTION CANNOT BE UNDONE!\n\nClick OK only if you are absolutely certain.`)) {
      return;
    }

    const confirmText = prompt(`⚠️ FINAL CONFIRMATION\n\nTo proceed with PERMANENT deletion of ${user.name}'s account, type exactly: DELETE PERMANENTLY`);
    if (confirmText !== "DELETE PERMANENTLY") {
      toast.error("Confirmation text does not match. Deletion cancelled.");
      return;
    }

    try {
      setIsLoading(true);
      setActionType('delete');

      const response = await fetch(`/api/admin/users/${user.id}/delete`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: reason.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to delete account");
      }

      toast.success("Account permanently deleted successfully");
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Error deleting account:", error);
      toast.error(error.message || "Failed to delete account");
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleReactivateAccount = async () => {
    if (!confirm(`Are you sure you want to reactivate ${user.name}'s account?`)) {
      return;
    }

    try {
      setIsLoading(true);
      setActionType('reactivate');

      const response = await fetch(`/api/admin/users/${user.id}/reactivate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to reactivate account");
      }

      toast.success("Account reactivated successfully");
      onSuccess();
    } catch (error: any) {
      console.error("Error reactivating account:", error);
      toast.error(error.message || "Failed to reactivate account");
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-full">
              <Cog6ToothIcon className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Account Status Management
              </h2>
              <p className="text-sm text-gray-600">
                Manage {user.name}'s account status and permissions
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Current Status Overview */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Current Status</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                {user.status === 'active' ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                ) : user.status === 'suspended' ? (
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
                ) : user.status === 'disabled' ? (
                  <XCircleIcon className="h-5 w-5 text-red-500" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-gray-500" />
                )}
                <span className="text-sm text-gray-700">
                  Account {user.status ? user.status.charAt(0).toUpperCase() + user.status.slice(1) : 'Unknown'}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {user.isVerified ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-red-500" />
                )}
                <span className="text-sm text-gray-700">
                  Email {user.isVerified ? 'Verified' : 'Not Verified'}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {user.isAdmin ? (
                  <ShieldCheckIcon className="h-5 w-5 text-blue-500" />
                ) : (
                  <UserIcon className="h-5 w-5 text-gray-500" />
                )}
                <span className="text-sm text-gray-700">
                  {user.isAdmin ? 'Admin User' : 'Regular User'}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {user.wallet.isActive ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-red-500" />
                )}
                <span className="text-sm text-gray-700">
                  Wallet {user.wallet.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>

            {/* Additional Status Info */}
            {user.status === 'suspended' && user.suspendedAt && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  <strong>Suspended:</strong> {new Date(user.suspendedAt).toLocaleDateString()}
                </p>
                {user.suspendedReason && (
                  <p className="text-sm text-yellow-700 mt-1">
                    <strong>Reason:</strong> {user.suspendedReason}
                  </p>
                )}
              </div>
            )}

            {user.status === 'deleted' && user.deletedAt && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800">
                  <strong>Deleted:</strong> {new Date(user.deletedAt).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>

          {/* Account Actions */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">Account Actions</h3>

            {/* Email Verification */}
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                {user.isVerified ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-red-500" />
                )}
                <div>
                  <div className="text-sm font-medium text-gray-900">Email Verification</div>
                  <div className="text-sm text-gray-600">
                    {user.isVerified ? "Email is verified" : "Email is not verified"}
                  </div>
                </div>
              </div>
              <Button
                variant={user.isVerified ? "outline" : "primary"}
                size="sm"
                onClick={handleVerifyEmail}
                disabled={isLoading && actionType === 'email'}
              >
                {isLoading && actionType === 'email' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                ) : (
                  user.isVerified ? "Remove Verification" : "Verify Email"
                )}
              </Button>
            </div>

            {/* Admin Status */}
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                {user.isAdmin ? (
                  <ShieldCheckIcon className="h-5 w-5 text-blue-500" />
                ) : (
                  <ShieldExclamationIcon className="h-5 w-5 text-gray-500" />
                )}
                <div>
                  <div className="text-sm font-medium text-gray-900">Admin Privileges</div>
                  <div className="text-sm text-gray-600">
                    {user.isAdmin ? "User has admin access" : "User has no admin access"}
                  </div>
                </div>
              </div>
              <Button
                variant={user.isAdmin ? "outline" : "primary"}
                size="sm"
                onClick={handleToggleAdmin}
                disabled={isLoading && actionType === 'admin'}
              >
                {isLoading && actionType === 'admin' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                ) : (
                  user.isAdmin ? "Remove Admin" : "Make Admin"
                )}
              </Button>
            </div>

            {/* Wallet Status */}
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                {user.wallet.isActive ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-red-500" />
                )}
                <div>
                  <div className="text-sm font-medium text-gray-900">Wallet Access</div>
                  <div className="text-sm text-gray-600">
                    {user.wallet.isActive ? "Wallet is active" : "Wallet is inactive"}
                  </div>
                </div>
              </div>
              <Button
                variant={user.wallet.isActive ? "outline" : "primary"}
                size="sm"
                onClick={handleToggleWallet}
                disabled={isLoading && actionType === 'wallet'}
              >
                {isLoading && actionType === 'wallet' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                ) : (
                  user.wallet.isActive ? "Deactivate Wallet" : "Activate Wallet"
                )}
              </Button>
            </div>
          </div>

          {/* Account Management Actions */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">Account Management</h3>

            {/* Reactivate Account (for disabled/suspended accounts) */}
            {(user.status === 'disabled' || user.status === 'suspended') && (
              <div className="flex items-center justify-between p-4 border border-green-200 rounded-lg bg-green-50">
                <div className="flex items-center space-x-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Reactivate Account</div>
                    <div className="text-sm text-gray-600">
                      Restore account to active status
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReactivateAccount}
                  disabled={isLoading && actionType === 'reactivate'}
                  className="border-green-300 text-green-600 hover:bg-green-100"
                >
                  {isLoading && actionType === 'reactivate' ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  ) : (
                    "Reactivate"
                  )}
                </Button>
              </div>
            )}

            {/* Disable Account (for active accounts) */}
            {user.status === 'active' && (
              <div className="flex items-center justify-between p-4 border border-orange-200 rounded-lg bg-orange-50">
                <div className="flex items-center space-x-3">
                  <XCircleIcon className="h-5 w-5 text-orange-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Disable Account</div>
                    <div className="text-sm text-gray-600">
                      Prevent user from logging in (reversible)
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDisableAccount}
                  disabled={isLoading && actionType === 'disable'}
                  className="border-orange-300 text-orange-600 hover:bg-orange-100"
                >
                  {isLoading && actionType === 'disable' ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  ) : (
                    "Disable"
                  )}
                </Button>
              </div>
            )}

            {/* Suspend Account (for active accounts) */}
            {user.status === 'active' && (
              <div className="flex items-center justify-between p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                <div className="flex items-center space-x-3">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Suspend Account</div>
                    <div className="text-sm text-gray-600">
                      Temporarily suspend with reason (reversible)
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSuspendAccount}
                  disabled={isLoading && actionType === 'suspend'}
                  className="border-yellow-300 text-yellow-600 hover:bg-yellow-100"
                >
                  {isLoading && actionType === 'suspend' ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  ) : (
                    "Suspend"
                  )}
                </Button>
              </div>
            )}

            {/* Delete Account (for any non-deleted account) */}
            {user.status !== 'deleted' && (
              <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                <div className="flex items-center space-x-3">
                  <XCircleIcon className="h-5 w-5 text-red-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">🚨 Permanently Delete Account</div>
                    <div className="text-sm text-gray-600">
                      ⚠️ PERMANENTLY delete account and ALL related data (IRREVERSIBLE)
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeleteAccount}
                  disabled={isLoading && actionType === 'delete'}
                  className="border-red-300 text-red-600 hover:bg-red-100"
                >
                  {isLoading && actionType === 'delete' ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  ) : (
                    "🗑️ Delete Permanently"
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50 flex-shrink-0">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
