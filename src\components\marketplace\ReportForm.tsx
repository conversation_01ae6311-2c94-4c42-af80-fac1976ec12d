"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/Button";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";

const reportSchema = z.object({
  reason: z.enum(["counterfeit", "inappropriate", "fraud", "prohibited", "other"], {
    errorMap: () => ({ message: "Please select a reason for reporting" }),
  }),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
});

type ReportFormData = z.infer<typeof reportSchema>;

interface ReportFormProps {
  productId: string;
  onClose: () => void;
}

export function ReportForm({ productId, onClose }: ReportFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ReportFormData>({
    resolver: zodResolver(reportSchema),
  });

  const onSubmit = async (data: ReportFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const response = await fetch("/api/marketplace/reports", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productId,
          reason: data.reason,
          description: data.description,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to submit report");
      }

      setIsSuccess(true);
    } catch (err) {
      console.error("Error submitting report:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const reasonOptions = [
    { value: "counterfeit", label: "Counterfeit or fake item" },
    { value: "inappropriate", label: "Inappropriate or offensive content" },
    { value: "fraud", label: "Fraudulent listing or scam" },
    { value: "prohibited", label: "Prohibited or illegal item" },
    { value: "other", label: "Other issue" },
  ];

  return (
    <Dialog open={true} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
          <div className="mb-4 flex items-center justify-between">
            <DialogTitle className="text-lg font-medium text-gray-900">
              {isSuccess ? "Report Submitted" : "Report This Listing"}
            </DialogTitle>
            <button
              type="button"
              onClick={onClose}
              className="rounded-md bg-white text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          
          {isSuccess ? (
            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="mt-3 text-lg font-medium text-gray-900">
                Thank you for your report
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                We'll review this listing and take appropriate action.
              </p>
              <div className="mt-4">
                <Button onClick={onClose}>Close</Button>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {error && (
                <div className="rounded-md bg-red-50 p-3 text-sm text-red-600">
                  {error}
                </div>
              )}
              
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Reason for reporting *
                </label>
                <select
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  {...register("reason")}
                >
                  <option value="">Select a reason</option>
                  {reasonOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.reason && (
                  <p className="mt-1 text-sm text-red-500">{errors.reason.message}</p>
                )}
              </div>
              
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Additional details
                </label>
                <textarea
                  className="w-full rounded-md border border-gray-300 px-3 py-2 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Please provide any additional information about this report"
                  {...register("description")}
                ></textarea>
                {errors.description && (
                  <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
                )}
              </div>
              
              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  isLoading={isSubmitting}
                >
                  Submit Report
                </Button>
              </div>
            </form>
          )}
        </DialogPanel>
      </div>
    </Dialog>
  );
}
