"use client";

import { useState, useEffect, useCallback } from "react";

interface Product {
  id: string;
  title: string;
  price: number;
  condition: string;
  photos: string[] | null;
  createdAt: string;
  store: {
    id: string;
    name: string;
    logo: string | null;
  } | null;
}

interface FeaturedProductsResponse {
  success: boolean;
  data: Product[];
  meta: {
    total: number;
    limit: number;
  };
}

interface UseFeaturedProductsOptions {
  limit?: number;
  enabled?: boolean;
  refetchInterval?: number;
}

export function useFeaturedProducts({
  limit = 12,
  enabled = true,
  refetchInterval
}: UseFeaturedProductsOptions = {}) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeaturedProducts = useCallback(async () => {
    if (!enabled) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/marketplace/featured-products?limit=${limit}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: FeaturedProductsResponse = await response.json();

      if (data.success) {
        setProducts(data.data);
      } else {
        throw new Error('Failed to fetch featured products');
      }
    } catch (err) {
      console.error('Error fetching featured products:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [limit, enabled]);

  useEffect(() => {
    fetchFeaturedProducts();
  }, [fetchFeaturedProducts]);

  // Set up refetch interval if specified
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchFeaturedProducts, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchFeaturedProducts, refetchInterval, enabled]);

  const refetch = useCallback(() => {
    fetchFeaturedProducts();
  }, [fetchFeaturedProducts]);

  return {
    products,
    loading,
    error,
    refetch,
  };
}
