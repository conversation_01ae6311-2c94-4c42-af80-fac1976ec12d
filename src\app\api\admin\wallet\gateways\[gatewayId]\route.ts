import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { paymentGatewaySchema } from "@/lib/wallet/validation";
import { z } from "zod";

// Get specific payment gateway
export async function GET(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    const gateway = await WalletService.getPaymentGatewayById(gatewayId);

    if (!gateway) {
      return NextResponse.json(
        { message: "Payment gateway not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: gateway,
    });
  } catch (error: any) {
    console.error("Error fetching payment gateway:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch payment gateway"
      },
      { status: 500 }
    );
  }
}

// Update payment gateway
export async function PUT(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    const body = await req.json();
    const validatedData = paymentGatewaySchema.parse(body);

    // Check if gateway exists
    const existingGateway = await WalletService.getPaymentGatewayById(gatewayId);
    if (!existingGateway) {
      return NextResponse.json(
        { message: "Payment gateway not found" },
        { status: 404 }
      );
    }

    await WalletService.updatePaymentGateway(gatewayId, validatedData);

    return NextResponse.json({
      success: true,
      message: "Payment gateway updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating payment gateway:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to update payment gateway"
      },
      { status: 500 }
    );
  }
}

// Delete payment gateway
export async function DELETE(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    // Check if gateway exists
    const existingGateway = await WalletService.getPaymentGatewayById(gatewayId);
    if (!existingGateway) {
      return NextResponse.json(
        { message: "Payment gateway not found" },
        { status: 404 }
      );
    }

    await WalletService.deletePaymentGateway(gatewayId);

    return NextResponse.json({
      success: true,
      message: "Payment gateway deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting payment gateway:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete payment gateway"
      },
      { status: 500 }
    );
  }
}

// Toggle gateway status
export async function PATCH(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    const body = await req.json();
    const { isActive } = body;

    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        { message: "isActive must be a boolean" },
        { status: 400 }
      );
    }

    // Check if gateway exists
    const existingGateway = await WalletService.getPaymentGatewayById(gatewayId);
    if (!existingGateway) {
      return NextResponse.json(
        { message: "Payment gateway not found" },
        { status: 404 }
      );
    }

    await WalletService.toggleGatewayStatus(gatewayId, isActive);

    return NextResponse.json({
      success: true,
      message: `Payment gateway ${isActive ? 'activated' : 'deactivated'} successfully`,
    });
  } catch (error: any) {
    console.error("Error toggling gateway status:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to toggle gateway status"
      },
      { status: 500 }
    );
  }
}
