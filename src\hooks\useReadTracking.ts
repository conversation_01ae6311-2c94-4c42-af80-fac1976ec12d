"use client";

import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";

interface UseReadTrackingProps {
  blogSlug: string;
  isEnabled?: boolean;
  minReadDuration?: number; // in seconds
}

export function useReadTracking({ 
  blogSlug, 
  isEnabled = true,
  minReadDuration = 120 
}: UseReadTrackingProps) {
  const [readDuration, setReadDuration] = useState(0);
  const [isTracking, setIsTracking] = useState(false);
  const [hasTracked, setHasTracked] = useState(false);
  
  const startTimeRef = useRef<number | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const sessionIdRef = useRef<string>(uuidv4());
  const visibilityTimeRef = useRef<number>(0);
  const lastVisibilityChangeRef = useRef<number>(Date.now());

  // Track page visibility
  useEffect(() => {
    const handleVisibilityChange = () => {
      const now = Date.now();
      
      if (document.hidden) {
        // Page became hidden, add time spent visible
        if (lastVisibilityChangeRef.current) {
          visibilityTimeRef.current += now - lastVisibilityChangeRef.current;
        }
      } else {
        // Page became visible, reset timer
        lastVisibilityChangeRef.current = now;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Start tracking when component mounts
  useEffect(() => {
    if (!isEnabled || !blogSlug) return;

    startTimeRef.current = Date.now();
    lastVisibilityChangeRef.current = Date.now();
    setIsTracking(true);

    // Update read duration every second
    intervalRef.current = setInterval(() => {
      const now = Date.now();
      let totalTime = visibilityTimeRef.current;
      
      // Add current visible time if page is visible
      if (!document.hidden && lastVisibilityChangeRef.current) {
        totalTime += now - lastVisibilityChangeRef.current;
      }
      
      const duration = Math.floor(totalTime / 1000);
      setReadDuration(duration);

      // Track read if minimum duration is reached and not already tracked
      if (duration >= minReadDuration && !hasTracked) {
        trackRead(duration);
        setHasTracked(true);
      }
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      
      // Track final read duration on unmount if not already tracked
      if (!hasTracked && readDuration > 0) {
        const finalDuration = Math.floor(visibilityTimeRef.current / 1000);
        if (finalDuration >= 30) { // Track if at least 30 seconds
          trackRead(finalDuration);
        }
      }
    };
  }, [blogSlug, isEnabled, minReadDuration]);

  const trackRead = async (duration: number) => {
    try {
      await fetch(`/api/blogs/${blogSlug}/track-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          readDuration: duration,
          sessionId: sessionIdRef.current,
          referrer: document.referrer || undefined,
        }),
      });
    } catch (error) {
      console.error('Error tracking read:', error);
    }
  };

  const forceTrack = () => {
    if (!hasTracked && readDuration > 0) {
      trackRead(readDuration);
      setHasTracked(true);
    }
  };

  return {
    readDuration,
    isTracking,
    hasTracked,
    forceTrack,
    isQualified: readDuration >= minReadDuration,
  };
}
