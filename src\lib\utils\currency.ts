interface CurrencySettings {
  default_currency: string;
  currency_symbol: string;
  currency_position: 'before' | 'after';
  decimal_places: number;
  thousands_separator: string;
  decimal_separator: string;
  supported_currencies: string;
  auto_currency_detection: boolean;
  currency_conversion_api: string;
}

// Default currency settings
const defaultCurrencySettings: CurrencySettings = {
  default_currency: 'USD',
  currency_symbol: '$',
  currency_position: 'before',
  decimal_places: 2,
  thousands_separator: ',',
  decimal_separator: '.',
  supported_currencies: 'USD,EUR,GBP,BDT,INR',
  auto_currency_detection: false,
  currency_conversion_api: '',
};

// Currency symbols mapping
const currencySymbols: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  BDT: '৳',
  INR: '₹',
  CAD: 'C$',
  AUD: 'A$',
  JPY: '¥',
  CNY: '¥',
  KRW: '₩',
  SGD: 'S$',
  THB: '฿',
  MYR: 'RM',
  IDR: 'Rp',
  PHP: '₱',
  VND: '₫',
  PKR: '₨',
  LKR: '₨',
  NPR: '₨',
  AFN: '؋',
  SAR: '﷼',
  AED: 'د.إ',
  QAR: '﷼',
  KWD: 'د.ك',
  BHD: '.د.ب',
  OMR: '﷼',
  JOD: 'د.ا',
  ILS: '₪',
  TRY: '₺',
  RUB: '₽',
  UAH: '₴',
  PLN: 'zł',
  CZK: 'Kč',
  HUF: 'Ft',
  RON: 'lei',
  BGN: 'лв',
  HRK: 'kn',
  RSD: 'дин',
  BAM: 'KM',
  MKD: 'ден',
  ALL: 'L',
  MDL: 'L',
  GEL: '₾',
  AMD: '֏',
  AZN: '₼',
  KZT: '₸',
  UZS: 'лв',
  KGS: 'лв',
  TJS: 'SM',
  TMT: 'T',
  MNT: '₮',
  BTC: '₿',
  ETH: 'Ξ',
};

/**
 * Format currency amount based on settings
 */
export function formatCurrency(
  amount: number | string,
  settings?: Partial<CurrencySettings>,
  currencyCode?: string
): string {
  const config = { ...defaultCurrencySettings, ...settings };
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numAmount)) {
    return '0';
  }

  // Get currency symbol
  const symbol = currencyCode
    ? (currencySymbols[currencyCode] || currencyCode)
    : config.currency_symbol;

  // Format the number
  const fixedAmount = numAmount.toFixed(config.decimal_places);
  const [integerPart, decimalPart] = fixedAmount.split('.');

  // Add thousands separator
  let formattedInteger = integerPart;
  if (config.thousands_separator && integerPart.length > 3) {
    formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, config.thousands_separator);
  }

  // Combine integer and decimal parts
  let formattedAmount = formattedInteger;
  if (config.decimal_places > 0 && decimalPart) {
    formattedAmount += config.decimal_separator + decimalPart;
  }

  // Position currency symbol
  if (config.currency_position === 'before') {
    return `${symbol}${formattedAmount}`;
  } else {
    return `${formattedAmount}${symbol}`;
  }
}

/**
 * Parse currency string to number
 */
export function parseCurrency(
  currencyString: string,
  settings?: Partial<CurrencySettings>
): number {
  const config = { ...defaultCurrencySettings, ...settings };

  // Remove currency symbol and spaces
  let cleanString = currencyString.replace(/[^\d\-+.,]/g, '');

  // Replace thousands separator with empty string
  if (config.thousands_separator) {
    cleanString = cleanString.replace(new RegExp(`\\${config.thousands_separator}`, 'g'), '');
  }

  // Replace decimal separator with dot
  if (config.decimal_separator !== '.') {
    cleanString = cleanString.replace(config.decimal_separator, '.');
  }

  return parseFloat(cleanString) || 0;
}

/**
 * Get supported currencies list
 */
export function getSupportedCurrencies(settings?: Partial<CurrencySettings>): string[] {
  const config = { ...defaultCurrencySettings, ...settings };
  return config.supported_currencies.split(',').map(c => c.trim()).filter(Boolean);
}

/**
 * Get currency symbol by code
 */
export function getCurrencySymbol(currencyCode: string): string {
  return currencySymbols[currencyCode] || currencyCode;
}

/**
 * Validate currency code
 */
export function isValidCurrencyCode(currencyCode: string, settings?: Partial<CurrencySettings>): boolean {
  const supportedCurrencies = getSupportedCurrencies(settings);
  return supportedCurrencies.includes(currencyCode);
}

/**
 * Convert amount between currencies (placeholder for future implementation)
 */
export async function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  apiKey?: string
): Promise<number> {
  // This is a placeholder. In a real implementation, you would:
  // 1. Use a currency conversion API like exchangerate-api.com
  // 2. Cache conversion rates
  // 3. Handle API errors gracefully

  if (fromCurrency === toCurrency) {
    return amount;
  }

  // For now, return the same amount
  // TODO: Implement actual currency conversion
  console.warn('Currency conversion not implemented yet');
  return amount;
}

/**
 * Get currency settings from database or use defaults
 */
export async function getCurrencySettings(): Promise<CurrencySettings> {
  try {
    const response = await fetch('/api/currency/settings');
    if (response.ok) {
      const data = await response.json();
      return data;
    }
  } catch (error) {
    console.error('Error fetching currency settings:', error);
  }

  return defaultCurrencySettings;
}
