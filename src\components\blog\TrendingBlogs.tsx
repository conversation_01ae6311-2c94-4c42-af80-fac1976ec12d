"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Spinner } from "@/components/ui/Spinner";
import {
  FireIcon,
  EyeIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

interface TrendingBlog {
  blogId: string;
  title: string;
  slug: string;
  viewCount: number;
  recentViews: number;
  trendScore: number;
}

interface TrendingBlogsProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

export function TrendingBlogs({ 
  limit = 5, 
  showHeader = true,
  className = ""
}: TrendingBlogsProps) {
  const [trendingBlogs, setTrendingBlogs] = useState<TrendingBlog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTrendingBlogs = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/blogs/trending?limit=${limit}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch trending blogs');
        }

        const data = await response.json();
        setTrendingBlogs(data.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchTrendingBlogs();
  }, [limit]);

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        {showHeader && (
          <div className="flex items-center space-x-2 mb-4">
            <FireIcon className="h-5 w-5 text-orange-500" />
            <h2 className="text-lg font-semibold text-gray-900">Trending Now</h2>
          </div>
        )}
        <div className="flex items-center justify-center py-8">
          <Spinner size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`p-6 ${className}`}>
        {showHeader && (
          <div className="flex items-center space-x-2 mb-4">
            <FireIcon className="h-5 w-5 text-orange-500" />
            <h2 className="text-lg font-semibold text-gray-900">Trending Now</h2>
          </div>
        )}
        <div className="text-center py-8">
          <p className="text-red-600">Error loading trending blogs: {error}</p>
        </div>
      </Card>
    );
  }

  if (trendingBlogs.length === 0) {
    return (
      <Card className={`p-6 ${className}`}>
        {showHeader && (
          <div className="flex items-center space-x-2 mb-4">
            <FireIcon className="h-5 w-5 text-orange-500" />
            <h2 className="text-lg font-semibold text-gray-900">Trending Now</h2>
          </div>
        )}
        <div className="text-center py-8">
          <p className="text-gray-600">No trending blogs available</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-6 ${className}`}>
      {showHeader && (
        <div className="flex items-center space-x-2 mb-6">
          <FireIcon className="h-5 w-5 text-orange-500" />
          <h2 className="text-lg font-semibold text-gray-900">Trending Now</h2>
          <Badge variant="secondary" className="ml-auto">
            Last 24h
          </Badge>
        </div>
      )}

      <div className="space-y-4">
        {trendingBlogs.map((blog, index) => (
          <Link
            key={blog.blogId}
            href={`/blogs/${blog.slug}`}
            className="block group"
          >
            <div className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              {/* Ranking */}
              <div className="flex-shrink-0">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold
                  ${index === 0 ? 'bg-yellow-100 text-yellow-800' : 
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    index === 2 ? 'bg-orange-100 text-orange-800' :
                    'bg-blue-100 text-blue-800'}
                `}>
                  {index + 1}
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                  {blog.title}
                </h3>
                
                <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <EyeIcon className="h-3 w-3" />
                    <span>{blog.viewCount.toLocaleString()}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <ArrowTrendingUpIcon className="h-3 w-3 text-green-500" />
                    <span className="text-green-600">+{blog.recentViews}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <FireIcon className="h-3 w-3 text-orange-500" />
                    <span className="text-orange-600">{blog.trendScore}</span>
                  </div>
                </div>
              </div>

              {/* Trend indicator */}
              <div className="flex-shrink-0">
                {index < 3 && (
                  <div className={`
                    w-2 h-2 rounded-full
                    ${index === 0 ? 'bg-red-500' : 
                      index === 1 ? 'bg-orange-500' :
                      'bg-yellow-500'}
                  `} />
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* View all link */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <Link
          href="/blogs?sort=trending"
          className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center space-x-1"
        >
          <span>View all trending blogs</span>
          <ArrowTrendingUpIcon className="h-4 w-4" />
        </Link>
      </div>
    </Card>
  );
}
