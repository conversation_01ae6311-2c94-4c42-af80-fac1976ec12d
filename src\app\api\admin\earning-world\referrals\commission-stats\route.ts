import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCommissions, users } from "@/lib/db/schema";
import { eq, count, sum, sql, desc } from "drizzle-orm";

// Get commission statistics for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Total commissions count
    const totalCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions);

    // Commission counts by status
    const pendingCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'pending'));

    const approvedCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'approved'));

    const paidCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'paid'));

    const cancelledCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'cancelled'));

    // Commission amounts
    const totalCommissionAmountResult = await db
      .select({ total: sum(referralCommissions.commissionAmount) })
      .from(referralCommissions);

    const paidCommissionAmountResult = await db
      .select({ total: sum(referralCommissions.commissionAmount) })
      .from(referralCommissions)
      .where(eq(referralCommissions.status, 'paid'));

    const pendingCommissionAmountResult = await db
      .select({ total: sum(referralCommissions.commissionAmount) })
      .from(referralCommissions)
      .where(sql`${referralCommissions.status} IN ('pending', 'approved')`);

    // Average commission amount
    const averageCommissionResult = await db
      .select({ 
        avg: sql<number>`AVG(${referralCommissions.commissionAmount})` 
      })
      .from(referralCommissions);

    // Top earners
    const topEarnersResult = await db
      .select({
        referrerId: referralCommissions.referrerId,
        name: users.name,
        email: users.email,
        totalCommissions: count(),
        totalEarnings: sum(referralCommissions.commissionAmount),
      })
      .from(referralCommissions)
      .innerJoin(users, eq(referralCommissions.referrerId, users.id))
      .where(eq(referralCommissions.status, 'paid'))
      .groupBy(referralCommissions.referrerId, users.name, users.email)
      .orderBy(desc(sum(referralCommissions.commissionAmount)))
      .limit(10);

    // Recent commissions
    const recentCommissionsResult = await db
      .select({
        id: referralCommissions.id,
        referrerName: sql<string>`referrer.name`,
        referredUserName: sql<string>`referred.name`,
        planName: referralCommissions.planName,
        commissionAmount: referralCommissions.commissionAmount,
        status: referralCommissions.status,
        createdAt: referralCommissions.createdAt,
      })
      .from(referralCommissions)
      .innerJoin(
        sql`users AS referrer`, 
        sql`${referralCommissions.referrerId} = referrer.id`
      )
      .innerJoin(
        sql`users AS referred`, 
        sql`${referralCommissions.referredUserId} = referred.id`
      )
      .orderBy(desc(referralCommissions.createdAt))
      .limit(10);

    const stats = {
      totalCommissions: totalCommissionsResult[0]?.count || 0,
      pendingCommissions: pendingCommissionsResult[0]?.count || 0,
      approvedCommissions: approvedCommissionsResult[0]?.count || 0,
      paidCommissions: paidCommissionsResult[0]?.count || 0,
      cancelledCommissions: cancelledCommissionsResult[0]?.count || 0,
      totalCommissionAmount: parseFloat(totalCommissionAmountResult[0]?.total || '0'),
      paidCommissionAmount: parseFloat(paidCommissionAmountResult[0]?.total || '0'),
      pendingCommissionAmount: parseFloat(pendingCommissionAmountResult[0]?.total || '0'),
      averageCommissionAmount: parseFloat(averageCommissionResult[0]?.avg?.toString() || '0'),
      topEarners: topEarnersResult.map(earner => ({
        id: earner.referrerId,
        name: earner.name || 'Unknown User',
        email: earner.email || '',
        totalCommissions: earner.totalCommissions,
        totalEarnings: parseFloat(earner.totalEarnings || '0'),
      })),
      recentCommissions: recentCommissionsResult.map(commission => ({
        id: commission.id,
        referrerName: commission.referrerName || 'Unknown User',
        referredUserName: commission.referredUserName || 'Unknown User',
        planName: commission.planName,
        commissionAmount: parseFloat(commission.commissionAmount),
        status: commission.status,
        createdAt: commission.createdAt.toISOString(),
      })),
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Error fetching commission statistics:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch commission statistics"
      },
      { status: 500 }
    );
  }
}
