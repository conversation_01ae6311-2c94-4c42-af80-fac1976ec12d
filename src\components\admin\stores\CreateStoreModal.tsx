"use client";

import { useState } from "react";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Spinner } from "@/components/ui/Spinner";
import { Switch } from "@/components/ui/Switch";
import { toast } from "react-hot-toast";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserSelect } from "@/components/admin/users/UserSelect";

interface CreateStoreModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStoreCreated: () => void;
}

const storeSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(255, "Name must be less than 255 characters"),
  slug: z
    .string()
    .min(3, "Slug must be at least 3 characters")
    .max(100, "Slug must be less than 100 characters")
    .regex(/^[a-zA-Z0-9-]+$/, "Slug can only contain letters, numbers, and hyphens"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  location: z.string().max(255, "Location must be less than 255 characters").optional(),
  phone: z.string().max(50, "Phone must be less than 50 characters").optional(),
  email: z.string().email("Invalid email address").max(255, "Email must be less than 255 characters").optional(),
  website: z.string().max(255, "Website must be less than 255 characters").optional(),
  logo: z.string().url("Invalid URL").optional(),
  banner: z.string().url("Invalid URL").optional(),
  ownerId: z.string().min(1, "Store owner is required"),
});

type StoreFormData = z.infer<typeof storeSchema>;

export function CreateStoreModal({ isOpen, onClose, onStoreCreated }: CreateStoreModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [selectedOwnerId, setSelectedOwnerId] = useState<string>("");

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<StoreFormData>({
    resolver: zodResolver(storeSchema),
    defaultValues: {
      name: "",
      slug: "",
      description: "",
      location: "",
      phone: "",
      email: "",
      website: "",
      logo: "",
      banner: "",
      ownerId: "",
    },
  });

  const handleClose = () => {
    reset();
    setIsVerified(false);
    setSelectedOwnerId("");
    onClose();
  };

  const onSubmit = async (data: StoreFormData) => {
    setIsSubmitting(true);
    try {
      // Include verification status and owner ID
      const createData = {
        ...data,
        isVerified,
        ownerId: selectedOwnerId,
      };

      const response = await fetch(`/api/admin/stores`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(createData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create store");
      }

      toast.success("Store created successfully");
      onStoreCreated();
      handleClose();
    } catch (error) {
      console.error("Error creating store:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create store");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUserSelect = (userId: string) => {
    setSelectedOwnerId(userId);
    setValue("ownerId", userId);
  };

  // Generate a slug from the store name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-+|-+$/g, "");
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    const slug = generateSlug(name);
    setValue("slug", slug);
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Create New Store">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Store Name *
            </label>
            <Input
              id="name"
              {...register("name")}
              onChange={handleNameChange}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
              Slug *
            </label>
            <Input
              id="slug"
              {...register("slug")}
              className={errors.slug ? "border-red-500" : ""}
            />
            {errors.slug && (
              <p className="mt-1 text-xs text-red-500">{errors.slug.message}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <Textarea
            id="description"
            {...register("description")}
            className={errors.description ? "border-red-500" : ""}
            rows={3}
          />
          {errors.description && (
            <p className="mt-1 text-xs text-red-500">{errors.description.message}</p>
          )}
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="location" className="block text-sm font-medium text-gray-700">
              Location
            </label>
            <Input
              id="location"
              {...register("location")}
              className={errors.location ? "border-red-500" : ""}
            />
            {errors.location && (
              <p className="mt-1 text-xs text-red-500">{errors.location.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
              Phone
            </label>
            <Input
              id="phone"
              {...register("phone")}
              className={errors.phone ? "border-red-500" : ""}
            />
            {errors.phone && (
              <p className="mt-1 text-xs text-red-500">{errors.phone.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <Input
              id="email"
              type="email"
              {...register("email")}
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-700">
              Website
            </label>
            <Input
              id="website"
              {...register("website")}
              className={errors.website ? "border-red-500" : ""}
            />
            {errors.website && (
              <p className="mt-1 text-xs text-red-500">{errors.website.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="logo" className="block text-sm font-medium text-gray-700">
              Logo URL
            </label>
            <Input
              id="logo"
              {...register("logo")}
              className={errors.logo ? "border-red-500" : ""}
              placeholder="https://example.com/logo.png"
            />
            {errors.logo && (
              <p className="mt-1 text-xs text-red-500">{errors.logo.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="banner" className="block text-sm font-medium text-gray-700">
              Banner URL
            </label>
            <Input
              id="banner"
              {...register("banner")}
              className={errors.banner ? "border-red-500" : ""}
              placeholder="https://example.com/banner.png"
            />
            {errors.banner && (
              <p className="mt-1 text-xs text-red-500">{errors.banner.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Store Owner *
          </label>
          <UserSelect 
            onUserSelect={handleUserSelect} 
            selectedUserId={selectedOwnerId}
          />
          {errors.ownerId && (
            <p className="mt-1 text-xs text-red-500">{errors.ownerId.message}</p>
          )}
        </div>

        <div className="flex items-center">
          <Switch
            id="isVerified"
            checked={isVerified}
            onCheckedChange={setIsVerified}
          />
          <label htmlFor="isVerified" className="ml-2 block text-sm font-medium text-gray-700">
            Verified Store
          </label>
        </div>

        <div className="mt-5 flex justify-end space-x-3">
          <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
            Create Store
          </Button>
        </div>
      </form>
    </Modal>
  );
}
