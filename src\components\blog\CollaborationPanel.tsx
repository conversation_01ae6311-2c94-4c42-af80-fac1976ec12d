"use client";

import React, { useState, useEffect } from 'react';
import {
  UsersIcon,
  EyeIcon,
  PencilIcon,
  ChatBubbleLeftIcon,
  BellIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

interface User {
  id: string;
  name: string;
  avatar?: string;
  color: string;
  status: 'online' | 'away' | 'offline';
  cursor?: {
    position: number;
    selection?: { start: number; end: number };
  };
}

interface Comment {
  id: string;
  userId: string;
  content: string;
  position: number;
  timestamp: Date;
  resolved: boolean;
}

interface CollaborationPanelProps {
  isOpen: boolean;
  onClose: () => void;
  currentUser: User;
  collaborators: User[];
  comments: Comment[];
  onAddComment: (content: string, position: number) => void;
  onResolveComment: (commentId: string) => void;
  documentId: string;
}

export const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  isOpen,
  onClose,
  currentUser,
  collaborators,
  comments,
  onAddComment,
  onResolveComment,
  documentId
}) => {
  const [activeTab, setActiveTab] = useState<'users' | 'comments' | 'activity'>('users');
  const [newComment, setNewComment] = useState('');
  const [commentPosition, setCommentPosition] = useState(0);
  const [showCommentForm, setShowCommentForm] = useState(false);

  // Mock activity data
  const [activities] = useState([
    {
      id: '1',
      userId: 'user2',
      type: 'edit',
      description: 'Made changes to paragraph 3',
      timestamp: new Date(Date.now() - 5 * 60000)
    },
    {
      id: '2',
      userId: 'user3',
      type: 'comment',
      description: 'Added a comment',
      timestamp: new Date(Date.now() - 15 * 60000)
    },
    {
      id: '3',
      userId: 'user2',
      type: 'join',
      description: 'Joined the document',
      timestamp: new Date(Date.now() - 30 * 60000)
    }
  ]);

  const getUserById = (userId: string) => {
    return collaborators.find(user => user.id === userId) || currentUser;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      default: return 'bg-gray-400';
    }
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      onAddComment(newComment.trim(), commentPosition);
      setNewComment('');
      setShowCommentForm(false);
    }
  };

  const unresolvedComments = comments.filter(comment => !comment.resolved);
  const resolvedComments = comments.filter(comment => comment.resolved);

  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-80 bg-white border-l border-gray-200 shadow-lg z-40 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <UsersIcon className="h-5 w-5" />
          <h3 className="font-semibold">Collaboration</h3>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('users')}
          className={`flex-1 px-3 py-2 text-sm font-medium ${
            activeTab === 'users'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Users ({collaborators.length + 1})
        </button>
        <button
          onClick={() => setActiveTab('comments')}
          className={`flex-1 px-3 py-2 text-sm font-medium ${
            activeTab === 'comments'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Comments ({unresolvedComments.length})
        </button>
        <button
          onClick={() => setActiveTab('activity')}
          className={`flex-1 px-3 py-2 text-sm font-medium ${
            activeTab === 'activity'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Activity
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'users' && (
          <div className="p-4 space-y-3">
            {/* Current User */}
            <div className="flex items-center space-x-3 p-2 bg-blue-50 rounded-lg">
              <div className="relative">
                <div 
                  className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                  style={{ backgroundColor: currentUser.color }}
                >
                  {currentUser.name.charAt(0).toUpperCase()}
                </div>
                <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(currentUser.status)}`} />
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">{currentUser.name} (You)</div>
                <div className="text-xs text-gray-500 capitalize">{currentUser.status}</div>
              </div>
              <PencilIcon className="h-4 w-4 text-blue-600" />
            </div>

            {/* Collaborators */}
            {collaborators.map((user) => (
              <div key={user.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg">
                <div className="relative">
                  <div 
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                    style={{ backgroundColor: user.color }}
                  >
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                  <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(user.status)}`} />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">{user.name}</div>
                  <div className="text-xs text-gray-500 capitalize">{user.status}</div>
                </div>
                {user.status === 'online' && (
                  <EyeIcon className="h-4 w-4 text-green-600" />
                )}
              </div>
            ))}

            {/* Invite Button */}
            <button className="w-full p-2 border-2 border-dashed border-gray-300 rounded-lg text-sm text-gray-500 hover:border-gray-400 hover:text-gray-600">
              + Invite collaborators
            </button>
          </div>
        )}

        {activeTab === 'comments' && (
          <div className="p-4 space-y-4">
            {/* Add Comment Button */}
            <button
              onClick={() => setShowCommentForm(true)}
              className="w-full flex items-center justify-center space-x-2 p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <ChatBubbleLeftIcon className="h-4 w-4" />
              <span>Add Comment</span>
            </button>

            {/* Comment Form */}
            {showCommentForm && (
              <div className="border border-gray-200 rounded-lg p-3 space-y-3">
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add your comment..."
                  className="w-full h-20 px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => setShowCommentForm(false)}
                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50"
                  >
                    Comment
                  </button>
                </div>
              </div>
            )}

            {/* Unresolved Comments */}
            {unresolvedComments.length > 0 && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">
                  Open Comments ({unresolvedComments.length})
                </h4>
                <div className="space-y-3">
                  {unresolvedComments.map((comment) => {
                    const user = getUserById(comment.userId);
                    return (
                      <div key={comment.id} className="border border-gray-200 rounded-lg p-3">
                        <div className="flex items-start space-x-2 mb-2">
                          <div 
                            className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                            style={{ backgroundColor: user.color }}
                          >
                            {user.name.charAt(0).toUpperCase()}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{user.name}</div>
                            <div className="text-xs text-gray-500">
                              {formatDistanceToNow(comment.timestamp, { addSuffix: true })}
                            </div>
                          </div>
                        </div>
                        <p className="text-sm text-gray-700 mb-2">{comment.content}</p>
                        <button
                          onClick={() => onResolveComment(comment.id)}
                          className="text-xs text-blue-600 hover:text-blue-700"
                        >
                          Resolve
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Resolved Comments */}
            {resolvedComments.length > 0 && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">
                  Resolved ({resolvedComments.length})
                </h4>
                <div className="space-y-2">
                  {resolvedComments.map((comment) => {
                    const user = getUserById(comment.userId);
                    return (
                      <div key={comment.id} className="border border-gray-100 rounded-lg p-2 opacity-75">
                        <div className="flex items-center space-x-2 mb-1">
                          <div 
                            className="w-5 h-5 rounded-full flex items-center justify-center text-white text-xs"
                            style={{ backgroundColor: user.color }}
                          >
                            {user.name.charAt(0).toUpperCase()}
                          </div>
                          <span className="text-xs font-medium">{user.name}</span>
                          <span className="text-xs text-gray-500">resolved</span>
                        </div>
                        <p className="text-xs text-gray-600">{comment.content}</p>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {comments.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <ChatBubbleLeftIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No comments yet</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="p-4 space-y-3">
            {activities.map((activity) => {
              const user = getUserById(activity.userId);
              return (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div 
                    className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                    style={{ backgroundColor: user.color }}
                  >
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">{user.name}</span>{' '}
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Document ID: {documentId.substring(0, 8)}...</span>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Auto-saved</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollaborationPanel;
