import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { products, stores } from "@/lib/db/schema";
import { desc, eq, and, isNotNull } from "drizzle-orm";

// GET /api/marketplace/featured-products - Get featured products for carousel
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '12'), 20); // Max 20 products

    // Get featured products (relaxed conditions for testing)
    const featuredProducts = await db
      .select({
        id: products.id,
        title: products.title,
        price: products.price,
        condition: products.item_condition,
        photos: products.photos,
        createdAt: products.createdAt,
        viewCount: products.viewCount,
        store: {
          id: stores.id,
          name: stores.name,
          logo: stores.logo,
        },
      })
      .from(products)
      .innerJoin(stores, eq(products.storeId, stores.id))
      .where(
        // Relaxed conditions - just need products with stores
        isNotNull(products.photos) // Only products with photos
      )
      .orderBy(
        desc(products.createdAt),   // Order by recency first
        desc(products.viewCount)    // Then by popularity
      )
      .limit(limit);



    // Format the response
    const formattedProducts = featuredProducts.map((product) => ({
      id: product.id,
      title: product.title,
      price: product.price,
      condition: product.condition,
      photos: product.photos,
      createdAt: product.createdAt.toISOString(),
      store: product.store,
    }));

    return NextResponse.json({
      success: true,
      data: formattedProducts,
      meta: {
        total: formattedProducts.length,
        limit,
      },
    });
  } catch (error) {
    console.error("Error fetching featured products:", error);
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error" 
      },
      { status: 500 }
    );
  }
}
