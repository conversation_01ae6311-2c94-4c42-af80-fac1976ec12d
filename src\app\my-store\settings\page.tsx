import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { stores, products, storeSettings } from "@/lib/db/schema";
import { eq, count } from "drizzle-orm";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { ChevronLeftIcon } from "@heroicons/react/24/outline";
import { StoreSettingsForm } from "@/components/marketplace/StoreSettingsForm";

export default async function StoreSettingsPage() {
  const user = await requireAuth();

  // Check if user has a store
  const userStore = await db
    .select({
      id: stores.id,
      name: stores.name,
      slug: stores.slug,
      isVerified: stores.isVerified,
    })
    .from(stores)
    .where(eq(stores.ownerId, user.id))
    .limit(1);

  // If user doesn't have a store, redirect to create store page
  if (userStore.length === 0) {
    redirect("/marketplace/create-store");
  }

  const store = userStore[0];

  // Get product count
  const productCountResult = await db
    .select({ count: count() })
    .from(products)
    .where(eq(products.storeId, store.id));

  const productCount = productCountResult[0]?.count || 0;

  // Get store settings
  const storeSettingsData = await db
    .select()
    .from(storeSettings)
    .where(eq(storeSettings.storeId, store.id))
    .limit(1);

  const initialSettings = storeSettingsData.length > 0 ? storeSettingsData[0] : undefined;

  return (
    <MainLayout>
      <div className="mx-auto max-w-3xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link href="/my-store/dashboard">
            <Button variant="outline" size="sm">
              <ChevronLeftIcon className="h-4 w-4 mr-1" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">
            Store Settings
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your store settings and preferences
          </p>
        </div>

        <StoreSettingsForm
          storeId={store.id}
          initialSettings={initialSettings}
          productCount={productCount}
        />
      </div>
    </MainLayout>
  );
}
