"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import Image from "next/image";
import { UserIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useDebounce } from "@/lib/hooks/useDebounce";

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface UserSelectProps {
  onUserSelect: (userId: string) => void;
  selectedUserId?: string;
}

export function UserSelect({ onUserSelect, selectedUserId }: UserSelectProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Fetch users based on search term
  useEffect(() => {
    if (debouncedSearchTerm.length < 2) {
      setUsers([]);
      return;
    }

    const fetchUsers = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/admin/users/search?q=${debouncedSearchTerm}`);
        if (!response.ok) {
          throw new Error("Failed to fetch users");
        }
        const data = await response.json();
        setUsers(data.users || []);
      } catch (error) {
        console.error("Error fetching users:", error);
        setUsers([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [debouncedSearchTerm]);

  // Fetch selected user details if selectedUserId is provided
  useEffect(() => {
    if (selectedUserId && !selectedUser) {
      const fetchUser = async () => {
        try {
          const response = await fetch(`/api/admin/users/${selectedUserId}`);
          if (response.ok) {
            const data = await response.json();
            setSelectedUser(data);
          }
        } catch (error) {
          console.error("Error fetching selected user:", error);
        }
      };

      fetchUser();
    }
  }, [selectedUserId, selectedUser]);

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
    onUserSelect(user.id);
    setShowDropdown(false);
    setSearchTerm("");
  };

  const handleInputFocus = () => {
    setShowDropdown(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (e.target.value === "") {
      setSelectedUser(null);
    }
    setShowDropdown(true);
  };

  const handleClearSelection = () => {
    setSelectedUser(null);
    onUserSelect("");
    setSearchTerm("");
  };

  return (
    <div className="relative">
      {selectedUser ? (
        <div className="flex items-center justify-between rounded-md border border-gray-300 p-2">
          <div className="flex items-center">
            {selectedUser.image ? (
              <Image
                src={selectedUser.image}
                alt={selectedUser.name || "User"}
                width={32}
                height={32}
                className="h-8 w-8 rounded-full"
              />
            ) : (
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                <UserIcon className="h-5 w-5 text-gray-500" />
              </div>
            )}
            <div className="ml-2">
              <div className="text-sm font-medium">{selectedUser.name || "Unnamed User"}</div>
              <div className="text-xs text-gray-500">{selectedUser.email}</div>
            </div>
          </div>
          <button
            type="button"
            onClick={handleClearSelection}
            className="text-gray-400 hover:text-gray-500"
          >
            &times;
          </button>
        </div>
      ) : (
        <div className="relative">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="Search users by name or email..."
            value={searchTerm}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            className="pl-10"
          />
        </div>
      )}

      {showDropdown && searchTerm.length >= 2 && (
        <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
          {isLoading ? (
            <div className="flex items-center justify-center py-2">
              <Spinner size="sm" />
            </div>
          ) : users.length > 0 ? (
            users.map((user) => (
              <div
                key={user.id}
                className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                onClick={() => handleUserClick(user)}
              >
                <div className="flex items-center">
                  {user.image ? (
                    <Image
                      src={user.image}
                      alt={user.name || "User"}
                      width={24}
                      height={24}
                      className="h-6 w-6 rounded-full"
                    />
                  ) : (
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200">
                      <UserIcon className="h-4 w-4 text-gray-500" />
                    </div>
                  )}
                  <div className="ml-2">
                    <div className="text-sm font-medium">{user.name || "Unnamed User"}</div>
                    <div className="text-xs text-gray-500">{user.email}</div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="px-4 py-2 text-sm text-gray-500">No users found</div>
          )}
        </div>
      )}
    </div>
  );
}
