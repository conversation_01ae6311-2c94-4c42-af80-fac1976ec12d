"use client";

import Link from "next/link";
import Image from "next/image";
import { CalendarIcon, MapPinIcon, ClockIcon } from "@heroicons/react/24/outline";

interface Event {
  id: string;
  name: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  category: string | null;
  coverImage: string | null;
}

interface EventCardProps {
  event: Event;
}

export function EventCard({ event }: EventCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  return (
    <Link href={`/events/${event.id}`}>
      <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 overflow-hidden">
        {/* Cover Image */}
        <div className="h-24 bg-gradient-to-r from-green-500 to-blue-600 relative">
          {event.coverImage ? (
            <Image
              src={event.coverImage}
              alt={event.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600" />
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          <h3 className="text-sm font-semibold text-gray-900 line-clamp-1 mb-2">
            {event.name}
          </h3>

          {/* Date and Time */}
          <div className="flex items-center text-xs text-gray-600 mb-2">
            <CalendarIcon className="w-4 h-4 mr-1" />
            <span>{formatDate(event.startTime)}</span>
            <ClockIcon className="w-4 h-4 ml-3 mr-1" />
            <span>{formatTime(event.startTime)}</span>
          </div>

          {/* Location */}
          {event.location && (
            <div className="flex items-center text-xs text-gray-600 mb-2">
              <MapPinIcon className="w-4 h-4 mr-1" />
              <span className="line-clamp-1">{event.location}</span>
            </div>
          )}

          {/* Description */}
          {event.description && (
            <p className="text-xs text-gray-600 line-clamp-2 mb-2">
              {event.description}
            </p>
          )}

          {/* Category */}
          {event.category && (
            <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
              {event.category}
            </span>
          )}
        </div>
      </div>
    </Link>
  );
}
