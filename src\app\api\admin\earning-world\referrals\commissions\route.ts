import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCommissions, users } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

// Get detailed commission information for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all commissions with user information
    const commissionsResult = await db
      .select({
        id: referralCommissions.id,
        referrerId: referralCommissions.referrerId,
        referredUserId: referralCommissions.referredUserId,
        planName: referralCommissions.planName,
        subscriptionAmount: referralCommissions.subscriptionAmount,
        commissionType: referralCommissions.commissionType,
        commissionRate: referralCommissions.commissionRate,
        commissionAmount: referralCommissions.commissionAmount,
        isFirstPurchase: referralCommissions.isFirstPurchase,
        status: referralCommissions.status,
        approvedAt: referralCommissions.approvedAt,
        paidAt: referralCommissions.paidAt,
        cancelledAt: referralCommissions.cancelledAt,
        cancelReason: referralCommissions.cancelReason,
        createdAt: referralCommissions.createdAt,
        referrerName: users.name,
        referrerEmail: users.email,
      })
      .from(referralCommissions)
      .innerJoin(users, eq(referralCommissions.referrerId, users.id))
      .orderBy(desc(referralCommissions.createdAt));

    // Get referred user information separately
    const referredUsersResult = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
      })
      .from(users);

    // Create a map for referred users
    const referredUsersMap = new Map(
      referredUsersResult.map(user => [user.id, user])
    );

    // Combine data
    const commissions = commissionsResult.map(commission => {
      const referredUser = referredUsersMap.get(commission.referredUserId);
      
      return {
        id: commission.id,
        referrer: {
          id: commission.referrerId,
          name: commission.referrerName || 'Unknown User',
          email: commission.referrerEmail || '',
        },
        referredUser: {
          id: commission.referredUserId,
          name: referredUser?.name || 'Unknown User',
          email: referredUser?.email || '',
        },
        planName: commission.planName,
        subscriptionAmount: parseFloat(commission.subscriptionAmount),
        commissionType: commission.commissionType,
        commissionRate: parseFloat(commission.commissionRate),
        commissionAmount: parseFloat(commission.commissionAmount),
        isFirstPurchase: commission.isFirstPurchase,
        status: commission.status,
        createdAt: commission.createdAt.toISOString(),
        approvedAt: commission.approvedAt?.toISOString(),
        paidAt: commission.paidAt?.toISOString(),
        cancelledAt: commission.cancelledAt?.toISOString(),
        cancelReason: commission.cancelReason,
      };
    });

    return NextResponse.json({
      success: true,
      data: commissions,
    });

  } catch (error) {
    console.error("Error fetching commission details:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch commission details"
      },
      { status: 500 }
    );
  }
}
