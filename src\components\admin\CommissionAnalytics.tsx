"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  UsersIcon,
  CalendarIcon,
  ArrowPathIcon
} from "@heroicons/react/24/outline";

interface CommissionAnalytics {
  totalCommissions: number;
  totalReferrals: number;
  averageCommission: number;
  topPerformingPlans: Array<{
    planName: string;
    totalCommissions: number;
    referralCount: number;
    averageCommission: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    commissions: number;
    referrals: number;
  }>;
  commissionsByType: {
    percentage: number;
    fixed: number;
  };
}

interface CommissionAnalyticsProps {
  refreshTrigger?: number;
}

export function CommissionAnalytics({ refreshTrigger }: CommissionAnalyticsProps) {
  const [analytics, setAnalytics] = useState<CommissionAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchAnalytics = async () => {
    try {
      const response = await fetch('/api/admin/earning-world/referrals/analytics');
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.data);
      }
    } catch (error) {
      console.error('Error fetching commission analytics:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [refreshTrigger]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchAnalytics();
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <Spinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No analytics data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <ChartBarIcon className="h-6 w-6 mr-2 text-blue-600" />
            Commission Analytics
          </h3>
          <p className="text-sm text-gray-500">Overview of referral commission performance</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-green-100">
                <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Commissions</p>
                <p className="text-2xl font-bold text-green-600">
                  ${analytics.totalCommissions.toFixed(2)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-blue-100">
                <UsersIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Referrals</p>
                <p className="text-2xl font-bold text-blue-600">
                  {analytics.totalReferrals.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-purple-100">
                <ArrowTrendingUpIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Average Commission</p>
                <p className="text-2xl font-bold text-purple-600">
                  ${analytics.averageCommission.toFixed(2)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-orange-100">
                <ChartBarIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                <p className="text-2xl font-bold text-orange-600">
                  {analytics.totalReferrals > 0 
                    ? ((analytics.totalCommissions / analytics.totalReferrals) * 100).toFixed(1)
                    : '0.0'
                  }%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Plans */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ArrowTrendingUpIcon className="h-5 w-5 mr-2 text-green-600" />
            Top Performing Plans
          </CardTitle>
          <CardDescription>
            Subscription plans generating the most commission revenue
          </CardDescription>
        </CardHeader>
        <CardContent>
          {analytics.topPerformingPlans.length > 0 ? (
            <div className="space-y-4">
              {analytics.topPerformingPlans.map((plan, index) => (
                <div key={plan.planName} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      index === 0 ? 'bg-yellow-100 text-yellow-800' :
                      index === 1 ? 'bg-gray-100 text-gray-800' :
                      index === 2 ? 'bg-orange-100 text-orange-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="ml-3">
                      <p className="font-medium text-gray-900">{plan.planName}</p>
                      <p className="text-sm text-gray-500">{plan.referralCount} referrals</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">${plan.totalCommissions.toFixed(2)}</p>
                    <p className="text-sm text-gray-500">Avg: ${plan.averageCommission.toFixed(2)}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No commission data available yet</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Commission Types Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CurrencyDollarIcon className="h-5 w-5 mr-2 text-blue-600" />
            Commission Types Distribution
          </CardTitle>
          <CardDescription>
            Breakdown of commission earnings by type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl font-bold text-blue-600">%</span>
              </div>
              <h4 className="font-medium text-gray-900">Percentage-based</h4>
              <p className="text-2xl font-bold text-blue-600">${analytics.commissionsByType.percentage.toFixed(2)}</p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl font-bold text-purple-600">$</span>
              </div>
              <h4 className="font-medium text-gray-900">Fixed Amount</h4>
              <p className="text-2xl font-bold text-purple-600">${analytics.commissionsByType.fixed.toFixed(2)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
