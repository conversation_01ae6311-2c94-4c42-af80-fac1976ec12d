import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user's pending payouts
    const result = await WalletService.getUserPendingPayouts(session.user.id);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error("Error fetching pending payouts:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to fetch pending payouts" 
      },
      { status: 500 }
    );
  }
}
