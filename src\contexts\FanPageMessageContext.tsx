"use client";

import { createContext, useContext, useState, useCallback, ReactNode, useEffect } from "react";
import { useSession } from "next-auth/react";

interface FanPageMessage {
  id: string;
  content: string;
  read: boolean;
  createdAt: string;
  isFromPage?: boolean;
  sender: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface FanPageMessageState {
  isOpen: boolean;
  pageId: string | null;
  pageName: string | null;
  pageImage: string | null;
  messages: FanPageMessage[];
  isLoading: boolean;
  unreadCount: number;
}

interface FanPageMessageContextType {
  state: FanPageMessageState;
  openMessageBox: (pageId: string, pageName: string, pageImage?: string | null) => void;
  closeMessageBox: () => void;
  sendMessage: (content: string) => Promise<void>;
  markAsRead: () => Promise<void>;
  addMessage: (message: FanPageMessage) => void;
  clearAllData: () => void;
}

const FanPageMessageContext = createContext<FanPageMessageContextType | undefined>(undefined);

const initialState: FanPageMessageState = {
  isOpen: false,
  pageId: null,
  pageName: null,
  pageImage: null,
  messages: [],
  isLoading: false,
  unreadCount: 0,
};

interface FanPageMessageProviderProps {
  children: ReactNode;
}

export function FanPageMessageProvider({ children }: FanPageMessageProviderProps) {
  const { data: session } = useSession();
  const [state, setState] = useState<FanPageMessageState>(initialState);

  // LocalStorage keys
  const getStorageKey = (pageId: string) => `fanpage_messages_${pageId}`;
  const getStateStorageKey = () => `fanpage_message_state`;

  // Load state from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem(getStateStorageKey());
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          if (parsedState.pageId) {
            // Load messages for the saved page
            const savedMessages = localStorage.getItem(getStorageKey(parsedState.pageId));
            if (savedMessages) {
              const messages = JSON.parse(savedMessages);
              setState(prev => ({
                ...prev,
                ...parsedState,
                messages,
              }));
            }
          }
        }
      } catch (error) {
        console.error('Error loading fan page message state from localStorage:', error);
      }
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined' && state.pageId) {
      try {
        // Save messages
        localStorage.setItem(getStorageKey(state.pageId), JSON.stringify(state.messages));

        // Save state (without messages to avoid duplication)
        const stateToSave = {
          isOpen: state.isOpen,
          pageId: state.pageId,
          pageName: state.pageName,
          pageImage: state.pageImage,
          unreadCount: state.unreadCount,
        };
        localStorage.setItem(getStateStorageKey(), JSON.stringify(stateToSave));
      } catch (error) {
        console.error('Error saving fan page message state to localStorage:', error);
      }
    }
  }, [state]);

  // Real-time fan page messaging disabled - Socket.IO removed
  // TODO: Implement alternative real-time messaging system

  const openMessageBox = useCallback(async (pageId: string, pageName: string, pageImage?: string | null) => {
    try {
      // First check if user is the page owner
      const pageCheckResponse = await fetch(`/api/fan-pages/${pageId}`);
      if (pageCheckResponse.ok) {
        const pageData = await pageCheckResponse.json();

        // If user is the page owner, don't open message box
        if (pageData.isOwner) {
          console.log("Page owners should use Fan Page Messages section");
          return;
        }
      }
    } catch (error) {
      console.error("Error checking page ownership:", error);
    }

    // Load cached messages first
    let cachedMessages: FanPageMessage[] = [];
    if (typeof window !== 'undefined') {
      try {
        const savedMessages = localStorage.getItem(getStorageKey(pageId));
        if (savedMessages) {
          cachedMessages = JSON.parse(savedMessages);
        }
      } catch (error) {
        console.error('Error loading cached messages:', error);
      }
    }

    setState(prev => ({
      ...prev,
      isOpen: true,
      pageId,
      pageName,
      pageImage: pageImage || null,
      messages: cachedMessages,
      isLoading: true,
    }));

    try {
      // Fetch latest messages from server
      const response = await fetch(`/api/fan-pages/${pageId}/messages`);
      if (response.ok) {
        const data = await response.json();
        setState(prev => ({
          ...prev,
          messages: data.messages || [],
          isLoading: false,
        }));
      } else {
        setState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error("Error fetching messages:", error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [getStorageKey]);

  const closeMessageBox = useCallback(() => {
    setState(initialState);
    // Clear localStorage when closing
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem(getStateStorageKey());
      } catch (error) {
        console.error('Error clearing fan page message state:', error);
      }
    }
  }, [getStateStorageKey]);

  const sendMessage = useCallback(async (content: string) => {
    if (!state.pageId || !session?.user?.id) return;

    try {
      const response = await fetch(`/api/fan-pages/${state.pageId}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content }),
      });

      if (response.ok) {
        const data = await response.json();
        const newMessage = data.data;

        setState(prev => ({
          ...prev,
          messages: [newMessage, ...prev.messages],
        }));

        // Real-time messaging disabled - Socket.IO removed
        // TODO: Implement alternative real-time messaging system
      }
    } catch (error) {
      console.error("Error sending message:", error);
      throw error;
    }
  }, [state.pageId, session?.user?.id]);

  const markAsRead = useCallback(async () => {
    if (!state.pageId) return;

    try {
      await fetch(`/api/fan-pages/${state.pageId}/messages`, {
        method: "PUT",
      });

      setState(prev => ({
        ...prev,
        unreadCount: 0,
        messages: prev.messages.map(msg => ({ ...msg, read: true })),
      }));
    } catch (error) {
      console.error("Error marking messages as read:", error);
    }
  }, [state.pageId]);

  const addMessage = useCallback((message: FanPageMessage) => {
    setState(prev => ({
      ...prev,
      messages: [message, ...prev.messages],
      unreadCount: prev.unreadCount + 1,
    }));
  }, []);

  // Clear all localStorage data (useful for logout)
  const clearAllData = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        // Clear state
        localStorage.removeItem(getStateStorageKey());

        // Clear all fan page message data
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('fanpage_messages_')) {
            localStorage.removeItem(key);
          }
        });

        setState(initialState);
      } catch (error) {
        console.error('Error clearing all fan page message data:', error);
      }
    }
  }, [getStateStorageKey]);

  const value: FanPageMessageContextType = {
    state,
    openMessageBox,
    closeMessageBox,
    sendMessage,
    markAsRead,
    addMessage,
    clearAllData,
  };

  return (
    <FanPageMessageContext.Provider value={value}>
      {children}
    </FanPageMessageContext.Provider>
  );
}

export function useFanPageMessage() {
  const context = useContext(FanPageMessageContext);
  if (context === undefined) {
    throw new Error("useFanPageMessage must be used within a FanPageMessageProvider");
  }
  return context;
}
