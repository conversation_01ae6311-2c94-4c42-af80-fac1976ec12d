import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { status } = await request.json();

    // Validate status
    const validStatuses = ['online', 'away', 'offline'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be 'online', 'away', or 'offline'" },
        { status: 400 }
      );
    }

    // Update user's status (lastSeen will be implemented later)
    await db
      .update(users)
      .set({
        updatedAt: new Date()
      })
      .where(eq(users.id, session.user.id));

    return NextResponse.json({
      message: "Status updated successfully",
      status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Error updating user status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET endpoint to check current user's status
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await db
      .select({
        showOnlineStatus: users.showOnlineStatus,
        updatedAt: users.updatedAt
      })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // For now, return a default status (will be improved with real lastSeen data)
    const status = 'online'; // Simplified for demo

    return NextResponse.json({
      status,
      lastSeen: null, // Will be implemented when lastSeen column is added
      showOnlineStatus: user[0].showOnlineStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Error getting user status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
