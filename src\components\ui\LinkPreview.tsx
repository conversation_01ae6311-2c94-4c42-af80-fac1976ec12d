"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { ArrowTopRightOnSquareIcon, GlobeAltIcon } from "@heroicons/react/24/outline";
import { SafeImage } from "./SafeImage";

interface LinkPreviewData {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  siteName?: string;
  favicon?: string;
}

interface LinkPreviewProps {
  url: string;
  className?: string;
}

export function LinkPreview({ url, className = "" }: LinkPreviewProps) {
  const [preview, setPreview] = useState<LinkPreviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    const fetchPreview = async () => {
      try {
        setLoading(true);
        setError(false);

        const response = await fetch('/api/link-preview', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ url }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch preview');
        }

        const data = await response.json();
        setPreview(data);
      } catch (err) {
        console.error('Error fetching link preview:', err);
        setError(true);
        // Fallback preview with just the URL
        setPreview({
          url,
          title: new URL(url).hostname,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPreview();
  }, [url]);

  if (loading) {
    return (
      <div className={`border border-gray-200 rounded-lg p-4 animate-pulse ${className}`}>
        <div className="flex space-x-3">
          <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!preview) {
    return null;
  }

  return (
    <Link
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={`block border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200 overflow-hidden ${className}`}
    >
      <div className="flex">
        {/* Image */}
        {preview.image && (
          <div className="w-24 h-24 flex-shrink-0 relative">
            <SafeImage
              src={preview.image}
              alt={preview.title || "Link preview"}
              fill
              className="object-cover"
              onError={() => {
                // Hide image on error
                setPreview(prev => prev ? { ...prev, image: undefined } : null);
              }}
            />
          </div>
        )}

        {/* Content */}
        <div className="flex-1 p-4 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              {/* Title */}
              <h3 className="font-medium text-gray-900 text-sm mb-1" style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}>
                {preview.title || new URL(url).hostname}
              </h3>

              {/* Description */}
              {preview.description && (
                <p className="text-gray-600 text-xs mb-2" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}>
                  {preview.description}
                </p>
              )}

              {/* Site info */}
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                {preview.favicon ? (
                  <SafeImage
                    src={preview.favicon}
                    alt=""
                    width={16}
                    height={16}
                    className="rounded"
                    onError={() => {
                      // Hide favicon on error
                      setPreview(prev => prev ? { ...prev, favicon: undefined } : null);
                    }}
                  />
                ) : (
                  <GlobeAltIcon className="w-4 h-4" />
                )}
                <span className="truncate">
                  {preview.siteName || new URL(url).hostname}
                </span>
              </div>
            </div>

            {/* External link icon */}
            <ArrowTopRightOnSquareIcon className="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" />
          </div>
        </div>
      </div>
    </Link>
  );
}

// Utility function to detect URLs in text
export function detectUrls(text: string): string[] {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.match(urlRegex) || [];
}

// Utility function to check if a string is a valid URL
export function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}
