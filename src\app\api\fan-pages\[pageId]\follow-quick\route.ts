import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageFollowers } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// POST /api/fan-pages/[pageId]/follow-quick - Quick follow/unfollow with minimal response
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { action } = await request.json(); // "follow" or "unfollow"

    // Check if fan page exists
    const pageResult = await db
      .select({ id: fanPages.id, followerCount: fanPages.followerCount })
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];

    // Check current follow status
    const existingFollow = await db
      .select()
      .from(fanPageFollowers)
      .where(
        and(
          eq(fanPageFollowers.userId, session.user.id),
          eq(fanPageFollowers.fanPageId, pageId)
        )
      )
      .limit(1);

    const isCurrentlyFollowing = existingFollow.length > 0;

    if (action === "follow" && !isCurrentlyFollowing) {
      // Follow
      await Promise.all([
        db.insert(fanPageFollowers).values({
          id: uuidv4(),
          userId: session.user.id,
          fanPageId: pageId,
        }),
        db
          .update(fanPages)
          .set({
            followerCount: page.followerCount + 1,
            updatedAt: new Date(),
          })
          .where(eq(fanPages.id, pageId)),
      ]);

      return NextResponse.json({
        success: true,
        isFollowing: true,
        followerCount: page.followerCount + 1,
      });

    } else if (action === "unfollow" && isCurrentlyFollowing) {
      // Unfollow
      await Promise.all([
        db
          .delete(fanPageFollowers)
          .where(
            and(
              eq(fanPageFollowers.userId, session.user.id),
              eq(fanPageFollowers.fanPageId, pageId)
            )
          ),
        db
          .update(fanPages)
          .set({
            followerCount: Math.max(0, page.followerCount - 1),
            updatedAt: new Date(),
          })
          .where(eq(fanPages.id, pageId)),
      ]);

      return NextResponse.json({
        success: true,
        isFollowing: false,
        followerCount: Math.max(0, page.followerCount - 1),
      });

    } else {
      // No change needed
      return NextResponse.json({
        success: true,
        isFollowing: isCurrentlyFollowing,
        followerCount: page.followerCount,
      });
    }

  } catch (error) {
    console.error("Error in quick follow:", error);
    return NextResponse.json(
      { error: "Failed to update follow status" },
      { status: 500 }
    );
  }
}
