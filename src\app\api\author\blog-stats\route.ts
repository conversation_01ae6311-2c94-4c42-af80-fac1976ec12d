import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogMonetization } from "@/lib/db/schema";
import { eq, desc, sql, and } from "drizzle-orm";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    const url = new URL(req.url);
    const authorId = url.searchParams.get("authorId");

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is requesting their own stats or is admin
    if (session.user.id !== authorId && !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      );
    }

    if (!authorId) {
      return NextResponse.json(
        { message: "Author ID is required" },
        { status: 400 }
      );
    }

    // Get basic blog stats
    const totalBlogsResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(eq(blogs.authorId, authorId));

    const publishedBlogsResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(and(eq(blogs.authorId, authorId), eq(blogs.status, 'published')));

    const draftBlogsResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(and(eq(blogs.authorId, authorId), eq(blogs.status, 'draft')));

    const totalViewsResult = await db
      .select({ totalViews: sql<number>`COALESCE(SUM(${blogs.viewCount}), 0)` })
      .from(blogs)
      .where(eq(blogs.authorId, authorId));

    // Get monetization stats
    const monetizationStatsResult = await db
      .select({
        monetizedCount: sql<number>`COUNT(CASE WHEN ${blogMonetization.isApproved} = true THEN 1 END)`,
        pendingCount: sql<number>`COUNT(CASE WHEN ${blogMonetization.isApproved} = false AND ${blogMonetization.rejectedAt} IS NULL THEN 1 END)`,
        totalEarnings: sql<string>`COALESCE(SUM(CASE WHEN ${blogMonetization.isApproved} = true THEN ${blogMonetization.totalEarnings} ELSE 0 END), 0)`
      })
      .from(blogs)
      .leftJoin(blogMonetization, eq(blogs.id, blogMonetization.blogId))
      .where(eq(blogs.authorId, authorId));

    // Get recent blogs with monetization status
    const recentBlogs = await db.query.blogs.findMany({
      where: eq(blogs.authorId, authorId),
      orderBy: [desc(blogs.createdAt)],
      limit: 5,
      with: {
        monetization: true,
      },
    });

    const totalBlogs = totalBlogsResult[0]?.count || 0;
    const publishedBlogs = publishedBlogsResult[0]?.count || 0;
    const draftBlogs = draftBlogsResult[0]?.count || 0;
    const totalViews = totalViewsResult[0]?.totalViews || 0;
    
    const monetizationStats = monetizationStatsResult[0];
    const monetizedBlogs = monetizationStats?.monetizedCount || 0;
    const pendingMonetization = monetizationStats?.pendingCount || 0;
    const totalEarnings = monetizationStats?.totalEarnings || "0.00";

    // Format recent blogs
    const formattedRecentBlogs = recentBlogs.map(blog => ({
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      status: blog.status,
      viewCount: blog.viewCount || 0,
      monetization: blog.monetization ? {
        status: blog.monetization.isApproved ? 'approved' as const : 
                blog.monetization.rejectedAt ? 'rejected' as const : 'pending' as const,
        totalEarnings: blog.monetization.totalEarnings || "0.00",
      } : null,
    }));

    return NextResponse.json({
      totalBlogs,
      publishedBlogs,
      draftBlogs,
      totalViews,
      monetizedBlogs,
      pendingMonetization,
      totalEarnings,
      recentBlogs: formattedRecentBlogs,
    });

  } catch (error) {
    console.error("Error fetching blog stats:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
