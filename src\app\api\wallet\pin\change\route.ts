import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { z } from "zod";

const pinChangeSchema = z.object({
  currentPin: z.string().length(4, "Current PIN must be 4 digits"),
  newPin: z.string().length(4, "New PIN must be 4 digits"),
  confirmPin: z.string().length(4, "Confirm PIN must be 4 digits"),
}).refine((data) => data.newPin === data.confirmPin, {
  message: "New PIN and confirm PIN don't match",
  path: ["confirmPin"],
}).refine((data) => data.currentPin !== data.newPin, {
  message: "New PIN must be different from current PIN",
  path: ["newPin"],
});

// Change existing PIN
export async function PATCH(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = pinChangeSchema.parse(body);

    // Verify current PIN first
    try {
      await WalletService.verifyPin(session.user.id, validatedData.currentPin);
    } catch (error: any) {
      return NextResponse.json(
        { 
          success: false,
          message: error.message || "Invalid current PIN" 
        },
        { status: 400 }
      );
    }

    // Set new PIN
    await WalletService.setPin(session.user.id, validatedData.newPin);

    return NextResponse.json({
      success: true,
      message: "PIN changed successfully",
    });
  } catch (error: any) {
    console.error("Error changing PIN:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid input data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: "Failed to change PIN" 
      },
      { status: 500 }
    );
  }
}
