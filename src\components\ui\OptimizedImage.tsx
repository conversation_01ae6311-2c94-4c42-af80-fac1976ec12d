"use client";

import { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { placeholderConfig } from '@/lib/config';

interface OptimizedImageProps extends Omit<ImageProps, 'placeholder' | 'blurDataURL' | 'onLoadingComplete'> {
  /**
   * Optional custom placeholder type. If not provided, uses the default from config.
   */
  customPlaceholder?: 'blur' | 'empty';

  /**
   * Optional custom blur data URL. If not provided, uses the default from config.
   */
  customBlurDataURL?: string;

  /**
   * Optional class name to apply when the image is loading
   */
  loadingClassName?: string;

  /**
   * Force disable placeholder for small images (overrides automatic detection)
   */
  disablePlaceholder?: boolean;
}

/**
 * OptimizedImage component that extends Next.js Image with better placeholder handling
 */
export function OptimizedImage({
  src,
  alt,
  customPlaceholder,
  customBlurDataURL,
  loadingClassName,
  className,
  onLoad,
  disablePlaceholder,
  width,
  height,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);

  // Check if image is small (less than 40x40) to avoid placeholder performance issues
  const isSmallImage = (width && height &&
    typeof width === 'number' && typeof height === 'number' &&
    width < 40 && height < 40) || disablePlaceholder;

  // Use custom placeholder or default from config, but disable for small images
  // Add fallback in case placeholderConfig is undefined
  const placeholder = isSmallImage ? undefined : (
    customPlaceholder ||
    (placeholderConfig?.defaultPlaceholder as 'blur' | 'empty') ||
    'blur'
  );

  // Use custom blur data URL or default from config
  // Add fallback in case placeholderConfig is undefined
  const blurDataURL = customBlurDataURL ||
    placeholderConfig?.defaultBlurDataURL ||
    'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiB2aWV3Qm94PSIwIDAgNDAwIDQwMCI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2YxZjVmOSIvPjwvc3ZnPg==';

  // Combine classes
  const combinedClassName = `${className || ''} ${isLoading ? loadingClassName || 'animate-pulse' : ''}`.trim();

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={combinedClassName}
      placeholder={placeholder}
      blurDataURL={placeholder === 'blur' ? blurDataURL : undefined}
      sizes={props.sizes || "(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"}
      onLoad={(event) => {
        setIsLoading(false);
        if (onLoad) {
          onLoad(event);
        }
      }}
      {...props}
    />
  );
}
