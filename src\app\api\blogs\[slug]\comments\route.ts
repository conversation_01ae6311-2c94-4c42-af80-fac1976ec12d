import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogComments } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, desc } from "drizzle-orm";

const commentSchema = z.object({
  content: z.string().min(1).max(1000),
  parentId: z.string().optional(),
});

// Get comments for a blog
export async function GET(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;

    // First, get the blog to find its ID
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Fetch comments from database
    const comments = await db.query.blogComments.findMany({
      where: eq(blogComments.blogId, blog.id),
      orderBy: [desc(blogComments.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
    });

    // Get session to check user's like/dislike status
    const session = await getServerSession(authOptions);

    // Format comments for frontend
    const formattedComments = comments.map((comment) => ({
      id: comment.id,
      content: comment.content,
      createdAt: comment.createdAt.toISOString(),
      user: comment.user,
      parentId: comment.parentId,
      _count: {
        likes: 0, // TODO: Implement comment likes count from likes table
        dislikes: 0, // TODO: Implement comment dislikes count from likes table
        replies: 0, // Will be calculated when organizing comments
      },
      liked: false, // TODO: Check if current user liked this comment
      disliked: false, // TODO: Check if current user disliked this comment
      replies: [], // Initialize empty replies array
    }));

    // Organize comments into a tree structure (parent-child)
    const organizeComments = (comments: any[]) => {
      const commentMap = new Map();
      const rootComments: any[] = [];

      // First pass: create map of all comments
      comments.forEach(comment => {
        commentMap.set(comment.id, { ...comment, replies: [] });
      });

      // Second pass: organize into parent-child structure
      comments.forEach(comment => {
        const commentWithReplies = commentMap.get(comment.id);

        if (comment.parentId) {
          const parent = commentMap.get(comment.parentId);
          if (parent) {
            parent.replies = parent.replies || [];
            parent.replies.push(commentWithReplies);
            // Update parent's reply count
            parent._count.replies = parent.replies.length;
          }
        } else {
          rootComments.push(commentWithReplies);
        }
      });

      // Sort replies by creation date (oldest first for better conversation flow)
      const sortReplies = (comments: any[]) => {
        comments.forEach(comment => {
          if (comment.replies && comment.replies.length > 0) {
            comment.replies.sort((a: any, b: any) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );
            sortReplies(comment.replies);
          }
        });
      };

      sortReplies(rootComments);
      return rootComments;
    };

    const organizedComments = organizeComments(formattedComments);
    return NextResponse.json(organizedComments);
  } catch (error) {
    console.error("Error fetching blog comments:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new comment on a blog
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { slug } = params;
    const body = await req.json();
    const validatedData = commentSchema.parse(body);

    // First, get the blog to find its ID
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true, authorId: true },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // If parentId is provided, check if parent comment exists
    if (validatedData.parentId) {
      const parentComment = await db.query.blogComments.findFirst({
        where: eq(blogComments.id, validatedData.parentId),
      });

      if (!parentComment) {
        return NextResponse.json(
          { message: "Parent comment not found" },
          { status: 404 }
        );
      }
    }

    const commentId = uuidv4();

    // Insert comment into database
    await db.insert(blogComments).values({
      id: commentId,
      content: validatedData.content,
      userId: session.user.id,
      blogId: blog.id,
      parentId: validatedData.parentId || null,
    });

    // Get the created comment with user info
    const newComment = await db.query.blogComments.findFirst({
      where: eq(blogComments.id, commentId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        id: newComment!.id,
        content: newComment!.content,
        createdAt: newComment!.createdAt.toISOString(),
        user: newComment!.user,
        parentId: newComment!.parentId,
        replies: [],
        _count: {
          likes: 0,
        },
        liked: false,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating blog comment:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
