"use client";

import {
  DocumentTextIcon,
  UserIcon,
  PhotoIcon,
  UserGroupIcon,
  ClockIcon,
  HeartIcon,
  NewspaperIcon,
  ShoppingBagIcon
} from "@heroicons/react/24/outline";

interface ProfileTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isOwnProfile: boolean;
  postCount: number;
  photoCount: number;
  blogCount?: number;
  productCount?: number;
}

export function ProfileTabs({
  activeTab,
  onTabChange,
  isOwnProfile,
  postCount,
  photoCount,
  blogCount = 0,
  productCount = 0
}: ProfileTabsProps) {
  const tabs = [
    {
      id: 'posts',
      name: 'Posts',
      icon: DocumentTextIcon,
      count: postCount,
      visible: true
    },
    {
      id: 'about',
      name: 'About',
      icon: UserIcon,
      visible: true
    },
    {
      id: 'photos',
      name: 'Photos',
      icon: PhotoIcon,
      count: photoCount,
      visible: true
    },
    {
      id: 'connections',
      name: 'Connections',
      icon: UserGroupIcon,
      visible: true
    },
    {
      id: 'activity',
      name: 'Activity',
      icon: ClockIcon,
      visible: isOwnProfile
    },
    {
      id: 'liked',
      name: 'Liked Posts',
      icon: HeartIcon,
      visible: isOwnProfile
    },
    {
      id: 'blogs',
      name: 'Blogs',
      icon: NewspaperIcon,
      count: blogCount,
      visible: true
    },
    {
      id: 'products',
      name: 'Products',
      icon: ShoppingBagIcon,
      count: productCount,
      visible: true
    }
  ];

  const visibleTabs = tabs.filter(tab => tab.visible);

  return (
    <div className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="-mb-px flex space-x-8 overflow-x-auto scrollbar-none">
          {visibleTabs.map((tab) => {
            const isActive = activeTab === tab.id;
            const Icon = tab.icon;

            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`
                  flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm whitespace-nowrap transition-all duration-300
                  ${isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.name}</span>
                {tab.count !== undefined && (
                  <span className={`
                    inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                    ${isActive
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                    }
                  `}>
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
