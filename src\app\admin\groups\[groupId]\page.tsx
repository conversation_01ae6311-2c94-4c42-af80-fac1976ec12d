"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { <PERSON><PERSON>, <PERSON><PERSON>List, <PERSON>bsTrigger, TabsContent } from "@/components/ui/Tabs";
import { toast } from "react-hot-toast";
import Image from "next/image";
import Link from "next/link";
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
  UsersIcon,
  NewspaperIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  LockClosedIcon,
  EyeIcon,
  CalendarIcon,
  ChatBubbleLeftIcon,
  UserPlusIcon,
  UserMinusIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { formatTimeAgo } from "@/lib/utils";
import { EditGroupModal } from "@/components/admin/groups/EditGroupModal";
import { DeleteGroupModal } from "@/components/admin/groups/DeleteGroupModal";
import { AddMemberModal } from "@/components/admin/groups/AddMemberModal";

interface GroupDetailPageProps {
  params: Promise<{
    groupId: string;
  }>;
}

export default function GroupDetailPage({ params }: GroupDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { groupId } = resolvedParams;
  const [isLoading, setIsLoading] = useState(true);
  const [group, setGroup] = useState<any>(null);
  const [members, setMembers] = useState<any[]>([]);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [isMembersLoading, setIsMembersLoading] = useState(false);

  useEffect(() => {
    fetchGroupDetails();
  }, [groupId]);

  useEffect(() => {
    if (activeTab === "members" && members.length === 0) {
      fetchGroupMembers();
    }
  }, [activeTab]);

  const fetchGroupDetails = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/groups/${groupId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch group details");
      }

      const data = await response.json();
      setGroup(data);
    } catch (error) {
      console.error("Error fetching group details:", error);
      toast.error("Failed to load group details");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchGroupMembers = async () => {
    setIsMembersLoading(true);
    try {
      const response = await fetch(`/api/admin/groups/${groupId}/members`);

      if (!response.ok) {
        throw new Error("Failed to fetch group members");
      }

      const data = await response.json();
      setMembers(data);
    } catch (error) {
      console.error("Error fetching group members:", error);
      toast.error("Failed to load group members");
    } finally {
      setIsMembersLoading(false);
    }
  };

  const handleEditSuccess = (updatedGroup: any) => {
    toast.success("Group updated successfully");
    setGroup((prev: any) => ({ ...prev, ...updatedGroup }));
    setIsEditModalOpen(false);
  };

  const handleDeleteSuccess = () => {
    toast.success("Group deleted successfully");
    router.push("/admin/groups");
  };

  const handleMemberAction = async (userId: string, action: string, role?: string) => {
    try {
      const response = await fetch(`/api/admin/groups/${groupId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId, action, role }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${action} member`);
      }

      const data = await response.json();
      toast.success(data.message);
      fetchGroupMembers();
    } catch (error) {
      console.error(`Error performing member action (${action}):`, error);
      toast.error(error instanceof Error ? error.message : `Failed to ${action} member`);
    }
  };

  const getVisibilityBadge = (visibility: string) => {
    switch (visibility) {
      case "public":
        return (
          <Badge variant="success" className="flex items-center">
            <GlobeAltIcon className="mr-1 h-3 w-3" />
            Public
          </Badge>
        );
      case "private-visible":
        return (
          <Badge variant="warning" className="flex items-center">
            <EyeIcon className="mr-1 h-3 w-3" />
            Private (Visible)
          </Badge>
        );
      case "private-hidden":
        return (
          <Badge variant="danger" className="flex items-center">
            <LockClosedIcon className="mr-1 h-3 w-3" />
            Private (Hidden)
          </Badge>
        );
      default:
        return null;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "admin":
        return (
          <Badge variant="primary" className="flex items-center">
            <ShieldCheckIcon className="mr-1 h-3 w-3" />
            Admin
          </Badge>
        );
      case "moderator":
        return (
          <Badge variant="warning" className="flex items-center">
            <ShieldCheckIcon className="mr-1 h-3 w-3" />
            Moderator
          </Badge>
        );
      case "member":
        return (
          <Badge variant="default" className="flex items-center">
            <UserGroupIcon className="mr-1 h-3 w-3" />
            Member
          </Badge>
        );
      case "pending":
        return (
          <Badge variant="outline" className="flex items-center">
            <ClockIcon className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!group) {
    return (
      <AdminLayout>
        <div className="flex h-64 flex-col items-center justify-center">
          <h2 className="text-xl font-semibold text-gray-800">Group not found</h2>
          <p className="mt-2 text-gray-600">The group you're looking for doesn't exist or has been deleted.</p>
          <Button
            onClick={() => router.push("/admin/groups")}
            className="mt-4"
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Groups
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            onClick={() => router.push("/admin/groups")}
            variant="outline"
            className="mr-4"
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">{group.name}</h1>
          {getVisibilityBadge(group.visibility)}
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => setIsEditModalOpen(true)}
            variant="outline"
          >
            <PencilIcon className="mr-2 h-5 w-5" />
            Edit
          </Button>
          <Button
            onClick={() => setIsDeleteModalOpen(true)}
            variant="danger"
          >
            <TrashIcon className="mr-2 h-5 w-5" />
            Delete
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="overview">
            <UserGroupIcon className="mr-2 h-5 w-5" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="members">
            <UsersIcon className="mr-2 h-5 w-5" />
            Members
          </TabsTrigger>
          <TabsTrigger value="content">
            <NewspaperIcon className="mr-2 h-5 w-5" />
            Content
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Group header with cover image */}
          <div className="relative h-48 w-full overflow-hidden rounded-lg bg-gray-100">
            {group.coverImage ? (
              <Image
                src={group.coverImage}
                alt={group.name}
                fill
                className="object-cover"
              />
            ) : (
              <div className="flex h-full items-center justify-center bg-gradient-to-r from-blue-100 to-blue-50">
                <UserGroupIcon className="h-16 w-16 text-blue-300" />
              </div>
            )}
          </div>

          {/* Group details */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Left column - Group info */}
            <div className="space-y-6 lg:col-span-2">
              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h2 className="mb-4 text-lg font-medium text-gray-900">Group Information</h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Description</h3>
                    <p className="mt-1 text-gray-900">{group.description || "No description provided."}</p>
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Category</h3>
                      <p className="mt-1 text-gray-900">{group.category || "Uncategorized"}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Post Permission</h3>
                      <p className="mt-1 text-gray-900">
                        {group.postPermission === "all-members" ? "All Members" : "Admins Only"}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Rules</h3>
                    <p className="mt-1 whitespace-pre-line text-gray-900">
                      {group.rules || "No rules specified."}
                    </p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h2 className="mb-4 text-lg font-medium text-gray-900">Activity</h2>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div className="rounded-lg bg-blue-50 p-4 text-center">
                    <div className="text-2xl font-bold text-blue-700">{group.memberCount}</div>
                    <div className="text-sm text-blue-600">Members</div>
                  </div>
                  <div className="rounded-lg bg-green-50 p-4 text-center">
                    <div className="text-2xl font-bold text-green-700">{group.postCount}</div>
                    <div className="text-sm text-green-600">Posts</div>
                  </div>
                  <div className="rounded-lg bg-purple-50 p-4 text-center">
                    <div className="text-2xl font-bold text-purple-700">{group.adminCount}</div>
                    <div className="text-sm text-purple-600">Admins</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right column - Creator info and timestamps */}
            <div className="space-y-6">
              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h2 className="mb-4 text-lg font-medium text-gray-900">Creator</h2>
                <div className="flex items-center">
                  <div className="h-12 w-12 flex-shrink-0">
                    {group.creator?.image ? (
                      <Image
                        src={group.creator.image}
                        alt={group.creator.name || "Creator"}
                        width={48}
                        height={48}
                        className="h-12 w-12 rounded-full"
                      />
                    ) : (
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                        <UsersIcon className="h-6 w-6" />
                      </div>
                    )}
                  </div>
                  <div className="ml-4">
                    <div className="font-medium text-gray-900">
                      {group.creator?.name || "Unknown User"}
                    </div>
                    {group.creator?.username && (
                      <div className="text-sm text-gray-500">
                        @{group.creator.username}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h2 className="mb-4 text-lg font-medium text-gray-900">Timestamps</h2>
                <div className="space-y-3">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Created</h3>
                    <p className="mt-1 text-gray-900">
                      {new Date(group.createdAt).toLocaleDateString()} ({formatTimeAgo(group.createdAt)})
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                    <p className="mt-1 text-gray-900">
                      {new Date(group.updatedAt).toLocaleDateString()} ({formatTimeAgo(group.updatedAt)})
                    </p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h2 className="mb-4 text-lg font-medium text-gray-900">Actions</h2>
                <div className="space-y-2">
                  <Button
                    onClick={() => window.open(`/groups/${groupId}`, "_blank")}
                    variant="outline"
                    className="w-full justify-start"
                  >
                    <EyeIcon className="mr-2 h-5 w-5" />
                    View Group
                  </Button>
                  <Button
                    onClick={() => setIsAddMemberModalOpen(true)}
                    variant="outline"
                    className="w-full justify-start"
                  >
                    <UserPlusIcon className="mr-2 h-5 w-5" />
                    Add Member
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="members" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Group Members</h2>
            <Button onClick={() => setIsAddMemberModalOpen(true)}>
              <UserPlusIcon className="mr-2 h-5 w-5" />
              Add Member
            </Button>
          </div>

          {isMembersLoading ? (
            <div className="flex h-64 items-center justify-center">
              <Spinner size="lg" />
            </div>
          ) : members.length === 0 ? (
            <div className="flex h-64 flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
              <UsersIcon className="mb-2 h-12 w-12 text-gray-400" />
              <h3 className="mb-1 text-lg font-medium text-gray-900">No members found</h3>
              <p className="text-gray-500">This group doesn't have any members yet.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Admins section */}
              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h3 className="mb-4 text-md font-medium text-gray-900 flex items-center">
                  <ShieldCheckIcon className="mr-2 h-5 w-5 text-blue-600" />
                  Admins
                </h3>
                <div className="divide-y divide-gray-200">
                  {members
                    .filter((m) => m.member.role === "admin")
                    .map((item) => (
                      <div key={item.member.id} className="flex items-center justify-between py-4">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            {item.user.image ? (
                              <Image
                                src={item.user.image}
                                alt={item.user.name || "User"}
                                width={40}
                                height={40}
                                className="h-10 w-10 rounded-full"
                              />
                            ) : (
                              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                                <UsersIcon className="h-5 w-5" />
                              </div>
                            )}
                          </div>
                          <div className="ml-3">
                            <div className="font-medium text-gray-900">{item.user.name}</div>
                            <div className="text-sm text-gray-500">
                              {item.user.username ? `@${item.user.username}` : item.user.email}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getRoleBadge(item.member.role)}
                          <div className="flex space-x-1">
                            <Button
                              onClick={() => handleMemberAction(item.user.id, "demote", "moderator")}
                              variant="outline"
                              size="sm"
                            >
                              Demote
                            </Button>
                            <Button
                              onClick={() => handleMemberAction(item.user.id, "remove")}
                              variant="danger"
                              size="sm"
                            >
                              <UserMinusIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Moderators section */}
              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h3 className="mb-4 text-md font-medium text-gray-900 flex items-center">
                  <ShieldCheckIcon className="mr-2 h-5 w-5 text-yellow-600" />
                  Moderators
                </h3>
                <div className="divide-y divide-gray-200">
                  {members
                    .filter((m) => m.member.role === "moderator")
                    .map((item) => (
                      <div key={item.member.id} className="flex items-center justify-between py-4">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            {item.user.image ? (
                              <Image
                                src={item.user.image}
                                alt={item.user.name || "User"}
                                width={40}
                                height={40}
                                className="h-10 w-10 rounded-full"
                              />
                            ) : (
                              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                                <UsersIcon className="h-5 w-5" />
                              </div>
                            )}
                          </div>
                          <div className="ml-3">
                            <div className="font-medium text-gray-900">{item.user.name}</div>
                            <div className="text-sm text-gray-500">
                              {item.user.username ? `@${item.user.username}` : item.user.email}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getRoleBadge(item.member.role)}
                          <div className="flex space-x-1">
                            <Button
                              onClick={() => handleMemberAction(item.user.id, "promote", "admin")}
                              variant="outline"
                              size="sm"
                            >
                              Promote
                            </Button>
                            <Button
                              onClick={() => handleMemberAction(item.user.id, "demote", "member")}
                              variant="outline"
                              size="sm"
                            >
                              Demote
                            </Button>
                            <Button
                              onClick={() => handleMemberAction(item.user.id, "remove")}
                              variant="danger"
                              size="sm"
                            >
                              <UserMinusIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Regular members section */}
              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h3 className="mb-4 text-md font-medium text-gray-900 flex items-center">
                  <UserGroupIcon className="mr-2 h-5 w-5 text-gray-600" />
                  Members
                </h3>
                <div className="divide-y divide-gray-200">
                  {members
                    .filter((m) => m.member.role === "member")
                    .map((item) => (
                      <div key={item.member.id} className="flex items-center justify-between py-4">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            {item.user.image ? (
                              <Image
                                src={item.user.image}
                                alt={item.user.name || "User"}
                                width={40}
                                height={40}
                                className="h-10 w-10 rounded-full"
                              />
                            ) : (
                              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                                <UsersIcon className="h-5 w-5" />
                              </div>
                            )}
                          </div>
                          <div className="ml-3">
                            <div className="font-medium text-gray-900">{item.user.name}</div>
                            <div className="text-sm text-gray-500">
                              {item.user.username ? `@${item.user.username}` : item.user.email}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getRoleBadge(item.member.role)}
                          <div className="flex space-x-1">
                            <Button
                              onClick={() => handleMemberAction(item.user.id, "promote", "moderator")}
                              variant="outline"
                              size="sm"
                            >
                              Promote
                            </Button>
                            <Button
                              onClick={() => handleMemberAction(item.user.id, "remove")}
                              variant="danger"
                              size="sm"
                            >
                              <UserMinusIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Group Content</h2>
            <Button
              onClick={() => window.open(`/groups/${groupId}`, "_blank")}
              variant="outline"
            >
              <EyeIcon className="mr-2 h-5 w-5" />
              View Group
            </Button>
          </div>

          <div className="rounded-lg bg-white p-6 shadow-sm">
            <p className="text-gray-500">
              Content moderation features for this group will be implemented in a future update.
            </p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {group && (
        <>
          <EditGroupModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSuccess={handleEditSuccess}
            group={group}
          />

          <DeleteGroupModal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            onSuccess={handleDeleteSuccess}
            group={group}
          />

          <AddMemberModal
            isOpen={isAddMemberModalOpen}
            onClose={() => setIsAddMemberModalOpen(false)}
            onSuccess={() => {
              fetchGroupMembers();
              fetchGroupDetails();
            }}
            groupId={groupId}
          />
        </>
      )}
    </AdminLayout>
  );
}
