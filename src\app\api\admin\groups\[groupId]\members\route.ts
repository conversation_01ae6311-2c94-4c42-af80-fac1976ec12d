import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, users, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, or, not, desc } from "drizzle-orm";

const memberActionSchema = z.object({
  userId: z.string(),
  action: z.enum(["add", "remove", "promote", "demote"]),
  role: z.enum(["admin", "moderator", "member"]).optional(),
});

// Get members of a group for admin
export async function GET(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const url = new URL(req.url);
    const roleParam = url.searchParams.get("role") || "all"; // all, admin, moderator, member, pending

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Build where conditions
    const whereConditions = [eq(groupMembers.groupId, groupId)];

    // Filter by role if specified
    if (roleParam !== "all") {
      // Validate role is one of the allowed enum values
      const validRoles = ['admin', 'moderator', 'member', 'pending'] as const;

      if (validRoles.includes(roleParam as any)) {
        whereConditions.push(eq(groupMembers.role, roleParam as typeof validRoles[number]));
      }
    }

    // Build the query
    const query = db.select({
      member: {
        id: groupMembers.id,
        groupId: groupMembers.groupId,
        userId: groupMembers.userId,
        role: groupMembers.role,
        createdAt: groupMembers.createdAt,
      },
      user: {
        id: users.id,
        name: users.name,
        username: users.username,
        email: users.email,
        image: users.image,
      },
    })
    .from(groupMembers)
    .innerJoin(users, eq(groupMembers.userId, users.id))
    .where(and(...whereConditions));

    // Order by role importance and then join date
    const results = await query
      .orderBy(
        desc(groupMembers.role),
        desc(groupMembers.createdAt)
      )
      .execute();

    return NextResponse.json(results);
  } catch (error) {
    console.error("Error fetching group members:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Perform admin actions on group members
export async function POST(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const body = await req.json();
    const { userId, action, role } = memberActionSchema.parse(body);

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Handle different actions
    switch (action) {
      case "add":
        // Check if user is already a member
        const existingMembership = await db.query.groupMembers.findFirst({
          where: and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ),
        });

        if (existingMembership) {
          return NextResponse.json(
            { message: "User is already a member of this group" },
            { status: 400 }
          );
        }

        // Add user to the group with specified role
        const membershipId = uuidv4();
        await db.insert(groupMembers).values({
          id: membershipId,
          groupId,
          userId,
          role: role || "member",
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Create notification
        const addNotificationId = uuidv4();
        await db.insert(notifications).values({
          id: addNotificationId,
          recipientId: userId,
          type: "group_join_approved",
          senderId: session.user.id,
          groupId,
          read: false,
          createdAt: new Date(),
        });

        return NextResponse.json({
          message: `User added to group as ${role || "member"}`,
        });

      case "remove":
        // Check if user is a member
        const membership = await db.query.groupMembers.findFirst({
          where: and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ),
        });

        if (!membership) {
          return NextResponse.json(
            { message: "User is not a member of this group" },
            { status: 400 }
          );
        }

        // Remove user from the group
        await db.delete(groupMembers)
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        return NextResponse.json({
          message: "User removed from group",
        });

      case "promote":
        // Check if user is a member
        const memberToPromote = await db.query.groupMembers.findFirst({
          where: and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ),
        });

        if (!memberToPromote) {
          return NextResponse.json(
            { message: "User is not a member of this group" },
            { status: 400 }
          );
        }

        // Determine new role
        let newRole: "admin" | "moderator" | "member" | "pending" = "moderator";
        if (role) {
          newRole = role;
        } else if (memberToPromote.role === "member") {
          newRole = "moderator";
        } else if (memberToPromote.role === "moderator") {
          newRole = "admin";
        }

        // Update user's role
        await db.update(groupMembers)
          .set({
            role: newRole,
            updatedAt: new Date(),
          })
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        // Create notification
        const promoteNotificationId = uuidv4();
        await db.insert(notifications).values({
          id: promoteNotificationId,
          recipientId: userId,
          type: "group_join_approved", // Using this type as a general group notification
          senderId: session.user.id,
          groupId,
          read: false,
          createdAt: new Date(),
        });

        return NextResponse.json({
          message: `User promoted to ${newRole}`,
        });

      case "demote":
        // Check if user is a member
        const memberToDemote = await db.query.groupMembers.findFirst({
          where: and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ),
        });

        if (!memberToDemote) {
          return NextResponse.json(
            { message: "User is not a member of this group" },
            { status: 400 }
          );
        }

        // Determine new role
        let demotedRole: "admin" | "moderator" | "member" | "pending" = "member";
        if (role) {
          demotedRole = role;
        } else if (memberToDemote.role === "admin") {
          demotedRole = "moderator";
        } else if (memberToDemote.role === "moderator") {
          demotedRole = "member";
        }

        // Update user's role
        await db.update(groupMembers)
          .set({
            role: demotedRole,
            updatedAt: new Date(),
          })
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        return NextResponse.json({
          message: `User demoted to ${demotedRole}`,
        });

      default:
        return NextResponse.json(
          { message: "Invalid action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error managing group member:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
