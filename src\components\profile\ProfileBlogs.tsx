"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { 
  NewspaperIcon, 
  EyeIcon, 
  ClockIcon,
  PlusIcon,
  CalendarIcon
} from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  coverImage: string | null;
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  viewCount: number;
  readTime: number | null;
  tags: string[] | null;
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  category: {
    name: string;
    slug: string;
  } | null;
  isLiked: boolean;
  isBookmarked: boolean;
}

interface ProfileBlogsProps {
  userId: string;
  isOwnProfile: boolean;
}

export function ProfileBlogs({ userId, isOwnProfile }: ProfileBlogsProps) {
  const { data: session } = useSession();
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'published' | 'draft'>(isOwnProfile ? 'all' : 'published');

  const fetchBlogs = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: '1',
        limit: '20',
      });

      if (filter !== 'all') {
        params.append('status', filter);
      } else if (!isOwnProfile) {
        // For other users, always show only published blogs
        params.append('status', 'published');
      }

      const response = await fetch(`/api/users/${userId}/blogs?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch blogs");
      }
      
      const data = await response.json();
      setBlogs(data.blogs);
    } catch (err) {
      console.error("Error fetching blogs:", err);
      setError(err instanceof Error ? err.message : "Failed to load blogs. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [userId, filter]);

  useEffect(() => {
    if (session?.user) {
      fetchBlogs();
    }
  }, [session, fetchBlogs]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      published: 'bg-green-100 text-green-800',
      draft: 'bg-yellow-100 text-yellow-800',
      archived: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles[status as keyof typeof statusStyles]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
            <div className="flex space-x-4">
              <div className="w-24 h-24 bg-gray-200 rounded-lg"></div>
              <div className="flex-1 space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="flex space-x-4">
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
        <div className="text-red-500 mb-4">
          <NewspaperIcon className="mx-auto h-12 w-12" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Blogs</h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <Button onClick={fetchBlogs} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with filters and create button */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {isOwnProfile ? 'Your Blogs' : 'Blogs'}
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {blogs.length} blog{blogs.length !== 1 ? 's' : ''} found
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {isOwnProfile && (
              <>
                {/* Filter buttons - only for own profile */}
                {isOwnProfile && (
                  <div className="flex rounded-lg border border-gray-200 p-1">
                    {(['all', 'published', 'draft'] as const).map((filterOption) => (
                      <button
                        key={filterOption}
                        onClick={() => setFilter(filterOption)}
                        className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                          filter === filterOption
                            ? 'bg-blue-600 text-white'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
                      </button>
                    ))}
                  </div>
                )}
                
                {/* Create blog button */}
                <Link href="/blogs/create">
                  <Button className="flex items-center gap-2">
                    <PlusIcon className="h-4 w-4" />
                    Create Blog
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Blogs list */}
      {blogs.length === 0 ? (
        <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
          <div className="text-gray-400 mb-4">
            <NewspaperIcon className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No blogs yet</h3>
          <p className="text-gray-500 mb-4">
            {isOwnProfile 
              ? "You haven't written any blog posts yet. Start sharing your thoughts and ideas!"
              : "This user hasn't written any blog posts yet."
            }
          </p>
          {isOwnProfile && (
            <Link href="/blogs/create">
              <Button>
                <PlusIcon className="h-4 w-4 mr-2" />
                Write Your First Blog
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {blogs.map((blog) => (
            <div key={blog.id} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex space-x-4">
                {/* Cover image */}
                <div className="flex-shrink-0">
                  {blog.coverImage ? (
                    <img
                      src={blog.coverImage}
                      alt={blog.title}
                      className="w-24 h-24 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                      <NewspaperIcon className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Link
                          href={`/blogs/${blog.slug}`}
                          className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors line-clamp-1"
                        >
                          {blog.title}
                        </Link>
                        {isOwnProfile && getStatusBadge(blog.status)}
                        {blog.featured && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Featured
                          </span>
                        )}
                      </div>
                      
                      {blog.excerpt && (
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {blog.excerpt}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <CalendarIcon className="h-3 w-3" />
                          {formatDate(blog.publishedAt || blog.createdAt)}
                        </div>
                        <div className="flex items-center gap-1">
                          <EyeIcon className="h-3 w-3" />
                          {blog.viewCount} views
                        </div>
                        {blog.readTime && (
                          <div className="flex items-center gap-1">
                            <ClockIcon className="h-3 w-3" />
                            {blog.readTime} min read
                          </div>
                        )}
                        {blog.category && (
                          <span className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full">
                            {blog.category.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
