/**
 * Storage Configuration
 * Configure your storage providers here
 */

export interface StorageConfig {
  provider: 'local' | 'cloudinary' | 's3';
  local?: {
    uploadsPath: string;
  };
  cloudinary?: {
    cloudName: string;
    apiKey: string;
    apiSecret: string;
  };
  s3?: {
    region: string;
    bucket: string;
    accessKeyId: string;
    secretAccessKey: string;
  };
}

/**
 * Get storage configuration from environment variables
 */
export function getStorageConfig(): StorageConfig {
  // Determine primary storage provider
  const provider = (process.env.STORAGE_PROVIDER as StorageConfig['provider']) || 'local';

  const config: StorageConfig = {
    provider,
  };

  // Configure based on provider
  switch (provider) {
    case 'local':
      config.local = {
        uploadsPath: process.env.UPLOADS_PATH || '/uploads/images',
      };
      break;

    case 'cloudinary':
      config.cloudinary = {
        cloudName: process.env.CLOUDINARY_CLOUD_NAME || '',
        apiKey: process.env.CLOUDINARY_API_KEY || '',
        apiSecret: process.env.CLOUDINARY_API_SECRET || '',
      };
      break;

    case 's3':
      config.s3 = {
        region: process.env.AWS_REGION || 'us-east-1',
        bucket: process.env.AWS_S3_BUCKET || '',
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      };
      break;
  }

  return config;
}

/**
 * Check if storage provider is properly configured
 */
export function isStorageConfigured(provider: StorageConfig['provider']): boolean {
  const config = getStorageConfig();

  switch (provider) {
    case 'local':
      return true; // Local storage is always available

    case 'cloudinary':
      return !!(
        config.cloudinary?.cloudName &&
        config.cloudinary?.apiKey &&
        config.cloudinary?.apiSecret
      );

    case 's3':
      return !!(
        config.s3?.region &&
        config.s3?.bucket &&
        config.s3?.accessKeyId &&
        config.s3?.secretAccessKey
      );

    default:
      return false;
  }
}

/**
 * Get available storage providers
 */
export function getAvailableProviders(): StorageConfig['provider'][] {
  const providers: StorageConfig['provider'][] = ['local']; // Local is always available

  if (isStorageConfigured('cloudinary')) {
    providers.push('cloudinary');
  }

  if (isStorageConfigured('s3')) {
    providers.push('s3');
  }

  return providers;
}
