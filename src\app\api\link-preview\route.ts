import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { z } from "zod";

const linkPreviewSchema = z.object({
  url: z.string().url("Invalid URL format"),
});

interface LinkPreview {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  siteName?: string;
  favicon?: string;
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { url } = linkPreviewSchema.parse(body);

    // Fetch the webpage with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; LinkPreviewBot/1.0)',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch URL: ${response.status}`);
    }

    const html = await response.text();
    
    // Extract metadata using regex patterns
    const preview: LinkPreview = {
      url: url,
    };

    // Extract title
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      preview.title = titleMatch[1].trim();
    }

    // Extract Open Graph title (preferred)
    const ogTitleMatch = html.match(/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
    if (ogTitleMatch) {
      preview.title = ogTitleMatch[1].trim();
    }

    // Extract description
    const descMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
    if (descMatch) {
      preview.description = descMatch[1].trim();
    }

    // Extract Open Graph description (preferred)
    const ogDescMatch = html.match(/<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
    if (ogDescMatch) {
      preview.description = ogDescMatch[1].trim();
    }

    // Extract Open Graph image
    const ogImageMatch = html.match(/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
    if (ogImageMatch) {
      let imageUrl = ogImageMatch[1].trim();
      // Convert relative URLs to absolute
      if (imageUrl.startsWith('/')) {
        const urlObj = new URL(url);
        imageUrl = `${urlObj.protocol}//${urlObj.host}${imageUrl}`;
      }
      preview.image = imageUrl;
    }

    // Extract site name
    const ogSiteNameMatch = html.match(/<meta[^>]*property=["\']og:site_name["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
    if (ogSiteNameMatch) {
      preview.siteName = ogSiteNameMatch[1].trim();
    }

    // Extract favicon
    const faviconMatch = html.match(/<link[^>]*rel=["\'](?:icon|shortcut icon)["\'][^>]*href=["\']([^"\']+)["\'][^>]*>/i);
    if (faviconMatch) {
      let faviconUrl = faviconMatch[1].trim();
      // Convert relative URLs to absolute
      if (faviconUrl.startsWith('/')) {
        const urlObj = new URL(url);
        faviconUrl = `${urlObj.protocol}//${urlObj.host}${faviconUrl}`;
      }
      preview.favicon = faviconUrl;
    }

    // If no title found, use the domain name
    if (!preview.title) {
      const urlObj = new URL(url);
      preview.title = urlObj.hostname;
    }

    // Limit description length
    if (preview.description && preview.description.length > 200) {
      preview.description = preview.description.substring(0, 200) + '...';
    }

    return NextResponse.json(preview);

  } catch (error) {
    console.error("Error fetching link preview:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid URL format" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Failed to fetch link preview" },
      { status: 500 }
    );
  }
}
