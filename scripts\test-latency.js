#!/usr/bin/env node

/**
 * Latency Testing Script for PartyKit Real-time System
 * 
 * This script tests the actual latency of the PartyKit implementation
 * to validate the sub-100ms requirement.
 */

const WebSocket = require('ws');
const { performance } = require('perf_hooks');

class LatencyTester {
  constructor(host = 'localhost:1999') {
    this.host = host;
    this.results = [];
    this.testCount = 0;
    this.maxTests = 100;
    this.connected = false;
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(`ws://${this.host}/parties/chat/latency-test`);
      
      this.ws.on('open', () => {
        console.log('✅ Connected to PartyKit server');
        this.connected = true;
        
        // Send authentication
        this.ws.send(JSON.stringify({
          type: 'auth',
          token: 'test-token',
          timestamp: new Date().toISOString()
        }));
        
        resolve();
      });

      this.ws.on('message', (data) => {
        this.handleMessage(JSON.parse(data.toString()));
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error.message);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('🔌 Connection closed');
        this.connected = false;
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        if (!this.connected) {
          reject(new Error('Connection timeout'));
        }
      }, 10000);
    });
  }

  handleMessage(message) {
    const now = performance.now();

    switch (message.type) {
      case 'chat_auth_success':
        console.log('🔐 Authentication successful');
        this.startLatencyTests();
        break;

      case 'latency_response':
        const latency = now - message.originalTimestamp;
        this.results.push(latency);
        console.log(`📊 Test ${this.testCount}: ${latency.toFixed(2)}ms`);
        
        this.testCount++;
        if (this.testCount < this.maxTests) {
          // Send next test after a short delay
          setTimeout(() => this.sendLatencyTest(), 100);
        } else {
          this.analyzeResults();
        }
        break;

      case 'error':
        console.error('❌ Server error:', message.message);
        break;
    }
  }

  startLatencyTests() {
    console.log(`🚀 Starting ${this.maxTests} latency tests...`);
    this.sendLatencyTest();
  }

  sendLatencyTest() {
    if (!this.connected) {
      console.error('❌ Not connected to server');
      return;
    }

    const timestamp = performance.now();
    this.ws.send(JSON.stringify({
      type: 'latency_test',
      timestamp: timestamp,
      testNumber: this.testCount
    }));
  }

  analyzeResults() {
    if (this.results.length === 0) {
      console.error('❌ No latency results to analyze');
      return;
    }

    const sortedResults = this.results.sort((a, b) => a - b);
    const average = this.results.reduce((sum, val) => sum + val, 0) / this.results.length;
    const median = sortedResults[Math.floor(sortedResults.length / 2)];
    const min = sortedResults[0];
    const max = sortedResults[sortedResults.length - 1];
    const p95 = sortedResults[Math.floor(sortedResults.length * 0.95)];
    const p99 = sortedResults[Math.floor(sortedResults.length * 0.99)];

    // Count results under 100ms
    const under100ms = this.results.filter(latency => latency < 100).length;
    const successRate = (under100ms / this.results.length) * 100;

    console.log('\n📈 LATENCY TEST RESULTS');
    console.log('========================');
    console.log(`Total tests: ${this.results.length}`);
    console.log(`Average latency: ${average.toFixed(2)}ms`);
    console.log(`Median latency: ${median.toFixed(2)}ms`);
    console.log(`Min latency: ${min.toFixed(2)}ms`);
    console.log(`Max latency: ${max.toFixed(2)}ms`);
    console.log(`95th percentile: ${p95.toFixed(2)}ms`);
    console.log(`99th percentile: ${p99.toFixed(2)}ms`);
    console.log(`\n🎯 SUB-100MS REQUIREMENT`);
    console.log(`Messages under 100ms: ${under100ms}/${this.results.length} (${successRate.toFixed(1)}%)`);

    // Determine if requirement is met
    if (average < 100 && p95 < 100) {
      console.log('✅ PASSED: Sub-100ms latency requirement met!');
      process.exit(0);
    } else if (successRate >= 95) {
      console.log('⚠️  PARTIAL: 95%+ messages under 100ms, but average/p95 exceeds target');
      process.exit(0);
    } else {
      console.log('❌ FAILED: Sub-100ms latency requirement not met');
      process.exit(1);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// Connection stability test
class StabilityTester {
  constructor(host = 'localhost:1999') {
    this.host = host;
    this.connections = [];
    this.disconnections = 0;
    this.reconnections = 0;
    this.startTime = Date.now();
  }

  async testConnectionStability(duration = 60000) { // 1 minute test
    console.log(`🔄 Testing connection stability for ${duration / 1000} seconds...`);
    
    const ws = new WebSocket(`ws://${this.host}/parties/chat/stability-test`);
    
    ws.on('open', () => {
      console.log('✅ Stability test connection established');
      this.connections.push(Date.now());
    });

    ws.on('close', () => {
      this.disconnections++;
      console.log(`🔌 Disconnection #${this.disconnections}`);
      
      // Attempt reconnection
      setTimeout(() => {
        this.reconnect();
      }, 1000);
    });

    ws.on('error', (error) => {
      console.error('❌ Stability test error:', error.message);
    });

    // Send periodic pings
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now()
        }));
      }
    }, 5000);

    // End test after duration
    setTimeout(() => {
      clearInterval(pingInterval);
      ws.close();
      this.analyzeStability();
    }, duration);
  }

  reconnect() {
    const ws = new WebSocket(`ws://${this.host}/parties/chat/stability-test`);
    
    ws.on('open', () => {
      this.reconnections++;
      console.log(`🔄 Reconnection #${this.reconnections} successful`);
    });

    ws.on('error', () => {
      // Ignore reconnection errors for this test
    });
  }

  analyzeStability() {
    const testDuration = Date.now() - this.startTime;
    const uptime = testDuration - (this.disconnections * 1000); // Assume 1s per disconnection
    const uptimePercentage = (uptime / testDuration) * 100;

    console.log('\n📊 CONNECTION STABILITY RESULTS');
    console.log('================================');
    console.log(`Test duration: ${(testDuration / 1000).toFixed(1)}s`);
    console.log(`Total disconnections: ${this.disconnections}`);
    console.log(`Successful reconnections: ${this.reconnections}`);
    console.log(`Estimated uptime: ${uptimePercentage.toFixed(1)}%`);

    if (uptimePercentage >= 99) {
      console.log('✅ PASSED: Connection stability requirement met (>99% uptime)');
    } else if (uptimePercentage >= 95) {
      console.log('⚠️  PARTIAL: Good stability but below 99% target');
    } else {
      console.log('❌ FAILED: Poor connection stability');
    }
  }
}

// Main test runner
async function runTests() {
  const args = process.argv.slice(2);
  const host = args[0] || 'localhost:1999';
  const testType = args[1] || 'latency';

  console.log('🧪 PartyKit Real-time System Testing');
  console.log('====================================');
  console.log(`Host: ${host}`);
  console.log(`Test type: ${testType}\n`);

  try {
    if (testType === 'latency' || testType === 'all') {
      const latencyTester = new LatencyTester(host);
      await latencyTester.connect();
      
      // Wait for tests to complete
      await new Promise(resolve => {
        const checkComplete = () => {
          if (latencyTester.testCount >= latencyTester.maxTests) {
            resolve();
          } else {
            setTimeout(checkComplete, 1000);
          }
        };
        checkComplete();
      });
      
      latencyTester.disconnect();
    }

    if (testType === 'stability' || testType === 'all') {
      const stabilityTester = new StabilityTester(host);
      await stabilityTester.testConnectionStability(30000); // 30 second test
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Test terminated');
  process.exit(0);
});

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { LatencyTester, StabilityTester };
