"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import ConfirmationModal from "@/components/ui/ConfirmationModal";
import {
  PlusIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  FlagIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";
import { formatTimeAgo } from "@/lib/utils";

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  };
  _count: {
    likes: number;
    comments: number;
    shares: number;
  };
  reported?: boolean;
}

export default function AdminPostsPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPosts, setTotalPosts] = useState(0);
  const [postsPerPage] = useState(10);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [postToDelete, setPostToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [filterOptions, setFilterOptions] = useState({
    privacy: "all",
    reported: false,
    dateRange: "all",
    sortBy: "newest"
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchPosts();
  }, [currentPage, filterOptions]);

  const fetchPosts = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: postsPerPage.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(filterOptions.privacy !== "all" && { privacy: filterOptions.privacy }),
        ...(filterOptions.reported && { reported: "true" }),
        ...(filterOptions.sortBy && { sortBy: filterOptions.sortBy }),
      });

      // Add date filters
      if (filterOptions.dateRange === "today") {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        params.append("dateFrom", today.toISOString());
      } else if (filterOptions.dateRange === "week") {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        params.append("dateFrom", weekAgo.toISOString());
      } else if (filterOptions.dateRange === "month") {
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        params.append("dateFrom", monthAgo.toISOString());
      }

      const response = await fetch(`/api/admin/posts?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch posts");
      }

      const data = await response.json();
      setPosts(data.posts);
      setTotalPosts(data.pagination.total);
    } catch (error) {
      console.error("Error fetching posts:", error);
      // Show error message to user
      alert("Failed to fetch posts. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchPosts();
  };

  const handleDeletePost = (postId: string) => {
    setPostToDelete(postId);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!postToDelete) return;

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/admin/posts/${postToDelete}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete post");
      }

      // Parse response to get media deletion info
      const result = await response.json();

      // Remove post from UI
      setPosts(posts.filter(post => post.id !== postToDelete));
      setTotalPosts(prev => prev - 1);
      setDeleteModalOpen(false);
      setPostToDelete(null);

      // Show success message with media deletion info
      let successMessage = 'Post deleted successfully';
      if (result.mediaDeleted) {
        if (result.mediaDeleted.deletedCount > 0) {
          successMessage += ` (${result.mediaDeleted.deletedCount} media files removed)`;
        }
        if (result.mediaDeleted.failedCount > 0) {
          successMessage += ` (${result.mediaDeleted.failedCount} media files could not be removed)`;
        }
      }

      alert(successMessage);
    } catch (error) {
      console.error("Error deleting post:", error);
      alert("Failed to delete post");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setPostToDelete(null);
  };

  const handleApprovePost = async (postId: string) => {
    try {
      const response = await fetch(`/api/admin/posts/${postId}/moderate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "approve",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to approve post");
      }

      // Update post in UI
      setPosts(posts.map(post =>
        post.id === postId ? { ...post, reported: false } : post
      ));
      alert("Post approved successfully");
    } catch (error) {
      console.error("Error approving post:", error);
      alert("Failed to approve post");
    }
  };

  const handleRejectPost = async (postId: string) => {
    if (!confirm("Are you sure you want to reject this post?")) return;

    try {
      const response = await fetch(`/api/admin/posts/${postId}/moderate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "reject",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to reject post");
      }

      // Update post in UI (mark as rejected but keep in list)
      setPosts(posts.map(post =>
        post.id === postId ? { ...post, reported: true } : post
      ));
      alert("Post rejected successfully");
    } catch (error) {
      console.error("Error rejecting post:", error);
      alert("Failed to reject post");
    }
  };

  const totalPages = Math.ceil(totalPosts / postsPerPage);

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Posts & Feed Control</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage user posts, content moderation, and feed settings
          </p>
        </div>
        <div className="mt-4 flex space-x-2 sm:mt-0">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <FunnelIcon className="mr-2 h-5 w-5" />
            Filters
          </Button>
          <Button onClick={() => router.push("/admin/posts/new")}>
            <PlusIcon className="mr-2 h-5 w-5" />
            Create Post
          </Button>
        </div>
      </div>

      {/* Search and filter section */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex w-full max-w-lg items-center">
          <div className="relative flex-grow">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full rounded-md border-0 py-2 pl-10 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
              placeholder="Search posts by content or user..."
            />
          </div>
          <Button type="submit" className="ml-2">
            Search
          </Button>
          <Button
            type="button"
            variant="outline"
            className="ml-2"
            onClick={() => {
              setSearchQuery("");
              setFilterOptions({
                privacy: "all",
                reported: false,
                dateRange: "all",
                sortBy: "newest"
              });
              setCurrentPage(1);
              fetchPosts();
            }}
          >
            <ArrowPathIcon className="h-5 w-5" />
          </Button>
        </form>
      </div>

      {/* Filter options */}
      {showFilters && (
        <div className="mb-6 rounded-md border border-gray-200 bg-white p-4 shadow-sm">
          <h2 className="mb-3 text-lg font-medium text-gray-900">Filter Options</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <label htmlFor="privacy" className="block text-sm font-medium text-gray-700">
                Privacy
              </label>
              <select
                id="privacy"
                value={filterOptions.privacy}
                onChange={(e) => setFilterOptions({ ...filterOptions, privacy: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="all">All</option>
                <option value="public">Public</option>
                <option value="friends">Friends</option>
                <option value="private">Private</option>
              </select>
            </div>
            <div>
              <label htmlFor="dateRange" className="block text-sm font-medium text-gray-700">
                Date Range
              </label>
              <select
                id="dateRange"
                value={filterOptions.dateRange}
                onChange={(e) => setFilterOptions({ ...filterOptions, dateRange: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
              </select>
            </div>
            <div>
              <label htmlFor="sortBy" className="block text-sm font-medium text-gray-700">
                Sort By
              </label>
              <select
                id="sortBy"
                value={filterOptions.sortBy}
                onChange={(e) => setFilterOptions({ ...filterOptions, sortBy: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="mostLiked">Most Liked</option>
                <option value="mostCommented">Most Commented</option>
              </select>
            </div>
            <div className="flex items-end">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filterOptions.reported}
                  onChange={(e) => setFilterOptions({ ...filterOptions, reported: e.target.checked })}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Reported Posts Only</span>
              </label>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              onClick={() => {
                setCurrentPage(1);
                fetchPosts();
              }}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}

      {/* Posts table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Spinner size="lg" />
          </div>
        ) : posts.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center p-6 text-center">
            <p className="text-lg font-medium text-gray-900">No posts found</p>
            <p className="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Post
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Author
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Privacy
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Engagement
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {posts.map((post) => (
                  <tr key={post.id} className={post.reported ? "bg-red-50" : ""}>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        {post.images && post.images.length > 0 ? (
                          <div className="h-10 w-10 flex-shrink-0">
                            <Image
                              src={post.images[0]}
                              alt="Post image"
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded object-cover"
                            />
                          </div>
                        ) : (
                          <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded bg-gray-100">
                            <span className="text-xs text-gray-500">No img</span>
                          </div>
                        )}
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {post.content.length > 50 ? post.content.substring(0, 50) + "..." : post.content}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {post.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        <div className="h-8 w-8 flex-shrink-0">
                          {post.user.image ? (
                            <Image
                              src={post.user.image}
                              alt={post.user.name}
                              width={32}
                              height={32}
                              className="h-8 w-8 rounded-full"
                            />
                          ) : (
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                              <span className="text-xs font-medium text-gray-500">
                                {post.user.name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{post.user.name}</div>
                          <div className="text-sm text-gray-500">@{post.user.username}</div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <div>{new Date(post.createdAt).toLocaleDateString()}</div>
                      <div className="text-xs">{formatTimeAgo(post.createdAt)}</div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                          post.privacy === "public"
                            ? "bg-green-100 text-green-800"
                            : post.privacy === "friends"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {post.privacy}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                          </svg>
                          {post._count.likes}
                        </span>
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                          </svg>
                          {post._count.comments}
                        </span>
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                          </svg>
                          {post._count.shares}
                        </span>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      {post.reported ? (
                        <span className="inline-flex rounded-full bg-red-100 px-2 text-xs font-semibold leading-5 text-red-800">
                          Reported
                        </span>
                      ) : (
                        <span className="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">
                          Active
                        </span>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link
                          href={`/admin/posts/${post.id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Post"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </Link>
                        <Link
                          href={`/admin/posts/${post.id}/edit`}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="Edit Post"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </Link>
                        <button
                          onClick={() => handleDeletePost(post.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Post"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                        {post.reported && (
                          <>
                            <button
                              onClick={() => handleApprovePost(post.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Approve Post"
                            >
                              <CheckCircleIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleRejectPost(post.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Reject Post"
                            >
                              <XCircleIcon className="h-5 w-5" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && posts.length > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{(currentPage - 1) * postsPerPage + 1}</span> to{" "}
            <span className="font-medium">
              {Math.min(currentPage * postsPerPage, totalPosts)}
            </span>{" "}
            of <span className="font-medium">{totalPosts}</span> posts
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className={`relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium ${
                currentPage === 1
                  ? "cursor-not-allowed text-gray-400"
                  : "text-gray-700 hover:bg-gray-50"
              }`}
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className={`relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium ${
                currentPage === totalPages
                  ? "cursor-not-allowed text-gray-400"
                  : "text-gray-700 hover:bg-gray-50"
              }`}
            >
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Post"
        message="Are you sure you want to delete this post? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        isLoading={isDeleting}
      />
    </AdminLayout>
  );
}
