"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Switch } from "@/components/ui/Switch";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { toast } from "react-hot-toast";
import {
  UserGroupIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  BanknotesIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UserIcon,
  CalendarIcon,
  CurrencyDollarIcon
} from "@heroicons/react/24/outline";

interface ReferralSettings {
  id: string;
  isEnabled: boolean;
  rewardAmount: string;
  minPayoutThreshold: string;
  requiresVerification: boolean;
  maxReferralsPerUser: number;
  rewardBothUsers: boolean;
  referredUserReward: string;
}

interface ReferralStats {
  totalReferrals: number;
  completedReferrals: number;
  pendingReferrals: number;
  cancelledReferrals: number;
  totalRewards: number;
  paidRewards: number;
  pendingRewards: number;
  topReferrers: Array<{
    id: string;
    name: string;
    username: string;
    totalReferrals: number;
    completedReferrals: number;
    totalEarnings: number;
  }>;
  recentReferrals: Array<{
    id: string;
    referrerName: string;
    referredName: string;
    status: string;
    rewardAmount: number;
    createdAt: string;
    completedAt?: string;
  }>;
}

interface ReferralUser {
  id: string;
  name: string;
  email: string;
  username: string;
  referralCode: string;
  totalReferrals: number;
  completedReferrals: number;
  pendingReferrals: number;
  totalEarnings: number;
  paidEarnings: number;
  pendingEarnings: number;
  isActive: boolean;
  createdAt: string;
  lastActivity: string;
}

interface ReferralDetail {
  id: string;
  referrer: {
    id: string;
    name: string;
    email: string;
    username: string;
  };
  referred: {
    id: string;
    name: string;
    email: string;
    username: string;
  };
  referralCode: string;
  status: 'pending' | 'completed' | 'cancelled';
  rewardAmount: number;
  createdAt: string;
  completedAt?: string;
  paidAt?: string;
  metadata?: any;
}

interface CommissionStats {
  totalCommissions: number;
  pendingCommissions: number;
  approvedCommissions: number;
  paidCommissions: number;
  cancelledCommissions: number;
  totalCommissionAmount: number;
  paidCommissionAmount: number;
  pendingCommissionAmount: number;
  averageCommissionAmount: number;
  topEarners: Array<{
    id: string;
    name: string;
    email: string;
    totalCommissions: number;
    totalEarnings: number;
  }>;
  recentCommissions: Array<{
    id: string;
    referrerName: string;
    referredUserName: string;
    planName: string;
    commissionAmount: number;
    status: string;
    createdAt: string;
  }>;
}

interface Commission {
  id: string;
  referrer: {
    id: string;
    name: string;
    email: string;
  };
  referredUser: {
    id: string;
    name: string;
    email: string;
  };
  planName: string;
  subscriptionAmount: number;
  commissionType: 'fixed' | 'percentage';
  commissionRate: number;
  commissionAmount: number;
  isFirstPurchase: boolean;
  status: 'pending' | 'approved' | 'paid' | 'cancelled';
  createdAt: string;
  approvedAt?: string;
  paidAt?: string;
}

type TabType = 'overview' | 'settings' | 'referrals' | 'analytics' | 'users' | 'commissions';

export default function AdminReferralSystemPage() {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [settings, setSettings] = useState<ReferralSettings | null>(null);
  const [stats, setStats] = useState<ReferralStats | null>(null);
  const [referralUsers, setReferralUsers] = useState<ReferralUser[]>([]);
  const [referralDetails, setReferralDetails] = useState<ReferralDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedReferral, setSelectedReferral] = useState<ReferralDetail | null>(null);
  const [showReferralModal, setShowReferralModal] = useState(false);
  const [selectedReferrals, setSelectedReferrals] = useState<string[]>([]);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [commissionStats, setCommissionStats] = useState<CommissionStats | null>(null);
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [selectedCommission, setSelectedCommission] = useState<Commission | null>(null);
  const [showCommissionModal, setShowCommissionModal] = useState(false);
  const [showRejectConfirm, setShowRejectConfirm] = useState(false);
  const [referralToReject, setReferralToReject] = useState<string | null>(null);

  const tabs = [
    {
      id: 'overview' as TabType,
      name: 'Overview',
      icon: ChartBarIcon,
      description: 'Referral system statistics and overview',
    },
    {
      id: 'settings' as TabType,
      name: 'Settings',
      icon: Cog6ToothIcon,
      description: 'Configure referral system parameters',
    },
    {
      id: 'referrals' as TabType,
      name: 'Referrals',
      icon: UserGroupIcon,
      description: 'Manage individual referrals',
    },
    {
      id: 'users' as TabType,
      name: 'Users',
      icon: UserIcon,
      description: 'Manage referral users',
    },
    {
      id: 'analytics' as TabType,
      name: 'Analytics',
      icon: BanknotesIcon,
      description: 'Detailed referral analytics',
    },
    {
      id: 'commissions' as TabType,
      name: 'Commission Tracking',
      icon: CurrencyDollarIcon,
      description: 'Monitor and manage referral commissions',
    },
  ];

  const fetchData = async () => {
    try {
      const endpoints = [
        '/api/admin/earning-world/referrals/settings',
        '/api/admin/earning-world/referrals/stats',
        '/api/admin/earning-world/referrals/users',
        '/api/admin/earning-world/referrals/details',
        '/api/admin/earning-world/referrals/commission-stats',
        '/api/admin/earning-world/referrals/commissions'
      ];

      const [settingsResponse, statsResponse, usersResponse, detailsResponse, commissionStatsResponse, commissionsResponse] = await Promise.all(
        endpoints.map(endpoint => fetch(endpoint))
      );

      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setSettings(settingsData.data);
      }

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        setReferralUsers(usersData.data || []);
      }

      if (detailsResponse.ok) {
        const detailsData = await detailsResponse.json();
        setReferralDetails(detailsData.data || []);
      }

      if (commissionStatsResponse.ok) {
        const commissionStatsData = await commissionStatsResponse.json();
        setCommissionStats(commissionStatsData.data);
      }

      if (commissionsResponse.ok) {
        const commissionsData = await commissionsResponse.json();
        setCommissions(commissionsData.data || []);
      }
    } catch (error) {
      console.error('Error fetching referral data:', error);
      toast.error('Failed to load referral data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'r':
            event.preventDefault();
            handleRefresh();
            break;
          case 'e':
            event.preventDefault();
            exportReferrals();
            break;
          case '1':
            event.preventDefault();
            setActiveTab('overview');
            break;
          case '2':
            event.preventDefault();
            setActiveTab('settings');
            break;
          case '3':
            event.preventDefault();
            setActiveTab('referrals');
            break;
          case '4':
            event.preventDefault();
            setActiveTab('users');
            break;
          case '5':
            event.preventDefault();
            setActiveTab('analytics');
            break;
          case '6':
            event.preventDefault();
            setActiveTab('commissions');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  // Filter and pagination logic
  const filteredReferrals = referralDetails.filter(referral => {
    const matchesSearch = searchTerm === '' ||
      referral.referrer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      referral.referred.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      referral.referrer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      referral.referred.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      referral.referralCode.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || referral.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredReferrals.length / itemsPerPage);
  const paginatedReferrals = filteredReferrals.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Update referral status
  const updateReferralStatus = async (referralId: string, newStatus: 'pending' | 'completed' | 'cancelled') => {
    setSaving(true);
    try {
      const response = await fetch(`/api/admin/earning-world/referrals/${referralId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();

      if (response.ok) {
        const statusText = newStatus === 'completed' ? 'approved' : newStatus === 'cancelled' ? 'rejected' : newStatus;
        toast.success(`Referral ${statusText} successfully`);
        await fetchData(); // Refresh data
      } else {
        toast.error(data.message || 'Failed to update referral status');
      }
    } catch (error) {
      console.error('Error updating referral status:', error);
      toast.error('Failed to update referral status. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // Process reward payment
  const processRewardPayment = async (referralId: string) => {
    setSaving(true);
    try {
      const response = await fetch(`/api/admin/earning-world/referrals/${referralId}/pay`, {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Reward payment processed successfully');
        await fetchData(); // Refresh data
      } else {
        toast.error(data.message || 'Failed to process reward payment');
      }
    } catch (error) {
      console.error('Error processing reward payment:', error);
      toast.error('Failed to process reward payment. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // Handle reject confirmation
  const handleRejectReferral = (referralId: string) => {
    setReferralToReject(referralId);
    setShowRejectConfirm(true);
  };

  // Confirm reject action
  const confirmRejectReferral = async () => {
    if (referralToReject) {
      await updateReferralStatus(referralToReject, 'cancelled');
      setShowRejectConfirm(false);
      setReferralToReject(null);
    }
  };

  // Bulk actions
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedReferrals(paginatedReferrals.map(r => r.id));
    } else {
      setSelectedReferrals([]);
    }
  };

  const handleSelectReferral = (referralId: string, checked: boolean) => {
    if (checked) {
      setSelectedReferrals(prev => [...prev, referralId]);
    } else {
      setSelectedReferrals(prev => prev.filter(id => id !== referralId));
    }
  };

  const processBulkAction = async (action: 'approve' | 'reject' | 'pay') => {
    if (selectedReferrals.length === 0) {
      toast.error('Please select referrals to process');
      return;
    }

    setBulkActionLoading(true);
    try {
      const promises = selectedReferrals.map(async (referralId) => {
        if (action === 'approve') {
          return updateReferralStatus(referralId, 'completed');
        } else if (action === 'reject') {
          return updateReferralStatus(referralId, 'cancelled');
        } else if (action === 'pay') {
          return processRewardPayment(referralId);
        }
      });

      await Promise.all(promises);
      toast.success(`Bulk ${action} completed successfully`);
      setSelectedReferrals([]);
      fetchData();
    } catch (error) {
      console.error(`Error in bulk ${action}:`, error);
      toast.error(`Failed to process bulk ${action}`);
    } finally {
      setBulkActionLoading(false);
    }
  };

  // Export functionality
  const exportReferrals = () => {
    const csvContent = [
      ['ID', 'Referrer Name', 'Referrer Email', 'Referred Name', 'Referred Email', 'Referral Code', 'Status', 'Reward Amount', 'Created Date', 'Completed Date', 'Paid Date'].join(','),
      ...filteredReferrals.map(referral => [
        referral.id,
        `"${referral.referrer.name}"`,
        referral.referrer.email,
        `"${referral.referred.name}"`,
        referral.referred.email,
        referral.referralCode,
        referral.status,
        referral.rewardAmount,
        referral.createdAt,
        referral.completedAt || '',
        referral.paidAt || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `referrals_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      const response = await fetch('/api/admin/earning-world/referrals/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        toast.success('Referral settings updated successfully');
      } else {
        toast.error('Failed to update referral settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to update referral settings');
    } finally {
      setSaving(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      cancelled: 'bg-red-100 text-red-800',
      approved: 'bg-blue-100 text-blue-800',
      paid: 'bg-green-100 text-green-800'
    };
    return variants[status as keyof typeof variants] || variants.pending;
  };

  // Commission functions
  const updateCommissionStatus = async (commissionId: string, newStatus: 'approved' | 'cancelled') => {
    try {
      const response = await fetch(`/api/admin/earning-world/referrals/commissions/${commissionId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        toast.success('Commission status updated successfully');
        fetchData();
      } else {
        toast.error('Failed to update commission status');
      }
    } catch (error) {
      console.error('Error updating commission status:', error);
      toast.error('Failed to update commission status');
    }
  };

  const processCommissionPayment = async (commissionId: string) => {
    try {
      const response = await fetch(`/api/admin/earning-world/referrals/commissions/${commissionId}/pay`, {
        method: 'POST',
      });

      if (response.ok) {
        toast.success('Commission payment processed successfully');
        fetchData();
      } else {
        toast.error('Failed to process commission payment');
      }
    } catch (error) {
      console.error('Error processing commission payment:', error);
      toast.error('Failed to process commission payment');
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Quick Actions</h3>
                    <div className="space-y-2">
                      <Button
                        size="sm"
                        onClick={() => setActiveTab('referrals')}
                        className="w-full"
                      >
                        <UserGroupIcon className="h-4 w-4 mr-2" />
                        Manage Referrals
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setActiveTab('users')}
                        className="w-full"
                      >
                        <UserIcon className="h-4 w-4 mr-2" />
                        View Users
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">System Status</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-600">System Online</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-600">Payments Active</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Settings</h3>
                    <div className="space-y-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setActiveTab('settings')}
                        className="w-full"
                      >
                        <Cog6ToothIcon className="h-4 w-4 mr-2" />
                        Configure System
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setActiveTab('commissions')}
                        className="w-full"
                      >
                        <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                        Commission Tracking
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={exportReferrals}
                        className="w-full"
                      >
                        <ArrowPathIcon className="h-4 w-4 mr-2" />
                        Export Data
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Stats Cards */}
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                  <div className="flex items-center">
                    <UserGroupIcon className="h-8 w-8 mr-4" />
                    <div>
                      <p className="text-sm opacity-90">Total Referrals</p>
                      <p className="text-2xl font-bold">{stats.totalReferrals}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                  <div className="flex items-center">
                    <CheckCircleIcon className="h-8 w-8 mr-4" />
                    <div>
                      <p className="text-sm opacity-90">Completed</p>
                      <p className="text-2xl font-bold">{stats.completedReferrals}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
                  <div className="flex items-center">
                    <ClockIcon className="h-8 w-8 mr-4" />
                    <div>
                      <p className="text-sm opacity-90">Pending</p>
                      <p className="text-2xl font-bold">{stats.pendingReferrals}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg p-6 text-white">
                  <div className="flex items-center">
                    <CurrencyDollarIcon className="h-8 w-8 mr-4" />
                    <div>
                      <p className="text-sm opacity-90">Total Rewards</p>
                      <p className="text-2xl font-bold">{formatCurrency(stats.totalRewards)}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Top Referrers */}
            {stats && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Top Referrers</CardTitle>
                    <CardDescription>Users with most successful referrals</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {stats.topReferrers.map((referrer, index) => (
                        <div key={referrer.id} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                              <span className="text-sm font-medium">#{index + 1}</span>
                            </div>
                            <div>
                              <p className="text-sm font-medium">{referrer.name}</p>
                              <p className="text-xs text-gray-500">@{referrer.username}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{referrer.completedReferrals} referrals</p>
                            <p className="text-xs text-gray-500">{formatCurrency(referrer.totalEarnings)}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Recent Referrals</CardTitle>
                    <CardDescription>Latest referral activities</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {stats.recentReferrals.map((referral) => (
                        <div key={referral.id} className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium">
                              {referral.referrerName} → {referral.referredName}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatDate(referral.createdAt)}
                            </p>
                          </div>
                          <div className="text-right">
                            <Badge className={getStatusBadge(referral.status)}>
                              {referral.status}
                            </Badge>
                            <p className="text-xs text-gray-500 mt-1">
                              {formatCurrency(referral.rewardAmount)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        );

      case 'settings':
        return (
          <div className="space-y-6">
            {settings && (
              <Card>
                <CardHeader>
                  <CardTitle>Referral System Settings</CardTitle>
                  <CardDescription>
                    Configure referral system parameters and rewards
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="isEnabled">Enable Referral System</Label>
                        <Switch
                          id="isEnabled"
                          checked={settings.isEnabled}
                          onCheckedChange={(checked) =>
                            setSettings({ ...settings, isEnabled: checked })
                          }
                        />
                      </div>

                      <div>
                        <Label htmlFor="rewardAmount">Reward Amount ($)</Label>
                        <Input
                          id="rewardAmount"
                          type="number"
                          step="0.01"
                          value={settings.rewardAmount}
                          onChange={(e) =>
                            setSettings({ ...settings, rewardAmount: e.target.value })
                          }
                        />
                      </div>

                      <div>
                        <Label htmlFor="minPayoutThreshold">Min Payout Threshold ($)</Label>
                        <Input
                          id="minPayoutThreshold"
                          type="number"
                          step="0.01"
                          value={settings.minPayoutThreshold}
                          onChange={(e) =>
                            setSettings({ ...settings, minPayoutThreshold: e.target.value })
                          }
                        />
                      </div>

                      <div>
                        <Label htmlFor="maxReferralsPerUser">Max Referrals Per User</Label>
                        <Input
                          id="maxReferralsPerUser"
                          type="number"
                          value={settings.maxReferralsPerUser}
                          onChange={(e) =>
                            setSettings({ ...settings, maxReferralsPerUser: parseInt(e.target.value) })
                          }
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="requiresVerification">Require Verification</Label>
                        <Switch
                          id="requiresVerification"
                          checked={settings.requiresVerification}
                          onCheckedChange={(checked) =>
                            setSettings({ ...settings, requiresVerification: checked })
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="rewardBothUsers">Reward Both Users</Label>
                        <Switch
                          id="rewardBothUsers"
                          checked={settings.rewardBothUsers}
                          onCheckedChange={(checked) =>
                            setSettings({ ...settings, rewardBothUsers: checked })
                          }
                        />
                      </div>

                      {settings.rewardBothUsers && (
                        <div>
                          <Label htmlFor="referredUserReward">Referred User Reward ($)</Label>
                          <Input
                            id="referredUserReward"
                            type="number"
                            step="0.01"
                            value={settings.referredUserReward}
                            onChange={(e) =>
                              setSettings({ ...settings, referredUserReward: e.target.value })
                            }
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={handleSaveSettings} disabled={saving}>
                      {saving ? <Spinner size="sm" className="mr-2" /> : null}
                      Save Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      case 'analytics':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Referral Analytics</CardTitle>
                <CardDescription>
                  Detailed analytics and performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <ChartBarIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Advanced Analytics Coming Soon
                  </h3>
                  <p className="text-gray-500">
                    Detailed referral analytics with charts and insights will be available here.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'referrals':
        return (
          <div className="space-y-6">
            {/* Search and Filter Controls */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Label htmlFor="search">Search Referrals</Label>
                    <Input
                      id="search"
                      placeholder="Search by name, email, or referral code..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="w-full md:w-48">
                    <Label htmlFor="status-filter">Status Filter</Label>
                    <select
                      id="status-filter"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="all">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                  <div className="flex items-end">
                    <Button
                      variant="outline"
                      onClick={exportReferrals}
                      className="mt-6"
                    >
                      <ArrowPathIcon className="h-4 w-4 mr-2" />
                      Export CSV
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Referrals Table */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Referral Details ({filteredReferrals.length})</CardTitle>
                    <CardDescription>
                      Manage individual referrals and their status
                    </CardDescription>
                  </div>
                  {selectedReferrals.length > 0 && (
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        onClick={() => processBulkAction('approve')}
                        disabled={bulkActionLoading}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircleIcon className="h-4 w-4 mr-1" />
                        Approve ({selectedReferrals.length})
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => processBulkAction('reject')}
                        disabled={bulkActionLoading}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <XCircleIcon className="h-4 w-4 mr-1" />
                        Reject ({selectedReferrals.length})
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => processBulkAction('pay')}
                        disabled={bulkActionLoading}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                        Pay ({selectedReferrals.length})
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <input
                            type="checkbox"
                            checked={selectedReferrals.length === paginatedReferrals.length && paginatedReferrals.length > 0}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Referrer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Referred User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Code
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Reward
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {paginatedReferrals.map((referral) => (
                        <tr key={referral.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="checkbox"
                              checked={selectedReferrals.includes(referral.id)}
                              onChange={(e) => handleSelectReferral(referral.id, e.target.checked)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {referral.referrer.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {referral.referrer.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {referral.referred.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {referral.referred.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {referral.referralCode}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge className={getStatusBadge(referral.status)}>
                              {referral.status}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(referral.rewardAmount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(referral.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              {/* View Details Button */}
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedReferral(referral);
                                  setShowReferralModal(true);
                                }}
                                title="View Details"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Button>

                              {/* Action buttons for pending referrals */}
                              {referral.status === 'pending' && (
                                <>
                                  <Button
                                    size="sm"
                                    onClick={() => updateReferralStatus(referral.id, 'completed')}
                                    className="bg-green-600 hover:bg-green-700 text-white"
                                    title="Approve Referral"
                                    disabled={saving}
                                  >
                                    {saving ? (
                                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                    ) : (
                                      <CheckCircleIcon className="h-4 w-4" />
                                    )}
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleRejectReferral(referral.id)}
                                    className="text-red-600 border-red-600 hover:bg-red-50"
                                    title="Reject Referral"
                                    disabled={saving}
                                  >
                                    {saving ? (
                                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent" />
                                    ) : (
                                      <XCircleIcon className="h-4 w-4" />
                                    )}
                                  </Button>
                                </>
                              )}

                              {/* Payment button for completed but unpaid referrals */}
                              {referral.status === 'completed' && !referral.paidAt && (
                                <Button
                                  size="sm"
                                  onClick={() => processRewardPayment(referral.id)}
                                  className="bg-blue-600 hover:bg-blue-700 text-white"
                                  title="Process Payment"
                                  disabled={saving}
                                >
                                  {saving ? (
                                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                  ) : (
                                    <CurrencyDollarIcon className="h-4 w-4" />
                                  )}
                                </Button>
                              )}

                              {/* Show status for completed and paid referrals */}
                              {referral.status === 'completed' && referral.paidAt && (
                                <span className="text-green-600 text-sm font-medium">
                                  Paid
                                </span>
                              )}

                              {/* Show status for cancelled referrals */}
                              {referral.status === 'cancelled' && (
                                <span className="text-red-600 text-sm font-medium">
                                  Rejected
                                </span>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-6">
                    <div className="text-sm text-gray-700">
                      Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredReferrals.length)} of {filteredReferrals.length} results
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <span className="flex items-center px-3 py-1 text-sm text-gray-700">
                        Page {currentPage} of {totalPages}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      case 'users':
        const filteredUsers = referralUsers.filter(user =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.referralCode.toLowerCase().includes(searchTerm.toLowerCase())
        );

        return (
          <div className="space-y-6">
            {/* Search Users */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Label htmlFor="user-search">Search Users</Label>
                    <Input
                      id="user-search"
                      placeholder="Search by name, email, username, or referral code..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Referral Users ({filteredUsers.length})</CardTitle>
                <CardDescription>
                  Manage users participating in the referral program
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Referral Code
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Referrals
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Completed
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Earnings
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredUsers.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {user.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {user.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {user.referralCode}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {user.totalReferrals}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {user.completedReferrals}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(user.totalEarnings)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge className={user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'commissions':
        const filteredCommissions = commissions.filter(commission => {
          const matchesSearch = searchTerm === '' ||
            commission.referrer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            commission.referrer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            commission.referredUser.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            commission.referredUser.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            commission.planName.toLowerCase().includes(searchTerm.toLowerCase());

          const matchesStatus = statusFilter === 'all' || commission.status === statusFilter;

          return matchesSearch && matchesStatus;
        });

        const totalCommissionPages = Math.ceil(filteredCommissions.length / itemsPerPage);
        const paginatedCommissions = filteredCommissions.slice(
          (currentPage - 1) * itemsPerPage,
          currentPage * itemsPerPage
        );

        return (
          <div className="space-y-6">
            {/* Commission Stats Cards */}
            {commissionStats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Commissions</p>
                        <p className="text-2xl font-bold text-gray-900">{commissionStats.totalCommissions}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <BanknotesIcon className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Paid</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {formatCurrency(commissionStats.paidCommissionAmount)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-yellow-100 rounded-lg">
                        <ClockIcon className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Pending Amount</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {formatCurrency(commissionStats.pendingCommissionAmount)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <ChartBarIcon className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Avg. Commission</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {formatCurrency(commissionStats.averageCommissionAmount)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Search and Filter Controls */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Label htmlFor="commission-search">Search Commissions</Label>
                    <Input
                      id="commission-search"
                      placeholder="Search by referrer, referred user, or plan name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="w-full md:w-48">
                    <Label htmlFor="commission-status-filter">Status Filter</Label>
                    <select
                      id="commission-status-filter"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="all">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="approved">Approved</option>
                      <option value="paid">Paid</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Commissions Table */}
            <Card>
              <CardHeader>
                <CardTitle>Commission Details ({filteredCommissions.length})</CardTitle>
                <CardDescription>
                  Manage individual commission payments and status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Referrer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Referred User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Commission
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {paginatedCommissions.map((commission) => (
                        <tr key={commission.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {commission.referrer.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {commission.referrer.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {commission.referredUser.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {commission.referredUser.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{commission.planName}</div>
                            <div className="text-sm text-gray-500">
                              {formatCurrency(commission.subscriptionAmount)}
                              {commission.isFirstPurchase && (
                                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                  First Purchase
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {formatCurrency(commission.commissionAmount)}
                            </div>
                            <div className="text-sm text-gray-500">
                              {commission.commissionType === 'percentage'
                                ? `${commission.commissionRate}%`
                                : 'Fixed'
                              }
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge className={getStatusBadge(commission.status)}>
                              {commission.status}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(commission.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedCommission(commission);
                                  setShowCommissionModal(true);
                                }}
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Button>
                              {commission.status === 'pending' && (
                                <>
                                  <Button
                                    size="sm"
                                    onClick={() => updateCommissionStatus(commission.id, 'approved')}
                                    className="bg-green-600 hover:bg-green-700"
                                  >
                                    <CheckCircleIcon className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => updateCommissionStatus(commission.id, 'cancelled')}
                                    className="text-red-600 border-red-600 hover:bg-red-50"
                                  >
                                    <XCircleIcon className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                              {commission.status === 'approved' && (
                                <Button
                                  size="sm"
                                  onClick={() => processCommissionPayment(commission.id)}
                                  className="bg-blue-600 hover:bg-blue-700"
                                >
                                  <CurrencyDollarIcon className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalCommissionPages > 1 && (
                  <div className="flex items-center justify-between mt-6">
                    <div className="text-sm text-gray-700">
                      Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredCommissions.length)} of {filteredCommissions.length} results
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <span className="flex items-center px-3 py-1 text-sm text-gray-700">
                        Page {currentPage} of {totalCommissionPages}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalCommissionPages))}
                        disabled={currentPage === totalCommissionPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Top Earners */}
            {commissionStats && commissionStats.topEarners.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Top Commission Earners</CardTitle>
                  <CardDescription>
                    Users with highest commission earnings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {commissionStats.topEarners.map((earner, index) => (
                      <div key={earner.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">#{index + 1}</span>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">{earner.name}</p>
                            <p className="text-xs text-gray-500">{earner.email}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            {formatCurrency(earner.totalEarnings)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {earner.totalCommissions} commissions
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            {/* Analytics Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <UserGroupIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {stats ? ((stats.completedReferrals / Math.max(stats.totalReferrals, 1)) * 100).toFixed(1) : '0'}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Avg. Reward</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {stats ? formatCurrency(stats.totalRewards / Math.max(stats.totalReferrals, 1)) : '$0.00'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <ClockIcon className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Pending Rate</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {stats ? ((stats.pendingReferrals / Math.max(stats.totalReferrals, 1)) * 100).toFixed(1) : '0'}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <XCircleIcon className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Cancellation Rate</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {stats ? ((stats.cancelledReferrals / Math.max(stats.totalReferrals, 1)) * 100).toFixed(1) : '0'}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Performers */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Top Referrers by Earnings</CardTitle>
                  <CardDescription>Users with highest referral earnings</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats?.topReferrers.slice(0, 5).map((referrer, index) => (
                      <div key={referrer.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">#{index + 1}</span>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">{referrer.name}</p>
                            <p className="text-xs text-gray-500">@{referrer.username}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            {formatCurrency(referrer.totalEarnings)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {referrer.completedReferrals} referrals
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Latest referral activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats?.recentReferrals.slice(0, 5).map((referral) => (
                      <div key={referral.id} className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {referral.referrerName} → {referral.referredName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(referral.createdAt)}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge className={getStatusBadge(referral.status)}>
                            {referral.status}
                          </Badge>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatCurrency(referral.rewardAmount)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* System Health */}
            <Card>
              <CardHeader>
                <CardTitle>System Health & Insights</CardTitle>
                <CardDescription>Key metrics and recommendations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      {stats ? Math.round((stats.paidRewards / Math.max(stats.totalRewards, 1)) * 100) : 0}%
                    </div>
                    <p className="text-sm text-gray-600">Payment Completion Rate</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {stats ? formatCurrency(stats.paidRewards) : '$0'} of {stats ? formatCurrency(stats.totalRewards) : '$0'} paid
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {referralUsers.filter(u => u.isActive).length}
                    </div>
                    <p className="text-sm text-gray-600">Active Referrers</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {referralUsers.length} total users
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">
                      {stats ? Math.round(stats.totalReferrals / Math.max(referralUsers.length, 1) * 10) / 10 : 0}
                    </div>
                    <p className="text-sm text-gray-600">Avg. Referrals per User</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Performance indicator
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-64">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">Loading referral system data...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!stats) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-64">
          <XCircleIcon className="h-16 w-16 text-red-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Failed to Load Data
          </h3>
          <p className="text-gray-500 mb-4">
            Unable to load referral system data. Please try again.
          </p>
          <Button onClick={handleRefresh} disabled={refreshing}>
            <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Retry
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Referral System Management</h1>
            <p className="text-gray-600 mt-1">
              Manage referral system settings and monitor performance
            </p>
            {/* Notifications */}
            {stats && stats.pendingReferrals > 0 && (
              <div className="mt-2 flex items-center text-sm text-amber-600">
                <ClockIcon className="h-4 w-4 mr-1" />
                {stats.pendingReferrals} referrals pending approval
              </div>
            )}
            {stats && stats.completedReferrals > 0 && stats.paidRewards < stats.totalRewards && (
              <div className="mt-1 flex items-center text-sm text-blue-600">
                <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                {formatCurrency(stats.totalRewards - stats.paidRewards)} in unpaid rewards
              </div>
            )}
          </div>
          <div className="flex space-x-3">
            <div className="relative group">
              <Button
                variant="outline"
                size="sm"
                className="text-gray-500 hover:text-gray-700"
                title="Keyboard Shortcuts"
              >
                ?
              </Button>
              <div className="absolute right-0 top-full mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg p-4 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <h4 className="font-medium text-gray-900 mb-2">Keyboard Shortcuts</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Refresh</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+R</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Export</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+E</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Overview</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+1</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Settings</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+2</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Referrals</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+3</kbd>
                  </div>
                </div>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex flex-wrap space-x-2 md:space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-2 md:px-1 border-b-2 font-medium text-xs md:text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 md:h-5 md:w-5 inline mr-1 md:mr-2" />
                <span className="hidden sm:inline">{tab.name}</span>
                <span className="sm:hidden">{tab.name.substring(0, 4)}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {renderTabContent()}

        {/* Referral Detail Modal */}
        {showReferralModal && selectedReferral && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Referral Details
                  </h3>
                  <button
                    onClick={() => setShowReferralModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircleIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Referrer Information</h4>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <p className="text-sm"><strong>Name:</strong> {selectedReferral.referrer.name}</p>
                        <p className="text-sm"><strong>Email:</strong> {selectedReferral.referrer.email}</p>
                        <p className="text-sm"><strong>Username:</strong> {selectedReferral.referrer.username}</p>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Referred User Information</h4>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <p className="text-sm"><strong>Name:</strong> {selectedReferral.referred.name}</p>
                        <p className="text-sm"><strong>Email:</strong> {selectedReferral.referred.email}</p>
                        <p className="text-sm"><strong>Username:</strong> {selectedReferral.referred.username}</p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Referral Code</h4>
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        {selectedReferral.referralCode}
                      </span>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Status</h4>
                      <Badge className={getStatusBadge(selectedReferral.status)}>
                        {selectedReferral.status}
                      </Badge>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Reward Amount</h4>
                      <p className="text-lg font-semibold text-green-600">
                        {formatCurrency(selectedReferral.rewardAmount)}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Created Date</h4>
                      <p className="text-sm text-gray-600">
                        {formatDate(selectedReferral.createdAt)}
                      </p>
                    </div>

                    {selectedReferral.completedAt && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Completed Date</h4>
                        <p className="text-sm text-gray-600">
                          {formatDate(selectedReferral.completedAt)}
                        </p>
                      </div>
                    )}

                    {selectedReferral.paidAt && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Paid Date</h4>
                        <p className="text-sm text-gray-600">
                          {formatDate(selectedReferral.paidAt)}
                        </p>
                      </div>
                    )}
                  </div>

                  {selectedReferral.metadata && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Additional Information</h4>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                          {JSON.stringify(selectedReferral.metadata, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end space-x-3 pt-4 border-t">
                    {/* Close button - always visible */}
                    <Button
                      variant="outline"
                      onClick={() => setShowReferralModal(false)}
                    >
                      Close
                    </Button>

                    {/* Action buttons for pending referrals */}
                    {selectedReferral.status === 'pending' && (
                      <>
                        <Button
                          onClick={async () => {
                            await updateReferralStatus(selectedReferral.id, 'completed');
                            setShowReferralModal(false);
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white"
                          disabled={saving}
                        >
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                          {saving ? 'Approving...' : 'Approve'}
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setShowReferralModal(false);
                            handleRejectReferral(selectedReferral.id);
                          }}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                          disabled={saving}
                        >
                          <XCircleIcon className="h-4 w-4 mr-2" />
                          Reject
                        </Button>
                      </>
                    )}

                    {/* Payment button for completed but unpaid referrals */}
                    {selectedReferral.status === 'completed' && !selectedReferral.paidAt && (
                      <Button
                        onClick={async () => {
                          await processRewardPayment(selectedReferral.id);
                          setShowReferralModal(false);
                        }}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        disabled={saving}
                      >
                        <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                        {saving ? 'Processing...' : 'Process Payment'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Reject Confirmation Modal */}
        {showRejectConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <XCircleIcon className="h-6 w-6 text-red-600 mr-3" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Reject Referral
                </h3>
              </div>

              <p className="text-gray-600 mb-6">
                Are you sure you want to reject this referral? This action cannot be undone and the referrer will not receive any reward for this referral.
              </p>

              <div className="flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowRejectConfirm(false);
                    setReferralToReject(null);
                  }}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={confirmRejectReferral}
                  className="bg-red-600 hover:bg-red-700 text-white"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent mr-2" />
                      Rejecting...
                    </>
                  ) : (
                    <>
                      <XCircleIcon className="h-4 w-4 mr-2" />
                      Reject Referral
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
