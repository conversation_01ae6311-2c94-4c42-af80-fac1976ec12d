"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/Button";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, StarIcon } from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import { toast } from "react-hot-toast";

const reviewSchema = z.object({
  rating: z.number().min(1, "Rating is required").max(5),
  comment: z.string().max(1000, "Comment must be less than 1000 characters").optional(),
});

type ReviewFormData = z.infer<typeof reviewSchema>;

interface StoreReviewFormProps {
  storeId: string;
  existingReview: {
    id: string;
    rating: number;
    comment: string | null;
  } | null;
  onClose: () => void;
  onReviewSubmitted: (review: any) => void;
}

export function StoreReviewForm({ 
  storeId, 
  existingReview, 
  onClose,
  onReviewSubmitted
}: StoreReviewFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hoverRating, setHoverRating] = useState(0);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      rating: existingReview?.rating || 0,
      comment: existingReview?.comment || "",
    },
  });
  
  const watchRating = watch("rating", existingReview?.rating || 0);

  const onSubmit = async (data: ReviewFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const response = await fetch(`/api/marketplace/stores/${storeId}/reviews`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          rating: data.rating,
          comment: data.comment || null,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to submit review");
      }

      // Review submitted successfully - close the form and notify parent
      onClose();

      // Try to refresh the reviews data, but don't fail if this doesn't work
      try {
        const reviewsResponse = await fetch(`/api/marketplace/stores/${storeId}/reviews`);
        if (reviewsResponse.ok) {
          const reviewsData = await reviewsResponse.json();
          onReviewSubmitted({
            success: true,
            message: result.message,
            averageRating: reviewsData.averageRating,
            reviews: reviewsData.reviews
          });
        } else {
          // Fallback - just notify success without updated data
          onReviewSubmitted({
            success: true,
            message: result.message
          });
        }
      } catch (refreshError) {
        console.warn("Could not refresh reviews data:", refreshError);
        // Still notify success since the review was submitted
        onReviewSubmitted({
          success: true,
          message: result.message
        });
      }
    } catch (err) {
      console.error("Error submitting review:", err);
      const errorMessage = err instanceof Error ? err.message : "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={true} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
          <div className="mb-4 flex items-center justify-between">
            <DialogTitle className="text-lg font-medium text-gray-900">
              {existingReview ? "Edit Your Review" : "Write a Review"}
            </DialogTitle>
            <button
              type="button"
              onClick={onClose}
              className="rounded-md bg-white text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <div className="rounded-md bg-red-50 p-3 text-sm text-red-600">
                {error}
              </div>
            )}
            
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Rating *
              </label>
              <div className="flex items-center">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((rating) => (
                    <button
                      key={rating}
                      type="button"
                      className="p-1"
                      onMouseEnter={() => setHoverRating(rating)}
                      onMouseLeave={() => setHoverRating(0)}
                      onClick={() => setValue("rating", rating, { shouldValidate: true })}
                    >
                      {(hoverRating || watchRating) >= rating ? (
                        <StarIconSolid className="h-8 w-8 text-yellow-400" />
                      ) : (
                        <StarIcon className="h-8 w-8 text-gray-300" />
                      )}
                    </button>
                  ))}
                  <input
                    type="hidden"
                    {...register("rating")}
                  />
                </div>
              </div>
              {errors.rating && (
                <p className="mt-1 text-sm text-red-500">{errors.rating.message}</p>
              )}
            </div>
            
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Comment
              </label>
              <textarea
                className="w-full rounded-md border border-gray-300 px-3 py-2 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
                placeholder="Share your experience with this store (optional)"
                {...register("comment")}
              ></textarea>
              {errors.comment && (
                <p className="mt-1 text-sm text-red-500">{errors.comment.message}</p>
              )}
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isSubmitting}
              >
                {existingReview ? "Update Review" : "Submit Review"}
              </Button>
            </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
