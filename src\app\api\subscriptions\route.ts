import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptions, notifications, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { and, eq, sql, or, desc } from "drizzle-orm";

const subscriptionSchema = z.object({
  targetUserId: z.string(),
});

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const type = url.searchParams.get('type') || 'subscriptions'; // 'subscriptions' or 'subscribers'
    const search = url.searchParams.get('search') || '';
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    if (type === 'subscriptions') {
      // Get user's subscriptions (people they follow)
      const userSubscriptions = await db
        .select({
          id: subscriptions.id,
          targetUserId: subscriptions.targetUserId,
          name: users.name,
          username: users.username,
          image: users.image,
          createdAt: subscriptions.createdAt,
        })
        .from(subscriptions)
        .leftJoin(users, eq(subscriptions.targetUserId, users.id))
        .where(
          and(
            eq(subscriptions.subscriberId, session.user.id),
            search ? or(
              sql`${users.name} LIKE ${`%${search}%`}`,
              sql`${users.username} LIKE ${`%${search}%`}`
            ) : sql`1=1`
          )
        )
        .orderBy(desc(subscriptions.createdAt))
        .limit(limit)
        .offset(offset);

      // Get total count for pagination
      const totalCount = await db
        .select({ count: sql<number>`count(*)` })
        .from(subscriptions)
        .leftJoin(users, eq(subscriptions.targetUserId, users.id))
        .where(
          and(
            eq(subscriptions.subscriberId, session.user.id),
            search ? or(
              sql`${users.name} LIKE ${`%${search}%`}`,
              sql`${users.username} LIKE ${`%${search}%`}`
            ) : sql`1=1`
          )
        );

      return NextResponse.json({
        subscriptions: userSubscriptions,
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit,
          total: totalCount[0]?.count || 0,
          hasMore: (offset + limit) < (totalCount[0]?.count || 0)
        }
      });

    } else if (type === 'subscribers') {
      // Get user's subscribers (people who follow them)
      const userSubscribers = await db
        .select({
          id: subscriptions.id,
          subscriberId: subscriptions.subscriberId,
          name: users.name,
          username: users.username,
          image: users.image,
          createdAt: subscriptions.createdAt,
        })
        .from(subscriptions)
        .leftJoin(users, eq(subscriptions.subscriberId, users.id))
        .where(
          and(
            eq(subscriptions.targetUserId, session.user.id),
            search ? or(
              sql`${users.name} LIKE ${`%${search}%`}`,
              sql`${users.username} LIKE ${`%${search}%`}`
            ) : sql`1=1`
          )
        )
        .orderBy(desc(subscriptions.createdAt))
        .limit(limit)
        .offset(offset);

      // Get total count for pagination
      const totalCount = await db
        .select({ count: sql<number>`count(*)` })
        .from(subscriptions)
        .leftJoin(users, eq(subscriptions.subscriberId, users.id))
        .where(
          and(
            eq(subscriptions.targetUserId, session.user.id),
            search ? or(
              sql`${users.name} LIKE ${`%${search}%`}`,
              sql`${users.username} LIKE ${`%${search}%`}`
            ) : sql`1=1`
          )
        );

      return NextResponse.json({
        subscribers: userSubscribers,
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit,
          total: totalCount[0]?.count || 0,
          hasMore: (offset + limit) < (totalCount[0]?.count || 0)
        }
      });
    }

    return NextResponse.json(
      { message: "Invalid type parameter" },
      { status: 400 }
    );

  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { targetUserId } = subscriptionSchema.parse(body);

    // Prevent subscribing to self
    if (targetUserId === session.user.id) {
      return NextResponse.json(
        { message: "Cannot subscribe to yourself" },
        { status: 400 }
      );
    }

    // Check if already subscribed
    const existingSubscription = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, session.user.id),
        eq(subscriptions.targetUserId, targetUserId)
      ),
    });

    if (existingSubscription) {
      return NextResponse.json(
        { message: "Already subscribed to this user" },
        { status: 400 }
      );
    }

    // Create the subscription
    const subscriptionId = uuidv4();
    await db.insert(subscriptions).values({
      id: subscriptionId,
      subscriberId: session.user.id,
      targetUserId: targetUserId,
    });

    // Create a notification for the target user
    await db.insert(notifications).values({
      id: uuidv4(),
      recipientId: targetUserId,
      type: "subscription",
      senderId: session.user.id,
      subscriptionId,
    });

    return NextResponse.json(
      { message: "Successfully subscribed", subscriptionId },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating subscription:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
