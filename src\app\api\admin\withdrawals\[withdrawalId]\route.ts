import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { walletTransactions, wallets } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";
import { z } from "zod";

const updateWithdrawalSchema = z.object({
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']),
  adminNote: z.string().optional(),
  gatewayTransactionId: z.string().optional(),
});

// Update withdrawal status
export async function PATCH(
  req: Request,
  context: { params: Promise<{ withdrawalId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { withdrawalId } = await context.params;
    const body = await req.json();
    const validatedData = updateWithdrawalSchema.parse(body);

    // Get the withdrawal transaction
    const withdrawal = await db.query.walletTransactions.findFirst({
      where: eq(walletTransactions.id, withdrawalId),
    });

    if (!withdrawal || withdrawal.type !== 'withdraw') {
      return NextResponse.json(
        { message: "Withdrawal not found" },
        { status: 404 }
      );
    }

    const currentStatus = withdrawal.status;
    const newStatus = validatedData.status;

    // Handle status transitions
    if (currentStatus === 'pending' && newStatus === 'cancelled') {
      // Refund the amount back to earning wallet
      await db.update(wallets)
        .set({
          earningBalance: sql`earningBalance + ${withdrawal.amount}`,
          totalWithdrawn: sql`totalWithdrawn - ${withdrawal.amount}`
        })
        .where(eq(wallets.userId, withdrawal.userId));
    } else if (currentStatus === 'processing' && newStatus === 'failed') {
      // Refund the amount back to earning wallet
      await db.update(wallets)
        .set({
          earningBalance: sql`earningBalance + ${withdrawal.amount}`,
          totalWithdrawn: sql`totalWithdrawn - ${withdrawal.amount}`
        })
        .where(eq(wallets.userId, withdrawal.userId));
    }

    // Update the withdrawal transaction
    const updateData: any = {
      status: newStatus,
      updatedAt: new Date(),
    };

    if (validatedData.gatewayTransactionId) {
      updateData.gatewayTransactionId = validatedData.gatewayTransactionId;
    }

    // Store admin note in metadata for now
    if (validatedData.adminNote) {
      const currentMetadata = withdrawal.metadata || {};
      updateData.metadata = {
        ...currentMetadata,
        adminNote: validatedData.adminNote,
        processedAt: newStatus === 'completed' ? new Date().toISOString() : undefined,
      };
    } else if (newStatus === 'completed') {
      const currentMetadata = withdrawal.metadata || {};
      updateData.metadata = {
        ...currentMetadata,
        processedAt: new Date().toISOString(),
      };
    }

    await db.update(walletTransactions)
      .set(updateData)
      .where(eq(walletTransactions.id, withdrawalId));

    // Get updated withdrawal
    const updatedWithdrawal = await db.query.walletTransactions.findFirst({
      where: eq(walletTransactions.id, withdrawalId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedWithdrawal,
      message: `Withdrawal ${newStatus} successfully`,
    });
  } catch (error: any) {
    console.error("Error updating withdrawal:", error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid input data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to update withdrawal"
      },
      { status: 500 }
    );
  }
}

// Get single withdrawal details
export async function GET(
  req: Request,
  context: { params: Promise<{ withdrawalId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { withdrawalId } = await context.params;

    const withdrawal = await db.query.walletTransactions.findFirst({
      where: eq(walletTransactions.id, withdrawalId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
      },
    });

    if (!withdrawal || withdrawal.type !== 'withdraw') {
      return NextResponse.json(
        { message: "Withdrawal not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: withdrawal,
    });
  } catch (error: any) {
    console.error("Error fetching withdrawal:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch withdrawal"
      },
      { status: 500 }
    );
  }
}
