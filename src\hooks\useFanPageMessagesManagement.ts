"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import toast from "react-hot-toast";

interface FanPage {
  id: string;
  name: string;
  username: string;
  profileImage: string | null;
  isVerified: boolean;
}

interface FanPageMessage {
  id: string;
  fanPageId: string;
  senderId: string;
  content: string;
  isFromPage?: boolean; // true if message is from page owner
  read: boolean;
  createdAt: string;
  sender: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface FanPageConversation {
  senderId: string;
  sender: {
    id: string;
    name: string | null;
    image: string | null;
  };
  lastMessage: FanPageMessage;
  unreadCount: number;
  messages: FanPageMessage[];
}

interface MessageStats {
  totalMessages: number;
  unreadMessages: number;
  totalConversations: number;
  averageResponseTime: number; // in minutes
  messagesLast24h: number;
}

export function useFanPageMessagesManagement() {
  const { data: session } = useSession();
  const [ownedPages, setOwnedPages] = useState<FanPage[]>([]);
  const [selectedPageId, setSelectedPageId] = useState<string | null>(null);
  const [conversations, setConversations] = useState<FanPageConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [messages, setMessages] = useState<FanPageMessage[]>([]);
  const [stats, setStats] = useState<MessageStats>({
    totalMessages: 0,
    unreadMessages: 0,
    totalConversations: 0,
    averageResponseTime: 0,
    messagesLast24h: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);

  // Fetch owned fan pages
  const fetchOwnedPages = useCallback(async () => {
    if (!session?.user) return;

    try {
      const response = await fetch("/api/fan-pages/owned");
      const data = await response.json();

      if (data.success) {
        setOwnedPages(data.data);
        // Auto-select first page if available
        if (data.data.length > 0 && !selectedPageId) {
          setSelectedPageId(data.data[0].id);
        }
      }
    } catch (error) {
      console.error("Error fetching owned pages:", error);
      toast.error("Failed to load fan pages");
    }
  }, [session?.user, selectedPageId]);

  // Fetch conversations for selected page
  const fetchConversations = useCallback(async (pageId: string) => {
    if (!pageId) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/fan-pages/${pageId}/messages`);
      const data = await response.json();

      console.log('API Response:', data); // Debug log

      if (data.success && data.data) {
        // Group messages by sender
        const messagesBySender: { [senderId: string]: FanPageMessage[] } = {};

        data.data.forEach((message: FanPageMessage) => {
          if (!messagesBySender[message.senderId]) {
            messagesBySender[message.senderId] = [];
          }
          messagesBySender[message.senderId].push(message);
        });

        // Create conversations
        const conversationList: FanPageConversation[] = Object.entries(messagesBySender).map(([senderId, messages]) => {
          const sortedMessages = messages.sort((a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
          const lastMessage = sortedMessages[sortedMessages.length - 1];
          // Only count unread messages from users (not from page)
          const unreadCount = messages.filter(m => !m.read && !m.isFromPage).length;

          return {
            senderId,
            sender: lastMessage.sender,
            lastMessage,
            unreadCount,
            messages: sortedMessages,
          };
        });

        // Sort conversations by last message time
        conversationList.sort((a, b) =>
          new Date(b.lastMessage.createdAt).getTime() - new Date(a.lastMessage.createdAt).getTime()
        );

        setConversations(conversationList);
      } else {
        console.error('API Error:', data);
        setConversations([]);
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
      toast.error("Failed to load conversations");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch message statistics
  const fetchStats = useCallback(async (pageId: string) => {
    if (!pageId) return;

    try {
      const response = await fetch(`/api/fan-pages/${pageId}/messages/stats`);
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  }, []);

  // Send message
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!selectedPageId || !content.trim()) return false;

    try {
      setIsSending(true);
      const response = await fetch(`/api/fan-pages/${selectedPageId}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: content.trim(),
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Refresh conversations and messages
        await fetchConversations(selectedPageId);
        await fetchStats(selectedPageId);
        return true;
      } else {
        toast.error(data.error || "Failed to send message");
        return false;
      }
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message");
      return false;
    } finally {
      setIsSending(false);
    }
  }, [selectedPageId, fetchConversations, fetchStats]);

  // Send reply to user
  const sendReply = useCallback(async (content: string, recipientId: string) => {
    if (!selectedPageId || !content.trim() || !recipientId || !session?.user) return false;

    try {
      setIsSending(true);

      // Create optimistic message for immediate UI update
      const optimisticMessage: FanPageMessage = {
        id: `temp-${Date.now()}`,
        fanPageId: selectedPageId,
        senderId: session.user.id,
        content: content.trim(),
        isFromPage: true,
        read: false,
        createdAt: new Date().toISOString(),
        sender: {
          id: session.user.id,
          name: session.user.name || null,
          image: session.user.image || null,
        },
      };

      // Add optimistic message to current messages
      setMessages(prev => [...prev, optimisticMessage]);

      // Update conversations with optimistic message
      setConversations(prev =>
        prev.map(conv => {
          if (conv.senderId === recipientId) {
            return {
              ...conv,
              messages: [...conv.messages, optimisticMessage],
              lastMessage: optimisticMessage,
            };
          }
          return conv;
        })
      );

      const response = await fetch(`/api/fan-pages/${selectedPageId}/messages/reply`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: content.trim(),
          recipientId,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Replace optimistic message with real message
        const realMessage = data.data;
        setMessages(prev =>
          prev.map(msg =>
            msg.id === optimisticMessage.id ? realMessage : msg
          )
        );

        // Update conversations with new message
        setConversations(prev =>
          prev.map(conv => {
            if (conv.senderId === recipientId) {
              const updatedMessages = conv.messages.map(msg =>
                msg.id === optimisticMessage.id ? realMessage : msg
              );
              return {
                ...conv,
                messages: updatedMessages,
                lastMessage: realMessage,
              };
            }
            return conv;
          })
        );

        // Real-time messaging disabled - Socket.IO removed
        // TODO: Implement alternative real-time messaging system

        // Refresh stats
        await fetchStats(selectedPageId);
        return true;
      } else {
        // Remove optimistic message on failure
        setMessages(prev => prev.filter(msg => msg.id !== optimisticMessage.id));
        setConversations(prev =>
          prev.map(conv => {
            if (conv.senderId === recipientId) {
              return {
                ...conv,
                messages: conv.messages.filter(msg => msg.id !== optimisticMessage.id),
              };
            }
            return conv;
          })
        );
        toast.error(data.error || "Failed to send reply");
        return false;
      }
    } catch (error) {
      console.error("Error sending reply:", error);
      // Remove optimistic message on error
      setMessages(prev => prev.filter(msg => msg.id.startsWith('temp-')));
      setConversations(prev =>
        prev.map(conv => {
          if (conv.senderId === recipientId) {
            return {
              ...conv,
              messages: conv.messages.filter(msg => !msg.id.startsWith('temp-')),
            };
          }
          return conv;
        })
      );
      toast.error("Failed to send reply");
      return false;
    } finally {
      setIsSending(false);
    }
  }, [selectedPageId, session?.user, fetchConversations, fetchStats]);

  // Mark messages as read
  const markAsRead = useCallback(async (messageIds: string[]) => {
    if (!selectedPageId || messageIds.length === 0) return;

    try {
      // Optimistic update - mark messages as read immediately in UI
      setMessages(prev =>
        prev.map(msg =>
          messageIds.includes(msg.id) ? { ...msg, read: true } : msg
        )
      );

      setConversations(prev =>
        prev.map(conv => ({
          ...conv,
          messages: conv.messages.map(msg =>
            messageIds.includes(msg.id) ? { ...msg, read: true } : msg
          ),
          unreadCount: conv.messages.filter(msg =>
            !messageIds.includes(msg.id) && !msg.read && !msg.isFromPage
          ).length
        }))
      );

      const response = await fetch(`/api/fan-pages/${selectedPageId}/messages`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messageIds,
          read: true,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Refresh stats to get updated counts
        await fetchStats(selectedPageId);
      } else {
        // Revert optimistic update on failure
        setMessages(prev =>
          prev.map(msg =>
            messageIds.includes(msg.id) ? { ...msg, read: false } : msg
          )
        );

        setConversations(prev =>
          prev.map(conv => ({
            ...conv,
            messages: conv.messages.map(msg =>
              messageIds.includes(msg.id) ? { ...msg, read: false } : msg
            ),
            unreadCount: conv.messages.filter(msg => !msg.read && !msg.isFromPage).length
          }))
        );
      }
    } catch (error) {
      console.error("Error marking messages as read:", error);
      // Revert optimistic update on error
      setMessages(prev =>
        prev.map(msg =>
          messageIds.includes(msg.id) ? { ...msg, read: false } : msg
        )
      );

      setConversations(prev =>
        prev.map(conv => ({
          ...conv,
          messages: conv.messages.map(msg =>
            messageIds.includes(msg.id) ? { ...msg, read: false } : msg
          ),
          unreadCount: conv.messages.filter(msg => !msg.read && !msg.isFromPage).length
        }))
      );
    }
  }, [selectedPageId, fetchStats]);

  // Select conversation and mark as read
  const selectConversation = useCallback(async (senderId: string) => {
    setSelectedConversation(senderId);

    const conversation = conversations.find(c => c.senderId === senderId);
    if (conversation) {
      setMessages(conversation.messages);

      // Mark unread user messages as read (not page replies)
      const unreadMessageIds = conversation.messages
        .filter(m => !m.read && !m.isFromPage)
        .map(m => m.id);

      if (unreadMessageIds.length > 0) {
        await markAsRead(unreadMessageIds);
      }
    }
  }, [conversations, markAsRead]);

  // Update messages when selected conversation changes
  useEffect(() => {
    if (selectedConversation) {
      const conversation = conversations.find(c => c.senderId === selectedConversation);
      if (conversation) {
        setMessages(conversation.messages);
      }
    }
  }, [conversations, selectedConversation]);

  // Effects
  useEffect(() => {
    if (session?.user) {
      fetchOwnedPages();
    }
  }, [session?.user, fetchOwnedPages]);

  useEffect(() => {
    if (selectedPageId) {
      fetchConversations(selectedPageId);
      fetchStats(selectedPageId);
    }
  }, [selectedPageId, fetchConversations, fetchStats]);

  return {
    ownedPages,
    selectedPageId,
    setSelectedPageId,
    conversations,
    selectedConversation,
    messages,
    stats,
    isLoading,
    isSending,
    sendMessage,
    sendReply,
    selectConversation,
    markAsRead,
    refreshData: () => {
      if (selectedPageId) {
        fetchConversations(selectedPageId);
        fetchStats(selectedPageId);
      }
    },
  };
}
