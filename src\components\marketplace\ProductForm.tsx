"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { useDropzone } from "react-dropzone";
import { PhotoIcon, XMarkIcon, CheckCircleIcon } from "@heroicons/react/24/outline";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";

const productSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters").max(255, "Title must be less than 255 characters"),
  description: z.string().max(2000, "Description must be less than 2000 characters").optional(),
  price: z.string().min(1, "Price is required").refine(
    (val) => !isNaN(parseFloat(val)) && parseFloat(val) >= 0,
    { message: "Price must be a valid number" }
  ),
  condition: z.enum(["new", "like_new", "good", "fair", "poor"], {
    errorMap: () => ({ message: "Please select a valid condition" }),
  }),
  category: z.string().min(1, "Category is required").max(100, "Category must be less than 100 characters"),
  location: z.string().max(255, "Location must be less than 255 characters").optional(),
});

type ProductFormData = z.infer<typeof productSchema>;

interface ProductFormProps {
  storeId: string;
  product?: {
    id: string;
    title: string;
    description: string | null;
    price: number;
    condition: string;
    category: string;
    location: string | null;
    photos: string[] | null;
  };
}

export function ProductForm({ storeId, product }: ProductFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const [photosPreviews, setPhotosPreviews] = useState<string[]>(
    product?.photos ? [...product.photos] : []
  );
  const isEditing = !!product;

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: product
      ? {
          title: product.title,
          description: product.description || "",
          price: (product.price / 100).toString(), // Convert cents to dollars for display
          condition: product.condition as any,
          category: product.category,
          location: product.location || "",
        }
      : undefined,
  });

  // Photos dropzone
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 10,
    onDrop: (acceptedFiles) => {
      const newPhotos = [...photos, ...acceptedFiles].slice(0, 10);
      setPhotos(newPhotos);

      // Create previews for new photos
      const newPreviews = acceptedFiles.map(file => URL.createObjectURL(file));
      setPhotosPreviews(prev => [...prev, ...newPreviews].slice(0, 10));
    },
  });

  const removePhoto = (index: number) => {
    // If it's an existing photo (from product.photos)
    if (index < (product?.photos?.length || 0)) {
      setPhotosPreviews(prev => prev.filter((_, i) => i !== index));
    }
    // If it's a new photo
    else {
      const newPhotosIndex = index - (product?.photos?.length || 0);
      setPhotos(prev => prev.filter((_, i) => i !== newPhotosIndex));
      setPhotosPreviews(prev => prev.filter((_, i) => i !== index));
    }
  };

  // No useEffect needed as we handle redirection directly in onSubmit

  const onSubmit = async (data: ProductFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate photos
      if (photosPreviews.length === 0) {
        setError("At least one photo is required");
        setIsSubmitting(false);
        return;
      }

      // Convert price from dollars to cents
      const priceInCents = Math.round(parseFloat(data.price) * 100);

      // Prepare photo URLs array
      let photoUrls: string[] = [];

      // Keep existing photos
      if (product?.photos) {
        const existingPhotoCount = Math.min(
          product.photos.length,
          photosPreviews.length
        );
        photoUrls = photosPreviews.slice(0, existingPhotoCount);
      }

      // Upload new photos
      if (photos.length > 0) {
        try {
          const uploadedPhotoUrls = await uploadMultipleToCloudinary(photos);
          photoUrls = [...photoUrls, ...uploadedPhotoUrls];
        } catch (uploadError) {
          console.error("Error uploading photos:", uploadError);
          setError("Error uploading photos. Please try again.");
          setIsSubmitting(false);
          return;
        }
      }

      // Create or update product
      const endpoint = isEditing
        ? `/api/marketplace/products/${product?.id}`
        : "/api/marketplace/products";

      const method = isEditing ? "PUT" : "POST";

      try {
        // Log what we're sending to the API
        const requestData = {
          title: data.title,
          description: data.description || null,
          price: priceInCents,
          condition: data.condition,
          category: data.category,
          location: data.location || null,
          photos: photoUrls,
          storeId,
        };
        console.log("Sending to API:", requestData);

        const response = await fetch(endpoint, {
          method,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestData),
        });

        const result = await response.json();
        console.log("API Response:", { status: response.status, result });

        if (!response.ok) {
          setError(result.message || `Failed to ${isEditing ? "update" : "create"} product`);
          setIsSubmitting(false);
          return;
        }

        // Show success message and immediately redirect
        setSuccess(`Product ${isEditing ? "updated" : "created"} successfully!`);

        // Immediately redirect to dashboard
        router.push("/my-store/dashboard");
        router.refresh();

      } catch (apiError) {
        console.error("API request error:", apiError);
        setError("Error communicating with the server. Please try again.");
      }
    } catch (err) {
      console.error(`Unexpected error ${isEditing ? "updating" : "creating"} product:`, err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const conditionOptions = [
    { value: "new", label: "New" },
    { value: "like_new", label: "Like New" },
    { value: "good", label: "Good" },
    { value: "fair", label: "Fair" },
    { value: "poor", label: "Poor" },
  ];

  const categoryOptions = [
    "Electronics",
    "Clothing & Accessories",
    "Home & Garden",
    "Toys & Games",
    "Sports & Outdoors",
    "Vehicles & Automotive",
    "Books & Media",
    "Collectibles",
    "Health & Beauty",
    "Furniture",
    "Other",
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      {success && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
            <div className="text-sm text-green-700">{success}</div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div className="sm:col-span-2">
          <Input
            label="Product Title *"
            placeholder="Enter product title"
            error={errors.title?.message}
            {...register("title")}
          />
        </div>

        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Product Description
          </label>
          <textarea
            className="w-full rounded-md border border-gray-300 px-3 py-2 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
            placeholder="Describe your product in detail"
            {...register("description")}
          ></textarea>
          {errors.description && (
            <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
          )}
        </div>

        <div>
          <Input
            label="Price *"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            error={errors.price?.message}
            {...register("price")}
          />
          <p className="mt-1 text-xs text-gray-500">
            Enter the price in dollars (e.g., 19.99)
          </p>
        </div>

        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Condition *
          </label>
          <select
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            {...register("condition")}
          >
            <option value="">Select condition</option>
            {conditionOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {errors.condition && (
            <p className="mt-1 text-sm text-red-500">{errors.condition.message}</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Category *
          </label>
          <select
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            {...register("category")}
          >
            <option value="">Select category</option>
            {categoryOptions.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
          {errors.category && (
            <p className="mt-1 text-sm text-red-500">{errors.category.message}</p>
          )}
        </div>

        <div>
          <Input
            label="Location"
            placeholder="City, State, Country"
            error={errors.location?.message}
            {...register("location")}
          />
        </div>

        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Product Photos *
          </label>

          {/* Photo previews */}
          {photosPreviews.length > 0 && (
            <div className="mb-4 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
              {photosPreviews.map((preview, index) => (
                <div key={index} className="relative h-32 overflow-hidden rounded-md border border-gray-200">
                  <img
                    src={preview}
                    alt={`Product photo ${index + 1}`}
                    className="h-full w-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={() => removePhoto(index)}
                    className="absolute right-1 top-1 rounded-full bg-white p-1 shadow hover:bg-gray-100"
                  >
                    <XMarkIcon className="h-4 w-4 text-gray-600" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Dropzone */}
          {photosPreviews.length < 10 && (
            <div
              {...getRootProps()}
              className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pb-6 pt-5"
            >
              <div className="space-y-1 text-center">
                <input {...getInputProps()} />
                <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="text-sm text-gray-600">
                  <span className="font-medium text-blue-600 hover:text-blue-500">
                    Upload photos
                  </span>{" "}
                  or drag and drop
                </div>
                <p className="text-xs text-gray-500">
                  PNG, JPG, GIF up to 5MB each (max 10 photos)
                </p>
              </div>
            </div>
          )}

          {photosPreviews.length === 0 && (
            <p className="mt-1 text-sm text-red-500">
              At least one photo is required
            </p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button type="submit" isLoading={isSubmitting}>
          {isEditing ? "Update Product" : "Create Product"}
        </Button>
      </div>
    </form>
  );
}
