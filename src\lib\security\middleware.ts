import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security headers configuration
export const securityHeaders = {
  // Prevent clickjacking attacks
  'X-Frame-Options': 'DENY',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // XSS Protection
  'X-XSS-Protection': '1; mode=block',
  
  // Referrer Policy
  'Referrer-Policy': 'origin-when-cross-origin',
  
  // Permissions Policy (Feature Policy)
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()',
  
  // Content Security Policy
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live https://va.vercel-scripts.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com data:",
    "img-src 'self' data: blob: https: http:",
    "media-src 'self' data: blob:",
    "connect-src 'self' https: wss:",
    "frame-src 'self' https:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),
  
  // HSTS (HTTP Strict Transport Security)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Prevent DNS prefetching
  'X-DNS-Prefetch-Control': 'off',
  
  // Disable download of untrusted content
  'X-Download-Options': 'noopen',
  
  // Prevent IE from executing downloads in site context
  'X-Permitted-Cross-Domain-Policies': 'none',
};

// Rate limiting configuration
interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

const rateLimitConfigs: Record<string, RateLimitConfig> = {
  // General API rate limiting (increased for development)
  '/api/': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // Increased from 100 to 1000 for development
    message: 'Too many requests from this IP, please try again later.',
  },

  // Session endpoint (very lenient for frequent checks)
  '/api/auth/session': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 500, // Increased for frequent session checks
    message: 'Too many session requests, please try again later.',
  },

  // Authentication endpoints (more lenient for session checks)
  '/api/auth/': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 200, // Increased from 50 to 200 for development
    message: 'Too many authentication attempts, please try again later.',
  },
  
  // User registration/login
  '/api/auth/register': {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5,
    message: 'Too many registration attempts, please try again later.',
  },
  
  // Password reset
  '/api/auth/forgot-password': {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many password reset attempts, please try again later.',
  },
  
  // File uploads
  '/api/upload': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20,
    message: 'Too many upload attempts, please try again later.',
  },
  
  // Admin endpoints
  '/api/admin/': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 50,
    message: 'Too many admin requests, please try again later.',
  },
};

/**
 * Rate limiting middleware
 */
export function rateLimit(request: NextRequest): NextResponse | null {
  // Skip rate limiting in development environment
  if (process.env.NODE_ENV === 'development') {
    return null;
  }

  const ip = getClientIP(request);
  const pathname = request.nextUrl.pathname;

  // Find matching rate limit config
  let config: RateLimitConfig | null = null;
  let matchedPath = '';

  for (const [path, cfg] of Object.entries(rateLimitConfigs)) {
    if (pathname.startsWith(path) && path.length > matchedPath.length) {
      config = cfg;
      matchedPath = path;
    }
  }

  if (!config) return null;
  
  const key = `${ip}:${matchedPath}`;
  const now = Date.now();
  const windowStart = now - config.windowMs;
  
  // Get or create rate limit entry
  let entry = rateLimitStore.get(key);
  
  if (!entry || entry.resetTime <= now) {
    entry = { count: 0, resetTime: now + config.windowMs };
    rateLimitStore.set(key, entry);
  }
  
  // Clean up old entries periodically
  if (Math.random() < 0.01) { // 1% chance
    cleanupRateLimitStore();
  }
  
  entry.count++;
  
  if (entry.count > config.maxRequests) {
    return new NextResponse(
      JSON.stringify({
        error: config.message || 'Rate limit exceeded',
        retryAfter: Math.ceil((entry.resetTime - now) / 1000),
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': Math.ceil((entry.resetTime - now) / 1000).toString(),
          'X-RateLimit-Limit': config.maxRequests.toString(),
          'X-RateLimit-Remaining': Math.max(0, config.maxRequests - entry.count).toString(),
          'X-RateLimit-Reset': entry.resetTime.toString(),
        },
      }
    );
  }
  
  return null;
}

/**
 * CSRF Protection middleware
 */
export async function csrfProtection(request: NextRequest): Promise<NextResponse | null> {
  // Skip CSRF for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
    return null;
  }

  // Skip CSRF for API routes that don't need it (like webhooks)
  const pathname = request.nextUrl.pathname;
  const skipCSRFPaths = [
    '/api/webhooks/',
    '/api/auth/callback/',
    '/api/auth/register',
    '/api/auth/signin',
    '/api/auth/session',
    '/api/auth/providers',
    '/api/auth/csrf',
  ];

  // Check for exact matches first
  if (pathname === '/api/auth/register' || pathname === '/api/auth/signin') {
    return null;
  }

  if (skipCSRFPaths.some(path => pathname.startsWith(path))) {
    return null;
  }
  
  // Check for CSRF token in headers
  const csrfToken = request.headers.get('x-csrf-token') || 
                   request.headers.get('x-xsrf-token');
  
  // For public auth endpoints, skip token validation
  const publicAuthPaths = ['/api/auth/register', '/api/auth/signin', '/api/auth/session', '/api/auth/providers'];
  const isPublicAuth = publicAuthPaths.includes(pathname);

  if (!isPublicAuth) {
    // Get token from session for protected routes
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    if (!token) {
      return new NextResponse(
        JSON.stringify({ error: 'Authentication required' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }
  }

  // For now, we'll use a simple CSRF check
  // In production, implement proper CSRF token validation
  const origin = request.headers.get('origin');
  const host = request.headers.get('host');

  // Skip CSRF check for same-origin requests or when origin is not set
  if (origin && host && !origin.includes(host)) {
    // Allow localhost and development origins
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://localhost:3000',
      'https://localhost:3001'
    ];

    const isAllowedOrigin = allowedOrigins.some(allowed => origin.startsWith(allowed));

    if (!isAllowedOrigin) {
      return new NextResponse(
        JSON.stringify({ error: 'CSRF token validation failed' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }
  }
  
  return null;
}

/**
 * Security headers middleware
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Input validation middleware
 */
export function validateRequest(request: NextRequest): NextResponse | null {
  const pathname = request.nextUrl.pathname;
  
  // Check for common attack patterns
  const suspiciousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /onload\s*=/gi,
    /onerror\s*=/gi,
    /onclick\s*=/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi,
    /url\s*\(/gi,
    /import\s*\(/gi,
  ];
  
  // Check URL for suspicious patterns
  const url = request.url;
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(url)) {
      return new NextResponse(
        JSON.stringify({ error: 'Invalid request detected' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
  }
  
  // Check for SQL injection patterns in query parameters and suspicious contexts
  // Only check query parameters and fragments, not the path itself
  const urlObj = new URL(url);
  const queryString = urlObj.search + urlObj.hash;

  const sqlPatterns = [
    // SQL keywords in suspicious contexts (with spaces/operators around them)
    /[\s\+\-\*\/\(\)\=\<\>\!\&\|]+(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)[\s\+\-\*\/\(\)\=\<\>\!\&\|]+/gi,
    // SQL comment patterns
    /(--|\/\*|\*\/)/g,
    // Classic SQL injection patterns
    /(\b(OR|AND)\b\s+\d+\s*=\s*\d+)/gi,
    // Union-based injection
    /union[\s\+]+select/gi,
    // Boolean-based blind injection
    /(\b(OR|AND)\b\s+['"]\w+['"]?\s*=\s*['"]\w+['"]?)/gi,
  ];

  for (const pattern of sqlPatterns) {
    if (pattern.test(queryString)) {
      return new NextResponse(
        JSON.stringify({ error: 'SQL injection attempt detected' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
  }
  
  return null;
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();
  
  return request.ip || 'unknown';
}

/**
 * Clean up old rate limit entries
 */
function cleanupRateLimitStore(): void {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime <= now) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Check if request is from admin
 */
export async function isAdminRequest(request: NextRequest): Promise<boolean> {
  try {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });
    
    // In production, check user role from database
    return token?.email === process.env.ADMIN_EMAIL || false;
  } catch {
    return false;
  }
}
