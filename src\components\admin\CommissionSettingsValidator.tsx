"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  PlayIcon,
  ClipboardDocumentListIcon
} from "@heroicons/react/24/outline";

interface ValidationResult {
  isValid: boolean;
  warnings: string[];
  errors: string[];
  suggestions: string[];
}

interface TestScenario {
  planPrice: string;
  commissionType: 'fixed' | 'percentage';
  commissionValue: string;
  maxCommissionAmount?: string;
  expectedResult: number;
}

interface CommissionSettingsValidatorProps {
  settings: any;
  packageSettings: any[];
  subscriptionPlans: any[];
}

export function CommissionSettingsValidator({ 
  settings, 
  packageSettings, 
  subscriptionPlans 
}: CommissionSettingsValidatorProps) {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [testScenarios, setTestScenarios] = useState<TestScenario[]>([
    {
      planPrice: '29.99',
      commissionType: 'percentage',
      commissionValue: '10',
      expectedResult: 2.99
    }
  ]);
  const [testing, setTesting] = useState(false);
  const [validating, setValidating] = useState(false);

  const validateSettings = () => {
    setValidating(true);
    
    const warnings: string[] = [];
    const errors: string[] = [];
    const suggestions: string[] = [];

    try {
      // Validate global settings
      if (!settings) {
        errors.push("Global commission settings are missing");
        setValidationResult({ isValid: false, warnings, errors, suggestions });
        return;
      }

      // Check if system is enabled but no commission types are enabled
      if (settings.isEnabled && !settings.firstPurchaseEnabled && !settings.recurringPurchaseEnabled) {
        errors.push("Commission system is enabled but no commission types are active");
      }

      // Check commission values
      if (settings.defaultCommissionType === 'percentage') {
        const value = parseFloat(settings.defaultCommissionValue);
        if (value > 50) {
          warnings.push(`Default commission rate (${value}%) is quite high - consider if this is sustainable`);
        }
        if (value > 100) {
          errors.push("Percentage commission cannot exceed 100%");
        }
      }

      // Check max commission amount
      const maxCommission = parseFloat(settings.maxCommissionAmount);
      if (maxCommission > 1000) {
        warnings.push(`Maximum commission amount ($${maxCommission}) is very high`);
      }

      // Check eligibility period
      if (settings.eligibilityPeriodDays < 7) {
        warnings.push("Short eligibility period may reduce referral effectiveness");
      }
      if (settings.eligibilityPeriodDays > 365) {
        warnings.push("Very long eligibility period may impact tracking accuracy");
      }

      // Validate package settings
      packageSettings.forEach((pkg, index) => {
        const plan = subscriptionPlans.find(p => p.id === pkg.planId);
        
        if (!plan) {
          errors.push(`Package setting ${index + 1}: Referenced plan not found`);
          return;
        }

        if (pkg.commissionType === 'percentage') {
          const firstPurchase = parseFloat(pkg.firstPurchaseCommission);
          const recurring = parseFloat(pkg.recurringCommission);
          
          if (firstPurchase > 100) {
            errors.push(`${pkg.planName}: First purchase commission percentage cannot exceed 100%`);
          }
          if (recurring > 100) {
            errors.push(`${pkg.planName}: Recurring commission percentage cannot exceed 100%`);
          }

          // Calculate estimated commission
          const planPrice = parseFloat(plan.price);
          const estimatedCommission = (planPrice * firstPurchase) / 100;
          
          if (pkg.maxCommissionAmount) {
            const maxAmount = parseFloat(pkg.maxCommissionAmount);
            if (estimatedCommission > maxAmount) {
              warnings.push(`${pkg.planName}: Calculated commission ($${estimatedCommission.toFixed(2)}) exceeds max amount ($${maxAmount})`);
            }
          }

          if (estimatedCommission > planPrice * 0.5) {
            warnings.push(`${pkg.planName}: Commission (${firstPurchase}%) is more than 50% of plan price`);
          }
        }

        if (pkg.commissionType === 'fixed') {
          const commission = parseFloat(pkg.firstPurchaseCommission);
          const planPrice = parseFloat(plan.price);
          
          if (commission > planPrice) {
            errors.push(`${pkg.planName}: Fixed commission ($${commission}) exceeds plan price ($${planPrice})`);
          }
          
          if (commission > planPrice * 0.8) {
            warnings.push(`${pkg.planName}: Fixed commission is more than 80% of plan price`);
          }
        }
      });

      // Check for duplicate plan settings
      const planIds = packageSettings.map(pkg => pkg.planId);
      const duplicates = planIds.filter((id, index) => planIds.indexOf(id) !== index);
      if (duplicates.length > 0) {
        errors.push("Duplicate package settings found for the same plans");
      }

      // Suggestions
      if (packageSettings.length === 0) {
        suggestions.push("Consider adding package-specific settings for better commission control");
      }

      if (settings.recurringPurchaseEnabled && packageSettings.some(pkg => parseFloat(pkg.recurringCommission) === 0)) {
        suggestions.push("Some packages have 0% recurring commission - consider if this is intentional");
      }

      if (!settings.requireActiveReferrer) {
        suggestions.push("Consider requiring active referrers to prevent abuse");
      }

      const isValid = errors.length === 0;
      setValidationResult({ isValid, warnings, errors, suggestions });

    } catch (error) {
      console.error('Validation error:', error);
      setValidationResult({
        isValid: false,
        warnings: [],
        errors: ['Validation failed due to an unexpected error'],
        suggestions: []
      });
    } finally {
      setValidating(false);
    }
  };

  const runCommissionTests = () => {
    setTesting(true);
    
    try {
      const results = testScenarios.map((scenario, index) => {
        const planPrice = parseFloat(scenario.planPrice);
        const commissionValue = parseFloat(scenario.commissionValue);
        
        let calculatedCommission = 0;
        
        if (scenario.commissionType === 'percentage') {
          calculatedCommission = (planPrice * commissionValue) / 100;
        } else {
          calculatedCommission = commissionValue;
        }
        
        if (scenario.maxCommissionAmount) {
          const maxAmount = parseFloat(scenario.maxCommissionAmount);
          calculatedCommission = Math.min(calculatedCommission, maxAmount);
        }
        
        const isCorrect = Math.abs(calculatedCommission - scenario.expectedResult) < 0.01;
        
        return {
          scenario: index + 1,
          expected: scenario.expectedResult,
          calculated: calculatedCommission,
          isCorrect,
          difference: Math.abs(calculatedCommission - scenario.expectedResult)
        };
      });
      
      const allPassed = results.every(r => r.isCorrect);
      
      if (allPassed) {
        toast.success('All commission calculation tests passed!');
      } else {
        toast.error('Some commission calculation tests failed');
      }
      
      console.log('Test Results:', results);
      
    } catch (error) {
      console.error('Testing error:', error);
      toast.error('Commission testing failed');
    } finally {
      setTesting(false);
    }
  };

  const addTestScenario = () => {
    setTestScenarios([
      ...testScenarios,
      {
        planPrice: '19.99',
        commissionType: 'percentage',
        commissionValue: '15',
        expectedResult: 3.00
      }
    ]);
  };

  const removeTestScenario = (index: number) => {
    setTestScenarios(testScenarios.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      {/* Validation Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ClipboardDocumentListIcon className="h-6 w-6 mr-2 text-blue-600" />
            Settings Validation
          </CardTitle>
          <CardDescription>
            Validate commission settings for potential issues and optimization opportunities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={validateSettings}
            disabled={validating}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {validating ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Validating...
              </>
            ) : (
              <>
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Validate Settings
              </>
            )}
          </Button>

          {validationResult && (
            <div className="space-y-4">
              {/* Overall Status */}
              <div className={`p-4 rounded-lg border ${
                validationResult.isValid 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center">
                  {validationResult.isValid ? (
                    <CheckCircleIcon className="h-6 w-6 text-green-600 mr-2" />
                  ) : (
                    <XCircleIcon className="h-6 w-6 text-red-600 mr-2" />
                  )}
                  <span className={`font-medium ${
                    validationResult.isValid ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {validationResult.isValid ? 'Settings are valid' : 'Settings have issues'}
                  </span>
                </div>
              </div>

              {/* Errors */}
              {validationResult.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-800 mb-2 flex items-center">
                    <XCircleIcon className="h-5 w-5 mr-1" />
                    Errors ({validationResult.errors.length})
                  </h4>
                  <ul className="space-y-1">
                    {validationResult.errors.map((error, index) => (
                      <li key={index} className="text-sm text-red-700">• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Warnings */}
              {validationResult.warnings.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-800 mb-2 flex items-center">
                    <ExclamationTriangleIcon className="h-5 w-5 mr-1" />
                    Warnings ({validationResult.warnings.length})
                  </h4>
                  <ul className="space-y-1">
                    {validationResult.warnings.map((warning, index) => (
                      <li key={index} className="text-sm text-yellow-700">• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Suggestions */}
              {validationResult.suggestions.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-800 mb-2">
                    Suggestions ({validationResult.suggestions.length})
                  </h4>
                  <ul className="space-y-1">
                    {validationResult.suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm text-blue-700">• {suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Commission Testing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <PlayIcon className="h-6 w-6 mr-2 text-green-600" />
            Commission Calculator Testing
          </CardTitle>
          <CardDescription>
            Test commission calculations with different scenarios
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <Button
              onClick={runCommissionTests}
              disabled={testing}
              className="bg-green-600 hover:bg-green-700"
            >
              {testing ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Testing...
                </>
              ) : (
                <>
                  <PlayIcon className="h-4 w-4 mr-2" />
                  Run Tests
                </>
              )}
            </Button>
            <Button
              onClick={addTestScenario}
              variant="outline"
              size="sm"
            >
              Add Test Scenario
            </Button>
          </div>

          <div className="space-y-3">
            {testScenarios.map((scenario, index) => (
              <div key={index} className="grid grid-cols-6 gap-3 p-3 bg-gray-50 rounded-lg">
                <div>
                  <Label className="text-xs">Plan Price</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={scenario.planPrice}
                    onChange={(e) => {
                      const updated = [...testScenarios];
                      updated[index].planPrice = e.target.value;
                      setTestScenarios(updated);
                    }}
                    className="h-8 text-sm"
                  />
                </div>
                <div>
                  <Label className="text-xs">Type</Label>
                  <select
                    value={scenario.commissionType}
                    onChange={(e) => {
                      const updated = [...testScenarios];
                      updated[index].commissionType = e.target.value as 'fixed' | 'percentage';
                      setTestScenarios(updated);
                    }}
                    className="h-8 text-sm w-full rounded border-gray-300"
                  >
                    <option value="percentage">%</option>
                    <option value="fixed">$</option>
                  </select>
                </div>
                <div>
                  <Label className="text-xs">Value</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={scenario.commissionValue}
                    onChange={(e) => {
                      const updated = [...testScenarios];
                      updated[index].commissionValue = e.target.value;
                      setTestScenarios(updated);
                    }}
                    className="h-8 text-sm"
                  />
                </div>
                <div>
                  <Label className="text-xs">Max Amount</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={scenario.maxCommissionAmount || ''}
                    onChange={(e) => {
                      const updated = [...testScenarios];
                      updated[index].maxCommissionAmount = e.target.value;
                      setTestScenarios(updated);
                    }}
                    className="h-8 text-sm"
                    placeholder="Optional"
                  />
                </div>
                <div>
                  <Label className="text-xs">Expected</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={scenario.expectedResult}
                    onChange={(e) => {
                      const updated = [...testScenarios];
                      updated[index].expectedResult = parseFloat(e.target.value) || 0;
                      setTestScenarios(updated);
                    }}
                    className="h-8 text-sm"
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={() => removeTestScenario(index)}
                    variant="outline"
                    size="sm"
                    className="h-8 text-red-600 border-red-600 hover:bg-red-50"
                  >
                    Remove
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
