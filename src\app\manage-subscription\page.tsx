"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { SubscriptionDashboard } from "@/components/subscription/SubscriptionDashboard";
import { PlanComparison } from "@/components/subscription/PlanComparison";
import { BillingHistory } from "@/components/subscription/BillingHistory";
import { PaymentForm } from "@/components/subscription/PaymentForm";
import { Button } from "@/components/ui/Button";
import {
  ArrowLeftIcon,
  CreditCardIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

type ViewMode = 'dashboard' | 'plans' | 'billing' | 'payment';

interface PaymentData {
  subscriptionId: string;
  planName: string;
  amount: string;
  currency: string;
}

function ManageSubscriptionContent() {
  const [currentView, setCurrentView] = useState<ViewMode>('dashboard');
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [loading, setLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const searchParams = useSearchParams();

  // Check for payment status in URL parameters
  useEffect(() => {
    const paymentStatus = searchParams.get('payment');
    if (paymentStatus === 'success') {
      toast.success('Payment completed successfully! Your subscription is now active.');
      setCurrentView('dashboard');
      setRefreshKey(prev => prev + 1);
      // Clean up URL
      window.history.replaceState({}, '', '/manage-subscription');
    } else if (paymentStatus === 'cancelled') {
      toast.error('Payment was cancelled. Please try again.');
      setCurrentView('dashboard');
      // Clean up URL
      window.history.replaceState({}, '', '/manage-subscription');
    }
  }, [searchParams]);

  const handleUpgrade = () => {
    setCurrentView('plans');
  };

  const handleSelectPlan = async (planId: string) => {
    if (loading) return; // Prevent multiple simultaneous requests

    setLoading(true);
    try {
      // First, get the plan details
      const plansResponse = await fetch('/api/subscription-plans');
      const plansResult = await plansResponse.json();

      if (!plansResult.success) {
        toast.error('Failed to fetch plan details');
        return;
      }

      const selectedPlan = plansResult.data.find((plan: any) => plan.id === planId);
      if (!selectedPlan) {
        toast.error('Selected plan not found');
        return;
      }

      // Check if it's a free plan
      if (parseFloat(selectedPlan.price) === 0) {
        // For free plan, create subscription directly without payment
        const subscriptionResponse = await fetch('/api/user-subscription', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            planId,
            billingCycle: selectedPlan.billingCycle,
            skipPayment: true, // Flag to indicate no payment needed
          }),
        });

        const subscriptionResult = await subscriptionResponse.json();

        if (subscriptionResult.success) {
          toast.success(`Successfully switched to ${selectedPlan.displayName} plan`);
          setCurrentView('dashboard');
          // Refresh dashboard data
          setRefreshKey(prev => prev + 1);
        } else {
          toast.error(subscriptionResult.message || 'Failed to switch to free plan');
        }
        return;
      }

      // Create or upgrade subscription
      const subscriptionResponse = await fetch('/api/user-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId,
          billingCycle: selectedPlan.billingCycle,
        }),
      });

      const subscriptionResult = await subscriptionResponse.json();

      if (subscriptionResult.success) {
        // Set up payment data and show payment form
        setPaymentData({
          subscriptionId: subscriptionResult.data.subscriptionId,
          planName: selectedPlan.displayName,
          amount: selectedPlan.price,
          currency: selectedPlan.currency,
        });
        setCurrentView('payment');
      } else {
        // If user already has subscription, try upgrade instead
        if (subscriptionResult.message?.includes('already has an active subscription')) {
          const upgradeResponse = await fetch('/api/user-subscription/upgrade', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              newPlanId: planId,
            }),
          });

          const upgradeResult = await upgradeResponse.json();

          if (upgradeResult.success) {
            setPaymentData({
              subscriptionId: upgradeResult.data.subscriptionId,
              planName: selectedPlan.displayName,
              amount: selectedPlan.price,
              currency: selectedPlan.currency,
            });
            setCurrentView('payment');
          } else {
            toast.error(upgradeResult.message || 'Failed to upgrade subscription');
          }
        } else {
          toast.error(subscriptionResult.message || 'Failed to create subscription');
        }
      }
    } catch (error) {
      console.error('Error selecting plan:', error);
      toast.error('Failed to process plan selection');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (data: any) => {
    toast.success('Payment successful! Your subscription is now active.');
    setCurrentView('dashboard');
    setPaymentData(null);
    setRefreshKey(prev => prev + 1); // Refresh dashboard data
  };

  const handlePaymentError = (error: string) => {
    toast.error(error);
  };

  const handleCancel = () => {
    setCurrentView('dashboard');
    setPaymentData(null);
  };

  const renderNavigation = () => (
    <div className="mb-8">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-1">
        <nav className="flex space-x-1" aria-label="Tabs">
          <button
            onClick={() => setCurrentView('dashboard')}
            className={`flex items-center px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 ${
              currentView === 'dashboard'
                ? 'bg-blue-600 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <Cog6ToothIcon className="h-5 w-5 mr-2" />
            Dashboard
          </button>
          <button
            onClick={() => setCurrentView('plans')}
            className={`flex items-center px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 ${
              currentView === 'plans'
                ? 'bg-blue-600 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <ChartBarIcon className="h-5 w-5 mr-2" />
            Plans
          </button>
          <button
            onClick={() => setCurrentView('billing')}
            className={`flex items-center px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 ${
              currentView === 'billing'
                ? 'bg-blue-600 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <CreditCardIcon className="h-5 w-5 mr-2" />
            Billing
          </button>
        </nav>
      </div>
    </div>
  );

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Subscription Management
                </h1>
                <p className="text-gray-600">
                  Manage your subscription plans, billing, and payment methods
                </p>
              </div>
              {currentView === 'payment' && (
                <Button
                  onClick={() => setCurrentView('plans')}
                  variant="outline"
                  className="flex items-center bg-white shadow-sm hover:shadow-md transition-shadow"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back to Plans
                </Button>
              )}
            </div>
          </div>

        {/* Navigation */}
        {currentView !== 'payment' && renderNavigation()}

          {/* Content based on current view */}
          <div className="transition-all duration-300 ease-in-out">
            {currentView === 'dashboard' && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <SubscriptionDashboard
                  key={refreshKey}
                  onUpgrade={handleUpgrade}
                  onCancel={() => {
                    // Refresh the dashboard after cancellation
                    setRefreshKey(prev => prev + 1);
                    toast.success('Subscription cancelled successfully');
                  }}
                />
              </div>
            )}

            {currentView === 'plans' && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <PlanComparison
                  onSelectPlan={handleSelectPlan}
                  showCurrentPlan={true}
                  loading={loading}
                />
              </div>
            )}

            {currentView === 'billing' && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <BillingHistory limit={20} />
              </div>
            )}

            {currentView === 'payment' && paymentData && (
              <div className="max-w-3xl mx-auto">
                <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                    <h2 className="text-xl font-semibold text-white flex items-center">
                      <CreditCardIcon className="h-6 w-6 mr-3" />
                      Complete Your Payment
                    </h2>
                    <p className="text-blue-100 text-sm mt-1">
                      Secure payment processing for your subscription
                    </p>
                  </div>
                  <div className="p-6">
                    <PaymentForm
                      subscriptionId={paymentData.subscriptionId}
                      planName={paymentData.planName}
                      amount={paymentData.amount}
                      currency={paymentData.currency}
                      onPaymentSuccess={handlePaymentSuccess}
                      onPaymentError={handlePaymentError}
                      onCancel={handleCancel}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

export default function ManageSubscriptionPage() {
  return (
    <Suspense fallback={
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    }>
      <ManageSubscriptionContent />
    </Suspense>
  );
}
