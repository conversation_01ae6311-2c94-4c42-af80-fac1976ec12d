import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptionPlans } from "@/lib/db/schema";
import { eq, gt, lt, desc, asc } from "drizzle-orm";
import { z } from "zod";

const sortSchema = z.object({
  direction: z.enum(['up', 'down']),
});

// PATCH - Update sort order
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { direction } = sortSchema.parse(body);

    // Get current plan
    const currentPlan = await db.query.subscriptionPlans.findFirst({
      where: eq(subscriptionPlans.id, params.id),
    });

    if (!currentPlan) {
      return NextResponse.json(
        { message: "Subscription plan not found" },
        { status: 404 }
      );
    }

    let targetPlan;

    if (direction === 'up') {
      // Find the plan with the next higher sort order
      targetPlan = await db.query.subscriptionPlans.findFirst({
        where: gt(subscriptionPlans.sortOrder, currentPlan.sortOrder),
        orderBy: [asc(subscriptionPlans.sortOrder)],
      });
    } else {
      // Find the plan with the next lower sort order
      targetPlan = await db.query.subscriptionPlans.findFirst({
        where: lt(subscriptionPlans.sortOrder, currentPlan.sortOrder),
        orderBy: [desc(subscriptionPlans.sortOrder)],
      });
    }

    if (!targetPlan) {
      return NextResponse.json(
        {
          success: false,
          message: `Cannot move plan ${direction}. Already at ${direction === 'up' ? 'top' : 'bottom'}.`,
        },
        { status: 400 }
      );
    }

    // Swap sort orders
    const currentSortOrder = currentPlan.sortOrder;
    const targetSortOrder = targetPlan.sortOrder;

    // Update current plan
    await db
      .update(subscriptionPlans)
      .set({ sortOrder: targetSortOrder })
      .where(eq(subscriptionPlans.id, currentPlan.id));

    // Update target plan
    await db
      .update(subscriptionPlans)
      .set({ sortOrder: currentSortOrder })
      .where(eq(subscriptionPlans.id, targetPlan.id));

    return NextResponse.json({
      success: true,
      message: `Plan moved ${direction} successfully`,
    });
  } catch (error) {
    console.error("Error updating sort order:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to update sort order",
      },
      { status: 500 }
    );
  }
}
