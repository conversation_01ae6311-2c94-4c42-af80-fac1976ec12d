"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  HashtagIcon
} from "@heroicons/react/24/outline";

interface SearchResult {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  author: {
    name: string;
    username?: string;
  };
  category?: {
    name: string;
    color: string;
  };
  readTime?: number;
}

interface BlogSearchProps {
  onSearch?: (query: string) => void;
  placeholder?: string;
  showSuggestions?: boolean;
  className?: string;
}

export function BlogSearch({
  onSearch,
  placeholder = "Search news/blog...",
  showSuggestions = true,
  className = ""
}: BlogSearchProps) {
  const [query, setQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [trendingTags, setTrendingTags] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Mock data
  useEffect(() => {
    setRecentSearches(["web development", "react", "design systems"]);
    setTrendingTags(["javascript", "typescript", "nextjs", "tailwind", "react", "nodejs"]);
  }, []);

  // Handle search
  useEffect(() => {
    if (query.length > 2) {
      setIsLoading(true);

      const searchBlogs = async () => {
        try {
          const response = await fetch(`/api/blogs/search?q=${encodeURIComponent(query)}&limit=5`);
          if (response.ok) {
            const data = await response.json();
            setResults(data);
          } else {
            console.error("Failed to search blogs");
            setResults([]);
          }
        } catch (error) {
          console.error("Error searching blogs:", error);
          setResults([]);
        } finally {
          setIsLoading(false);
        }
      };

      const timer = setTimeout(searchBlogs, 300);
      return () => clearTimeout(timer);
    } else {
      setResults([]);
      setIsLoading(false);
    }
  }, [query]);

  // Handle outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    onSearch?.(value);
    setIsOpen(true);
  };

  const handleClear = () => {
    setQuery("");
    setResults([]);
    onSearch?.("");
    inputRef.current?.focus();
  };

  const handleSearchSubmit = (searchQuery: string) => {
    if (searchQuery.trim()) {
      // Add to recent searches
      setRecentSearches(prev => {
        const updated = [searchQuery, ...prev.filter(s => s !== searchQuery)].slice(0, 5);
        return updated;
      });

      setQuery(searchQuery);
      onSearch?.(searchQuery);
      setIsOpen(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearchSubmit(query);
    } else if (e.key === "Escape") {
      setIsOpen(false);
    }
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {query && (
          <button
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Search Dropdown */}
      {isOpen && showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Loading State */}
          {isLoading && (
            <div className="p-4 text-center text-gray-500">
              <div className="inline-block animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-r-transparent"></div>
              <span className="ml-2">Searching...</span>
            </div>
          )}

          {/* Search Results */}
          {!isLoading && results.length > 0 && (
            <div className="p-2">
              <h3 className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                Search Results
              </h3>
              {results.map((result) => (
                <Link
                  key={result.id}
                  href={`/blogs/${result.slug}`}
                  onClick={() => setIsOpen(false)}
                  className="block px-3 py-3 hover:bg-gray-50 rounded-md transition-colors"
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 line-clamp-1">
                        {result.title}
                      </h4>
                      {result.excerpt && (
                        <p className="text-xs text-gray-600 line-clamp-2 mt-1">
                          {result.excerpt}
                        </p>
                      )}
                      <div className="flex items-center space-x-2 mt-2 text-xs text-gray-500">
                        <span>{result.author.name}</span>
                        {result.category && (
                          <>
                            <span>•</span>
                            <span
                              className="px-2 py-0.5 rounded-full text-white"
                              style={{ backgroundColor: result.category.color }}
                            >
                              {result.category.name}
                            </span>
                          </>
                        )}
                        {result.readTime && (
                          <>
                            <span>•</span>
                            <div className="flex items-center">
                              <ClockIcon className="h-3 w-3 mr-1" />
                              <span>{result.readTime} min</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}

          {/* No Results */}
          {!isLoading && query.length > 2 && results.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              <p>No news/blog found for "{query}"</p>
              <p className="text-xs mt-1">Try different keywords or browse categories</p>
            </div>
          )}

          {/* Recent Searches */}
          {!query && recentSearches.length > 0 && (
            <div className="p-2 border-b border-gray-100">
              <h3 className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                Recent Searches
              </h3>
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleSearchSubmit(search)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-md transition-colors text-sm text-gray-700"
                >
                  {search}
                </button>
              ))}
            </div>
          )}

          {/* Trending Tags */}
          {!query && trendingTags.length > 0 && (
            <div className="p-2">
              <h3 className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center">
                <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
                Trending Tags
              </h3>
              <div className="px-3 py-2">
                <div className="flex flex-wrap gap-2">
                  {trendingTags.map((tag, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearchSubmit(tag)}
                      className="inline-flex items-center px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs rounded-full transition-colors"
                    >
                      <HashtagIcon className="h-3 w-3 mr-1" />
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
