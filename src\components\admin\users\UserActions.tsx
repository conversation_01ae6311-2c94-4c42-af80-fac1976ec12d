"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Card } from "@/components/ui/Card";
import { Modal } from "@/components/ui/Modal";
import { toast } from "react-hot-toast";
import {
  KeyIcon,
  LockClosedIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  TrashIcon,
  UserIcon,
} from "@heroicons/react/24/outline";

interface User {
  id: string;
  name: string;
  email: string;
  isAdmin: boolean;
  wallet: {
    isActive: boolean;
  };
}

interface UserActionsProps {
  user: User;
  onUserUpdate: () => void;
}

export function UserActions({ user, onUserUpdate }: UserActionsProps) {
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [isPinModalOpen, setIsPinModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Password Reset State
  const [passwordData, setPasswordData] = useState({
    newPassword: "",
    confirmPassword: "",
  });

  // PIN Reset State
  const [pinData, setPinData] = useState({
    newPin: "",
    confirmPin: "",
  });

  const handlePasswordReset = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("Passwords don't match");
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error("Password must be at least 6 characters");
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/users/${user.id}/reset-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          newPassword: passwordData.newPassword,
          confirmPassword: passwordData.confirmPassword,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to reset password");
      }

      toast.success("Password reset successfully");
      setIsPasswordModalOpen(false);
      setPasswordData({ newPassword: "", confirmPassword: "" });
      onUserUpdate();
    } catch (error: any) {
      console.error("Error resetting password:", error);
      toast.error(error.message || "Failed to reset password");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePinReset = async () => {
    if (pinData.newPin !== pinData.confirmPin) {
      toast.error("PINs don't match");
      return;
    }

    if (pinData.newPin.length !== 4 || !/^\d{4}$/.test(pinData.newPin)) {
      toast.error("PIN must be exactly 4 digits");
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/users/${user.id}/reset-wallet-pin`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          newPin: pinData.newPin,
          confirmPin: pinData.confirmPin,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to reset wallet PIN");
      }

      toast.success("Wallet PIN reset successfully");
      setIsPinModalOpen(false);
      setPinData({ newPin: "", confirmPin: "" });
      onUserUpdate();
    } catch (error: any) {
      console.error("Error resetting PIN:", error);
      toast.error(error.message || "Failed to reset wallet PIN");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemovePIN = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/users/${user.id}/reset-wallet-pin`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to remove wallet PIN");
      }

      toast.success("Wallet PIN removed successfully");
      onUserUpdate();
    } catch (error: any) {
      console.error("Error removing PIN:", error);
      toast.error(error.message || "Failed to remove wallet PIN");
    } finally {
      setIsLoading(false);
    }
  };

  const actionCards = [
    {
      title: "Reset Password",
      description: "Reset user's login password",
      icon: LockClosedIcon,
      color: "blue",
      action: () => setIsPasswordModalOpen(true),
      buttonText: "Reset Password",
    },
    {
      title: "Reset Wallet PIN",
      description: "Reset user's wallet PIN for transactions",
      icon: KeyIcon,
      color: "green",
      action: () => setIsPinModalOpen(true),
      buttonText: "Reset PIN",
    },
    {
      title: "Remove Wallet PIN",
      description: "Remove wallet PIN completely",
      icon: ShieldCheckIcon,
      color: "orange",
      action: handleRemovePIN,
      buttonText: "Remove PIN",
      loading: isLoading,
    },
    {
      title: "Delete User Account",
      description: "Permanently delete this user account",
      icon: TrashIcon,
      color: "red",
      action: () => setIsDeleteModalOpen(true),
      buttonText: "Delete Account",
      dangerous: true,
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "border-blue-200 bg-blue-50",
      green: "border-green-200 bg-green-50",
      orange: "border-orange-200 bg-orange-50",
      red: "border-red-200 bg-red-50",
    } as const;

    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColor = (color: string) => {
    const colorMap = {
      blue: "text-blue-600",
      green: "text-green-600",
      orange: "text-orange-600",
      red: "text-red-600",
    } as const;

    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="space-y-6">
      {/* User Info */}
      <Card className="p-6">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
            <UserIcon className="h-6 w-6 text-gray-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
            <p className="text-sm text-gray-600">{user.email}</p>
            <div className="flex items-center space-x-2 mt-1">
              {user.isAdmin && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Admin
                </span>
              )}
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                user.wallet.isActive 
                  ? "bg-green-100 text-green-800" 
                  : "bg-gray-100 text-gray-800"
              }`}>
                Wallet {user.wallet.isActive ? "Active" : "Inactive"}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {/* Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {actionCards.map((card, index) => (
          <Card key={index} className={`p-6 border-l-4 ${getColorClasses(card.color)}`}>
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${getColorClasses(card.color)}`}>
                  <card.icon className={`h-6 w-6 ${getIconColor(card.color)}`} />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">{card.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{card.description}</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant={card.dangerous ? "danger" : "primary"}
                onClick={card.action}
                disabled={card.loading}
                className="w-full"
              >
                {card.loading ? "Processing..." : card.buttonText}
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Password Reset Modal */}
      <Modal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
        title="Reset User Password"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-2 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
            <p className="text-sm text-yellow-800">
              This will reset the user's password. They will need to use the new password to log in.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              New Password
            </label>
            <Input
              type="password"
              value={passwordData.newPassword}
              onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
              placeholder="Enter new password (min 6 characters)"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Confirm Password
            </label>
            <Input
              type="password"
              value={passwordData.confirmPassword}
              onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
              placeholder="Confirm new password"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsPasswordModalOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handlePasswordReset}
              disabled={isLoading || !passwordData.newPassword || !passwordData.confirmPassword}
              className="flex-1"
            >
              {isLoading ? "Resetting..." : "Reset Password"}
            </Button>
          </div>
        </div>
      </Modal>

      {/* PIN Reset Modal */}
      <Modal
        isOpen={isPinModalOpen}
        onClose={() => setIsPinModalOpen(false)}
        title="Reset Wallet PIN"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-2 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <KeyIcon className="h-5 w-5 text-blue-600" />
            <p className="text-sm text-blue-800">
              This will reset the user's wallet PIN. They will need the new PIN for wallet transactions.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              New PIN (4 digits)
            </label>
            <Input
              type="password"
              maxLength={4}
              value={pinData.newPin}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, '');
                setPinData(prev => ({ ...prev, newPin: value }));
              }}
              placeholder="Enter 4-digit PIN"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Confirm PIN
            </label>
            <Input
              type="password"
              maxLength={4}
              value={pinData.confirmPin}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, '');
                setPinData(prev => ({ ...prev, confirmPin: value }));
              }}
              placeholder="Confirm 4-digit PIN"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsPinModalOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handlePinReset}
              disabled={isLoading || pinData.newPin.length !== 4 || pinData.confirmPin.length !== 4}
              className="flex-1"
            >
              {isLoading ? "Resetting..." : "Reset PIN"}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete User Account"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
            <div>
              <p className="text-sm font-medium text-red-800">Warning: This action cannot be undone!</p>
              <p className="text-sm text-red-700 mt-1">
                This will permanently delete the user account and all associated data.
              </p>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">This will delete:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• User profile and personal information</li>
              <li>• All posts, comments, and interactions</li>
              <li>• Wallet and transaction history</li>
              <li>• Created groups, pages, and stores</li>
              <li>• All uploaded content and media</li>
            </ul>
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={() => {
                // Implement delete functionality
                toast.error("Delete functionality not implemented yet");
                setIsDeleteModalOpen(false);
              }}
              className="flex-1"
            >
              Delete Account
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
