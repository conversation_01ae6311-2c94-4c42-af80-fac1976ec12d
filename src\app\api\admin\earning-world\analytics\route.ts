import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { 
  walletTransactions, 
  wallets, 
  users, 
  referrals, 
  blogs,
  blogEarnings,
  blogMonetization 
} from "@/lib/db/schema";
import { eq, and, sql, desc, gte, lte, count, sum } from "drizzle-orm";

// Get earning world analytics
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Calculate date ranges
    const now = new Date();
    let startDate: Date;
    let previousStartDate: Date;

    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 730 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
    }

    // Overview metrics
    const totalEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, startDate)
        )
      );

    const previousEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, previousStartDate),
          lte(walletTransactions.createdAt, startDate)
        )
      );

    const totalUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.status, 'active'));

    const activeEarnersResult = await db
      .select({ count: count() })
      .from(wallets)
      .where(
        and(
          eq(wallets.type, 'earning'),
          sql`${wallets.balance} > 0`
        )
      );

    const totalPayoutsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'withdrawal'),
          eq(walletTransactions.walletType, 'earning'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, startDate)
        )
      );

    // Top performers
    const topEarnersResult = await db
      .select({
        userId: wallets.userId,
        totalEarnings: wallets.balance,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
        }
      })
      .from(wallets)
      .innerJoin(users, eq(wallets.userId, users.id))
      .where(eq(wallets.type, 'earning'))
      .orderBy(desc(wallets.balance))
      .limit(10);

    const topReferrersResult = await db
      .select({
        referrerId: referrals.referrerId,
        totalReferrals: count(),
        completedReferrals: count(sql`CASE WHEN ${referrals.status} = 'completed' THEN 1 END`),
        totalEarnings: sum(sql`CASE WHEN ${referrals.status} = 'completed' THEN ${referrals.rewardAmount} ELSE 0 END`),
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
        }
      })
      .from(referrals)
      .innerJoin(users, eq(referrals.referrerId, users.id))
      .where(gte(referrals.createdAt, startDate))
      .groupBy(referrals.referrerId, users.id, users.name, users.username)
      .orderBy(desc(count(sql`CASE WHEN ${referrals.status} = 'completed' THEN 1 END`)))
      .limit(10);

    const topBlogsResult = await db
      .select({
        blogId: blogEarnings.blogId,
        totalEarnings: sum(blogEarnings.earningAmount),
        totalReads: sum(blogEarnings.readCount),
        blog: {
          id: blogs.id,
          title: blogs.title,
          author: users.name,
        }
      })
      .from(blogEarnings)
      .innerJoin(blogs, eq(blogEarnings.blogId, blogs.id))
      .innerJoin(users, eq(blogs.authorId, users.id))
      .where(
        and(
          eq(blogEarnings.status, 'paid'),
          gte(blogEarnings.createdAt, startDate)
        )
      )
      .groupBy(blogEarnings.blogId, blogs.id, blogs.title, users.name)
      .orderBy(desc(sum(blogEarnings.earningAmount)))
      .limit(10);

    // Calculate metrics
    const totalEarnings = parseFloat(totalEarningsResult[0]?.total || '0');
    const previousEarnings = parseFloat(previousEarningsResult[0]?.total || '0');
    const totalUsers = totalUsersResult[0]?.count || 0;
    const activeEarners = activeEarnersResult[0]?.count || 0;
    const totalPayouts = parseFloat(totalPayoutsResult[0]?.total || '0');

    const growthRate = previousEarnings > 0 
      ? ((totalEarnings - previousEarnings) / previousEarnings) * 100 
      : 0;

    const conversionRate = totalUsers > 0 ? (activeEarners / totalUsers) * 100 : 0;

    // Additional metrics calculations
    const averageEarningPerUser = activeEarners > 0 ? totalEarnings / activeEarners : 0;
    const averageReferralReward = 5.00; // Default from settings
    const averageBlogEarning = topBlogsResult.length > 0 
      ? topBlogsResult.reduce((sum, blog) => sum + parseFloat(blog.totalEarnings || '0'), 0) / topBlogsResult.length 
      : 0;
    const payoutSuccessRate = 95.0; // Mock data - would need actual calculation
    const userRetentionRate = 80.0; // Mock data - would need actual calculation
    const referralConversionRate = 25.0; // Mock data - would need actual calculation

    const analytics = {
      overview: {
        totalEarnings,
        totalUsers,
        activeEarners,
        totalPayouts,
        growthRate,
        conversionRate,
      },
      trends: {
        earningsTrend: [], // Would be populated with daily data
        userGrowth: [], // Would be populated with daily data
      },
      performance: {
        topEarners: topEarnersResult.map(earner => ({
          id: earner.user.id,
          name: earner.user.name || 'Unknown User',
          username: earner.user.username || '',
          totalEarnings: parseFloat(earner.totalEarnings),
          earningType: 'Mixed'
        })),
        topReferrers: topReferrersResult.map(referrer => ({
          id: referrer.user.id,
          name: referrer.user.name || 'Unknown User',
          username: referrer.user.username || '',
          referrals: referrer.completedReferrals,
          earnings: parseFloat(referrer.totalEarnings || '0')
        })),
        topBlogs: topBlogsResult.map(blog => ({
          id: blog.blog.id,
          title: blog.blog.title || 'Untitled',
          author: blog.blog.author || 'Unknown Author',
          reads: blog.totalReads || 0,
          earnings: parseFloat(blog.totalEarnings || '0')
        }))
      },
      metrics: {
        averageEarningPerUser,
        averageReferralReward,
        averageBlogEarning,
        payoutSuccessRate,
        userRetentionRate,
        referralConversionRate,
      }
    };

    return NextResponse.json({
      success: true,
      data: analytics,
    });

  } catch (error) {
    console.error("Error fetching earning world analytics:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch earning world analytics"
      },
      { status: 500 }
    );
  }
}
