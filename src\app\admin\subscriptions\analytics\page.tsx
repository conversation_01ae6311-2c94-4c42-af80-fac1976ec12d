"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Card } from "@/components/ui/Card";
import { Select } from "@/components/ui/Select";
import { Spinner } from "@/components/ui/Spinner";
import { StatCard } from "@/components/admin/StatCard";
import { toast } from "react-hot-toast";
import {
  BanknotesIcon,
  UsersIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  CreditCardIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

interface AnalyticsData {
  overview: {
    totalRevenue: number;
    monthlyRevenue: number;
    totalSubscriptions: number;
    activeSubscriptions: number;
    churnRate: number;
    retentionRate: number;
    averageRevenuePerUser: number;
    conversionRate: number;
  };
  revenueByPlan: Array<{
    planName: string;
    revenue: number;
    subscriptions: number;
    percentage: number;
  }>;
  subscriptionGrowth: Array<{
    month: string;
    newSubscriptions: number;
    cancelledSubscriptions: number;
    netGrowth: number;
  }>;
  paymentMetrics: {
    successRate: number;
    failedPayments: number;
    totalTransactions: number;
    averageTransactionValue: number;
  };
  topPlans: Array<{
    planName: string;
    subscriptions: number;
    revenue: number;
  }>;
}

export default function SubscriptionAnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/subscriptions/analytics?range=${timeRange}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }
      
      const data = await response.json();
      setAnalyticsData(data.analytics);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!analyticsData) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No analytics data available</h3>
          <p className="mt-2 text-sm text-gray-500">
            Analytics data will appear here once you have subscription activity.
          </p>
        </div>
      </AdminLayout>
    );
  }

  const { overview, revenueByPlan, subscriptionGrowth, paymentMetrics, topPlans } = analyticsData;

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Analytics</h1>
          <p className="mt-1 text-sm text-gray-500">
            Track subscription performance and revenue metrics
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="w-40"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </Select>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <StatCard
          title="Total Revenue"
          value={formatCurrency(overview.totalRevenue)}
          icon={<BanknotesIcon className="h-6 w-6" />}
          color="text-green-600"
          bgColor="bg-green-50"
          change={{
            value: 12.5,
            isPositive: true,
          }}
        />
        <StatCard
          title="Active Subscriptions"
          value={overview.activeSubscriptions.toLocaleString()}
          icon={<UsersIcon className="h-6 w-6" />}
          color="text-blue-600"
          bgColor="bg-blue-50"
          change={{
            value: 8.2,
            isPositive: true,
          }}
        />
        <StatCard
          title="Monthly Revenue"
          value={formatCurrency(overview.monthlyRevenue)}
          icon={<ChartBarIcon className="h-6 w-6" />}
          color="text-purple-600"
          bgColor="bg-purple-50"
          change={{
            value: 15.3,
            isPositive: true,
          }}
        />
        <StatCard
          title="Churn Rate"
          value={formatPercentage(overview.churnRate)}
          icon={<ArrowTrendingDownIcon className="h-6 w-6" />}
          color="text-red-600"
          bgColor="bg-red-50"
          change={{
            value: 2.1,
            isPositive: false,
          }}
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <StatCard
          title="Retention Rate"
          value={formatPercentage(overview.retentionRate)}
          icon={<ArrowTrendingUpIcon className="h-6 w-6" />}
          color="text-green-600"
          bgColor="bg-green-50"
        />
        <StatCard
          title="Avg Revenue Per User"
          value={formatCurrency(overview.averageRevenuePerUser)}
          icon={<BanknotesIcon className="h-6 w-6" />}
          color="text-blue-600"
          bgColor="bg-blue-50"
        />
        <StatCard
          title="Conversion Rate"
          value={formatPercentage(overview.conversionRate)}
          icon={<ChartBarIcon className="h-6 w-6" />}
          color="text-purple-600"
          bgColor="bg-purple-50"
        />
        <StatCard
          title="Payment Success Rate"
          value={formatPercentage(paymentMetrics.successRate)}
          icon={<CheckCircleIcon className="h-6 w-6" />}
          color="text-green-600"
          bgColor="bg-green-50"
        />
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-8">
        {/* Revenue by Plan */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue by Plan</h3>
          <div className="space-y-4">
            {revenueByPlan.map((plan, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-900">{plan.planName}</span>
                    <span className="text-sm text-gray-500">{formatCurrency(plan.revenue)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${plan.percentage}%` }}
                    ></div>
                  </div>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs text-gray-500">{plan.subscriptions} subscriptions</span>
                    <span className="text-xs text-gray-500">{formatPercentage(plan.percentage)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Top Performing Plans */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Plans</h3>
          <div className="space-y-4">
            {topPlans.map((plan, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-blue-600">#{index + 1}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{plan.planName}</p>
                    <p className="text-xs text-gray-500">{plan.subscriptions} subscribers</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900">{formatCurrency(plan.revenue)}</p>
                  <p className="text-xs text-gray-500">revenue</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Subscription Growth Chart */}
      <Card className="p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription Growth</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 text-sm font-medium text-gray-500">Month</th>
                <th className="text-right py-2 text-sm font-medium text-gray-500">New</th>
                <th className="text-right py-2 text-sm font-medium text-gray-500">Cancelled</th>
                <th className="text-right py-2 text-sm font-medium text-gray-500">Net Growth</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {subscriptionGrowth.map((month, index) => (
                <tr key={index}>
                  <td className="py-3 text-sm text-gray-900">{month.month}</td>
                  <td className="py-3 text-sm text-right text-green-600">+{month.newSubscriptions}</td>
                  <td className="py-3 text-sm text-right text-red-600">-{month.cancelledSubscriptions}</td>
                  <td className={`py-3 text-sm text-right font-medium ${
                    month.netGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {month.netGrowth >= 0 ? '+' : ''}{month.netGrowth}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Payment Metrics */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Metrics</h3>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-2">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900">{formatPercentage(paymentMetrics.successRate)}</p>
            <p className="text-sm text-gray-500">Success Rate</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mx-auto mb-2">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900">{paymentMetrics.failedPayments}</p>
            <p className="text-sm text-gray-500">Failed Payments</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-2">
              <CreditCardIcon className="h-6 w-6 text-blue-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900">{paymentMetrics.totalTransactions}</p>
            <p className="text-sm text-gray-500">Total Transactions</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-2">
              <BanknotesIcon className="h-6 w-6 text-purple-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900">{formatCurrency(paymentMetrics.averageTransactionValue)}</p>
            <p className="text-sm text-gray-500">Avg Transaction</p>
          </div>
        </div>
      </Card>
    </AdminLayout>
  );
}
