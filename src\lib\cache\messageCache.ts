/**
 * Advanced Message Caching System for WhatsApp-style Performance
 * 
 * Features:
 * - Intelligent conversation preloading
 * - LRU cache with size limits
 * - Optimistic updates
 * - Background sync
 * - Memory-efficient storage
 */

interface CachedMessage {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: number;
  status: 'optimistic' | 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  type: 'text' | 'image' | 'file';
  tempId?: string;
  lastAccessed: number;
}

interface CachedConversation {
  id: string;
  participants: string[];
  messages: CachedMessage[];
  lastMessage?: CachedMessage;
  unreadCount: number;
  lastAccessed: number;
  isPreloaded: boolean;
  totalMessages: number;
  hasMoreMessages: boolean;
}

interface CacheStats {
  totalConversations: number;
  totalMessages: number;
  memoryUsage: number;
  hitRate: number;
  missRate: number;
}

class MessageCache {
  private conversations = new Map<string, CachedConversation>();
  private messageIndex = new Map<string, string>(); // messageId -> conversationId
  private accessOrder: string[] = []; // For LRU
  private maxConversations = 50;
  private maxMessagesPerConversation = 200;
  private preloadQueue: string[] = [];
  private isPreloading = false;
  
  // Performance tracking
  private stats = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
  };

  /**
   * Get conversation from cache
   */
  getConversation(conversationId: string): CachedConversation | null {
    this.stats.totalRequests++;
    
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      this.stats.hits++;
      this.updateAccessOrder(conversationId);
      conversation.lastAccessed = Date.now();
      return { ...conversation }; // Return copy to prevent mutations
    }
    
    this.stats.misses++;
    return null;
  }

  /**
   * Cache conversation with messages
   */
  setConversation(conversationId: string, conversation: Omit<CachedConversation, 'lastAccessed'>): void {
    const cachedConversation: CachedConversation = {
      ...conversation,
      lastAccessed: Date.now(),
    };

    // Limit messages per conversation
    if (cachedConversation.messages.length > this.maxMessagesPerConversation) {
      cachedConversation.messages = cachedConversation.messages
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, this.maxMessagesPerConversation);
    }

    this.conversations.set(conversationId, cachedConversation);
    this.updateAccessOrder(conversationId);
    
    // Update message index
    cachedConversation.messages.forEach(message => {
      this.messageIndex.set(message.id, conversationId);
    });

    // Enforce cache size limit
    this.enforceLimit();
  }

  /**
   * Add message to conversation cache
   */
  addMessage(conversationId: string, message: CachedMessage): void {
    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      // Create new conversation if it doesn't exist
      this.setConversation(conversationId, {
        id: conversationId,
        participants: [message.senderId, message.receiverId],
        messages: [message],
        lastMessage: message,
        unreadCount: 0,
        isPreloaded: false,
        totalMessages: 1,
        hasMoreMessages: false,
      });
      return;
    }

    // Add message to existing conversation
    const existingIndex = conversation.messages.findIndex(m => 
      m.id === message.id || m.tempId === message.tempId
    );

    if (existingIndex >= 0) {
      // Update existing message
      conversation.messages[existingIndex] = { ...message, lastAccessed: Date.now() };
    } else {
      // Add new message
      conversation.messages.push({ ...message, lastAccessed: Date.now() });
      conversation.totalMessages++;
    }

    // Sort messages by timestamp
    conversation.messages.sort((a, b) => a.timestamp - b.timestamp);
    
    // Update last message
    conversation.lastMessage = conversation.messages[conversation.messages.length - 1];
    conversation.lastAccessed = Date.now();
    
    // Update message index
    this.messageIndex.set(message.id, conversationId);
    
    this.updateAccessOrder(conversationId);
  }

  /**
   * Update message status (for optimistic updates)
   */
  updateMessageStatus(messageId: string, status: CachedMessage['status'], tempId?: string): boolean {
    // Try to find by messageId first
    let conversationId = this.messageIndex.get(messageId);
    
    // If not found, search by tempId
    if (!conversationId && tempId) {
      for (const [convId, conversation] of this.conversations) {
        const message = conversation.messages.find(m => m.tempId === tempId);
        if (message) {
          conversationId = convId;
          break;
        }
      }
    }

    if (!conversationId) return false;

    const conversation = this.conversations.get(conversationId);
    if (!conversation) return false;

    const messageIndex = conversation.messages.findIndex(m => 
      m.id === messageId || m.tempId === tempId
    );

    if (messageIndex >= 0) {
      conversation.messages[messageIndex].status = status;
      conversation.messages[messageIndex].lastAccessed = Date.now();
      
      // If this is the last message, update conversation's last message
      if (messageIndex === conversation.messages.length - 1) {
        conversation.lastMessage = conversation.messages[messageIndex];
      }
      
      return true;
    }

    return false;
  }

  /**
   * Get messages for a conversation with pagination
   */
  getMessages(conversationId: string, limit = 50, before?: number): CachedMessage[] {
    const conversation = this.getConversation(conversationId);
    if (!conversation) return [];

    let messages = conversation.messages;
    
    if (before) {
      messages = messages.filter(m => m.timestamp < before);
    }

    return messages
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit)
      .reverse(); // Return in chronological order
  }

  /**
   * Preload conversations for better performance
   */
  async preloadConversations(conversationIds: string[]): Promise<void> {
    if (this.isPreloading) return;
    
    this.isPreloading = true;
    this.preloadQueue = [...conversationIds];

    try {
      for (const conversationId of this.preloadQueue) {
        if (this.conversations.has(conversationId)) {
          const conversation = this.conversations.get(conversationId)!;
          conversation.isPreloaded = true;
          continue;
        }

        // Simulate API call to load conversation
        // In real implementation, this would fetch from your API
        await this.loadConversationFromAPI(conversationId);
        
        // Small delay to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } finally {
      this.isPreloading = false;
      this.preloadQueue = [];
    }
  }

  /**
   * Clear cache
   */
  clear(): void {
    this.conversations.clear();
    this.messageIndex.clear();
    this.accessOrder = [];
    this.stats = { hits: 0, misses: 0, totalRequests: 0 };
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const memoryUsage = this.calculateMemoryUsage();
    const hitRate = this.stats.totalRequests > 0 ? this.stats.hits / this.stats.totalRequests : 0;
    const missRate = this.stats.totalRequests > 0 ? this.stats.misses / this.stats.totalRequests : 0;

    return {
      totalConversations: this.conversations.size,
      totalMessages: Array.from(this.conversations.values()).reduce((sum, conv) => sum + conv.messages.length, 0),
      memoryUsage,
      hitRate,
      missRate,
    };
  }

  /**
   * Cleanup old conversations and messages
   */
  cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [conversationId, conversation] of this.conversations) {
      if (now - conversation.lastAccessed > maxAge) {
        this.conversations.delete(conversationId);
        this.accessOrder = this.accessOrder.filter(id => id !== conversationId);
        
        // Clean up message index
        conversation.messages.forEach(message => {
          this.messageIndex.delete(message.id);
        });
      }
    }
  }

  // Private methods

  private updateAccessOrder(conversationId: string): void {
    const index = this.accessOrder.indexOf(conversationId);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
    this.accessOrder.unshift(conversationId);
  }

  private enforceLimit(): void {
    while (this.conversations.size > this.maxConversations) {
      const oldestId = this.accessOrder.pop();
      if (oldestId) {
        const conversation = this.conversations.get(oldestId);
        if (conversation) {
          // Clean up message index
          conversation.messages.forEach(message => {
            this.messageIndex.delete(message.id);
          });
        }
        this.conversations.delete(oldestId);
      }
    }
  }

  private calculateMemoryUsage(): number {
    let size = 0;
    for (const conversation of this.conversations.values()) {
      size += JSON.stringify(conversation).length;
    }
    return size;
  }

  private async loadConversationFromAPI(conversationId: string): Promise<void> {
    // This would be implemented to fetch from your actual API
    // For now, we'll just mark it as preloaded
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      conversation.isPreloaded = true;
    }
  }
}

// Export singleton instance
export const messageCache = new MessageCache();

// Auto-cleanup every 30 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    messageCache.cleanup();
  }, 30 * 60 * 1000);
}

export type { CachedMessage, CachedConversation, CacheStats };
