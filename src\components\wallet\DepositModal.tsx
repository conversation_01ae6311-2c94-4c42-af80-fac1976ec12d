"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, CreditCardIcon, BanknotesIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { depositSchema, type DepositFormData } from "@/lib/wallet/validation";
import { toast } from "react-hot-toast";

interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  type: string;
  depositFee: string;
  depositFixedFee: string;
  minDeposit: string;
  maxDeposit: string;
  currency: string;
}

interface DepositModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function DepositModal({ isOpen, onClose, onSuccess }: DepositModalProps) {
  const [gateways, setGateways] = useState<PaymentGateway[]>([]);
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null);
  const [loading, setLoading] = useState(false);
  const [gatewaysLoading, setGatewaysLoading] = useState(true);
  const [feeCalculation, setFeeCalculation] = useState<any>(null);
  const [calculatingFee, setCalculatingFee] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<DepositFormData>({
    resolver: zodResolver(depositSchema),
  });

  const amount = watch("amount");
  const paymentGateway = watch("paymentGateway");

  // Fetch payment gateways
  useEffect(() => {
    if (isOpen) {
      fetchGateways();
    }
  }, [isOpen]);

  const fetchGateways = async () => {
    try {
      const response = await fetch('/api/wallet/gateways');
      const data = await response.json();

      if (data.success) {
        setGateways(data.data);
        if (data.data.length > 0) {
          setValue("paymentGateway", data.data[0].name);
          setSelectedGateway(data.data[0]);
        }
      } else {
        toast.error('Failed to fetch payment gateways');
      }
    } catch (error) {
      console.error('Error fetching gateways:', error);
      toast.error('Failed to fetch payment gateways');
    } finally {
      setGatewaysLoading(false);
    }
  };

  // Update selected gateway when payment gateway changes
  useEffect(() => {
    if (paymentGateway) {
      const gateway = gateways.find(g => g.name === paymentGateway);
      setSelectedGateway(gateway || null);
    }
  }, [paymentGateway, gateways]);

  // Calculate fees using API
  const calculateFees = async (amount: string) => {
    if (!amount || parseFloat(amount) <= 0) {
      setFeeCalculation(null);
      return;
    }

    setCalculatingFee(true);
    try {
      const response = await fetch('/api/wallet/deposit/calculate-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      });

      const result = await response.json();

      if (result.success) {
        setFeeCalculation(result.data);
      } else {
        setFeeCalculation(null);
        if (result.message) {
          toast.error(result.message);
        }
      }
    } catch (error) {
      console.error('Error calculating fee:', error);
      setFeeCalculation(null);
    } finally {
      setCalculatingFee(false);
    }
  };

  // Calculate fees when amount changes
  useEffect(() => {
    if (amount) {
      const timeoutId = setTimeout(() => {
        calculateFees(amount);
      }, 500); // Debounce API calls

      return () => clearTimeout(timeoutId);
    } else {
      setFeeCalculation(null);
    }
  }, [amount]);

  const onSubmit = async (data: DepositFormData) => {
    if (!selectedGateway) {
      toast.error('Please select a payment gateway');
      return;
    }

    if (!feeCalculation) {
      toast.error('Please wait for fee calculation to complete');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/wallet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          paymentGateway: data.paymentGateway,
          metadata: {
            gateway: selectedGateway,
            fees: feeCalculation,
          },
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Deposit initiated successfully!');
        onSuccess();
        handleClose();

        // Here you would typically redirect to the payment gateway
        // For demo purposes, we'll just show a success message
        toast.success('Redirecting to payment gateway...');
      } else {
        toast.error(result.message || 'Failed to initiate deposit');
      }
    } catch (error) {
      console.error('Error initiating deposit:', error);
      toast.error('Failed to initiate deposit');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedGateway(null);
    onClose();
  };

  const getGatewayIcon = (type: string) => {
    switch (type) {
      case 'stripe':
      case 'paypal':
        return <CreditCardIcon className="h-6 w-6" />;
      case 'bkash':
      case 'nagad':
      case 'rocket':
      case 'uddoktapay':
      case 'manual':
        return <BanknotesIcon className="h-6 w-6" />;
      default:
        return <CreditCardIcon className="h-6 w-6" />;
    }
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full bg-white rounded-lg shadow-xl">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Deposit Money
            </DialogTitle>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Amount Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount to Deposit
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <Input
                  {...register("amount")}
                  type="number"
                  step="0.01"
                  min={selectedGateway?.minDeposit || "1"}
                  max={selectedGateway?.maxDeposit || "10000"}
                  placeholder="0.00"
                  className="pl-7"
                />
              </div>
              {errors.amount && (
                <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
              )}

            </div>

            {/* Payment Gateway Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Method
              </label>
              {gatewaysLoading ? (
                <div className="space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-12 bg-gray-200 rounded animate-pulse" />
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {gateways.map((gateway) => (
                    <label
                      key={gateway.id}
                      className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
                    >
                      <input
                        {...register("paymentGateway")}
                        type="radio"
                        value={gateway.name}
                        className="sr-only"
                      />
                      <div className="flex items-center space-x-3 flex-1">
                        <div className={`p-2 rounded-lg ${
                          paymentGateway === gateway.name
                            ? 'bg-blue-100 text-blue-600'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {getGatewayIcon(gateway.type)}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">
                            {gateway.displayName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {gateway.type.charAt(0).toUpperCase() + gateway.type.slice(1)} Payment
                          </div>
                        </div>
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          paymentGateway === gateway.name
                            ? 'border-blue-600 bg-blue-600'
                            : 'border-gray-300'
                        }`}>
                          {paymentGateway === gateway.name && (
                            <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                          )}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              )}
              {errors.paymentGateway && (
                <p className="text-sm text-red-600 mt-1">{errors.paymentGateway.message}</p>
              )}
            </div>

            {/* Fee Breakdown */}
            {amount && (
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <h4 className="font-medium text-gray-900">Transaction Summary</h4>
                {calculatingFee ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-sm text-gray-600">Calculating fees...</span>
                  </div>
                ) : feeCalculation ? (
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Deposit Amount:</span>
                      <span className="font-medium">${feeCalculation.amount}</span>
                    </div>
                    {parseFloat(feeCalculation.percentageFee) > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Percentage Fee ({feeCalculation.feePercentage}%):</span>
                        <span className="font-medium">${feeCalculation.percentageFee}</span>
                      </div>
                    )}
                    {parseFloat(feeCalculation.feeFixed) > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Fixed Fee:</span>
                        <span className="font-medium">${feeCalculation.feeFixed}</span>
                      </div>
                    )}
                    {parseFloat(feeCalculation.totalFee) > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Fee:</span>
                        <span className="font-medium text-red-600">${feeCalculation.totalFee}</span>
                      </div>
                    )}
                    <div className="flex justify-between border-t border-gray-200 pt-1">
                      <span className="text-gray-900 font-medium">You'll Receive:</span>
                      <span className="font-bold text-lg text-green-600">${feeCalculation.netAmount}</span>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      Min: ${feeCalculation.limits.min} - Max: ${feeCalculation.limits.max}
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-gray-500 text-center py-2">
                    Enter an amount to see fee calculation
                  </div>
                )}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="flex-1"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1"
                disabled={loading || !amount || !selectedGateway}
                isLoading={loading}
              >
                {loading ? 'Processing...' : 'Proceed to Payment'}
              </Button>
            </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
