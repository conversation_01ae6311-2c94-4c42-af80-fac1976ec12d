import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { MessagesClient } from "@/components/messages/MessagesClient";

interface MessagePageProps {
  params: Promise<{
    username: string;
  }>;
}

export default async function MessagePage({ params }: MessagePageProps) {
  await requireAuth();
  
  const { username } = await params;

  return (
    <MainLayout>
      <MessagesClient initialUsername={username} />
    </MainLayout>
  );
}
