"use client";

import { useState } from "react";
import { 
  ShoppingBagIcon, 
  InformationCircleIcon, 
  StarIcon 
} from "@heroicons/react/24/outline";

interface StoreTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function StoreTabs({ activeTab, onTabChange }: StoreTabsProps) {
  const tabs = [
    { id: "products", name: "Products", icon: ShoppingBagIcon },
    { id: "info", name: "Store Info", icon: InformationCircleIcon },
    { id: "reviews", name: "Reviews", icon: StarIcon },
  ];

  return (
    <div className="border-b border-gray-200 mb-6">
      <div className="flex space-x-8 overflow-x-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`border-b-2 py-4 px-1 text-sm font-medium flex items-center whitespace-nowrap ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
              }`}
            >
              <Icon className="mr-2 h-5 w-5" />
              <span>{tab.name}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
}
