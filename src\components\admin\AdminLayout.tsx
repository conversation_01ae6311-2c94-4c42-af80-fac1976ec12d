"use client";

import { ReactNode, useState } from "react";
import { useSession } from "next-auth/react";
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { AdminHeader } from "@/components/admin/AdminHeader";
import { Breadcrumb } from "@/components/admin/Breadcrumb";
import { Spinner } from "@/components/ui/Spinner";
import { Toaster } from "react-hot-toast";
import { useAdminSessionValidation } from "@/hooks/useSessionValidation";

interface AdminLayoutProps {
  children: ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const { data: session, status } = useSession();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Real-time session validation for admin
  const { isValid } = useAdminSessionValidation({
    checkInterval: 30000, // Check every 30 seconds
    redirectOnInvalid: true,
    showToast: true
  });

  console.log('AdminLayout - Session status:', status);
  console.log('AdminLayout - Session data:', JSON.stringify(session, null, 2));

  // Show loading state while checking session
  if (status === "loading") {
    console.log('AdminLayout - Loading session...');
    return (
      <div className="flex h-screen items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  // If not authenticated or not an admin, this should be caught by middleware
  // But we'll add an extra check here just in case
  if (status !== "authenticated" || !session?.user?.isAdmin) {
    console.log('AdminLayout - Access denied:', {
      status,
      isAdmin: session?.user?.isAdmin
    });
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
        <p className="mt-2 text-gray-600">
          You don't have permission to access the admin panel.
        </p>
        <div className="mt-4 text-sm text-gray-500">
          <p>Status: {status}</p>
          <p>Is Admin: {session?.user?.isAdmin ? 'Yes' : 'No'}</p>
        </div>
      </div>
    );
  }

  console.log('AdminLayout - Access granted for admin:', session.user.email);

  return (
    <div className="flex h-screen bg-gray-100 relative">
      {/* Toast notifications */}
      <Toaster position="top-right" toastOptions={{
        duration: 4000,
        style: {
          background: '#fff',
          color: '#333',
        },
        success: {
          style: {
            background: '#ECFDF5',
            border: '1px solid #10B981',
            color: '#065F46',
          },
        },
        error: {
          style: {
            background: '#FEF2F2',
            border: '1px solid #EF4444',
            color: '#991B1B',
          },
        },
      }} />

      {/* Sidebar */}
      <AdminSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <AdminHeader
          onMenuClick={() => setSidebarOpen(true)}
          user={session.user}
        />

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8 lg:ml-72">
          <div className="mx-auto w-full">
            <Breadcrumb />
            <div className="mt-4 rounded-lg bg-white p-6 shadow-md">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
