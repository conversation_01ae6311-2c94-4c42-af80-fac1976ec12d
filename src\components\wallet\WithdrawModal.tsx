"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { XMarkIcon, InformationCircleIcon } from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

const withdrawSchema = z.object({
  amount: z.string().min(1, "Amount is required"),
  accountType: z.string().min(1, "Account type is required"),
  accountNumber: z.string().min(1, "Account number is required"),
  accountName: z.string().min(1, "Account name is required"),
  note: z.string().optional(),
  pin: z.string().min(4, "PIN must be at least 4 digits"),
});

type WithdrawFormData = z.infer<typeof withdrawSchema>;

interface WithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  earningBalance: string;
}

const accountTypes = [
  { value: 'bKash', label: 'bKash' },
  { value: 'Nagad', label: 'Nagad' },
  { value: 'Rocket', label: 'Rocket' },
  { value: 'Bank', label: 'Bank Account' },
];

interface FeeCalculation {
  amount: string;
  feeBreakdown: {
    percentage: number;
    percentageFee: string;
    fixedFee: string;
    totalFee: string;
  };
  netAmount: string;
  limits: {
    minimum: string;
    maximum: string;
  };
  balance: {
    earning: string;
    hasSufficientBalance: boolean;
  };
}

export function WithdrawModal({ isOpen, onClose, onSuccess, earningBalance }: WithdrawModalProps) {
  const [loading, setLoading] = useState(false);
  const [calculatingFee, setCalculatingFee] = useState(false);
  const [feeCalculation, setFeeCalculation] = useState<FeeCalculation | null>(null);
  const [step, setStep] = useState<'amount' | 'account' | 'confirm'>('amount');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
    setValue,
  } = useForm<WithdrawFormData>({
    resolver: zodResolver(withdrawSchema),
  });

  const amount = watch("amount");
  const accountType = watch("accountType");
  const accountNumber = watch("accountNumber");
  const accountName = watch("accountName");
  const note = watch("note");
  const pin = watch("pin");

  const onSubmit = async (data: WithdrawFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/wallet/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          accountType: data.accountType,
          accountNumber: data.accountNumber,
          accountName: data.accountName,
          note: data.note,
          pin: data.pin,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Withdraw request submitted successfully!');
        onSuccess();
        handleClose();
      } else {
        toast.error(result.message || 'Failed to submit withdraw request');
      }
    } catch (error) {
      console.error('Error submitting withdraw:', error);
      toast.error('Failed to submit withdraw request');
    } finally {
      setLoading(false);
    }
  };

  const calculateFee = async (amount: string) => {
    if (!amount || parseFloat(amount) <= 0) {
      setFeeCalculation(null);
      return;
    }

    try {
      setCalculatingFee(true);
      const response = await fetch('/api/wallet/withdraw/calculate-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      });

      const result = await response.json();

      if (result.success) {
        setFeeCalculation(result.data);
      } else {
        setFeeCalculation(null);
        if (result.message) {
          toast.error(result.message);
        }
      }
    } catch (error) {
      console.error('Error calculating fee:', error);
      setFeeCalculation(null);
    } finally {
      setCalculatingFee(false);
    }
  };

  const handleClose = () => {
    reset();
    setStep('amount');
    setFeeCalculation(null);
    onClose();
  };

  const handleNext = () => {
    if (step === 'amount' && amount) {
      setStep('account');
    } else if (step === 'account' && accountType && accountNumber && accountName) {
      setStep('confirm');
    }
  };

  const handleBack = () => {
    if (step === 'confirm') {
      setStep('account');
    } else if (step === 'account') {
      setStep('amount');
    }
  };

  // Calculate fee when amount changes
  useEffect(() => {
    if (amount && step === 'amount') {
      const timeoutId = setTimeout(() => {
        calculateFee(amount);
      }, 500); // Debounce for 500ms

      return () => clearTimeout(timeoutId);
    }
  }, [amount, step]);

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full bg-white rounded-lg shadow-xl">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <DialogTitle className="text-lg font-semibold text-gray-900">
                  Withdraw from Earning Wallet
                </DialogTitle>
                <p className="text-sm text-gray-500">
                  Available Balance: ${earningBalance}
                </p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

          <div className="p-6">
            {/* Step 1: Amount */}
            {step === 'amount' && (
              <div className="space-y-4">
                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                    Withdraw Amount
                  </label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="1"
                    max={earningBalance}
                    placeholder="Enter amount"
                    {...register("amount")}
                  />
                  {errors.amount && (
                    <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
                  )}

                  {/* Fee Calculation Display */}
                  {amount && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      {calculatingFee ? (
                        <div className="flex items-center text-sm text-gray-600">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                          Calculating fees...
                        </div>
                      ) : feeCalculation ? (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Withdrawal Amount:</span>
                            <span className="font-medium">${feeCalculation.amount}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Fee ({feeCalculation.feeBreakdown.percentage}%):</span>
                            <span className="font-medium">${feeCalculation.feeBreakdown.totalFee}</span>
                          </div>
                          {parseFloat(feeCalculation.feeBreakdown.fixedFee) > 0 && (
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Fixed Fee:</span>
                              <span className="font-medium">${feeCalculation.feeBreakdown.fixedFee}</span>
                            </div>
                          )}
                          <div className="flex justify-between text-sm font-semibold border-t pt-2">
                            <span className="text-gray-900">You will receive:</span>
                            <span className="text-green-600">${feeCalculation.netAmount}</span>
                          </div>

                          {!feeCalculation.balance.hasSufficientBalance && (
                            <div className="flex items-center text-sm text-red-600 mt-2">
                              <InformationCircleIcon className="h-4 w-4 mr-1" />
                              Insufficient balance. Available: ${feeCalculation.balance.earning}
                            </div>
                          )}
                        </div>
                      ) : null}
                    </div>
                  )}
                </div>

                <div>
                  <label htmlFor="note" className="block text-sm font-medium text-gray-700 mb-2">
                    Note (Optional)
                  </label>
                  <Input
                    id="note"
                    placeholder="Add a note"
                    {...register("note")}
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <Button type="button" variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    onClick={handleNext}
                    disabled={!amount || !feeCalculation || !feeCalculation.balance.hasSufficientBalance}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}

            {/* Step 2: Account Details */}
            {step === 'account' && (
              <div className="space-y-4">
                <div>
                  <label htmlFor="accountType" className="block text-sm font-medium text-gray-700 mb-2">
                    Account Type
                  </label>
                  <select
                    id="accountType"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    {...register("accountType")}
                  >
                    <option value="">Select account type</option>
                    {accountTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  {errors.accountType && (
                    <p className="text-sm text-red-600 mt-1">{errors.accountType.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    Account Number
                  </label>
                  <Input
                    id="accountNumber"
                    placeholder="Enter account number"
                    {...register("accountNumber")}
                  />
                  {errors.accountNumber && (
                    <p className="text-sm text-red-600 mt-1">{errors.accountNumber.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 mb-2">
                    Account Name
                  </label>
                  <Input
                    id="accountName"
                    placeholder="Enter account holder name"
                    {...register("accountName")}
                  />
                  {errors.accountName && (
                    <p className="text-sm text-red-600 mt-1">{errors.accountName.message}</p>
                  )}
                </div>

                <div className="flex justify-between space-x-3">
                  <Button type="button" variant="outline" onClick={handleBack}>
                    Back
                  </Button>
                  <Button
                    type="button"
                    onClick={handleNext}
                    disabled={!accountType || !accountNumber || !accountName}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}

            {/* Step 3: Confirmation */}
            {step === 'confirm' && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Withdraw Summary</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Amount:</span>
                      <span className="font-medium">${amount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Account Type:</span>
                      <span>{accountType}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Account Number:</span>
                      <span>{accountNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Account Name:</span>
                      <span>{accountName}</span>
                    </div>
                    {note && (
                      <div className="flex justify-between">
                        <span>Note:</span>
                        <span>{note}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label htmlFor="pin" className="block text-sm font-medium text-gray-700 mb-2">
                    Enter PIN to Confirm
                  </label>
                  <Input
                    id="pin"
                    type="password"
                    placeholder="Enter your PIN"
                    {...register("pin")}
                  />
                  {errors.pin && (
                    <p className="text-sm text-red-600 mt-1">{errors.pin.message}</p>
                  )}
                </div>

                <div className="flex justify-between space-x-3">
                  <Button type="button" variant="outline" onClick={handleBack}>
                    Back
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading || !pin}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {loading ? 'Processing...' : 'Confirm Withdraw'}
                  </Button>
                </div>
              </div>
            )}
          </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
