"use client";

export interface FeatureFlags {
  // Real-time features
  realtimeMessaging: boolean;
  realtimeNotifications: boolean;
  typingIndicators: boolean;
  onlinePresence: boolean;
  messageDeliveryStatus: boolean;
  
  // Performance features
  connectionPooling: boolean;
  smartReconnection: boolean;
  messageQueueing: boolean;
  
  // Fallback features
  pollingFallback: boolean;
  hybridMode: boolean; // Use both real-time and polling
  
  // Monitoring features
  performanceMonitoring: boolean;
  latencyTracking: boolean;
  connectionStatusReporting: boolean;
}

export interface FeatureFlagConfig {
  flags: FeatureFlags;
  rolloutPercentage: number; // 0-100
  userGroups?: string[]; // Specific user groups to enable for
  environment: "development" | "staging" | "production";
}

class FeatureFlagManager {
  private config: FeatureFlagConfig;
  private userGroup: string | null = null;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): FeatureFlagConfig {
    // Default configuration
    const defaultConfig: FeatureFlagConfig = {
      flags: {
        realtimeMessaging: false,
        realtimeNotifications: false,
        typingIndicators: false,
        onlinePresence: false,
        messageDeliveryStatus: false,
        connectionPooling: true,
        smartReconnection: true,
        messageQueueing: true,
        pollingFallback: true,
        hybridMode: false,
        performanceMonitoring: true,
        latencyTracking: true,
        connectionStatusReporting: true,
      },
      rolloutPercentage: 0,
      environment: "development"
    };

    // Load from environment variables
    const envConfig: Partial<FeatureFlagConfig> = {
      flags: {
        realtimeMessaging: process.env.NEXT_PUBLIC_FEATURE_REALTIME_MESSAGING === "true",
        realtimeNotifications: process.env.NEXT_PUBLIC_FEATURE_REALTIME_NOTIFICATIONS === "true",
        typingIndicators: process.env.NEXT_PUBLIC_FEATURE_TYPING_INDICATORS === "true",
        onlinePresence: process.env.NEXT_PUBLIC_FEATURE_ONLINE_PRESENCE === "true",
        messageDeliveryStatus: process.env.NEXT_PUBLIC_FEATURE_MESSAGE_DELIVERY_STATUS === "true",
        connectionPooling: process.env.NEXT_PUBLIC_FEATURE_CONNECTION_POOLING !== "false",
        smartReconnection: process.env.NEXT_PUBLIC_FEATURE_SMART_RECONNECTION !== "false",
        messageQueueing: process.env.NEXT_PUBLIC_FEATURE_MESSAGE_QUEUEING !== "false",
        pollingFallback: process.env.NEXT_PUBLIC_FEATURE_POLLING_FALLBACK !== "false",
        hybridMode: process.env.NEXT_PUBLIC_FEATURE_HYBRID_MODE === "true",
        performanceMonitoring: process.env.NEXT_PUBLIC_FEATURE_PERFORMANCE_MONITORING !== "false",
        latencyTracking: process.env.NEXT_PUBLIC_FEATURE_LATENCY_TRACKING !== "false",
        connectionStatusReporting: process.env.NEXT_PUBLIC_FEATURE_CONNECTION_STATUS_REPORTING !== "false",
      },
      rolloutPercentage: parseInt(process.env.NEXT_PUBLIC_FEATURE_ROLLOUT_PERCENTAGE || "0"),
      environment: (process.env.NODE_ENV as any) || "development"
    };

    // Merge configurations
    return {
      ...defaultConfig,
      ...envConfig,
      flags: {
        ...defaultConfig.flags,
        ...envConfig.flags
      }
    };
  }

  setUserGroup(group: string) {
    this.userGroup = group;
  }

  isEnabled(feature: keyof FeatureFlags, userId?: string): boolean {
    const flag = this.config.flags[feature];
    
    // If flag is explicitly disabled, return false
    if (!flag) return false;

    // Check user group eligibility
    if (this.config.userGroups && this.userGroup) {
      if (!this.config.userGroups.includes(this.userGroup)) {
        return false;
      }
    }

    // Check rollout percentage
    if (this.config.rolloutPercentage < 100 && userId) {
      const hash = this.hashUserId(userId);
      const userPercentile = hash % 100;
      return userPercentile < this.config.rolloutPercentage;
    }

    return this.config.rolloutPercentage > 0;
  }

  getConfig(): FeatureFlagConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<FeatureFlagConfig>) {
    this.config = {
      ...this.config,
      ...newConfig,
      flags: {
        ...this.config.flags,
        ...(newConfig.flags || {})
      }
    };
  }

  // Simple hash function for consistent user bucketing
  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  // Get recommended configuration based on environment
  getRecommendedConfig(): FeatureFlagConfig {
    const baseConfig = this.getConfig();

    switch (baseConfig.environment) {
      case "development":
        return {
          ...baseConfig,
          flags: {
            ...baseConfig.flags,
            realtimeMessaging: true,
            realtimeNotifications: true,
            typingIndicators: true,
            onlinePresence: true,
            messageDeliveryStatus: true,
            pollingFallback: true,
            hybridMode: true,
            performanceMonitoring: true,
          },
          rolloutPercentage: 100
        };

      case "staging":
        return {
          ...baseConfig,
          flags: {
            ...baseConfig.flags,
            realtimeMessaging: true,
            realtimeNotifications: true,
            typingIndicators: true,
            pollingFallback: true,
            hybridMode: false,
            performanceMonitoring: true,
          },
          rolloutPercentage: 50
        };

      case "production":
        return {
          ...baseConfig,
          flags: {
            ...baseConfig.flags,
            realtimeMessaging: false, // Start with false for gradual rollout
            realtimeNotifications: false,
            typingIndicators: false,
            pollingFallback: true,
            hybridMode: true, // Use hybrid mode for safety
            performanceMonitoring: true,
          },
          rolloutPercentage: 10 // Start with 10% rollout
        };

      default:
        return baseConfig;
    }
  }

  // Migration helpers
  shouldUseRealtime(feature: keyof FeatureFlags, userId?: string): boolean {
    return this.isEnabled(feature, userId);
  }

  shouldUsePolling(userId?: string): boolean {
    return this.isEnabled("pollingFallback", userId) || 
           !this.isEnabled("realtimeMessaging", userId);
  }

  shouldUseHybridMode(userId?: string): boolean {
    return this.isEnabled("hybridMode", userId);
  }

  // Performance monitoring helpers
  shouldTrackLatency(userId?: string): boolean {
    return this.isEnabled("latencyTracking", userId);
  }

  shouldReportConnectionStatus(userId?: string): boolean {
    return this.isEnabled("connectionStatusReporting", userId);
  }

  // Debug helpers
  getDebugInfo(userId?: string): Record<string, any> {
    return {
      config: this.config,
      userGroup: this.userGroup,
      userId,
      userHash: userId ? this.hashUserId(userId) : null,
      userPercentile: userId ? this.hashUserId(userId) % 100 : null,
      enabledFeatures: Object.keys(this.config.flags).filter(
        feature => this.isEnabled(feature as keyof FeatureFlags, userId)
      )
    };
  }
}

// Singleton instance
export const featureFlags = new FeatureFlagManager();

// React hook for feature flags
export function useFeatureFlags(userId?: string) {
  const isEnabled = (feature: keyof FeatureFlags) => featureFlags.isEnabled(feature, userId);
  
  return {
    isEnabled,
    shouldUseRealtime: (feature: keyof FeatureFlags) => featureFlags.shouldUseRealtime(feature, userId),
    shouldUsePolling: () => featureFlags.shouldUsePolling(userId),
    shouldUseHybridMode: () => featureFlags.shouldUseHybridMode(userId),
    shouldTrackLatency: () => featureFlags.shouldTrackLatency(userId),
    shouldReportConnectionStatus: () => featureFlags.shouldReportConnectionStatus(userId),
    getConfig: () => featureFlags.getConfig(),
    getDebugInfo: () => featureFlags.getDebugInfo(userId)
  };
}

// Environment variable helpers for .env configuration
export const ENV_VARS = {
  REALTIME_MESSAGING: "NEXT_PUBLIC_FEATURE_REALTIME_MESSAGING",
  REALTIME_NOTIFICATIONS: "NEXT_PUBLIC_FEATURE_REALTIME_NOTIFICATIONS", 
  TYPING_INDICATORS: "NEXT_PUBLIC_FEATURE_TYPING_INDICATORS",
  ONLINE_PRESENCE: "NEXT_PUBLIC_FEATURE_ONLINE_PRESENCE",
  MESSAGE_DELIVERY_STATUS: "NEXT_PUBLIC_FEATURE_MESSAGE_DELIVERY_STATUS",
  POLLING_FALLBACK: "NEXT_PUBLIC_FEATURE_POLLING_FALLBACK",
  HYBRID_MODE: "NEXT_PUBLIC_FEATURE_HYBRID_MODE",
  ROLLOUT_PERCENTAGE: "NEXT_PUBLIC_FEATURE_ROLLOUT_PERCENTAGE",
  PARTYKIT_HOST: "NEXT_PUBLIC_PARTYKIT_HOST"
} as const;
