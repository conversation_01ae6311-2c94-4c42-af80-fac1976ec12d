CREATE TABLE `accounts` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`type` varchar(255) NOT NULL,
	`provider` varchar(255) NOT NULL,
	`providerAccountId` varchar(255) NOT NULL,
	`refresh_token` text,
	`access_token` text,
	`expires_at` int,
	`token_type` varchar(255),
	`scope` varchar(255),
	`id_token` text,
	`session_state` varchar(255),
	CONSTRAINT `accounts_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `comments` (
	`id` varchar(255) NOT NULL,
	`content` text NOT NULL,
	`userId` varchar(255) NOT NULL,
	`postId` varchar(255) NOT NULL,
	`parentId` varchar(255),
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `comments_id` PRIMARY KEY(`id`)
);

--> statement-breakpoint
CREATE TABLE `groupMembers` (
	`id` varchar(255) NOT NULL,
	`groupId` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`role` enum('admin','moderator','member','pending') NOT NULL DEFAULT 'pending',
	`joinedAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `groupMembers_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `groupReports` (
	`id` varchar(255) NOT NULL,
	`groupId` varchar(255) NOT NULL,
	`reporterId` varchar(255) NOT NULL,
	`reportedUserId` varchar(255),
	`postId` varchar(255),
	`commentId` varchar(255),
	`reason` enum('spam','harassment','inappropriate_content','hate_speech','violence','other') NOT NULL,
	`description` text,
	`status` enum('pending','reviewed','resolved','dismissed') NOT NULL DEFAULT 'pending',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `groupReports_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `groupRules` (
	`id` varchar(255) NOT NULL,
	`groupId` varchar(255) NOT NULL,
	`title` varchar(255) NOT NULL,
	`description` text,
	`order` int DEFAULT 0,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `groupRules_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `groups` (
	`id` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`description` text,
	`visibility` enum('public','private-visible','private-hidden') NOT NULL DEFAULT 'public',
	`coverImage` varchar(255),
	`profileImage` varchar(255),
	`category` varchar(100),
	`creatorId` varchar(255) NOT NULL,
	`postingPermission` enum('all-members','admin-only','admin-and-moderators') NOT NULL DEFAULT 'all-members',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `groups_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `likes` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`postId` varchar(255),
	`commentId` varchar(255),
	`type` enum('like','dislike') NOT NULL DEFAULT 'like',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `likes_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `messages` (
	`id` varchar(255) NOT NULL,
	`senderId` varchar(255) NOT NULL,
	`receiverId` varchar(255) NOT NULL,
	`content` text NOT NULL,
	`read` boolean NOT NULL DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `messages_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `notifications` (
	`id` varchar(255) NOT NULL,
	`recipientId` varchar(255) NOT NULL,
	`type` enum('like','comment','friend_request','friend_accept','message','group_invite','group_join_request','group_join_approved','group_post') NOT NULL,
	`senderId` varchar(255),
	`postId` varchar(255),
	`commentId` varchar(255),
	`messageId` varchar(255),
	`friendshipId` varchar(255),
	`groupId` varchar(255),
	`read` boolean NOT NULL DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `notifications_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `pageLikes` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`pageId` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `pageLikes_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `pages` (
	`id` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`username` varchar(50),
	`category` varchar(100),
	`description` text,
	`coverImage` varchar(255),
	`profileImage` varchar(255),
	`website` varchar(255),
	`location` varchar(255),
	`creatorId` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `pages_id` PRIMARY KEY(`id`),
	CONSTRAINT `pages_username_unique` UNIQUE(`username`)
);
--> statement-breakpoint
CREATE TABLE `posts` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`pageId` varchar(255),
	`groupId` varchar(255),
	`content` text,
	`images` json,
	`videos` json,
	`privacy` enum('public','friends','private') NOT NULL DEFAULT 'public',
	`sharedPostId` varchar(255),
	`backgroundColor` varchar(50),
	`feeling` varchar(100),
	`activity` varchar(100),
	`location` varchar(255),
	`formattedContent` boolean DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `posts_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `reports` (
	`id` varchar(255) NOT NULL,
	`reporterId` varchar(255) NOT NULL,
	`reportedUserId` varchar(255) NOT NULL,
	`reason` enum('spam','harassment','inappropriate_content','impersonation','other') NOT NULL,
	`description` text,
	`status` enum('pending','reviewed','resolved','dismissed') NOT NULL DEFAULT 'pending',
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `reports_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `savedPosts` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`postId` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `savedPosts_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `sessions` (
	`id` varchar(255) NOT NULL,
	`sessionToken` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`expires` timestamp NOT NULL,
	CONSTRAINT `sessions_id` PRIMARY KEY(`id`),
	CONSTRAINT `sessions_sessionToken_unique` UNIQUE(`sessionToken`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` varchar(255) NOT NULL,
	`name` varchar(255),
	`username` varchar(50),
	`email` varchar(255) NOT NULL,
	`emailVerified` timestamp,
	`image` varchar(255),
	`coverImage` varchar(255),
	`bio` text,
	`location` varchar(255),
	`birthday` datetime,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_username_unique` UNIQUE(`username`),
	CONSTRAINT `users_email_unique` UNIQUE(`email`)
);
--> statement-breakpoint
CREATE TABLE `verificationTokens` (
	`identifier` varchar(255) NOT NULL,
	`token` varchar(255) NOT NULL,
	`expires` timestamp NOT NULL,
	CONSTRAINT `verificationTokens_identifier_token_pk` PRIMARY KEY(`identifier`,`token`)
);
