import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { adminPermissions } from "@/lib/db/schema";

// Get all permissions
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all permissions
    const permissions = await db.query.adminPermissions.findMany({
      orderBy: (adminPermissions, { asc }) => [asc(adminPermissions.module), asc(adminPermissions.name)],
    });

    return NextResponse.json(permissions, { status: 200 });
  } catch (error) {
    console.error("Error fetching admin permissions:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
