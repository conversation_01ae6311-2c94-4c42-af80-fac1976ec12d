import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { fanPagePosts, fanPages } from "@/lib/db/schema";
import { eq, and, sql, isNotNull } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId]/photos/stats - Get photo statistics
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;

    // Check if fan page exists
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    // Get total posts with images count
    const postsWithImagesResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(fanPagePosts)
      .where(
        and(
          eq(fanPagePosts.fanPageId, pageId),
          eq(fanPagePosts.isPublished, true),
          isNotNull(fanPagePosts.images),
          sql`JSON_LENGTH(${fanPagePosts.images}) > 0`
        )
      );

    const postsWithImagesCount = postsWithImagesResult[0]?.count || 0;

    // Get total individual photos count
    const postsWithImages = await db
      .select({
        images: fanPagePosts.images,
      })
      .from(fanPagePosts)
      .where(
        and(
          eq(fanPagePosts.fanPageId, pageId),
          eq(fanPagePosts.isPublished, true),
          isNotNull(fanPagePosts.images),
          sql`JSON_LENGTH(${fanPagePosts.images}) > 0`
        )
      );

    let totalPhotosCount = 0;
    postsWithImages.forEach(post => {
      if (post.images && Array.isArray(post.images)) {
        totalPhotosCount += post.images.length;
      }
    });

    return NextResponse.json({
      totalPhotos: totalPhotosCount,
      postsWithPhotos: postsWithImagesCount,
      averagePhotosPerPost: postsWithImagesCount > 0 ? Math.round((totalPhotosCount / postsWithImagesCount) * 10) / 10 : 0,
    });

  } catch (error) {
    console.error("Error fetching photo stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch photo statistics" },
      { status: 500 }
    );
  }
}
