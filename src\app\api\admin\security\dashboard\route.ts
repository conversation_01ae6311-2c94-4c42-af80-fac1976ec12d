import { NextRequest, NextResponse } from "next/server";
import { withSecurity } from "@/lib/security/api-wrapper";

// Mock security data (in production, this would come from database/monitoring service)
const mockSecurityData = {
  rateLimitViolations: [
    {
      id: '1',
      ip: '*************',
      endpoint: '/api/auth/login',
      attempts: 15,
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      blocked: true,
    },
    {
      id: '2',
      ip: '*********',
      endpoint: '/api/posts',
      attempts: 120,
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      blocked: true,
    },
  ],
  
  securityEvents: [
    {
      id: '1',
      type: 'SQL_INJECTION_ATTEMPT',
      severity: 'high',
      ip: '************',
      userAgent: 'Mozilla/5.0 (compatible; BadBot/1.0)',
      endpoint: '/api/users',
      payload: "'; DROP TABLE users; --",
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      blocked: true,
    },
    {
      id: '2',
      type: 'XSS_ATTEMPT',
      severity: 'medium',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      endpoint: '/api/posts',
      payload: '<script>alert("xss")</script>',
      timestamp: new Date(Date.now() - 5400000).toISOString(),
      blocked: true,
    },
    {
      id: '3',
      type: 'CSRF_VIOLATION',
      severity: 'medium',
      ip: '**********',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      endpoint: '/api/admin/users',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      blocked: true,
    },
  ],
  
  suspiciousIPs: [
    {
      ip: '************',
      country: 'Unknown',
      attempts: 25,
      lastSeen: new Date(Date.now() - 3600000).toISOString(),
      blocked: true,
      reason: 'Multiple security violations',
    },
    {
      ip: '*************',
      country: 'US',
      attempts: 8,
      lastSeen: new Date(Date.now() - 5400000).toISOString(),
      blocked: false,
      reason: 'XSS attempts',
    },
  ],
  
  authenticationEvents: [
    {
      id: '1',
      type: 'FAILED_LOGIN',
      userId: null,
      email: '<EMAIL>',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      reason: 'Invalid password',
    },
    {
      id: '2',
      type: 'ACCOUNT_LOCKOUT',
      userId: 'user-123',
      email: '<EMAIL>',
      ip: '*********',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1)',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      reason: 'Too many failed attempts',
    },
  ],
};

export const GET = withSecurity(
  async ({ user, request }) => {
    try {
      const url = new URL(request.url);
      const timeRange = url.searchParams.get('timeRange') || '24h';
      const eventType = url.searchParams.get('eventType') || 'all';
      
      // Calculate time range
      const now = new Date();
      let startTime: Date;
      
      switch (timeRange) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      // Filter data based on time range
      const filterByTime = (items: any[]) => 
        items.filter(item => new Date(item.timestamp) >= startTime);

      // Security metrics
      const metrics = {
        totalSecurityEvents: mockSecurityData.securityEvents.length,
        blockedAttacks: mockSecurityData.securityEvents.filter(e => e.blocked).length,
        rateLimitViolations: mockSecurityData.rateLimitViolations.length,
        suspiciousIPs: mockSecurityData.suspiciousIPs.length,
        failedLogins: mockSecurityData.authenticationEvents.filter(e => e.type === 'FAILED_LOGIN').length,
        accountLockouts: mockSecurityData.authenticationEvents.filter(e => e.type === 'ACCOUNT_LOCKOUT').length,
      };

      // Event distribution by type
      const eventDistribution = {
        sqlInjection: mockSecurityData.securityEvents.filter(e => e.type === 'SQL_INJECTION_ATTEMPT').length,
        xss: mockSecurityData.securityEvents.filter(e => e.type === 'XSS_ATTEMPT').length,
        csrf: mockSecurityData.securityEvents.filter(e => e.type === 'CSRF_VIOLATION').length,
        rateLimit: mockSecurityData.rateLimitViolations.length,
        authFailures: mockSecurityData.authenticationEvents.filter(e => e.type === 'FAILED_LOGIN').length,
      };

      // Top attacked endpoints
      const endpointAttacks = [
        { endpoint: '/api/auth/login', attacks: 15, severity: 'high' },
        { endpoint: '/api/posts', attacks: 8, severity: 'medium' },
        { endpoint: '/api/users', attacks: 5, severity: 'high' },
        { endpoint: '/api/admin/users', attacks: 3, severity: 'medium' },
      ];

      // Security health score (0-100)
      const calculateHealthScore = () => {
        let score = 100;
        
        // Deduct points for security events
        score -= Math.min(metrics.totalSecurityEvents * 2, 30);
        score -= Math.min(metrics.failedLogins * 1, 20);
        score -= Math.min(metrics.suspiciousIPs * 5, 25);
        score -= Math.min(metrics.rateLimitViolations * 3, 25);
        
        return Math.max(score, 0);
      };

      const healthScore = calculateHealthScore();
      
      // Determine health status
      let healthStatus: 'excellent' | 'good' | 'warning' | 'critical';
      if (healthScore >= 90) healthStatus = 'excellent';
      else if (healthScore >= 70) healthStatus = 'good';
      else if (healthScore >= 50) healthStatus = 'warning';
      else healthStatus = 'critical';

      // Recent security events (filtered by time and type)
      let recentEvents = filterByTime(mockSecurityData.securityEvents);
      if (eventType !== 'all') {
        recentEvents = recentEvents.filter(event => 
          event.type.toLowerCase().includes(eventType.toLowerCase())
        );
      }

      // Security recommendations
      const recommendations = [];
      if (metrics.failedLogins > 10) {
        recommendations.push({
          type: 'warning',
          message: 'High number of failed login attempts detected',
          action: 'Consider implementing account lockout policies',
        });
      }
      if (metrics.suspiciousIPs > 5) {
        recommendations.push({
          type: 'critical',
          message: 'Multiple suspicious IP addresses detected',
          action: 'Review and consider blocking suspicious IPs',
        });
      }
      if (healthScore < 70) {
        recommendations.push({
          type: 'warning',
          message: 'Security health score is below recommended threshold',
          action: 'Review security policies and implement additional protections',
        });
      }

      const dashboardData = {
        timeRange,
        metrics,
        healthScore,
        healthStatus,
        eventDistribution,
        endpointAttacks,
        recentEvents: recentEvents.slice(0, 20), // Limit to 20 most recent
        rateLimitViolations: filterByTime(mockSecurityData.rateLimitViolations).slice(0, 10),
        suspiciousIPs: mockSecurityData.suspiciousIPs.slice(0, 10),
        authenticationEvents: filterByTime(mockSecurityData.authenticationEvents).slice(0, 10),
        recommendations,
        lastUpdated: new Date().toISOString(),
      };

      return NextResponse.json({
        success: true,
        data: dashboardData,
      });

    } catch (error) {
      console.error('Security dashboard error:', error);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to fetch security dashboard data' 
        },
        { status: 500 }
      );
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    allowedMethods: ['GET'],
  }
);
