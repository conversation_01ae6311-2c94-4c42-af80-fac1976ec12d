"use client";

import { useState, useEffect } from "react";
import { DashboardStats } from "./DashboardStats";
import { DashboardCharts } from "./DashboardCharts";
import { RecentActivity } from "./RecentActivity";
import { QuickActions } from "./QuickActions";
import { Button } from "@/components/ui/Button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/Tabs";
import {
  ChartBarIcon,
  ClockIcon,
  Cog6ToothIcon,
  ArrowLeftIcon,
  PencilIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { toast } from "react-hot-toast";

interface FanPage {
  id: string;
  name: string;
  username: string;
  description: string | null;
  profileImage: string | null;
  coverImage: string | null;
  category: string;
  isVerified: boolean;
  followerCount: number;
  postCount: number;
  owner: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface DashboardStats {
  overview: {
    totalFollowers: number;
    totalPosts: number;
    totalMessages: number;
    unreadMessages: number;
    totalLikes: number;
    totalComments: number;
    engagementRate: number;
  };
  growth: {
    newFollowersThisMonth: number;
    newFollowersLastMonth: number;
    followerGrowthRate: number;
    postsThisMonth: number;
  };
  engagement: {
    likesThisWeek: number;
    commentsThisWeek: number;
    totalEngagement: number;
  };
}

interface FanPageDashboardProps {
  page: FanPage;
  isOwner: boolean;
  hasAdminRole: boolean;
  currentUserId: string;
}

export function FanPageDashboard({
  page,
  isOwner,
  hasAdminRole,
  currentUserId
}: FanPageDashboardProps) {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    fetchDashboardStats();
  }, [page.id]);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/fan-pages/${page.id}/dashboard/stats`);

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats');
      }

      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      toast.error('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <Link href={`/pages/${page.username}`}>
                <Button variant="outline" size="sm">
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back to Page
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                {page.profileImage ? (
                  <img
                    src={page.profileImage}
                    alt={page.name}
                    className="h-12 w-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <span className="text-white font-semibold text-lg">
                      {page.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    {page.name}
                    {page.isVerified && (
                      <svg className="ml-2 h-6 w-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    )}
                  </h1>
                  <p className="text-sm text-gray-500">@{page.username} • Dashboard</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Link href={`/pages/${page.username}/edit`}>
                <Button variant="outline" size="sm">
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit Page
                </Button>
              </Link>
              <Link href={`/pages/${page.username}?tab=settings`}>
                <Button variant="outline" size="sm">
                  <Cog6ToothIcon className="h-4 w-4 mr-2" />
                  Settings
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <ChartBarIcon className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center space-x-2">
              <ChartBarIcon className="h-4 w-4" />
              <span>Analytics</span>
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center space-x-2">
              <ClockIcon className="h-4 w-4" />
              <span>Activity</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Quick Actions */}
            <QuickActions page={page} />

            {/* Stats Cards */}
            {stats && <DashboardStats stats={stats} />}

            {/* Recent Activity Preview */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <RecentActivity pageId={page.id} limit={5} showHeader={true} />
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                {stats && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Engagement Rate</span>
                      <span className="text-sm font-medium text-gray-900">
                        {stats.overview.engagementRate}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">This Week's Engagement</span>
                      <span className="text-sm font-medium text-gray-900">
                        {stats.engagement.likesThisWeek + stats.engagement.commentsThisWeek}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Posts This Month</span>
                      <span className="text-sm font-medium text-gray-900">
                        {stats.growth.postsThisMonth}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Follower Growth</span>
                      <span className={`text-sm font-medium ${
                        stats.growth.followerGrowthRate >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stats.growth.followerGrowthRate >= 0 ? '+' : ''}{stats.growth.followerGrowthRate}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <DashboardCharts pageId={page.id} />
          </TabsContent>

          <TabsContent value="activity" className="space-y-6">
            <RecentActivity pageId={page.id} limit={50} showHeader={false} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
