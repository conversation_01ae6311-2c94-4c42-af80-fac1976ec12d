import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { internalTransferSchema } from "@/lib/wallet/validation";

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = internalTransferSchema.parse(body);

    // Check if internal transfers are enabled
    const transferEnabled = await WalletService.getWalletSetting('internal_transfer_enabled');
    if (transferEnabled === 'false') {
      return NextResponse.json(
        { 
          success: false,
          message: "Internal transfers are currently disabled" 
        },
        { status: 400 }
      );
    }

    // Process internal transfer (earning to general)
    const transactionId = await WalletService.internalTransfer(session.user.id, {
      amount: validatedData.amount,
      fromWalletType: 'earning',
      toWalletType: 'general',
      pin: validatedData.pin,
    });

    return NextResponse.json({
      success: true,
      data: {
        transactionId,
        message: "Transfer completed successfully",
      },
    });
  } catch (error: any) {
    console.error("Error processing internal transfer:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid input data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to process transfer" 
      },
      { status: 500 }
    );
  }
}
