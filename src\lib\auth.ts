import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import GithubProvider from "next-auth/providers/github";
import { executeQuery } from "./db/mysql";
import bcrypt from "bcryptjs";
import { authConfig } from "./config";
import { db } from "./db";
import { users, adminRoles } from "./db/schema";
import { eq } from "drizzle-orm";
import { getOAuthConfig } from "./oauth-config";

// Function to build auth options dynamically
export async function buildAuthOptions(): Promise<NextAuthOptions> {
  const oauthConfig = await getOAuthConfig();

  const providers = [];

  // Add Google provider if enabled and configured
  if (oauthConfig.googleOAuthEnabled && oauthConfig.googleClientId && oauthConfig.googleClientSecret) {
    providers.push(
      GoogleProvider({
        clientId: oauthConfig.googleClientId,
        clientSecret: oauthConfig.googleClientSecret,
      })
    );
  }

  // Add GitHub provider if enabled and configured
  if (oauthConfig.githubOAuthEnabled && oauthConfig.githubClientId && oauthConfig.githubClientSecret) {
    providers.push(
      GithubProvider({
        clientId: oauthConfig.githubClientId,
        clientSecret: oauthConfig.githubClientSecret,
      })
    );
  }

  // Always add credentials provider
  providers.push(
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        emailOrUsername: { label: "Email or Username", type: "text" },
        password: { label: "Password", type: "password" },
        isAdmin: { label: "Is Admin Login", type: "text" },
      },
      async authorize(credentials) {
        const emailOrUsername = credentials?.emailOrUsername || credentials?.email;
        if (!emailOrUsername || !credentials?.password) {
          if (process.env.NODE_ENV === 'development') {
            console.log('Missing email/username or password');
          }
          return null;
        }

        try {
          // Check if this is an admin login attempt (from admin login page)
          const isAdminLogin = credentials.isAdmin === 'true';
          if (process.env.NODE_ENV === 'development') {
            console.log('Admin login attempt:', isAdminLogin, 'for emailOrUsername:', emailOrUsername);
            console.log('Querying database for user with email or username:', emailOrUsername);
          }

          // Check if input is email or username
          const isEmail = emailOrUsername.includes('@');
          const users = await executeQuery<any[]>(
            isEmail
              ? "SELECT * FROM users WHERE email = ?"
              : "SELECT * FROM users WHERE username = ? OR email = ?",
            isEmail
              ? [emailOrUsername]
              : [emailOrUsername, emailOrUsername]
          );

          if (process.env.NODE_ENV === 'development') {
            console.log('Database query result:', JSON.stringify(users, null, 2));
          }
          const user = users[0];

          if (!user) {
            if (process.env.NODE_ENV === 'development') {
              console.log('User not found in database');
            }
            return null;
          }

          if (process.env.NODE_ENV === 'development') {
            console.log('User found:', {
              id: user.id,
              email: user.email,
              is_admin: user.is_admin,
              role: user.role,
              status: user.status,
              is_active: user.is_active,
              hasPassword: user.password ? 'YES' : 'NO',
              passwordLength: user.password ? user.password.length : 0
            });
          }

          // Check if user account is active
          if (user.status !== 'active' || !user.is_active) {
            console.log('User account is not active:', {
              status: user.status,
              is_active: user.is_active,
              suspended_at: user.suspended_at,
              suspended_reason: user.suspended_reason
            });
            return null;
          }

          // If this is an admin login attempt, verify the user is an admin
          if (isAdminLogin && !user.is_admin) {
            console.log('Admin login attempt by non-admin user:', user.email);
            return null;
          }

          // Handle password authentication
          if (user.password && user.password.trim() !== '') {
            // User has a password set - verify it
            console.log('Verifying password for user:', user.email);
            const passwordMatch = await bcrypt.compare(credentials.password, user.password);
            if (!passwordMatch) {
              console.log('Password verification failed for user:', user.email);
              return null;
            }
            console.log('Password verified successfully for user:', user.email);
          } else {
            // First time login - set the password
            console.log('First time login detected for user:', user.email, '- setting password');
            const hashedPassword = await bcrypt.hash(credentials.password, 12);

            // Update user with the new password
            await executeQuery(
              "UPDATE users SET password = ? WHERE id = ?",
              [hashedPassword, user.id]
            );

            console.log('Password set successfully for first-time login:', user.email);
          }

          const userData = {
            id: user.id,
            name: user.name,
            email: user.email,
            image: user.image,
            isAdmin: user.is_admin === 1 || user.is_admin === true, // Ensure boolean conversion
            role: user.role,
            adminRoleId: user.admin_role_id,
            status: user.status,
            isActive: user.is_active === 1 || user.is_active === true,
          };

          console.log('Returning user data from authorize:', JSON.stringify(userData, null, 2));
          return userData;
        } catch (error) {
          console.error("Error during authentication:", error);
          return null;
        }
      },
    })
  );

  return {
    session: {
      strategy: "jwt",
      maxAge: 30 * 24 * 60 * 60, // 30 days
    },
    jwt: {
      maxAge: 30 * 24 * 60 * 60, // 30 days
    },
    pages: {
      signIn: "/login",
      error: "/login",
    },
    providers,
    callbacks: {
    async session({ session, token }) {
      // Reduced logging for performance - only log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Session callback - token data:', JSON.stringify(token, null, 2));
      }

      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.picture as string || token.image as string;

        // Add admin information to the session
        session.user.isAdmin = token.isAdmin as boolean;
        session.user.role = token.role as string;
        session.user.adminRoleId = token.adminRoleId as string;
        session.user.adminRoleName = token.adminRoleName as string;

        // Add user status information
        session.user.status = token.status as string;
        session.user.isActive = token.isActive as boolean;

        if (process.env.NODE_ENV === 'development') {
          console.log('Updated session:', JSON.stringify(session, null, 2));
        }
      }
      return session;
    },
    async jwt({ token, user, account, profile }) {
      // Initial sign in
      if (user) {
        // Reduced logging for performance - only log in development
        if (process.env.NODE_ENV === 'development') {
          console.log('JWT callback - user data:', JSON.stringify(user, null, 2));
        }
        token.id = user.id;
        if (user.image) {
          token.image = user.image;
        }

        // Set admin properties directly from the user object first
        if (user.isAdmin !== undefined) {
          token.isAdmin = user.isAdmin;
          if (process.env.NODE_ENV === 'development') {
            console.log('Setting isAdmin from user object:', user.isAdmin);
          }
        }

        if (user.role !== undefined) {
          token.role = user.role;
        }

        if (user.adminRoleId !== undefined) {
          token.adminRoleId = user.adminRoleId;
        }

        if (user.status !== undefined) {
          token.status = user.status;
        }

        if (user.isActive !== undefined) {
          token.isActive = user.isActive;
        }

        // Fetch additional user data from the database
        try {
          if (process.env.NODE_ENV === 'development') {
            console.log('Fetching user data from database for ID:', user.id);
          }
          const userData = await db.query.users.findFirst({
            where: eq(users.id, user.id),
            with: {
              adminRole: true,
            },
          });

          if (process.env.NODE_ENV === 'development') {
            console.log('User data from database:', JSON.stringify(userData, null, 2));
          }

          if (userData) {
            // Only override if not already set from user object
            if (token.isAdmin === undefined) {
              token.isAdmin = userData.isAdmin || false;
              if (process.env.NODE_ENV === 'development') {
                console.log('Setting isAdmin from database:', userData.isAdmin);
              }
            }

            if (token.role === undefined) {
              token.role = userData.role || 'user';
            }

            if (token.adminRoleId === undefined) {
              token.adminRoleId = userData.adminRoleId || null;
            }

            token.adminRoleName = userData.adminRole?.name || null;

            // Always update status and isActive from database for real-time validation
            token.status = userData.status || 'active';
            token.isActive = userData.isActive || false;
          }
        } catch (error) {
          console.error('Error fetching user data for JWT:', error);
        }
      }

      // Reduced logging for performance - only log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Final JWT token:', JSON.stringify(token, null, 2));
      }
      return token;
    },
  },
  secret: authConfig.nextAuthSecret,
  };
}

// Static auth options for backward compatibility
export const authOptions: NextAuthOptions = {
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  providers: [
    GoogleProvider({
      clientId: authConfig.googleClientId,
      clientSecret: authConfig.googleClientSecret,
    }),
    GithubProvider({
      clientId: authConfig.githubClientId,
      clientSecret: authConfig.githubClientSecret,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        emailOrUsername: { label: "Email or Username", type: "text" },
        password: { label: "Password", type: "password" },
        isAdmin: { label: "Is Admin Login", type: "text" },
      },
      async authorize(credentials) {
        const emailOrUsername = credentials?.emailOrUsername || credentials?.email;
        if (!emailOrUsername || !credentials?.password) {
          console.log('Missing email/username or password');
          return null;
        }

        try {
          // Check if this is an admin login attempt (from admin login page)
          const isAdminLogin = credentials.isAdmin === 'true';
          console.log('Admin login attempt:', isAdminLogin, 'for emailOrUsername:', emailOrUsername);

          // Get user from database
          console.log('Querying database for user with email or username:', emailOrUsername);

          // Check if input is email or username
          const isEmail = emailOrUsername.includes('@');
          const users = await executeQuery<any[]>(
            isEmail
              ? "SELECT * FROM users WHERE email = ?"
              : "SELECT * FROM users WHERE username = ? OR email = ?",
            isEmail
              ? [emailOrUsername]
              : [emailOrUsername, emailOrUsername]
          );

          console.log('Database query result:', JSON.stringify(users, null, 2));
          const user = users[0];

          if (!user) {
            console.log('User not found in database');
            return null;
          }

          // Check if user account is active
          if (user.status === 'suspended' || user.status === 'banned') {
            console.log('User account is suspended or banned:', user.status);
            return null;
          }

          if (!user.is_active) {
            console.log('User account is not active');
            return null;
          }

          // Verify password
          const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
          if (!isPasswordValid) {
            console.log('Invalid password for user:', credentials.email);
            return null;
          }

          // For admin login, check if user has admin privileges
          if (isAdminLogin) {
            console.log('Checking admin privileges for user:', user.email, 'isAdmin:', user.is_admin);
            if (!user.is_admin) {
              console.log('User does not have admin privileges');
              return null;
            }
          }

          console.log('Authentication successful for user:', user.email);

          const userData = {
            id: user.id,
            name: user.name,
            email: user.email,
            image: user.image,
            isAdmin: user.is_admin === 1 || user.is_admin === true, // Ensure boolean conversion
            role: user.role,
            adminRoleId: user.admin_role_id,
            status: user.status,
            isActive: user.is_active === 1 || user.is_active === true,
          };

          console.log('Returning user data from authorize:', JSON.stringify(userData, null, 2));
          return userData;
        } catch (error) {
          console.error("Error during authentication:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      // Reduced logging for performance - only log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Session callback - token data:', JSON.stringify(token, null, 2));
      }

      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.picture as string || token.image as string;

        // Add admin information to the session
        session.user.isAdmin = token.isAdmin as boolean;
        session.user.role = token.role as string;
        session.user.adminRoleId = token.adminRoleId as string;
        session.user.adminRoleName = token.adminRoleName as string;

        // Add user status information
        session.user.status = token.status as string;
        session.user.isActive = token.isActive as boolean;

        if (process.env.NODE_ENV === 'development') {
          console.log('Updated session:', JSON.stringify(session, null, 2));
        }
      }
      return session;
    },
    async jwt({ token, user, account, profile }) {
      // Initial sign in
      if (user) {
        if (process.env.NODE_ENV === 'development') {
          console.log('JWT callback - user data:', JSON.stringify(user, null, 2));
        }
        token.id = user.id;
        if (user.image) {
          token.image = user.image;
        }

        // Set admin properties directly from the user object first
        if (user.isAdmin !== undefined) {
          token.isAdmin = user.isAdmin;
          if (process.env.NODE_ENV === 'development') {
            console.log('Setting isAdmin from user object:', user.isAdmin);
          }
        }

        if (user.role !== undefined) {
          token.role = user.role;
        }

        if (user.adminRoleId !== undefined) {
          token.adminRoleId = user.adminRoleId;
        }

        if (user.status !== undefined) {
          token.status = user.status;
        }

        if (user.isActive !== undefined) {
          token.isActive = user.isActive;
        }

        // Handle OAuth sign-in (Google/GitHub)
        if (account?.provider === 'google' || account?.provider === 'github') {
          try {
            // Check if user exists in database
            const existingUsers = await executeQuery<any[]>(
              "SELECT * FROM users WHERE email = ?",
              [user.email]
            );

            if (existingUsers.length === 0) {
              // Create new user for OAuth sign-in
              const userId = require('uuid').v4();
              await executeQuery(
                "INSERT INTO users (id, name, email, image, createdAt, updatedAt) VALUES (?, ?, ?, ?, NOW(), NOW())",
                [userId, user.name, user.email, user.image]
              );

              token.id = userId;
              token.isAdmin = false;
              token.role = 'user';
              token.status = 'active';
              token.isActive = true;
            } else {
              // Update existing user
              const existingUser = existingUsers[0];
              token.id = existingUser.id;
              token.isAdmin = existingUser.is_admin === 1 || existingUser.is_admin === true;
              token.role = existingUser.role || 'user';
              token.adminRoleId = existingUser.admin_role_id;
              token.status = existingUser.status || 'active';
              token.isActive = existingUser.is_active === 1 || existingUser.is_active === true;
            }
          } catch (error) {
            console.error('Error handling OAuth sign-in:', error);
          }
        }

        // Fetch additional user data from the database
        try {
          console.log('Fetching user data from database for ID:', user.id);
          const userData = await db.query.users.findFirst({
            where: eq(users.id, user.id),
            with: {
              adminRole: true,
            },
          });

          console.log('User data from database:', JSON.stringify(userData, null, 2));

          if (userData) {
            // Only override if not already set from user object
            if (token.isAdmin === undefined) {
              token.isAdmin = userData.isAdmin || false;
              console.log('Setting isAdmin from database:', userData.isAdmin);
            }

            if (token.role === undefined) {
              token.role = userData.role || 'user';
            }

            if (token.adminRoleId === undefined) {
              token.adminRoleId = userData.adminRoleId || null;
            }

            token.adminRoleName = userData.adminRole?.name || null;

            // Always update status and isActive from database for real-time validation
            token.status = userData.status || 'active';
            token.isActive = userData.isActive || false;
          }
        } catch (error) {
          console.error('Error fetching user data for JWT:', error);
        }
      }

      console.log('Final JWT token:', JSON.stringify(token, null, 2));
      return token;
    },
  },
  secret: authConfig.nextAuthSecret,
};
