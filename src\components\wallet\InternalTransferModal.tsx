"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, ArrowRightIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { PinInputField } from "@/components/ui/PinInput";
import { internalTransferSchema, type InternalTransferFormData } from "@/lib/wallet/validation";
import { toast } from "react-hot-toast";

interface InternalTransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  earningBalance: string;
}

export function InternalTransferModal({
  isOpen,
  onClose,
  onSuccess,
  earningBalance
}: InternalTransferModalProps) {
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<InternalTransferFormData>({
    resolver: zodResolver(internalTransferSchema),
  });

  const amount = watch("amount");
  const pin = watch("pin");

  const onSubmit = async (data: InternalTransferFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/wallet/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          pin: data.pin,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Transfer completed successfully!');
        onSuccess();
        handleClose();
      } else {
        toast.error(result.message || 'Failed to complete transfer');
      }
    } catch (error) {
      console.error('Error completing transfer:', error);
      toast.error('Failed to complete transfer');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const availableBalance = parseFloat(earningBalance);
  const transferAmount = parseFloat(amount || "0");
  const isValidAmount = transferAmount > 0 && transferAmount <= availableBalance;

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full bg-white rounded-lg shadow-xl">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Transfer Between Wallets
            </DialogTitle>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Transfer Direction */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-green-600 font-semibold">E</span>
                  </div>
                  <div className="text-sm font-medium text-gray-900">Earning Wallet</div>
                  <div className="text-xs text-gray-500">${availableBalance.toFixed(2)} available</div>
                </div>

                <div className="flex-1 flex justify-center">
                  <ArrowRightIcon className="h-6 w-6 text-gray-400" />
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-blue-600 font-semibold">G</span>
                  </div>
                  <div className="text-sm font-medium text-gray-900">General Wallet</div>
                  <div className="text-xs text-gray-500">Primary wallet</div>
                </div>
              </div>
            </div>

            {/* Amount Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transfer Amount
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <Input
                  {...register("amount")}
                  type="number"
                  step="0.01"
                  min="1"
                  max={earningBalance}
                  placeholder="0.00"
                  className="pl-7"
                />
              </div>
              {errors.amount && (
                <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
              )}
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Min: $1.00</span>
                <span>Available: ${availableBalance.toFixed(2)}</span>
              </div>

              {/* Quick Amount Buttons */}
              <div className="flex space-x-2 mt-2">
                {[25, 50, 75, 100].map((percentage) => {
                  const quickAmount = (availableBalance * percentage) / 100;
                  if (quickAmount >= 1) {
                    return (
                      <button
                        key={percentage}
                        type="button"
                        onClick={() => setValue("amount", quickAmount.toFixed(2))}
                        className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                      >
                        {percentage}%
                      </button>
                    );
                  }
                  return null;
                })}
              </div>
            </div>

            {/* Transfer Summary */}
            {amount && isValidAmount && (
              <div className="bg-blue-50 rounded-lg p-4 space-y-2">
                <h4 className="font-medium text-blue-900">Transfer Summary</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">Transfer Amount:</span>
                    <span className="font-medium text-blue-900">${transferAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">Transfer Fee:</span>
                    <span className="font-medium text-blue-900">$0.00</span>
                  </div>
                  <div className="flex justify-between border-t border-blue-200 pt-1">
                    <span className="text-blue-900 font-medium">Total:</span>
                    <span className="font-bold text-blue-900">${transferAmount.toFixed(2)}</span>
                  </div>
                </div>
                <div className="text-xs text-blue-600 mt-2">
                  This amount will be moved from your Earning Wallet to your General Wallet instantly.
                </div>
              </div>
            )}

            {/* Insufficient Balance Warning */}
            {amount && !isValidAmount && transferAmount > availableBalance && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="text-sm text-red-800">
                  Insufficient balance. You can transfer up to ${availableBalance.toFixed(2)}.
                </div>
              </div>
            )}

            {/* PIN Input */}
            <PinInputField
              label="Enter your wallet PIN"
              description="Enter your 4-digit PIN to confirm this transfer"
              value={pin || ""}
              onChange={(value) => setValue("pin", value)}
              error={errors.pin?.message}
              required
              length={4}
              autoFocus
            />

            {/* Submit Button */}
            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="flex-1"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1"
                disabled={loading || !isValidAmount || !pin || pin.length < 4}
                isLoading={loading}
              >
                {loading ? 'Transferring...' : 'Transfer Now'}
              </Button>
            </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
