import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  wallets,
  walletTransactions,
  blogs,
  blogMonetization,
  referrals,
  referralCommissions
} from "@/lib/db/schema";
import { eq, and, or, sum, count, desc, gte, sql } from "drizzle-orm";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get wallet data
    const wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    // Get total earnings from earning wallet transactions
    const totalEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.userId, userId),
          eq(walletTransactions.walletType, 'earning'),
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      );

    // Get pending payouts (withdraw/cashout requests that are pending)
    const pendingPayoutsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.userId, userId),
          eq(walletTransactions.walletType, 'earning'),
          or(
            eq(walletTransactions.type, 'withdraw'),
            eq(walletTransactions.type, 'cashout')
          ),
          eq(walletTransactions.status, 'pending')
        )
      );

    // Get blog monetization stats
    const blogStats = await db
      .select({
        totalBlogs: count(),
        totalEarnings: sum(blogMonetization.totalEarnings),
      })
      .from(blogs)
      .leftJoin(blogMonetization, eq(blogs.id, blogMonetization.blogId))
      .where(eq(blogs.authorId, userId));

    // Get monetized blogs count
    const monetizedBlogsResult = await db
      .select({ count: count() })
      .from(blogs)
      .innerJoin(blogMonetization, eq(blogs.id, blogMonetization.blogId))
      .where(
        and(
          eq(blogs.authorId, userId),
          eq(blogMonetization.isApproved, true)
        )
      );

    // Get blog earnings specifically (using metadata to identify blog earnings)
    const blogEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.userId, userId),
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          sql`JSON_EXTRACT(metadata, '$.blogId') IS NOT NULL`
        )
      );

    // Get referral earnings from referralCommissions table (primary source)
    // Only count paid commissions
    const referralEarningsFromCommissionsResult = await db
      .select({ total: sum(referralCommissions.commissionAmount) })
      .from(referralCommissions)
      .where(
        and(
          eq(referralCommissions.referrerId, userId),
          eq(referralCommissions.status, 'paid')
        )
      );

    // Get referral earnings from wallet transactions (backup method)
    const referralEarningsFromWalletResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.userId, userId),
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          sql`JSON_EXTRACT(metadata, '$.type') = 'referral_commission'`
        )
      );

    // Get referral statistics
    const totalReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.referrerId, userId));

    const activeReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(
        and(
          eq(referrals.referrerId, userId),
          eq(referrals.status, 'completed')
        )
      );



    // Get recent earning transactions
    const recentTransactions = await db.query.walletTransactions.findMany({
      where: and(
        eq(walletTransactions.userId, userId),
        eq(walletTransactions.walletType, 'earning')
      ),
      orderBy: [desc(walletTransactions.createdAt)],
      limit: 10,
    });

    // Calculate totals
    const totalEarnings = parseFloat(totalEarningsResult[0]?.total || '0');
    const pendingPayouts = parseFloat(pendingPayoutsResult[0]?.total || '0');
    const availableBalance = parseFloat(wallet?.earningBalance || '0');
    const blogEarnings = parseFloat(blogEarningsResult[0]?.total || '0');

    // Calculate referral earnings from commissions table (primary source)
    const referralEarningsFromCommissions = parseFloat(referralEarningsFromCommissionsResult[0]?.total || '0');
    const referralEarningsFromWallet = parseFloat(referralEarningsFromWalletResult[0]?.total || '0');

    // Use commission table data as primary source for referral earnings
    const referralEarnings = referralEarningsFromCommissions > 0 ? referralEarningsFromCommissions : referralEarningsFromWallet;

    // Subscription commissions are the same as referral commissions in this system
    const subscriptionCommissions = referralEarningsFromCommissions;

    const totalReferrals = totalReferralsResult[0]?.count || 0;
    const activeReferrals = activeReferralsResult[0]?.count || 0;

    // Format response data
    const overviewData = {
      totalEarnings,
      pendingPayouts,
      availableBalance,
      blogEarnings,
      referralEarnings,
      subscriptionCommissions,
      totalBlogs: blogStats[0]?.totalBlogs || 0,
      monetizedBlogs: monetizedBlogsResult[0]?.count || 0,
      totalReferrals: totalReferrals,
      activeReferrals: activeReferrals,
      currency: "USD",
    };

    // Format recent transactions
    const formattedTransactions = recentTransactions.map(transaction => ({
      id: transaction.id,
      type: parseFloat(transaction.amount) > 0 ? 'credit' : 'debit',
      amount: Math.abs(parseFloat(transaction.amount)),
      description: transaction.description || transaction.note || getTransactionDescription(transaction.type),
      date: new Date(transaction.createdAt).toLocaleDateString(),
      status: transaction.status,
      source: transaction.note,
    }));

    return NextResponse.json({
      success: true,
      data: {
        overview: overviewData,
        recentTransactions: formattedTransactions,
      },
    });

  } catch (error) {
    console.error("Error fetching earning overview:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch earning overview" 
      },
      { status: 500 }
    );
  }
}

// Helper function to get transaction description
function getTransactionDescription(type: string): string {
  switch (type) {
    case 'earning':
      return 'Earning transaction';
    case 'withdraw':
      return 'Withdrawal to bank account';
    case 'internal_transfer':
      return 'Transfer to general wallet';
    case 'deposit':
      return 'Wallet deposit';
    case 'send':
      return 'Money sent';
    case 'receive':
      return 'Money received';
    default:
      return 'Wallet transaction';
  }
}
