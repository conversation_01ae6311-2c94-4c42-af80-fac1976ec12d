# কাস্টম বিলিং সাইকেল ফিচার

এই ফিচারটি সাবস্ক্রিপশন প্লানে কাস্টম বিলিং সাইকেল যুক্ত করার সুবিধা প্রদান করে।

## ✨ নতুন ফিচার সমূহ

### 1. কাস্টম বিলিং পিরিয়ড
- **কাস্টম মাস**: ০-১২০ মাস পর্যন্ত সেট করা যায়
- **কাস্টম বছর**: ০-১০ বছর পর্যন্ত সেট করা যায়
- যদি উভয় ফিল্ড ০ থাকে, তাহলে ডিফল্ট বিলিং সাইকেল (monthly/yearly) ব্যবহার হবে

### 2. স্মার্ট প্লান হাইডিং
- যেসব প্লানের সব গুরুত্বপূর্ণ ফিল্ড খালি বা ০, সেগুলো ফ্রন্টএন্ডে হাইড থাকবে
- এটি অসম্পূর্ণ বা টেস্ট প্লান লুকিয়ে রাখতে সাহায্য করে

## 🔧 ইনস্টলেশন

### ১. ডাটাবেস মাইগ্রেশন চালান
```bash
npm run ts-node src/scripts/add-custom-billing-fields.ts
```

### ২. অ্যাপ্লিকেশন রিস্টার্ট করুন
```bash
npm run dev
```

## 📋 ব্যবহারের নির্দেশনা

### অ্যাডমিন প্যানেলে প্লান তৈরি/এডিট করা

1. **অ্যাডমিন প্যানেল** → **Subscriptions** → **Plans** → **New Plan** বা **Edit Plan**
2. **Custom Billing Period** সেকশনে:
   - **Custom Months**: কাস্টম মাস সংখ্যা (যেমন: ৩ মাসের জন্য ৩)
   - **Custom Years**: কাস্টম বছর সংখ্যা (যেমন: ২ বছরের জন্য ২)
3. **উভয় ফিল্ড ০ রাখলে** ডিফল্ট বিলিং সাইকেল ব্যবহার হবে

### উদাহরণ

#### ৩ মাসের প্লান
- Custom Months: `3`
- Custom Years: `0`
- ফলাফল: "$9.99/3 months"

#### ২ বছরের প্লান
- Custom Months: `0`
- Custom Years: `2`
- ফলাফল: "$99.99/2 years"

#### ৬ মাসের প্লান
- Custom Months: `6`
- Custom Years: `0`
- ফলাফল: "$19.99/6 months"

## 🎯 প্রাইওরিটি সিস্টেম

কাস্টম বিলিং এর প্রাইওরিটি:
1. **Custom Years** (সর্বোচ্চ প্রাইওরিটি)
2. **Custom Months**
3. **Default Billing Cycle** (সর্বনিম্ন প্রাইওরিটি)

## 🔍 প্লান হাইডিং লজিক

একটি প্লান হাইড হবে যদি:
- Price = 0
- Custom Billing Months = 0
- Custom Billing Years = 0
- Max Posts = 0 বা -1
- Max Storage = 0 বা -1
- Max Groups = 0 বা -1
- সব বুলিয়ান ফিচার false
- Features array খালি

## 🛠️ টেকনিক্যাল ডিটেইলস

### ডাটাবেস স্কিমা
```sql
ALTER TABLE subscription_plans 
ADD COLUMN customBillingMonths INT DEFAULT 0;

ALTER TABLE subscription_plans 
ADD COLUMN customBillingYears INT DEFAULT 0;
```

### API এন্ডপয়েন্ট
- `POST /api/admin/subscriptions/plans` - নতুন প্লান তৈরি
- `PUT /api/admin/subscriptions/plans/[id]` - প্লান আপডেট

### ভ্যালিডেশন
- Custom Months: 0-120 (০-১০ বছর)
- Custom Years: 0-10 (০-১০ বছর)

## 🚀 ফিচার ব্যবহারের সুবিধা

1. **ফ্লেক্সিবিলিটি**: যেকোনো সময়ের জন্য প্লান তৈরি করা যায়
2. **মার্কেটিং**: বিশেষ অফার (৩ মাস, ৬ মাস) তৈরি করা যায়
3. **ক্লিন UI**: অসম্পূর্ণ প্লান স্বয়ংক্রিয়ভাবে লুকানো থাকে
4. **ব্যাকওয়ার্ড কম্প্যাটিবিলিটি**: পুরাতন প্লানগুলো ঠিকভাবে কাজ করবে

## 🔄 আপগ্রেড পাথ

বিদ্যমান প্লানগুলো স্বয়ংক্রিয়ভাবে:
- `customBillingMonths = 0`
- `customBillingYears = 0`
- ডিফল্ট বিলিং সাইকেল ব্যবহার করবে

## 📞 সাপোর্ট

কোনো সমস্যা হলে অ্যাডমিন প্যানেলের লগ চেক করুন বা ডেভেলপার টিমের সাথে যোগাযোগ করুন।
