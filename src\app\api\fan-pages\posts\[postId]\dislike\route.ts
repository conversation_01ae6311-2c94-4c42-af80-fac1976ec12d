import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePostLikes, fanPagePosts } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    postId: string;
  }>;
}

// POST /api/fan-pages/posts/[postId]/dislike - Toggle dislike on fan page post
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the post exists
    const postResult = await db
      .select({
        id: fanPagePosts.id,
        fanPageId: fanPagePosts.fanPageId,
        likeCount: fanPagePosts.likeCount,
      })
      .from(fanPagePosts)
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    if (postResult.length === 0) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    const post = postResult[0];

    // Check if user has already disliked this post
    const existingDislike = await db
      .select()
      .from(fanPagePostLikes)
      .where(
        and(
          eq(fanPagePostLikes.fanPagePostId, postId),
          eq(fanPagePostLikes.userId, session.user.id),
          eq(fanPagePostLikes.type, 'angry') // Using 'angry' as dislike
        )
      )
      .limit(1);

    let action: 'disliked' | 'undisliked';
    let newLikeCount: number = post.likeCount;

    if (existingDislike.length > 0) {
      // Remove dislike
      await db
        .delete(fanPagePostLikes)
        .where(
          and(
            eq(fanPagePostLikes.fanPagePostId, postId),
            eq(fanPagePostLikes.userId, session.user.id),
            eq(fanPagePostLikes.type, 'angry')
          )
        );

      action = 'undisliked';
    } else {
      // Check if user has liked this post and remove like
      const existingLike = await db
        .select()
        .from(fanPagePostLikes)
        .where(
          and(
            eq(fanPagePostLikes.fanPagePostId, postId),
            eq(fanPagePostLikes.userId, session.user.id),
            eq(fanPagePostLikes.type, 'like')
          )
        )
        .limit(1);

      if (existingLike.length > 0) {
        // Remove existing like
        await db
          .delete(fanPagePostLikes)
          .where(
            and(
              eq(fanPagePostLikes.fanPagePostId, postId),
              eq(fanPagePostLikes.userId, session.user.id),
              eq(fanPagePostLikes.type, 'like')
            )
          );

        // Decrease like count
        newLikeCount = Math.max(0, post.likeCount - 1);
        await db
          .update(fanPagePosts)
          .set({ likeCount: newLikeCount })
          .where(eq(fanPagePosts.id, postId));
      }

      // Add dislike
      await db.insert(fanPagePostLikes).values({
        id: uuidv4(),
        userId: session.user.id,
        fanPagePostId: postId,
        type: 'angry', // Using 'angry' as dislike
      });

      action = 'disliked';
    }

    return NextResponse.json({
      success: true,
      action,
      likeCount: newLikeCount,
    });

  } catch (error) {
    console.error("Error toggling dislike on fan page post:", error);
    return NextResponse.json(
      { error: "Failed to toggle dislike" },
      { status: 500 }
    );
  }
}
