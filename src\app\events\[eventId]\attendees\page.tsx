"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Input } from "@/components/ui/Input";
import { ChevronLeftIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";

interface Attendee {
  id: string;
  status: "going" | "interested" | "not_going";
  createdAt: string;
  user: {
    id: string;
    name: string;
    username: string | null;
    image: string | null;
  };
}

export default function EventAttendeesPage() {
  // Get the eventId from the URL params
  const params = useParams();
  const eventId = params?.eventId as string;
  const router = useRouter();

  const [event, setEvent] = useState<any>(null);
  const [attendees, setAttendees] = useState<Attendee[]>([]);
  const [filteredAttendees, setFilteredAttendees] = useState<Attendee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<"going" | "interested" | "not_going">("going");

  useEffect(() => {
    const fetchEventAndAttendees = async () => {
      if (!eventId) {
        setError("Event ID is missing");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log(`Fetching event with ID: ${eventId}`);
        // Fetch event details
        const eventResponse = await fetch(`/api/events/${eventId}`);

        if (!eventResponse.ok) {
          if (eventResponse.status === 404) {
            throw new Error("Event not found");
          }
          const errorData = await eventResponse.json().catch(() => ({}));
          throw new Error(errorData.message || "Failed to fetch event");
        }

        const eventData = await eventResponse.json();
        console.log("Event data received:", eventData);
        setEvent(eventData);

        // Fetch attendees
        console.log(`Fetching attendees for event: ${eventId}, status: ${activeTab}`);
        const attendeesResponse = await fetch(`/api/events/${eventId}/attendees?status=${activeTab}`);

        if (!attendeesResponse.ok) {
          const errorData = await attendeesResponse.json().catch(() => ({}));
          throw new Error(errorData.message || "Failed to fetch attendees");
        }

        const attendeesData = await attendeesResponse.json();
        console.log("Attendees data received:", attendeesData);
        setAttendees(attendeesData);
        setFilteredAttendees(attendeesData);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError(err instanceof Error ? err.message : "Failed to load data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventAndAttendees();
  }, [eventId, activeTab]);

  useEffect(() => {
    // Filter attendees based on search query
    if (searchQuery.trim() === "") {
      setFilteredAttendees(attendees);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = attendees.filter(
        (attendee) =>
          attendee.user.name.toLowerCase().includes(query) ||
          (attendee.user.username && attendee.user.username.toLowerCase().includes(query))
      );
      setFilteredAttendees(filtered);
    }
  }, [searchQuery, attendees]);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="flex justify-center py-20">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !event) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-red-50 p-6 text-red-800">
            <h2 className="text-lg font-semibold">Error</h2>
            <p className="mt-2">{error || "Failed to load event"}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => router.push("/events")}
            >
              <ChevronLeftIcon className="h-5 w-5 mr-1" />
              Back to Events
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(`/events/${eventId}`)}
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Back to Event
          </Button>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">
              Attendees for {event.name}
            </h1>

            <div className="mt-6 border-b border-gray-200">
              <div className="flex space-x-8">
                <button
                  className={`pb-4 px-1 ${
                    activeTab === "going"
                      ? "border-b-2 border-blue-500 text-blue-600 font-medium"
                      : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                  onClick={() => setActiveTab("going")}
                >
                  Going ({event.attendeeCounts.going})
                </button>
                <button
                  className={`pb-4 px-1 ${
                    activeTab === "interested"
                      ? "border-b-2 border-blue-500 text-blue-600 font-medium"
                      : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                  onClick={() => setActiveTab("interested")}
                >
                  Interested ({event.attendeeCounts.interested})
                </button>
                <button
                  className={`pb-4 px-1 ${
                    activeTab === "not_going"
                      ? "border-b-2 border-blue-500 text-blue-600 font-medium"
                      : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                  onClick={() => setActiveTab("not_going")}
                >
                  Not Going ({event.attendeeCounts.not_going})
                </button>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="mb-6">
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  type="search"
                  placeholder="Search attendees..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {filteredAttendees.length === 0 ? (
              <div className="text-center py-10">
                <p className="text-gray-500">
                  {searchQuery
                    ? "No attendees found matching your search."
                    : `No one is ${activeTab === "going" ? "going to" : activeTab === "interested" ? "interested in" : "not going to"} this event yet.`}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredAttendees.map((attendee) => (
                  <div
                    key={attendee.id}
                    className="flex items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50"
                  >
                    <div className="flex-shrink-0 mr-4">
                      {attendee.user.image ? (
                        <Image
                          src={attendee.user.image}
                          alt={attendee.user.name}
                          width={48}
                          height={48}
                          className="h-12 w-12 rounded-full"
                        />
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500 text-sm font-medium">
                            {attendee.user.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <Link
                        href={`/user/${attendee.user.username || attendee.user.id}`}
                        className="text-sm font-medium text-gray-900 hover:underline"
                      >
                        {attendee.user.name}
                      </Link>
                      {attendee.user.username && (
                        <p className="text-xs text-gray-500">
                          @{attendee.user.username}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
