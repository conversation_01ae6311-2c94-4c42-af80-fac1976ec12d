@import "tailwindcss";
@import "../styles/notifications.css";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: system-ui, monospace;
}

html {
  scroll-behavior: smooth;
}

/* Prevent HeadlessUI from hiding scrollbar when Menu dropdowns are opened */
html:has([role="menu"]) {
  overflow: auto !important;
  padding-right: 0 !important;
}

/* Fallback for browsers that don't support :has() */
@supports not (selector(:has(*))) {
  /* Override HeadlessUI's automatic overflow hidden for all cases except when explicitly needed */
  html {
    overflow: auto !important;
  }

  /* Only hide overflow when a modal dialog is present */
  html[data-headlessui-state~="open"] {
    overflow: auto !important;
    padding-right: 0 !important;
  }
}

/* Allow scrollbar hiding only for Dialog components (modals) */
html:has([role="dialog"]) {
  overflow: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Enhanced Like/Dislike Button Animations */
@keyframes likePress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1.05); }
}

@keyframes dislikePress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1.05); }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.1); }
  28% { transform: scale(1); }
  42% { transform: scale(1.1); }
  70% { transform: scale(1); }
}

.like-animation {
  animation: likePress 0.25s ease-out, heartBeat 0.6s ease-in-out 0.25s;
}

.dislike-animation {
  animation: dislikePress 0.25s ease-out;
}

.ripple-effect {
  animation: ripple 0.6s ease-out;
}

/* Smooth transitions for all interactive elements */
.smooth-transition {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced button hover effects */
.button-hover-scale:hover {
  transform: scale(1.02);
}

.button-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar utilities */
.scrollbar-none {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

.scrollbar-none::-webkit-scrollbar {
  display: none;             /* Chrome, Safari and Opera */
}

.scrollbar-thin {
  scrollbar-width: thin;
}

/* Messages page specific styles */
.messages-container {
  height: calc(100vh - 280px);
  min-height: 500px;
}

.messages-chat-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  scroll-behavior: smooth;
}

.conversation-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.conversation-list-items {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* Custom scrollbar styles */
.messages-list::-webkit-scrollbar,
.conversation-list-items::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track,
.conversation-list-items::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb,
.conversation-list-items::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb:hover,
.conversation-list-items::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Conversation Info scrollbar */
.conversation-info-scroll {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.conversation-info-scroll::-webkit-scrollbar {
  width: 6px;
}

.conversation-info-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.conversation-info-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.conversation-info-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* PIN Setup Modal specific styles */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-enter {
  animation: modalSlideIn 0.3s ease-out;
}

/* PIN input focus animation */
@keyframes pinFocus {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pin-focus {
  animation: pinFocus 0.2s ease-in-out;
}

/* Custom animations for blog cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient text utilities */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Custom line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

/* Text shadow utility */
.shadow-text {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

/* FullCalendar Styles */
.fc .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.fc .fc-button {
  background-color: #f3f4f6;
  border-color: #e5e7eb;
  color: #374151;
}

.fc .fc-button:hover {
  background-color: #e5e7eb;
  border-color: #d1d5db;
  color: #111827;
}

.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.fc-event {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
}

.fc-event:hover {
  opacity: 0.9;
}

.fc-daygrid-day-number,
.fc-col-header-cell-cushion {
  color: #374151;
  text-decoration: none !important;
}

.fc-day-today {
  background-color: #eff6ff !important;
}

.fc-day-today .fc-daygrid-day-number {
  color: #3b82f6;
  font-weight: 600;
}

.fc-h-event .fc-event-title {
  font-weight: 500;
}

.fc-list-day-cushion {
  background-color: #f3f4f6 !important;
}

.fc-list-event:hover td {
  background-color: #eff6ff !important;
}

.fc-theme-standard .fc-list-day-cushion {
  background-color: #f9fafb;
}

.fc .fc-timegrid-slot {
  height: 2.5em;
}

.fc .fc-timegrid-axis-cushion,
.fc .fc-timegrid-slot-label-cushion {
  color: #6b7280;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .fc .fc-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }

  .fc .fc-toolbar-title {
    font-size: 1rem;
  }

  .fc .fc-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  /* Mobile-specific optimizations */
  body {
    font-size: 14px;
    line-height: 1.5;
  }

  /* Improve touch targets */
  button, a, input, select, textarea {
    min-height: 44px;
  }

  /* Optimize spacing for mobile */
  .space-y-5 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-3 > * + * {
    margin-top: 0.75rem !important;
  }

  /* Mobile-friendly padding */
  .mobile-padding {
    padding: 0.75rem;
  }

  /* Responsive text sizes */
  .mobile-text-sm {
    font-size: 0.875rem;
  }

  .mobile-text-xs {
    font-size: 0.75rem;
  }

  /* Mobile navigation optimizations */
  .mobile-nav-item {
    padding: 0.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
  }

  /* Mobile card optimizations */
  .mobile-card {
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Mobile image optimizations */
  .mobile-image {
    border-radius: 0.5rem;
    max-width: 100%;
    height: auto;
  }

  /* Wallet mobile optimizations */
  .wallet-mobile-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .wallet-mobile-card {
    padding: 0.75rem;
    border-radius: 0.75rem;
    min-height: auto;
  }

  .wallet-mobile-action {
    min-height: 44px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.5rem;
  }

  .wallet-mobile-balance {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .wallet-mobile-stats {
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  .wallet-mobile-stats-text {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }

  .wallet-mobile-stats-value {
    font-size: 1rem;
    font-weight: 600;
  }

  /* Mobile wallet page specific optimizations */
  .wallet-mobile-container {
    padding: 0.75rem;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .wallet-mobile-header {
    margin-bottom: 1rem;
  }

  .wallet-mobile-header h1 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.25rem !important;
  }

  .wallet-mobile-header p {
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
  }

  /* Mobile transaction optimizations */
  .transaction-mobile-card {
    padding: 0.75rem;
    border-radius: 0.75rem;
  }

  .transaction-mobile-icon {
    width: 1rem;
    height: 1rem;
  }

  .transaction-mobile-amount {
    font-size: 0.875rem;
    font-weight: 600;
  }

  /* Mobile button optimizations for wallet */
  .wallet-mobile-button {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.75rem;
    font-weight: 500;
  }

  /* Mobile modal optimizations */
  .wallet-mobile-modal {
    margin: 0.5rem;
    max-height: calc(100vh - 1rem);
    border-radius: 1rem;
  }

  /* Earning World mobile optimizations */
  .earning-mobile-container {
    padding: 0.75rem;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .earning-mobile-header {
    margin-bottom: 1rem;
  }

  .earning-mobile-header h1 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.25rem !important;
  }

  .earning-mobile-header p {
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
  }

  /* Mobile tab navigation */
  .earning-mobile-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .earning-mobile-tabs::-webkit-scrollbar {
    display: none;
  }

  .earning-mobile-tab {
    min-width: max-content;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.5rem;
  }

  /* Mobile earning cards */
  .earning-mobile-card {
    padding: 0.75rem;
    border-radius: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .earning-mobile-balance {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .earning-mobile-stats {
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  .earning-mobile-stats-text {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }

  .earning-mobile-stats-value {
    font-size: 1rem;
    font-weight: 600;
  }

  /* Mobile opportunity cards */
  .opportunity-mobile-card {
    padding: 0.75rem;
    border-radius: 0.75rem;
    min-height: auto;
  }

  .opportunity-mobile-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .opportunity-mobile-title {
    font-size: 0.875rem;
    line-height: 1.3;
  }

  .opportunity-mobile-description {
    font-size: 0.75rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Mobile transaction cards */
  .transaction-mobile-item {
    padding: 0.75rem;
    border-radius: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .transaction-mobile-amount {
    font-size: 0.875rem;
    font-weight: 600;
  }

  .transaction-mobile-description {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  /* Mobile referral section */
  .referral-mobile-banner {
    padding: 0.75rem;
    border-radius: 0.75rem;
  }

  .referral-mobile-code {
    font-size: 1rem;
    font-weight: 700;
  }

  /* Scrollbar hide utility */
  .scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Line clamp utility for mobile */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Typography optimizations for mobile */
  h1 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
  }

  h2 {
    font-size: 1.25rem !important;
    line-height: 1.4 !important;
  }

  h3 {
    font-size: 1.125rem !important;
    line-height: 1.4 !important;
  }

  /* Post content typography */
  .post-content {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Button text optimization */
  .btn-mobile {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }

  /* Form input optimization */
  input, textarea, select {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }

  /* Card spacing optimization */
  .card-mobile {
    margin-bottom: 0.75rem;
    padding: 0.75rem;
  }

  /* Navigation spacing */
  .nav-mobile {
    padding: 0.5rem;
  }

  /* Performance optimizations for mobile */
  * {
    -webkit-tap-highlight-color: transparent;
  }

  /* Smooth scrolling optimization */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* GPU acceleration for animations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* Optimize image loading */
  img {
    content-visibility: auto;
  }

  /* Optimize large content blocks */
  .content-block {
    content-visibility: auto;
    contain-intrinsic-size: 200px;
  }

  /* Mobile-specific animations */
  @keyframes mobileSlideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes mobileSlideDown {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes mobileFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .mobile-slide-up {
    animation: mobileSlideUp 0.3s ease-out;
  }

  .mobile-slide-down {
    animation: mobileSlideDown 0.3s ease-out;
  }

  .mobile-fade-in {
    animation: mobileFadeIn 0.2s ease-out;
  }

  /* Mobile pull-to-refresh styles */
  .pull-to-refresh-indicator {
    transform-origin: center;
    transition: transform 0.2s ease-out;
  }

  /* Mobile swipe feedback */
  .swipe-feedback {
    transition: transform 0.1s ease-out;
  }

  .swipe-feedback.swipe-left {
    transform: translateX(-10px);
  }

  .swipe-feedback.swipe-right {
    transform: translateX(10px);
  }

  /* Notification badge animations */
  @keyframes notificationPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
  }

  @keyframes notificationBounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      transform: translate3d(0, -8px, 0);
    }
    70% {
      transform: translate3d(0, -4px, 0);
    }
    90% {
      transform: translate3d(0, -2px, 0);
    }
  }

  @keyframes notificationShake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(2px);
    }
  }

  .notification-pulse {
    animation: notificationPulse 2s infinite;
  }

  .notification-bounce {
    animation: notificationBounce 0.6s ease-out;
  }

  .notification-shake {
    animation: notificationShake 0.5s ease-in-out;
  }

  /* High priority notification styles */
  .notification-high-priority {
    animation: notificationPulse 1s infinite;
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
  }

  /* Message notification styles */
  .notification-message {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    animation: notificationBounce 0.6s ease-out;
  }

  /* Notification preview animations */
  .notification-item-enter {
    animation: mobileSlideUp 0.3s ease-out;
  }

  .notification-item-exit {
    animation: mobileSlideDown 0.3s ease-out;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet-specific styles */
  .tablet-padding {
    padding: 1rem;
  }

  .tablet-text {
    font-size: 0.9rem;
  }
}
