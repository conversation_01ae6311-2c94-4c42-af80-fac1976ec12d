"use server";

import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptions, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";
import { revalidatePath } from "next/cache";

// Subscribe to a user
export async function subscribeToUser(targetUserId: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return { success: false, message: "Unauthorized" };
    }

    // Prevent subscribing to self
    if (!session.user.id || targetUserId === session.user.id) {
      return { success: false, message: "Cannot subscribe to yourself" };
    }

    // Check if already subscribed
    const existingSubscription = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, session.user.id),
        eq(subscriptions.targetUserId, targetUserId)
      ),
    });

    if (existingSubscription) {
      return {
        success: false,
        message: "Already subscribed to this user",
        subscriptionId: existingSubscription.id
      };
    }

    // Create the subscription
    const subscriptionId = uuidv4();
    await db.insert(subscriptions).values({
      id: subscriptionId,
      subscriberId: session.user.id,
      targetUserId: targetUserId,
    });

    // Create a notification for the target user
    await db.insert(notifications).values({
      id: uuidv4(),
      recipientId: targetUserId,
      type: "subscription",
      senderId: session.user.id,
      subscriptionId,
    });

    // Revalidate the user's profile page
    revalidatePath(`/user/[username]`);

    return {
      success: true,
      message: "Successfully subscribed",
      subscriptionId,
    };
  } catch (error) {
    console.error("Error subscribing to user:", error);
    return { success: false, message: "Failed to subscribe" };
  }
}

// Unsubscribe from a user
export async function unsubscribeFromUser(targetUserId: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return { success: false, message: "Unauthorized" };
    }

    // Find the subscription
    const subscription = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, session.user.id),
        eq(subscriptions.targetUserId, targetUserId)
      ),
    });

    if (!subscription) {
      return { success: false, message: "Not subscribed to this user" };
    }

    // Delete the subscription
    await db.delete(subscriptions).where(eq(subscriptions.id, subscription.id));

    // Delete any related notifications
    await db
      .delete(notifications)
      .where(eq(notifications.subscriptionId, subscription.id));

    // Revalidate the user's profile page
    revalidatePath(`/user/[username]`);

    return {
      success: true,
      message: "Successfully unsubscribed",
    };
  } catch (error) {
    console.error("Error unsubscribing from user:", error);
    return { success: false, message: "Failed to unsubscribe" };
  }
}

// Get subscription status between two users
export async function getSubscriptionStatus(targetUserId: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return { success: false, message: "Unauthorized" };
    }

    // Check if current user is subscribed to target user
    const isSubscribed = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, session.user.id),
        eq(subscriptions.targetUserId, targetUserId)
      ),
    });

    // Check if target user is subscribed to current user (mutual subscription)
    const isSubscribedBack = await db.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.subscriberId, targetUserId),
        eq(subscriptions.targetUserId, session.user.id)
      ),
    });

    return {
      success: true,
      isSubscribed: !!isSubscribed,
      isSubscribedBack: !!isSubscribedBack,
      subscriptionId: isSubscribed?.id || null,
    };
  } catch (error) {
    console.error("Error getting subscription status:", error);
    return { success: false, message: "Failed to get subscription status" };
  }
}

// Get user's subscriptions (following)
export async function getUserSubscriptions(userId?: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return { success: false, message: "Unauthorized" };
    }

    const targetUserId = userId || session.user.id;

    const userSubscriptions = await db.query.subscriptions.findMany({
      where: eq(subscriptions.subscriberId, targetUserId),
      with: {
        targetUser: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
      orderBy: (subscriptions, { desc }) => [desc(subscriptions.createdAt)],
    });

    return {
      success: true,
      subscriptions: userSubscriptions,
    };
  } catch (error) {
    console.error("Error getting user subscriptions:", error);
    return { success: false, message: "Failed to get subscriptions" };
  }
}

// Get user's subscribers
export async function getUserSubscribers(userId?: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return { success: false, message: "Unauthorized" };
    }

    const targetUserId = userId || session.user.id;

    const userSubscribers = await db.query.subscriptions.findMany({
      where: eq(subscriptions.targetUserId, targetUserId),
      with: {
        subscriber: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
      orderBy: (subscriptions, { desc }) => [desc(subscriptions.createdAt)],
    });

    return {
      success: true,
      subscribers: userSubscribers,
    };
  } catch (error) {
    console.error("Error getting user subscribers:", error);
    return { success: false, message: "Failed to get subscribers" };
  }
}
