import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, users, products } from "@/lib/db/schema";
import { eq, desc, count, sql } from "drizzle-orm";

// Get user's stores
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user's stores
    const userStores = await db.select({
      id: stores.id,
      name: stores.name,
      slug: stores.slug,
      description: stores.description,
      logo: stores.logo,
      banner: stores.banner,
      location: stores.location,
      phone: stores.phone,
      email: stores.email,
      website: stores.website,
      isVerified: stores.isVerified,
      createdAt: stores.createdAt,
      updatedAt: stores.updatedAt,
    })
    .from(stores)
    .where(eq(stores.ownerId, userId))
    .orderBy(desc(stores.createdAt))
    .limit(limit)
    .offset(offset);

    // Get product count for each store separately
    const storesWithProductCount = await Promise.all(
      userStores.map(async (store) => {
        try {
          const productCountResult = await db.select({ count: count() })
            .from(products)
            .where(eq(products.storeId, store.id));

          return {
            ...store,
            productCount: productCountResult[0]?.count || 0,
          };
        } catch (err) {
          console.error('Error processing store:', store, err);
          return {
            ...store,
            productCount: 0,
          };
        }
      })
    );

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(stores)
      .where(eq(stores.ownerId, userId));

    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      stores: storesWithProductCount,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user stores:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
