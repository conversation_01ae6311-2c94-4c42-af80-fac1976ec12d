"use client";

import React, { useState } from 'react';
import {
  XMarkIcon,
  CodeBracketIcon,
  DocumentDuplicateIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

interface CodeBlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (codeBlock: string) => void;
}

const PROGRAMMING_LANGUAGES = [
  { value: '', label: 'Plain Text' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'c', label: 'C' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'swift', label: 'Swift' },
  { value: 'kotlin', label: 'Kotlin' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'scss', label: 'SCSS' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'yaml', label: 'YAML' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'bash', label: 'Bash' },
  { value: 'shell', label: 'Shell' },
  { value: 'sql', label: 'SQL' },
  { value: 'dockerfile', label: 'Dockerfile' },
  { value: 'nginx', label: 'Nginx' },
  { value: 'apache', label: 'Apache' }
];

const CODE_EXAMPLES = {
  javascript: `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));`,
  
  python: `def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

print(fibonacci(10))`,
  
  typescript: `interface User {
  id: number;
  name: string;
  email: string;
}

const createUser = (userData: User): User => {
  return { ...userData };
};`,
  
  html: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Hello World</title>
</head>
<body>
    <h1>Hello, World!</h1>
</body>
</html>`,
  
  css: `.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}`,
  
  json: `{
  "name": "My Project",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.0.0",
    "typescript": "^4.9.0"
  },
  "scripts": {
    "start": "npm run dev",
    "build": "npm run build"
  }
}`
};

export const CodeBlockModal: React.FC<CodeBlockModalProps> = ({
  isOpen,
  onClose,
  onInsert
}) => {
  const [language, setLanguage] = useState('javascript');
  const [code, setCode] = useState(CODE_EXAMPLES.javascript);
  const [copied, setCopied] = useState(false);

  const handleLanguageChange = (newLanguage: string) => {
    setLanguage(newLanguage);
    if (CODE_EXAMPLES[newLanguage as keyof typeof CODE_EXAMPLES]) {
      setCode(CODE_EXAMPLES[newLanguage as keyof typeof CODE_EXAMPLES]);
    } else {
      setCode('// Your code here...');
    }
  };

  const handleInsert = () => {
    const codeBlock = language 
      ? `\`\`\`${language}\n${code}\n\`\`\``
      : `\`\`\`\n${code}\n\`\`\``;
    onInsert(codeBlock);
    onClose();
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const getLanguageColor = (lang: string) => {
    const colors: { [key: string]: string } = {
      javascript: 'bg-yellow-100 text-yellow-800',
      typescript: 'bg-blue-100 text-blue-800',
      python: 'bg-green-100 text-green-800',
      java: 'bg-orange-100 text-orange-800',
      cpp: 'bg-purple-100 text-purple-800',
      html: 'bg-red-100 text-red-800',
      css: 'bg-indigo-100 text-indigo-800',
      json: 'bg-gray-100 text-gray-800'
    };
    return colors[lang] || 'bg-gray-100 text-gray-800';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <CodeBracketIcon className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Insert Code Block</h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Editor Panel */}
          <div className="flex-1 flex flex-col">
            {/* Language Selector */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <label className="text-sm font-medium text-gray-700">
                    Language:
                  </label>
                  <select
                    value={language}
                    onChange={(e) => handleLanguageChange(e.target.value)}
                    className="border border-gray-300 rounded px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {PROGRAMMING_LANGUAGES.map((lang) => (
                      <option key={lang.value} value={lang.value}>
                        {lang.label}
                      </option>
                    ))}
                  </select>
                  {language && (
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getLanguageColor(language)}`}>
                      {PROGRAMMING_LANGUAGES.find(l => l.value === language)?.label}
                    </span>
                  )}
                </div>
                
                <button
                  onClick={copyToClipboard}
                  className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
                >
                  {copied ? (
                    <>
                      <CheckIcon className="h-4 w-4 text-green-600" />
                      <span className="text-green-600">Copied!</span>
                    </>
                  ) : (
                    <>
                      <DocumentDuplicateIcon className="h-4 w-4" />
                      <span>Copy</span>
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Code Editor */}
            <div className="flex-1 p-4">
              <textarea
                value={code}
                onChange={(e) => setCode(e.target.value)}
                placeholder="Enter your code here..."
                className="w-full h-full font-mono text-sm border border-gray-300 rounded-lg p-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                style={{ minHeight: '300px' }}
              />
            </div>
          </div>

          {/* Preview Panel */}
          <div className="w-1/3 border-l border-gray-200 bg-gray-50">
            <div className="p-4">
              <h4 className="font-medium text-gray-700 mb-3">Preview</h4>
              
              {/* Markdown Preview */}
              <div className="mb-4">
                <p className="text-xs text-gray-500 mb-2">Markdown:</p>
                <div className="bg-white border border-gray-200 rounded p-3">
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap font-mono">
                    {language ? `\`\`\`${language}\n${code}\n\`\`\`` : `\`\`\`\n${code}\n\`\`\``}
                  </pre>
                </div>
              </div>

              {/* Rendered Preview */}
              <div>
                <p className="text-xs text-gray-500 mb-2">Rendered:</p>
                <div className="bg-white border border-gray-200 rounded overflow-hidden">
                  {language && (
                    <div className="bg-gray-100 px-3 py-1 border-b border-gray-200 flex items-center justify-between">
                      <span className="text-xs font-medium text-gray-600">
                        {PROGRAMMING_LANGUAGES.find(l => l.value === language)?.label || 'Code'}
                      </span>
                      <span className={`px-2 py-0.5 rounded text-xs ${getLanguageColor(language)}`}>
                        {language}
                      </span>
                    </div>
                  )}
                  <pre className="p-3 text-sm font-mono text-gray-800 overflow-x-auto">
                    <code>{code}</code>
                  </pre>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={handleInsert}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Insert Code Block
          </button>
        </div>
      </div>
    </div>
  );
};

export default CodeBlockModal;
