import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogDislikes } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

// Toggle dislike on a blog
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { slug } = params;

    // First, get the blog to find its ID
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user has already disliked this blog
    const existingDislike = await db.query.blogDislikes.findFirst({
      where: and(
        eq(blogDislikes.blogId, blog.id),
        eq(blogDislikes.userId, session.user.id)
      ),
    });

    if (existingDislike) {
      // Remove dislike: remove the dislike
      await db.delete(blogDislikes).where(eq(blogDislikes.id, existingDislike.id));
      
      return NextResponse.json({
        message: "Blog dislike removed",
        disliked: false,
      });
    } else {
      // Add dislike
      const dislikeId = uuidv4();
      
      await db.insert(blogDislikes).values({
        id: dislikeId,
        blogId: blog.id,
        userId: session.user.id,
        createdAt: new Date(),
      });

      return NextResponse.json({
        message: "Blog disliked",
        disliked: true,
      });
    }
  } catch (error) {
    console.error("Error toggling blog dislike:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
