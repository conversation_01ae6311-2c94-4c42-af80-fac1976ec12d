import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, users } from "@/lib/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, desc, asc, count, sql, and, like, or } from "drizzle-orm";

const groupSchema = z.object({
  name: z.string().min(2).max(255),
  description: z.string().max(1000).optional(),
  visibility: z.enum(["public", "private-visible", "private-hidden"]).default("public"),
  category: z.string().max(100).optional(),
  rules: z.string().max(1000).optional(),
  postPermission: z.enum(["all-members", "admin-only"]).default("all-members"),
});

const querySchema = z.object({
  page: z.coerce.number().default(1),
  limit: z.coerce.number().default(10),
  search: z.string().optional().nullable().transform(val => val || undefined),
  visibility: z.string().optional().nullable().transform(val => val || undefined),
  category: z.string().optional().nullable().transform(val => val || undefined),
  sort: z.string().default("createdAt"),
  order: z.enum(["asc", "desc"]).default("desc"),
});

// Get all groups with admin filtering capabilities
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const query = querySchema.parse({
      page: url.searchParams.get("page") || 1,
      limit: url.searchParams.get("limit") || 10,
      search: url.searchParams.get("search"),
      visibility: url.searchParams.get("visibility"),
      category: url.searchParams.get("category"),
      sort: url.searchParams.get("sort") || "createdAt",
      order: url.searchParams.get("order") || "desc",
    });

    // Build where conditions
    const whereConditions = [];

    // Apply search filter
    if (query.search) {
      whereConditions.push(
        or(
          like(groups.name, `%${query.search}%`),
          like(groups.description, `%${query.search}%`)
        )
      );
    }

    // Apply visibility filter
    if (query.visibility && query.visibility !== "all") {
      // Validate visibility is one of the allowed enum values
      const validVisibilities = ['public', 'private-visible', 'private-hidden'] as const;

      if (validVisibilities.includes(query.visibility as any)) {
        whereConditions.push(eq(groups.visibility, query.visibility as typeof validVisibilities[number]));
      }
    }

    // Apply category filter
    if (query.category && query.category !== "all") {
      whereConditions.push(eq(groups.category, query.category));
    }

    // Count total groups for pagination
    const countQuery = db.select({
      count: sql`COUNT(DISTINCT ${groups.id})`.as('count')
    })
    .from(groups)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

    const totalCountResult = await countQuery.execute();
    const totalCount = Number(totalCountResult[0]?.count || 0);
    const totalPages = Math.ceil(totalCount / query.limit);

    // Build the main query
    const offset = (query.page - 1) * query.limit;

    // Build query with conditional ordering
    let baseQuery;

    if (query.sort === "memberCount") {
      // For memberCount sorting, we can't use orderBy easily, so we'll sort in memory later
      baseQuery = db.select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        visibility: groups.visibility,
        category: groups.category,
        rules: groups.rules,
        coverImage: groups.coverImage,
        postPermission: groups.postPermission,
        creatorId: groups.creatorId,
        createdAt: groups.createdAt,
        updatedAt: groups.updatedAt,
      })
      .from(groups)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .limit(query.limit)
      .offset(offset);
    } else {
      // For other sorts, we can use orderBy
      const validSortFields = ['id', 'name', 'createdAt', 'updatedAt'] as const;
      const sortField = validSortFields.includes(query.sort as any)
        ? groups[query.sort as 'id' | 'name' | 'createdAt' | 'updatedAt']
        : groups.createdAt; // default sort

      baseQuery = db.select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        visibility: groups.visibility,
        category: groups.category,
        rules: groups.rules,
        coverImage: groups.coverImage,
        postPermission: groups.postPermission,
        creatorId: groups.creatorId,
        createdAt: groups.createdAt,
        updatedAt: groups.updatedAt,
      })
      .from(groups)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(query.order === "asc" ? asc(sortField) : desc(sortField))
      .limit(query.limit)
      .offset(offset);
    }

    // Execute the query
    const results = await baseQuery.execute();

    // Format the response - get additional data separately
    const formattedGroups = await Promise.all(results.map(async (group) => {
      // Get member count
      const memberCountResult = await db.select({
        count: count().as('count')
      })
      .from(groupMembers)
      .where(eq(groupMembers.groupId, group.id))
      .execute();

      // Get creator info
      const creator = await db.query.users.findFirst({
        where: eq(users.id, group.creatorId),
        columns: {
          id: true,
          name: true,
          username: true,
          image: true,
        },
      });

      return {
        ...group,
        memberCount: Number(memberCountResult[0]?.count || 0),
        creator,
      };
    }));

    return NextResponse.json({
      groups: formattedGroups,
      pagination: {
        page: query.page,
        limit: query.limit,
        totalItems: totalCount,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching groups:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new group as admin
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = groupSchema.parse(body);

    // Create the group
    const groupId = uuidv4();
    await db.insert(groups).values({
      id: groupId,
      name: validatedData.name,
      description: validatedData.description || null,
      visibility: validatedData.visibility,
      category: validatedData.category || null,
      rules: validatedData.rules || null,
      coverImage: null,
      postPermission: validatedData.postPermission,
      creatorId: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Add the creator as an admin
    const membershipId = uuidv4();
    await db.insert(groupMembers).values({
      id: membershipId,
      groupId,
      userId: session.user.id,
      role: "admin",
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Get the created group without using joins to avoid collation issues
    const createdGroup = await db.select({
      id: groups.id,
      name: groups.name,
      description: groups.description,
      visibility: groups.visibility,
      category: groups.category,
      rules: groups.rules,
      coverImage: groups.coverImage,
      postPermission: groups.postPermission,
      creatorId: groups.creatorId,
      createdAt: groups.createdAt,
      updatedAt: groups.updatedAt,
    })
    .from(groups)
    .where(eq(groups.id, groupId))
    .limit(1)
    .execute();

    // Get creator info separately
    const creator = await db.select({
      id: users.id,
      name: users.name,
      username: users.username,
      image: users.image,
    })
    .from(users)
    .where(eq(users.id, session.user.id))
    .limit(1)
    .execute();

    // Combine the results
    const result = {
      ...createdGroup[0],
      creator: creator[0] || null,
    };

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Error creating group:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
