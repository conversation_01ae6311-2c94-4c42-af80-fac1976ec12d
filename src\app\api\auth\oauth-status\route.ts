import { NextResponse } from "next/server";
import { getOAuthConfig } from "@/lib/oauth-config";

export async function GET() {
  try {
    const oauthConfig = await getOAuthConfig();

    return NextResponse.json({
      google: {
        enabled: oauthConfig.googleOAuthEnabled &&
                 !!(oauthConfig.googleClientId && oauthConfig.googleClientSecret),
      },
      github: {
        enabled: oauthConfig.githubOAuthEnabled &&
                 !!(oauthConfig.githubClientId && oauthConfig.githubClientSecret),
      },
    });
  } catch (error) {
    console.error("Error fetching OAuth status:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
