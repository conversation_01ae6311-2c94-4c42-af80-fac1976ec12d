import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { userSubscriptions, subscriptionTransactions } from "@/lib/db/schema";
import { eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const bulkActionSchema = z.object({
  action: z.enum(['cancel', 'suspend', 'activate', 'delete']),
  subscriptionIds: z.array(z.string()).min(1),
  reason: z.string().optional(),
});

// POST - Perform bulk actions on subscriptions
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, subscriptionIds, reason } = bulkActionSchema.parse(body);

    // Verify all subscriptions exist
    const existingSubscriptions = await db.query.userSubscriptions.findMany({
      where: inArray(userSubscriptions.id, subscriptionIds),
    });

    if (existingSubscriptions.length !== subscriptionIds.length) {
      return NextResponse.json(
        {
          success: false,
          message: "Some subscriptions were not found",
        },
        { status: 404 }
      );
    }

    let updateData: any = {};
    let transactionType: 'payment' | 'refund' | 'upgrade' | 'downgrade' | 'cancellation' = 'cancellation';

    switch (action) {
      case 'cancel':
        updateData = {
          status: 'cancelled' as const,
          cancelledAt: new Date(),
          cancelReason: reason || 'Cancelled by admin',
          autoRenew: false,
        };
        transactionType = 'cancellation';
        break;

      case 'suspend':
        updateData = {
          status: 'suspended' as const,
          cancelReason: reason || 'Suspended by admin',
          autoRenew: false,
        };
        transactionType = 'cancellation';
        break;

      case 'activate':
        updateData = {
          status: 'active' as const,
          cancelledAt: null,
          cancelReason: null,
          autoRenew: true,
        };
        break;

      case 'delete':
        // For delete, we'll handle it separately
        break;

      default:
        return NextResponse.json(
          {
            success: false,
            message: "Invalid action",
          },
          { status: 400 }
        );
    }

    if (action === 'delete') {
      // Delete subscriptions (be careful with this in production)
      await db.delete(userSubscriptions).where(inArray(userSubscriptions.id, subscriptionIds));
      
      return NextResponse.json({
        success: true,
        message: `${subscriptionIds.length} subscription(s) deleted successfully`,
        processedCount: subscriptionIds.length,
      });
    } else {
      // Update subscriptions
      await db
        .update(userSubscriptions)
        .set(updateData)
        .where(inArray(userSubscriptions.id, subscriptionIds));

      // Create transaction records for tracking
      const transactionPromises = existingSubscriptions.map(async (subscription) => {
        const transactionId = uuidv4();
        
        return db.insert(subscriptionTransactions).values({
          id: transactionId,
          subscriptionId: subscription.id,
          userId: subscription.userId,
          planId: subscription.planId,
          type: transactionType,
          amount: '0.00', // No monetary transaction for admin actions
          currency: 'USD',
          status: 'completed',
          paymentGateway: 'admin',
          gatewayTransactionId: `admin_${Date.now()}`,
          description: `Bulk ${action} by admin: ${reason || 'No reason provided'}`,
          processedAt: new Date(),
          metadata: JSON.stringify({
            adminAction: true,
            adminUserId: session.user.id,
            bulkOperation: true,
            reason: reason || 'No reason provided',
          }),
        });
      });

      await Promise.all(transactionPromises);

      return NextResponse.json({
        success: true,
        message: `${subscriptionIds.length} subscription(s) ${action}ed successfully`,
        processedCount: subscriptionIds.length,
      });
    }
  } catch (error) {
    console.error("Error performing bulk action:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to perform bulk action",
      },
      { status: 500 }
    );
  }
}
