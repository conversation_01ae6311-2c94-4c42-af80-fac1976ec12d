import { NextRequest, NextResponse } from "next/server";
import { withSecurity } from "@/lib/security/api-wrapper";
import { validationSchemas } from "@/lib/security/validation";
import <PERSON><PERSON> from "joi";

// Example validation schema
const exampleSchema = Joi.object({
  title: Joi.string().min(3).max(100).required(),
  content: Joi.string().min(10).max(1000).required(),
  category: Joi.string().valid('tech', 'business', 'lifestyle').optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(5).optional(),
});

// Secure GET handler
export const GET = withSecurity(
  async ({ user, request }) => {
    try {
      // Example: Get user's posts
      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '20');
      
      // Validate pagination parameters
      if (page < 1 || limit < 1 || limit > 100) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Invalid pagination parameters' 
          },
          { status: 400 }
        );
      }

      // Mock data for example
      const mockData = {
        posts: [
          {
            id: '1',
            title: 'Example Post',
            content: 'This is a secure API example',
            userId: user?.id,
            createdAt: new Date().toISOString(),
          }
        ],
        pagination: {
          page,
          limit,
          total: 1,
          totalPages: 1,
        }
      };

      return NextResponse.json({
        success: true,
        data: mockData,
      });

    } catch (error) {
      console.error('GET /api/secure-example error:', error);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to fetch data' 
        },
        { status: 500 }
      );
    }
  },
  {
    requireAuth: true,
    allowedMethods: ['GET'],
  }
);

// Secure POST handler with validation
export const POST = withSecurity(
  async ({ user, validatedData }) => {
    try {
      // validatedData is already sanitized and validated
      const { title, content, category, tags } = validatedData;

      // Mock creation logic
      const newPost = {
        id: Math.random().toString(36).substring(7),
        title,
        content,
        category,
        tags,
        userId: user?.id,
        createdAt: new Date().toISOString(),
      };

      return NextResponse.json({
        success: true,
        data: newPost,
        message: 'Post created successfully',
      }, { status: 201 });

    } catch (error) {
      console.error('POST /api/secure-example error:', error);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to create post' 
        },
        { status: 500 }
      );
    }
  },
  {
    requireAuth: true,
    validationSchema: exampleSchema,
    allowedMethods: ['POST'],
  }
);

// Admin-only PUT handler
export const PUT = withSecurity(
  async ({ user, validatedData }) => {
    try {
      const { title, content, category, tags } = validatedData;

      // Mock update logic
      const updatedPost = {
        id: '1',
        title,
        content,
        category,
        tags,
        userId: user?.id,
        updatedAt: new Date().toISOString(),
      };

      return NextResponse.json({
        success: true,
        data: updatedPost,
        message: 'Post updated successfully',
      });

    } catch (error) {
      console.error('PUT /api/secure-example error:', error);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to update post' 
        },
        { status: 500 }
      );
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    validationSchema: exampleSchema,
    allowedMethods: ['PUT'],
  }
);

// Admin-only DELETE handler
export const DELETE = withSecurity(
  async ({ user, request }) => {
    try {
      const url = new URL(request.url);
      const postId = url.searchParams.get('id');

      if (!postId) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Post ID is required' 
          },
          { status: 400 }
        );
      }

      // Mock deletion logic
      return NextResponse.json({
        success: true,
        message: 'Post deleted successfully',
      });

    } catch (error) {
      console.error('DELETE /api/secure-example error:', error);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to delete post' 
        },
        { status: 500 }
      );
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    allowedMethods: ['DELETE'],
  }
);
