import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { walletTransactions, users } from "@/lib/db/schema";
import { eq, desc, and, like, gte, lte, count, or } from "drizzle-orm";
import { z } from "zod";

const querySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(20),
  search: z.string().optional(),
  status: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sort: z.string().optional().default("createdAt"),
  order: z.string().optional().default("desc"),
});

// Get all withdrawal requests for admin
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const searchParams = Object.fromEntries(url.searchParams);
    const validatedParams = querySchema.parse(searchParams);

    const {
      page,
      limit,
      search,
      status,
      dateFrom,
      dateTo,
      sort,
      order,
    } = validatedParams;

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(walletTransactions.type, 'withdraw')
    ];

    if (search) {
      whereConditions.push(
        or(
          like(users.name, `%${search}%`),
          like(users.username, `%${search}%`),
          like(users.email, `%${search}%`),
          like(walletTransactions.reference, `%${search}%`)
        )
      );
    }

    if (status) {
      whereConditions.push(eq(walletTransactions.status, status as any));
    }

    if (dateFrom) {
      whereConditions.push(gte(walletTransactions.createdAt, new Date(dateFrom)));
    }

    if (dateTo) {
      whereConditions.push(lte(walletTransactions.createdAt, new Date(dateTo)));
    }

    // Determine sort column and order
    const sortColumn = walletTransactions[sort as keyof typeof walletTransactions] || walletTransactions.createdAt;
    const orderBy = order === 'asc' ? sortColumn : desc(sortColumn);

    // Get withdrawal requests with user details
    const withdrawals = await db.query.walletTransactions.findMany({
      where: whereConditions.length > 0 ? and(...whereConditions) : undefined,
      orderBy: [orderBy],
      limit,
      offset,
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(walletTransactions)
      .leftJoin(users, eq(walletTransactions.userId, users.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Get summary statistics
    const pendingCount = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(and(
        eq(walletTransactions.type, 'withdraw'),
        eq(walletTransactions.status, 'pending')
      ));

    const processingCount = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(and(
        eq(walletTransactions.type, 'withdraw'),
        eq(walletTransactions.status, 'processing')
      ));

    const completedCount = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(and(
        eq(walletTransactions.type, 'withdraw'),
        eq(walletTransactions.status, 'completed')
      ));

    return NextResponse.json({
      success: true,
      data: {
        withdrawals,
        pagination: {
          page,
          limit,
          totalPages,
          totalCount,
        },
        stats: {
          pending: pendingCount[0]?.count || 0,
          processing: processingCount[0]?.count || 0,
          completed: completedCount[0]?.count || 0,
        },
      },
    });
  } catch (error: any) {
    console.error("Error fetching withdrawals:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch withdrawals" 
      },
      { status: 500 }
    );
  }
}
