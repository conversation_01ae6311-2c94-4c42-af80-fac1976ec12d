"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  BoldIcon,
  ItalicIcon,
  LinkIcon,
  ListBulletIcon,
  PhotoIcon,
  EyeIcon,
  PencilIcon,
  Bars3BottomLeftIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ClockIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  ChevronDownIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import {
  H1Icon,
  H2Icon,
  H3Icon,
  StrikethroughIcon,
  UnderlineIcon
} from './EditorIcons';
import MarkdownPreview from './MarkdownPreview';
import VisualEditor from './VisualEditor';
import ImageUploadModal from './ImageUploadModal';
import CodeBlockModal from './CodeBlockModal';
import VersionHistoryModal from './VersionHistoryModal';



interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
  autoSave?: boolean;
  onAutoSave?: (content: string) => void;
  showWordCount?: boolean;
  showReadingTime?: boolean;
  maxLength?: number;
}

interface LinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (url: string, text: string) => void;
  selectedText?: string;
}

const LinkModal: React.FC<LinkModalProps> = ({ isOpen, onClose, onInsert, selectedText }) => {
  const [url, setUrl] = useState('');
  const [text, setText] = useState(selectedText || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url && text) {
      onInsert(url, text);
      setUrl('');
      setText('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold mb-4">Add Link</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Link Text
            </label>
            <input
              type="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter link text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              URL
            </label>
            <input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Insert Link
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  rows = 20,
  className = "",
  autoSave = false,
  onAutoSave,
  showWordCount = true,
  showReadingTime = true,
  maxLength
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showCodeBlockModal, setShowCodeBlockModal] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [editorMode, setEditorMode] = useState<'markdown' | 'visual' | 'preview'>('visual');
  const [showHeadingDropdown, setShowHeadingDropdown] = useState(false);
  const [history, setHistory] = useState<string[]>([value]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);



  // Calculate statistics
  useEffect(() => {
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(value.length);
    setReadingTime(Math.ceil(words.length / 200)); // Average reading speed: 200 words per minute
  }, [value]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && onAutoSave && value.trim()) {
      const timer = setTimeout(() => {
        onAutoSave(value);
        setLastSaved(new Date());
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timer);
    }
  }, [value, autoSave, onAutoSave]);

  // History management
  const addToHistory = useCallback((newValue: string) => {
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newValue);
      return newHistory.slice(-50); // Keep last 50 states
    });
    setHistoryIndex(prev => Math.min(prev + 1, 49));
  }, [historyIndex]);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      onChange(history[newIndex]);
    }
  }, [historyIndex, history, onChange]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      onChange(history[newIndex]);
    }
  }, [historyIndex, history, onChange]);

  const restoreVersion = useCallback((index: number) => {
    setHistoryIndex(index);
    onChange(history[index]);
  }, [history, onChange]);

  const handleVersionHistory = () => {
    setShowVersionHistory(true);
  };



  const insertText = (before: string, after: string = '', placeholder: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const textToInsert = selectedText || placeholder;

    const newText = value.substring(0, start) + before + textToInsert + after + value.substring(end);
    onChange(newText);

    // Set cursor position
    setTimeout(() => {
      if (selectedText) {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      } else {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      }
      textarea.focus();
    }, 0);
  };

  const handleBold = () => {
    // For Visual Editor, we'll trigger the formatting through the VisualEditor component
    if (editorMode === 'visual') {
      // This will be handled by VisualEditor's built-in formatting
      document.execCommand('bold', false);
    } else {
      insertText('**', '**', 'bold text');
    }
  };

  const handleItalic = () => {
    if (editorMode === 'visual') {
      document.execCommand('italic', false);
    } else {
      insertText('*', '*', 'italic text');
    }
  };

  const handleStrikethrough = () => {
    if (editorMode === 'visual') {
      document.execCommand('strikeThrough', false);
    } else {
      insertText('~~', '~~', 'strikethrough text');
    }
  };

  const handleUnderline = () => {
    if (editorMode === 'visual') {
      document.execCommand('underline', false);
    } else {
      insertText('<u>', '</u>', 'underlined text');
    }
  };
  const handleCodeBlock = () => {
    setShowCodeBlockModal(true);
  };

  const insertCodeBlock = (codeBlock: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const newText = value.substring(0, start) + '\n' + codeBlock + '\n' + value.substring(end);
    onChange(newText);

    // Set cursor position after the code block
    setTimeout(() => {
      const newPosition = start + codeBlock.length + 2;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };
  const handleList = () => {
    if (editorMode === 'visual') {
      document.execCommand('insertUnorderedList', false);
    } else {
      insertText('\n- ', '', 'list item');
    }
  };

  const handleNumberedList = () => {
    if (editorMode === 'visual') {
      document.execCommand('insertOrderedList', false);
    } else {
      insertText('\n1. ', '', 'numbered item');
    }
  };

  const handleBlockquote = () => {
    if (editorMode === 'visual') {
      document.execCommand('formatBlock', false, 'blockquote');
    } else {
      insertText('\n> ', '', 'quote');
    }
  };

  const handleHeading = (level: number) => {
    if (editorMode === 'visual') {
      document.execCommand('formatBlock', false, `h${level}`);
    } else {
      const prefix = '#'.repeat(level) + ' ';
      insertText('\n' + prefix, '', `Heading ${level}`);
    }
    setShowHeadingDropdown(false);
  };



  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          handleBold();
          break;
        case 'i':
          e.preventDefault();
          handleItalic();
          break;
        case 'k':
          e.preventDefault();
          handleLink();
          break;
        case 'z':
          e.preventDefault();
          if (e.shiftKey) {
            redo();
          } else {
            undo();
          }
          break;
        case 'y':
          e.preventDefault();
          redo();
          break;
        case 'Enter':
          e.preventDefault();
          handleCodeBlock();
          break;
      }
    }
  }, []);

  const handleLink = () => {
    if (editorMode === 'visual') {
      const url = prompt('Enter URL:');
      if (url) {
        const text = window.getSelection()?.toString() || 'Link text';
        document.execCommand('createLink', false, url);

        // Add nofollow attribute to the created link
        setTimeout(() => {
          const selection = window.getSelection();
          if (selection && selection.anchorNode) {
            const linkElement = selection.anchorNode.parentElement?.closest('a') ||
                               selection.focusNode?.parentElement?.closest('a');
            if (linkElement) {
              linkElement.setAttribute('rel', 'nofollow noopener noreferrer');
              linkElement.setAttribute('target', '_blank');
            }
          }
        }, 10);
      }
    } else {
      const textarea = textareaRef.current;
      if (!textarea) return;

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selected = value.substring(start, end);

      setSelectedText(selected);
      setShowLinkModal(true);
    }
  };

  const insertLink = (url: string, text: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const linkMarkdown = `[${text}](${url})`;
    
    const newText = value.substring(0, start) + linkMarkdown + value.substring(end);
    onChange(newText);

    // Set cursor position after the link
    setTimeout(() => {
      const newPosition = start + linkMarkdown.length;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  const insertImage = (url: string, alt: string) => {
    if (editorMode === 'visual') {
      document.execCommand('insertImage', false, url);
    } else {
      const textarea = textareaRef.current;
      if (!textarea) return;

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const imageMarkdown = `![${alt}](${url})`;

      const newText = value.substring(0, start) + imageMarkdown + value.substring(end);
      onChange(newText);

      // Set cursor position after the image
      setTimeout(() => {
        const newPosition = start + imageMarkdown.length;
        textarea.setSelectionRange(newPosition, newPosition);
        textarea.focus();
      }, 0);
    }
  };

  const handleImage = () => {
    if (editorMode === 'visual') {
      const url = prompt('Enter image URL:');
      if (url) {
        const alt = prompt('Enter alt text (optional):') || 'Image';
        insertImage(url, alt);
      }
    } else {
      setShowImageModal(true);
    }
  };



  const toggleMode = (mode: 'markdown' | 'visual' | 'preview') => {
    setEditorMode(mode);
  };

  const HeadingDropdown = () => (
    <div className="relative">
      <button
        type="button"
        onClick={() => setShowHeadingDropdown(!showHeadingDropdown)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
        disabled={editorMode === 'preview'}
      >
        <span className="text-sm font-medium">H</span>
        <ChevronDownIcon className="h-3 w-3" />
      </button>

      {showHeadingDropdown && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
          {[1, 2, 3, 4, 5, 6].map(level => (
            <button
              key={level}
              type="button"
              onClick={() => handleHeading(level)}
              className="w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors"
            >
              <span className={`font-bold text-${level === 1 ? 'xl' : level === 2 ? 'lg' : 'base'}`}>
                H{level}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className={`relative ${className}`}>
      <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 px-3 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {/* Heading Dropdown */}
            <HeadingDropdown />

            {/* History Group */}
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={undo}
                title="Undo (Ctrl+Z)"
                className={`p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors ${
                  historyIndex <= 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={editorMode === 'preview' || historyIndex <= 0}
              >
                <ArrowUturnLeftIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={redo}
                title="Redo (Ctrl+Y)"
                className={`p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors ${
                  historyIndex >= history.length - 1 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={editorMode === 'preview' || historyIndex >= history.length - 1}
              >
                <ArrowUturnRightIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleVersionHistory}
                title="Version History"
                className={`p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors ${
                  history.length <= 1 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={editorMode === 'preview' || history.length <= 1}
              >
                <ClockIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300 mx-2" />

            {/* Formatting Group */}
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={handleBold}
                title="Bold (Ctrl+B)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <BoldIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleItalic}
                title="Italic (Ctrl+I)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <ItalicIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleStrikethrough}
                title="Strikethrough"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <StrikethroughIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleUnderline}
                title="Underline"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <UnderlineIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300 mx-2" />

            {/* Structure Group */}
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={handleCodeBlock}
                title="Code Block (Ctrl+Enter)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <DocumentTextIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleList}
                title="Bullet List"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <ListBulletIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleNumberedList}
                title="Numbered List"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <Bars3BottomLeftIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleBlockquote}
                title="Blockquote"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300 mx-2" />

            {/* Insert Group */}
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={handleLink}
                title="Link (Ctrl+K)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <LinkIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleImage}
                title="Image"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <PhotoIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300 mx-2" />

            {/* Formatting Group */}
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={handleBold}
                title="Bold (Ctrl+B)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <BoldIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleItalic}
                title="Italic (Ctrl+I)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <ItalicIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleStrikethrough}
                title="Strikethrough"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <StrikethroughIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleUnderline}
                title="Underline"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <UnderlineIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300 mx-2" />

            {/* Structure Group */}
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={handleCodeBlock}
                title="Code Block (Ctrl+Enter)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <DocumentTextIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleList}
                title="Bullet List"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <ListBulletIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleNumberedList}
                title="Numbered List"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <Bars3BottomLeftIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleBlockquote}
                title="Blockquote"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300 mx-2" />

            {/* Insert Group */}
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={handleLink}
                title="Link (Ctrl+K)"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <LinkIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleImage}
                title="Image"
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={editorMode === 'preview'}
              >
                <PhotoIcon className="h-4 w-4" />
              </button>
            </div>
          </div>


        </div>
      </div>

      {/* Mode Toggle Buttons - Above Content Area */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center bg-gray-100 rounded-lg p-1">
          <button
            type="button"
            onClick={() => toggleMode('visual')}
            className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors text-sm ${
              editorMode === 'visual'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <SparklesIcon className="h-4 w-4" />
            <span>Visual</span>
          </button>

          <button
            type="button"
            onClick={() => toggleMode('preview')}
            className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors text-sm ${
              editorMode === 'preview'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <EyeIcon className="h-4 w-4" />
            <span>Preview</span>
          </button>
        </div>

        {/* Current Mode Indicator */}
        <div className="text-sm text-gray-500">
          Mode: <span className="font-medium capitalize">{editorMode}</span>
        </div>
      </div>

      {/* Content Area */}
      {editorMode === 'preview' ? (
        <div className="w-full px-4 py-3 min-h-[400px] bg-white border border-gray-300 rounded-lg">
          <MarkdownPreview content={value} />
        </div>
      ) : (
        <VisualEditor
          value={value}
          onChange={(newValue) => {
            if (!maxLength || newValue.length <= maxLength) {
              onChange(newValue);
              addToHistory(newValue);
            }
          }}
          placeholder={placeholder}
          className="focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      )}

      {/* Statistics and Help */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Statistics */}
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            {showWordCount && (
              <span className="flex items-center">
                <DocumentTextIcon className="h-3 w-3 mr-1" />
                {wordCount} words
              </span>
            )}
            <span>{charCount} characters</span>
            {showReadingTime && (
              <span className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                {readingTime} min read
              </span>
            )}
            {maxLength && (
              <span className={charCount > maxLength * 0.9 ? 'text-orange-500' : ''}>
                {charCount}/{maxLength}
              </span>
            )}
            {lastSaved && (
              <span className="text-green-600">
                Saved {lastSaved.toLocaleTimeString()}
              </span>
            )}
          </div>

          {/* Quick Help */}
          <div className="text-xs text-gray-500">
            <details className="relative">
              <summary className="cursor-pointer hover:text-gray-700">Quick Help</summary>
              <div className="absolute right-0 bottom-full mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 w-80 z-20">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <strong>Formatting:</strong>
                    <div>**Bold** *Italic*</div>
                    <div>~~Strike~~ `Code`</div>
                    <div># H1 ## H2 ### H3</div>
                  </div>
                  <div>
                    <strong>Structure:</strong>
                    <div>- List item</div>
                    <div>1. Numbered</div>
                    <div>&gt; Blockquote</div>
                  </div>
                  <div>
                    <strong>Links & Media:</strong>
                    <div>[Link](url)</div>
                    <div>![Image](url)</div>
                  </div>
                  <div>
                    <strong>Shortcuts:</strong>
                    <div>Ctrl+B Bold</div>
                    <div>Ctrl+I Italic</div>
                    <div>Ctrl+K Link</div>
                  </div>
                </div>
              </div>
            </details>
          </div>
        </div>
      </div>

      {/* Link Modal */}
      <LinkModal
        isOpen={showLinkModal}
        onClose={() => setShowLinkModal(false)}
        onInsert={insertLink}
        selectedText={selectedText}
      />

      {/* Image Upload Modal */}
      <ImageUploadModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        onInsert={insertImage}
      />



      {/* Code Block Modal */}
      <CodeBlockModal
        isOpen={showCodeBlockModal}
        onClose={() => setShowCodeBlockModal(false)}
        onInsert={insertCodeBlock}
      />

      {/* Version History Modal */}
      <VersionHistoryModal
        isOpen={showVersionHistory}
        onClose={() => setShowVersionHistory(false)}
        history={history}
        currentIndex={historyIndex}
        onRestore={restoreVersion}
      />






    </div>
    </div>
  );
};

export default RichTextEditor;
