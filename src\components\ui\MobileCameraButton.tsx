"use client";

import { useState } from 'react';
import { CameraIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { useMobileCamera } from '@/hooks/useMobileCamera';
import { useMobileView } from '@/hooks/useClientSide';
import { haptic } from '@/utils/haptics';
import { MobileModal } from './MobileModal';
import { cn } from '@/lib/utils';

interface MobileCameraButtonProps {
  onImageCapture: (result: { file: File; dataUrl: string; width: number; height: number }) => void;
  className?: string;
  disabled?: boolean;
  variant?: 'camera' | 'gallery' | 'both';
}

export function MobileCameraButton({
  onImageCapture,
  className,
  disabled = false,
  variant = 'both'
}: MobileCameraButtonProps) {
  const isMobile = useMobileView();
  const [showOptions, setShowOptions] = useState(false);
  const {
    captureFromCamera,
    selectFromGallery,
    isCapturing,
    error,
    clearError,
    isMobileSupported
  } = useMobileCamera();

  const handleCameraCapture = async () => {
    haptic.medium();
    setShowOptions(false);
    
    try {
      const result = await captureFromCamera();
      if (result) {
        haptic.success();
        onImageCapture(result);
      }
    } catch (error) {
      haptic.error();
      console.error('Camera capture failed:', error);
    }
  };

  const handleGallerySelect = async () => {
    haptic.light();
    setShowOptions(false);
    
    try {
      const result = await selectFromGallery();
      if (result) {
        haptic.success();
        onImageCapture(result);
      }
    } catch (error) {
      haptic.error();
      console.error('Gallery selection failed:', error);
    }
  };

  const handleButtonClick = () => {
    if (disabled || isCapturing) return;
    
    haptic.light();
    
    if (variant === 'camera') {
      handleCameraCapture();
    } else if (variant === 'gallery') {
      handleGallerySelect();
    } else {
      setShowOptions(true);
    }
  };

  // Don't render if not mobile
  if (!isMobile || !isMobileSupported) {
    return null;
  }

  return (
    <>
      <button
        type="button"
        onClick={handleButtonClick}
        disabled={disabled || isCapturing}
        className={cn(
          "inline-flex items-center justify-center rounded-lg px-3 py-2 text-sm font-medium transition-colors",
          "bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
          className
        )}
      >
        {isCapturing ? (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            <span>Processing...</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            {variant === 'camera' ? (
              <CameraIcon className="w-4 h-4" />
            ) : variant === 'gallery' ? (
              <PhotoIcon className="w-4 h-4" />
            ) : (
              <CameraIcon className="w-4 h-4" />
            )}
            <span>
              {variant === 'camera' ? 'Camera' : variant === 'gallery' ? 'Gallery' : 'Photo'}
            </span>
          </div>
        )}
      </button>

      {/* Options Modal */}
      <MobileModal
        isOpen={showOptions}
        onClose={() => setShowOptions(false)}
        title="Add Photo"
        className="max-w-sm"
      >
        <div className="p-4 space-y-3">
          <button
            onClick={handleCameraCapture}
            disabled={isCapturing}
            className="w-full flex items-center space-x-3 p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <CameraIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">Take Photo</div>
              <div className="text-sm text-gray-500">Use your camera</div>
            </div>
          </button>

          <button
            onClick={handleGallerySelect}
            disabled={isCapturing}
            className="w-full flex items-center space-x-3 p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <PhotoIcon className="w-5 h-5 text-green-600" />
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">Choose from Gallery</div>
              <div className="text-sm text-gray-500">Select existing photo</div>
            </div>
          </button>
        </div>
      </MobileModal>

      {/* Error Display */}
      {error && (
        <div className="fixed bottom-20 left-4 right-4 bg-red-500 text-white p-3 rounded-lg shadow-lg z-50">
          <div className="flex items-center justify-between">
            <span className="text-sm">{error}</span>
            <button
              onClick={clearError}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
}
