"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { PartyKitManager, PartyKitClient } from "@/lib/partykit/client";

export interface RealtimeMessage {
  id: string;
  senderId: string;
  receiverId?: string;
  content: string;
  type: "direct" | "fanpage";
  fanPageId?: string;
  timestamp: string;
  status: "sending" | "sent" | "delivered" | "read";
  optimistic?: boolean;
}

export interface TypingStatus {
  userId: string;
  conversationId: string;
  isTyping: boolean;
  timestamp: string;
}

export interface MessageDeliveryStatus {
  messageId: string;
  status: "sent" | "delivered" | "read";
  timestamp: string;
}

interface UseRealtimeMessagingOptions {
  conversationId?: string;
  enableTypingIndicators?: boolean;
  enableDeliveryStatus?: boolean;
  fallbackToPolling?: boolean;
}

export function useRealtimeMessaging(options: UseRealtimeMessagingOptions = {}) {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<RealtimeMessage[]>([]);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [connectionStatus, setConnectionStatus] = useState({
    connected: false,
    authenticated: false,
    latency: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const chatClient = useRef<PartyKitClient | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pollingFallbackRef = useRef<NodeJS.Timeout | null>(null);

  const {
    conversationId = "global",
    enableTypingIndicators = true,
    enableDeliveryStatus = true,
    fallbackToPolling = true
  } = options;

  // Initialize PartyKit connection
  useEffect(() => {
    if (!session?.user?.id) return;

    const initializeConnection = async () => {
      try {
        chatClient.current = PartyKitManager.getClient("chat", conversationId);
        
        // Set up event listeners
        chatClient.current.on("connected", () => {
          setConnectionStatus(prev => ({ ...prev, connected: true }));
          setError(null);
        });

        chatClient.current.on("authenticated", () => {
          setConnectionStatus(prev => ({ ...prev, authenticated: true }));
          setIsLoading(false);
        });

        chatClient.current.on("disconnected", () => {
          setConnectionStatus(prev => ({ ...prev, connected: false, authenticated: false }));
          if (fallbackToPolling) {
            startPollingFallback();
          }
        });

        chatClient.current.on("error", (error: any) => {
          setError(error.message || "Connection error");
          if (fallbackToPolling) {
            startPollingFallback();
          }
        });

        chatClient.current.on("latency_update", (latency: number) => {
          setConnectionStatus(prev => ({ ...prev, latency }));
        });

        // Message event listeners
        chatClient.current.on("new_message", handleNewMessage);
        chatClient.current.on("message_sent", handleMessageSent);
        chatClient.current.on("message_delivered", handleMessageDelivered);
        chatClient.current.on("message_read", handleMessageRead);
        
        if (enableTypingIndicators) {
          chatClient.current.on("user_typing", handleUserTyping);
        }

        // Connect to PartyKit
        await chatClient.current.connect();
        
        // Join conversation
        if (conversationId !== "global") {
          chatClient.current.send({
            type: "join_conversation",
            conversationId
          });
        }

      } catch (error) {
        console.error("Failed to initialize PartyKit connection:", error);
        setError("Failed to connect to real-time messaging");
        setIsLoading(false);
        
        if (fallbackToPolling) {
          startPollingFallback();
        }
      }
    };

    initializeConnection();

    return () => {
      if (chatClient.current) {
        if (conversationId !== "global") {
          chatClient.current.send({
            type: "leave_conversation",
            conversationId
          });
        }
        chatClient.current.disconnect();
      }
      
      if (pollingFallbackRef.current) {
        clearInterval(pollingFallbackRef.current);
      }
      
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [session?.user?.id, conversationId]);

  const handleNewMessage = useCallback((data: any) => {
    const message: RealtimeMessage = {
      ...data.message,
      optimistic: false
    };
    
    setMessages(prev => {
      // Remove optimistic message if it exists
      const filtered = prev.filter(m => !(m.optimistic && m.id === message.id));
      return [...filtered, message].sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
    });

    // Send delivery confirmation
    if (chatClient.current && enableDeliveryStatus) {
      chatClient.current.send({
        type: "message_delivered",
        messageId: message.id
      });
    }
  }, [enableDeliveryStatus]);

  const handleMessageSent = useCallback((data: any) => {
    setMessages(prev => prev.map(msg => 
      msg.id === data.messageId 
        ? { ...msg, status: "sent", optimistic: false }
        : msg
    ));
  }, []);

  const handleMessageDelivered = useCallback((data: any) => {
    setMessages(prev => prev.map(msg => 
      msg.id === data.messageId 
        ? { ...msg, status: "delivered" }
        : msg
    ));
  }, []);

  const handleMessageRead = useCallback((data: any) => {
    setMessages(prev => prev.map(msg => 
      msg.id === data.messageId 
        ? { ...msg, status: "read" }
        : msg
    ));
  }, []);

  const handleUserTyping = useCallback((data: any) => {
    if (!enableTypingIndicators) return;
    
    setTypingUsers(prev => {
      const newSet = new Set(prev);
      if (data.isTyping) {
        newSet.add(data.userId);
      } else {
        newSet.delete(data.userId);
      }
      return newSet;
    });

    // Auto-clear typing status after 3 seconds
    if (data.isTyping) {
      setTimeout(() => {
        setTypingUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.userId);
          return newSet;
        });
      }, 3000);
    }
  }, [enableTypingIndicators]);

  const sendMessage = useCallback(async (content: string, receiverId?: string, fanPageId?: string): Promise<boolean> => {
    if (!session?.user?.id || !content.trim()) return false;

    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date().toISOString();
    
    const message: RealtimeMessage = {
      id: messageId,
      senderId: session.user.id,
      receiverId,
      content: content.trim(),
      type: fanPageId ? "fanpage" : "direct",
      fanPageId,
      timestamp,
      status: "sending",
      optimistic: true
    };

    // Add optimistic message
    setMessages(prev => [...prev, message]);

    try {
      if (chatClient.current?.isConnected()) {
        // Send via PartyKit
        chatClient.current.send({
          type: "send_message",
          message: {
            ...message,
            optimistic: undefined
          }
        });
        return true;
      } else {
        // Fallback to API
        const response = await fetch(fanPageId ? `/api/fan-pages/${fanPageId}/messages` : '/api/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            content,
            receiverId,
            fanPageId
          }),
        });

        if (response.ok) {
          const result = await response.json();
          // Update optimistic message with server response
          setMessages(prev => prev.map(msg => 
            msg.id === messageId 
              ? { ...result.data, status: "sent", optimistic: false }
              : msg
          ));
          return true;
        } else {
          throw new Error('Failed to send message');
        }
      }
    } catch (error) {
      console.error("Failed to send message:", error);
      // Mark optimistic message as failed
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, status: "sending", error: true }
          : msg
      ));
      return false;
    }
  }, [session?.user?.id]);

  const sendTypingIndicator = useCallback((isTyping: boolean) => {
    if (!enableTypingIndicators || !chatClient.current?.isConnected()) return;

    chatClient.current.send({
      type: "typing",
      conversationId,
      isTyping
    });

    // Auto-stop typing after 3 seconds
    if (isTyping) {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      typingTimeoutRef.current = setTimeout(() => {
        sendTypingIndicator(false);
      }, 3000);
    }
  }, [enableTypingIndicators, conversationId]);

  const markAsRead = useCallback((messageId: string) => {
    if (!chatClient.current?.isConnected()) return;

    chatClient.current.send({
      type: "mark_read",
      messageId,
      conversationId
    });
  }, [conversationId]);

  const startPollingFallback = useCallback(() => {
    if (pollingFallbackRef.current) return;

    console.log("Starting polling fallback for messaging");
    pollingFallbackRef.current = setInterval(async () => {
      try {
        // Fetch messages via API as fallback
        const response = await fetch(`/api/messages?conversationId=${conversationId}`);
        if (response.ok) {
          const data = await response.json();
          setMessages(data.messages || []);
        }
      } catch (error) {
        console.error("Polling fallback error:", error);
      }
    }, 10000); // Poll every 10 seconds as fallback
  }, [conversationId]);

  return {
    // State
    messages,
    typingUsers: Array.from(typingUsers),
    connectionStatus,
    isLoading,
    error,

    // Actions
    sendMessage,
    sendTypingIndicator,
    markAsRead,

    // Utilities
    isConnected: connectionStatus.connected && connectionStatus.authenticated,
    latency: connectionStatus.latency
  };
}
