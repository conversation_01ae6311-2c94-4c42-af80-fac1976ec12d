"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { formatDistanceToNow } from "date-fns";
import {
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  PaperAirplaneIcon,
  CheckBadgeIcon,
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { CommentTime } from "@/components/ui/TimeDisplay";
import { toast } from "react-hot-toast";
import { parseTextContent, renderParsedContent } from "@/lib/utils/link-utils";
import { renderTextWithMentions } from "@/lib/utils/mention-utils";

interface Comment {
  id: string;
  content: string;
  parentId: string | null;
  likeCount: number;
  dislikeCount?: number;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username: string | null;
    image: string | null;
  } | null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  } | null;
  liked?: boolean;
  disliked?: boolean;
  replies?: Comment[];
}

interface FanPageCommentSectionProps {
  postId: string;
  initialCommentCount?: number;
  onCommentCountChange?: (count: number) => void;
  showComments?: boolean;
}

// Helper function to render comment content with both links and mentions
const renderCommentContent = (content: string) => {
  const parsedContent = parseTextContent(content);
  return renderParsedContent(parsedContent, {
    showLinkPreviews: false,
    linkClassName: "text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200",
    mentionClassName: "text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
  });
};

export function FanPageCommentSection({
  postId,
  initialCommentCount = 0,
  onCommentCountChange,
  showComments = false
}: FanPageCommentSectionProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const [replyingToUser, setReplyingToUser] = useState<string | null>(null);

  const [commentCount, setCommentCount] = useState(initialCommentCount);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  const fetchComments = useCallback(async (pageNum: number = 1, append: boolean = false) => {
    try {
      if (pageNum === 1) setLoading(true);
      else setLoadingMore(true);

      const response = await fetch(`/api/fan-pages/posts/${postId}/comments?page=${pageNum}&limit=10`);

      if (!response.ok) {
        throw new Error('Failed to fetch comments');
      }

      const data = await response.json();

      if (append) {
        setComments(prev => [...prev, ...data.comments]);
      } else {
        setComments(data.comments);
      }

      setHasMore(data.pagination.hasNextPage);
      setPage(pageNum);

    } catch (error) {
      console.error('Error fetching comments:', error);
      toast.error('Failed to load comments');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [postId]);

  useEffect(() => {
    if (showComments && comments.length === 0) {
      fetchComments();
    }
  }, [showComments, fetchComments, comments.length]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newComment.trim() || !session?.user) {
      return;
    }

    setSubmitting(true);

    try {
      const response = await fetch(`/api/fan-pages/posts/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newComment.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to post comment');
      }

      const newCommentData = await response.json();

      // Add new comment to the beginning of the list
      setComments(prev => [newCommentData, ...prev]);
      setNewComment("");

      // Update comment count
      const newCount = commentCount + 1;
      setCommentCount(newCount);
      onCommentCountChange?.(newCount);

      toast.success('Comment posted successfully!');

    } catch (error) {
      console.error('Error posting comment:', error);
      toast.error('Failed to post comment');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSubmitReply = async (parentId: string) => {
    if (!replyContent.trim() || !session?.user) {
      return;
    }

    setSubmitting(true);

    try {
      const response = await fetch(`/api/fan-pages/posts/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: replyContent.trim(),
          parentId,
          mentionedUserId: replyingToUser,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to post reply');
      }

      const newReply = await response.json();

      // Add reply to the parent comment
      setComments(prev => prev.map(comment => {
        if (comment.id === parentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), newReply]
          };
        }
        return comment;
      }));

      setReplyContent("");
      setReplyingTo(null);
      setReplyingToUser(null);

      // Update comment count
      const newCount = commentCount + 1;
      setCommentCount(newCount);
      onCommentCountChange?.(newCount);

      toast.success('Reply posted successfully!');

    } catch (error) {
      console.error('Error posting reply:', error);
      toast.error('Failed to post reply');
    } finally {
      setSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    if (!session?.user) {
      toast.error('Please login to like comments');
      return;
    }

    try {
      const response = await fetch(`/api/fan-pages/posts/comments/${commentId}/like`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to toggle like');
      }

      const { liked, likeCount } = await response.json();

      // Update comment like status
      setComments(prev => prev.map(comment => {
        if (comment.id === commentId) {
          return { ...comment, liked, likeCount };
        }
        // Also check replies
        if (comment.replies) {
          return {
            ...comment,
            replies: comment.replies.map(reply =>
              reply.id === commentId ? { ...reply, liked, likeCount } : reply
            )
          };
        }
        return comment;
      }));

    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Failed to update like');
    }
  };

  const handleDislikeComment = async (commentId: string) => {
    if (!session?.user) {
      toast.error('Please login to dislike comments');
      return;
    }

    try {
      const response = await fetch(`/api/fan-pages/posts/comments/${commentId}/dislike`, {
        method: 'POST',
      });

      if (!response.ok) {
        // Fan page comments dislike is not implemented yet
        toast.error('Dislike feature not available for fan page comments yet');
        return;
      }

      const { disliked, dislikeCount } = await response.json();

      // Update comment dislike status
      setComments(prev => prev.map(comment => {
        if (comment.id === commentId) {
          return { ...comment, disliked, dislikeCount };
        }
        // Also check replies
        if (comment.replies) {
          return {
            ...comment,
            replies: comment.replies.map(reply =>
              reply.id === commentId ? { ...reply, disliked, dislikeCount } : reply
            )
          };
        }
        return comment;
      }));

    } catch (error) {
      console.error('Error toggling dislike:', error);
      toast.error('Failed to update dislike');
    }
  };

  const loadMoreComments = () => {
    if (!loadingMore && hasMore) {
      fetchComments(page + 1, true);
    }
  };

  // Load comments when showComments becomes true
  useEffect(() => {
    if (showComments && comments.length === 0 && !loading) {
      fetchComments();
    }
  }, [showComments, comments.length, loading, fetchComments]);



  const getCommenterInfo = (comment: Comment) => {
    if (comment.fanPage) {
      return {
        name: comment.fanPage.name,
        username: comment.fanPage.username,
        image: comment.fanPage.profileImage,
        isVerified: comment.fanPage.isVerified,
        isPage: true,
      };
    } else if (comment.user) {
      return {
        name: comment.user.name,
        username: comment.user.username,
        image: comment.user.image,
        isVerified: false,
        isPage: false,
      };
    }
    return {
      name: 'Unknown User',
      username: null,
      image: null,
      isVerified: false,
      isPage: false,
    };
  };

  return (
    <div className="border-t border-gray-100 pt-3">
      {showComments && (
        <div className="space-y-4">
          {/* Comment Input */}
          {session?.user && (
            <form onSubmit={handleSubmitComment} className="flex space-x-3">
              <div className="flex-shrink-0">
                {session.user.image ? (
                  <OptimizedImage
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                    width={32}
                    height={32}
                    className="rounded-full"
                  />
                ) : (
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {session.user.name?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                )}
              </div>
              <div className="flex-1">
                <div className="relative">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Write a comment..."
                    className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={2}
                    disabled={submitting}
                  />
                  <button
                    type="submit"
                    disabled={!newComment.trim() || submitting}
                    className="absolute bottom-2 right-2 p-1.5 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <PaperAirplaneIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </form>
          )}

          {/* Comments List */}
          {loading ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex space-x-3 animate-pulse">
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              <ChatBubbleOvalLeftIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>No comments yet. Be the first to comment!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => {
                const commenterInfo = getCommenterInfo(comment);

                return (
                  <div key={comment.id} className="space-y-3">
                    {/* Main Comment */}
                    <div className="flex space-x-3">
                      <div className="flex-shrink-0">
                        {commenterInfo.image ? (
                          <OptimizedImage
                            src={commenterInfo.image}
                            alt={commenterInfo.name}
                            width={32}
                            height={32}
                            className="rounded-full"
                          />
                        ) : (
                          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                            <span className="text-white font-bold text-sm">
                              {commenterInfo.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-center space-x-2 mb-1">
                            <Link
                              href={comment.fanPage ? `/pages/${comment.fanPage.username}` : `/user/${comment.user?.id}`}
                              className="font-medium text-sm text-gray-900 hover:text-blue-600 hover:underline cursor-pointer"
                            >
                              {commenterInfo.name}
                            </Link>
                            {commenterInfo.isVerified && (
                              <CheckBadgeIcon className="w-4 h-4 text-blue-500" />
                            )}
                            {commenterInfo.isPage && (
                              <span className="text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full">
                                Page
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-700">{renderCommentContent(comment.content)}</div>
                        </div>

                        {/* Comment Actions */}
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <button
                            onClick={() => handleLikeComment(comment.id)}
                            className={`flex items-center space-x-1 hover:text-red-500 transition-colors duration-200 ${
                              comment.liked ? 'text-red-500' : ''
                            }`}
                          >
                            {comment.liked ? (
                              <HeartIconSolid className="w-4 h-4" />
                            ) : (
                              <HeartIcon className="w-4 h-4" />
                            )}
                            <span>{comment.likeCount > 0 ? comment.likeCount : 'Like'}</span>
                          </button>

                          <button
                            onClick={() => handleDislikeComment(comment.id)}
                            className={`flex items-center space-x-1 transition-colors duration-200 ${
                              comment.disliked ? 'text-red-600' : 'hover:text-red-600'
                            }`}
                          >
                            <span>👎</span>
                            <span>{comment.dislikeCount > 0 ? comment.dislikeCount : 'Dislike'}</span>
                          </button>

                          <button
                            onClick={() => {
                              if (replyingTo === comment.id) {
                                setReplyingTo(null);
                                setReplyContent("");
                                setReplyingToUser(null);
                              } else {
                                setReplyingTo(comment.id);
                                const username = comment.user?.username || comment.user?.name || comment.fanPage?.username || comment.fanPage?.name;
                                setReplyContent(`@${username} `);
                                setReplyingToUser(comment.user?.id || comment.fanPage?.id || null);
                              }
                            }}
                            className="hover:text-blue-500 transition-colors duration-200"
                          >
                            Reply
                          </button>

                          <CommentTime
                            date={comment.createdAt}
                            className="text-gray-400"
                            autoUpdate={true}
                          />
                        </div>

                        {/* Reply Input */}
                        {replyingTo === comment.id && session?.user && (
                          <div className="mt-3 flex space-x-2">
                            <div className="flex-shrink-0">
                              {session.user.image ? (
                                <OptimizedImage
                                  src={session.user.image}
                                  alt={session.user.name || 'User'}
                                  width={24}
                                  height={24}
                                  className="rounded-full"
                                />
                              ) : (
                                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                  <span className="text-white font-bold text-xs">
                                    {session.user.name?.charAt(0).toUpperCase() || 'U'}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="relative">
                                <textarea
                                  value={replyContent}
                                  onChange={(e) => setReplyContent(e.target.value)}
                                  placeholder={`Reply to ${commenterInfo.name}...`}
                                  className="w-full p-2 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                  rows={2}
                                  disabled={submitting}
                                />
                                <div className="flex justify-end space-x-2 mt-2">
                                  <button
                                    type="button"
                                    onClick={() => {
                                      setReplyingTo(null);
                                      setReplyContent("");
                                    }}
                                    className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800 transition-colors duration-200"
                                  >
                                    Cancel
                                  </button>
                                  <button
                                    onClick={() => handleSubmitReply(comment.id)}
                                    disabled={!replyContent.trim() || submitting}
                                    className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                  >
                                    Reply
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Replies */}
                        {comment.replies && comment.replies.length > 0 && (
                          <div className="mt-3 space-y-3 pl-4 border-l-2 border-gray-100">
                            {comment.replies.map((reply) => {
                              const replyInfo = getCommenterInfo(reply);

                              return (
                                <div key={reply.id} className="flex space-x-2">
                                  <div className="flex-shrink-0">
                                    {replyInfo.image ? (
                                      <OptimizedImage
                                        src={replyInfo.image}
                                        alt={replyInfo.name}
                                        width={24}
                                        height={24}
                                        className="rounded-full"
                                      />
                                    ) : (
                                      <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                        <span className="text-white font-bold text-xs">
                                          {replyInfo.name.charAt(0).toUpperCase()}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex-1">
                                    <div className="bg-gray-50 rounded-lg p-2">
                                      <div className="flex items-center space-x-2 mb-1">
                                        <Link
                                          href={reply.fanPage ? `/pages/${reply.fanPage.username}` : `/user/${reply.user?.id}`}
                                          className="font-medium text-xs text-gray-900 hover:text-blue-600 hover:underline cursor-pointer"
                                        >
                                          {replyInfo.name}
                                        </Link>
                                        {replyInfo.isVerified && (
                                          <CheckBadgeIcon className="w-3 h-3 text-blue-500" />
                                        )}
                                        {replyInfo.isPage && (
                                          <span className="text-xs bg-blue-100 text-blue-600 px-1 py-0.5 rounded-full">
                                            Page
                                          </span>
                                        )}
                                      </div>
                                      <div className="text-xs text-gray-700">{renderCommentContent(reply.content)}</div>
                                    </div>

                                    <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                                      <button
                                        onClick={() => handleLikeComment(reply.id)}
                                        className={`flex items-center space-x-1 hover:text-red-500 transition-colors duration-200 ${
                                          reply.liked ? 'text-red-500' : ''
                                        }`}
                                      >
                                        {reply.liked ? (
                                          <HeartIconSolid className="w-3 h-3" />
                                        ) : (
                                          <HeartIcon className="w-3 h-3" />
                                        )}
                                        <span>{reply.likeCount > 0 ? reply.likeCount : 'Like'}</span>
                                      </button>

                                      <button
                                        onClick={() => handleDislikeComment(reply.id)}
                                        className={`flex items-center space-x-1 transition-colors duration-200 ${
                                          reply.disliked ? 'text-red-600' : 'hover:text-red-600'
                                        }`}
                                      >
                                        <span>👎</span>
                                        <span>{reply.dislikeCount > 0 ? reply.dislikeCount : 'Dislike'}</span>
                                      </button>

                                      <CommentTime
                                        date={reply.createdAt}
                                        className="text-gray-400"
                                        autoUpdate={true}
                                      />
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Load More Comments */}
              {hasMore && (
                <div className="text-center">
                  <button
                    onClick={loadMoreComments}
                    disabled={loadingMore}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200 disabled:opacity-50"
                  >
                    {loadingMore ? 'Loading...' : 'Load more comments'}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
