import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { 
  referrals, 
  users, 
  walletTransactions 
} from "@/lib/db/schema";
import { eq, and, sql, desc, count, sum } from "drizzle-orm";

// Get referral statistics for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Total referrals count
    const totalReferralsResult = await db
      .select({ count: count() })
      .from(referrals);

    // Completed referrals count
    const completedReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.status, 'completed'));

    // Pending referrals count
    const pendingReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.status, 'pending'));

    // Cancelled referrals count
    const cancelledReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.status, 'cancelled'));

    // Total rewards (sum of all referral rewards)
    const totalRewardsResult = await db
      .select({ total: sum(referrals.rewardAmount) })
      .from(referrals);

    // Paid rewards (completed referrals)
    const paidRewardsResult = await db
      .select({ total: sum(referrals.rewardAmount) })
      .from(referrals)
      .where(eq(referrals.status, 'completed'));

    // Pending rewards (pending referrals)
    const pendingRewardsResult = await db
      .select({ total: sum(referrals.rewardAmount) })
      .from(referrals)
      .where(eq(referrals.status, 'pending'));

    // Top referrers (users with most completed referrals)
    const topReferrersResult = await db
      .select({
        referrerId: referrals.referrerId,
        totalReferrals: count(),
        completedReferrals: count(sql`CASE WHEN ${referrals.status} = 'completed' THEN 1 END`),
        totalEarnings: sum(sql`CASE WHEN ${referrals.status} = 'completed' THEN ${referrals.rewardAmount} ELSE 0 END`),
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
        }
      })
      .from(referrals)
      .innerJoin(users, eq(referrals.referrerId, users.id))
      .groupBy(referrals.referrerId, users.id, users.name, users.username)
      .orderBy(desc(count(sql`CASE WHEN ${referrals.status} = 'completed' THEN 1 END`)))
      .limit(10);

    // Recent referrals
    const recentReferralsResult = await db
      .select({
        id: referrals.id,
        status: referrals.status,
        rewardAmount: referrals.rewardAmount,
        createdAt: referrals.createdAt,
        completedAt: referrals.completedAt,
        referrer: {
          id: users.id,
          name: users.name,
          username: users.username,
        },
        referred: {
          id: sql`referred_user.id`,
          name: sql`referred_user.name`,
          username: sql`referred_user.username`,
        }
      })
      .from(referrals)
      .innerJoin(users, eq(referrals.referrerId, users.id))
      .innerJoin(
        sql`users AS referred_user`, 
        sql`${referrals.referredUserId} = referred_user.id`
      )
      .orderBy(desc(referrals.createdAt))
      .limit(10);

    // Calculate values
    const totalReferrals = totalReferralsResult[0]?.count || 0;
    const completedReferrals = completedReferralsResult[0]?.count || 0;
    const pendingReferrals = pendingReferralsResult[0]?.count || 0;
    const cancelledReferrals = cancelledReferralsResult[0]?.count || 0;
    const totalRewards = parseFloat(totalRewardsResult[0]?.total || '0');
    const paidRewards = parseFloat(paidRewardsResult[0]?.total || '0');
    const pendingRewards = parseFloat(pendingRewardsResult[0]?.total || '0');

    const stats = {
      totalReferrals,
      completedReferrals,
      pendingReferrals,
      cancelledReferrals,
      totalRewards,
      paidRewards,
      pendingRewards,
      topReferrers: topReferrersResult.map(referrer => ({
        id: referrer.user.id,
        name: referrer.user.name || 'Unknown User',
        username: referrer.user.username || '',
        totalReferrals: referrer.totalReferrals,
        completedReferrals: referrer.completedReferrals,
        totalEarnings: parseFloat(referrer.totalEarnings || '0'),
      })),
      recentReferrals: recentReferralsResult.map(referral => ({
        id: referral.id,
        referrerName: referral.referrer.name || 'Unknown User',
        referredName: referral.referred.name || 'Unknown User',
        status: referral.status,
        rewardAmount: parseFloat(referral.rewardAmount),
        createdAt: referral.createdAt.toISOString(),
        completedAt: referral.completedAt?.toISOString(),
      })),
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Error fetching referral stats:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch referral statistics"
      },
      { status: 500 }
    );
  }
}
