# HIFNF - Social Media Platform

HIFNF is a Facebook-like social media platform built with Next.js, MySQL, and Drizzle ORM. It features a responsive design, clean architecture, and a comprehensive set of social features.

## Features

- User authentication (signup, login, profile)
- News feed with posts, comments, and likes
- Friend/connection system
- Real-time messaging
- Notifications
- Responsive design for mobile and desktop
- Dark/light mode support

## Tech Stack

- **Frontend**: Next.js, React, <PERSON><PERSON>windCSS
- **Backend**: Next.js API Routes
- **Database**: MySQL with Drizzle ORM
- **Authentication**: NextAuth.js
- **Form Handling**: React Hook Form with Zod validation
- **UI Components**: Custom components with Headless UI

## Prerequisites

- Node.js (v18 or higher)
- MySQL server (local or cPanel MySQL)

## Getting Started

1. Clone the repository:

```bash
git clone https://github.com/yourusername/hifnf.git
cd hifnf
```

2. Install dependencies:

```bash
npm install
```

3. Configure environment variables:

Create a `.env` file in the root directory with the following variables:

```
# Database
DATABASE_HOST=localhost
DATABASE_USERNAME=root
DATABASE_PASSWORD=your_mysql_password
DATABASE_NAME=hifnf_db
DATABASE_PORT=3306

# Database Connection Pool (Optimized Settings)
DATABASE_CONNECTION_LIMIT=50
DATABASE_MAX_IDLE=20
DATABASE_IDLE_TIMEOUT=60000
DATABASE_QUEUE_LIMIT=100
DATABASE_ACQUIRE_TIMEOUT=60000
DATABASE_TIMEOUT=60000

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-change-this-in-production

# OAuth Providers (optional)
# GOOGLE_CLIENT_ID=
# GOOGLE_CLIENT_SECRET=
# GITHUB_CLIENT_ID=
# GITHUB_CLIENT_SECRET=
```

All configuration settings are centralized in `src/lib/config/index.ts`. This file loads values from environment variables and provides default values when needed. You can modify this file to customize the application configuration.

4. Set up the database:

For local MySQL:
```bash
npm run setup-db
```

For cPanel MySQL:
```bash
npm run setup-cpanel-db
```

5. Run the development server:

```bash
npm run dev
```

This will start the Next.js application on port 3001.

6. Open [http://localhost:3001](http://localhost:3001) in your browser.

### Alternative Development Commands

- `npm run dev` - Start the Next.js development server

## Project Structure

```
hifnf/
├── src/
│   ├── app/                  # Next.js App Router
│   │   ├── api/              # API routes
│   │   ├── (routes)/         # Page routes
│   ├── components/           # React components
│   │   ├── layout/           # Layout components
│   │   ├── ui/               # UI components
│   ├── lib/                  # Utility functions and libraries
│   │   ├── config/           # Configuration settings
│   │   ├── db/               # Database configuration and schema
│   │   ├── utils/            # Utility functions
├── public/                   # Static assets
├── .env                      # Environment variables
├── drizzle.config.ts         # Drizzle ORM configuration
└── ...
```

## Database Schema

The database schema includes the following tables:

- `users`: User profiles and authentication data
- `posts`: User posts with content and privacy settings
- `comments`: Comments on posts
- `likes`: Likes on posts and comments
- `friendships`: Friend connections between users
- `messages`: Private messages between users
- `notifications`: User notifications for various activities

## Using with cPanel MySQL

If you're using cPanel MySQL, follow these steps:

1. Create a MySQL database in your cPanel
2. Create a MySQL user and assign it to the database with all privileges
3. Update your `.env` file with the cPanel MySQL credentials:
   ```
   DATABASE_HOST=your-cpanel-host (e.g., localhost or server IP)
   DATABASE_USERNAME=your-cpanel-db-username
   DATABASE_PASSWORD=your-cpanel-db-password
   DATABASE_NAME=your-cpanel-db-name
   DATABASE_PORT=3306
   ```
4. Run the cPanel database setup script:
   ```bash
   npm run setup-cpanel-db
   ```
5. Start the development server:
   ```bash
   npm run dev
   ```

## Database Connection Pooling

This application uses MySQL connection pooling to optimize database performance. Connection pooling helps reduce the time spent connecting to the MySQL server by reusing previous connections instead of creating new ones for each request.

The connection pool is configured with the following optimized parameters:

- `connectionLimit`: Maximum number of connections in the pool (optimized: 50)
- `maxIdle`: Maximum number of idle connections in the pool (optimized: 20)
- `idleTimeout`: Time in milliseconds that a connection can remain idle before being closed (60000ms = 1 minute)
- `queueLimit`: Maximum number of connection requests the pool will queue (optimized: 100)
- `acquireTimeout`: Maximum time to wait for a connection from the pool (60000ms = 1 minute)
- `timeout`: Maximum time for query execution (60000ms = 1 minute)
- `waitForConnections`: Whether the pool should wait for a connection to become available (true)
- `enableKeepAlive`: Whether to use TCP keep-alive on the connections (true)
- `keepAliveInitialDelay`: Initial delay before the first keepalive probe is sent (0)
- `reconnect`: Automatically reconnect on connection loss (true)

You can customize these settings by adding the corresponding environment variables to your `.env` file:

```
DATABASE_CONNECTION_LIMIT=10
DATABASE_MAX_IDLE=10
DATABASE_IDLE_TIMEOUT=60000
DATABASE_QUEUE_LIMIT=0
```

## Learn More

To learn more about the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs)
- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
- [NextAuth.js Documentation](https://next-auth.js.org/getting-started/introduction)
- [MySQL2 Documentation](https://github.com/sidorares/node-mysql2#readme)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
