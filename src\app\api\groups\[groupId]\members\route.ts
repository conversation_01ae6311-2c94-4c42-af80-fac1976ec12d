import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, users, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, or, not } from "drizzle-orm";

const memberActionSchema = z.object({
  userId: z.string(),
  action: z.enum(["invite", "request", "approve", "reject", "remove", "promote", "demote"]),
});

// Get members of a group
export async function GET(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const url = new URL(req.url);
    const roleParam = url.searchParams.get("role") || "all"; // all, admin, moderator, member, pending

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user has access to view members
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id)
      ),
    });

    const isPublic = group.visibility === "public";
    const isPrivateVisible = group.visibility === "private-visible";
    const isMember = userMembership && (userMembership.role === "admin" || userMembership.role === "moderator" || userMembership.role === "member");
    const isAdmin = userMembership && userMembership.role === "admin";
    const isModerator = userMembership && userMembership.role === "moderator";

    // For private groups, only members can see the member list
    // For pending members, only admins and moderators can see them
    if ((!isPublic && !isPrivateVisible && !isMember) ||
        (roleParam === "pending" && !isAdmin && !isModerator)) {
      return NextResponse.json(
        { message: "You don't have permission to view members" },
        { status: 403 }
      );
    }

    // Build the query conditions
    const conditions = [eq(groupMembers.groupId, groupId)];

    if (roleParam !== "all") {
      conditions.push(eq(groupMembers.role, roleParam as "admin" | "moderator" | "member" | "pending"));
    }

    // Execute the query with all conditions at once
    const query = db.select({
      id: groupMembers.id,
      userId: groupMembers.userId,
      groupId: groupMembers.groupId,
      role: groupMembers.role,
      createdAt: groupMembers.createdAt,
      user: {
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
      },
    })
    .from(groupMembers)
    .innerJoin(users, eq(groupMembers.userId, users.id))
    .where(and(...conditions));

    const members = await query;

    return NextResponse.json(members);
  } catch (error) {
    console.error("Error fetching group members:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Add a member to a group or request to join
export async function POST(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const body = await req.json();
    const { userId, action } = memberActionSchema.parse(body);

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user exists
    const targetUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!targetUser) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get the current user's membership
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id)
      ),
    });

    // Check if the target user is already a member
    const targetMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, userId)
      ),
    });

    const isAdmin = userMembership && userMembership.role === "admin";
    const isModerator = userMembership && userMembership.role === "moderator";
    const isCreator = group.creatorId === session.user.id;

    // Handle different actions
    switch (action) {
      case "invite":
        // Only admins and moderators can invite users
        if (!isAdmin && !isModerator && !isCreator) {
          return NextResponse.json(
            { message: "You don't have permission to invite users" },
            { status: 403 }
          );
        }

        // Check if the user is already a member or has a pending invitation
        if (targetMembership) {
          return NextResponse.json(
            { message: "User is already a member or has a pending invitation" },
            { status: 400 }
          );
        }

        // Add the user as a pending member
        const memberId = uuidv4();
        await db.insert(groupMembers).values({
          id: memberId,
          groupId,
          userId,
          role: "pending",
        });

        // Create a notification for the invited user
        const notificationId = uuidv4();
        await db.insert(notifications).values({
          id: notificationId,
          recipientId: userId,
          type: "group_invite",
          senderId: session.user.id,
          groupId,
        });

        return NextResponse.json(
          { message: "User invited successfully" },
          { status: 200 }
        );

      case "request":
        // User is requesting to join the group
        if (session.user.id !== userId) {
          return NextResponse.json(
            { message: "You can only request to join for yourself" },
            { status: 403 }
          );
        }

        // Check if the user already has a membership
        if (targetMembership) {
          return NextResponse.json(
            { message: "You are already a member or have a pending request" },
            { status: 400 }
          );
        }

        // For private-hidden groups, users can only join by invitation
        if (group.visibility === "private-hidden") {
          return NextResponse.json(
            { message: "This group is private and requires an invitation to join" },
            { status: 403 }
          );
        }

        // Add the user as a pending member for private groups, or as a member for public groups
        const newMemberId = uuidv4();
        const initialRole = group.visibility === "public" ? "member" : "pending";

        await db.insert(groupMembers).values({
          id: newMemberId,
          groupId,
          userId: session.user.id,
          role: initialRole,
        });

        // If it's a private group, create a notification for the group admin
        if (group.visibility !== "public") {
          const joinRequestNotificationId = uuidv4();
          await db.insert(notifications).values({
            id: joinRequestNotificationId,
            recipientId: group.creatorId,
            type: "group_join_request",
            senderId: session.user.id,
            groupId,
          });
        }

        return NextResponse.json(
          {
            message: group.visibility === "public"
              ? "You have joined the group"
              : "Your request to join has been sent"
          },
          { status: 200 }
        );

      case "approve":
        // Only admins and moderators can approve join requests
        if (!isAdmin && !isModerator && !isCreator) {
          return NextResponse.json(
            { message: "You don't have permission to approve join requests" },
            { status: 403 }
          );
        }

        // Check if the user has a pending request
        if (!targetMembership || targetMembership.role !== "pending") {
          return NextResponse.json(
            { message: "User does not have a pending join request" },
            { status: 400 }
          );
        }

        // Update the user's role to member
        await db.update(groupMembers)
          .set({ role: "member" })
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        // Create a notification for the approved user
        const approvalNotificationId = uuidv4();
        await db.insert(notifications).values({
          id: approvalNotificationId,
          recipientId: userId,
          type: "group_join_approved",
          senderId: session.user.id,
          groupId,
        });

        return NextResponse.json(
          { message: "Join request approved" },
          { status: 200 }
        );

      case "reject":
        // Only admins and moderators can reject join requests
        if (!isAdmin && !isModerator && !isCreator) {
          return NextResponse.json(
            { message: "You don't have permission to reject join requests" },
            { status: 403 }
          );
        }

        // Check if the user has a pending request
        if (!targetMembership || targetMembership.role !== "pending") {
          return NextResponse.json(
            { message: "User does not have a pending join request" },
            { status: 400 }
          );
        }

        // Remove the pending membership
        await db.delete(groupMembers)
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        return NextResponse.json(
          { message: "Join request rejected" },
          { status: 200 }
        );

      case "remove":
        // Check permissions: admins can remove anyone except the creator
        // moderators can remove regular members
        // users can remove themselves (leave the group)
        const isSelf = session.user.id === userId;
        const isTargetCreator = group.creatorId === userId;
        const isTargetAdmin = targetMembership && targetMembership.role === "admin";
        const isTargetModerator = targetMembership && targetMembership.role === "moderator";

        if (!isSelf && !isAdmin && !isModerator) {
          return NextResponse.json(
            { message: "You don't have permission to remove members" },
            { status: 403 }
          );
        }

        if (isTargetCreator && !isSelf) {
          return NextResponse.json(
            { message: "The creator cannot be removed from the group" },
            { status: 403 }
          );
        }

        if (isTargetAdmin && !isCreator && !isSelf) {
          return NextResponse.json(
            { message: "Only the creator can remove admins" },
            { status: 403 }
          );
        }

        if (isTargetModerator && !isAdmin && !isCreator && !isSelf) {
          return NextResponse.json(
            { message: "Only admins can remove moderators" },
            { status: 403 }
          );
        }

        // Remove the membership
        await db.delete(groupMembers)
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        return NextResponse.json(
          { message: isSelf ? "You have left the group" : "Member removed successfully" },
          { status: 200 }
        );

      case "promote":
        // Only admins and the creator can promote members
        if (!isAdmin && !isCreator) {
          return NextResponse.json(
            { message: "You don't have permission to promote members" },
            { status: 403 }
          );
        }

        // Check if the user is a member
        if (!targetMembership || targetMembership.role === "pending") {
          return NextResponse.json(
            { message: "User is not a member of the group" },
            { status: 400 }
          );
        }

        // Check target user roles
        const isTargetCreatorInPromote = group.creatorId === userId;
        const isTargetAdminInPromote = targetMembership && targetMembership.role === "admin";

        // Cannot promote the creator or an admin
        if (isTargetCreatorInPromote || isTargetAdminInPromote) {
          return NextResponse.json(
            { message: "Cannot promote the creator or an admin" },
            { status: 400 }
          );
        }

        // Promote the member to moderator or admin
        const newRole = isTargetModerator ? "admin" : "moderator";
        await db.update(groupMembers)
          .set({ role: newRole })
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        return NextResponse.json(
          { message: `Member promoted to ${newRole}` },
          { status: 200 }
        );

      case "demote":
        // Only admins and the creator can demote members
        if (!isAdmin && !isCreator) {
          return NextResponse.json(
            { message: "You don't have permission to demote members" },
            { status: 403 }
          );
        }

        // Check target user roles for demotion
        const isTargetCreatorInDemote = group.creatorId === userId;
        const isTargetAdminInDemote = targetMembership && targetMembership.role === "admin";
        const isTargetModeratorInDemote = targetMembership && targetMembership.role === "moderator";

        // Check if the user is a moderator or admin
        if (!targetMembership || !isTargetModeratorInDemote && !isTargetAdminInDemote) {
          return NextResponse.json(
            { message: "User is not a moderator or admin" },
            { status: 400 }
          );
        }

        // Cannot demote the creator
        if (isTargetCreatorInDemote) {
          return NextResponse.json(
            { message: "Cannot demote the creator" },
            { status: 400 }
          );
        }

        // Only the creator can demote admins
        if (isTargetAdminInDemote && !isCreator) {
          return NextResponse.json(
            { message: "Only the creator can demote admins" },
            { status: 403 }
          );
        }

        // Demote the member to moderator or regular member
        const demotedRole = isTargetAdmin ? "moderator" : "member";
        await db.update(groupMembers)
          .set({ role: demotedRole })
          .where(and(
            eq(groupMembers.groupId, groupId),
            eq(groupMembers.userId, userId)
          ));

        return NextResponse.json(
          { message: `Member demoted to ${demotedRole}` },
          { status: 200 }
        );

      default:
        return NextResponse.json(
          { message: "Invalid action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error managing group members:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
