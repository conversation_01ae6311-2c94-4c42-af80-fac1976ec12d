import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages } from "@/lib/db/schema";
import { eq, desc, like, and } from "drizzle-orm";

// GET /api/fan-pages/user/created - Get user's created fan pages
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search");
    const category = searchParams.get("category");
    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = [eq(fanPages.ownerId, session.user.id)];

    if (search) {
      whereConditions.push(like(fanPages.name, `%${search}%`));
    }

    if (category && category !== "all") {
      whereConditions.push(eq(fanPages.category, category));
    }

    // Get user's created pages
    const userPages = await db
      .select({
        id: fanPages.id,
        name: fanPages.name,
        username: fanPages.username,
        category: fanPages.category,
        description: fanPages.description,
        profileImage: fanPages.profileImage,
        isVerified: fanPages.isVerified,
        followerCount: fanPages.followerCount,
        createdAt: fanPages.createdAt,
      })
      .from(fanPages)
      .where(and(...whereConditions))
      .orderBy(desc(fanPages.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await db
      .select({ count: fanPages.id })
      .from(fanPages)
      .where(and(...whereConditions));

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    // Add isFollowing flag (always false for own pages)
    const pagesWithFollowStatus = userPages.map(page => ({
      ...page,
      isFollowing: false, // User doesn't follow their own pages
    }));

    return NextResponse.json({
      pages: pagesWithFollowStatus,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });

  } catch (error) {
    console.error("Error fetching user's created fan pages:", error);
    return NextResponse.json(
      { error: "Failed to fetch created pages" },
      { status: 500 }
    );
  }
}
