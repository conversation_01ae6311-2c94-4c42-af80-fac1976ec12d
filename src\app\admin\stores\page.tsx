"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Select } from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { toast } from "react-hot-toast";
import { EditStoreModal } from "@/components/admin/stores/EditStoreModal";
import { CreateStoreModal } from "@/components/admin/stores/CreateStoreModal";
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BuildingStorefrontIcon,
  UserIcon,
  CalendarIcon,
  ShieldCheckIcon,
  ShoppingBagIcon,
  HeartIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import { formatTimeAgo } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";

interface Store {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  logo: string | null;
  banner: string | null;
  location: string | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  owner: {
    id: string;
    name: string | null;
    image: string | null;
  };
  metrics: {
    productCount: number;
    followerCount: number;
    averageRating: number;
    reviewCount: number;
  };
}

export default function AdminStoresPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [stores, setStores] = useState<Store[]>([]);
  const [filteredStores, setFilteredStores] = useState<Store[]>([]);
  const [selectedStores, setSelectedStores] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    verified: "all",
    dateRange: "all",
  });
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [bulkAction, setBulkAction] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);

  // Fetch stores data
  useEffect(() => {
    fetchStores();
  }, [currentPage, sortBy, sortOrder]);

  // Apply filters and search
  useEffect(() => {
    if (stores.length > 0) {
      let result = [...stores];

      // Apply search
      if (searchTerm) {
        result = result.filter(
          (store) =>
            store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (store.description &&
              store.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (store.location &&
              store.location.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (store.owner.name &&
              store.owner.name.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply filters
      if (filters.verified !== "all") {
        result = result.filter((store) =>
          filters.verified === "verified" ? store.isVerified : !store.isVerified
        );
      }

      // Apply date range filter
      if (filters.dateRange !== "all") {
        const now = new Date();
        let dateLimit;

        switch (filters.dateRange) {
          case "today":
            dateLimit = new Date(now.setDate(now.getDate() - 1));
            break;
          case "week":
            dateLimit = new Date(now.setDate(now.getDate() - 7));
            break;
          case "month":
            dateLimit = new Date(now.setMonth(now.getMonth() - 1));
            break;
          case "year":
            dateLimit = new Date(now.setFullYear(now.getFullYear() - 1));
            break;
          default:
            dateLimit = null;
        }

        if (dateLimit) {
          result = result.filter(
            (store) => new Date(store.createdAt) > dateLimit
          );
        }
      }

      setFilteredStores(result);
    }
  }, [stores, searchTerm, filters]);

  const fetchStores = async () => {
    setIsLoading(true);
    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        sort: sortBy,
        order: sortOrder,
      });

      // Add search term if present
      if (searchTerm) {
        queryParams.append("search", searchTerm);
      }

      // Add filters if they're not set to 'all'
      if (filters.verified === "verified") {
        queryParams.append("verified", "true");
      } else if (filters.verified === "unverified") {
        queryParams.append("verified", "false");
      }

      // Add date range filter
      if (filters.dateRange !== "all") {
        const now = new Date();
        let dateFrom;

        switch (filters.dateRange) {
          case "today":
            dateFrom = new Date(now);
            dateFrom.setDate(now.getDate() - 1);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
          case "week":
            dateFrom = new Date(now);
            dateFrom.setDate(now.getDate() - 7);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
          case "month":
            dateFrom = new Date(now);
            dateFrom.setMonth(now.getMonth() - 1);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
          case "year":
            dateFrom = new Date(now);
            dateFrom.setFullYear(now.getFullYear() - 1);
            queryParams.append("dateFrom", dateFrom.toISOString());
            break;
        }
      }

      // Make API request
      const response = await fetch(`/api/admin/stores?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch stores: ${response.status}`);
      }

      const data = await response.json();

      if (data && Array.isArray(data.stores)) {
        setStores(data.stores);
        setFilteredStores(data.stores);
        setTotalPages(data.pagination.totalPages || 1);
      } else {
        console.error("Invalid response format from admin stores API:", data);
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      toast.error("Failed to load stores");

      // Set empty arrays as a fallback
      setStores([]);
      setFilteredStores([]);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchStores();
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  const handleSelectStore = (storeId: string) => {
    setSelectedStores((prev) =>
      prev.includes(storeId)
        ? prev.filter((id) => id !== storeId)
        : [...prev, storeId]
    );
  };

  const handleSelectAllStores = () => {
    if (selectedStores.length === filteredStores.length) {
      setSelectedStores([]);
    } else {
      setSelectedStores(filteredStores.map((store) => store.id));
    }
  };

  const handleEditStore = (store: Store) => {
    setSelectedStore(store);
    setIsEditModalOpen(true);
  };

  const handleStoreUpdated = () => {
    // Refresh the stores list after a store is updated
    fetchStores();
  };

  const handleDeleteStore = async (storeId: string) => {
    if (!confirm("Are you sure you want to delete this store? This action cannot be undone.")) return;

    try {
      const response = await fetch(`/api/admin/stores/${storeId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete store");
      }

      // Update UI after successful deletion
      setStores(stores.filter(store => store.id !== storeId));
      setFilteredStores(filteredStores.filter(store => store.id !== storeId));
      toast.success("Store deleted successfully");
    } catch (error) {
      console.error("Error deleting store:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete store");
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedStores.length === 0) return;

    try {
      if (bulkAction === "delete") {
        const confirmed = window.confirm(
          `Are you sure you want to delete ${selectedStores.length} stores? This action cannot be undone.`
        );

        if (!confirmed) return;

        const response = await fetch(`/api/admin/stores`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ storeIds: selectedStores }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to delete stores");
        }

        // Update UI after successful deletion
        setStores(stores.filter(store => !selectedStores.includes(store.id)));
        setFilteredStores(filteredStores.filter(store => !selectedStores.includes(store.id)));
        toast.success(`${selectedStores.length} stores deleted successfully`);
        setSelectedStores([]);
      } else if (bulkAction === "verify" || bulkAction === "unverify") {
        const isVerified = bulkAction === "verify";
        const actionText = isVerified ? "verify" : "unverify";

        const confirmed = window.confirm(
          `Are you sure you want to ${actionText} ${selectedStores.length} stores?`
        );

        if (!confirmed) return;

        // Process each store individually
        const promises = selectedStores.map(storeId =>
          fetch(`/api/admin/stores/${storeId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ isVerified }),
          })
        );

        const results = await Promise.allSettled(promises);
        const successCount = results.filter(result => result.status === 'fulfilled').length;

        if (successCount > 0) {
          // Update UI for successfully updated stores
          setStores(stores.map(store => {
            if (selectedStores.includes(store.id)) {
              return { ...store, isVerified };
            }
            return store;
          }));

          setFilteredStores(filteredStores.map(store => {
            if (selectedStores.includes(store.id)) {
              return { ...store, isVerified };
            }
            return store;
          }));

          toast.success(`${successCount} stores ${actionText}d successfully`);
          setSelectedStores([]);
        }

        if (successCount < selectedStores.length) {
          toast.error(`Failed to ${actionText} ${selectedStores.length - successCount} stores`);
        }
      }
    } catch (error) {
      console.error("Error performing bulk action:", error);
      toast.error(error instanceof Error ? error.message : "Failed to perform bulk action");
    }
  };

  const renderStarRating = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 fill-current text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 fill-current text-yellow-400 opacity-50" />
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }

    return (
      <div className="flex items-center">
        {stars}
        <span className="ml-1 text-xs text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Store Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage marketplace stores, verification, and store owners
          </p>
        </div>
        <div className="mt-4 flex space-x-2 sm:mt-0">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <FunnelIcon className="mr-2 h-5 w-5" />
            Filters
          </Button>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <BuildingStorefrontIcon className="mr-2 h-5 w-5" />
            Create Store
          </Button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="mb-6">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
          <form onSubmit={handleSearch} className="relative flex-grow">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search stores..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </form>
          <Button
            onClick={fetchStores}
            variant="outline"
            className="flex-shrink-0"
          >
            <ArrowPathIcon className="mr-2 h-5 w-5" />
            Refresh
          </Button>
        </div>

        {showFilters && (
          <div className="mt-4 rounded-lg border border-gray-200 bg-white p-4">
            <h3 className="mb-3 font-medium text-gray-700">Filter Stores</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Verification Status
                </label>
                <Select
                  value={filters.verified}
                  onChange={(e) => handleFilterChange("verified", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Stores</option>
                  <option value="verified">Verified Stores</option>
                  <option value="unverified">Unverified Stores</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Created
                </label>
                <Select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange("dateRange", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="year">This Year</option>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk actions */}
      {selectedStores.length > 0 && (
        <div className="mb-4 flex items-center justify-between rounded-lg bg-blue-50 p-3">
          <span className="text-sm font-medium text-blue-700">
            {selectedStores.length} stores selected
          </span>
          <div className="flex items-center space-x-2">
            <Select
              value={bulkAction}
              onChange={(e) => setBulkAction(e.target.value)}
              className="text-sm"
            >
              <option value="">Bulk Action</option>
              <option value="verify">Verify Selected</option>
              <option value="unverify">Unverify Selected</option>
              <option value="delete">Delete Selected</option>
            </Select>
            <Button
              onClick={handleBulkAction}
              disabled={!bulkAction}
              size="sm"
              variant={bulkAction === "delete" ? "danger" : "primary"}
            >
              Apply
            </Button>
          </div>
        </div>
      )}

      {/* Stores table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Spinner size="lg" />
          </div>
        ) : filteredStores.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center p-4 text-center">
            <BuildingStorefrontIcon className="mb-2 h-12 w-12 text-gray-400" />
            <h3 className="mb-1 text-lg font-medium text-gray-900">No stores found</h3>
            <p className="text-gray-500">
              {searchTerm || Object.values(filters).some((v) => v !== "all")
                ? "Try adjusting your search or filters"
                : "No stores have been created yet"}
            </p>
            {(searchTerm || Object.values(filters).some((v) => v !== "all")) && (
              <Button
                onClick={() => {
                  setSearchTerm("");
                  setFilters({
                    verified: "all",
                    dateRange: "all",
                  });
                }}
                variant="outline"
                className="mt-4"
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="w-12 px-3 py-3 text-left">
                    <Checkbox
                      checked={
                        selectedStores.length > 0 &&
                        selectedStores.length === filteredStores.length
                      }
                      onChange={handleSelectAllStores}
                      aria-label="Select all stores"
                    />
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("name")}
                    >
                      Store
                      {sortBy === "name" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("owner.name")}
                    >
                      Owner
                      {sortBy === "owner.name" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <span className="flex items-center">Products</span>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <span className="flex items-center">Rating</span>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("isVerified")}
                    >
                      Status
                      {sortBy === "isVerified" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("createdAt")}
                    >
                      Created
                      {sortBy === "createdAt" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {filteredStores.map((store) => (
                  <tr key={store.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-3 py-4">
                      <Checkbox
                        checked={selectedStores.includes(store.id)}
                        onChange={() => handleSelectStore(store.id)}
                        aria-label={`Select ${store.name}`}
                      />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          {store.logo ? (
                            <Image
                              src={store.logo}
                              alt={store.name}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                              <BuildingStorefrontIcon className="h-6 w-6 text-gray-500" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {store.name}
                            {store.isVerified && (
                              <ShieldCheckIcon className="ml-1 inline-block h-4 w-4 text-blue-500" />
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            @{store.slug}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="h-6 w-6 flex-shrink-0">
                          {store.owner.image ? (
                            <Image
                              src={store.owner.image}
                              alt={store.owner.name || ""}
                              width={24}
                              height={24}
                              className="h-6 w-6 rounded-full"
                            />
                          ) : (
                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200">
                              <UserIcon className="h-4 w-4 text-gray-500" />
                            </div>
                          )}
                        </div>
                        <div className="ml-2 text-sm">{store.owner.name || "Unknown"}</div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ShoppingBagIcon className="mr-1 h-4 w-4 text-gray-400" />
                        <span>{store.metrics.productCount}</span>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {store.metrics.reviewCount > 0 ? (
                        <div className="flex flex-col">
                          {renderStarRating(store.metrics.averageRating)}
                          <span className="text-xs text-gray-500">
                            {store.metrics.reviewCount} reviews
                          </span>
                        </div>
                      ) : (
                        <span className="text-xs text-gray-500">No reviews</span>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {store.isVerified ? (
                        <Badge variant="success" className="flex items-center">
                          <ShieldCheckIcon className="mr-1 h-3 w-3" />
                          Verified
                        </Badge>
                      ) : (
                        <Badge variant="default" className="flex items-center">
                          Unverified
                        </Badge>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <div>{new Date(store.createdAt).toLocaleDateString()}</div>
                      <div className="text-xs">{formatTimeAgo(store.createdAt)}</div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <div className="flex space-x-1">
                          <Link
                            href={`/admin/stores/${store.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              title="Manage Store"
                            >
                              <BuildingStorefrontIcon className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link
                            href={`/store/${store.slug}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              title="View Public Store"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                        <Button
                          onClick={() => handleEditStore(store)}
                          variant="outline"
                          size="sm"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleDeleteStore(store.id)}
                          variant="danger"
                          size="sm"
                          disabled={store.metrics.productCount > 0}
                          title={store.metrics.productCount > 0 ? "Cannot delete store with products" : "Delete store"}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && filteredStores.length > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{filteredStores.length}</span>{" "}
            stores
          </div>
          <div className="flex space-x-1">
            <Button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            <Button
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Edit Store Modal */}
      <EditStoreModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        store={selectedStore}
        onStoreUpdated={handleStoreUpdated}
      />

      {/* Create Store Modal */}
      <CreateStoreModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onStoreCreated={handleStoreUpdated}
      />
    </AdminLayout>
  );
}
