import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { products, stores, users } from "@/lib/db/schema";
import { eq, desc, count } from "drizzle-orm";

// Get user's products
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Allow viewing any user's products (products are public)
    const isOwnProfile = userId === session.user.id;
    const isAdmin = session.user.isAdmin;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user's products with store data
    const userProducts = await db.select({
      id: products.id,
      title: products.title,
      description: products.description,
      price: products.price,
      condition: products.item_condition,
      category: products.category,
      location: products.location,
      photos: products.photos,
      viewCount: products.viewCount,
      createdAt: products.createdAt,
      updatedAt: products.updatedAt,
      // Store data
      storeId: stores.id,
      storeName: stores.name,
      storeSlug: stores.slug,
    })
    .from(products)
    .leftJoin(stores, eq(products.storeId, stores.id))
    .where(eq(stores.ownerId, userId))
    .orderBy(desc(products.createdAt))
    .limit(limit)
    .offset(offset);

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(products)
      .leftJoin(stores, eq(products.storeId, stores.id))
      .where(eq(stores.ownerId, userId));

    const total = totalResult[0]?.count || 0;

    // Format products for frontend
    const formattedProducts = userProducts.map(product => ({
      id: product.id,
      title: product.title,
      description: product.description,
      price: product.price,
      condition: product.condition,
      category: product.category,
      location: product.location,
      photos: product.photos || [],
      images: product.photos || [], // Alias for compatibility
      viewCount: product.viewCount,
      views: product.viewCount, // Alias for compatibility
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      store: product.storeId ? {
        id: product.storeId,
        name: product.storeName,
        slug: product.storeSlug,
      } : null,
    }));

    return NextResponse.json({
      products: formattedProducts,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user products:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
