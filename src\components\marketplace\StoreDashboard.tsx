"use client";

import { useRouter } from "next/navigation";

// Import sub-components
import { StoreDashboardHeader } from "./store/StoreDashboardHeader";
import { StoreDashboardStats } from "./store/StoreDashboardStats";
import { StoreDashboardProducts } from "./store/StoreDashboardProducts";

interface StoreDashboardProps {
  store: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    banner: string | null;
    location: string | null;
    isVerified: boolean | null;
    createdAt: Date;
    productCount: number;
    followerCount: number;
    reviewCount: number;
    averageRating: number | string | null;
    products: {
      id: string;
      title: string;
      price: number;
      condition: string;
      photos: string[] | null;
      viewCount: number | null;
      createdAt: Date;
    }[];
  };
}

export function StoreDashboard({ store }: StoreDashboardProps) {
  const router = useRouter();

  // Calculate if there are more products than what's shown
  const hasMoreProducts = store.productCount > store.products.length;

  // Mock data for analytics - in a real app, this would come from the database
  const totalViews = store.products.reduce((sum, product) => sum + (product.viewCount || 0), 0);
  const totalRevenue = 0; // This would be calculated from orders in a real app

  return (
    <div className="space-y-6">
      {/* Store Header */}
      <StoreDashboardHeader store={store} />

      {/* Stats */}
      <StoreDashboardStats
        store={store}
        totalViews={totalViews}
        totalRevenue={totalRevenue}
      />

      {/* Products section */}
      <div>
        <StoreDashboardProducts
          products={store.products}
          hasMoreProducts={hasMoreProducts}
        />
      </div>
    </div>
  );
}
