"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import { 
  CurrencyDollarIcon, 
  EyeIcon, 
  DocumentTextIcon,
  TrendingUpIcon,
  ClockIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface MonetizedBlog {
  id: string;
  blogId: string;
  isEnabled: boolean;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  cprRate: string;
  totalReads: number;
  uniqueReads: number;
  totalEarnings: string;
  blog: {
    id: string;
    title: string;
    slug: string;
    viewCount: number;
  };
}

interface AuthorStats {
  monetizedBlogs: MonetizedBlog[];
  totalEarnings: number;
  totalReads: number;
  averageCPR: number;
}

export function MonetizationStats() {
  const [stats, setStats] = useState<AuthorStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/author/monetization/stats');
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      } else {
        console.error('Failed to fetch monetization stats:', data.message);
      }
    } catch (error) {
      console.error('Error fetching monetization stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      case 'approved':
        return <Badge variant="success">Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      case 'suspended':
        return <Badge variant="secondary">Suspended</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CurrencyDollarIcon className="h-5 w-5" />
            Monetization Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">Failed to load monetization statistics.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-2xl font-bold text-green-600">
                  ${stats.totalEarnings.toFixed(2)}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Qualified Reads</p>
                <p className="text-2xl font-bold text-blue-600">
                  {stats.totalReads.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <EyeIcon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Monetized Blogs</p>
                <p className="text-2xl font-bold text-purple-600">
                  {stats.monetizedBlogs.length}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <DocumentTextIcon className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Average CPR</p>
                <p className="text-2xl font-bold text-orange-600">
                  ${stats.averageCPR.toFixed(2)}
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <TrendingUpIcon className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monetized Blogs List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DocumentTextIcon className="h-5 w-5" />
            Your Monetized Blogs
          </CardTitle>
          <CardDescription>
            Track performance of your monetized blog posts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stats.monetizedBlogs.length === 0 ? (
            <div className="text-center py-8">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Monetized Blogs Yet
              </h3>
              <p className="text-gray-600 mb-4">
                Start monetizing your blog posts to earn money from qualified readers.
              </p>
              <Button asChild>
                <Link href="/blogs">
                  View Your Blogs
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {stats.monetizedBlogs.map((blog) => (
                <div
                  key={blog.id}
                  className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-medium text-gray-900">
                          <Link 
                            href={`/blogs/${blog.blog.slug}`}
                            className="hover:text-blue-600 transition-colors"
                          >
                            {blog.blog.title}
                          </Link>
                        </h3>
                        {getStatusBadge(blog.status)}
                        {blog.isEnabled && blog.status === 'approved' && (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircleIcon className="h-4 w-4" />
                            <span className="text-xs">Active</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                        <div className="text-center p-2 bg-blue-50 rounded">
                          <div className="text-lg font-semibold text-blue-600">
                            {blog.uniqueReads.toLocaleString()}
                          </div>
                          <div className="text-xs text-blue-700">Qualified Reads</div>
                        </div>
                        <div className="text-center p-2 bg-green-50 rounded">
                          <div className="text-lg font-semibold text-green-600">
                            ${parseFloat(blog.totalEarnings).toFixed(2)}
                          </div>
                          <div className="text-xs text-green-700">Earnings</div>
                        </div>
                        <div className="text-center p-2 bg-purple-50 rounded">
                          <div className="text-lg font-semibold text-purple-600">
                            ${blog.cprRate}
                          </div>
                          <div className="text-xs text-purple-700">CPR Rate</div>
                        </div>
                        <div className="text-center p-2 bg-orange-50 rounded">
                          <div className="text-lg font-semibold text-orange-600">
                            {blog.blog.viewCount.toLocaleString()}
                          </div>
                          <div className="text-xs text-orange-700">Total Views</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manage your blog monetization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button asChild variant="outline">
              <Link href="/blogs">
                <DocumentTextIcon className="h-4 w-4 mr-2" />
                View All Blogs
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/wallet">
                <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                Check Wallet
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/blogs/create">
                <DocumentTextIcon className="h-4 w-4 mr-2" />
                Create New Blog
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
