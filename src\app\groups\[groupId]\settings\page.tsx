import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { groups, groupMembers } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { notFound, redirect } from "next/navigation";
import { GroupSettings } from "@/components/groups/GroupSettings";
import { GroupHeader } from "@/components/groups/GroupHeader";
import { GroupTabs } from "@/components/groups/GroupTabs";

interface GroupSettingsPageProps {
  params: Promise<{
    groupId: string;
  }>;
}

export default async function GroupSettingsPage({ params }: GroupSettingsPageProps) {
  const user = await requireAuth();
  const { groupId } = await params;

  // Get group details
  const group = await db.query.groups.findFirst({
    where: eq(groups.id, groupId),
    with: {
      creator: {
        columns: {
          id: true,
          name: true,
          username: true,
          image: true,
        },
      },
    },
  });

  if (!group) {
    notFound();
  }

  // Check if user is creator or admin
  const membership = await db.query.groupMembers.findFirst({
    where: and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.userId, user.id)
    ),
  });

  const isCreator = group.creatorId === user.id;
  const isAdmin = membership?.role === "admin";

  // Only creators and admins can access settings
  if (!isCreator && !isAdmin) {
    redirect(`/groups/${groupId}`);
  }

  // Get member count for header
  const membersCount = await db
    .select({ count: db.$count(groupMembers, eq(groupMembers.groupId, groupId)) })
    .from(groupMembers)
    .where(eq(groupMembers.groupId, groupId));

  const isPublic = group.visibility === "public";
  const isPrivateVisible = group.visibility === "private-visible";

  return (
    <MainLayout>
      {/* Hero section with cover image */}
      <GroupHeader
        group={group}
        membersCount={membersCount[0]?.count || 0}
        isCreator={isCreator}
        isAdmin={isAdmin}
        isMember={true}
        isPending={false}
        isPublic={isPublic}
        isPrivateVisible={isPrivateVisible}
      />

      {/* Tabs navigation */}
      <GroupTabs groupId={groupId} activeTab="settings" showSettings={true} />

      {/* Main content */}
      <div className="mx-auto max-w-4xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Group Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your group's information, privacy, and settings.
          </p>
        </div>

        <GroupSettings group={group} />
      </div>
    </MainLayout>
  );
}
