"use client";

import { useEffect } from "react";

/**
 * HydrationProvider handles browser extension attributes that can cause hydration mismatches
 * This component runs only on the client side to prevent SSR/client differences
 */
export function HydrationProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Handle browser extension attributes that might be added after hydration
    // This prevents hydration warnings for common browser extension attributes
    const handleBrowserExtensionAttributes = () => {
      const body = document.body;
      
      // Common browser extension attributes that can cause hydration mismatches
      const extensionAttributes = [
        'cz-shortcut-listen', // ColorZilla extension
        'data-new-gr-c-s-check-loaded', // Grammarly
        'data-gr-ext-installed', // Grammarly
        'spellcheck', // Various spell check extensions
        'data-lt-installed', // LanguageTool
        'data-adblock-key', // AdBlock extensions
      ];

      // Remove any extension attributes that might have been added during SSR
      extensionAttributes.forEach(attr => {
        if (body.hasAttribute(attr)) {
          body.removeAttribute(attr);
        }
      });

      // Set up a MutationObserver to handle attributes added by extensions after hydration
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.target === body) {
            const attributeName = mutation.attributeName;
            if (attributeName && extensionAttributes.includes(attributeName)) {
              // Allow the extension attribute but don't let it cause hydration warnings
              console.debug(`Browser extension attribute detected: ${attributeName}`);
            }
          }
        });
      });

      observer.observe(body, {
        attributes: true,
        attributeFilter: extensionAttributes,
      });

      return () => observer.disconnect();
    };

    // Run after a short delay to ensure DOM is fully ready
    const timeoutId = setTimeout(handleBrowserExtensionAttributes, 100);

    return () => clearTimeout(timeoutId);
  }, []);

  return <>{children}</>;
}
