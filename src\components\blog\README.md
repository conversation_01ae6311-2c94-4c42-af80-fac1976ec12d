# Enhanced RichTextEditor

A professional, feature-rich markdown editor built with React and TypeScript. This editor provides a comprehensive writing experience with advanced formatting options, collaboration features, and powerful productivity tools.

## Features

### ✨ Core Features
- **Markdown Support**: Full markdown syntax support with live preview
- **Advanced Formatting**: Bold, italic, strikethrough, underline, headings (H1-H6)
- **Code Support**: Inline code and multi-line code blocks with syntax highlighting
- **Lists**: Bullet lists and numbered lists
- **Links & Media**: Easy link insertion and image upload with drag-and-drop
- **Tables**: Visual table editor with row/column management
- **Blockquotes & Horizontal Rules**: Rich content structuring

### 🎨 User Interface
- **Dual Mode**: Edit and preview modes with seamless switching
- **Customizable Toolbar**: Drag-and-drop toolbar customization
- **Responsive Design**: Works perfectly on desktop and mobile
- **Dark/Light Theme Support**: Adapts to system preferences
- **Keyboard Shortcuts**: Comprehensive keyboard shortcuts for power users

### 📊 Productivity Features
- **Auto-save**: Automatic content saving with visual indicators
- **Version History**: Track changes with restore functionality
- **Document Statistics**: Word count, character count, reading time, readability score
- **Export/Import**: Support for Markdown, HTML, and plain text formats
- **Draft Management**: Local storage for draft persistence

### 👥 Collaboration (Beta)
- **Real-time Editing**: Multi-user collaborative editing
- **User Presence**: See who's online and their cursor positions
- **Comments System**: Add and resolve comments on specific content
- **Activity Feed**: Track all document changes and user actions

## Usage

### Basic Usage

```tsx
import { RichTextEditor } from '@/components/blog/RichTextEditor';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <RichTextEditor
      value={content}
      onChange={setContent}
      placeholder="Start writing..."
    />
  );
}
```

### Advanced Usage

```tsx
import { RichTextEditor } from '@/components/blog/RichTextEditor';

function AdvancedEditor() {
  const [content, setContent] = useState('');

  const handleAutoSave = (content: string) => {
    // Save to your backend
    console.log('Auto-saving:', content);
  };

  return (
    <RichTextEditor
      value={content}
      onChange={setContent}
      placeholder="Write your story..."
      rows={25}
      autoSave={true}
      onAutoSave={handleAutoSave}
      showWordCount={true}
      showReadingTime={true}
      maxLength={50000}
      customToolbar={true}
      enableCollaboration={true}
      documentId="doc-123"
      className="my-custom-editor"
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | - | Current editor content (required) |
| `onChange` | `(value: string) => void` | - | Content change handler (required) |
| `placeholder` | `string` | "Start writing..." | Placeholder text |
| `rows` | `number` | 20 | Number of textarea rows |
| `className` | `string` | "" | Additional CSS classes |
| `autoSave` | `boolean` | false | Enable auto-save functionality |
| `onAutoSave` | `(content: string) => void` | - | Auto-save callback |
| `showWordCount` | `boolean` | true | Show word count in footer |
| `showReadingTime` | `boolean` | true | Show reading time estimate |
| `maxLength` | `number` | - | Maximum character limit |
| `customToolbar` | `boolean` | true | Enable toolbar customization |
| `enableCollaboration` | `boolean` | false | Enable collaboration features |
| `documentId` | `string` | auto-generated | Unique document identifier |

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl/Cmd + B` | Bold text |
| `Ctrl/Cmd + I` | Italic text |
| `Ctrl/Cmd + K` | Insert link |
| `Ctrl/Cmd + Z` | Undo |
| `Ctrl/Cmd + Y` | Redo |
| `Ctrl/Cmd + Shift + Z` | Redo (alternative) |
| `Ctrl/Cmd + \`` | Inline code |
| `Ctrl/Cmd + Enter` | Code block |

## Markdown Support

The editor supports all standard markdown syntax:

### Text Formatting
- `**bold**` or `__bold__` → **bold**
- `*italic*` or `_italic_` → *italic*
- `~~strikethrough~~` → ~~strikethrough~~
- `<u>underline</u>` → <u>underline</u>

### Headings
```markdown
# Heading 1
## Heading 2
### Heading 3
#### Heading 4
##### Heading 5
###### Heading 6
```

### Lists
```markdown
- Bullet list item
- Another item

1. Numbered list item
2. Another numbered item
```

### Links and Images
```markdown
[Link text](https://example.com)
![Image alt text](https://example.com/image.jpg)
```

### Code
```markdown
`inline code`

```javascript
// Code block
function hello() {
  console.log('Hello, world!');
}
```

### Tables
```markdown
| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |
```

### Other Elements
```markdown
> Blockquote text

---
Horizontal rule (above)
```

## Customization

### Toolbar Customization
Users can customize the toolbar by:
1. Clicking the settings icon in the toolbar
2. Enabling/disabling tools
3. Reordering tools by group
4. Resetting to default configuration

### Styling
The editor uses Tailwind CSS classes and can be customized by:
1. Overriding CSS classes
2. Using the `className` prop
3. Modifying the component's internal styles

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 18+
- TypeScript 4.5+
- Tailwind CSS 3+
- Heroicons
- date-fns
- react-dropzone

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Changelog

### v2.0.0 (Latest)
- ✨ Added collaboration features
- ✨ Enhanced toolbar customization
- ✨ Export/import functionality
- ✨ Advanced statistics panel
- ✨ Version history with restore
- 🐛 Fixed various bugs and improved performance

### v1.0.0
- 🎉 Initial release with basic markdown editing
- ✨ Live preview mode
- ✨ Basic formatting tools
- ✨ Auto-save functionality
