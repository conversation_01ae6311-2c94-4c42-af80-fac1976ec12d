import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { reports, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq } from "drizzle-orm";

const reportSchema = z.object({
  reason: z.enum(["spam", "harassment", "inappropriate_content", "impersonation", "other"]),
  description: z.string().max(1000).optional(),
});

export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const reportedUserId = params.userId;

    // Prevent reporting self
    if (reportedUserId === session.user.id) {
      return NextResponse.json(
        { message: "Cannot report yourself" },
        { status: 400 }
      );
    }

    // Check if the reported user exists
    const reportedUser = await db.query.users.findFirst({
      where: eq(users.id, reportedUserId),
      columns: {
        id: true,
      },
    });

    if (!reportedUser) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { reason, description } = reportSchema.parse(body);

    // Check if the user has already reported this user
    const existingReport = await db.query.reports.findFirst({
      where: (reports, { and, eq }) =>
        and(
          eq(reports.reporterId, session.user.id),
          eq(reports.reportedUserId, reportedUserId),
          eq(reports.status, "pending")
        ),
    });

    if (existingReport) {
      return NextResponse.json(
        { message: "You have already reported this user" },
        { status: 400 }
      );
    }

    // Create the report
    const reportId = uuidv4();
    await db.insert(reports).values({
      id: reportId,
      reporterId: session.user.id,
      reportedUserId: reportedUserId,
      reason,
      description: description || null,
      status: "pending",
    });

    return NextResponse.json(
      { message: "User reported successfully" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error reporting user:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
