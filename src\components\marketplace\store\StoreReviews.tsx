"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { StarIcon } from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";
import { StoreReviewForm } from "@/components/marketplace/StoreReviewForm";

interface StoreReviewsProps {
  storeId: string;
  isOwner: boolean;
  reviews: {
    id: string;
    rating: number;
    comment: string | null;
    createdAt: Date;
    user: {
      id: string;
      name: string | null;
      image: string | null;
    } | null;
  }[];
  userReview: {
    id: string;
    rating: number;
    comment: string | null;
  } | null;
  onReviewSubmitted: (newReview: any) => void;
}

export function StoreReviews({
  storeId,
  isOwner,
  reviews,
  userReview,
  onReviewSubmitted
}: StoreReviewsProps) {
  const [showReviewForm, setShowReviewForm] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Reviews</h2>
        {!isOwner && (
          <Button
            onClick={() => setShowReviewForm(true)}
            variant="outline"
            size="sm"
          >
            {userReview ? "Edit Your Review" : "Write a Review"}
          </Button>
        )}
      </div>

      {reviews.length > 0 ? (
        <div className="space-y-4">
          {reviews.map((review) => (
            <div key={review.id} className="rounded-lg bg-gray-50 p-4 shadow-sm hover:shadow-md transition-all duration-300">
              <div className="mb-2 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 overflow-hidden rounded-full">
                    {review.user?.image ? (
                      <OptimizedImage
                        src={review.user.image}
                        alt={review.user.name || 'User'}
                        width={32}
                        height={32}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-gray-200">
                        <span className="text-xs font-semibold text-gray-600">
                          {review.user?.name ? review.user.name.charAt(0).toUpperCase() : 'U'}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{review.user?.name || 'Anonymous User'}</p>
                    <p className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(review.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <StarIcon
                        key={star}
                        className={`h-5 w-5 ${
                          star <= review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="ml-1 text-sm font-medium text-gray-900">{review.rating}</span>
                </div>
              </div>
              {review.comment && (
                <p className="text-gray-600">{review.comment}</p>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="rounded-lg bg-gray-50 p-8 text-center">
          <StarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Be the first to review this store.
          </p>
          {!isOwner && (
            <div className="mt-4">
              <Button
                onClick={() => setShowReviewForm(true)}
                variant="outline"
              >
                Write a Review
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Review form modal */}
      {showReviewForm && (
        <StoreReviewForm
          storeId={storeId}
          existingReview={userReview}
          onClose={() => setShowReviewForm(false)}
          onReviewSubmitted={onReviewSubmitted}
        />
      )}
    </div>
  );
}
