"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  HeartIcon, 
  ChatBubbleOvalLeftIcon, 
  ShareIcon, 
  UserPlusIcon,
  AtSymbolIcon,
  EnvelopeIcon,
  ExclamationTriangleIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { MobileModal } from './MobileModal';
import { NotificationItem, MessageThread } from '@/hooks/useNotificationCount';
import { cn } from '@/lib/utils';
import { haptic } from '@/utils/haptics';

interface NotificationPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: NotificationItem[];
  messages: MessageThread[];
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteNotification: (id: string) => void;
  onMarkMessageAsRead: (threadId: string) => void;
  type: 'notifications' | 'messages';
}

export function NotificationPreview({
  isOpen,
  onClose,
  notifications,
  messages,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onMarkMessageAsRead,
  type
}: NotificationPreviewProps) {
  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('unread');

  const getNotificationIcon = (notificationType: string) => {
    switch (notificationType) {
      case 'like':
        return <HeartIcon className="w-5 h-5 text-red-500" />;
      case 'comment':
        return <ChatBubbleOvalLeftIcon className="w-5 h-5 text-blue-500" />;
      case 'share':
        return <ShareIcon className="w-5 h-5 text-green-500" />;
      case 'follow':
        return <UserPlusIcon className="w-5 h-5 text-purple-500" />;
      case 'mention':
        return <AtSymbolIcon className="w-5 h-5 text-orange-500" />;
      case 'message':
        return <EnvelopeIcon className="w-5 h-5 text-blue-500" />;
      case 'system':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-50';
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50';
      case 'low':
        return 'border-l-gray-500 bg-gray-50';
      default:
        return 'border-l-gray-300 bg-white';
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  };

  const handleNotificationClick = (notification: NotificationItem) => {
    haptic.light();
    if (!notification.read) {
      onMarkAsRead(notification.id);
    }
    if (notification.actionUrl) {
      onClose();
      // Navigate to the action URL
      window.location.href = notification.actionUrl;
    }
  };

  const handleMessageClick = (message: MessageThread) => {
    haptic.light();
    if (message.unreadCount > 0) {
      onMarkMessageAsRead(message.id);
    }
    onClose();
    // Navigate to message thread
    window.location.href = `/messages/${message.id}`;
  };

  const handleMarkAllAsRead = () => {
    haptic.medium();
    onMarkAllAsRead();
  };

  const handleDeleteNotification = (e: React.MouseEvent, notificationId: string) => {
    e.stopPropagation();
    haptic.light();
    onDeleteNotification(notificationId);
  };

  const filteredNotifications = notifications.filter(n => 
    activeTab === 'all' || !n.read
  );

  const unreadMessages = messages.filter(m => m.unreadCount > 0);

  return (
    <MobileModal
      isOpen={isOpen}
      onClose={onClose}
      title={type === 'notifications' ? 'Notifications' : 'Messages'}
      className="max-w-md"
    >
      <div className="max-h-[70vh] overflow-hidden flex flex-col">
        {type === 'notifications' && (
          <>
            {/* Tabs */}
            <div className="flex border-b border-gray-200 bg-white sticky top-0 z-10">
              <button
                onClick={() => setActiveTab('unread')}
                className={cn(
                  "flex-1 py-3 px-4 text-sm font-medium transition-colors",
                  activeTab === 'unread'
                    ? "text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-500 hover:text-gray-700"
                )}
              >
                Unread ({notifications.filter(n => !n.read).length})
              </button>
              <button
                onClick={() => setActiveTab('all')}
                className={cn(
                  "flex-1 py-3 px-4 text-sm font-medium transition-colors",
                  activeTab === 'all'
                    ? "text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-500 hover:text-gray-700"
                )}
              >
                All ({notifications.length})
              </button>
            </div>

            {/* Mark all as read button */}
            {notifications.some(n => !n.read) && (
              <div className="p-3 border-b border-gray-200 bg-gray-50">
                <button
                  onClick={handleMarkAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Mark all as read
                </button>
              </div>
            )}
          </>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {type === 'notifications' ? (
            filteredNotifications.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={cn(
                      "p-4 cursor-pointer hover:bg-gray-50 transition-colors border-l-4",
                      getPriorityColor(notification.priority),
                      !notification.read && "bg-blue-50"
                    )}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {notification.avatar ? (
                          <Image
                            src={notification.avatar}
                            alt=""
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                            {getNotificationIcon(notification.type)}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className={cn(
                              "text-sm",
                              !notification.read ? "font-semibold text-gray-900" : "text-gray-700"
                            )}>
                              {notification.title}
                            </p>
                            <p className="text-sm text-gray-500 mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-400 mt-1">
                              {formatTime(notification.timestamp)}
                            </p>
                          </div>
                          
                          <div className="flex items-center space-x-1 ml-2">
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full" />
                            )}
                            <button
                              onClick={(e) => handleDeleteNotification(e, notification.id)}
                              className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                            >
                              <XMarkIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center text-gray-500">
                <EnvelopeIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No notifications</p>
              </div>
            )
          ) : (
            unreadMessages.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {unreadMessages.map((message) => (
                  <div
                    key={message.id}
                    onClick={() => handleMessageClick(message)}
                    className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        {message.participantAvatar ? (
                          <Image
                            src={message.participantAvatar}
                            alt={message.participantName}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-600">
                              {message.participantName.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                        {message.isOnline && (
                          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full" />
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-semibold text-gray-900 truncate">
                            {message.participantName}
                          </p>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-400">
                              {formatTime(message.timestamp)}
                            </span>
                            {message.unreadCount > 0 && (
                              <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                                {message.unreadCount}
                              </span>
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-gray-500 truncate mt-1">
                          {message.lastMessage}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center text-gray-500">
                <ChatBubbleOvalLeftIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No new messages</p>
              </div>
            )
          )}
        </div>
      </div>
    </MobileModal>
  );
}
