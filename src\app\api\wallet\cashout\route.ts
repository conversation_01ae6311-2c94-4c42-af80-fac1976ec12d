import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { cashoutSchema } from "@/lib/wallet/validation";

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = cashoutSchema.parse(body);

    // Check if cashout is enabled
    const cashoutEnabled = await WalletService.getWalletSetting('cashout_enabled');
    if (cashoutEnabled === 'false') {
      return NextResponse.json(
        {
          success: false,
          message: "Cashout is currently disabled"
        },
        { status: 400 }
      );
    }

    // Process cashout request
    const requestId = await WalletService.requestCashout(session.user.id, {
      agentId: validatedData.agentId,
      amount: validatedData.amount,
      note: validatedData.note,
      pin: validatedData.pin,
    });

    return NextResponse.json({
      success: true,
      data: {
        requestId,
        message: "Cashout request submitted successfully. Please wait for agent approval.",
      },
    });
  } catch (error: any) {
    console.error("Error processing cashout:", error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid input data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: error.message || "Failed to process cashout request"
      },
      { status: 500 }
    );
  }
}

// Get user's cashout requests
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");

    const { searchParams } = url;
    const status = searchParams.get("status");

    // Get cashout requests from database
    const { db } = await import("@/lib/db");
    const { cashoutRequests, agents } = await import("@/lib/db/schema");
    const { eq, and, desc } = await import("drizzle-orm");

    let whereConditions = [eq(cashoutRequests.userId, session.user.id)];

    if (status) {
      whereConditions.push(eq(cashoutRequests.status, status as any));
    }

    const offset = (page - 1) * limit;

    const requests = await db.query.cashoutRequests.findMany({
      where: and(...whereConditions),
      orderBy: [desc(cashoutRequests.createdAt)],
      limit,
      offset,
      with: {
        agent: {
          columns: {
            id: true,
            name: true,
            serviceType: true,
            phone: true,
            location: true,
          },
        },
      },
    });

    const totalCount = await db
      .select({ count: count() })
      .from(cashoutRequests)
      .where(and(...whereConditions));

    return NextResponse.json({
      success: true,
      data: {
        requests,
        totalCount: totalCount[0].count,
        totalPages: Math.ceil(totalCount[0].count / limit),
        currentPage: page,
      },
    });
  } catch (error: any) {
    console.error("Error fetching cashout requests:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch cashout requests"
      },
      { status: 500 }
    );
  }
}

// Import count function
import { count } from "drizzle-orm";
