"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
  UserIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  UserPlusIcon,
  UserMinusIcon
} from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";
import { formatDistanceToNow } from "date-fns";
import { subscribeToUser, unsubscribeFromUser, getSubscriptionStatus } from "@/app/actions/subscription";

interface Follower {
  id: string;
  subscriberId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
  isFollowingBack?: boolean;
}

interface FollowersResponse {
  subscribers: Follower[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

interface FollowersListProps {
  searchQuery?: string;
  onCountChange?: () => void;
}

export function FollowersList({ searchQuery = "", onCountChange }: FollowersListProps) {
  const { data: session } = useSession();
  const [followers, setFollowers] = useState<Follower[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [pagination, setPagination] = useState({
    page: 1,
    hasMore: true
  });
  const [processingUsers, setProcessingUsers] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (session?.user) {
      fetchFollowers();
    }
  }, [session]);

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
    if (searchQuery !== localSearchQuery) {
      fetchFollowers(1, false);
    }
  }, [searchQuery]);

  const fetchFollowers = async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const params = new URLSearchParams({
        type: 'subscribers',
        limit: '20',
        offset: ((page - 1) * 20).toString(),
        ...(localSearchQuery && { search: localSearchQuery })
      });

      const response = await fetch(`/api/subscriptions?${params}`);

      if (!response.ok) {
        throw new Error('Failed to fetch followers');
      }

      const data: FollowersResponse = await response.json();

      // Check subscription status for each follower
      const followersWithStatus = await Promise.all(
        data.subscribers.map(async (follower) => {
          try {
            const statusResult = await getSubscriptionStatus(follower.subscriberId);
            return {
              ...follower,
              isFollowingBack: statusResult.success ? statusResult.isSubscribed : false
            };
          } catch (error) {
            console.error('Error checking subscription status:', error);
            return { ...follower, isFollowingBack: false };
          }
        })
      );

      if (append) {
        setFollowers(prev => [...prev, ...followersWithStatus]);
      } else {
        setFollowers(followersWithStatus);
      }

      setPagination({
        page: data.pagination.page,
        hasMore: data.pagination.hasMore
      });
    } catch (error) {
      console.error('Error fetching followers:', error);
      toast.error('Failed to load followers');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreFollowers = () => {
    if (pagination.hasMore && !loadingMore) {
      fetchFollowers(pagination.page + 1, true);
    }
  };

  const handleFollowBack = async (subscriberId: string, name: string) => {
    if (!session?.user) return;

    setProcessingUsers(prev => new Set(prev).add(subscriberId));

    try {
      const result = await subscribeToUser(subscriberId);

      if (result.success) {
        // Update local state
        setFollowers(prev => prev.map(sub =>
          sub.subscriberId === subscriberId
            ? { ...sub, isFollowingBack: true }
            : sub
        ));
        toast.success(`Following ${name}`);
        // Update parent counts
        onCountChange?.();
      } else {
        toast.error(result.message || 'Failed to follow');
      }
    } catch (error) {
      console.error('Error following:', error);
      toast.error('Failed to follow');
    } finally {
      setProcessingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(subscriberId);
        return newSet;
      });
    }
  };

  const handleUnfollowBack = async (subscriberId: string, name: string) => {
    if (!session?.user) return;

    setProcessingUsers(prev => new Set(prev).add(subscriberId));

    try {
      const result = await unsubscribeFromUser(subscriberId);

      if (result.success) {
        // Update local state
        setFollowers(prev => prev.map(sub =>
          sub.subscriberId === subscriberId
            ? { ...sub, isFollowingBack: false }
            : sub
        ));
        toast.success(`Unfollowed ${name}`);
        // Update parent counts
        onCountChange?.();
      } else {
        toast.error(result.message || 'Failed to unfollow');
      }
    } catch (error) {
      console.error('Error unfollowing:', error);
      toast.error('Failed to unfollow');
    } finally {
      setProcessingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(subscriberId);
        return newSet;
      });
    }
  };

  const handleStartChat = (follower: Follower) => {
    // This will be implemented when chat functionality is added
    toast.success(`Starting chat with ${follower.name}`);
  };

  // Filter followers based on search query
  const filteredFollowers = followers.filter(follower =>
    follower.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
    follower.username.toLowerCase().includes(localSearchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg animate-pulse">
            <div className="h-16 w-16 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
            <div className="flex space-x-2">
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (filteredFollowers.length === 0) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {localSearchQuery ? 'No subscribers found' : 'No subscribers yet'}
        </h3>
        <p className="text-gray-600 mb-4">
          {localSearchQuery
            ? `No subscribers match "${localSearchQuery}"`
            : "When people subscribe to you, they'll appear here."
          }
        </p>
        {!localSearchQuery && (
          <Link
            href="/connection?tab=suggestions"
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <UserIcon className="h-4 w-4 mr-2" />
            Discover People
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {filteredFollowers.map((follower) => (
        <div key={follower.id} className="group relative bg-gradient-to-br from-white to-purple-50/30 border border-purple-100 rounded-2xl p-6 hover:shadow-2xl hover:border-purple-300 transition-all duration-500 overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-purple-100/20 rounded-full -translate-y-16 translate-x-16 group-hover:scale-150 transition-transform duration-700"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-violet-100/20 rounded-full translate-y-12 -translate-x-12 group-hover:scale-150 transition-transform duration-700"></div>

          <div className="relative flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
            {/* Enhanced Profile Section */}
            <div className="flex items-center space-x-4 flex-1">
              <div className="relative">
                <div className="h-16 w-16 sm:h-20 sm:w-20 rounded-2xl ring-4 ring-purple-100 group-hover:ring-purple-200 transition-all duration-300 overflow-hidden shadow-lg">
                  {follower.image ? (
                    <OptimizedImage
                      src={follower.image}
                      alt={follower.name}
                      width={80}
                      height={80}
                      className="rounded-2xl object-cover w-full h-full group-hover:scale-110 transition-transform duration-300"
                    />
                  ) : (
                    <div className="h-full w-full bg-gradient-to-br from-purple-100 to-violet-100 rounded-2xl flex items-center justify-center group-hover:from-purple-200 group-hover:to-violet-200 transition-colors duration-300">
                      <UserIcon className="h-8 w-8 sm:h-10 sm:w-10 text-purple-600" />
                    </div>
                  )}
                </div>
                <div className="absolute -bottom-1 -right-1 h-5 w-5 bg-purple-400 border-3 border-white rounded-full animate-pulse shadow-lg"></div>
                <div className="absolute -top-1 -left-1 h-4 w-4 bg-violet-400 border-2 border-white rounded-full shadow-md"></div>
              </div>

              <div className="flex-1 min-w-0">
                <Link
                  href={`/user/${follower.username}`}
                  className="text-xl font-bold text-gray-900 hover:text-purple-600 transition-colors duration-200 block truncate group-hover:text-purple-600"
                >
                  {follower.name}
                </Link>
                <p className="text-sm text-gray-500 truncate font-medium">@{follower.username}</p>
                <div className="flex items-center gap-3 mt-3">
                  {follower.isFollowingBack ? (
                    <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 border border-blue-200 shadow-sm">
                      🤝 Mutual Connection
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-purple-100 text-purple-800 border border-purple-200 shadow-sm">
                      👤 Subscriber
                    </span>
                  )}
                  <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full font-medium">
                    {formatDistanceToNow(new Date(follower.createdAt), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>

            {/* Premium Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:min-w-[340px]">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleStartChat(follower)}
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 h-11 bg-gradient-to-r from-purple-50 to-violet-50 border-purple-200 text-purple-700 hover:from-purple-100 hover:to-violet-100 hover:border-purple-300 transition-all duration-300 shadow-sm hover:shadow-md"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
                <span>Message</span>
              </Button>

              <Link
                href={`/user/${follower.username}`}
                className="flex-1 sm:flex-none"
              >
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full h-11 flex items-center justify-center gap-2 bg-gradient-to-r from-gray-50 to-slate-50 border-gray-200 text-gray-700 hover:from-gray-100 hover:to-slate-100 hover:border-gray-300 transition-all duration-300 shadow-sm hover:shadow-md"
                >
                  <UserIcon className="h-4 w-4" />
                  <span>Profile</span>
                </Button>
              </Link>

              {follower.isFollowingBack ? (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleUnfollowBack(follower.subscriberId, follower.name)}
                  disabled={processingUsers.has(follower.subscriberId)}
                  className="flex-1 sm:flex-none flex items-center justify-center gap-2 h-11 bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-cyan-100 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-sm hover:shadow-md"
                >
                  <UserMinusIcon className="h-4 w-4" />
                  <span>
                    {processingUsers.has(follower.subscriberId) ? 'Processing...' : 'Subscribed'}
                  </span>
                </Button>
              ) : (
                <Button
                  size="sm"
                  variant="primary"
                  onClick={() => handleFollowBack(follower.subscriberId, follower.name)}
                  disabled={processingUsers.has(follower.subscriberId)}
                  className="flex-1 sm:flex-none flex items-center justify-center gap-2 h-11 bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <UserPlusIcon className="h-4 w-4" />
                  <span>
                    {processingUsers.has(follower.subscriberId) ? 'Processing...' : 'Subscribe Back'}
                  </span>
                </Button>
              )}
            </div>
          </div>
        </div>
      ))}

      {/* Load More Button */}
      {pagination.hasMore && (
        <div className="text-center pt-4">
          <Button
            variant="outline"
            onClick={loadMoreFollowers}
            disabled={loadingMore}
            className="px-6 sm:px-8"
          >
            {loadingMore ? 'Loading...' : 'Load More Subscribers'}
          </Button>
        </div>
      )}
    </div>
  );
}
