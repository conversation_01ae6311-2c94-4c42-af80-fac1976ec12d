import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { products, stores, users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and } from "drizzle-orm";

const productUpdateSchema = z.object({
  title: z.string().min(3).max(255).optional(),
  description: z.string().max(2000).optional(),
  price: z.number().min(0).optional(),
  condition: z.enum(['new', 'like_new', 'good', 'fair', 'poor']).optional(),
  category: z.string().min(1).max(100).optional(),
  location: z.string().max(255).optional(),
  photos: z.array(z.string().url()).min(1).max(10).optional(),
  storeId: z.string().optional(),
});

// Get a single product by ID
export async function GET(
  request: Request,
  context: { params: Promise<{ productId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { productId } = params;

    // Get product details
    const productDetails = await db
      .select({
        id: products.id,
        title: products.title,
        description: products.description,
        price: products.price,
        condition: products.item_condition,
        category: products.category,
        location: products.location,
        photos: products.photos,
        viewCount: products.viewCount,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
        store: {
          id: stores.id,
          name: stores.name,
          slug: stores.slug,
          logo: stores.logo,
          isVerified: stores.isVerified,
        },
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(products)
      .leftJoin(stores, eq(products.storeId, stores.id))
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(eq(products.id, productId))
      .limit(1);

    if (productDetails.length === 0) {
      return NextResponse.json(
        { message: "Product not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(productDetails[0]);
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { message: "Error fetching product" },
      { status: 500 }
    );
  }
}

// Update a product as admin
export async function PUT(
  request: Request,
  context: { params: Promise<{ productId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { productId } = params;
    const body = await request.json();
    const validatedData = productUpdateSchema.parse(body);

    // Check if product exists
    const existingProduct = await db
      .select({
        id: products.id,
      })
      .from(products)
      .where(eq(products.id, productId))
      .limit(1);

    if (existingProduct.length === 0) {
      return NextResponse.json(
        { message: "Product not found" },
        { status: 404 }
      );
    }

    // If storeId is provided, check if store exists
    if (validatedData.storeId) {
      const storeExists = await db
        .select({ id: stores.id })
        .from(stores)
        .where(eq(stores.id, validatedData.storeId))
        .limit(1);

      if (storeExists.length === 0) {
        return NextResponse.json(
          { message: "Store not found" },
          { status: 404 }
        );
      }
    }

    // Update product
    await db
      .update(products)
      .set({
        ...validatedData,
        item_condition: validatedData.condition,
        updatedAt: new Date(),
      })
      .where(eq(products.id, productId));

    // Get updated product
    const updatedProduct = await db
      .select()
      .from(products)
      .where(eq(products.id, productId))
      .limit(1);

    return NextResponse.json({
      message: "Product updated successfully",
      product: updatedProduct[0],
    });
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { message: "Error updating product" },
      { status: 500 }
    );
  }
}

// Delete a product as admin
export async function DELETE(
  request: Request,
  context: { params: Promise<{ productId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { productId } = params;

    // Check if product exists
    const existingProduct = await db
      .select({
        id: products.id,
      })
      .from(products)
      .where(eq(products.id, productId))
      .limit(1);

    if (existingProduct.length === 0) {
      return NextResponse.json(
        { message: "Product not found" },
        { status: 404 }
      );
    }

    // Delete product
    await db
      .delete(products)
      .where(eq(products.id, productId));

    return NextResponse.json({
      message: "Product deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { message: "Error deleting product" },
      { status: 500 }
    );
  }
}
