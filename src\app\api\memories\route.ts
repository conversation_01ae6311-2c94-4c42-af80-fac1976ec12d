import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts } from "@/lib/db/schema";
import { and, eq, or, desc, sql } from "drizzle-orm";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const type = url.searchParams.get("type") || "onThisDay"; // onThisDay, recent

    // Get current date
    const today = new Date();
    const currentMonth = today.getMonth() + 1; // JavaScript months are 0-indexed
    const currentDay = today.getDate();
    
    if (type === "onThisDay") {
      // Find posts from previous years on the same day and month
      const onThisDayPosts = await db.query.posts.findMany({
        where: and(
          eq(posts.userId, session.user.id),
          sql`MONTH(${posts.createdAt}) = ${currentMonth}`,
          sql`DAY(${posts.createdAt}) = ${currentDay}`,
          sql`YEAR(${posts.createdAt}) < YEAR(CURRENT_DATE())`
        ),
        orderBy: [desc(posts.createdAt)],
        with: {
          user: {
            columns: {
              id: true,
              name: true,
              username: true,
              image: true,
            },
          },
          likes: true,
          comments: {
            columns: {
              id: true,
            },
          },
          sharedPost: {
            with: {
              user: {
                columns: {
                  id: true,
                  name: true,
                  username: true,
                  image: true,
                },
              },
            },
          },
          shares: true,
        },
      });

      // Format posts for the frontend
      const formattedPosts = onThisDayPosts.map((post) => {
        // Count likes and dislikes
        const likesCount = post.likes.filter(like => like.type === 'like').length;
        const dislikesCount = post.likes.filter(like => like.type === 'dislike').length;

        // Check if the current user has liked or disliked this post
        const userLike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'like'
        );

        const userDislike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'dislike'
        );

        // Calculate years ago
        const postDate = new Date(post.createdAt);
        const yearsAgo = today.getFullYear() - postDate.getFullYear();

        return {
          id: post.id,
          content: post.content,
          images: post.images,
          videos: post.videos,
          privacy: post.privacy,
          backgroundColor: post.backgroundColor,
          feeling: post.feeling,
          activity: post.activity,
          location: post.location,
          formattedContent: post.formattedContent,
          createdAt: post.createdAt.toISOString(),
          yearsAgo: yearsAgo,
          user: post.user,
          _count: {
            likes: likesCount,
            dislikes: dislikesCount,
            comments: post.comments.length,
            shares: post.shares.length,
          },
          liked: !!userLike,
          disliked: !!userDislike,
          sharedPost: post.sharedPost,
        };
      });

      return NextResponse.json({ memories: formattedPosts });
    } else if (type === "recent") {
      // Get posts from the past 3 years, excluding today's date
      const threeYearsAgo = new Date();
      threeYearsAgo.setFullYear(today.getFullYear() - 3);

      const recentMemories = await db.query.posts.findMany({
        where: and(
          eq(posts.userId, session.user.id),
          sql`${posts.createdAt} BETWEEN ${threeYearsAgo} AND ${today}`,
          or(
            sql`MONTH(${posts.createdAt}) != ${currentMonth}`,
            sql`DAY(${posts.createdAt}) != ${currentDay}`
          )
        ),
        orderBy: [desc(posts.createdAt)],
        limit: 10,
        with: {
          user: {
            columns: {
              id: true,
              name: true,
              username: true,
              image: true,
            },
          },
          likes: true,
          comments: {
            columns: {
              id: true,
            },
          },
          sharedPost: {
            with: {
              user: {
                columns: {
                  id: true,
                  name: true,
                  username: true,
                  image: true,
                },
              },
            },
          },
          shares: true,
        },
      });

      // Format posts for the frontend
      const formattedPosts = recentMemories.map((post) => {
        // Count likes and dislikes
        const likesCount = post.likes.filter(like => like.type === 'like').length;
        const dislikesCount = post.likes.filter(like => like.type === 'dislike').length;

        // Check if the current user has liked or disliked this post
        const userLike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'like'
        );

        const userDislike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'dislike'
        );

        return {
          id: post.id,
          content: post.content,
          images: post.images,
          videos: post.videos,
          privacy: post.privacy,
          backgroundColor: post.backgroundColor,
          feeling: post.feeling,
          activity: post.activity,
          location: post.location,
          formattedContent: post.formattedContent,
          createdAt: post.createdAt.toISOString(),
          user: post.user,
          _count: {
            likes: likesCount,
            dislikes: dislikesCount,
            comments: post.comments.length,
            shares: post.shares.length,
          },
          liked: !!userLike,
          disliked: !!userDislike,
          sharedPost: post.sharedPost,
        };
      });

      return NextResponse.json({ memories: formattedPosts });
    }

    return NextResponse.json(
      { message: "Invalid memory type" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error fetching memories:", error);
    return NextResponse.json(
      { message: "Failed to fetch memories" },
      { status: 500 }
    );
  }
}
