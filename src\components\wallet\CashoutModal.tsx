"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, StarIcon, MapPinIcon, PhoneIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { PinInputField } from "@/components/ui/PinInput";
import { cashoutSchema, type CashoutFormData } from "@/lib/wallet/validation";
import { toast } from "react-hot-toast";

interface Agent {
  id: string;
  name: string;
  phone: string;
  location: string;
  serviceType: string;
  commission: string;
  rating: string;
  totalTransactions: number;
  dailyLimit: string;
  currentDailyAmount: string;
}

interface CashoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function CashoutModal({ isOpen, onClose, onSuccess }: CashoutModalProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(false);
  const [agentsLoading, setAgentsLoading] = useState(true);
  const [step, setStep] = useState<'select' | 'amount' | 'confirm'>('select');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<CashoutFormData>({
    resolver: zodResolver(cashoutSchema),
  });

  const amount = watch("amount");
  const note = watch("note");
  const pin = watch("pin");

  // Fetch agents
  useEffect(() => {
    if (isOpen) {
      fetchAgents();
    }
  }, [isOpen]);

  const fetchAgents = async () => {
    try {
      const response = await fetch('/api/wallet/agents');
      const data = await response.json();

      if (data.success) {
        setAgents(data.data);
      } else {
        toast.error('Failed to fetch agents');
      }
    } catch (error) {
      console.error('Error fetching agents:', error);
      toast.error('Failed to fetch agents');
    } finally {
      setAgentsLoading(false);
    }
  };

  const selectAgent = (agent: Agent) => {
    setSelectedAgent(agent);
    setValue("agentId", agent.id);
    setStep('amount');
  };

  const proceedToConfirm = () => {
    if (!amount || parseFloat(amount) < 10) {
      toast.error('Minimum cashout amount is $10');
      return;
    }
    setStep('confirm');
  };

  // Calculate commission
  const calculateCommission = () => {
    if (!selectedAgent || !amount) return { commission: 0, netAmount: 0 };

    const cashoutAmount = parseFloat(amount);
    const commission = (cashoutAmount * parseFloat(selectedAgent.commission)) / 100;
    const netAmount = cashoutAmount - commission;

    return { commission, netAmount };
  };

  const { commission, netAmount } = calculateCommission();

  const onSubmit = async (data: CashoutFormData) => {
    if (!selectedAgent) {
      toast.error('Please select an agent');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/wallet/cashout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId: data.agentId,
          amount: data.amount,
          note: data.note,
          pin: data.pin,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Cashout request submitted successfully!');
        onSuccess();
        handleClose();
      } else {
        toast.error(result.message || 'Failed to submit cashout request');
      }
    } catch (error) {
      console.error('Error submitting cashout:', error);
      toast.error('Failed to submit cashout request');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedAgent(null);
    setStep('select');
    onClose();
  };

  const goBack = () => {
    if (step === 'confirm') {
      setStep('amount');
    } else if (step === 'amount') {
      setStep('select');
      setSelectedAgent(null);
    }
  };

  const getServiceTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'bkash':
        return 'bg-pink-100 text-pink-800';
      case 'nagad':
        return 'bg-orange-100 text-orange-800';
      case 'rocket':
        return 'bg-purple-100 text-purple-800';
      case 'bank':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full bg-white rounded-lg shadow-xl">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              {step !== 'select' && (
                <button
                  onClick={goBack}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <DialogTitle className="text-lg font-semibold text-gray-900">
                Cashout Request
              </DialogTitle>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6">
            {/* Step 1: Select Agent */}
            {step === 'select' && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Select Cashout Agent
                  </h3>
                  <p className="text-xs text-gray-500 mb-4">
                    Choose an agent to process your cashout request
                  </p>
                </div>

                {agentsLoading ? (
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="h-20 bg-gray-200 rounded-lg animate-pulse" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {agents.map((agent) => {
                      const availableLimit = parseFloat(agent.dailyLimit) - parseFloat(agent.currentDailyAmount);

                      return (
                        <button
                          key={agent.id}
                          onClick={() => selectAgent(agent)}
                          className="w-full p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
                          disabled={availableLimit <= 0}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h4 className="font-medium text-gray-900">{agent.name}</h4>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getServiceTypeColor(agent.serviceType)}`}>
                                  {agent.serviceType}
                                </span>
                              </div>

                              <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                                <div className="flex items-center space-x-1">
                                  <PhoneIcon className="h-3 w-3" />
                                  <span>{agent.phone}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <MapPinIcon className="h-3 w-3" />
                                  <span>{agent.location}</span>
                                </div>
                              </div>

                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-1">
                                  <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                                  <span className="text-sm font-medium">{parseFloat(agent.rating).toFixed(1)}</span>
                                  <span className="text-xs text-gray-500">({agent.totalTransactions} transactions)</span>
                                </div>
                                <div className="text-xs text-gray-500">
                                  Fee: {agent.commission}%
                                </div>
                              </div>

                              <div className="mt-2">
                                <div className="flex justify-between text-xs">
                                  <span className="text-gray-500">Available today:</span>
                                  <span className={availableLimit > 0 ? 'text-green-600' : 'text-red-600'}>
                                    ${availableLimit.toFixed(2)}
                                  </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                  <div
                                    className="bg-blue-600 h-1.5 rounded-full"
                                    style={{
                                      width: `${Math.min((parseFloat(agent.currentDailyAmount) / parseFloat(agent.dailyLimit)) * 100, 100)}%`
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>

                          {availableLimit <= 0 && (
                            <div className="mt-2 text-xs text-red-600">
                              Daily limit reached
                            </div>
                          )}
                        </button>
                      );
                    })}

                    {agents.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <p>No agents available at the moment</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Step 2: Enter Amount */}
            {step === 'amount' && selectedAgent && (
              <div className="space-y-6">
                {/* Selected Agent */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{selectedAgent.name}</div>
                      <div className="text-sm text-gray-500">{selectedAgent.serviceType} • {selectedAgent.location}</div>
                    </div>
                    <span className="text-sm text-gray-600">Fee: {selectedAgent.commission}%</span>
                  </div>
                </div>

                {/* Amount Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cashout Amount
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <Input
                      {...register("amount")}
                      type="number"
                      step="0.01"
                      min="10"
                      max="2000"
                      placeholder="0.00"
                      className="pl-7"
                    />
                  </div>
                  {errors.amount && (
                    <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Min: $10.00 - Max: $2,000.00
                  </p>
                </div>

                {/* Note */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Note (Optional)
                  </label>
                  <textarea
                    {...register("note")}
                    rows={3}
                    placeholder="Add any special instructions..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.note && (
                    <p className="text-sm text-red-600 mt-1">{errors.note.message}</p>
                  )}
                </div>

                <Button
                  onClick={proceedToConfirm}
                  className="w-full"
                  disabled={!amount || parseFloat(amount) < 10}
                >
                  Continue
                </Button>
              </div>
            )}

            {/* Step 3: Confirm & PIN */}
            {step === 'confirm' && selectedAgent && (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Transaction Summary */}
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <h4 className="font-medium text-gray-900">Cashout Summary</h4>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Agent:</span>
                      <span className="font-medium">{selectedAgent.name}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Service:</span>
                      <span className="font-medium">{selectedAgent.serviceType}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Phone:</span>
                      <span className="font-medium">{selectedAgent.phone}</span>
                    </div>
                  </div>

                  <div className="border-t border-gray-200 pt-3 space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Cashout Amount:</span>
                      <span className="font-medium">${parseFloat(amount || "0").toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Agent Fee ({selectedAgent.commission}%):</span>
                      <span className="font-medium">${commission.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between border-t border-gray-200 pt-1">
                      <span className="text-gray-900 font-medium">You'll Receive:</span>
                      <span className="font-bold text-green-600">${netAmount.toFixed(2)}</span>
                    </div>
                  </div>

                  {note && (
                    <div className="border-t border-gray-200 pt-3">
                      <span className="text-sm text-gray-600">Note:</span>
                      <p className="text-sm text-gray-900 mt-1">{note}</p>
                    </div>
                  )}
                </div>

                {/* PIN Input */}
                <PinInputField
                  label="Enter your wallet PIN"
                  description="Enter your 4-digit PIN to confirm this cashout request"
                  value={pin || ""}
                  onChange={(value) => setValue("pin", value)}
                  error={errors.pin?.message}
                  required
                  length={4}
                  autoFocus
                />

                {/* Submit Button */}
                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={goBack}
                    className="flex-1"
                    disabled={loading}
                  >
                    Back
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={loading || !pin || pin.length < 4}
                    isLoading={loading}
                  >
                    {loading ? 'Submitting...' : 'Submit Request'}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
