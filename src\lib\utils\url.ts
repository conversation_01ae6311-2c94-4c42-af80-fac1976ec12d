// URL utility functions for dynamic domain detection

/**
 * Get the application URL dynamically based on environment and request context
 * @param req - Optional request object for server-side detection
 * @returns The base URL of the application
 */
export function getAppUrl(req?: any): string {
  // If NEXT_PUBLIC_APP_URL is set, use it
  if (process.env.NEXT_PUBLIC_APP_URL) {
    return process.env.NEXT_PUBLIC_APP_URL;
  }

  // For client side (browser)
  if (typeof window !== 'undefined') {
    return `${window.location.protocol}//${window.location.host}`;
  }

  // For server side with request object
  if (req) {
    const protocol = req.headers?.['x-forwarded-proto'] || 
                    (req.connection?.encrypted ? 'https' : 'http');
    const host = req.headers?.['x-forwarded-host'] || 
                req.headers?.host || 
                'localhost:3001';
    return `${protocol}://${host}`;
  }

  // Fallback for server side without request
  return process.env.NEXTAUTH_URL || 'http://localhost:3001';
}

/**
 * Generate a referral URL with the given referral code
 * @param referralCode - The referral code to include in the URL
 * @param req - Optional request object for server-side detection
 * @returns Complete referral URL
 */
export function generateReferralUrl(referralCode: string, req?: any): string {
  const baseUrl = getAppUrl(req);
  return `${baseUrl}/signup?ref=${referralCode}`;
}

/**
 * Client-side URL helper for components
 * @returns The base URL of the application (client-side only)
 */
export function getClientAppUrl(): string {
  if (typeof window !== 'undefined') {
    return `${window.location.protocol}//${window.location.host}`;
  }
  return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';
}
