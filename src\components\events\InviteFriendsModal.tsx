"use client";

import { useState, useEffect } from "react";
import { <PERSON>alog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, UserIcon, MagnifyingGlassIcon, UserPlusIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import Image from "next/image";

interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  image?: string;
}

interface InviteFriendsModalProps {
  isOpen: boolean;
  onClose: () => void;
  eventId: string;
}

export function InviteFriendsModal({ isOpen, onClose, eventId }: InviteFriendsModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isInviting, setIsInviting] = useState(false);

  // Search for users
  const searchUsers = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}`);
      const data = await response.json();

      if (data.success) {
        // Filter out already selected users
        const filteredResults = data.data.filter(
          (user: User) => !selectedUsers.some(selected => selected.id === user.id)
        );
        setSearchResults(filteredResults);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      searchUsers(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, selectedUsers]);

  const handleUserSelect = (user: User) => {
    setSelectedUsers(prev => [...prev, user]);
    setSearchResults(prev => prev.filter(u => u.id !== user.id));
    setSearchTerm("");
  };

  const handleUserRemove = (userId: string) => {
    setSelectedUsers(prev => prev.filter(user => user.id !== userId));
  };

  const handleSendInvites = async () => {
    if (selectedUsers.length === 0) {
      toast.error("Please select at least one user to invite");
      return;
    }

    setIsInviting(true);
    try {
      const response = await fetch(`/api/events/${eventId}/invites`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userIds: selectedUsers.map(user => user.id),
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Invites sent to ${selectedUsers.length} user(s)`);
        onClose();
        setSelectedUsers([]);
        setSearchTerm("");
        setSearchResults([]);
      } else {
        toast.error(data.message || "Failed to send invites");
      }
    } catch (error) {
      console.error('Error sending invites:', error);
      toast.error("Failed to send invites");
    } finally {
      setIsInviting(false);
    }
  };

  const handleClose = () => {
    setSelectedUsers([]);
    setSearchTerm("");
    setSearchResults([]);
    onClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full bg-white rounded-lg shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <UserPlusIcon className="h-5 w-5 text-blue-600" />
              </div>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                Invite Friends
              </DialogTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Search Input */}
            <div className="mb-4">
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  type="text"
                  placeholder="Search users by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Selected Users */}
            {selectedUsers.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Selected Users ({selectedUsers.length})
                </h4>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {selectedUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
                    >
                      <div className="flex items-center space-x-2">
                        {user.image ? (
                          <Image
                            src={user.image}
                            alt={user.name}
                            width={24}
                            height={24}
                            className="h-6 w-6 rounded-full"
                          />
                        ) : (
                          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200">
                            <UserIcon className="h-4 w-4 text-gray-500" />
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium">{user.name}</div>
                          <div className="text-xs text-gray-500">@{user.username}</div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUserRemove(user.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Search Results */}
            {searchTerm.length >= 2 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Search Results</h4>
                <div className="border border-gray-200 rounded-md max-h-48 overflow-y-auto">
                  {isSearching ? (
                    <div className="flex items-center justify-center py-4">
                      <Spinner size="sm" />
                    </div>
                  ) : searchResults.length > 0 ? (
                    <div className="divide-y divide-gray-200">
                      {searchResults.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer"
                          onClick={() => handleUserSelect(user)}
                        >
                          <div className="flex items-center space-x-3">
                            {user.image ? (
                              <Image
                                src={user.image}
                                alt={user.name}
                                width={32}
                                height={32}
                                className="h-8 w-8 rounded-full"
                              />
                            ) : (
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                                <UserIcon className="h-5 w-5 text-gray-500" />
                              </div>
                            )}
                            <div>
                              <div className="text-sm font-medium">{user.name}</div>
                              <div className="text-xs text-gray-500">@{user.username}</div>
                            </div>
                          </div>
                          <UserPlusIcon className="h-5 w-5 text-gray-400" />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500 text-sm">
                      No users found
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSendInvites}
              disabled={selectedUsers.length === 0 || isInviting}
              className="min-w-[100px]"
            >
              {isInviting ? (
                <Spinner size="sm" />
              ) : (
                `Send Invites (${selectedUsers.length})`
              )}
            </Button>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
