import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { 
  users, 
  posts, 
  blogs, 
  groups, 
  reports, 
  subscriptions,
  walletTransactions,
  referrals
} from "@/lib/db/schema";
import { eq, and, sql, desc, gte, lte, count, sum, isNotNull } from "drizzle-orm";

// Get recent activity for admin dashboard
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const activities = [];

    // Recent user registrations
    const recentUsers = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        createdAt: users.createdAt,
      })
      .from(users)
      .orderBy(desc(users.createdAt))
      .limit(5);

    recentUsers.forEach(user => {
      activities.push({
        id: `user-${user.id}`,
        type: 'user_registration',
        title: 'New user registered',
        description: `${user.name || user.username || 'Unknown User'} created an account`,
        timestamp: user.createdAt,
        icon: 'user',
        color: 'blue'
      });
    });

    // Recent posts
    const recentPosts = await db
      .select({
        id: posts.id,
        content: posts.content,
        authorId: posts.authorId,
        createdAt: posts.createdAt,
        author: {
          name: users.name,
          username: users.username,
        }
      })
      .from(posts)
      .innerJoin(users, eq(posts.authorId, users.id))
      .orderBy(desc(posts.createdAt))
      .limit(5);

    recentPosts.forEach(post => {
      activities.push({
        id: `post-${post.id}`,
        type: 'post_created',
        title: 'New post created',
        description: `${post.author.name || post.author.username || 'Unknown User'} published a new post`,
        timestamp: post.createdAt,
        icon: 'document',
        color: 'green'
      });
    });

    // Recent blogs
    const recentBlogs = await db
      .select({
        id: blogs.id,
        title: blogs.title,
        authorId: blogs.authorId,
        status: blogs.status,
        createdAt: blogs.createdAt,
        author: {
          name: users.name,
          username: users.username,
        }
      })
      .from(blogs)
      .innerJoin(users, eq(blogs.authorId, users.id))
      .where(eq(blogs.status, 'published'))
      .orderBy(desc(blogs.createdAt))
      .limit(3);

    recentBlogs.forEach(blog => {
      activities.push({
        id: `blog-${blog.id}`,
        type: 'blog_published',
        title: 'New blog published',
        description: `${blog.author.name || blog.author.username || 'Unknown User'} published "${blog.title}"`,
        timestamp: blog.createdAt,
        icon: 'document-text',
        color: 'purple'
      });
    });

    // Recent reports
    const recentReports = await db
      .select({
        id: reports.id,
        reason: reports.reason,
        status: reports.status,
        createdAt: reports.createdAt,
        reporter: {
          name: users.name,
          username: users.username,
        }
      })
      .from(reports)
      .innerJoin(users, eq(reports.reporterId, users.id))
      .orderBy(desc(reports.createdAt))
      .limit(3);

    recentReports.forEach(report => {
      activities.push({
        id: `report-${report.id}`,
        type: 'report_submitted',
        title: 'New report submitted',
        description: `${report.reporter.name || report.reporter.username || 'Unknown User'} reported content for ${report.reason}`,
        timestamp: report.createdAt,
        icon: 'flag',
        color: 'red'
      });
    });

    // Recent subscriptions
    const recentSubscriptions = await db
      .select({
        id: subscriptions.id,
        planName: subscriptions.planName,
        status: subscriptions.status,
        createdAt: subscriptions.createdAt,
        user: {
          name: users.name,
          username: users.username,
        }
      })
      .from(subscriptions)
      .innerJoin(users, eq(subscriptions.userId, users.id))
      .where(eq(subscriptions.status, 'active'))
      .orderBy(desc(subscriptions.createdAt))
      .limit(3);

    recentSubscriptions.forEach(subscription => {
      activities.push({
        id: `subscription-${subscription.id}`,
        type: 'subscription_created',
        title: 'New subscription',
        description: `${subscription.user.name || subscription.user.username || 'Unknown User'} subscribed to ${subscription.planName}`,
        timestamp: subscription.createdAt,
        icon: 'credit-card',
        color: 'indigo'
      });
    });

    // Recent wallet transactions (deposits)
    const recentDeposits = await db
      .select({
        id: walletTransactions.id,
        amount: walletTransactions.amount,
        status: walletTransactions.status,
        createdAt: walletTransactions.createdAt,
        user: {
          name: users.name,
          username: users.username,
        }
      })
      .from(walletTransactions)
      .innerJoin(users, eq(walletTransactions.userId, users.id))
      .where(
        and(
          eq(walletTransactions.type, 'deposit'),
          eq(walletTransactions.status, 'completed')
        )
      )
      .orderBy(desc(walletTransactions.createdAt))
      .limit(3);

    recentDeposits.forEach(deposit => {
      activities.push({
        id: `deposit-${deposit.id}`,
        type: 'wallet_deposit',
        title: 'Wallet deposit',
        description: `${deposit.user.name || deposit.user.username || 'Unknown User'} deposited $${parseFloat(deposit.amount).toFixed(2)}`,
        timestamp: deposit.createdAt,
        icon: 'banknotes',
        color: 'emerald'
      });
    });

    // Recent referrals
    const recentReferrals = await db
      .select({
        id: referrals.id,
        status: referrals.status,
        createdAt: referrals.createdAt,
        referrer: {
          name: users.name,
          username: users.username,
        }
      })
      .from(referrals)
      .innerJoin(users, eq(referrals.referrerId, users.id))
      .where(eq(referrals.status, 'completed'))
      .orderBy(desc(referrals.createdAt))
      .limit(2);

    recentReferrals.forEach(referral => {
      activities.push({
        id: `referral-${referral.id}`,
        type: 'referral_completed',
        title: 'Referral completed',
        description: `${referral.referrer.name || referral.referrer.username || 'Unknown User'} earned a referral reward`,
        timestamp: referral.createdAt,
        icon: 'user-group',
        color: 'yellow'
      });
    });

    // Sort all activities by timestamp (most recent first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Take only the most recent 15 activities
    const recentActivities = activities.slice(0, 15).map(activity => ({
      ...activity,
      timestamp: activity.timestamp.toISOString(),
      timeAgo: getTimeAgo(activity.timestamp)
    }));

    return NextResponse.json({
      success: true,
      data: recentActivities,
    });

  } catch (error) {
    console.error("Error fetching recent activity:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch recent activity"
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate time ago
function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months} month${months > 1 ? 's' : ''} ago`;
  }
}
