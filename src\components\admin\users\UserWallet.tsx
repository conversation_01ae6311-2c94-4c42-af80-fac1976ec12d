"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { toast } from "react-hot-toast";
import {
  BanknotesIcon,
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PaperAirplaneIcon,
  InboxIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  EyeIcon,
  PlusIcon,
} from "@heroicons/react/24/outline";
import { ViewTransactionsModal } from "./modals/ViewTransactionsModal";
import { AddFundsModal } from "./modals/AddFundsModal";
import { WalletSettingsModal } from "./modals/WalletSettingsModal";

interface User {
  id: string;
  name: string;
  email: string;
  wallet: {
    generalBalance: string;
    earningBalance: string;
    totalDeposited: string;
    totalWithdrawn: string;
    totalSent: string;
    totalReceived: string;
    isActive: boolean;
  };
  totalBalance: string;
}

interface UserWalletProps {
  user: User;
  onUserUpdate: () => void;
}

export function UserWallet({ user, onUserUpdate }: UserWalletProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  // Modal states
  const [showTransactionsModal, setShowTransactionsModal] = useState(false);
  const [showAddFundsModal, setShowAddFundsModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  const handleToggleWalletStatus = async () => {
    try {
      setIsUpdating(true);
      // This would be implemented when wallet status toggle API is available
      toast.success(
        user.wallet.isActive
          ? "Wallet deactivated successfully"
          : "Wallet activated successfully"
      );
      onUserUpdate();
    } catch (error) {
      console.error("Error toggling wallet status:", error);
      toast.error("Failed to update wallet status");
    } finally {
      setIsUpdating(false);
    }
  };

  const walletStats = [
    {
      title: "General Balance",
      value: user.wallet.generalBalance,
      icon: BanknotesIcon,
      color: "blue",
      description: "Available for spending",
    },
    {
      title: "Earning Balance",
      value: user.wallet.earningBalance,
      icon: CurrencyDollarIcon,
      color: "green",
      description: "Earned from activities",
    },
    {
      title: "Total Deposited",
      value: user.wallet.totalDeposited,
      icon: ArrowDownIcon,
      color: "purple",
      description: "Total amount deposited",
    },
    {
      title: "Total Withdrawn",
      value: user.wallet.totalWithdrawn,
      icon: ArrowUpIcon,
      color: "red",
      description: "Total amount withdrawn",
    },
    {
      title: "Total Sent",
      value: user.wallet.totalSent,
      icon: PaperAirplaneIcon,
      color: "orange",
      description: "Total amount sent to others",
    },
    {
      title: "Total Received",
      value: user.wallet.totalReceived,
      icon: InboxIcon,
      color: "teal",
      description: "Total amount received from others",
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "bg-blue-50 text-blue-600 border-blue-200",
      green: "bg-green-50 text-green-600 border-green-200",
      purple: "bg-purple-50 text-purple-600 border-purple-200",
      red: "bg-red-50 text-red-600 border-red-200",
      orange: "bg-orange-50 text-orange-600 border-orange-200",
      teal: "bg-teal-50 text-teal-600 border-teal-200",
    } as const;

    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="space-y-6">
      {/* Wallet Status */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Wallet Status</h3>
          <Badge variant={user.wallet.isActive ? "success" : "secondary"}>
            {user.wallet.isActive ? "Active" : "Inactive"}
          </Badge>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">
              Wallet is currently {user.wallet.isActive ? "active" : "inactive"}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {user.wallet.isActive
                ? "User can perform wallet transactions"
                : "User cannot perform wallet transactions"
              }
            </p>
          </div>
          <Button
            variant={user.wallet.isActive ? "outline" : "primary"}
            onClick={handleToggleWalletStatus}
            disabled={isUpdating}
          >
            {user.wallet.isActive ? "Deactivate Wallet" : "Activate Wallet"}
          </Button>
        </div>
      </Card>

      {/* Total Balance Overview */}
      <Card className="p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Balance</h3>
          <div className="text-4xl font-bold text-green-600 mb-2">
            ৳{parseFloat(user.totalBalance).toLocaleString()}
          </div>
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
            <div>
              <span className="font-medium">General: </span>
              ৳{parseFloat(user.wallet.generalBalance).toLocaleString()}
            </div>
            <div>
              <span className="font-medium">Earning: </span>
              ৳{parseFloat(user.wallet.earningBalance).toLocaleString()}
            </div>
          </div>
        </div>
      </Card>

      {/* Wallet Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {walletStats.map((stat, index) => (
          <Card key={index} className={`p-6 border-l-4 ${getColorClasses(stat.color)}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-900">{stat.title}</p>
                <p className="text-2xl font-bold mt-1">
                  ৳{parseFloat(stat.value).toLocaleString()}
                </p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
              <div className={`p-3 rounded-full ${getColorClasses(stat.color)}`}>
                <stat.icon className="h-6 w-6" />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Wallet Analytics */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Wallet Analytics</h3>
          <Button variant="outline" size="sm">
            <ChartBarIcon className="h-4 w-4 mr-2" />
            View Full Analytics
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Transaction Summary */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Transaction Summary</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Net Deposits</span>
                <span className="text-sm font-medium text-green-600">
                  +৳{(parseFloat(user.wallet.totalDeposited) - parseFloat(user.wallet.totalWithdrawn)).toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Net Transfers</span>
                <span className="text-sm font-medium text-blue-600">
                  {parseFloat(user.wallet.totalReceived) - parseFloat(user.wallet.totalSent) >= 0 ? '+' : ''}
                  ৳{(parseFloat(user.wallet.totalReceived) - parseFloat(user.wallet.totalSent)).toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Activity</span>
                <span className="text-sm font-medium text-purple-600">
                  ৳{(
                    parseFloat(user.wallet.totalDeposited) +
                    parseFloat(user.wallet.totalWithdrawn) +
                    parseFloat(user.wallet.totalSent) +
                    parseFloat(user.wallet.totalReceived)
                  ).toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Balance Distribution */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Balance Distribution</h4>
            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-gray-600">General Balance</span>
                  <span className="text-sm font-medium">
                    {((parseFloat(user.wallet.generalBalance) / parseFloat(user.totalBalance)) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{
                      width: `${(parseFloat(user.wallet.generalBalance) / parseFloat(user.totalBalance)) * 100}%`
                    }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-gray-600">Earning Balance</span>
                  <span className="text-sm font-medium">
                    {((parseFloat(user.wallet.earningBalance) / parseFloat(user.totalBalance)) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{
                      width: `${(parseFloat(user.wallet.earningBalance) / parseFloat(user.totalBalance)) * 100}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            variant="outline"
            className="flex items-center justify-center space-x-2"
            onClick={() => setShowTransactionsModal(true)}
          >
            <EyeIcon className="h-4 w-4" />
            <span>View Transactions</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center justify-center space-x-2 text-green-600 border-green-300 hover:bg-green-50"
            onClick={() => setShowAddFundsModal(true)}
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Funds</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center justify-center space-x-2"
            onClick={() => setShowSettingsModal(true)}
          >
            <Cog6ToothIcon className="h-4 w-4" />
            <span>Wallet Settings</span>
          </Button>
        </div>
      </Card>

      {/* Modals */}
      <ViewTransactionsModal
        isOpen={showTransactionsModal}
        onClose={() => setShowTransactionsModal(false)}
        userId={user.id}
        userName={user.name}
      />

      <AddFundsModal
        isOpen={showAddFundsModal}
        onClose={() => setShowAddFundsModal(false)}
        userId={user.id}
        userName={user.name}
        onSuccess={onUserUpdate}
      />

      <WalletSettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        userId={user.id}
        userName={user.name}
        onSuccess={onUserUpdate}
      />
    </div>
  );
}
