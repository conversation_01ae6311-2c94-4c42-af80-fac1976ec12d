"use client";

import { useState } from "react";
import { formatDistanceToNow, format, isToday, isYesterday } from "date-fns";
import { MessageTime } from "@/components/ui/TimeDisplay";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import {
  MagnifyingGlassIcon,
  CheckIcon,
  EllipsisVerticalIcon,
  ArchiveBoxIcon,
  TrashIcon
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

interface FanPageMessage {
  id: string;
  fanPageId: string;
  senderId: string;
  content: string;
  read: boolean;
  createdAt: string;
  sender: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface FanPageConversation {
  senderId: string;
  sender: {
    id: string;
    name: string | null;
    image: string | null;
  };
  lastMessage: FanPageMessage;
  unreadCount: number;
  messages: FanPageMessage[];
}

interface FanPageConversationListProps {
  conversations: FanPageConversation[];
  selectedConversation: string | null;
  onConversationSelect: (senderId: string) => void;
  onMarkAsRead?: (conversationId: string) => void;
  isLoading?: boolean;
  className?: string;
}

export function FanPageConversationList({
  conversations,
  selectedConversation,
  onConversationSelect,
  onMarkAsRead,
  isLoading = false,
  className
}: FanPageConversationListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedConversations, setSelectedConversations] = useState<Set<string>>(new Set());

  // Removed formatMessageTime function as we'll use MessageTime component

  const filteredConversations = conversations.filter(conversation =>
    conversation.sender.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.lastMessage.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleConversationSelection = (senderId: string) => {
    const newSelected = new Set(selectedConversations);
    if (newSelected.has(senderId)) {
      newSelected.delete(senderId);
    } else {
      newSelected.add(senderId);
    }
    setSelectedConversations(newSelected);
  };

  const selectAllConversations = () => {
    if (selectedConversations.size === filteredConversations.length) {
      setSelectedConversations(new Set());
    } else {
      setSelectedConversations(new Set(filteredConversations.map(c => c.senderId)));
    }
  };

  if (isLoading) {
    return (
      <div className={cn("flex flex-col h-full", className)}>
        <div className="p-4 border-b border-gray-200">
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="flex-1 p-4 space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-white", className)}>
      {/* Search and bulk actions */}
      <div className="p-4 border-b border-gray-200 space-y-3">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="search"
            placeholder="Search conversations..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Bulk actions */}
        {selectedConversations.size > 0 && (
          <div className="flex items-center justify-between bg-blue-50 p-2 rounded-lg">
            <span className="text-sm text-blue-700">
              {selectedConversations.size} selected
            </span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  selectedConversations.forEach(id => onMarkAsRead?.(id));
                  setSelectedConversations(new Set());
                }}
              >
                <CheckIcon className="h-4 w-4 mr-1" />
                Mark Read
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedConversations(new Set())}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Select all toggle */}
        {filteredConversations.length > 0 && (
          <div className="flex items-center justify-between">
            <Button
              size="sm"
              variant="ghost"
              onClick={selectAllConversations}
              className="text-xs"
            >
              {selectedConversations.size === filteredConversations.length ? "Deselect All" : "Select All"}
            </Button>
            <span className="text-xs text-gray-500">
              {filteredConversations.length} conversation{filteredConversations.length !== 1 ? 's' : ''}
            </span>
          </div>
        )}
      </div>

      {/* Conversations list */}
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="flex items-center justify-center h-full p-8">
            <div className="text-center">
              <div className="text-gray-500 mb-2">
                {searchQuery ? "No conversations found" : "No messages yet"}
              </div>
              {searchQuery && (
                <Button
                  onClick={() => setSearchQuery("")}
                  variant="outline"
                  size="sm"
                >
                  Clear search
                </Button>
              )}
            </div>
          </div>
        ) : (
          filteredConversations.map((conversation) => (
            <div
              key={conversation.senderId}
              className={cn(
                "group relative border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer transition-colors",
                selectedConversation === conversation.senderId ? "bg-blue-50 border-blue-200" : "",
                selectedConversations.has(conversation.senderId) && "bg-blue-100"
              )}
              onClick={() => onConversationSelect(conversation.senderId)}
            >
              {/* Selection checkbox */}
              <div className="absolute top-2 left-2">
                <input
                  type="checkbox"
                  checked={selectedConversations.has(conversation.senderId)}
                  onChange={(e) => {
                    e.stopPropagation();
                    toggleConversationSelection(conversation.senderId);
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>

              <div className="flex items-center space-x-3 ml-6">
                <div className="relative">
                  {conversation.sender.image ? (
                    <OptimizedImage
                      src={conversation.sender.image}
                      alt={conversation.sender.name || "User"}
                      width={48}
                      height={48}
                      className="h-12 w-12 flex-shrink-0 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-12 w-12 flex-shrink-0 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <span className="text-lg font-semibold text-white">
                        {(conversation.sender.name || "U").charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {conversation.sender.name || "Unknown User"}
                    </p>
                    <div className="flex items-center space-x-2">
                      {conversation.unreadCount > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {conversation.unreadCount}
                        </Badge>
                      )}
                      <MessageTime
                        date={conversation.lastMessage.createdAt}
                        className="text-xs text-gray-500"
                        autoUpdate={true}
                      />
                    </div>
                  </div>
                  <p className={cn(
                    "text-sm truncate",
                    conversation.unreadCount > 0 ? "text-gray-900 font-medium" : "text-gray-500"
                  )}>
                    {conversation.lastMessage.content}
                  </p>
                </div>

                {/* Action menu */}
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle action menu
                    }}
                  >
                    <EllipsisVerticalIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
