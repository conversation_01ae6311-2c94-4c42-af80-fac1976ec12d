import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { ReferralService } from "@/lib/referral/referralService";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get actual referral stats from database
    const stats = await ReferralService.getUserReferralStats(session.user.id, request);

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Error fetching referral stats:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch referral stats"
      },
      { status: 500 }
    );
  }
}
