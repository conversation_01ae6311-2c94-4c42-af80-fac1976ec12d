"use client";

import Link from "next/link";
import Image from "next/image";
import { UserIcon, ClockIcon } from "@heroicons/react/24/outline";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  author: {
    id: string;
    name: string;
    username: string;
    image: string | null;
  };
  category: {
    id: string;
    name: string;
    color: string;
  } | null;
  createdAt: string;
}

interface BlogCardProps {
  blog: Blog;
}

export function BlogCard({ blog }: BlogCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  return (
    <Link href={`/blogs/${blog.slug}`}>
      <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 p-4">
        {/* Title */}
        <h3 className="text-sm font-semibold text-gray-900 line-clamp-2 mb-2">
          {blog.title}
        </h3>

        {/* Excerpt */}
        {blog.excerpt && (
          <p className="text-xs text-gray-600 line-clamp-3 mb-3">
            {blog.excerpt}
          </p>
        )}

        {/* Author and Date */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {blog.author.image ? (
              <Image
                src={blog.author.image}
                alt={blog.author.name}
                width={24}
                height={24}
                className="w-6 h-6 rounded-full object-cover"
              />
            ) : (
              <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                <UserIcon className="w-3 h-3 text-gray-500" />
              </div>
            )}
            <span className="text-xs text-gray-600">{blog.author.name}</span>
          </div>

          <div className="flex items-center text-xs text-gray-500">
            <ClockIcon className="w-3 h-3 mr-1" />
            <span>{formatDate(blog.createdAt)}</span>
          </div>
        </div>

        {/* Category */}
        {blog.category && (
          <div className="mt-3">
            <span 
              className="inline-block text-xs px-2 py-1 rounded-full text-white"
              style={{ backgroundColor: blog.category.color }}
            >
              {blog.category.name}
            </span>
          </div>
        )}
      </div>
    </Link>
  );
}
