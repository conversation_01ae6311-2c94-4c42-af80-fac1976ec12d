/**
 * @jest-environment jsdom
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { useRealtimeMessaging } from '@/hooks/useRealtimeMessaging';

// Mock the PartyKit client
const mockClient = {
  connect: jest.fn().mockResolvedValue(true),
  disconnect: jest.fn(),
  send: jest.fn().mockReturnValue(true),
  on: jest.fn(),
  off: jest.fn(),
  isConnected: jest.fn().mockReturnValue(true),
  getStatus: jest.fn().mockReturnValue({
    connected: true,
    authenticated: true,
    latency: 50
  })
};

jest.mock('@/lib/partykit/client', () => ({
  PartyKitManager: {
    getClient: jest.fn().mockReturnValue(mockClient)
  }
}));

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn().mockReturnValue({
    data: {
      user: { id: 'test-user-id', name: 'Test User' }
    }
  })
}));

// Mock feature flags
jest.mock('@/lib/features/flags', () => ({
  useFeatureFlags: jest.fn().mockReturnValue({
    shouldUseRealtime: jest.fn().mockReturnValue(true),
    shouldUsePolling: jest.fn().mockReturnValue(false),
    shouldUseHybridMode: jest.fn().mockReturnValue(false)
  })
}));

describe('useRealtimeMessaging', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize with default state', () => {
    const { result } = renderHook(() => useRealtimeMessaging());

    expect(result.current.messages).toEqual([]);
    expect(result.current.typingUsers).toEqual([]);
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(null);
    expect(result.current.isConnected).toBe(false);
  });

  test('should connect to PartyKit on mount', async () => {
    renderHook(() => useRealtimeMessaging({
      conversationId: 'test-conversation'
    }));

    await waitFor(() => {
      expect(mockClient.connect).toHaveBeenCalled();
    });
  });

  test('should send messages via PartyKit', async () => {
    const { result } = renderHook(() => useRealtimeMessaging({
      conversationId: 'test-conversation'
    }));

    await act(async () => {
      const success = await result.current.sendMessage('Hello World', 'receiver-id');
      expect(success).toBe(true);
    });

    expect(mockClient.send).toHaveBeenCalledWith({
      type: 'send_message',
      message: expect.objectContaining({
        content: 'Hello World',
        receiverId: 'receiver-id',
        type: 'direct'
      })
    });
  });

  test('should handle new message events', async () => {
    let messageHandler: Function;
    mockClient.on.mockImplementation((event: string, handler: Function) => {
      if (event === 'new_message') {
        messageHandler = handler;
      }
    });

    const { result } = renderHook(() => useRealtimeMessaging());

    // Simulate receiving a message
    act(() => {
      messageHandler({
        message: {
          id: 'msg-1',
          senderId: 'sender-id',
          content: 'Hello from sender',
          timestamp: new Date().toISOString(),
          status: 'delivered'
        }
      });
    });

    await waitFor(() => {
      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0].content).toBe('Hello from sender');
    });
  });

  test('should handle typing indicators', async () => {
    let typingHandler: Function;
    mockClient.on.mockImplementation((event: string, handler: Function) => {
      if (event === 'user_typing') {
        typingHandler = handler;
      }
    });

    const { result } = renderHook(() => useRealtimeMessaging({
      enableTypingIndicators: true
    }));

    // Simulate typing event
    act(() => {
      typingHandler({
        userId: 'typing-user',
        conversationId: 'test-conversation',
        isTyping: true,
        timestamp: new Date().toISOString()
      });
    });

    await waitFor(() => {
      expect(result.current.typingUsers).toContain('typing-user');
    });

    // Simulate stop typing
    act(() => {
      typingHandler({
        userId: 'typing-user',
        conversationId: 'test-conversation',
        isTyping: false,
        timestamp: new Date().toISOString()
      });
    });

    await waitFor(() => {
      expect(result.current.typingUsers).not.toContain('typing-user');
    });
  });

  test('should send typing indicators', async () => {
    const { result } = renderHook(() => useRealtimeMessaging({
      conversationId: 'test-conversation',
      enableTypingIndicators: true
    }));

    act(() => {
      result.current.sendTypingIndicator(true);
    });

    expect(mockClient.send).toHaveBeenCalledWith({
      type: 'typing',
      conversationId: 'test-conversation',
      isTyping: true
    });
  });

  test('should handle message delivery status updates', async () => {
    let deliveryHandler: Function;
    mockClient.on.mockImplementation((event: string, handler: Function) => {
      if (event === 'message_delivered') {
        deliveryHandler = handler;
      }
    });

    const { result } = renderHook(() => useRealtimeMessaging());

    // Add a message first
    act(() => {
      result.current.messages.push({
        id: 'msg-1',
        senderId: 'test-user-id',
        content: 'Test message',
        timestamp: new Date().toISOString(),
        status: 'sent',
        type: 'direct'
      });
    });

    // Simulate delivery confirmation
    act(() => {
      deliveryHandler({
        messageId: 'msg-1',
        timestamp: new Date().toISOString()
      });
    });

    await waitFor(() => {
      const message = result.current.messages.find(m => m.id === 'msg-1');
      expect(message?.status).toBe('delivered');
    });
  });

  test('should mark messages as read', async () => {
    const { result } = renderHook(() => useRealtimeMessaging({
      conversationId: 'test-conversation'
    }));

    act(() => {
      result.current.markAsRead('msg-1');
    });

    expect(mockClient.send).toHaveBeenCalledWith({
      type: 'mark_read',
      messageId: 'msg-1',
      conversationId: 'test-conversation'
    });
  });

  test('should handle optimistic message updates', async () => {
    const { result } = renderHook(() => useRealtimeMessaging());

    await act(async () => {
      await result.current.sendMessage('Optimistic message');
    });

    // Should immediately add optimistic message
    expect(result.current.messages).toHaveLength(1);
    expect(result.current.messages[0].optimistic).toBe(true);
    expect(result.current.messages[0].status).toBe('sending');
  });

  test('should fallback to API when PartyKit is unavailable', async () => {
    // Mock PartyKit as disconnected
    mockClient.isConnected.mockReturnValue(false);
    
    // Mock fetch for API fallback
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({
        data: {
          id: 'api-msg-1',
          content: 'API message',
          senderId: 'test-user-id',
          timestamp: new Date().toISOString()
        }
      })
    });

    const { result } = renderHook(() => useRealtimeMessaging());

    await act(async () => {
      const success = await result.current.sendMessage('Fallback message');
      expect(success).toBe(true);
    });

    expect(global.fetch).toHaveBeenCalledWith('/api/messages', expect.objectContaining({
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: 'Fallback message',
        receiverId: undefined,
        fanPageId: undefined
      })
    }));
  });

  test('should handle connection errors gracefully', async () => {
    let errorHandler: Function;
    mockClient.on.mockImplementation((event: string, handler: Function) => {
      if (event === 'error') {
        errorHandler = handler;
      }
    });

    const { result } = renderHook(() => useRealtimeMessaging());

    // Simulate connection error
    act(() => {
      errorHandler({ message: 'Connection failed' });
    });

    await waitFor(() => {
      expect(result.current.error).toBe('Connection failed');
    });
  });

  test('should cleanup on unmount', () => {
    const { unmount } = renderHook(() => useRealtimeMessaging({
      conversationId: 'test-conversation'
    }));

    unmount();

    expect(mockClient.send).toHaveBeenCalledWith({
      type: 'leave_conversation',
      conversationId: 'test-conversation'
    });
    expect(mockClient.disconnect).toHaveBeenCalled();
  });

  test('should handle fan page messaging', async () => {
    const { result } = renderHook(() => useRealtimeMessaging());

    await act(async () => {
      await result.current.sendMessage('Fan page message', undefined, 'fanpage-123');
    });

    expect(mockClient.send).toHaveBeenCalledWith({
      type: 'send_message',
      message: expect.objectContaining({
        content: 'Fan page message',
        type: 'fanpage',
        fanPageId: 'fanpage-123'
      })
    });
  });

  test('should auto-clear typing indicators after timeout', async () => {
    jest.useFakeTimers();
    
    let typingHandler: Function;
    mockClient.on.mockImplementation((event: string, handler: Function) => {
      if (event === 'user_typing') {
        typingHandler = handler;
      }
    });

    const { result } = renderHook(() => useRealtimeMessaging({
      enableTypingIndicators: true
    }));

    // Start typing
    act(() => {
      typingHandler({
        userId: 'typing-user',
        conversationId: 'test-conversation',
        isTyping: true,
        timestamp: new Date().toISOString()
      });
    });

    expect(result.current.typingUsers).toContain('typing-user');

    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    await waitFor(() => {
      expect(result.current.typingUsers).not.toContain('typing-user');
    });

    jest.useRealTimers();
  });
});
