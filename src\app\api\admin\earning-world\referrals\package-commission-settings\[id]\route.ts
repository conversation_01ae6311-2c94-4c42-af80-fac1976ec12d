import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { packageCommissionSettings } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Get specific package commission settings
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const settings = await db.query.packageCommissionSettings.findFirst({
      where: eq(packageCommissionSettings.id, params.id),
    });

    if (!settings) {
      return NextResponse.json(
        { message: "Package commission settings not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: settings,
    });

  } catch (error) {
    console.error("Error fetching package commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch package commission settings"
      },
      { status: 500 }
    );
  }
}

// Update specific package commission settings
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      isEnabled,
      commissionType,
      firstPurchaseCommission,
      recurringCommission,
      maxCommissionAmount
    } = body;

    // Check if settings exist
    const existingSettings = await db.query.packageCommissionSettings.findFirst({
      where: eq(packageCommissionSettings.id, params.id),
    });

    if (!existingSettings) {
      return NextResponse.json(
        { message: "Package commission settings not found" },
        { status: 404 }
      );
    }

    // Prepare update data (only include provided fields)
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (typeof isEnabled === 'boolean') {
      updateData.isEnabled = isEnabled;
    }

    if (commissionType && ['fixed', 'percentage'].includes(commissionType)) {
      updateData.commissionType = commissionType;
    }

    if (firstPurchaseCommission !== undefined) {
      const value = parseFloat(firstPurchaseCommission);
      if (isNaN(value) || value < 0) {
        return NextResponse.json(
          { message: "Invalid first purchase commission value" },
          { status: 400 }
        );
      }
      updateData.firstPurchaseCommission = firstPurchaseCommission;
    }

    if (recurringCommission !== undefined) {
      const value = parseFloat(recurringCommission);
      if (isNaN(value) || value < 0) {
        return NextResponse.json(
          { message: "Invalid recurring commission value" },
          { status: 400 }
        );
      }
      updateData.recurringCommission = recurringCommission;
    }

    if (maxCommissionAmount !== undefined) {
      if (maxCommissionAmount && (isNaN(parseFloat(maxCommissionAmount)) || parseFloat(maxCommissionAmount) <= 0)) {
        return NextResponse.json(
          { message: "Invalid max commission amount" },
          { status: 400 }
        );
      }
      updateData.maxCommissionAmount = maxCommissionAmount || null;
    }

    // Update settings
    await db
      .update(packageCommissionSettings)
      .set(updateData)
      .where(eq(packageCommissionSettings.id, params.id));

    return NextResponse.json({
      success: true,
      message: "Package commission settings updated successfully",
    });

  } catch (error) {
    console.error("Error updating package commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update package commission settings"
      },
      { status: 500 }
    );
  }
}

// Delete package commission settings
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const settingsId = params.id;

    // Check if settings exist
    const existingSettings = await db.query.packageCommissionSettings.findFirst({
      where: eq(packageCommissionSettings.id, settingsId),
    });

    if (!existingSettings) {
      return NextResponse.json(
        { message: "Package commission settings not found" },
        { status: 404 }
      );
    }

    // Delete the settings
    await db
      .delete(packageCommissionSettings)
      .where(eq(packageCommissionSettings.id, settingsId));

    return NextResponse.json({
      success: true,
      message: "Package commission settings deleted successfully",
    });

  } catch (error) {
    console.error("Error deleting package commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete package commission settings"
      },
      { status: 500 }
    );
  }
}
