"use client";

import { useState, useEffect, Fragment } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Menu, Transition } from "@headlessui/react";
import {
  UserGroupIcon,
  UserPlusIcon,
  ChevronDownIcon,
  UserIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  SparklesIcon
} from "@heroicons/react/24/outline";
import { NotificationBadge } from "@/components/notifications/NotificationBadge";
import { Spinner } from "@/components/ui/Spinner";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { cn } from "@/lib/utils";
import { toast } from "react-hot-toast";
import { formatDistanceToNow } from "date-fns";


interface Following {
  id: string;
  targetUserId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
}

interface Follower {
  id: string;
  subscriberId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
}

export function ConnectionDropdown() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [following, setFollowing] = useState<Following[]>([]);
  const [followers, setFollowers] = useState<Follower[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'subscribed' | 'subscribers'>('subscribed');


  useEffect(() => {
    if (session?.user) {
      fetchData();
    }
  }, [session]);

  const fetchData = async () => {
    if (!session?.user) return;

    try {
      setLoading(true);

      // Fetch following
      const followingResponse = await fetch('/api/subscriptions?type=subscriptions&limit=5');
      if (followingResponse.ok) {
        const followingData = await followingResponse.json();
        setFollowing(followingData.subscriptions || []);
      }

      // Fetch followers
      const followersResponse = await fetch('/api/subscriptions?type=subscribers&limit=5');
      if (followersResponse.ok) {
        const followersData = await followersResponse.json();
        setFollowers(followersData.subscribers || []);
      }
    } catch (error) {
      console.error('Error fetching connection data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const isActive = pathname?.startsWith('/connection') || false;

  return (
    <Menu as="div" className="relative">
      {({ open }) => (
        <>
      <Menu.Button
        className={cn(
          "flex items-center space-x-1 px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 group active:scale-95 hover:shadow-md",
          isActive
            ? "bg-gradient-to-r from-blue-50 to-blue-100 text-blue-600 shadow-sm"
            : "text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-blue-600"
        )}
      >
        <UserGroupIcon className="h-5 w-5 transition-transform duration-200 group-hover:scale-110" />
        <span className="hidden sm:inline">Connections</span>
        <ChevronDownIcon className="h-4 w-4 transition-all duration-200 group-hover:rotate-180" />
        {followers.length > 0 && (
          <NotificationBadge
            count={followers.length}
            animate={true}
            size="sm"
            color="blue"
            className="-top-1 -right-1"
          />
        )}
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-300"
        enterFrom="transform opacity-0 scale-95 translate-y-2"
        enterTo="transform opacity-100 scale-100 translate-y-0"
        leave="transition ease-in duration-200"
        leaveFrom="transform opacity-100 scale-100 translate-y-0"
        leaveTo="transform opacity-0 scale-95 translate-y-2"
      >
        <Menu.Items className="absolute right-0 z-50 mt-3 w-96 origin-top-right rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden border border-gray-100 backdrop-blur-sm">
          {/* Enhanced Header with Gradient */}
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-white/20 rounded-full p-2">
                  <UserGroupIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">Connections</h3>
                  <p className="text-blue-100 text-sm">
                    {followers.length > 0 ? `${followers.length} new subscribers` : 'Manage your connections'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
                  title="Refresh"
                >
                  <ArrowPathIcon className={cn(
                    "h-5 w-5 text-white transition-transform",
                    refreshing && "animate-spin"
                  )} />
                </button>

              </div>
            </div>
          </div>

          {/* Enhanced Filter Tabs */}
          <div className="px-4 py-2 bg-white border-b border-gray-100">
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('subscribed')}
                className={cn(
                  "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                  activeTab === 'subscribed'
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                Subscribed ({following.length})
              </button>
              <button
                onClick={() => setActiveTab('subscribers')}
                className={cn(
                  "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                  activeTab === 'subscribers'
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                Subscribers ({followers.length})
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Spinner size="sm" />
                <span className="ml-2 text-sm text-gray-500">Loading...</span>
              </div>
            ) : (
              <div className="p-4">
                {activeTab === 'subscribed' ? (
                  following.length === 0 ? (
                    <div className="text-center py-8">
                      <UserPlusIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 mb-3">Not subscribed to anyone yet</p>
                      <Link
                        href="/connection?tab=suggestions"
                        className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        <SparklesIcon className="h-3 w-3 mr-1" />
                        Discover People
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {following.slice(0, 4).map((follow) => (
                        <Link
                          key={follow.id}
                          href={`/user/${follow.username}`}
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                          <div className="relative h-10 w-10 flex-shrink-0">
                            {follow.image ? (
                              <OptimizedImage
                                src={follow.image}
                                alt={follow.name}
                                width={40}
                                height={40}
                                className="rounded-full object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
                                <UserIcon className="h-5 w-5 text-gray-500" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {follow.name}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              @{follow.username}
                            </p>
                          </div>
                        </Link>
                      ))}
                      {following.length > 4 && (
                        <Link
                          href="/connection?tab=subscribed"
                          className="block text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                          View all {following.length} subscribed
                        </Link>
                      )}
                    </div>
                  )
                ) : (
                  followers.length === 0 ? (
                    <div className="text-center py-8">
                      <UserGroupIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 mb-3">No subscribers yet</p>
                      <Link
                        href="/connection?tab=suggestions"
                        className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        <SparklesIcon className="h-3 w-3 mr-1" />
                        Discover People
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {followers.slice(0, 4).map((follower) => (
                        <Link
                          key={follower.id}
                          href={`/user/${follower.username}`}
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                          <div className="relative h-10 w-10 flex-shrink-0">
                            {follower.image ? (
                              <OptimizedImage
                                src={follower.image}
                                alt={follower.name}
                                width={40}
                                height={40}
                                className="rounded-full object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full flex items-center justify-center">
                                <UserIcon className="h-5 w-5 text-gray-500" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {follower.name}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              @{follower.username}
                            </p>
                          </div>
                        </Link>
                      ))}
                      {followers.length > 4 && (
                        <Link
                          href="/connection?tab=subscribers"
                          className="block text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                          View all {followers.length} subscribers
                        </Link>
                      )}
                    </div>
                  )
                )}
              </div>
            )}
          </div>

          {/* Enhanced Footer */}
          <div className="border-t border-gray-100 bg-gray-50 px-4 py-3">
            <div className="flex items-center justify-between">
              <Link
                href="/connection"
                className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors"
              >
                View All Connections
              </Link>
              <Link
                href="/connection?tab=suggestions"
                className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
              >
                <MagnifyingGlassIcon className="h-3 w-3 mr-1" />
                Find People
              </Link>
            </div>
          </div>
        </Menu.Items>
      </Transition>
        </>
      )}
    </Menu>
  );
}
