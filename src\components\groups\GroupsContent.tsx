"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { UsersIcon } from "@heroicons/react/24/outline";
import { GroupCard } from "@/components/groups/GroupCard";
import { CreateGroupButton } from "@/components/groups/CreateGroupButton";

interface Group {
  id: string;
  name: string;
  description: string | null;
  visibility: "public" | "private-visible" | "private-hidden";
  coverImage: string | null;
  category: string | null;
  creatorId: string;
  postPermission: "all-members" | "admin-only";
  createdAt: Date;
}

interface GroupWithMeta {
  group: Group;
  memberCount: number;
  userRole: { role: string } | null;
}

interface GroupsContentProps {
  userId: string;
}

export function GroupsContent({ userId }: GroupsContentProps) {
  const searchParams = useSearchParams();
  const filter = searchParams?.get("filter") || "all";
  const search = searchParams?.get("search") || "";
  const category = searchParams?.get("category") || "";

  const [userGroups, setUserGroups] = useState<GroupWithMeta[]>([]);
  const [createdGroups, setCreatedGroups] = useState<GroupWithMeta[]>([]);
  const [suggestedGroups, setSuggestedGroups] = useState<GroupWithMeta[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch user's groups
        const userGroupsResponse = await fetch(`/api/groups?filter=joined&search=${search}&category=${category}`);
        if (!userGroupsResponse.ok) {
          throw new Error("Failed to fetch user groups");
        }
        const userGroupsData = await userGroupsResponse.json();
        setUserGroups(userGroupsData);

        // Fetch created groups
        const createdGroupsResponse = await fetch(`/api/groups?filter=created&search=${search}&category=${category}`);
        if (!createdGroupsResponse.ok) {
          throw new Error("Failed to fetch created groups");
        }
        const createdGroupsData = await createdGroupsResponse.json();
        setCreatedGroups(createdGroupsData);

        // Fetch suggested groups
        const suggestedGroupsResponse = await fetch(`/api/groups?filter=discover&search=${search}&category=${category}`);
        if (!suggestedGroupsResponse.ok) {
          throw new Error("Failed to fetch suggested groups");
        }
        const suggestedGroupsData = await suggestedGroupsResponse.json();
        setSuggestedGroups(suggestedGroupsData);
      } catch (error) {
        console.error("Error fetching groups:", error);
        setError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    fetchGroups();
  }, [filter, search, category]);

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Groups
        </h1>
        <CreateGroupButton />
      </div>

      <div className="mb-8 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex-1 sm:max-w-xs">
          <form action="/groups" method="get">
            <Input
              type="search"
              name="search"
              placeholder="Search groups..."
              className="w-full"
              defaultValue={search}
            />
          </form>
        </div>
        <div className="flex space-x-2 overflow-x-auto pb-2">
          <a href="/groups?filter=all">
            <Button variant={filter === 'all' ? 'primary' : 'outline'}>All Groups</Button>
          </a>
          <a href="/groups?filter=joined">
            <Button variant={filter === 'joined' ? 'primary' : 'outline'}>Your Groups</Button>
          </a>
          <a href="/groups?filter=created">
            <Button variant={filter === 'created' ? 'primary' : 'outline'}>Created by You</Button>
          </a>
          <a href="/groups?filter=discover">
            <Button variant={filter === 'discover' ? 'primary' : 'outline'}>Discover</Button>
          </a>
        </div>
      </div>

      {error && (
        <div className="mb-6 rounded-md bg-red-50 p-4 text-sm text-red-600">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
          <span className="ml-2 text-gray-500">Loading groups...</span>
        </div>
      ) : (
        <>
          {/* Display groups based on filter */}
          {(filter === 'all' || filter === 'joined') && (
            <div className="mb-8">
              <h2 className="mb-4 text-xl font-semibold text-gray-900">
                Your Groups
              </h2>
              {userGroups.length > 0 ? (
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {userGroups.map((item) => (
                    <GroupCard
                      key={item.group.id}
                      id={item.group.id}
                      name={item.group.name}
                      description={item.group.description}
                      visibility={item.group.visibility}
                      coverImage={item.group.coverImage}
                      category={item.group.category}
                      memberCount={Number(item.memberCount)}
                      createdAt={new Date(item.group.createdAt)}
                      userRole={item.userRole?.role || null}
                    />
                  ))}
                </div>
              ) : (
                <div className="rounded-lg border border-gray-200 bg-white p-8 text-center shadow">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <UsersIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <p className="mt-4 text-sm text-gray-500">
                    You haven't joined any groups yet.
                  </p>
                  <p className="mt-2 text-sm text-gray-500">
                    Join groups to connect with people who share your interests.
                  </p>
                  <div className="mt-4">
                    <a href="/groups?filter=discover">
                      <Button variant="outline" size="sm">
                        Discover Groups
                      </Button>
                    </a>
                  </div>
                </div>
              )}
            </div>
          )}

          {(filter === 'all' || filter === 'created') && (
            <div className="mb-8">
              <h2 className="mb-4 text-xl font-semibold text-gray-900">
                Groups You Created
              </h2>
              {createdGroups.length > 0 ? (
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {createdGroups.map((item) => (
                    <GroupCard
                      key={item.group.id}
                      id={item.group.id}
                      name={item.group.name}
                      description={item.group.description}
                      visibility={item.group.visibility}
                      coverImage={item.group.coverImage}
                      category={item.group.category}
                      memberCount={Number(item.memberCount)}
                      createdAt={new Date(item.group.createdAt)}
                      userRole={item.userRole?.role || null}
                    />
                  ))}
                </div>
              ) : (
                <div className="rounded-lg border border-gray-200 bg-white p-8 text-center shadow">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <UsersIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <p className="mt-4 text-sm text-gray-500">
                    You haven't created any groups yet.
                  </p>
                  <p className="mt-2 text-sm text-gray-500">
                    Create a group to connect with people who share your interests.
                  </p>
                  <div className="mt-4">
                    <CreateGroupButton />
                  </div>
                </div>
              )}
            </div>
          )}

          {(filter === 'all' || filter === 'discover') && (
            <div className="mb-8">
              <h2 className="mb-4 text-xl font-semibold text-gray-900">
                Suggested Groups
              </h2>
              {suggestedGroups.length > 0 ? (
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {suggestedGroups.map((item) => (
                    <GroupCard
                      key={item.group.id}
                      id={item.group.id}
                      name={item.group.name}
                      description={item.group.description}
                      visibility={item.group.visibility}
                      coverImage={item.group.coverImage}
                      category={item.group.category}
                      memberCount={Number(item.memberCount)}
                      createdAt={new Date(item.group.createdAt)}
                      userRole={item.userRole?.role || null}
                    />
                  ))}
                </div>
              ) : (
                <div className="rounded-lg border border-gray-200 bg-white p-8 text-center shadow">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <UsersIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <p className="mt-4 text-sm text-gray-500">
                    No suggested groups found.
                  </p>
                  <p className="mt-2 text-sm text-gray-500">
                    Try creating your own group!
                  </p>
                  <div className="mt-4">
                    <CreateGroupButton />
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
}
