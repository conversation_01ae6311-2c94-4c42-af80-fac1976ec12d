"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/Tabs";
import { toast } from "react-hot-toast";
import { EditStoreModal } from "@/components/admin/stores/EditStoreModal";
import { StoreAnalytics } from "@/components/admin/stores/StoreAnalytics";
import { StoreSettings } from "@/components/admin/stores/StoreSettings";
import { StoreReviews } from "@/components/admin/stores/StoreReviews";
import { StoreImportExport } from "@/components/admin/stores/StoreImportExport";
import Image from "next/image";
import Link from "next/link";
import {
  <PERSON><PERSON><PERSON>tIcon,
  PencilIcon,
  TrashIcon,
  BuildingStorefrontIcon,
  UserIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  ShoppingBagIcon,
  StarIcon,
  HeartIcon,
  CalendarIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";
import { formatTimeAgo, formatCurrency } from "@/lib/utils";

interface Store {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  logo: string | null;
  banner: string | null;
  location: string | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  owner: {
    id: string;
    name: string | null;
    image: string | null;
  };
  productCount: number;
  reviewCount: number;
}

interface Product {
  id: string;
  title: string;
  description: string | null;
  price: number;
  condition: "new" | "like_new" | "good" | "fair" | "poor";
  category: string;
  location: string | null;
  photos: string[] | null;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function AdminStoreDetailPage() {
  const params = useParams();
  const router = useRouter();
  const storeId = params?.storeId as string;

  const [isLoading, setIsLoading] = useState(true);
  const [store, setStore] = useState<Store | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    fetchStoreDetails();
  }, [storeId]);

  useEffect(() => {
    if (activeTab === "products" && store) {
      fetchStoreProducts();
    }
  }, [activeTab, store]);

  const fetchStoreDetails = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/stores/${storeId}`);

      if (!response.ok) {
        if (response.status === 404) {
          toast.error("Store not found");
          router.push("/admin/stores");
          return;
        }
        throw new Error(`Failed to fetch store details: ${response.status}`);
      }

      const data = await response.json();
      setStore(data);
    } catch (error) {
      console.error("Error fetching store details:", error);
      toast.error("Failed to load store details");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStoreProducts = async () => {
    if (!store) return;

    setIsLoadingProducts(true);
    try {
      const response = await fetch(`/api/marketplace/products?storeId=${store.id}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch store products: ${response.status}`);
      }

      const data = await response.json();
      // API returns { products: [...], pagination: {...} }
      if (data && data.products && Array.isArray(data.products)) {
        setProducts(data.products);
      } else if (Array.isArray(data)) {
        // Fallback in case API returns array directly
        setProducts(data);
      } else {
        setProducts([]);
      }
    } catch (error) {
      console.error("Error fetching store products:", error);
      toast.error("Failed to load store products");
      setProducts([]);
    } finally {
      setIsLoadingProducts(false);
    }
  };

  const handleStoreUpdated = () => {
    fetchStoreDetails();
  };

  const handleDeleteStore = async () => {
    if (!store) return;

    if (!confirm(`Are you sure you want to delete ${store.name}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/stores/${store.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete store");
      }

      toast.success("Store deleted successfully");
      router.push("/admin/stores");
    } catch (error) {
      console.error("Error deleting store:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete store");
    }
  };

  const handleVerifyStore = async () => {
    if (!store) return;

    try {
      const response = await fetch(`/api/admin/stores/${store.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isVerified: !store.isVerified }),
      });

      if (!response.ok) {
        throw new Error("Failed to update store verification status");
      }

      const data = await response.json();
      setStore(prev => prev ? { ...prev, isVerified: !prev.isVerified } : null);
      toast.success(`Store ${store.isVerified ? "unverified" : "verified"} successfully`);
    } catch (error) {
      console.error("Error updating store verification:", error);
      toast.error("Failed to update store verification status");
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm("Are you sure you want to delete this product?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/marketplace/products/${productId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete product");
      }

      setProducts(products.filter(product => product.id !== productId));
      toast.success("Product deleted successfully");
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    }
  };

  const renderStarRating = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 fill-current text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 fill-current text-yellow-400 opacity-50" />
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }

    return (
      <div className="flex items-center">
        {stars}
        <span className="ml-1 text-xs text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "new":
        return (
          <Badge variant="success">New</Badge>
        );
      case "like_new":
        return (
          <Badge variant="success">Like New</Badge>
        );
      case "good":
        return (
          <Badge variant="warning">Good</Badge>
        );
      case "fair":
        return (
          <Badge variant="warning">Fair</Badge>
        );
      case "poor":
        return (
          <Badge variant="danger">Poor</Badge>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!store) {
    return (
      <AdminLayout>
        <div className="flex h-64 flex-col items-center justify-center">
          <BuildingStorefrontIcon className="mb-4 h-16 w-16 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-700">Store not found</h2>
          <p className="mt-2 text-gray-500">The store you're looking for doesn't exist or has been deleted.</p>
          <Button className="mt-4" onClick={() => router.push("/admin/stores")}>
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Stores
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {/* Header */}
      <div className="mb-6 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex items-center">
          <Button
            variant="outline"
            className="mr-4"
            onClick={() => router.push("/admin/stores")}
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              {store.name}
              {store.isVerified && (
                <ShieldCheckIcon className="ml-2 h-5 w-5 text-blue-500" />
              )}
            </h1>
            <p className="mt-1 text-sm text-gray-500">@{store.slug}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={store.isVerified ? "outline" : "primary"}
            onClick={handleVerifyStore}
          >
            <ShieldCheckIcon className="mr-2 h-5 w-5" />
            {store.isVerified ? "Unverify Store" : "Verify Store"}
          </Button>
          <Button variant="outline" onClick={() => setIsEditModalOpen(true)}>
            <PencilIcon className="mr-2 h-5 w-5" />
            Edit
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteStore}
            disabled={store.productCount > 0}
            title={store.productCount > 0 ? "Cannot delete store with products" : "Delete store"}
          >
            <TrashIcon className="mr-2 h-5 w-5" />
            Delete
          </Button>
        </div>
      </div>

      {/* Store Banner and Logo */}
      <div className="relative mb-6 h-48 w-full overflow-hidden rounded-lg bg-gray-200">
        {store.banner ? (
          <Image
            src={store.banner}
            alt={`${store.name} banner`}
            fill
            className="object-cover"
          />
        ) : (
          <div className="flex h-full items-center justify-center bg-gradient-to-r from-blue-100 to-indigo-100">
            <BuildingStorefrontIcon className="h-16 w-16 text-gray-400" />
          </div>
        )}
        <div className="absolute -bottom-12 left-6">
          <div className="h-24 w-24 overflow-hidden rounded-full border-4 border-white bg-white">
            {store.logo ? (
              <Image
                src={store.logo}
                alt={`${store.name} logo`}
                width={96}
                height={96}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-gray-200">
                <BuildingStorefrontIcon className="h-12 w-12 text-gray-400" />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="mt-12">
        <Tabs defaultValue="overview" onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="import-export">Import/Export</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Store Info */}
              <div className="rounded-lg border border-gray-200 bg-white p-6">
                <h2 className="mb-4 text-lg font-semibold text-gray-900">Store Information</h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Description</h3>
                    <p className="mt-1 text-gray-900">{store.description || "No description provided"}</p>
                  </div>
                  {store.location && (
                    <div className="flex items-start">
                      <MapPinIcon className="mr-2 h-5 w-5 text-gray-400" />
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Location</h3>
                        <p className="text-gray-900">{store.location}</p>
                      </div>
                    </div>
                  )}
                  {store.phone && (
                    <div className="flex items-start">
                      <PhoneIcon className="mr-2 h-5 w-5 text-gray-400" />
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                        <p className="text-gray-900">{store.phone}</p>
                      </div>
                    </div>
                  )}
                  {store.email && (
                    <div className="flex items-start">
                      <EnvelopeIcon className="mr-2 h-5 w-5 text-gray-400" />
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Email</h3>
                        <p className="text-gray-900">{store.email}</p>
                      </div>
                    </div>
                  )}
                  {store.website && (
                    <div className="flex items-start">
                      <GlobeAltIcon className="mr-2 h-5 w-5 text-gray-400" />
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Website</h3>
                        <a
                          href={store.website.startsWith("http") ? store.website : `https://${store.website}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {store.website}
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Owner Info */}
              <div className="rounded-lg border border-gray-200 bg-white p-6">
                <h2 className="mb-4 text-lg font-semibold text-gray-900">Owner Information</h2>
                <div className="flex items-center">
                  <div className="h-12 w-12 flex-shrink-0">
                    {store.owner.image ? (
                      <Image
                        src={store.owner.image}
                        alt={store.owner.name || "Store owner"}
                        width={48}
                        height={48}
                        className="h-12 w-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200">
                        <UserIcon className="h-6 w-6 text-gray-500" />
                      </div>
                    )}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">{store.owner.name || "Unnamed User"}</h3>
                    <Link href={`/admin/users/${store.owner.id}`} className="text-blue-600 hover:underline">
                      View Profile
                    </Link>
                  </div>
                </div>
              </div>

              {/* Store Stats */}
              <div className="rounded-lg border border-gray-200 bg-white p-6">
                <h2 className="mb-4 text-lg font-semibold text-gray-900">Store Statistics</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div className="rounded-lg bg-blue-50 p-4">
                    <div className="flex items-center">
                      <ShoppingBagIcon className="mr-2 h-6 w-6 text-blue-500" />
                      <span className="text-lg font-semibold text-gray-900">{store.productCount}</span>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">Products</p>
                  </div>
                  <div className="rounded-lg bg-yellow-50 p-4">
                    <div className="flex items-center">
                      <StarIcon className="mr-2 h-6 w-6 text-yellow-500" />
                      <span className="text-lg font-semibold text-gray-900">{store.reviewCount}</span>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">Reviews</p>
                  </div>
                </div>
              </div>

              {/* Store Dates */}
              <div className="rounded-lg border border-gray-200 bg-white p-6">
                <h2 className="mb-4 text-lg font-semibold text-gray-900">Store Timeline</h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CalendarIcon className="mr-2 h-5 w-5 text-gray-400" />
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Created</h3>
                      <p className="text-gray-900">
                        {new Date(store.createdAt).toLocaleDateString()} ({formatTimeAgo(store.createdAt)})
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CalendarIcon className="mr-2 h-5 w-5 text-gray-400" />
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                      <p className="text-gray-900">
                        {new Date(store.updatedAt).toLocaleDateString()} ({formatTimeAgo(store.updatedAt)})
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Products Tab */}
          <TabsContent value="products">
            <div className="rounded-lg border border-gray-200 bg-white">
              <div className="flex items-center justify-between border-b border-gray-200 p-4">
                <h2 className="text-lg font-semibold text-gray-900">Store Products</h2>
                <Link href={`/admin/marketplace`}>
                  <Button variant="outline" size="sm">
                    Manage All Products
                  </Button>
                </Link>
              </div>

              {isLoadingProducts ? (
                <div className="flex h-64 items-center justify-center">
                  <Spinner size="lg" />
                </div>
              ) : products.length === 0 ? (
                <div className="flex h-64 flex-col items-center justify-center p-4 text-center">
                  <ShoppingBagIcon className="mb-2 h-12 w-12 text-gray-400" />
                  <h3 className="mb-1 text-lg font-medium text-gray-900">No products found</h3>
                  <p className="text-gray-500">This store doesn't have any products yet.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Product
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Price
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Condition
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Category
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Views
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Listed
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {products.map((product) => (
                        <tr key={product.id} className="hover:bg-gray-50">
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="flex items-center">
                              <div className="h-10 w-10 flex-shrink-0">
                                {product.photos && product.photos.length > 0 ? (
                                  <Image
                                    src={product.photos[0]}
                                    alt={product.title}
                                    width={40}
                                    height={40}
                                    className="h-10 w-10 rounded-md object-cover"
                                  />
                                ) : (
                                  <div className="flex h-10 w-10 items-center justify-center rounded-md bg-gray-100 text-gray-500">
                                    <ShoppingBagIcon className="h-6 w-6" />
                                  </div>
                                )}
                              </div>
                              <div className="ml-4">
                                <div className="font-medium text-gray-900">
                                  {product.title}
                                </div>
                                <div className="text-xs text-gray-500">
                                  ID: {product.id}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm">
                            <div className="font-medium text-gray-900">
                              {formatCurrency(product.price)}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm">
                            {getConditionBadge(product.condition)}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {product.category}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {product.viewCount}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <div>{new Date(product.createdAt).toLocaleDateString()}</div>
                            <div className="text-xs">{formatTimeAgo(product.createdAt)}</div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <Link
                                href={`/marketplace/product/${product.id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <Button
                                  variant="outline"
                                  size="sm"
                                >
                                  View
                                </Button>
                              </Link>
                              <Link href={`/admin/marketplace/products/${product.id}`}>
                                <Button
                                  variant="outline"
                                  size="sm"
                                >
                                  Edit
                                </Button>
                              </Link>
                              <Button
                                onClick={() => handleDeleteProduct(product.id)}
                                variant="danger"
                                size="sm"
                              >
                                Delete
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <StoreAnalytics storeId={storeId} />
          </TabsContent>

          {/* Reviews Tab */}
          <TabsContent value="reviews">
            <StoreReviews storeId={storeId} />
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <StoreSettings storeId={storeId} />
          </TabsContent>

          {/* Import/Export Tab */}
          <TabsContent value="import-export">
            <StoreImportExport />
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit Store Modal */}
      <EditStoreModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        store={store}
        onStoreUpdated={handleStoreUpdated}
      />
    </AdminLayout>
  );
}
