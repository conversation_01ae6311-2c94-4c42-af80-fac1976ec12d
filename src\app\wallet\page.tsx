import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { WalletDashboard } from "@/components/wallet/WalletDashboard";

export const dynamic = 'force-dynamic';

export default async function WalletPage() {
  const user = await requireAuth();

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
        <div className="mb-4 sm:mb-6 lg:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Wallet</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1 sm:mt-2">
            Manage your funds, send money, and track transactions
          </p>
        </div>

        <WalletDashboard userId={user.id} />
      </div>
    </MainLayout>
  );
}
