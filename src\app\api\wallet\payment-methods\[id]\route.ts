import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

// Delete payment method
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    console.log('DELETE payment method API called with ID:', resolvedParams.id);

    const session = await getServerSession(authOptions);

    if (!session?.user) {
      console.log('Unauthorized access attempt');
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = resolvedParams;
    console.log('User ID:', session.user.id, 'Payment Method ID:', id);

    // Check if payment method belongs to user
    const paymentMethod = await WalletService.getPaymentMethodById(id);
    console.log('Found payment method:', paymentMethod);

    if (!paymentMethod || paymentMethod.userId !== session.user.id) {
      console.log('Payment method not found or unauthorized');
      return NextResponse.json(
        {
          success: false,
          message: "Payment method not found"
        },
        { status: 404 }
      );
    }

    // Allow deletion but warn if it's the only method of its type
    if (paymentMethod.isDefault) {
      const userMethods = await WalletService.getUserPaymentMethods(session.user.id, paymentMethod.methodType);
      if (userMethods && userMethods.length === 1) {
        console.log(`Warning: Deleting the only ${paymentMethod.methodType} method for user ${session.user.id}`);
        // We'll allow deletion but log it for monitoring
      }
    }

    console.log('Proceeding to delete payment method:', id);
    await WalletService.deletePaymentMethod(id);
    console.log('Payment method deleted successfully');

    return NextResponse.json({
      success: true,
      message: "Payment method deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting payment method:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete payment method"
      },
      { status: 500 }
    );
  }
}
