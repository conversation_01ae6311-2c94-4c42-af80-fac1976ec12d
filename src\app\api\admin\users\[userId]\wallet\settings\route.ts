import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { wallets, users, pinCodes } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

const updateWalletSettingsSchema = z.object({
  isActive: z.boolean().optional(),
  dailyDepositLimit: z.number().positive().optional(),
  dailySendLimit: z.number().positive().optional(),
  dailyCashoutLimit: z.number().positive().optional(),
});

// Get user wallet settings
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user wallet
    const wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (!wallet) {
      return NextResponse.json(
        { message: "User wallet not found" },
        { status: 404 }
      );
    }

    // Check if user has PIN
    const pinCode = await db.query.pinCodes.findFirst({
      where: eq(pinCodes.userId, userId),
    });

    const settings = {
      isActive: wallet.isActive,
      hasPin: !!pinCode,
      pinSetAt: pinCode?.createdAt || null,
      dailyLimits: {
        deposit: wallet.dailyDepositLimit || "10000.00",
        send: wallet.dailySendLimit || "5000.00",
        cashout: wallet.dailyCashoutLimit || "3000.00",
      },
      balances: {
        general: wallet.generalBalance,
        earning: wallet.earningBalance,
      },
      totals: {
        deposited: wallet.totalDeposited,
        withdrawn: wallet.totalWithdrawn,
        sent: wallet.totalSent,
        received: wallet.totalReceived,
      },
    };

    return NextResponse.json({
      success: true,
      data: settings,
    });
  } catch (error: any) {
    console.error("Error fetching user wallet settings:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch wallet settings" 
      },
      { status: 500 }
    );
  }
}

// Update user wallet settings
export async function PATCH(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const body = await req.json();
    const validatedData = updateWalletSettingsSchema.parse(body);

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user wallet
    const wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (!wallet) {
      return NextResponse.json(
        { message: "User wallet not found" },
        { status: 404 }
      );
    }

    // Update wallet settings
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (validatedData.isActive !== undefined) {
      updateData.isActive = validatedData.isActive;
    }

    if (validatedData.dailyDepositLimit !== undefined) {
      updateData.dailyDepositLimit = validatedData.dailyDepositLimit.toFixed(2);
    }

    if (validatedData.dailySendLimit !== undefined) {
      updateData.dailySendLimit = validatedData.dailySendLimit.toFixed(2);
    }

    if (validatedData.dailyCashoutLimit !== undefined) {
      updateData.dailyCashoutLimit = validatedData.dailyCashoutLimit.toFixed(2);
    }

    await db
      .update(wallets)
      .set(updateData)
      .where(eq(wallets.id, wallet.id));

    // Log the action
    console.log(`Admin ${session.user.id} updated wallet settings for user ${userId}:`, validatedData);

    return NextResponse.json({
      success: true,
      message: "Wallet settings updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating user wallet settings:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to update wallet settings" 
      },
      { status: 500 }
    );
  }
}
