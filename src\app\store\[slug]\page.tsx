import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { stores, products, storeReviews, users, storeFollows } from "@/lib/db/schema";
import { eq, and, desc, count, avg } from "drizzle-orm";
import { notFound } from "next/navigation";
import { StoreProfile } from "@/components/marketplace/StoreProfile";

export default async function StorePage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const user = await requireAuth();
  const resolvedParams = await params;
  const { slug } = resolvedParams;

  // Fetch store details
  const storeDetails = await db
    .select({
      id: stores.id,
      name: stores.name,
      slug: stores.slug,
      description: stores.description,
      logo: stores.logo,
      banner: stores.banner,
      location: stores.location,
      phone: stores.phone,
      email: stores.email,
      website: stores.website,
      isVerified: stores.isVerified,
      createdAt: stores.createdAt,
      ownerId: stores.ownerId,
      owner: {
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
      },
    })
    .from(stores)
    .leftJoin(users, eq(stores.ownerId, users.id))
    .where(eq(stores.slug, slug))
    .limit(1);

  if (storeDetails.length === 0) {
    notFound();
  }

  const store = storeDetails[0];

  // Check if user is following the store
  const followCheck = await db
    .select({ id: storeFollows.id })
    .from(storeFollows)
    .where(
      and(
        eq(storeFollows.storeId, store.id),
        eq(storeFollows.userId, user.id)
      )
    )
    .limit(1);

  const isFollowing = followCheck.length > 0;
  const isOwner = store.ownerId === user.id;

  // Get store stats
  const productCountResult = await db
    .select({ count: count() })
    .from(products)
    .where(eq(products.storeId, store.id));

  const followerCountResult = await db
    .select({ count: count() })
    .from(storeFollows)
    .where(eq(storeFollows.storeId, store.id));

  const reviewCountResult = await db
    .select({ count: count() })
    .from(storeReviews)
    .where(eq(storeReviews.storeId, store.id));

  const ratingResult = await db
    .select({ averageRating: avg(storeReviews.rating) })
    .from(storeReviews)
    .where(eq(storeReviews.storeId, store.id));

  // Get store products
  const storeProducts = await db
    .select({
      id: products.id,
      title: products.title,
      price: products.price,
      condition: products.item_condition,
      category: products.category,
      photos: products.photos,
      createdAt: products.createdAt,
    })
    .from(products)
    .where(eq(products.storeId, store.id))
    .orderBy(desc(products.createdAt))
    .limit(12);

  // Get store reviews
  const storeReviewsList = await db
    .select({
      id: storeReviews.id,
      rating: storeReviews.rating,
      comment: storeReviews.comment,
      createdAt: storeReviews.createdAt,
      user: {
        id: users.id,
        name: users.name,
        image: users.image,
      },
    })
    .from(storeReviews)
    .leftJoin(users, eq(storeReviews.userId, users.id))
    .where(eq(storeReviews.storeId, store.id))
    .orderBy(desc(storeReviews.createdAt))
    .limit(5);

  // Check if user has already reviewed this store
  const userReview = await db
    .select({
      id: storeReviews.id,
      rating: storeReviews.rating,
      comment: storeReviews.comment,
    })
    .from(storeReviews)
    .where(
      and(
        eq(storeReviews.storeId, store.id),
        eq(storeReviews.userId, user.id)
      )
    )
    .limit(1);

  const hasReviewed = userReview.length > 0;

  const storeData = {
    ...store,
    productCount: productCountResult[0]?.count || 0,
    followerCount: followerCountResult[0]?.count || 0,
    reviewCount: reviewCountResult[0]?.count || 0,
    averageRating: ratingResult[0]?.averageRating || 0,
    products: storeProducts,
    reviews: storeReviewsList,
    isFollowing,
    isOwner,
    userReview: hasReviewed ? userReview[0] : null,
  };

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <StoreProfile store={storeData} />
      </div>
    </MainLayout>
  );
}
