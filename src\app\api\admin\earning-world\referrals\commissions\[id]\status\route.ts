import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCommissions } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Update commission status
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { status } = await request.json();
    const commissionId = params.id;

    if (!['pending', 'approved', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { message: "Invalid status" },
        { status: 400 }
      );
    }

    // Get the commission
    const commission = await db.query.referralCommissions.findFirst({
      where: eq(referralCommissions.id, commissionId),
    });

    if (!commission) {
      return NextResponse.json(
        { message: "Commission not found" },
        { status: 404 }
      );
    }

    // Update commission status
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (status === 'approved') {
      updateData.approvedAt = new Date();
    } else if (status === 'cancelled') {
      updateData.cancelledAt = new Date();
      updateData.cancelReason = 'Cancelled by admin';
    }

    await db
      .update(referralCommissions)
      .set(updateData)
      .where(eq(referralCommissions.id, commissionId));

    return NextResponse.json({
      success: true,
      message: "Commission status updated successfully",
    });

  } catch (error) {
    console.error("Error updating commission status:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update commission status"
      },
      { status: 500 }
    );
  }
}
