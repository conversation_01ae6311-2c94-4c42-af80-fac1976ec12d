"use client";

import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import {
  BuildingStorefrontIcon,
  MapPinIcon,
  CalendarIcon,
  TagIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  ClockIcon
} from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";

interface StoreInfoSidebarProps {
  store: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    location: string | null;
    phone?: string | null;
    email?: string | null;
    website?: string | null;
    isVerified: boolean | null;
    createdAt: Date;
    owner: {
      id: string;
      name: string | null;
      image: string | null;
    } | null;
  };
  categories?: string[];
}

export function StoreInfoSidebar({ store, categories = [] }: StoreInfoSidebarProps) {

  // Mock data for store hours - in a real app, this would come from the database
  const storeHours = [
    { day: "Monday", hours: "9:00 AM - 5:00 PM" },
    { day: "Tuesday", hours: "9:00 AM - 5:00 PM" },
    { day: "Wednesday", hours: "9:00 AM - 5:00 PM" },
    { day: "Thursday", hours: "9:00 AM - 5:00 PM" },
    { day: "Friday", hours: "9:00 AM - 5:00 PM" },
    { day: "Saturday", hours: "10:00 AM - 3:00 PM" },
    { day: "Sunday", hours: "Closed" },
  ];

  // Use store contact info or fallback to empty strings
  const contactInfo = {
    phone: store.phone || "",
    email: store.email || "",
    website: store.website || ""
  };

  return (
    <div className="space-y-4">

      {/* Categories */}
      {categories.length > 0 && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Categories</h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((category, index) => (
              <Link
                key={index}
                href={`/marketplace?category=${encodeURIComponent(category)}&storeId=${store.id}`}
                className="inline-flex items-center rounded-full bg-blue-50 px-3 py-1 text-sm text-blue-700 hover:bg-blue-100"
              >
                <TagIcon className="mr-1 h-4 w-4" />
                {category}
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
