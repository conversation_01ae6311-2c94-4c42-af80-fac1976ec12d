"use client";

import { useState, useEffect } from "react";
import { formatCurrency, getCurrencySettings } from "@/lib/utils/currency";

interface CurrencyDisplayProps {
  amount: number | string;
  currencyCode?: string;
  className?: string;
  showCode?: boolean;
}

export function CurrencyDisplay({ 
  amount, 
  currencyCode, 
  className = "",
  showCode = false 
}: CurrencyDisplayProps) {
  const [formattedAmount, setFormattedAmount] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadCurrencySettings() {
      try {
        const settings = await getCurrencySettings();
        const formatted = formatCurrency(amount, settings, currencyCode);
        setFormattedAmount(formatted);
      } catch (error) {
        console.error("Error loading currency settings:", error);
        // Fallback to simple formatting
        const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
        setFormattedAmount(`$${numAmount.toFixed(2)}`);
      } finally {
        setIsLoading(false);
      }
    }

    loadCurrencySettings();
  }, [amount, currencyCode]);

  if (isLoading) {
    return <span className={className}>...</span>;
  }

  return (
    <span className={className}>
      {formattedAmount}
      {showCode && currencyCode && (
        <span className="ml-1 text-xs text-gray-500">
          {currencyCode}
        </span>
      )}
    </span>
  );
}

// Hook for using currency formatting
export function useCurrencyFormat() {
  const [settings, setSettings] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadSettings() {
      try {
        const currencySettings = await getCurrencySettings();
        setSettings(currencySettings);
      } catch (error) {
        console.error("Error loading currency settings:", error);
      } finally {
        setIsLoading(false);
      }
    }

    loadSettings();
  }, []);

  const format = (amount: number | string, currencyCode?: string) => {
    if (!settings) {
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
      return `$${numAmount.toFixed(2)}`;
    }
    return formatCurrency(amount, settings, currencyCode);
  };

  return { format, settings, isLoading };
}
