"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, CreditCardIcon, BanknotesIcon, Cog6ToothIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { paymentGatewaySchema, type PaymentGatewayFormData } from "@/lib/wallet/validation";
import { toast } from "react-hot-toast";

interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  type: string;
  isActive: boolean;
  config: any;
  depositFee: string;
  depositFixedFee: string;
  minDeposit: string;
  maxDeposit: string;
  currency: string;
  sortOrder: number;
}

interface GatewayConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  gateway?: PaymentGateway | null;
  mode: 'create' | 'edit';
}

const gatewayTypes = [
  { value: 'manual', label: 'Manual Payment Method', icon: BanknotesIcon },
  { value: 'stripe', label: 'Stripe', icon: CreditCardIcon },
  { value: 'paypal', label: 'PayPal', icon: BanknotesIcon },
  { value: 'uddoktapay', label: 'UddoktaPay', icon: Cog6ToothIcon },
];

export function GatewayConfigModal({ isOpen, onClose, onSuccess, gateway, mode }: GatewayConfigModalProps) {
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('manual');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<PaymentGatewayFormData>({
    resolver: zodResolver(paymentGatewaySchema),
    defaultValues: {
      type: 'manual',
      isActive: false,
      depositFee: '0.00',
      depositFixedFee: '0.00',
      minDeposit: '1.00',
      maxDeposit: '10000.00',
      currency: 'USD',
      sortOrder: 0,
      config: {},
    },
  });

  const watchedType = watch("type");

  useEffect(() => {
    setSelectedType(watchedType);
  }, [watchedType]);

  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && gateway) {
        // Populate form with gateway data
        setValue("name", gateway.name);
        setValue("displayName", gateway.displayName);
        setValue("type", gateway.type as any);
        setValue("isActive", gateway.isActive);
        setValue("depositFee", gateway.depositFee);
        setValue("depositFixedFee", gateway.depositFixedFee);
        setValue("minDeposit", gateway.minDeposit);
        setValue("maxDeposit", gateway.maxDeposit);
        setValue("currency", gateway.currency);
        setValue("sortOrder", gateway.sortOrder);
        setValue("config", gateway.config || {});
        setSelectedType(gateway.type);
      } else {
        // Reset form for create mode
        reset({
          type: 'manual',
          isActive: false,
          depositFee: '0.00',
          depositFixedFee: '0.00',
          minDeposit: '1.00',
          maxDeposit: '10000.00',
          currency: 'USD',
          sortOrder: 0,
          config: {},
        });
        setSelectedType('manual');
      }
    }
  }, [isOpen, mode, gateway, setValue, reset]);

  const onSubmit = async (data: PaymentGatewayFormData) => {
    setLoading(true);
    try {
      const url = mode === 'edit' && gateway 
        ? `/api/admin/wallet/gateways/${gateway.id}`
        : '/api/admin/wallet/gateways';
      
      const method = mode === 'edit' ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(result.message);
        onSuccess();
      } else {
        toast.error(result.message || 'Failed to save gateway');
      }
    } catch (error) {
      console.error('Error saving gateway:', error);
      toast.error('Failed to save gateway');
    } finally {
      setLoading(false);
    }
  };

  const renderConfigFields = () => {
    switch (selectedType) {
      case 'stripe':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Publishable Key
              </label>
              <Input
                {...register("config.publishableKey")}
                placeholder="pk_test_..."
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Secret Key
              </label>
              <Input
                {...register("config.secretKey")}
                type="password"
                placeholder="sk_test_..."
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Webhook Secret
              </label>
              <Input
                {...register("config.webhookSecret")}
                type="password"
                placeholder="whsec_..."
                className="w-full"
              />
            </div>
          </div>
        );

      case 'paypal':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Client ID
              </label>
              <Input
                {...register("config.clientId")}
                placeholder="PayPal Client ID"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Client Secret
              </label>
              <Input
                {...register("config.clientSecret")}
                type="password"
                placeholder="PayPal Client Secret"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mode
              </label>
              <select
                {...register("config.mode")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="sandbox">Sandbox</option>
                <option value="live">Live</option>
              </select>
            </div>
          </div>
        );

      case 'uddoktapay':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Key
              </label>
              <Input
                {...register("config.apiKey")}
                type="password"
                placeholder="UddoktaPay API Key"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API URL
              </label>
              <Input
                {...register("config.apiUrl")}
                placeholder="https://sandbox.uddoktapay.com/api/checkout-v2"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Store ID
              </label>
              <Input
                {...register("config.storeId")}
                placeholder="Store ID"
                className="w-full"
              />
            </div>
          </div>
        );

      case 'manual':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Instructions
              </label>
              <textarea
                {...register("config.instructions")}
                rows={4}
                placeholder="Enter payment instructions for users..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Account Details
              </label>
              <textarea
                {...register("config.accountDetails")}
                rows={3}
                placeholder="Bank account or payment details..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-2xl w-full bg-white rounded-lg shadow-xl">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              {mode === 'edit' ? 'Edit Payment Gateway' : 'Add Payment Gateway'}
            </DialogTitle>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Gateway Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Gateway Type
              </label>
              <div className="grid grid-cols-2 gap-3">
                {gatewayTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <label
                      key={type.value}
                      className={`flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        selectedType === type.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <input
                        {...register("type")}
                        type="radio"
                        value={type.value}
                        className="sr-only"
                      />
                      <Icon className="h-5 w-5 mr-3 text-gray-600" />
                      <span className="text-sm font-medium text-gray-900">
                        {type.label}
                      </span>
                    </label>
                  );
                })}
              </div>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gateway Name
                </label>
                <Input
                  {...register("name")}
                  placeholder="e.g., stripe_main"
                  className="w-full"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Display Name
                </label>
                <Input
                  {...register("displayName")}
                  placeholder="e.g., Stripe Payment"
                  className="w-full"
                />
                {errors.displayName && (
                  <p className="mt-1 text-sm text-red-600">{errors.displayName.message}</p>
                )}
              </div>
            </div>

            {/* Configuration Fields */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Configuration</h3>
              {renderConfigFields()}
            </div>

            {/* Fee Configuration */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Fee Configuration</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Deposit Fee (%)
                  </label>
                  <Input
                    {...register("depositFee")}
                    type="number"
                    step="0.01"
                    min="0"
                    max="10"
                    className="w-full"
                  />
                  {errors.depositFee && (
                    <p className="mt-1 text-sm text-red-600">{errors.depositFee.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fixed Fee ($)
                  </label>
                  <Input
                    {...register("depositFixedFee")}
                    type="number"
                    step="0.01"
                    min="0"
                    className="w-full"
                  />
                  {errors.depositFixedFee && (
                    <p className="mt-1 text-sm text-red-600">{errors.depositFixedFee.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Limits Configuration */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Deposit Limits</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum ($)
                  </label>
                  <Input
                    {...register("minDeposit")}
                    type="number"
                    step="0.01"
                    min="0"
                    className="w-full"
                  />
                  {errors.minDeposit && (
                    <p className="mt-1 text-sm text-red-600">{errors.minDeposit.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum ($)
                  </label>
                  <Input
                    {...register("maxDeposit")}
                    type="number"
                    step="0.01"
                    min="0"
                    className="w-full"
                  />
                  {errors.maxDeposit && (
                    <p className="mt-1 text-sm text-red-600">{errors.maxDeposit.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency
                  </label>
                  <Input
                    {...register("currency")}
                    placeholder="USD"
                    className="w-full"
                  />
                  {errors.currency && (
                    <p className="mt-1 text-sm text-red-600">{errors.currency.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center">
              <input
                {...register("isActive")}
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Enable this gateway
              </label>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
              >
                {loading ? 'Saving...' : (mode === 'edit' ? 'Update Gateway' : 'Create Gateway')}
              </Button>
            </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
