import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { walletTransactions, users, wallets } from "@/lib/db/schema";
import { eq, desc, and, like, gte, lte, count } from "drizzle-orm";
import { z } from "zod";

const querySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(20),
  type: z.string().optional(),
  status: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sort: z.string().optional().default("createdAt"),
  order: z.string().optional().default("desc"),
});

// Get user's wallet transactions
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const url = new URL(req.url);
    const searchParams = Object.fromEntries(url.searchParams);
    const validatedParams = querySchema.parse(searchParams);

    const {
      page,
      limit,
      type,
      status,
      dateFrom,
      dateTo,
      sort,
      order,
    } = validatedParams;

    const offset = (page - 1) * limit;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Build where conditions
    let whereConditions = [eq(walletTransactions.userId, userId)];

    if (type) {
      whereConditions.push(eq(walletTransactions.type, type));
    }

    if (status) {
      whereConditions.push(eq(walletTransactions.status, status));
    }

    if (dateFrom) {
      whereConditions.push(gte(walletTransactions.createdAt, new Date(dateFrom)));
    }

    if (dateTo) {
      whereConditions.push(lte(walletTransactions.createdAt, new Date(dateTo)));
    }

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;

    // Get transactions
    const transactions = await db.query.walletTransactions.findMany({
      where: and(...whereConditions),
      orderBy: order === "asc" ? walletTransactions.createdAt : desc(walletTransactions.createdAt),
      limit,
      offset,
      with: {
        toUser: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
        toAgent: {
          columns: {
            id: true,
            name: true,
            serviceType: true,
            phone: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        transactions,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error: any) {
    console.error("Error fetching user wallet transactions:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch wallet transactions" 
      },
      { status: 500 }
    );
  }
}
