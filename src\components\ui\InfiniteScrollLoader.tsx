"use client";

import React from 'react';

interface InfiniteScrollLoaderProps {
  loading: boolean;
  hasMore: boolean;
  loadMoreRef: React.RefObject<HTMLDivElement>;
  className?: string;
}

export function InfiniteScrollLoader({ 
  loading, 
  hasMore, 
  loadMoreRef, 
  className = "" 
}: InfiniteScrollLoaderProps) {
  if (!hasMore) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500 text-sm">You've reached the end</p>
      </div>
    );
  }

  return (
    <div
      ref={loadMoreRef}
      className={`flex justify-center items-center py-8 ${className}`}
    >
      {loading ? (
        <div className="flex items-center space-x-3 text-gray-600">
          <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          <span className="text-lg font-medium">Loading more...</span>
        </div>
      ) : (
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-pulse mx-auto mb-2" />
          <p className="text-gray-500 text-sm">Scroll to load more</p>
        </div>
      )}
    </div>
  );
}
