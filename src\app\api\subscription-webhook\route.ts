import { NextResponse } from "next/server";
import { SubscriptionService } from "@/lib/subscription/subscriptionService";
import { PaymentService } from "@/lib/payment/paymentService";
import { db } from "@/lib/db";
import { subscriptionTransactions } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { gatewayType, payload } = body;

    // Verify the payment with the gateway
    const isValid = await PaymentService.verifyPayment(gatewayType, payload);
    
    if (!isValid) {
      return NextResponse.json(
        { message: "Invalid payment verification" },
        { status: 400 }
      );
    }

    // Extract transaction details from payload
    const { transactionId, status, gatewayTransactionId, metadata } = payload;

    // Find the subscription transaction
    const transaction = await db.query.subscriptionTransactions.findFirst({
      where: eq(subscriptionTransactions.id, transactionId),
    });

    if (!transaction) {
      return NextResponse.json(
        { message: "Transaction not found" },
        { status: 404 }
      );
    }

    // Update transaction status
    await SubscriptionService.updateTransactionStatus(
      transactionId,
      status === 'success' ? 'completed' : 'failed',
      payload
    );

    // If payment was successful, activate the subscription
    if (status === 'success') {
      await SubscriptionService.activateSubscription(transaction.subscriptionId, transactionId);

      console.log(`Subscription ${transaction.subscriptionId} activated for user ${transaction.userId}`);
    } else {
      console.log(`Payment failed for subscription ${transaction.subscriptionId}`);
    }

    return NextResponse.json({
      success: true,
      message: "Webhook processed successfully",
    });

  } catch (error) {
    console.error("Error processing subscription webhook:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
