import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, posts, users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and, count } from "drizzle-orm";

const groupUpdateSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  description: z.string().max(1000).optional(),
  visibility: z.enum(["public", "private-visible", "private-hidden"]).optional(),
  category: z.string().max(100).optional(),
  rules: z.string().max(1000).optional(),
  postPermission: z.enum(["all-members", "admin-only"]).optional(),
});

// Get a single group with admin details
export async function GET(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const groupId = params.groupId;

    // Fetch the group without joins to avoid collation issues
    const groupResult = await db.select({
      id: groups.id,
      name: groups.name,
      description: groups.description,
      visibility: groups.visibility,
      category: groups.category,
      rules: groups.rules,
      coverImage: groups.coverImage,
      postPermission: groups.postPermission,
      creatorId: groups.creatorId,
      createdAt: groups.createdAt,
      updatedAt: groups.updatedAt,
    })
    .from(groups)
    .where(eq(groups.id, groupId))
    .limit(1)
    .execute()
    .then(results => results[0]);

    // Get member count separately
    const memberCount = await db.select({
      count: count().as('count')
    })
    .from(groupMembers)
    .where(eq(groupMembers.groupId, groupId))
    .execute()
    .then(results => results[0]?.count || 0);

    if (!groupResult) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Get creator details
    const creator = await db.query.users.findFirst({
      where: eq(users.id, groupResult.creatorId),
      columns: {
        id: true,
        name: true,
        username: true,
        image: true,
      },
    });

    // Get post count
    const postCount = await db.select({
      count: count().as('count')
    })
    .from(posts)
    .where(eq(posts.groupId, groupId))
    .execute()
    .then(results => results[0]?.count || 0);

    // Get admin count
    const adminCount = await db.select({
      count: count().as('count')
    })
    .from(groupMembers)
    .where(and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.role, "admin")
    ))
    .execute()
    .then(results => results[0]?.count || 0);

    // Format the response
    const formattedGroup = {
      ...groupResult,
      memberCount: Number(memberCount),
      postCount: Number(postCount),
      adminCount: Number(adminCount),
      creator,
    };

    return NextResponse.json(formattedGroup);
  } catch (error) {
    console.error("Error fetching group:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a group as admin
export async function PUT(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const groupId = params.groupId;
    const body = await req.json();
    const validatedData = groupUpdateSchema.parse(body);

    // Check if the group exists
    const existingGroup = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!existingGroup) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Update the group
    await db.update(groups)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(groups.id, groupId));

    // Get the updated group
    const updatedGroup = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
      with: {
        creator: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json(updatedGroup);
  } catch (error) {
    console.error("Error updating group:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a group as admin
export async function DELETE(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const groupId = params.groupId;

    // Check if the group exists
    const existingGroup = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!existingGroup) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Delete all group members first (due to foreign key constraints)
    await db.delete(groupMembers)
      .where(eq(groupMembers.groupId, groupId));

    // Delete all group posts
    await db.delete(posts)
      .where(eq(posts.groupId, groupId));

    // Delete the group
    await db.delete(groups)
      .where(eq(groups.id, groupId));

    return NextResponse.json({
      message: "Group deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting group:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
