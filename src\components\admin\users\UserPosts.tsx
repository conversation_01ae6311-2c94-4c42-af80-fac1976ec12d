"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { Card } from "@/components/ui/Card";
import { toast } from "react-hot-toast";
import {
  MagnifyingGlassIcon,
  EyeIcon,
  TrashIcon,
  PhotoIcon,
  VideoCameraIcon,
  MapPinIcon,
  FaceSmileIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  backgroundColor: string | null;
  sharedPostId: string | null;
  pageId: string | null;
  groupId: string | null;
  createdAt: string;
  updatedAt: string;
  pageName: string | null;
  groupName: string | null;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface UserPostsProps {
  userId: string;
}

export function UserPosts({ userId }: UserPostsProps) {
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });

  useEffect(() => {
    fetchPosts();
  }, [userId, pagination.page]);

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      const response = await fetch(`/api/admin/users/${userId}/posts?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch posts");
      }

      const data = await response.json();
      setPosts(data.posts);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching posts:", error);
      toast.error("Failed to fetch posts");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchPosts();
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const formatContent = (content: string) => {
    if (!content) return "No content";
    return content.length > 200 ? content.substring(0, 200) + "..." : content;
  };

  const getPrivacyBadge = (privacy: string) => {
    const variants = {
      public: "success",
      friends: "warning",
      private: "default",
    } as const;

    return (
      <Badge variant={variants[privacy as keyof typeof variants] || "default"}>
        {privacy}
      </Badge>
    );
  };

  if (isLoading && posts.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">User Posts</h3>
          <p className="text-sm text-gray-600">
            Total: {pagination.total} posts
          </p>
        </div>

        {/* Search */}
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search posts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              className="pl-10 w-64"
            />
          </div>
          <Button onClick={handleSearch}>Search</Button>
        </div>
      </div>

      {/* Posts List */}
      {posts.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-gray-500">
            <DocumentTextIcon className="mx-auto h-12 w-12 mb-4" />
            <h3 className="text-lg font-medium mb-2">No posts found</h3>
            <p>This user hasn't created any posts yet.</p>
          </div>
        </Card>
      ) : (
        <div className="space-y-4">
          {posts.map((post) => (
            <Card key={post.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Post Header */}
                  <div className="flex items-center space-x-2 mb-3">
                    {getPrivacyBadge(post.privacy)}
                    {post.pageName && (
                      <Badge variant="outline">Page: {post.pageName}</Badge>
                    )}
                    {post.groupName && (
                      <Badge variant="outline">Group: {post.groupName}</Badge>
                    )}
                    {post.sharedPostId && (
                      <Badge variant="primary">Shared Post</Badge>
                    )}
                  </div>

                  {/* Post Content */}
                  <div className="mb-4">
                    {post.content && (
                      <p className="text-gray-900 mb-3">{formatContent(post.content)}</p>
                    )}

                    {/* Post Metadata */}
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      {post.feeling && (
                        <div className="flex items-center space-x-1">
                          <FaceSmileIcon className="h-4 w-4" />
                          <span>Feeling {post.feeling}</span>
                        </div>
                      )}
                      {post.activity && (
                        <span>{post.activity}</span>
                      )}
                      {post.location && (
                        <div className="flex items-center space-x-1">
                          <MapPinIcon className="h-4 w-4" />
                          <span>{post.location}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Media Indicators */}
                  <div className="flex items-center space-x-4 mb-4">
                    {post.images && post.images.length > 0 && (
                      <div className="flex items-center space-x-1 text-sm text-blue-600">
                        <PhotoIcon className="h-4 w-4" />
                        <span>{post.images.length} image(s)</span>
                      </div>
                    )}
                    {post.videos && post.videos.length > 0 && (
                      <div className="flex items-center space-x-1 text-sm text-purple-600">
                        <VideoCameraIcon className="h-4 w-4" />
                        <span>{post.videos.length} video(s)</span>
                      </div>
                    )}
                  </div>

                  {/* Post Footer */}
                  <div className="text-xs text-gray-500">
                    Created: {new Date(post.createdAt).toLocaleString()}
                    {post.updatedAt !== post.createdAt && (
                      <span className="ml-4">
                        Updated: {new Date(post.updatedAt).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 ml-4">
                  <Button variant="outline" size="sm">
                    <EyeIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
            {pagination.total} results
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={pagination.page === page ? "primary" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              Next
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
