import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referrals, wallets, walletTransactions } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// Process referral reward payment
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const referralId = params.id;

    // Get the referral
    const referral = await db.query.referrals.findFirst({
      where: eq(referrals.id, referralId),
    });

    if (!referral) {
      return NextResponse.json(
        { message: "Referral not found" },
        { status: 404 }
      );
    }

    if (referral.status !== 'completed') {
      return NextResponse.json(
        { message: "Referral must be completed before payment" },
        { status: 400 }
      );
    }

    if (referral.paidAt) {
      return NextResponse.json(
        { message: "Referral reward already paid" },
        { status: 400 }
      );
    }

    // Get or create wallet for the referrer
    let wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, referral.referrerId),
    });

    if (!wallet) {
      // Create wallet if it doesn't exist
      await db.insert(wallets).values({
        id: uuidv4(),
        userId: referral.referrerId,
        generalBalance: '0.00',
        earningBalance: '0.00',
        totalDeposited: '0.00',
        totalWithdrawn: '0.00',
        totalSent: '0.00',
        totalReceived: '0.00',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      wallet = await db.query.wallets.findFirst({
        where: eq(wallets.userId, referral.referrerId),
      });
    }

    if (!wallet) {
      return NextResponse.json(
        { message: "Failed to create or find wallet" },
        { status: 500 }
      );
    }

    // Start transaction
    await db.transaction(async (tx) => {
      // Update earning wallet balance
      await tx
        .update(wallets)
        .set({
          earningBalance: sql`${wallets.earningBalance} + ${referral.rewardAmount}`,
          updatedAt: new Date(),
        })
        .where(eq(wallets.userId, referral.referrerId));

      // Create transaction record
      await tx.insert(walletTransactions).values({
        id: uuidv4(),
        userId: referral.referrerId,
        type: 'earning',
        amount: referral.rewardAmount,
        fee: '0.00',
        netAmount: referral.rewardAmount,
        walletType: 'earning',
        status: 'completed',
        reference: `referral_reward_${referral.id}`,
        note: `Referral reward for code: ${referral.referralCode}`,
        metadata: {
          referralId: referral.id,
          referralCode: referral.referralCode,
          type: 'referral_reward',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Mark referral as paid
      await tx
        .update(referrals)
        .set({
          paidAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(referrals.id, referralId));
    });

    return NextResponse.json({
      success: true,
      message: "Referral reward payment processed successfully",
    });

  } catch (error) {
    console.error("Error processing referral payment:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to process referral payment",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
