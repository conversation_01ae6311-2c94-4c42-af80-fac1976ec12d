"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import {
  ChartBarIcon,
  UsersIcon,
  BanknotesIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  StarIcon,
  DocumentTextIcon,
  CalendarIcon,
  ArrowPathIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";

interface EarningWorldStats {
  totalEarnings: number;
  totalUsers: number;
  activeEarners: number;
  totalReferrals: number;
  completedReferrals: number;
  pendingPayouts: number;
  totalPayouts: number;
  monthlyEarnings: number;
  monthlyGrowth: number;
  blogMonetization: {
    totalBlogs: number;
    monetizedBlogs: number;
    pendingApproval: number;
    totalReads: number;
    totalEarnings: number;
  };
  topEarners: Array<{
    id: string;
    name: string;
    username: string;
    totalEarnings: number;
    earningType: string;
  }>;
  recentActivity: Array<{
    id: string;
    type: string;
    user: string;
    amount: number;
    date: string;
    status: string;
  }>;
  earningBreakdown: {
    referrals: number;
    blogMonetization: number;
    other: number;
  };
}

export default function AdminEarningWorldPage() {
  const [stats, setStats] = useState<EarningWorldStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/earning-world/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching earning world stats:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchStats();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Earning World Overview</h1>
            <p className="text-gray-600 mt-1">
              Monitor and manage the earning world ecosystem
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={() => window.location.href = '/admin/earning-world/analytics'}>
              <ChartBarIcon className="h-5 w-5 mr-2" />
              View Analytics
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Total Earnings */}
              <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg p-6 text-white">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className="h-8 w-8" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium opacity-90">Total Earnings</p>
                    <p className="text-2xl font-bold">{formatCurrency(stats.totalEarnings)}</p>
                  </div>
                </div>
              </div>

              {/* Active Earners */}
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UsersIcon className="h-8 w-8" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium opacity-90">Active Earners</p>
                    <p className="text-2xl font-bold">{formatNumber(stats.activeEarners)}</p>
                    <p className="text-xs opacity-75">of {formatNumber(stats.totalUsers)} users</p>
                  </div>
                </div>
              </div>

              {/* Total Referrals */}
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UserGroupIcon className="h-8 w-8" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium opacity-90">Total Referrals</p>
                    <p className="text-2xl font-bold">{formatNumber(stats.totalReferrals)}</p>
                    <p className="text-xs opacity-75">{formatNumber(stats.completedReferrals)} completed</p>
                  </div>
                </div>
              </div>

              {/* Blog Monetization */}
              <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg p-6 text-white">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DocumentTextIcon className="h-8 w-8" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium opacity-90">Blog Monetization</p>
                    <p className="text-2xl font-bold">{formatNumber(stats.blogMonetization.monetizedBlogs)}</p>
                    <p className="text-xs opacity-75">{formatNumber(stats.blogMonetization.pendingApproval)} pending</p>
                  </div>
                </div>
              </div>

              {/* Pending Payouts */}
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BanknotesIcon className="h-8 w-8" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium opacity-90">Pending Payouts</p>
                    <p className="text-2xl font-bold">{formatCurrency(stats.pendingPayouts)}</p>
                    <p className="text-xs opacity-75">Total paid: {formatCurrency(stats.totalPayouts)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Monthly Performance */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Monthly Earnings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CalendarIcon className="h-5 w-5 mr-2" />
                    Monthly Performance
                  </CardTitle>
                  <CardDescription>
                    Current month earnings and growth
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">This Month</span>
                      <span className="text-2xl font-bold text-gray-900">
                        {formatCurrency(stats.monthlyEarnings)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">Growth</span>
                      <div className="flex items-center">
                        {stats.monthlyGrowth >= 0 ? (
                          <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                        ) : (
                          <ArrowTrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
                        )}
                        <span className={`text-sm font-medium ${
                          stats.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {Math.abs(stats.monthlyGrowth).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Earning Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ChartBarIcon className="h-5 w-5 mr-2" />
                    Earning Sources
                  </CardTitle>
                  <CardDescription>
                    Breakdown by earning type
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        <span className="text-sm font-medium">Referrals</span>
                      </div>
                      <span className="text-sm font-bold">
                        {formatCurrency(stats.earningBreakdown.referrals)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm font-medium">Blog Monetization</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-bold block">
                          {formatCurrency(stats.earningBreakdown.blogMonetization)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatNumber(stats.blogMonetization.totalReads)} reads
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                        <span className="text-sm font-medium">Other</span>
                      </div>
                      <span className="text-sm font-bold">
                        {formatCurrency(stats.earningBreakdown.other)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}

        {/* Blog Monetization Overview */}
        {stats && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Blog Monetization Overview
              </CardTitle>
              <CardDescription>
                Detailed blog monetization statistics and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatNumber(stats.blogMonetization.totalBlogs)}
                  </div>
                  <div className="text-sm text-gray-600">Total Blogs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {formatNumber(stats.blogMonetization.monetizedBlogs)}
                  </div>
                  <div className="text-sm text-gray-600">Monetized Blogs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {formatNumber(stats.blogMonetization.pendingApproval)}
                  </div>
                  <div className="text-sm text-gray-600">Pending Approval</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatCurrency(stats.blogMonetization.totalEarnings)}
                  </div>
                  <div className="text-sm text-gray-600">Total Earnings</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => window.location.href = '/admin/monetization'}
          >
            <CurrencyDollarIcon className="h-6 w-6 mb-2" />
            <span className="text-sm">Blog Monetization</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => window.location.href = '/admin/earning-world/users'}
          >
            <UsersIcon className="h-6 w-6 mb-2" />
            <span className="text-sm">Manage Users</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => window.location.href = '/admin/earning-world/referrals'}
          >
            <UserGroupIcon className="h-6 w-6 mb-2" />
            <span className="text-sm">Referral System</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => window.location.href = '/admin/earning-world/payouts'}
          >
            <BanknotesIcon className="h-6 w-6 mb-2" />
            <span className="text-sm">Manage Payouts</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => window.location.href = '/admin/earning-world/settings'}
          >
            <Cog6ToothIcon className="h-6 w-6 mb-2" />
            <span className="text-sm">System Settings</span>
          </Button>
        </div>
      </div>
    </AdminLayout>
  );
}
