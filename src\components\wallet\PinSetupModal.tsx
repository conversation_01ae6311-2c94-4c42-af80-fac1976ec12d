"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, ShieldCheckIcon, ExclamationTriangleIcon, EyeIcon, EyeSlashIcon, CheckCircleIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { PinInputField } from "@/components/ui/PinInput";
import { pinSetupSchema, type PinSetupFormData } from "@/lib/wallet/validation";
import { toast } from "react-hot-toast";

interface PinSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  language?: 'en' | 'bn';
  allowSkip?: boolean;
  title?: string;
  description?: string;
}

export function PinSetupModal({
  isOpen,
  onClose,
  onSuccess,
  language = 'en',
  allowSkip = false,
  title,
  description
}: PinSetupModalProps) {
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<'create' | 'confirm' | 'success'>('create');
  const [showPinPreview, setShowPinPreview] = useState(false);
  const [pinStrength, setPinStrength] = useState(0);

  const {
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<PinSetupFormData>({
    resolver: zodResolver(pinSetupSchema),
  });

  const pin = watch("pin");
  const confirmPin = watch("confirmPin");

  // Language support
  const texts = {
    en: {
      title: title || "Set Wallet PIN",
      description: description || "Your 4-digit PIN will be required for all wallet transactions including sending money, cashouts, and transfers. Choose a PIN that's easy for you to remember but hard for others to guess.",
      createPin: "Create your wallet PIN",
      createPinDesc: "Enter a 4-digit PIN to secure your wallet",
      confirmPin: "Confirm your PIN",
      confirmPinDesc: "Re-enter your PIN to confirm",
      pinSecurity: "PIN Security:",
      exactly4Digits: "Exactly 4 digits",
      noRepeated: "No repeated digits (e.g., 1111)",
      notSequential: "Not a sequential pattern",
      securityTips: "Security Tips",
      tips: [
        "Avoid obvious patterns like 1234, 0000, or your birth year",
        "Never share your PIN with anyone",
        "Use a PIN that's easy for you to remember but hard to guess",
        "Your account will be locked for 30 minutes after 3 failed attempts"
      ],
      cancel: "Cancel",
      setPin: "Set PIN",
      settingPin: "Setting PIN...",
      pinCreated: "PIN created successfully!",
      pinsMatch: "PINs match",
      pinsNotMatch: "PINs do not match",
      skip: "Skip for now",
      secureWallet: "Secure Your Wallet"
    },
    bn: {
      title: title || "ওয়ালেট পিন সেট করুন",
      description: description || "আপনার ৪-সংখ্যার পিন সকল ওয়ালেট লেনদেনের জন্য প্রয়োজন হবে যেমন টাকা পাঠানো, ক্যাশআউট এবং ট্রান্সফার। এমন একটি পিন বেছে নিন যা আপনার মনে রাখা সহজ কিন্তু অন্যদের অনুমান করা কঠিন।",
      createPin: "আপনার ওয়ালেট পিন তৈরি করুন",
      createPinDesc: "আপনার ওয়ালেট সুরক্ষিত করতে ৪-সংখ্যার পিন দিন",
      confirmPin: "আপনার পিন নিশ্চিত করুন",
      confirmPinDesc: "নিশ্চিত করতে আপনার পিন পুনরায় দিন",
      pinSecurity: "পিন নিরাপত্তা:",
      exactly4Digits: "ঠিক ৪টি সংখ্যা",
      noRepeated: "একই সংখ্যার পুনরাবৃত্তি নেই (যেমন, ১১১১)",
      notSequential: "ক্রমিক প্যাটার্ন নয়",
      securityTips: "নিরাপত্তা টিপস",
      tips: [
        "সুস্পষ্ট প্যাটার্ন এড়িয়ে চলুন যেমন ১২৩৪, ০০০০, বা আপনার জন্মসাল",
        "আপনার পিন কারো সাথে শেয়ার করবেন না",
        "এমন পিন ব্যবহার করুন যা আপনার মনে রাখা সহজ কিন্তু অনুমান করা কঠিন",
        "৩টি ভুল চেষ্টার পর আপনার অ্যাকাউন্ট ৩০ মিনিটের জন্য লক হয়ে যাবে"
      ],
      cancel: "বাতিল",
      setPin: "পিন সেট করুন",
      settingPin: "পিন সেট করা হচ্ছে...",
      pinCreated: "পিন সফলভাবে তৈরি হয়েছে!",
      pinsMatch: "পিন মিলেছে",
      pinsNotMatch: "পিন মিলেনি",
      skip: "এখনের জন্য এড়িয়ে যান",
      secureWallet: "আপনার ওয়ালেট সুরক্ষিত করুন"
    }
  };

  const t = texts[language];

  // Calculate PIN strength
  const calculatePinStrength = (pin: string): number => {
    if (!pin) return 0;
    let strength = 0;

    // Length check
    if (pin.length === 4) strength += 25;

    // No repeated digits
    if (!/(.)\1{2,}/.test(pin)) strength += 25;

    // Not sequential
    if (!/^(0123|1234|2345|3456|4567|5678|6789|9876|8765|7654|6543|5432|4321|3210)$/.test(pin)) strength += 25;

    // Not common patterns
    const commonPatterns = ['0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999'];
    if (!commonPatterns.includes(pin)) strength += 25;

    return strength;
  };

  // Update PIN strength when PIN changes
  useEffect(() => {
    if (pin) {
      setPinStrength(calculatePinStrength(pin));
      if (pin.length === 4) {
        setCurrentStep('confirm');
      }
    } else {
      setPinStrength(0);
      setCurrentStep('create');
    }
  }, [pin]);

  const onSubmit = async (data: PinSetupFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/wallet/pin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: data.pin,
          confirmPin: data.confirmPin,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentStep('success');
        toast.success(language === 'bn' ? 'ওয়ালেট পিন সফলভাবে সেট হয়েছে!' : 'Wallet PIN set successfully!');
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      } else {
        toast.error(result.message || (language === 'bn' ? 'পিন সেট করতে ব্যর্থ' : 'Failed to set PIN'));
      }
    } catch (error) {
      console.error('Error setting PIN:', error);
      toast.error('Failed to set PIN');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setCurrentStep('create');
    setPinStrength(0);
    setShowPinPreview(false);
    onClose();
  };

  const handleSkip = () => {
    if (allowSkip) {
      handleClose();
    }
  };

  const isPinValid = pin && pin.length === 4;
  const pinsMatch = pin === confirmPin && confirmPin && confirmPin.length === 4;
  const showConfirmPin = currentStep === 'confirm' || (pin && pin.length === 4);

  // Get strength color
  const getStrengthColor = (strength: number) => {
    if (strength >= 75) return 'text-green-600';
    if (strength >= 50) return 'text-yellow-600';
    if (strength >= 25) return 'text-orange-600';
    return 'text-red-600';
  };

  const getStrengthText = (strength: number) => {
    if (language === 'bn') {
      if (strength >= 75) return 'অত্যন্ত শক্তিশালী';
      if (strength >= 50) return 'ভালো';
      if (strength >= 25) return 'মধ্যম';
      return 'দুর্বল';
    } else {
      if (strength >= 75) return 'Very Strong';
      if (strength >= 50) return 'Good';
      if (strength >= 25) return 'Fair';
      return 'Weak';
    }
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4 sm:p-6">
        <DialogPanel className="mx-auto max-w-md w-full max-h-[95vh] sm:max-h-[90vh] bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden modal-enter">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <ShieldCheckIcon className="h-6 w-6 text-blue-600" />
              </div>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                {t.title}
              </DialogTitle>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Progress Indicator */}
          <div className="px-6 pt-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                currentStep === 'create' ? 'bg-blue-600' :
                currentStep === 'confirm' || currentStep === 'success' ? 'bg-green-600' : 'bg-gray-300'
              }`} />
              <div className={`flex-1 h-1 rounded ${
                currentStep === 'confirm' || currentStep === 'success' ? 'bg-green-600' : 'bg-gray-300'
              }`} />
              <div className={`w-3 h-3 rounded-full ${
                currentStep === 'confirm' ? 'bg-blue-600' :
                currentStep === 'success' ? 'bg-green-600' : 'bg-gray-300'
              }`} />
              <div className={`flex-1 h-1 rounded ${
                currentStep === 'success' ? 'bg-green-600' : 'bg-gray-300'
              }`} />
              <div className={`w-3 h-3 rounded-full ${
                currentStep === 'success' ? 'bg-green-600' : 'bg-gray-300'
              }`} />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-2">
              <span>{language === 'bn' ? 'তৈরি' : 'Create'}</span>
              <span>{language === 'bn' ? 'নিশ্চিত' : 'Confirm'}</span>
              <span>{language === 'bn' ? 'সম্পন্ন' : 'Complete'}</span>
            </div>
          </div>



          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            <div className="p-6 pb-2">
              {/* Security Info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <ShieldCheckIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 mb-1">
                      {t.secureWallet}
                    </h3>
                    <p className="text-sm text-blue-700">
                      {t.description}
                    </p>
                  </div>
                </div>
              </div>

            {/* Success Step */}
            {currentStep === 'success' && (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircleIcon className="h-10 w-10 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {language === 'bn' ? 'সফল!' : 'Success!'}
                </h3>
                <p className="text-gray-600">
                  {language === 'bn' ? 'আপনার ওয়ালেট পিন সফলভাবে সেট হয়েছে!' : 'Your wallet PIN has been set successfully!'}
                </p>
              </div>
            )}

            {currentStep !== 'success' && (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* PIN Input */}
                {currentStep === 'create' && (
                  <div className="space-y-4">
                    <PinInputField
                      label={t.createPin}
                      description={t.createPinDesc}
                      value={pin || ""}
                      onChange={(value) => setValue("pin", value)}
                      error={errors.pin?.message}
                      required
                      length={4}
                      autoFocus
                    />

                    {/* PIN Strength Indicator */}
                    {pin && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">
                            {language === 'bn' ? 'পিন শক্তি:' : 'PIN Strength:'}
                          </span>
                          <span className={`text-sm font-medium ${getStrengthColor(pinStrength)}`}>
                            {getStrengthText(pinStrength)}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              pinStrength >= 75 ? 'bg-green-600' :
                              pinStrength >= 50 ? 'bg-yellow-600' :
                              pinStrength >= 25 ? 'bg-orange-600' : 'bg-red-600'
                            }`}
                            style={{ width: `${pinStrength}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Confirm PIN Input */}
                {currentStep === 'confirm' && (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2 p-3 bg-green-50 rounded-lg">
                      <CheckCircleIcon className="w-5 h-5 text-green-600" />
                      <span className="text-sm text-green-700 font-medium">{t.pinCreated}</span>
                    </div>
                    <PinInputField
                      label={t.confirmPin}
                      description={t.confirmPinDesc}
                      value={confirmPin || ""}
                      onChange={(value) => setValue("confirmPin", value)}
                      error={errors.confirmPin?.message}
                      required
                      length={4}
                      autoFocus
                    />

                    {/* PIN Match Indicator */}
                    {confirmPin && (
                      <div className={`flex items-center text-sm p-2 rounded ${
                        pinsMatch ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50'
                      }`}>
                        {pinsMatch ? (
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                        ) : (
                          <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                        )}
                        {pinsMatch ? t.pinsMatch : t.pinsNotMatch}
                      </div>
                    )}
                  </div>
                )}

                {/* Security Tips */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="text-sm font-medium text-yellow-900 mb-1">
                        {t.securityTips}
                      </h3>
                      <ul className="text-sm text-yellow-700 space-y-1">
                        {t.tips.map((tip, index) => (
                          <li key={index}>• {tip}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

              </form>
            )}
            </div>
          </div>

          {/* Fixed Footer with Buttons */}
          {currentStep !== 'success' && (
            <div className="border-t border-gray-200 p-4 sm:p-6 bg-gray-50 rounded-b-xl flex-shrink-0">
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                {allowSkip && currentStep === 'create' && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSkip}
                    className="w-full sm:flex-1"
                    disabled={loading}
                  >
                    {t.skip}
                  </Button>
                )}

                {!allowSkip && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    className="w-full sm:flex-1"
                    disabled={loading}
                  >
                    {t.cancel}
                  </Button>
                )}

                <Button
                  type="submit"
                  onClick={handleSubmit(onSubmit)}
                  className="w-full sm:flex-1"
                  disabled={loading || (currentStep === 'confirm' && !pinsMatch) || (currentStep === 'create' && (!pin || pin.length !== 4))}
                  isLoading={loading}
                >
                  {loading ? t.settingPin :
                   currentStep === 'create' && pin && pin.length === 4 ?
                   (language === 'bn' ? 'পরবর্তী' : 'Next') :
                   t.setPin}
                </Button>
              </div>
            </div>
          )}
        </DialogPanel>
      </div>
    </Dialog>
  );
}
