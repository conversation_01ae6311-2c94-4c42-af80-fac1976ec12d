"use client";

import { useState, useEffect } from "react";
import { ListBulletIcon } from "@heroicons/react/24/outline";

interface TOCItem {
  id: string;
  text: string;
  level: number;
}

interface TableOfContentsProps {
  content: string;
}

export function TableOfContents({ content }: TableOfContentsProps) {
  const [tocItems, setTocItems] = useState<TOCItem[]>([]);
  const [activeId, setActiveId] = useState<string>("");
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Extract headings from content
    const headings: TOCItem[] = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      if (line.startsWith('#')) {
        const level = line.match(/^#+/)?.[0].length || 1;
        const text = line.replace(/^#+\s*/, '');
        const id = `heading-${index}-${text.toLowerCase().replace(/[^a-z0-9]+/g, '-')}`;
        
        if (level <= 3) { // Only show h1, h2, h3
          headings.push({ id, text, level });
        }
      }
    });

    setTocItems(headings);
  }, [content]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      },
      { rootMargin: "-20% 0% -35% 0%" }
    );

    // Observe all heading elements
    tocItems.forEach((item) => {
      const element = document.getElementById(item.id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [tocItems]);

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  if (tocItems.length === 0) {
    return null;
  }

  return (
    <>
      {/* Mobile Toggle Button */}
      <div className="lg:hidden mb-6">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-4 py-2 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all"
        >
          <ListBulletIcon className="w-5 h-5 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">Table of Contents</span>
        </button>
      </div>

      {/* Table of Contents */}
      <div className={`${isOpen ? 'block' : 'hidden'} lg:block bg-white rounded-2xl p-6 shadow-lg border border-gray-100 mb-8`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <ListBulletIcon className="w-5 h-5 mr-2 text-blue-600" />
          Table of Contents
        </h3>
        
        <nav className="space-y-2">
          {tocItems.map((item) => (
            <button
              key={item.id}
              onClick={() => scrollToHeading(item.id)}
              className={`block w-full text-left px-3 py-2 rounded-lg transition-all duration-200 ${
                activeId === item.id
                  ? "bg-blue-50 text-blue-700 border-l-4 border-blue-500"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              }`}
              style={{ 
                paddingLeft: `${(item.level - 1) * 16 + 12}px`,
                fontSize: item.level === 1 ? '14px' : item.level === 2 ? '13px' : '12px'
              }}
            >
              {item.text}
            </button>
          ))}
        </nav>
      </div>
    </>
  );
}
