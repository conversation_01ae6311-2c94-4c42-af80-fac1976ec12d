"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { FollowButton } from "./FollowButton";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  CheckBadgeIcon,
  UsersIcon,
  XMarkIcon,
  ChevronDownIcon
} from "@heroicons/react/24/outline";
import { CheckBadgeIcon as CheckBadgeIconSolid } from "@heroicons/react/24/solid";

interface FanPage {
  id: string;
  name: string;
  username: string;
  category: string;
  description: string | null;
  profileImage: string | null;
  isVerified: boolean;
  followerCount: number;
  createdAt: string;
  isFollowing?: boolean;
}

interface FanPagesDirectoryProps {
  currentUserId?: string;
}

const categories = [
  { value: "all", label: "All Categories" },
  { value: "musician", label: "Musicians" },
  { value: "actor", label: "Actors" },
  { value: "brand", label: "Brands" },
  { value: "business", label: "Businesses" },
  { value: "organization", label: "Organizations" },
  { value: "public_figure", label: "Public Figures" },
  { value: "artist", label: "Artists" },
  { value: "writer", label: "Writers" },
  { value: "athlete", label: "Athletes" },
  { value: "politician", label: "Politicians" },
  { value: "entertainment", label: "Entertainment" },
  { value: "media", label: "Media" },
  { value: "community", label: "Community" },
  { value: "cause", label: "Causes" },
  { value: "other", label: "Other" },
];

export function FanPagesDirectory({ currentUserId }: FanPagesDirectoryProps) {
  const router = useRouter();
  const [pages, setPages] = useState<FanPage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [activeTab, setActiveTab] = useState<'explore' | 'created' | 'liked'>('explore');

  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  const fetchPages = async (page: number = 1, append: boolean = false) => {
    try {
      if (page === 1) setLoading(true);
      else setLoadingMore(true);

      let apiUrl = '';
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "12",
        ...(searchQuery && { search: searchQuery }),
        ...(selectedCategory !== "all" && { category: selectedCategory }),
      });

      // Determine API endpoint based on active tab
      if (activeTab === 'explore') {
        if (searchQuery.trim() || selectedCategory !== 'all') {
          // Use enhanced search API for explore tab with search/category filters
          const searchParams = new URLSearchParams({
            page: page.toString(),
            limit: "12",
            ...(searchQuery && { q: searchQuery }),
            ...(selectedCategory !== "all" && { category: selectedCategory }),
          });
          apiUrl = `/api/fan-pages/search?${searchParams}`;
        } else {
          // Use regular API for simple listing
          apiUrl = `/api/fan-pages?${params}`;
        }
      } else {
        // For created and liked tabs, use existing APIs
        switch (activeTab) {
          case 'created':
            if (!currentUserId) {
              setPages([]);
              setLoading(false);
              return;
            }
            apiUrl = `/api/fan-pages/user/created?${params}`;
            break;
          case 'liked':
            if (!currentUserId) {
              setPages([]);
              setLoading(false);
              return;
            }
            apiUrl = `/api/fan-pages/user/liked?${params}`;
            break;
        }
      }

      const response = await fetch(apiUrl);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch fan pages");
      }

      if (append) {
        setPages(prev => [...prev, ...data.pages]);
      } else {
        setPages(data.pages);
      }

      setCurrentPage(data.pagination.page);
      setTotalPages(data.pagination.totalPages);

    } catch (error) {
      console.error("Error fetching fan pages:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch fan pages");
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Debounced search effect
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchPages();
    }, searchQuery ? 300 : 0); // 300ms delay for search, immediate for other changes

    setSearchTimeout(timeout);

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [searchQuery, selectedCategory, activeTab]);

  const handleTabChange = (tab: 'explore' | 'created' | 'liked') => {
    setActiveTab(tab);
    setCurrentPage(1);
    setSearchQuery("");
    setSelectedCategory("all");
  };

  const clearAllFilters = () => {
    setSearchQuery("");
    setSelectedCategory("all");
  };

  const hasActiveFilters = () => {
    return searchQuery.trim() || selectedCategory !== "all";
  };

  const getEmptyStateTitle = () => {
    if (hasActiveFilters()) {
      return "No pages match your search";
    }

    switch (activeTab) {
      case 'created':
        return "No pages created yet";
      case 'liked':
        return "No liked pages yet";
      case 'explore':
      default:
        return "No fan pages yet";
    }
  };

  const getEmptyStateDescription = () => {
    if (hasActiveFilters()) {
      return "Try adjusting your search terms, filters, or browse different categories to discover amazing fan pages.";
    }

    switch (activeTab) {
      case 'created':
        return "Start building your community! Create your first fan page and connect with your audience.";
      case 'liked':
        return "Explore and follow fan pages that interest you. Your liked pages will appear here.";
      case 'explore':
      default:
        return "Be the pioneer! Create the first fan page and start building your community today.";
    }
  };

  const getSearchPlaceholder = () => {
    switch (activeTab) {
      case 'created':
        return "Search your created pages...";
      case 'liked':
        return "Search your liked pages...";
      case 'explore':
      default:
        return "Search for creators, brands, topics...";
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by useEffect with debouncing, no need to call fetchPages here
  };

  const loadMore = () => {
    if (currentPage < totalPages && !loadingMore) {
      fetchPages(currentPage + 1, true);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6 sm:space-y-8">
        {/* Section Header Skeleton */}
        <div className="text-center mb-6 sm:mb-8 animate-pulse px-4">
          <div className="h-6 sm:h-8 bg-gray-300 rounded-lg w-48 sm:w-64 mx-auto mb-4"></div>
          <div className="h-3 sm:h-4 bg-gray-300 rounded-lg w-72 sm:w-96 mx-auto"></div>
        </div>

        {/* Enhanced Search and Filter Skeleton */}
        <div className="bg-white rounded-2xl shadow-lg border p-4 sm:p-6 lg:p-8 animate-pulse">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex-1 max-w-2xl">
              <div className="h-12 sm:h-14 bg-gray-300 rounded-xl w-full"></div>
            </div>
            <div className="flex gap-4">
              <div className="h-12 bg-gray-300 rounded-xl w-40 sm:w-48"></div>
              <div className="h-12 bg-gray-300 rounded-xl w-24 sm:w-32"></div>
            </div>
          </div>
        </div>

        {/* Enhanced Pages Grid Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-white rounded-2xl shadow-lg border p-4 sm:p-6 animate-pulse">
              <div className="h-16 w-16 sm:h-20 sm:w-20 bg-gray-300 rounded-2xl mx-auto mb-4"></div>
              <div className="h-4 sm:h-5 bg-gray-300 rounded w-3/4 mx-auto mb-2"></div>
              <div className="h-3 sm:h-4 bg-gray-300 rounded w-1/2 mx-auto mb-2"></div>
              <div className="h-5 sm:h-6 bg-gray-300 rounded-full w-16 sm:w-20 mx-auto mb-4"></div>
              <div className="h-3 sm:h-4 bg-gray-300 rounded w-full mb-4"></div>
              <div className="h-8 sm:h-10 bg-gray-300 rounded-xl w-full"></div>
            </div>
          ))}
        </div>

        {/* Loading Results Summary Skeleton */}
        <div className="flex items-center justify-between text-xs sm:text-sm text-gray-600 px-4 animate-pulse">
          <div className="h-3 sm:h-4 bg-gray-300 rounded w-32 sm:w-40"></div>
          <div className="h-3 sm:h-4 bg-gray-300 rounded w-20 sm:w-24"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 sm:space-y-8" id="explore">
      {/* Section Header */}
      <div className="text-center mb-6 sm:mb-8 px-4">
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">
          {activeTab === 'explore' && 'Explore Fan Pages'}
          {activeTab === 'created' && 'My Created Pages'}
          {activeTab === 'liked' && 'My Liked Pages'}
        </h2>
        <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
          {activeTab === 'explore' && 'Browse through our diverse collection of fan pages and find your next favorite community'}
          {activeTab === 'created' && 'Manage and view all the fan pages you have created'}
          {activeTab === 'liked' && 'View all the fan pages you have liked and followed'}
        </p>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-4 sm:p-6 lg:p-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          {/* Enhanced Search */}
          <form onSubmit={handleSearch} className="flex-1 max-w-2xl">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 sm:h-6 sm:w-6 text-gray-400" />
              <Input
                type="text"
                placeholder={getSearchPlaceholder()}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 sm:pl-12 pr-4 py-3 sm:py-4 text-base sm:text-lg rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 bg-gray-50 focus:bg-white transition-all duration-200"
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery("")}
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-1 transition-all duration-200 text-sm sm:text-base"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>
          </form>

          {/* Enhanced Category Filter and Actions */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
            <div className="relative">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="appearance-none bg-white border-2 border-gray-200 rounded-xl px-3 sm:px-4 py-3 pr-8 sm:pr-10 text-sm sm:text-base text-gray-700 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 cursor-pointer hover:border-gray-300"
              >
                {categories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <ChevronDownIcon className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
              </div>
            </div>



            {/* Create Page Button */}
            {currentUserId && (
              <Link href="/pages/create" className="w-full sm:w-auto">
                <Button className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center justify-center space-x-2 text-sm sm:text-base">
                  <PlusIcon className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span>Create Page</span>
                </Button>
              </Link>
            )}
          </div>
        </div>



        {/* Quick Filter Tags */}
        {hasActiveFilters() && (
          <div className="mt-4 sm:mt-6 flex flex-wrap items-center gap-2">
            <span className="text-xs sm:text-sm text-gray-500">Active filters:</span>
            {searchQuery && (
              <span className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors duration-200">
                Search: "{searchQuery}"
                <button
                  onClick={() => setSearchQuery("")}
                  className="ml-1 sm:ml-2 text-purple-600 hover:text-purple-800 hover:bg-purple-300 rounded-full p-1 transition-colors duration-200"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {selectedCategory !== "all" && (
              <span className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors duration-200">
                Category: {categories.find(c => c.value === selectedCategory)?.label}
                <button
                  onClick={() => setSelectedCategory("all")}
                  className="ml-1 sm:ml-2 text-blue-600 hover:text-blue-800 hover:bg-blue-300 rounded-full p-1 transition-colors duration-200"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-2">
        <div className="flex space-x-1">
          <button
            onClick={() => handleTabChange('explore')}
            className={`flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${
              activeTab === 'explore'
                ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            Explore Pages
          </button>
          {currentUserId && (
            <>
              <button
                onClick={() => handleTabChange('created')}
                className={`flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${
                  activeTab === 'created'
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                Created Pages
              </button>
              <button
                onClick={() => handleTabChange('liked')}
                className={`flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${
                  activeTab === 'liked'
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                Liked Pages
              </button>
            </>
          )}
        </div>
      </div>

      {/* Enhanced Error State */}
      {error && (
        <div className="bg-red-50 border-2 border-red-200 rounded-2xl p-6 sm:p-8 text-center">
          <div className="bg-white rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
            <svg className="h-6 w-6 sm:h-8 sm:w-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg sm:text-xl font-bold text-red-900 mb-2">Oops! Something went wrong</h3>
          <p className="text-sm sm:text-base text-red-700 mb-4 sm:mb-6">{error}</p>
          <Button
            onClick={() => fetchPages()}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-sm sm:text-base"
          >
            Try Again
          </Button>
        </div>
      )}

      {/* Results Summary */}
      {!loading && (
        <div className="flex items-center justify-between text-xs sm:text-sm text-gray-600 px-4">
          <span>
            {pages.length === 0
              ? "No pages found"
              : `Showing ${pages.length} of ${totalPages * 12} pages`
            }
          </span>
          {pages.length > 0 && (
            <span>Page {currentPage} of {totalPages}</span>
          )}
        </div>
      )}

      {/* Enhanced Pages Grid */}
      {pages.length === 0 && !loading ? (
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-dashed border-gray-300 p-8 sm:p-12 text-center">
          <div className="max-w-md mx-auto">
            <div className="bg-white rounded-full w-16 h-16 sm:w-20 sm:h-20 flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-lg">
              <UsersIcon className="h-8 w-8 sm:h-10 sm:w-10 text-gray-400" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3">
              {getEmptyStateTitle()}
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed px-4">
              {getEmptyStateDescription()}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {hasActiveFilters() && (
                <Button
                  onClick={clearAllFilters}
                  variant="outline"
                  className="w-full sm:w-auto border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 text-sm sm:text-base"
                >
                  Clear Filters
                </Button>
              )}
              {currentUserId && (
                <Link
                  href={activeTab === 'liked' ? '#' : '/pages/create'}
                  className="w-full sm:w-auto"
                  onClick={activeTab === 'liked' ? (e) => { e.preventDefault(); handleTabChange('explore'); } : undefined}
                >
                  <Button className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-sm sm:text-base">
                    <PlusIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    {activeTab === 'created' ? 'Create Your First Page' :
                     activeTab === 'liked' ? 'Explore Pages' : 'Create Your Page'}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6">
            {pages.map((page) => (
              <FanPageCard key={page.id} page={page} />
            ))}
          </div>

          {/* Enhanced Load More Button */}
          {currentPage < totalPages && (
            <div className="text-center pt-6 sm:pt-8">
              <Button
                onClick={loadMore}
                disabled={loadingMore}
                variant="outline"
                className="min-w-[140px] sm:min-w-[160px] py-3 px-6 sm:px-8 rounded-xl border-2 border-gray-300 hover:border-purple-500 hover:text-purple-600 hover:bg-purple-50 hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-sm sm:text-base"
              >
                {loadingMore ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-current mr-2"></div>
                    <span className="hidden sm:inline">Loading...</span>
                    <span className="sm:hidden">Loading</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">Load More Pages</span>
                    <span className="sm:hidden">Load More</span>
                  </>
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}

interface FanPageCardProps {
  page: FanPage;
}

function FanPageCard({ page }: FanPageCardProps) {
  const [profileImageError, setProfileImageError] = useState(false);
  const [coverImageError, setCoverImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const getCategoryColor = (category: string) => {
    const colors = {
      musician: "bg-purple-100 text-purple-700",
      actor: "bg-pink-100 text-pink-700",
      brand: "bg-blue-100 text-blue-700",
      business: "bg-green-100 text-green-700",
      artist: "bg-orange-100 text-orange-700",
      athlete: "bg-red-100 text-red-700",
      default: "bg-gray-100 text-gray-700"
    };
    return colors[category as keyof typeof colors] || colors.default;
  };

  return (
    <Link href={`/pages/${page.username}`}>
      <div
        className="group bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl hover:border-purple-200 transition-all duration-300 transform hover:-translate-y-1 sm:hover:-translate-y-2 text-center relative overflow-hidden h-[320px] sm:h-[360px] flex flex-col"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Cover Photo Header */}
        <div className="relative h-24 sm:h-28 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-500 overflow-hidden rounded-t-2xl">
          {page.coverImage && !coverImageError ? (
            <>
              <OptimizedImage
                src={page.coverImage}
                alt={`${page.name} cover`}
                fill
                className="object-cover"
                onError={() => setCoverImageError(true)}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
            </>
          ) : (
            <>
              <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-500" />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
            </>
          )}

          {/* Verified Badge on Cover */}
          {page.isVerified && (
            <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full p-1.5 shadow-lg">
              <CheckBadgeIconSolid className="h-4 w-4 text-blue-500" />
            </div>
          )}
        </div>

        {/* Content */}
        <div className="relative flex flex-col h-full p-4 sm:p-5">
          {/* Profile Image - Overlapping cover */}
          <div className="relative -mt-8 sm:-mt-10 mx-auto mb-3 sm:mb-4">
            <div className="h-16 w-16 sm:h-20 sm:w-20 mx-auto rounded-2xl overflow-hidden ring-4 ring-white shadow-lg group-hover:ring-purple-200 transition-all duration-300">
              {page.profileImage && !profileImageError ? (
                <OptimizedImage
                  src={page.profileImage}
                  alt={page.name}
                  width={80}
                  height={80}
                  className="object-cover w-full h-full"
                  onError={() => setProfileImageError(true)}
                />
              ) : (
                <div className="h-full w-full bg-gradient-to-br from-purple-400 to-blue-500 flex items-center justify-center">
                  <span className="text-lg sm:text-2xl font-bold text-white">
                    {page.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Page Info */}
          <div className="mb-4">
            <h3 className="font-bold text-base sm:text-lg text-gray-900 mb-1 group-hover:text-purple-700 transition-colors duration-200">
              {page.name}
            </h3>
            <p className="text-xs sm:text-sm text-gray-600 mb-2">@{page.username}</p>

            {/* Category Badge */}
            <span className={`inline-block px-2 sm:px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 hover:scale-105 ${getCategoryColor(page.category)}`}>
              {page.category.replace('_', ' ').toUpperCase()}
            </span>
          </div>

          {/* Stats */}
          <div className="flex items-center justify-center space-x-6 text-xs sm:text-sm text-gray-600 mb-4">
            <div className="flex items-center space-x-2">
              <div className="bg-purple-100 p-1 rounded-full group-hover:bg-purple-200 transition-colors duration-200">
                <UsersIcon className="h-3 w-3 sm:h-4 sm:w-4 text-purple-600" />
              </div>
              <span className="font-medium">{page.followerCount.toLocaleString()}</span>
            </div>
          </div>

          {/* Follow Button */}
          <div className="mt-auto">
            <FollowButton
              pageId={page.id}
              pageName={page.name}
              initialIsFollowing={page.isFollowing || false}
              initialFollowerCount={page.followerCount}
              variant="card"
              className={isHovered ? 'border-purple-500 text-purple-600 bg-purple-50 hover:bg-purple-100' : ''}
            />
          </div>
        </div>
      </div>
    </Link>
  );
}
