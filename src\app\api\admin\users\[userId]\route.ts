import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, adminRoles, wallets } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";
import bcrypt from "bcryptjs";

const userUpdateSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  username: z.string().min(3).max(50).optional().nullable(),
  email: z.string().email().optional(),
  phone: z.string().optional().nullable(),
  bio: z.string().max(500).optional().nullable(),
  location: z.string().max(100).optional().nullable(),
  birthday: z.string().optional().nullable(),
  password: z.string().min(6).optional(),
  role: z.string().optional(),
  isAdmin: z.boolean().optional(),
  adminRoleId: z.string().optional().nullable(),
  emailVerified: z.string().optional().nullable(),
});

// Get a specific user
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;

    // Get the user with wallet data
    const userWithWallet = await db.select({
      // User fields
      id: users.id,
      name: users.name,
      username: users.username,
      email: users.email,
      phone: users.phone,
      role: users.role,
      isAdmin: users.isAdmin,
      adminRoleId: users.adminRoleId,
      emailVerified: users.emailVerified,
      isVerified: users.isVerified,
      image: users.image,
      coverImage: users.coverImage,
      bio: users.bio,
      location: users.location,
      birthday: users.birthday,
      // Status fields
      status: users.status,
      isActive: users.isActive,
      suspendedAt: users.suspendedAt,
      suspendedReason: users.suspendedReason,
      suspendedBy: users.suspendedBy,
      deletedAt: users.deletedAt,
      deletedReason: users.deletedReason,
      deletedBy: users.deletedBy,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt,
      // Wallet fields
      generalBalance: wallets.generalBalance,
      earningBalance: wallets.earningBalance,
      totalDeposited: wallets.totalDeposited,
      totalWithdrawn: wallets.totalWithdrawn,
      totalSent: wallets.totalSent,
      totalReceived: wallets.totalReceived,
      walletIsActive: wallets.isActive,
    }).from(users)
    .leftJoin(wallets, eq(users.id, wallets.userId))
    .where(eq(users.id, userId))
    .limit(1);

    if (!userWithWallet || userWithWallet.length === 0) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    const user = userWithWallet[0];

    // Get admin role if user is an admin
    let adminRole = null;
    if (user.isAdmin && user.adminRoleId) {
      adminRole = await db.query.adminRoles.findFirst({
        where: eq(adminRoles.id, user.adminRoleId),
      });
    }

    // Format the response
    const formattedUser = {
      id: user.id,
      name: user.name,
      username: user.username,
      email: user.email,
      phone: user.phone,
      role: user.role,
      isAdmin: user.isAdmin,
      adminRoleId: user.adminRoleId,
      adminRoleName: adminRole?.name || null,
      image: user.image,
      coverImage: user.coverImage,
      bio: user.bio,
      location: user.location,
      birthday: user.birthday,
      emailVerified: user.emailVerified,
      // Status fields
      status: user.status,
      isActive: user.isActive,
      suspendedAt: user.suspendedAt,
      suspendedReason: user.suspendedReason,
      suspendedBy: user.suspendedBy,
      deletedAt: user.deletedAt,
      deletedReason: user.deletedReason,
      deletedBy: user.deletedBy,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      // Wallet information
      wallet: {
        generalBalance: user.generalBalance || "0.00",
        earningBalance: user.earningBalance || "0.00",
        totalDeposited: user.totalDeposited || "0.00",
        totalWithdrawn: user.totalWithdrawn || "0.00",
        totalSent: user.totalSent || "0.00",
        totalReceived: user.totalReceived || "0.00",
        isActive: user.walletIsActive ?? true,
      },
      // Derived fields
      isVerified: user.emailVerified !== null,
      totalBalance: (parseFloat(user.generalBalance || "0") + parseFloat(user.earningBalance || "0")).toFixed(2),
    };

    return NextResponse.json(formattedUser, { status: 200 });
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a user
export async function PATCH(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const body = await req.json();

    // Validate the request body
    const validatedData = userUpdateSchema.parse(body);

    // Check if the user exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!existingUser) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Check if email already exists (if email is being updated)
    if (validatedData.email && validatedData.email !== existingUser.email) {
      const emailExists = await db.query.users.findFirst({
        where: and(
          eq(users.email, validatedData.email),
          eq(users.id, userId).not()
        ),
      });

      if (emailExists) {
        return NextResponse.json(
          { message: "Email already exists" },
          { status: 400 }
        );
      }
    }

    // Check if username already exists (if username is being updated)
    if (validatedData.username && validatedData.username !== existingUser.username) {
      const usernameExists = await db.query.users.findFirst({
        where: and(
          eq(users.username, validatedData.username),
          eq(users.id, userId).not()
        ),
      });

      if (usernameExists) {
        return NextResponse.json(
          { message: "Username already exists" },
          { status: 400 }
        );
      }
    }

    // Check if admin role exists if provided
    if (validatedData.adminRoleId) {
      const adminRole = await db.query.adminRoles.findFirst({
        where: eq(adminRoles.id, validatedData.adminRoleId),
      });

      if (!adminRole) {
        return NextResponse.json(
          { message: "Admin role not found" },
          { status: 400 }
        );
      }
    }

    // Update the user
    const updateData: any = {};
    if (validatedData.name) updateData.name = validatedData.name;
    if (validatedData.username !== undefined) updateData.username = validatedData.username;
    if (validatedData.email) updateData.email = validatedData.email;
    if (validatedData.phone !== undefined) updateData.phone = validatedData.phone;
    if (validatedData.bio !== undefined) updateData.bio = validatedData.bio;
    if (validatedData.location !== undefined) updateData.location = validatedData.location;
    if (validatedData.birthday !== undefined) updateData.birthday = validatedData.birthday ? new Date(validatedData.birthday) : null;
    if (validatedData.role) updateData.role = validatedData.role;
    if (validatedData.isAdmin !== undefined) updateData.isAdmin = validatedData.isAdmin;
    if (validatedData.adminRoleId !== undefined) updateData.adminRoleId = validatedData.adminRoleId;
    if (validatedData.emailVerified !== undefined) updateData.emailVerified = validatedData.emailVerified;
    updateData.updatedAt = new Date();

    // Hash the password if provided
    if (validatedData.password) {
      const hashedPassword = await bcrypt.hash(validatedData.password, 10);
      // Note: In a real app, you would store the hashed password in a password field
      // This is just a placeholder since the current schema doesn't have a password field
    }

    await db.update(users)
      .set(updateData)
      .where(eq(users.id, userId));

    return NextResponse.json(
      { message: "User updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating user:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a user
export async function DELETE(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;

    // Check if the user exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!existingUser) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Prevent deleting yourself
    if (userId === session.user.id) {
      return NextResponse.json(
        { message: "You cannot delete your own account" },
        { status: 400 }
      );
    }

    // Delete the user
    await db.delete(users)
      .where(eq(users.id, userId));

    return NextResponse.json(
      { message: "User deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
