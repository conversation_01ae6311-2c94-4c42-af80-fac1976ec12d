/**
 * Updates CSS variables based on UI settings
 * @param settings The UI settings object
 */
export function updateCssVariables(settings: Record<string, any>) {
  if (!settings || typeof window === 'undefined') return;

  const root = document.documentElement;

  // Layout settings
  if (settings.layout_width) root.style.setProperty('--layout-width', settings.layout_width);
  if (settings.sidebar_width) root.style.setProperty('--sidebar-width', settings.sidebar_width);
  if (settings.content_padding) root.style.setProperty('--content-padding', settings.content_padding);

  // Color settings
  if (settings.primary_color) root.style.setProperty('--primary-color', settings.primary_color);
  if (settings.secondary_color) root.style.setProperty('--secondary-color', settings.secondary_color);
  if (settings.background_color) root.style.setProperty('--background-color', settings.background_color);
  if (settings.text_color) root.style.setProperty('--text-color', settings.text_color);
  if (settings.link_color) root.style.setProperty('--link-color', settings.link_color);

  // Typography settings
  if (settings.base_font_size) root.style.setProperty('--base-font-size', settings.base_font_size);
  if (settings.heading_font) root.style.setProperty('--heading-font', settings.heading_font);
  if (settings.body_font) root.style.setProperty('--body-font', settings.body_font);

  // Component settings
  if (settings.button_radius) root.style.setProperty('--button-radius', settings.button_radius);
  if (settings.card_radius) root.style.setProperty('--card-radius', settings.card_radius);
  if (settings.input_radius) root.style.setProperty('--input-radius', settings.input_radius);

  // Shadow intensity
  if (settings.shadow_intensity) {
    let shadowVar = '--shadow-medium';
    switch (settings.shadow_intensity) {
      case 'none':
        shadowVar = '--shadow-none';
        break;
      case 'light':
        shadowVar = '--shadow-light';
        break;
      case 'medium':
        shadowVar = '--shadow-medium';
        break;
      case 'heavy':
        shadowVar = '--shadow-heavy';
        break;
    }
    root.style.setProperty('--shadow-default', `var(${shadowVar})`);
  }
}

/**
 * Loads UI settings from the server and applies them
 */
export async function loadAndApplyUiSettings() {
  try {
    const response = await fetch('/api/admin/settings');
    if (!response.ok) return;
    
    const data = await response.json();
    if (data && data.ui) {
      updateCssVariables(data.ui);
    }
  } catch (error) {
    console.error('Error loading UI settings:', error);
  }
}
