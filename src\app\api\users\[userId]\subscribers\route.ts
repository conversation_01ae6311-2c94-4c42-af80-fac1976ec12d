import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptions, users } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;

    // Get all users who subscribed to this user
    const subscribers = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
      })
      .from(subscriptions)
      .innerJoin(users, eq(subscriptions.subscriberId, users.id))
      .where(eq(subscriptions.targetUserId, userId))
      .orderBy(desc(subscriptions.createdAt));

    // Check if current user is subscribed back to each subscriber
    const subscribersWithStatus = await Promise.all(
      subscribers.map(async (subscriber) => {
        const isSubscribedBack = await db.query.subscriptions.findFirst({
          where: eq(subscriptions.subscriberId, userId) && eq(subscriptions.targetUserId, subscriber.id)
        });

        return {
          ...subscriber,
          isSubscribedBack: !!isSubscribedBack,
        };
      })
    );

    return NextResponse.json({
      subscribers: subscribersWithStatus,
      total: subscribersWithStatus.length
    });

  } catch (error) {
    console.error("Error fetching subscribers:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
