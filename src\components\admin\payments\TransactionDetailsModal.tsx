"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { 
  XMarkIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CreditCardIcon,
  BanknotesIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon,
  UserIcon,
  CalendarIcon,
  DocumentTextIcon
} from "@heroicons/react/24/outline";
import { formatCurrency, formatTimeAgo } from "@/lib/utils";
import Image from "next/image";

interface Transaction {
  id: string;
  userId: string;
  type: 'deposit' | 'send' | 'receive' | 'cashout' | 'internal_transfer' | 'earning' | 'withdraw';
  amount: string;
  fee: string;
  netAmount: string;
  walletType: 'general' | 'earning';
  toUserId?: string;
  toAgentId?: string;
  fromWalletType?: 'general' | 'earning';
  toWalletType?: 'general' | 'earning';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  paymentGateway?: string;
  gatewayTransactionId?: string;
  reference?: string;
  note?: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    username: string;
    email: string;
    image?: string;
  };
  toUser?: {
    id: string;
    name: string;
    username: string;
    email: string;
    image?: string;
  };
  toAgent?: {
    id: string;
    name: string;
    serviceType: string;
    phone: string;
  };
}

interface TransactionDetailsModalProps {
  transaction: Transaction;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (transactionId: string, updates: any) => void;
}

export function TransactionDetailsModal({
  transaction,
  isOpen,
  onClose,
  onUpdate,
}: TransactionDetailsModalProps) {
  const [adminNote, setAdminNote] = useState("");
  const [gatewayTransactionId, setGatewayTransactionId] = useState(
    transaction.gatewayTransactionId || ""
  );
  const [updating, setUpdating] = useState(false);

  if (!isOpen) return null;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'yellow', icon: ClockIcon },
      processing: { color: 'blue', icon: ArrowPathIcon },
      completed: { color: 'green', icon: CheckCircleIcon },
      failed: { color: 'red', icon: XCircleIcon },
      cancelled: { color: 'gray', icon: ExclamationTriangleIcon },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.color as any} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      deposit: { color: 'green', icon: ArrowTrendingDownIcon },
      send: { color: 'blue', icon: ArrowTrendingUpIcon },
      receive: { color: 'green', icon: ArrowTrendingDownIcon },
      cashout: { color: 'orange', icon: BanknotesIcon },
      internal_transfer: { color: 'purple', icon: CurrencyDollarIcon },
      earning: { color: 'yellow', icon: CreditCardIcon },
      withdraw: { color: 'red', icon: ArrowTrendingUpIcon },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.deposit;
    const Icon = config.icon;

    return (
      <Badge variant={config.color as any} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {type.replace('_', ' ').charAt(0).toUpperCase() + type.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const handleStatusUpdate = async (newStatus: string) => {
    setUpdating(true);
    try {
      const updates: any = { status: newStatus };
      
      if (adminNote.trim()) {
        updates.adminNote = adminNote.trim();
      }
      
      if (gatewayTransactionId.trim() && gatewayTransactionId !== transaction.gatewayTransactionId) {
        updates.gatewayTransactionId = gatewayTransactionId.trim();
      }

      await onUpdate(transaction.id, updates);
    } finally {
      setUpdating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Transaction Details</h2>
              <p className="text-sm text-gray-500 mt-1">
                Transaction ID: <span className="font-mono">{transaction.id}</span>
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
            >
              <XMarkIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Transaction Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Transaction Information</h3>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500">Type</span>
                    {getTypeBadge(transaction.type)}
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500">Status</span>
                    {getStatusBadge(transaction.status)}
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500">Amount</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {formatCurrency(transaction.amount)}
                    </span>
                  </div>
                  
                  {transaction.fee !== "0.00" && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-500">Fee</span>
                      <span className="text-sm text-gray-900">
                        {formatCurrency(transaction.fee)}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500">Net Amount</span>
                    <span className="text-sm font-semibold text-green-600">
                      {formatCurrency(transaction.netAmount)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500">Wallet Type</span>
                    <Badge variant="blue">
                      {transaction.walletType.charAt(0).toUpperCase() + transaction.walletType.slice(1)}
                    </Badge>
                  </div>
                  
                  {transaction.paymentGateway && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-500">Payment Gateway</span>
                      <span className="text-sm text-gray-900">{transaction.paymentGateway}</span>
                    </div>
                  )}
                  
                  {transaction.gatewayTransactionId && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-500">Gateway Transaction ID</span>
                      <span className="text-sm font-mono text-gray-900">
                        {transaction.gatewayTransactionId}
                      </span>
                    </div>
                  )}
                  
                  {transaction.reference && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-500">Reference</span>
                      <span className="text-sm font-mono text-gray-900">
                        {transaction.reference}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">User Information</h3>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {transaction.user.image ? (
                        <Image
                          className="h-12 w-12 rounded-full"
                          src={transaction.user.image}
                          alt={transaction.user.name}
                          width={48}
                          height={48}
                        />
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-gray-600" />
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{transaction.user.name}</p>
                      <p className="text-sm text-gray-500">@{transaction.user.username}</p>
                      <p className="text-sm text-gray-500">{transaction.user.email}</p>
                    </div>
                  </div>
                </div>

                {/* Recipient Information */}
                {transaction.toUser && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Recipient</h4>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          {transaction.toUser.image ? (
                            <Image
                              className="h-10 w-10 rounded-full"
                              src={transaction.toUser.image}
                              alt={transaction.toUser.name}
                              width={40}
                              height={40}
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <UserIcon className="h-5 w-5 text-gray-600" />
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{transaction.toUser.name}</p>
                          <p className="text-sm text-gray-500">@{transaction.toUser.username}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Agent Information */}
                {transaction.toAgent && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Agent</h4>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-sm font-medium text-gray-900">{transaction.toAgent.name}</p>
                      <p className="text-sm text-gray-500">{transaction.toAgent.serviceType}</p>
                      <p className="text-sm text-gray-500">{transaction.toAgent.phone}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className="border-t border-gray-200 pt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Timeline</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <CalendarIcon className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-500">Created:</span>
                <span className="text-sm text-gray-900">
                  {new Date(transaction.createdAt).toLocaleString()}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <CalendarIcon className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-500">Updated:</span>
                <span className="text-sm text-gray-900">
                  {new Date(transaction.updatedAt).toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Notes */}
          {transaction.note && (
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">User Note</h3>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-700">{transaction.note}</p>
              </div>
            </div>
          )}

          {/* Admin Note */}
          {transaction.metadata?.adminNote && (
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Admin Note</h3>
              <div className="bg-blue-50 rounded-lg p-3">
                <p className="text-sm text-blue-700">{transaction.metadata.adminNote}</p>
                {transaction.metadata.adminUpdatedAt && (
                  <p className="text-xs text-blue-500 mt-1">
                    Updated: {new Date(transaction.metadata.adminUpdatedAt).toLocaleString()}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Admin Actions */}
          {transaction.status === 'pending' && (
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Admin Actions</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Admin Note (Optional)
                  </label>
                  <textarea
                    value={adminNote}
                    onChange={(e) => setAdminNote(e.target.value)}
                    placeholder="Add a note about this transaction..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                  />
                </div>

                {transaction.type === 'deposit' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Gateway Transaction ID (Optional)
                    </label>
                    <Input
                      type="text"
                      value={gatewayTransactionId}
                      onChange={(e) => setGatewayTransactionId(e.target.value)}
                      placeholder="Enter gateway transaction ID..."
                    />
                  </div>
                )}

                <div className="flex gap-3">
                  <Button
                    onClick={() => handleStatusUpdate('completed')}
                    disabled={updating}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    {updating ? 'Updating...' : 'Approve Transaction'}
                  </Button>
                  
                  <Button
                    onClick={() => handleStatusUpdate('failed')}
                    disabled={updating}
                    variant="outline"
                    className="text-red-600 border-red-600 hover:bg-red-50"
                  >
                    <XCircleIcon className="h-4 w-4 mr-2" />
                    {updating ? 'Updating...' : 'Reject Transaction'}
                  </Button>
                  
                  <Button
                    onClick={() => handleStatusUpdate('processing')}
                    disabled={updating}
                    variant="outline"
                    className="text-blue-600 border-blue-600 hover:bg-blue-50"
                  >
                    <ArrowPathIcon className="h-4 w-4 mr-2" />
                    {updating ? 'Updating...' : 'Mark as Processing'}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Metadata */}
          {transaction.metadata && Object.keys(transaction.metadata).length > 0 && (
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Metadata</h3>
              <div className="bg-gray-50 rounded-lg p-3">
                <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                  {JSON.stringify(transaction.metadata, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
