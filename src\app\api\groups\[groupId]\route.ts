import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, posts } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and, desc, count } from "drizzle-orm";

const groupUpdateSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  description: z.string().max(1000).optional(),
  visibility: z.enum(["public", "private-visible", "private-hidden"]).optional(),
  category: z.string().max(100).optional(),
  rules: z.string().max(1000).optional(),
  coverImage: z.string().url().optional().nullable(),
  postPermission: z.enum(["all-members", "admin-only"]).optional(),
});

// Get a single group
export async function GET(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    const params = await context.params;
    const { groupId } = params;

    // Fetch the group from the database
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
      with: {
        creator: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Get member count
    const membersCount = await db
      .select({ count: count() })
      .from(groupMembers)
      .where(and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.role, "member")
      ));

    // Get user's role in the group
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id)
      ),
    });

    // Get post count
    const postsCount = await db
      .select({ count: count() })
      .from(posts)
      .where(eq(posts.groupId, groupId));

    // Check if user has access to the group
    const isPublic = group.visibility === "public";
    const isPrivateVisible = group.visibility === "private-visible";
    const isMember = userMembership && (userMembership.role === "admin" || userMembership.role === "moderator" || userMembership.role === "member");
    const isPending = userMembership && userMembership.role === "pending";
    const isCreator = group.creatorId === session.user.id;

    // If the group is private and the user is not a member, return limited info
    if (!isPublic && !isMember && !isCreator && !isPrivateVisible) {
      return NextResponse.json(
        { message: "You don't have access to this group" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      ...group,
      membersCount: membersCount[0]?.count || 0,
      postsCount: postsCount[0]?.count || 0,
      userRole: userMembership?.role || null,
      isMember,
      isPending,
      isCreator,
    });
  } catch (error) {
    console.error("Error fetching group:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a group
export async function PUT(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const body = await req.json();
    const validatedData = groupUpdateSchema.parse(body);

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user is the creator or an admin
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id)
      ),
    });

    if (!userMembership || (userMembership.role !== "admin" && group.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "You don't have permission to update this group" },
        { status: 403 }
      );
    }

    // Update the group
    await db.update(groups)
      .set({
        name: validatedData.name !== undefined ? validatedData.name : group.name,
        description: validatedData.description !== undefined ? validatedData.description : group.description,
        visibility: validatedData.visibility !== undefined ? validatedData.visibility : group.visibility,
        category: validatedData.category !== undefined ? validatedData.category : group.category,
        rules: validatedData.rules !== undefined ? validatedData.rules : group.rules,
        coverImage: validatedData.coverImage !== undefined ? validatedData.coverImage : group.coverImage,
        postPermission: validatedData.postPermission !== undefined ? validatedData.postPermission : group.postPermission,
      })
      .where(eq(groups.id, groupId));

    return NextResponse.json(
      { message: "Group updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating group:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a group
export async function DELETE(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user is the creator
    if (group.creatorId !== session.user.id) {
      return NextResponse.json(
        { message: "Only the creator can delete the group" },
        { status: 403 }
      );
    }

    // Delete the group (cascade will delete members, posts, etc.)
    await db.delete(groups).where(eq(groups.id, groupId));

    return NextResponse.json(
      { message: "Group deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting group:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
