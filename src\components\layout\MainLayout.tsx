"use client";

import { ReactNode } from "react";
import { Navbar } from "./Navbar";
import { MobileNavbar } from "./MobileNavbar";
import { MobileBottomNav } from "./MobileBottomNav";
import { OfflineIndicator } from "@/components/ui/OfflineIndicator";
import { useSession } from "next-auth/react";
import { FanPageMessageProvider } from "@/contexts/FanPageMessageContext";
import { FanPageMessageBox } from "@/components/fan-pages/FanPageMessageBox";

interface MainLayoutProps {
  children: ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const { status } = useSession();
  const isAuthenticated = status === "authenticated";

  // Online status tracking disabled - Socket.IO removed
  // TODO: Implement alternative online status system

  return (
    <FanPageMessageProvider>
      <div className="flex min-h-screen flex-col bg-gray-50">
        {isAuthenticated && (
          <>
            {/* Desktop Navbar */}
            <div className="hidden sm:block">
              <Navbar />
            </div>
            {/* Mobile Navbar */}
            <MobileNavbar />
          </>
        )}
        {isAuthenticated && <OfflineIndicator />}
        <main className="flex-1 pt-14 sm:pt-16 pb-16 sm:pb-0">{children}</main>
        {isAuthenticated && <FanPageMessageBox />}
        {isAuthenticated && <MobileBottomNav />}
      </div>
    </FanPageMessageProvider>
  );
}
