"use client";

import { useState, useEffect } from "react";
import { 
  formatTimeAgo, 
  formatPostTime, 
  formatMessageTime, 
  formatCommentTime, 
  formatNotificationTime, 
  formatDetailedTime 
} from "@/lib/utils";

interface TimeDisplayProps {
  date: Date | string;
  type?: 'post' | 'message' | 'comment' | 'notification' | 'detailed' | 'custom';
  format?: 'full' | 'short' | 'minimal';
  maxUnit?: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'year';
  showSeconds?: boolean;
  autoUpdate?: boolean;
  updateInterval?: number; // in milliseconds
  className?: string;
  title?: boolean; // Show full date on hover
}

export function TimeDisplay({
  date,
  type = 'post',
  format = 'short',
  maxUnit = 'year',
  showSeconds = false,
  autoUpdate = true,
  updateInterval = 60000, // 1 minute default
  className = "",
  title = true
}: TimeDisplayProps) {
  const [displayTime, setDisplayTime] = useState<string>("");
  const [fullDate, setFullDate] = useState<string>("");

  const formatTime = () => {
    try {
      const dateObj = typeof date === "string" ? new Date(date) : date;
      
      // Set full date for title
      setFullDate(dateObj.toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }));

      // Format based on type
      switch (type) {
        case 'post':
          return formatPostTime(dateObj);
        case 'message':
          return formatMessageTime(dateObj);
        case 'comment':
          return formatCommentTime(dateObj);
        case 'notification':
          return formatNotificationTime(dateObj);
        case 'detailed':
          return formatDetailedTime(dateObj);
        case 'custom':
        default:
          return formatTimeAgo(dateObj, { style: format, maxUnit, showSeconds });
      }
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'Invalid date';
    }
  };

  useEffect(() => {
    // Initial format
    setDisplayTime(formatTime());

    // Set up auto-update if enabled
    if (autoUpdate) {
      const interval = setInterval(() => {
        setDisplayTime(formatTime());
      }, updateInterval);

      return () => clearInterval(interval);
    }
  }, [date, type, format, maxUnit, showSeconds, autoUpdate, updateInterval]);

  return (
    <span 
      className={className}
      title={title ? fullDate : undefined}
    >
      {displayTime}
    </span>
  );
}

// Specialized components for common use cases
export function PostTime({ date, className = "", autoUpdate = true }: {
  date: Date | string;
  className?: string;
  autoUpdate?: boolean;
}) {
  return (
    <TimeDisplay
      date={date}
      type="post"
      className={className}
      autoUpdate={autoUpdate}
      updateInterval={30000} // 30 seconds for posts
    />
  );
}

export function MessageTime({ date, className = "", autoUpdate = false }: {
  date: Date | string;
  className?: string;
  autoUpdate?: boolean;
}) {
  return (
    <TimeDisplay
      date={date}
      type="message"
      className={className}
      autoUpdate={autoUpdate}
      updateInterval={60000} // 1 minute for messages
    />
  );
}

export function CommentTime({ date, className = "", autoUpdate = true }: {
  date: Date | string;
  className?: string;
  autoUpdate?: boolean;
}) {
  return (
    <TimeDisplay
      date={date}
      type="comment"
      className={className}
      autoUpdate={autoUpdate}
      updateInterval={60000} // 1 minute for comments
    />
  );
}

export function NotificationTime({ date, className = "", autoUpdate = true }: {
  date: Date | string;
  className?: string;
  autoUpdate?: boolean;
}) {
  return (
    <TimeDisplay
      date={date}
      type="notification"
      className={className}
      autoUpdate={autoUpdate}
      updateInterval={120000} // 2 minutes for notifications
    />
  );
}

export function DetailedTime({ date, className = "", autoUpdate = false }: {
  date: Date | string;
  className?: string;
  autoUpdate?: boolean;
}) {
  return (
    <TimeDisplay
      date={date}
      type="detailed"
      className={className}
      autoUpdate={autoUpdate}
    />
  );
}
