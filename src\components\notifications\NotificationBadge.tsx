"use client";

import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface NotificationBadgeProps {
  count: number;
  maxCount?: number;
  className?: string;
  animate?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'red' | 'blue' | 'green' | 'yellow' | 'purple';
}

export function NotificationBadge({ 
  count, 
  maxCount = 99, 
  className,
  animate = true,
  size = 'md',
  color = 'red'
}: NotificationBadgeProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [prevCount, setPrevCount] = useState(count);

  useEffect(() => {
    if (count > 0) {
      setIsVisible(true);
    } else {
      // Delay hiding to allow exit animation
      const timer = setTimeout(() => setIsVisible(false), 200);
      return () => clearTimeout(timer);
    }
  }, [count]);

  useEffect(() => {
    if (count > prevCount && animate) {
      // Trigger animation when count increases
      const badge = document.querySelector('.notification-badge');
      if (badge) {
        badge.classList.add('animate-bounce');
        setTimeout(() => {
          badge.classList.remove('animate-bounce');
        }, 600);
      }
    }
    setPrevCount(count);
  }, [count, prevCount, animate]);

  if (!isVisible && count === 0) return null;

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  const sizeClasses = {
    sm: 'h-4 w-4 text-xs',
    md: 'h-5 w-5 text-xs',
    lg: 'h-6 w-6 text-sm'
  };

  const colorClasses = {
    red: 'bg-red-500 text-white',
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    yellow: 'bg-yellow-500 text-black',
    purple: 'bg-purple-500 text-white'
  };

  return (
    <span
      className={cn(
        "notification-badge absolute -top-1 -right-1 flex items-center justify-center rounded-full font-bold transition-all duration-300 transform",
        sizeClasses[size],
        colorClasses[color],
        count > 0 ? "scale-100 opacity-100" : "scale-0 opacity-0",
        animate && count > prevCount && "animate-pulse",
        className
      )}
      style={{
        minWidth: size === 'sm' ? '16px' : size === 'md' ? '20px' : '24px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        border: '2px solid white'
      }}
    >
      {displayCount}
    </span>
  );
}
