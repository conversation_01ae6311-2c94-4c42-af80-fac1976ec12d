import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { transactionFilterSchema } from "@/lib/wallet/validation";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const searchParams = Object.fromEntries(url.searchParams);
    
    // Validate query parameters
    const validatedParams = transactionFilterSchema.parse(searchParams);

    const page = parseInt(validatedParams.page || "1");
    const limit = parseInt(validatedParams.limit || "20");
    const type = validatedParams.type;
    const dateFrom = validatedParams.dateFrom ? new Date(validatedParams.dateFrom) : undefined;
    const dateTo = validatedParams.dateTo ? new Date(validatedParams.dateTo) : undefined;

    // Get user transactions
    const result = await WalletService.getUserTransactions(
      session.user.id,
      page,
      limit,
      type,
      dateFrom,
      dateTo
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error("Error fetching transactions:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid query parameters",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch transactions" 
      },
      { status: 500 }
    );
  }
}
