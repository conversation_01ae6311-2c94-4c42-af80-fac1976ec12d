"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON>alogPanel, DialogTitle } from "@headlessui/react";
import {
  XMarkIcon,
  LinkIcon,
  ShareIcon,
  ChatBubbleLeftRightIcon
} from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import eventBus from "@/lib/eventBus";

interface Post {
  id: string;
  content: string;
  type: 'user_post' | 'fan_page_post';
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  } | null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  } | null;
}

interface ShareModalProps {
  post: Post;
  isOpen: boolean;
  onClose: () => void;
}

export function ShareModal({ post, isOpen, onClose }: ShareModalProps) {
  const router = useRouter();
  const [shareContent, setShareContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [shareSuccess, setShareSuccess] = useState<string | null>(null);

  // Helper function to get the post author name
  const getPostAuthorName = () => {
    if (post.type === 'fan_page_post' && post.fanPage) {
      return post.fanPage.name;
    }
    if (post.type === 'user_post' && post.user) {
      return post.user.name;
    }
    return 'Unknown User';
  };

  // Function to share directly to timeline
  const handleShareToTimeline = async () => {
    try {
      setIsSubmitting(true);
      setError(null);

      const response = await fetch(`/api/posts/${post.id}/share`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shareType: "timeline",
          privacy: "public",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.message || `Failed to share post (${response.status})`;
        throw new Error(errorMessage);
      }

      // Show success message
      setShareSuccess("Post shared to your timeline!");

      // Emit event to notify NewsFeed component about the new post
      eventBus.emit('post-created');

      // Reset form after a delay
      setTimeout(() => {
        setShareSuccess(null);
        onClose();
      }, 2000);
    } catch (error) {
      console.error("Error sharing post:", error);
      setError("Failed to share post. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to share with content
  const handleShareWithContent = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!shareContent.trim()) {
      setError("Please add some content to your share");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const response = await fetch(`/api/posts/${post.id}/share`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shareType: "withContent",
          content: shareContent,
          privacy: "public",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.message || `Failed to share post (${response.status})`;
        throw new Error(errorMessage);
      }

      // Show success message
      setShareSuccess("Post shared successfully!");

      // Emit event to notify NewsFeed component about the new post
      eventBus.emit('post-created');

      // Reset form after a delay
      setTimeout(() => {
        setShareContent("");
        setShareSuccess(null);
        onClose();
      }, 2000);
    } catch (error) {
      console.error("Error sharing post:", error);
      setError("Failed to share post. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to copy post link to clipboard
  const handleCopyLink = () => {
    // Create a URL for the post based on post type
    let postUrl: string;
    if (post.type === 'blog_post' && post.blog?.slug) {
      postUrl = `${window.location.origin}/blogs/${post.blog.slug}`;
    } else {
      postUrl = `${window.location.origin}/posts/${post.id}`;
    }

    // Copy to clipboard
    navigator.clipboard.writeText(postUrl)
      .then(() => {
        setShareSuccess("Link copied to clipboard!");
        setTimeout(() => {
          setShareSuccess(null);
        }, 2000);
      })
      .catch(err => {
        console.error("Failed to copy link:", err);
        setError("Failed to copy link. Please try again.");
      });
  };

  // Function to share on Facebook
  const handleShareFacebook = () => {
    let postUrl: string;
    if (post.type === 'blog_post' && post.blog?.slug) {
      postUrl = `${window.location.origin}/blogs/${post.blog.slug}`;
    } else {
      postUrl = `${window.location.origin}/posts/${post.id}`;
    }
    const encodedUrl = encodeURIComponent(postUrl);
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };

  // Function to share on Twitter/X
  const handleShareTwitter = () => {
    let postUrl: string;
    if (post.type === 'blog_post' && post.blog?.slug) {
      postUrl = `${window.location.origin}/blogs/${post.blog.slug}`;
    } else {
      postUrl = `${window.location.origin}/posts/${post.id}`;
    }
    const encodedUrl = encodeURIComponent(postUrl);
    const contentType = post.type === 'blog_post' ? 'blog post' : 'post';
    const text = encodeURIComponent(`Check out this ${contentType} from ${getPostAuthorName()} on hifnf!`);
    const shareUrl = `https://twitter.com/intent/tweet?text=${text}&url=${encodedUrl}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };

  // Function to share on WhatsApp
  const handleShareWhatsApp = () => {
    let postUrl: string;
    if (post.type === 'blog_post' && post.blog?.slug) {
      postUrl = `${window.location.origin}/blogs/${post.blog.slug}`;
    } else {
      postUrl = `${window.location.origin}/posts/${post.id}`;
    }
    const encodedUrl = encodeURIComponent(postUrl);
    const contentType = post.type === 'blog_post' ? 'blog post' : 'post';
    const text = encodeURIComponent(`Check out this ${contentType} from ${getPostAuthorName()} on hifnf: `);
    const shareUrl = `https://wa.me/?text=${text}${encodedUrl}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-medium">
              Share Post
            </DialogTitle>
            <button
              onClick={onClose}
              className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Error message */}
          {error && (
            <div className="mt-3 rounded-md bg-red-50 p-3 text-sm text-red-500">
              {error}
            </div>
          )}

          {/* Success message */}
          {shareSuccess && (
            <div className="mt-3 rounded-md bg-green-50 p-3 text-sm text-green-500">
              {shareSuccess}
            </div>
          )}

          <div className="mt-4 space-y-4">
            {/* Share to timeline option */}
            <button
              onClick={handleShareToTimeline}
              disabled={isSubmitting}
              className="flex w-full items-center rounded-md border border-gray-300 px-4 py-2 text-left text-sm hover:bg-gray-50"
            >
              <ShareIcon className="mr-3 h-5 w-5 text-gray-500" />
              <span>Share now (on your timeline)</span>
            </button>

            {/* Share with content option */}
            <div className="rounded-md border border-gray-300 p-4">
              <form onSubmit={handleShareWithContent}>
                <textarea
                  value={shareContent}
                  onChange={(e) => setShareContent(e.target.value)}
                  placeholder={`Write something about this post from ${getPostAuthorName()}...`}
                  className="w-full resize-none rounded-md border-gray-300 bg-gray-50 p-2 text-sm focus:border-blue-500 focus:ring-blue-500"
                  rows={3}
                ></textarea>
                <div className="mt-2 flex justify-end">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-blue-600 text-white hover:bg-blue-700"
                  >
                    {isSubmitting ? "Sharing..." : "Share"}
                  </Button>
                </div>
              </form>
            </div>

            {/* Copy link option */}
            <button
              onClick={handleCopyLink}
              className="flex w-full items-center rounded-md border border-gray-300 px-4 py-2 text-left text-sm hover:bg-gray-50"
            >
              <LinkIcon className="mr-3 h-5 w-5 text-gray-500" />
              <span>Copy link</span>
            </button>

            {/* Social media sharing section */}
            <div className="mt-4">
              <h3 className="mb-2 text-sm font-medium text-gray-700">Share to social media</h3>
              <div className="grid grid-cols-3 gap-2">
                {/* Facebook */}
                <button
                  onClick={handleShareFacebook}
                  className="flex flex-col items-center justify-center rounded-md border border-gray-300 p-3 hover:bg-blue-50"
                >
                  <svg className="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                  </svg>
                  <span className="mt-1 text-xs">Facebook</span>
                </button>

                {/* Twitter/X */}
                <button
                  onClick={handleShareTwitter}
                  className="flex flex-col items-center justify-center rounded-md border border-gray-300 p-3 hover:bg-blue-50"
                >
                  <svg className="h-6 w-6 text-black" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                  <span className="mt-1 text-xs">Twitter/X</span>
                </button>

                {/* WhatsApp */}
                <button
                  onClick={handleShareWhatsApp}
                  className="flex flex-col items-center justify-center rounded-md border border-gray-300 p-3 hover:bg-green-50"
                >
                  <svg className="h-6 w-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z" />
                  </svg>
                  <span className="mt-1 text-xs">WhatsApp</span>
                </button>
              </div>
            </div>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
