"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { toast } from "react-hot-toast";
import {
  PencilIcon,
  TrashIcon,
  PlusIcon,
  XMarkIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";

interface Role {
  id: string;
  name: string;
  description: string | null;
  isSystem: boolean;
  createdAt: string;
  permissions: string[];
}

interface Permission {
  id: string;
  name: string;
  code: string;
  description: string | null;
  module: string;
  createdAt: string;
}

export default function AdminRolesPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deletingRoleId, setDeletingRoleId] = useState<string | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
  });

  // Fetch roles and permissions from the API
  const fetchRolesAndPermissions = async () => {
    setIsLoading(true);
    try {
      // Fetch roles
      const rolesResponse = await fetch('/api/admin/roles');
      if (!rolesResponse.ok) {
        throw new Error('Failed to fetch roles');
      }
      const rolesData = await rolesResponse.json();

      // Fetch permissions
      const permissionsResponse = await fetch('/api/admin/permissions');
      if (!permissionsResponse.ok) {
        throw new Error('Failed to fetch permissions');
      }
      const permissionsData = await permissionsResponse.json();

      setRoles(rolesData);
      setPermissions(permissionsData);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load roles and permissions');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRolesAndPermissions();
  }, []);

  const handleCreateRole = async () => {
    if (!newRole.name.trim()) {
      toast.error('Role name is required');
      return;
    }

    setIsSubmitting(true);
    try {
      // Call the API to create the role
      const response = await fetch('/api/admin/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newRole.name,
          description: newRole.description,
          permissions: newRole.permissions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create role');
      }

      // Refresh the roles list
      await fetchRolesAndPermissions();

      // Reset form and close modal
      setNewRole({
        name: "",
        description: "",
        permissions: [],
      });
      setIsCreateModalOpen(false);

      toast.success('Role created successfully');
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create role');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditRole = async () => {
    if (!selectedRole) return;

    if (!selectedRole.name.trim()) {
      toast.error('Role name is required');
      return;
    }

    setIsSubmitting(true);
    try {
      // Call the API to update the role
      const response = await fetch(`/api/admin/roles/${selectedRole.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: selectedRole.name,
          description: selectedRole.description,
          permissions: selectedRole.permissions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update role');
      }

      // Refresh the roles list
      await fetchRolesAndPermissions();
      setIsEditModalOpen(false);
      toast.success('Role updated successfully');
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update role');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    const roleToDelete = roles.find((role) => role.id === roleId);

    if (roleToDelete?.isSystem) {
      toast.error("System roles cannot be deleted.");
      return;
    }

    if (window.confirm("Are you sure you want to delete this role?")) {
      setDeletingRoleId(roleId);
      try {
        // Call the API to delete the role
        const response = await fetch(`/api/admin/roles/${roleId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to delete role');
        }

        // Refresh the roles list
        await fetchRolesAndPermissions();
        toast.success('Role deleted successfully');
      } catch (error) {
        console.error('Error deleting role:', error);
        toast.error(error instanceof Error ? error.message : 'Failed to delete role');
      } finally {
        setDeletingRoleId(null);
      }
    }
  };

  const handleTogglePermission = (permissionCode: string, isEdit = false) => {
    if (isEdit) {
      if (!selectedRole) return;

      const updatedPermissions = selectedRole.permissions.includes(permissionCode)
        ? selectedRole.permissions.filter((p: string) => p !== permissionCode)
        : [...selectedRole.permissions, permissionCode];

      setSelectedRole({
        ...selectedRole,
        permissions: updatedPermissions,
      });
    } else {
      const updatedPermissions = newRole.permissions.includes(permissionCode)
        ? newRole.permissions.filter((p) => p !== permissionCode)
        : [...newRole.permissions, permissionCode];

      setNewRole({
        ...newRole,
        permissions: updatedPermissions,
      });
    }
  };

  const groupedPermissions = permissions.reduce((acc, permission) => {
    if (!acc[permission.module]) {
      acc[permission.module] = [];
    }
    acc[permission.module].push(permission);
    return acc;
  }, {} as Record<string, any[]>);

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Roles</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage admin roles and permissions
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <PlusIcon className="mr-2 h-5 w-5" />
            Create Role
          </Button>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Role Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Description
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Created
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {roles.map((role) => (
                <tr key={role.id}>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {role.name}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {role.description}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    <span
                      className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                        role.isSystem
                          ? "bg-purple-100 text-purple-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {role.isSystem ? "System" : "Custom"}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {new Date(role.createdAt).toLocaleDateString()}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => {
                          setSelectedRole(role);
                          setIsEditModalOpen(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        disabled={role.isSystem}
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteRole(role.id)}
                        className="text-red-600 hover:text-red-900"
                        disabled={role.isSystem || deletingRoleId === role.id}
                      >
                        {deletingRoleId === role.id ? (
                          <Spinner size="sm" className="h-5 w-5" />
                        ) : (
                          <TrashIcon className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Role Modal */}
      <Dialog
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <DialogPanel className="mx-auto max-w-xl rounded-lg bg-white p-6 shadow-xl">
            <DialogTitle as="h3" className="text-lg font-medium text-gray-900">
              Create New Role
            </DialogTitle>
            <button
              type="button"
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
              onClick={() => setIsCreateModalOpen(false)}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>

            <div className="mt-4 space-y-4">
              <div>
                <label
                  htmlFor="role-name"
                  className="block text-sm font-medium text-gray-700"
                >
                  Role Name
                </label>
                <Input
                  type="text"
                  id="role-name"
                  value={newRole.name}
                  onChange={(e) =>
                    setNewRole({ ...newRole, name: e.target.value })
                  }
                  className="mt-1"
                />
              </div>

              <div>
                <label
                  htmlFor="role-description"
                  className="block text-sm font-medium text-gray-700"
                >
                  Description
                </label>
                <textarea
                  id="role-description"
                  rows={3}
                  value={newRole.description}
                  onChange={(e) =>
                    setNewRole({ ...newRole, description: e.target.value })
                  }
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Permissions
                </label>
                <div className="mt-2 max-h-60 overflow-y-auto rounded-md border border-gray-300 p-2">
                  {Object.entries(groupedPermissions).map(([module, modulePermissions]) => (
                    <div key={module} className="mb-4">
                      <h4 className="mb-2 text-sm font-medium capitalize text-gray-700">
                        {module}
                      </h4>
                      <div className="space-y-2">
                        {modulePermissions.map((permission) => (
                          <div
                            key={permission.id}
                            className="flex items-center"
                          >
                            <input
                              id={`permission-${permission.id}`}
                              type="checkbox"
                              checked={newRole.permissions.includes(permission.code)}
                              onChange={() => handleTogglePermission(permission.code)}
                              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <label
                              htmlFor={`permission-${permission.id}`}
                              className="ml-2 block text-sm text-gray-900"
                            >
                              {permission.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setIsCreateModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateRole}
                  disabled={!newRole.name || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Creating...
                    </>
                  ) : (
                    'Create Role'
                  )}
                </Button>
              </div>
            </div>
          </DialogPanel>
        </div>
      </Dialog>

      {/* Edit Role Modal */}
      {selectedRole && (
        <Dialog
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          className="relative z-50"
        >
          <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
          <div className="fixed inset-0 flex items-center justify-center p-4">
            <DialogPanel className="mx-auto max-w-xl rounded-lg bg-white p-6 shadow-xl">
              <DialogTitle as="h3" className="text-lg font-medium text-gray-900">
                Edit Role: {selectedRole.name}
              </DialogTitle>
              <button
                type="button"
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
                onClick={() => setIsEditModalOpen(false)}
              >
                <XMarkIcon className="h-6 w-6" />
              </button>

              <div className="mt-4 space-y-4">
                <div>
                  <label
                    htmlFor="edit-role-name"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Role Name
                  </label>
                  <Input
                    type="text"
                    id="edit-role-name"
                    value={selectedRole.name}
                    onChange={(e) =>
                      setSelectedRole({
                        ...selectedRole,
                        name: e.target.value,
                      })
                    }
                    disabled={selectedRole.isSystem}
                    className="mt-1"
                  />
                </div>

                <div>
                  <label
                    htmlFor="edit-role-description"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Description
                  </label>
                  <textarea
                    id="edit-role-description"
                    rows={3}
                    value={selectedRole.description || ""}
                    onChange={(e) =>
                      setSelectedRole({
                        ...selectedRole,
                        description: e.target.value,
                      })
                    }
                    disabled={selectedRole.isSystem}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Permissions
                  </label>
                  <div className="mt-2 max-h-60 overflow-y-auto rounded-md border border-gray-300 p-2">
                    {Object.entries(groupedPermissions).map(([module, modulePermissions]) => (
                      <div key={module} className="mb-4">
                        <h4 className="mb-2 text-sm font-medium capitalize text-gray-700">
                          {module}
                        </h4>
                        <div className="space-y-2">
                          {modulePermissions.map((permission) => (
                            <div
                              key={permission.id}
                              className="flex items-center"
                            >
                              <input
                                id={`edit-permission-${permission.id}`}
                                type="checkbox"
                                checked={
                                  selectedRole.permissions.includes("all") ||
                                  selectedRole.permissions.includes(permission.code)
                                }
                                onChange={() => handleTogglePermission(permission.code, true)}
                                disabled={selectedRole.isSystem || selectedRole.permissions.includes("all")}
                                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <label
                                htmlFor={`edit-permission-${permission.id}`}
                                className="ml-2 block text-sm text-gray-900"
                              >
                                {permission.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsEditModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleEditRole}
                    disabled={selectedRole.isSystem || !selectedRole.name || isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                </div>
              </div>
            </DialogPanel>
          </div>
        </Dialog>
      )}
    </AdminLayout>
  );
}
