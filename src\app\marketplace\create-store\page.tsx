import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { CreateStoreForm } from "@/components/marketplace/CreateStoreForm";
import { db } from "@/lib/db";
import { stores } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { redirect } from "next/navigation";

export default async function CreateStorePage() {
  const user = await requireAuth();
  
  // Check if user already has a store
  const existingStore = await db
    .select({ id: stores.id, slug: stores.slug })
    .from(stores)
    .where(eq(stores.ownerId, user.id))
    .limit(1);
  
  // If user already has a store, redirect to their store dashboard
  if (existingStore.length > 0) {
    redirect(`/my-store/dashboard`);
  }

  return (
    <MainLayout>
      <div className="mx-auto max-w-3xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
            Create Your Store
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Set up your store to start selling products in the marketplace
          </p>
        </div>
        
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-6">
            <CreateStoreForm />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
