"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import {
  UserGroupIcon,
  UserPlusIcon,
  UsersIcon
} from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { FollowingList } from "./FollowingList";
import { FollowersList } from "./FollowersList";
import { PeopleSuggestionsPreview } from "../friends/PeopleSuggestionsPreview";
import { cn } from "@/lib/utils";

type TabType = 'subscribed' | 'subscribers' | 'suggestions';

export function ConnectionContent() {
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [activeTab, setActiveTab] = useState<TabType>('subscribed');
  const [counts, setCounts] = useState({
    subscribed: 0,
    subscribers: 0
  });
  const [countsLoading, setCountsLoading] = useState(true);

  useEffect(() => {
    const tab = searchParams?.get('tab') as TabType;
    if (tab && ['subscribed', 'subscribers', 'suggestions'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  useEffect(() => {
    if (session?.user) {
      fetchCounts();
    }
  }, [session]);

  const fetchCounts = async () => {
    try {
      setCountsLoading(true);

      // Fetch subscribed count
      const subscribedResponse = await fetch('/api/subscriptions?type=subscriptions&limit=1');
      if (subscribedResponse.ok) {
        const subscribedData = await subscribedResponse.json();
        setCounts(prev => ({ ...prev, subscribed: subscribedData.pagination?.total || 0 }));
      }

      // Fetch subscribers count
      const subscribersResponse = await fetch('/api/subscriptions?type=subscribers&limit=1');
      if (subscribersResponse.ok) {
        const subscribersData = await subscribersResponse.json();
        setCounts(prev => ({ ...prev, subscribers: subscribersData.pagination?.total || 0 }));
      }
    } catch (error) {
      console.error('Error fetching counts:', error);
    } finally {
      setCountsLoading(false);
    }
  };

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('tab', tab);
    router.push(`/connection?${params.toString()}`);
  };



  const updateCounts = () => {
    fetchCounts();
  };

  if (!session?.user) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Please sign in</h3>
        <p className="text-gray-600">You need to be signed in to view connections.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Premium Header Card */}
      <div className="relative bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-500 rounded-2xl shadow-2xl overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

        <div className="relative p-6 sm:p-8">
          <div className="flex flex-col">
            {/* Header Content */}
            <div className="text-left">
              <div className="flex items-center justify-start gap-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg">
                  <UserGroupIcon className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl sm:text-4xl font-bold text-white mb-1">Connections</h1>
                  <div className="flex items-center gap-2">
                    <div className="h-1 w-12 bg-white/40 rounded-full"></div>
                    <span className="text-white/80 text-sm font-medium">Network Hub</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Premium Tab Navigation */}
        <div className="relative mt-6 sm:mt-8">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-2">
            <nav className="flex space-x-1">
              <button
                onClick={() => handleTabChange('subscribed')}
                className={cn(
                  "flex-1 py-4 px-6 rounded-xl font-semibold text-sm transition-all duration-300 flex items-center justify-center gap-3 relative overflow-hidden",
                  activeTab === 'subscribed'
                    ? "bg-gradient-to-r from-emerald-500 to-green-500 text-white shadow-lg transform scale-105"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                )}
              >
                {activeTab === 'subscribed' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-green-400 opacity-20 animate-pulse"></div>
                )}
                <UserPlusIcon className="h-5 w-5 relative z-10" />
                <span className="hidden sm:inline relative z-10">Subscribed</span>
                <span className="sm:hidden relative z-10">Subscribed</span>
                {activeTab === 'subscribed' && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-bounce"></div>
                )}
              </button>

              <button
                onClick={() => handleTabChange('subscribers')}
                className={cn(
                  "flex-1 py-4 px-6 rounded-xl font-semibold text-sm transition-all duration-300 flex items-center justify-center gap-3 relative overflow-hidden",
                  activeTab === 'subscribers'
                    ? "bg-gradient-to-r from-purple-500 to-violet-500 text-white shadow-lg transform scale-105"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                )}
              >
                {activeTab === 'subscribers' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-violet-400 opacity-20 animate-pulse"></div>
                )}
                <UsersIcon className="h-5 w-5 relative z-10" />
                <span className="hidden sm:inline relative z-10">Subscribers</span>
                <span className="sm:hidden relative z-10">Subscribers</span>
                {activeTab === 'subscribers' && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-bounce"></div>
                )}
              </button>

              <button
                onClick={() => handleTabChange('suggestions')}
                className={cn(
                  "flex-1 py-4 px-6 rounded-xl font-semibold text-sm transition-all duration-300 flex items-center justify-center gap-3 relative overflow-hidden",
                  activeTab === 'suggestions'
                    ? "bg-gradient-to-r from-orange-500 to-amber-500 text-white shadow-lg transform scale-105"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                )}
              >
                {activeTab === 'suggestions' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-amber-400 opacity-20 animate-pulse"></div>
                )}
                <UserGroupIcon className="h-5 w-5 relative z-10" />
                <span className="hidden sm:inline relative z-10">Discover</span>
                <span className="sm:hidden relative z-10">Find</span>
                {activeTab === 'suggestions' && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-bounce"></div>
                )}
              </button>
            </nav>
          </div>
        </div>
      </div>

      {/* Premium Stats Dashboard */}
      <div className="grid grid-cols-2 gap-3 sm:gap-6">
        <button
          onClick={() => handleTabChange('subscribed')}
          className="group relative bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-emerald-100 hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer text-left w-full"
        >
          <div className="absolute top-0 right-0 w-16 h-16 sm:w-20 sm:h-20 bg-emerald-200/30 rounded-full -translate-y-8 translate-x-8 sm:-translate-y-10 sm:translate-x-10"></div>
          <div className="relative">
            <div className="flex items-center justify-between mb-2 sm:mb-4">
              <div className="p-2 sm:p-3 bg-emerald-100 rounded-lg sm:rounded-xl group-hover:scale-110 transition-transform duration-300">
                <UserPlusIcon className="h-4 w-4 sm:h-6 sm:w-6 text-emerald-600" />
              </div>
              <div className="text-right">
                <div className="text-xl sm:text-3xl font-bold text-emerald-600 group-hover:scale-110 transition-transform duration-300">
                  {countsLoading ? (
                    <div className="h-6 w-8 sm:h-8 sm:w-12 bg-emerald-200 rounded animate-pulse"></div>
                  ) : (
                    counts.subscribed
                  )}
                </div>
                <div className="text-xs sm:text-sm text-emerald-500 font-medium">Subscribed</div>
              </div>
            </div>
            <div className="text-xs sm:text-sm text-gray-600">People you subscribe to</div>
            <div className="mt-1 sm:mt-2 h-1 bg-emerald-200 rounded-full overflow-hidden">
              <div className="h-full bg-emerald-500 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
            </div>
          </div>
        </button>

        <button
          onClick={() => handleTabChange('subscribers')}
          className="group relative bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-purple-100 hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer text-left w-full"
        >
          <div className="absolute top-0 right-0 w-16 h-16 sm:w-20 sm:h-20 bg-purple-200/30 rounded-full -translate-y-8 translate-x-8 sm:-translate-y-10 sm:translate-x-10"></div>
          <div className="relative">
            <div className="flex items-center justify-between mb-2 sm:mb-4">
              <div className="p-2 sm:p-3 bg-purple-100 rounded-lg sm:rounded-xl group-hover:scale-110 transition-transform duration-300">
                <UsersIcon className="h-4 w-4 sm:h-6 sm:w-6 text-purple-600" />
              </div>
              <div className="text-right">
                <div className="text-xl sm:text-3xl font-bold text-purple-600 group-hover:scale-110 transition-transform duration-300">
                  {countsLoading ? (
                    <div className="h-6 w-8 sm:h-8 sm:w-12 bg-purple-200 rounded animate-pulse"></div>
                  ) : (
                    counts.subscribers
                  )}
                </div>
                <div className="text-xs sm:text-sm text-purple-500 font-medium">Subscribers</div>
              </div>
            </div>
            <div className="text-xs sm:text-sm text-gray-600">People who subscribe to you</div>
            <div className="mt-1 sm:mt-2 h-1 bg-purple-200 rounded-full overflow-hidden">
              <div className="h-full bg-purple-500 rounded-full w-0 group-hover:w-full transition-all duration-1000 delay-200"></div>
            </div>
          </div>
        </button>


      </div>

      {/* Enhanced Content Cards */}
      <div className="space-y-8">
        {activeTab === 'subscribed' && (
          <div className="relative bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500 to-green-500"></div>
            <div className="bg-gradient-to-r from-emerald-50 to-green-50 p-6 border-b border-emerald-100">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-emerald-100 rounded-xl">
                  <UserPlusIcon className="h-6 w-6 text-emerald-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">People You Subscribe To</h2>
                  <p className="text-emerald-600 font-medium mt-1">
                    Stay connected with your subscriptions
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <FollowingList onCountChange={updateCounts} />
            </div>
          </div>
        )}

        {activeTab === 'subscribers' && (
          <div className="relative bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-violet-500"></div>
            <div className="bg-gradient-to-r from-purple-50 to-violet-50 p-6 border-b border-purple-100">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-100 rounded-xl">
                  <UsersIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Your Subscribers</h2>
                  <p className="text-purple-600 font-medium mt-1">
                    People who are interested in your content
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <FollowersList onCountChange={updateCounts} />
            </div>
          </div>
        )}

        {activeTab === 'suggestions' && (
          <div className="relative bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-amber-500"></div>
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 border-b border-orange-100">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-orange-100 rounded-xl">
                  <UserGroupIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Discover New People</h2>
                  <p className="text-orange-600 font-medium mt-1">
                    Find interesting people based on your interests
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <PeopleSuggestionsPreview showAll={true} onCountChange={updateCounts} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
