import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptionTransactions, subscriptionPlans, users } from "@/lib/db/schema";
import { eq, and, or, like, desc, asc, gte, lte, count } from "drizzle-orm";
import { z } from "zod";

const querySchema = z.object({
  search: z.string().optional(),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']).optional(),
  type: z.enum(['payment', 'refund', 'upgrade', 'downgrade', 'cancellation']).optional(),
  gateway: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  page: z.string().transform(val => parseInt(val) || 1).default('1'),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).default('20'),
  export: z.string().optional(),
});

// GET - Fetch payment transactions with filtering and pagination
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    // Build where conditions
    const whereConditions = [];

    if (query.status) {
      whereConditions.push(eq(subscriptionTransactions.status, query.status));
    }

    if (query.type) {
      whereConditions.push(eq(subscriptionTransactions.type, query.type));
    }

    if (query.gateway) {
      whereConditions.push(eq(subscriptionTransactions.paymentGateway, query.gateway));
    }

    if (query.dateFrom) {
      whereConditions.push(gte(subscriptionTransactions.createdAt, new Date(query.dateFrom)));
    }

    if (query.dateTo) {
      const dateTo = new Date(query.dateTo);
      dateTo.setHours(23, 59, 59, 999); // End of day
      whereConditions.push(lte(subscriptionTransactions.createdAt, dateTo));
    }

    // Build search conditions for user name/email
    let searchConditions = [];
    if (query.search) {
      searchConditions = [
        like(users.name, `%${query.search}%`),
        like(users.email, `%${query.search}%`),
        like(subscriptionTransactions.id, `%${query.search}%`),
        like(subscriptionTransactions.gatewayTransactionId, `%${query.search}%`),
      ];
    }

    // Get total count for pagination
    const totalCountQuery = db
      .select({ count: count() })
      .from(subscriptionTransactions)
      .innerJoin(users, eq(subscriptionTransactions.userId, users.id))
      .innerJoin(subscriptionPlans, eq(subscriptionTransactions.planId, subscriptionPlans.id));

    if (whereConditions.length > 0) {
      totalCountQuery.where(and(...whereConditions));
    }

    if (searchConditions.length > 0) {
      if (whereConditions.length > 0) {
        totalCountQuery.where(and(...whereConditions, or(...searchConditions)));
      } else {
        totalCountQuery.where(or(...searchConditions));
      }
    }

    const [{ count: totalCount }] = await totalCountQuery;

    // Get transactions with user and plan data
    let transactionsQuery = db
      .select({
        id: subscriptionTransactions.id,
        subscriptionId: subscriptionTransactions.subscriptionId,
        userId: subscriptionTransactions.userId,
        planId: subscriptionTransactions.planId,
        type: subscriptionTransactions.type,
        amount: subscriptionTransactions.amount,
        currency: subscriptionTransactions.currency,
        status: subscriptionTransactions.status,
        paymentGateway: subscriptionTransactions.paymentGateway,
        gatewayTransactionId: subscriptionTransactions.gatewayTransactionId,
        description: subscriptionTransactions.description,
        processedAt: subscriptionTransactions.processedAt,
        createdAt: subscriptionTransactions.createdAt,
        user: {
          name: users.name,
          email: users.email,
        },
        plan: {
          displayName: subscriptionPlans.displayName,
        },
      })
      .from(subscriptionTransactions)
      .innerJoin(users, eq(subscriptionTransactions.userId, users.id))
      .innerJoin(subscriptionPlans, eq(subscriptionTransactions.planId, subscriptionPlans.id))
      .orderBy(desc(subscriptionTransactions.createdAt));

    // Apply filters
    if (whereConditions.length > 0 && searchConditions.length > 0) {
      transactionsQuery = transactionsQuery.where(and(...whereConditions, or(...searchConditions)));
    } else if (whereConditions.length > 0) {
      transactionsQuery = transactionsQuery.where(and(...whereConditions));
    } else if (searchConditions.length > 0) {
      transactionsQuery = transactionsQuery.where(or(...searchConditions));
    }

    // Handle export
    if (query.export === 'true') {
      const allTransactions = await transactionsQuery;
      
      // Generate CSV
      const csvHeaders = [
        'Transaction ID',
        'User Name',
        'User Email',
        'Plan',
        'Type',
        'Amount',
        'Currency',
        'Status',
        'Gateway',
        'Gateway Transaction ID',
        'Description',
        'Created At',
        'Processed At',
      ];

      const csvRows = allTransactions.map(transaction => [
        transaction.id,
        transaction.user.name,
        transaction.user.email,
        transaction.plan.displayName,
        transaction.type,
        transaction.amount,
        transaction.currency,
        transaction.status,
        transaction.paymentGateway || '',
        transaction.gatewayTransactionId || '',
        transaction.description || '',
        transaction.createdAt.toISOString(),
        transaction.processedAt?.toISOString() || '',
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="transactions-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }

    // Apply pagination for regular requests
    transactionsQuery = transactionsQuery
      .limit(query.limit)
      .offset((query.page - 1) * query.limit);

    const transactions = await transactionsQuery;

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / query.limit);
    const hasNextPage = query.page < totalPages;
    const hasPrevPage = query.page > 1;

    return NextResponse.json({
      success: true,
      transactions,
      pagination: {
        currentPage: query.page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit: query.limit,
      },
    });
  } catch (error) {
    console.error("Error fetching payment transactions:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid query parameters",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch payment transactions",
      },
      { status: 500 }
    );
  }
}
