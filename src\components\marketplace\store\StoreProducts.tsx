"use client";

import Link from "next/link";
import { ShoppingBagIcon } from "@heroicons/react/24/outline";
import { ProductCard } from "@/components/marketplace/ProductCard";

interface StoreProductsProps {
  storeId: string;
  storeName: string;
  storeLogo: string | null;
  products: {
    id: string;
    title: string;
    price: number;
    condition: string;
    category: string;
    photos: string[] | null;
    createdAt: Date;
  }[];
  totalProductCount: number;
}

export function StoreProducts({ 
  storeId, 
  storeName, 
  storeLogo, 
  products, 
  totalProductCount 
}: StoreProductsProps) {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Products</h2>
        {products.length > 0 && totalProductCount > products.length && (
          <Link
            href={`/marketplace?storeId=${storeId}`}
            className="text-sm font-medium text-blue-600 hover:text-blue-800"
          >
            View all products
          </Link>
        )}
      </div>

      {products.length > 0 ? (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              product={{
                ...product,
                store: {
                  id: storeId,
                  name: storeName,
                  logo: storeLogo
                }
              }}
            />
          ))}
        </div>
      ) : (
        <div className="rounded-lg bg-white p-8 text-center">
          <ShoppingBagIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
          <p className="mt-1 text-sm text-gray-500">
            This store hasn't listed any products yet.
          </p>
        </div>
      )}
    </div>
  );
}
