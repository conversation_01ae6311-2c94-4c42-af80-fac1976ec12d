"use client";

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';

interface UseVirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
  enabled?: boolean;
}

interface UseVirtualScrollReturn<T> {
  virtualItems: Array<{
    index: number;
    start: number;
    end: number;
    item: T;
  }>;
  totalHeight: number;
  scrollElementRef: React.RefObject<HTMLDivElement>;
  scrollToIndex: (index: number) => void;
  scrollToTop: () => void;
}

export function useVirtualScroll<T = any>(
  items: T[],
  options: UseVirtualScrollOptions
): UseVirtualScrollReturn<T> {
  const {
    itemHeight,
    containerHeight,
    overscan = 5,
    enabled = true,
  } = options;

  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    if (!enabled || items.length === 0) {
      return {
        start: 0,
        end: items.length,
      };
    }

    const start = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(items.length, start + visibleCount + overscan * 2);

    return { start, end };
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length, enabled]);

  // Calculate virtual items
  const virtualItems = useMemo(() => {
    const { start, end } = visibleRange;
    
    return items.slice(start, end).map((item, index) => {
      const actualIndex = start + index;
      return {
        index: actualIndex,
        start: actualIndex * itemHeight,
        end: (actualIndex + 1) * itemHeight,
        item,
      };
    });
  }, [items, visibleRange, itemHeight]);

  // Total height for scrollbar
  const totalHeight = items.length * itemHeight;

  // Handle scroll
  const handleScroll = useCallback((e: Event) => {
    const target = e.target as HTMLDivElement;
    setScrollTop(target.scrollTop);
  }, []);

  // Setup scroll listener
  useEffect(() => {
    const element = scrollElementRef.current;
    if (!element || !enabled) return;

    element.addEventListener('scroll', handleScroll, { passive: true });
    return () => element.removeEventListener('scroll', handleScroll);
  }, [handleScroll, enabled]);

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number) => {
    const element = scrollElementRef.current;
    if (!element) return;

    const scrollTop = index * itemHeight;
    element.scrollTo({ top: scrollTop, behavior: 'smooth' });
  }, [itemHeight]);

  // Scroll to top
  const scrollToTop = useCallback(() => {
    const element = scrollElementRef.current;
    if (!element) return;

    element.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return {
    virtualItems,
    totalHeight,
    scrollElementRef,
    scrollToIndex,
    scrollToTop,
  };
}
