import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageSettings, fanPageFollowers, fanPageRoles, users } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";

const updateFanPageSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  category: z.enum([
    'musician', 'actor', 'brand', 'business', 'organization',
    'public_figure', 'artist', 'writer', 'athlete', 'politician',
    'entertainment', 'media', 'community', 'cause', 'other'
  ]).optional(),
  description: z.string().max(1000).optional(),
  website: z.union([z.string().url(), z.literal("")]).optional(),
  email: z.union([z.string().email(), z.literal("")]).optional(),
  phone: z.string().max(50).optional(),
  location: z.string().max(255).optional(),
  profileImage: z.string().optional(),
  coverImage: z.string().optional(),
});

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId] - Get fan page details
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    // Get fan page with owner info
    const pageResult = await db
      .select({
        id: fanPages.id,
        ownerId: fanPages.ownerId,
        name: fanPages.name,
        username: fanPages.username,
        category: fanPages.category,
        description: fanPages.description,
        profileImage: fanPages.profileImage,
        coverImage: fanPages.coverImage,
        website: fanPages.website,
        email: fanPages.email,
        phone: fanPages.phone,
        location: fanPages.location,
        isVerified: fanPages.isVerified,
        isActive: fanPages.isActive,
        followerCount: fanPages.followerCount,
        postCount: fanPages.postCount,
        createdAt: fanPages.createdAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPages)
      .leftJoin(users, eq(fanPages.ownerId, users.id))
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];

    // Check if current user is following this page
    let isFollowing = false;
    let userRole = null;

    if (session?.user?.id) {
      // Check if following
      const followResult = await db
        .select()
        .from(fanPageFollowers)
        .where(
          and(
            eq(fanPageFollowers.userId, session.user.id),
            eq(fanPageFollowers.fanPageId, pageId)
          )
        )
        .limit(1);

      isFollowing = followResult.length > 0;

      // Check if user has a role in this page
      const roleResult = await db
        .select()
        .from(fanPageRoles)
        .where(
          and(
            eq(fanPageRoles.userId, session.user.id),
            eq(fanPageRoles.fanPageId, pageId)
          )
        )
        .limit(1);

      userRole = roleResult[0]?.role || null;
    }

    // Check if user is the owner
    const isOwner = session?.user?.id === page.ownerId;

    return NextResponse.json({
      ...page,
      isFollowing,
      isOwner,
      userRole,
    });

  } catch (error) {
    console.error("Error fetching fan page:", error);
    return NextResponse.json(
      { error: "Failed to fetch fan page" },
      { status: 500 }
    );
  }
}

// PATCH /api/fan-pages/[pageId] - Update fan page
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to edit this page
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];
    const isOwner = page.ownerId === session.user.id;

    // Check if user has admin or editor role
    let hasEditPermission = isOwner;
    if (!isOwner) {
      const roleResult = await db
        .select()
        .from(fanPageRoles)
        .where(
          and(
            eq(fanPageRoles.userId, session.user.id),
            eq(fanPageRoles.fanPageId, pageId)
          )
        )
        .limit(1);

      hasEditPermission = roleResult.length > 0 &&
        (roleResult[0].role === 'admin' || roleResult[0].role === 'editor');
    }

    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to edit this page" },
        { status: 403 }
      );
    }

    const body = await request.json();
    console.log('Received update data:', body);
    const validatedData = updateFanPageSchema.parse(body);
    console.log('Validated data:', validatedData);

    // Update the fan page
    await db
      .update(fanPages)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(fanPages.id, pageId));

    // Fetch updated page
    const updatedPage = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    return NextResponse.json({
      message: "Fan page updated successfully",
      page: updatedPage[0],
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation error:", error.errors);
      return NextResponse.json(
        {
          error: "Invalid data",
          details: error.errors,
          message: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    console.error("Error updating fan page:", error);
    return NextResponse.json(
      { error: "Failed to update fan page" },
      { status: 500 }
    );
  }
}

// DELETE /api/fan-pages/[pageId] - Delete fan page (owner only)
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is the owner
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];
    if (page.ownerId !== session.user.id) {
      return NextResponse.json(
        { error: "Only the page owner can delete the page" },
        { status: 403 }
      );
    }

    // Soft delete by setting isActive to false
    await db
      .update(fanPages)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(fanPages.id, pageId));

    return NextResponse.json({
      message: "Fan page deleted successfully",
    });

  } catch (error) {
    console.error("Error deleting fan page:", error);
    return NextResponse.json(
      { error: "Failed to delete fan page" },
      { status: 500 }
    );
  }
}
