"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Input } from "@/components/ui/Input";
import { Select } from "@/components/ui/Select";
import { Badge } from "@/components/ui/Badge";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  PencilIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  UserIcon,
  CalendarIcon,
  CreditCardIcon,
  BanknotesIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired' | 'pending' | 'suspended';
  startDate: string;
  endDate: string;
  nextBillingDate: string | null;
  cancelledAt: string | null;
  cancelReason: string | null;
  autoRenew: boolean;
  paymentMethod: string | null;
  lastPaymentDate: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar: string | null;
  };
  plan: {
    id: string;
    name: string;
    displayName: string;
    price: string;
    currency: string;
    billingCycle: 'monthly' | 'yearly';
  };
}

interface Filters {
  search: string;
  status: string;
  planId: string;
  paymentMethod: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export default function UserSubscriptionsPage() {
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([]);
  const [plans, setPlans] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedSubscriptions, setSelectedSubscriptions] = useState<string[]>([]);
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: '',
    planId: '',
    paymentMethod: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  useEffect(() => {
    fetchData();
  }, [filters]);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch subscriptions
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });
      queryParams.append('page', '1');
      queryParams.append('limit', '50');

      const subscriptionsResponse = await fetch('/api/admin/subscriptions/users?' + queryParams.toString());
      
      if (!subscriptionsResponse.ok) {
        throw new Error('Failed to fetch subscriptions');
      }
      
      const subscriptionsData = await subscriptionsResponse.json();
      setSubscriptions(subscriptionsData.subscriptions || []);

      // Fetch plans for filter dropdown
      const plansResponse = await fetch('/api/admin/subscriptions/plans');
      if (plansResponse.ok) {
        const plansData = await plansResponse.json();
        setPlans(plansData.plans || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load subscription data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: keyof Filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSort = (field: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: field,
      sortOrder: prev.sortBy === field && prev.sortOrder === 'asc' ? 'desc' : 'asc',
    }));
  };

  const handleBulkAction = async (action: string) => {
    if (selectedSubscriptions.length === 0) {
      toast.error('Please select subscriptions to perform bulk action');
      return;
    }

    if (!confirm(`Are you sure you want to ${action} ${selectedSubscriptions.length} subscription(s)?`)) {
      return;
    }

    try {
      const response = await fetch('/api/admin/subscriptions/users/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          subscriptionIds: selectedSubscriptions,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} subscriptions`);
      }

      await fetchData();
      setSelectedSubscriptions([]);
      toast.success(`Successfully ${action}ed ${selectedSubscriptions.length} subscription(s)`);
    } catch (error) {
      console.error(`Error ${action}ing subscriptions:`, error);
      toast.error(`Failed to ${action} subscriptions`);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'success',
      pending: 'warning',
      cancelled: 'secondary',
      expired: 'danger',
      suspended: 'danger',
    };
    return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{status}</Badge>;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatPrice = (price: string, currency: string, billingCycle: string) => {
    const amount = parseFloat(price);
    return `${currency} ${amount.toFixed(2)}/${billingCycle === 'yearly' ? 'year' : 'month'}`;
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Subscriptions</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage all user subscriptions and billing
          </p>
        </div>
        <div className="mt-4 flex space-x-2 sm:mt-0">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <FunnelIcon className="mr-2 h-5 w-5" />
            Filters
          </Button>
          {selectedSubscriptions.length > 0 && (
            <div className="flex space-x-2">
              <Button
                onClick={() => handleBulkAction('cancel')}
                variant="outline"
                className="text-red-600"
              >
                Cancel Selected ({selectedSubscriptions.length})
              </Button>
              <Button
                onClick={() => handleBulkAction('suspend')}
                variant="outline"
                className="text-orange-600"
              >
                Suspend Selected ({selectedSubscriptions.length})
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card className="p-4 mb-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search users..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="cancelled">Cancelled</option>
                <option value="expired">Expired</option>
                <option value="suspended">Suspended</option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plan
              </label>
              <Select
                value={filters.planId}
                onChange={(e) => handleFilterChange('planId', e.target.value)}
              >
                <option value="">All Plans</option>
                {plans.map((plan) => (
                  <option key={plan.id} value={plan.id}>
                    {plan.displayName}
                  </option>
                ))}
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Method
              </label>
              <Select
                value={filters.paymentMethod}
                onChange={(e) => handleFilterChange('paymentMethod', e.target.value)}
              >
                <option value="">All Methods</option>
                <option value="stripe">Stripe</option>
                <option value="paypal">PayPal</option>
                <option value="wallet">Wallet</option>
              </Select>
            </div>
          </div>
        </Card>
      )}

      {/* Subscriptions Table */}
      {subscriptions.length === 0 ? (
        <Card className="p-8 text-center">
          <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No subscriptions found</h3>
          <p className="mt-2 text-sm text-gray-500">
            No user subscriptions match your current filters.
          </p>
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedSubscriptions.length === subscriptions.length}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedSubscriptions(subscriptions.map(s => s.id));
                        } else {
                          setSelectedSubscriptions([]);
                        }
                      }}
                      className="rounded border-gray-300"
                    />
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('user.name')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>User</span>
                      {filters.sortBy === 'user.name' && (
                        filters.sortOrder === 'asc' ? <ArrowUpIcon className="h-4 w-4" /> : <ArrowDownIcon className="h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('plan.displayName')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Plan</span>
                      {filters.sortBy === 'plan.displayName' && (
                        filters.sortOrder === 'asc' ? <ArrowUpIcon className="h-4 w-4" /> : <ArrowDownIcon className="h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Status</span>
                      {filters.sortBy === 'status' && (
                        filters.sortOrder === 'asc' ? <ArrowUpIcon className="h-4 w-4" /> : <ArrowDownIcon className="h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dates
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {subscriptions.map((subscription) => (
                  <tr key={subscription.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedSubscriptions.includes(subscription.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedSubscriptions(prev => [...prev, subscription.id]);
                          } else {
                            setSelectedSubscriptions(prev => prev.filter(id => id !== subscription.id));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="h-8 w-8 flex-shrink-0">
                          {subscription.user.avatar ? (
                            <img
                              className="h-8 w-8 rounded-full"
                              src={subscription.user.avatar}
                              alt={subscription.user.name}
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                              <UserIcon className="h-4 w-4 text-gray-600" />
                            </div>
                          )}
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {subscription.user.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {subscription.user.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">
                        {subscription.plan.displayName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatPrice(subscription.plan.price, subscription.plan.currency, subscription.plan.billingCycle)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {getStatusBadge(subscription.status)}
                      {subscription.autoRenew && subscription.status === 'active' && (
                        <div className="text-xs text-green-600 mt-1">Auto-renew</div>
                      )}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <CalendarIcon className="h-4 w-4" />
                        <span>Start: {formatDate(subscription.startDate)}</span>
                      </div>
                      <div className="flex items-center space-x-1 mt-1">
                        <CalendarIcon className="h-4 w-4" />
                        <span>End: {formatDate(subscription.endDate)}</span>
                      </div>
                      {subscription.nextBillingDate && (
                        <div className="flex items-center space-x-1 mt-1 text-blue-600">
                          <CreditCardIcon className="h-4 w-4" />
                          <span>Next: {formatDate(subscription.nextBillingDate)}</span>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <BanknotesIcon className="h-4 w-4" />
                        <span>{subscription.paymentMethod || 'N/A'}</span>
                      </div>
                      {subscription.lastPaymentDate && (
                        <div className="text-xs text-gray-400 mt-1">
                          Last: {formatDate(subscription.lastPaymentDate)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link href={`/admin/subscriptions/users/${subscription.id}`}>
                          <Button variant="ghost" size="sm">
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/admin/subscriptions/users/${subscription.id}/edit`}>
                          <Button variant="ghost" size="sm">
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}
    </AdminLayout>
  );
}
