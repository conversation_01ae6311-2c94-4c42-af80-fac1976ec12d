"use client";

import { useSession } from "next-auth/react";
import { useSessionValidation } from "@/hooks/useSessionValidation";
import { ReactNode } from "react";

interface MainLayoutWithMonitoringProps {
  children: ReactNode;
}

export function MainLayoutWithMonitoring({ children }: MainLayoutWithMonitoringProps) {
  const { data: session, status } = useSession();
  
  // Enable session monitoring for all authenticated users (non-admin)
  useSessionValidation({
    checkInterval: 20000, // Check every 20 seconds for regular users
    redirectOnInvalid: true,
    showToast: true
  });

  return <>{children}</>;
}
