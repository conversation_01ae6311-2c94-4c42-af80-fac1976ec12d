"use client";

import { Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { EditFanPageForm } from "./EditFanPageForm";

interface FanPage {
  id: string;
  name: string;
  username: string;
  category: string;
  description: string | null;
  profileImage: string | null;
  coverImage: string | null;
  website: string | null;
  email: string | null;
  phone: string | null;
  location: string | null;
  isVerified: boolean;
  followerCount: number;
  postCount: number;
}

interface EditFanPageModalProps {
  isOpen: boolean;
  onClose: () => void;
  page: FanPage;
  onSuccess?: (updatedPage: FanPage) => void;
}

export function EditFanPageModal({ isOpen, onClose, page, onSuccess }: EditFanPageModalProps) {
  const handleSuccess = (updatedPage: FanPage) => {
    onClose();
    if (onSuccess) {
      onSuccess(updatedPage);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                {/* Modal Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <Dialog.Title as="h3" className="text-2xl font-bold text-gray-900">
                    Edit Fan Page
                  </Dialog.Title>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                {/* Modal Content */}
                <div className="p-6 max-h-[80vh] overflow-y-auto">
                  <EditFanPageForm
                    page={page}
                    onSuccess={handleSuccess}
                    onCancel={onClose}
                    isModal={true}
                  />
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
