import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { 
  wallets, 
  walletTransactions, 
  agents, 
  cashoutRequests 
} from "@/lib/db/schema";
import { count, sum, eq, and, gte, sql } from "drizzle-orm";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get today's date range
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // Get total users with wallets
    const totalUsersResult = await db
      .select({ count: count() })
      .from(wallets)
      .where(eq(wallets.isActive, true));

    // Get total deposited amount
    const totalDepositedResult = await db
      .select({ 
        total: sql<string>`COALESCE(SUM(${wallets.totalDeposited}), 0)` 
      })
      .from(wallets);

    // Get total withdrawn amount
    const totalWithdrawnResult = await db
      .select({ 
        total: sql<string>`COALESCE(SUM(${wallets.totalWithdrawn}), 0)` 
      })
      .from(wallets);

    // Get total transferred amount
    const totalTransferredResult = await db
      .select({ 
        total: sql<string>`COALESCE(SUM(${wallets.totalSent}), 0)` 
      })
      .from(wallets);

    // Get pending transactions count
    const pendingTransactionsResult = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(eq(walletTransactions.status, 'pending'));

    // Get active agents count
    const activeAgentsResult = await db
      .select({ count: count() })
      .from(agents)
      .where(and(
        eq(agents.isActive, true),
        eq(agents.isVerified, true)
      ));

    // Get today's deposits
    const todayDepositsResult = await db
      .select({ 
        total: sql<string>`COALESCE(SUM(${walletTransactions.amount}), 0)` 
      })
      .from(walletTransactions)
      .where(and(
        eq(walletTransactions.type, 'deposit'),
        eq(walletTransactions.status, 'completed'),
        gte(walletTransactions.createdAt, startOfDay)
      ));

    // Get today's withdrawals (cashouts)
    const todayWithdrawalsResult = await db
      .select({ 
        total: sql<string>`COALESCE(SUM(${walletTransactions.amount}), 0)` 
      })
      .from(walletTransactions)
      .where(and(
        eq(walletTransactions.type, 'cashout'),
        eq(walletTransactions.status, 'completed'),
        gte(walletTransactions.createdAt, startOfDay)
      ));

    // Get today's transfers
    const todayTransfersResult = await db
      .select({ 
        total: sql<string>`COALESCE(SUM(${walletTransactions.amount}), 0)` 
      })
      .from(walletTransactions)
      .where(and(
        eq(walletTransactions.type, 'send'),
        eq(walletTransactions.status, 'completed'),
        gte(walletTransactions.createdAt, startOfDay)
      ));

    const stats = {
      totalUsers: totalUsersResult[0]?.count || 0,
      totalDeposited: totalDepositedResult[0]?.total || '0',
      totalWithdrawn: totalWithdrawnResult[0]?.total || '0',
      totalTransferred: totalTransferredResult[0]?.total || '0',
      pendingTransactions: pendingTransactionsResult[0]?.count || 0,
      activeAgents: activeAgentsResult[0]?.count || 0,
      todayDeposits: todayDepositsResult[0]?.total || '0',
      todayWithdrawals: todayWithdrawalsResult[0]?.total || '0',
      todayTransfers: todayTransfersResult[0]?.total || '0',
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error: any) {
    console.error("Error fetching wallet overview:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch wallet overview" 
      },
      { status: 500 }
    );
  }
}
