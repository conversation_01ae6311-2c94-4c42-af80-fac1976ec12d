"use client";

export function ConversationSkeleton() {
  return (
    <div className="animate-pulse">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="border-b border-gray-200 p-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="h-4 bg-gray-200 rounded w-24" />
                <div className="h-3 bg-gray-200 rounded w-12" />
              </div>
              <div className="mt-2 h-3 bg-gray-200 rounded w-32" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export function MessagesSkeleton() {
  return (
    <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
      <div className="space-y-4 animate-pulse">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div
            key={i}
            className={`flex ${i % 2 === 0 ? "justify-end" : "justify-start"}`}
          >
            <div className="flex items-end space-x-2 max-w-[70%]">
              {i % 2 !== 0 && (
                <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0" />
              )}
              <div
                className={`rounded-2xl px-4 py-2.5 ${
                  i % 2 === 0
                    ? "bg-gray-200 rounded-br-md"
                    : "bg-gray-200 rounded-bl-md"
                }`}
              >
                <div className="h-4 bg-gray-300 rounded w-32 mb-1" />
                <div className="h-3 bg-gray-300 rounded w-16" />
              </div>
              {i % 2 === 0 && <div className="w-8" />}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function ChatHeaderSkeleton() {
  return (
    <div className="border-b border-gray-200 p-4 bg-white animate-pulse">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-200 rounded-full" />
          <div>
            <div className="h-4 bg-gray-200 rounded w-24 mb-1" />
            <div className="h-3 bg-gray-200 rounded w-16" />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gray-200 rounded-full" />
          <div className="w-8 h-8 bg-gray-200 rounded-full" />
          <div className="w-8 h-8 bg-gray-200 rounded-full" />
        </div>
      </div>
    </div>
  );
}
