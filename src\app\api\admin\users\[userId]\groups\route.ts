import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, users, groupMembers } from "@/lib/db/schema";
import { eq, desc, count, sql } from "drizzle-orm";

// Get user's created groups
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user's created groups
    const userGroups = await db.select({
      id: groups.id,
      name: groups.name,
      description: groups.description,
      visibility: groups.visibility,
      category: groups.category,
      rules: groups.rules,
      postPermission: groups.postPermission,
      coverImage: groups.coverImage,
      createdAt: groups.createdAt,
      updatedAt: groups.updatedAt,
    })
    .from(groups)
    .where(eq(groups.creatorId, userId))
    .orderBy(desc(groups.createdAt))
    .limit(limit)
    .offset(offset);

    // Get member count for each group separately
    const groupsWithMemberCount = await Promise.all(
      userGroups.map(async (group) => {
        try {
          const memberCountResult = await db.select({ count: count() })
            .from(groupMembers)
            .where(eq(groupMembers.groupId, group.id));

          return {
            id: group.id,
            name: group.name,
            description: group.description,
            visibility: group.visibility,
            category: group.category,
            rules: group.rules,
            postPermission: group.postPermission,
            coverImage: group.coverImage,
            createdAt: group.createdAt,
            updatedAt: group.updatedAt,
            memberCount: memberCountResult[0]?.count || 0,
          };
        } catch (err) {
          console.error('Error processing group:', group, err);
          return {
            id: group.id,
            name: group.name,
            description: group.description,
            visibility: group.visibility,
            category: group.category,
            rules: group.rules,
            postPermission: group.postPermission,
            coverImage: group.coverImage,
            createdAt: group.createdAt,
            updatedAt: group.updatedAt,
            memberCount: 0,
          };
        }
      })
    );

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(groups)
      .where(eq(groups.creatorId, userId));

    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      groups: groupsWithMemberCount,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user groups:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
