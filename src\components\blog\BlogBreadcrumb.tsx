"use client";

import Link from "next/link";
import { ChevronRightIcon, HomeIcon } from "@heroicons/react/24/outline";

interface BlogBreadcrumbProps {
  category?: {
    name: string;
    slug: string;
  } | null;
  title: string;
}

export function BlogBreadcrumb({ category, title }: BlogBreadcrumbProps) {
  return (
    <nav className="mb-6 flex" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {/* Home */}
        <li className="inline-flex items-center">
          <Link
            href="/"
            className="inline-flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-white/80 hover:text-blue-600 transition-all duration-200"
          >
            <HomeIcon className="mr-2 h-4 w-4" aria-hidden="true" />
            Home
          </Link>
        </li>

        {/* Blogs */}
        <li>
          <div className="flex items-center">
            <ChevronRightIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
            <Link
              href="/blogs"
              className="ml-1 rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-white/80 hover:text-blue-600 transition-all duration-200 md:ml-2"
            >
              News/Blogs
            </Link>
          </div>
        </li>

        {/* Category (if exists) */}
        {category && (
          <li>
            <div className="flex items-center">
              <ChevronRightIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
              <Link
                href={`/blogs?category=${category.slug}`}
                className="ml-1 rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-white/80 hover:text-blue-600 transition-all duration-200 md:ml-2"
              >
                {category.name}
              </Link>
            </div>
          </li>
        )}

        {/* Current Post */}
        <li>
          <div className="flex items-center">
            <ChevronRightIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
            <span className="ml-1 rounded-md bg-white/60 backdrop-blur-sm px-3 py-2 text-sm font-medium text-gray-800 md:ml-2 line-clamp-1 max-w-[200px]">
              {title}
            </span>
          </div>
        </li>
      </ol>
    </nav>
  );
}
