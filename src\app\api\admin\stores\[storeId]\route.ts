import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, products, storeReviews, users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and, count } from "drizzle-orm";

const storeUpdateSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  slug: z.string().min(3).max(100).regex(/^[a-zA-Z0-9-]+$/).optional(),
  description: z.string().max(1000).optional().nullable(),
  location: z.string().max(255).optional().nullable(),
  phone: z.string().max(50).optional().nullable(),
  email: z.string().email().max(255).optional().nullable(),
  website: z.string().max(255).optional().nullable(),
  logo: z.string().url().optional().nullable(),
  banner: z.string().url().optional().nullable(),
  isVerified: z.boolean().optional(),
  ownerId: z.string().optional(),
});

// Get store details as admin
export async function GET(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;

    // Get store details
    const storeDetails = await db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        description: stores.description,
        logo: stores.logo,
        banner: stores.banner,
        location: stores.location,
        phone: stores.phone,
        email: stores.email,
        website: stores.website,
        isVerified: stores.isVerified,
        createdAt: stores.createdAt,
        updatedAt: stores.updatedAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(stores)
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(eq(stores.id, storeId))
      .limit(1);

    if (storeDetails.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Get product count
    const productCountResult = await db
      .select({ count: count() })
      .from(products)
      .where(eq(products.storeId, storeId));

    // Get review count
    const reviewCountResult = await db
      .select({ count: count() })
      .from(storeReviews)
      .where(eq(storeReviews.storeId, storeId));

    const store = {
      ...storeDetails[0],
      productCount: productCountResult[0]?.count || 0,
      reviewCount: reviewCountResult[0]?.count || 0,
    };

    return NextResponse.json(store);
  } catch (error) {
    console.error("Error fetching store details:", error);
    return NextResponse.json(
      { message: "Error fetching store details" },
      { status: 500 }
    );
  }
}

// Update store as admin
export async function PUT(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;
    const body = await request.json();
    const validatedData = storeUpdateSchema.parse(body);

    // Check if store exists
    const existingStore = await db
      .select({
        id: stores.id,
        slug: stores.slug,
      })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // If slug is being changed, check if it's already taken
    if (validatedData.slug && validatedData.slug !== existingStore[0].slug) {
      const slugExists = await db
        .select({ id: stores.id })
        .from(stores)
        .where(eq(stores.slug, validatedData.slug))
        .limit(1);

      if (slugExists.length > 0) {
        return NextResponse.json(
          { message: "Store slug already exists" },
          { status: 400 }
        );
      }
    }

    // If ownerId is being changed, check if the user exists
    if (validatedData.ownerId) {
      const userExists = await db
        .select({ id: users.id })
        .from(users)
        .where(eq(users.id, validatedData.ownerId))
        .limit(1);

      if (userExists.length === 0) {
        return NextResponse.json(
          { message: "User not found" },
          { status: 400 }
        );
      }
    }

    // Update store
    await db
      .update(stores)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(stores.id, storeId));

    // Get updated store
    const updatedStore = await db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        description: stores.description,
        logo: stores.logo,
        banner: stores.banner,
        location: stores.location,
        phone: stores.phone,
        email: stores.email,
        website: stores.website,
        isVerified: stores.isVerified,
        createdAt: stores.createdAt,
        updatedAt: stores.updatedAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(stores)
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(eq(stores.id, storeId))
      .limit(1);

    return NextResponse.json({
      message: "Store updated successfully",
      store: updatedStore[0],
    });
  } catch (error) {
    console.error("Error updating store:", error);
    return NextResponse.json(
      { message: "Error updating store" },
      { status: 500 }
    );
  }
}

// Delete store as admin
export async function DELETE(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;

    // Check if store exists
    const existingStore = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Check if store has products
    const productCount = await db
      .select({ count: count() })
      .from(products)
      .where(eq(products.storeId, storeId));

    if (productCount[0]?.count > 0) {
      return NextResponse.json(
        { message: "Cannot delete store with existing products. Please delete all products first." },
        { status: 400 }
      );
    }

    // Delete store
    await db.delete(stores).where(eq(stores.id, storeId));

    return NextResponse.json({
      message: "Store deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting store:", error);
    return NextResponse.json(
      { message: "Error deleting store" },
      { status: 500 }
    );
  }
}
