"use client";

import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";

interface UseViewTrackingProps {
  blogSlug: string;
  isEnabled?: boolean;
  minReadDuration?: number; // in seconds
}

interface UseViewTrackingReturn {
  readDuration: number;
  viewDuration: number;
  scrollDepth: number;
  isQualified: boolean;
  sessionId: string;
}

export function useViewTracking({
  blogSlug,
  isEnabled = true,
  minReadDuration = 120, // 2 minutes default
}: UseViewTrackingProps): UseViewTrackingReturn {
  const [readDuration, setReadDuration] = useState(0);
  const [viewDuration, setViewDuration] = useState(0);
  const [scrollDepth, setScrollDepth] = useState(0);
  const [isQualified, setIsQualified] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(Date.now());
  const visibilityTimeRef = useRef<number>(0);
  const isVisibleRef = useRef<boolean>(true);
  const sessionIdRef = useRef<string>(uuidv4());
  const hasTrackedView = useRef<boolean>(false);
  const hasTrackedRead = useRef<boolean>(false);
  const fingerprintRef = useRef<string>('');
  const maxScrollRef = useRef<number>(0);

  // Generate browser fingerprint
  useEffect(() => {
    const generateFingerprint = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Browser fingerprint', 2, 2);
      }
      
      const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL(),
        navigator.hardwareConcurrency || 0,
        (navigator as any).deviceMemory || 0,
      ].join('|');
      
      // Simple hash function
      let hash = 0;
      for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }
      
      return Math.abs(hash).toString(36);
    };

    fingerprintRef.current = generateFingerprint();
  }, []);

  // Track scroll depth
  useEffect(() => {
    if (!isEnabled) return;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? Math.round((scrollTop / docHeight) * 100) : 0;
      
      if (scrollPercent > maxScrollRef.current) {
        maxScrollRef.current = scrollPercent;
        setScrollDepth(scrollPercent);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isEnabled]);

  // Get location information
  const getLocationInfo = async () => {
    try {
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      return {
        country: data.country_name,
        city: data.city,
      };
    } catch (error) {
      return { country: undefined, city: undefined };
    }
  };

  useEffect(() => {
    if (!isEnabled || !blogSlug) return;

    // Reset tracking state
    startTimeRef.current = Date.now();
    visibilityTimeRef.current = 0;
    hasTrackedView.current = false;
    hasTrackedRead.current = false;
    maxScrollRef.current = 0;
    setReadDuration(0);
    setViewDuration(0);
    setScrollDepth(0);
    setIsQualified(false);

    // Track initial view
    const trackInitialView = async () => {
      if (hasTrackedView.current) return;
      hasTrackedView.current = true;

      const locationInfo = await getLocationInfo();
      
      try {
        await fetch(`/api/blogs/${blogSlug}/track-view`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sessionId: sessionIdRef.current,
            fingerprint: fingerprintRef.current,
            referrer: document.referrer || undefined,
            country: locationInfo.country,
            city: locationInfo.city,
          }),
        });
      } catch (error) {
        console.error('Error tracking view:', error);
      }
    };

    trackInitialView();

    // Handle visibility change
    const handleVisibilityChange = () => {
      const now = Date.now();
      
      if (document.hidden) {
        // Page became hidden
        if (isVisibleRef.current) {
          visibilityTimeRef.current += now - startTimeRef.current;
          isVisibleRef.current = false;
        }
      } else {
        // Page became visible
        if (!isVisibleRef.current) {
          startTimeRef.current = now;
          isVisibleRef.current = true;
        }
      }
    };

    // Update durations every second
    const updateDurations = () => {
      if (!document.hidden && isVisibleRef.current) {
        const now = Date.now();
        const currentVisibleTime = now - startTimeRef.current;
        const totalTime = visibilityTimeRef.current + currentVisibleTime;
        const durationInSeconds = Math.floor(totalTime / 1000);
        
        setReadDuration(durationInSeconds);
        setViewDuration(durationInSeconds);
        setIsQualified(durationInSeconds >= minReadDuration);
        
        // Update view metrics every 10 seconds
        if (durationInSeconds > 0 && durationInSeconds % 10 === 0) {
          updateViewMetrics(durationInSeconds);
        }
        
        // Track read after minimum duration is reached (only once)
        if (!hasTrackedRead.current && durationInSeconds >= minReadDuration) {
          hasTrackedRead.current = true;
          trackRead(durationInSeconds);
        }
      }
    };

    // Set up event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    intervalRef.current = setInterval(updateDurations, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      
      // Final update on unmount
      const finalDuration = Math.floor(visibilityTimeRef.current / 1000);
      if (finalDuration >= 5) { // Update if at least 5 seconds
        updateViewMetrics(finalDuration);
      }
      
      // Track final read duration if not already tracked
      if (!hasTrackedRead.current && finalDuration >= 30) {
        trackRead(finalDuration);
      }
    };
  }, [blogSlug, isEnabled, minReadDuration]);

  const updateViewMetrics = async (duration: number) => {
    try {
      await fetch(`/api/blogs/${blogSlug}/track-view`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: sessionIdRef.current,
          viewDuration: duration,
          scrollDepth: maxScrollRef.current,
        }),
      });
    } catch (error) {
      console.error('Error updating view metrics:', error);
    }
  };

  const trackRead = async (duration: number) => {
    try {
      await fetch(`/api/blogs/${blogSlug}/track-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          readDuration: duration,
          sessionId: sessionIdRef.current,
          referrer: document.referrer || undefined,
          scrollDepth: maxScrollRef.current,
        }),
      });
    } catch (error) {
      console.error('Error tracking read:', error);
    }
  };

  return {
    readDuration,
    isQualified,
    viewDuration,
    scrollDepth,
    sessionId: sessionIdRef.current,
  };
}
