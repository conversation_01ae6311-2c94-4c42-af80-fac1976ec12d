import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  fanPages,
  fanPageFollowers,
  fanPagePosts,
  fanPageMessages,
  fanPagePostLikes,
  fanPagePostComments,
  fanPageRoles
} from "@/lib/db/schema";
import { eq, and, count, sql, desc } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId]/dashboard/stats - Get dashboard statistics
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;

    // Verify user owns this fan page or has admin role
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, pageId),
        eq(fanPages.isActive, true)
      ),
    });

    if (!page) {
      return NextResponse.json({ error: "Fan page not found" }, { status: 404 });
    }

    const isOwner = page.ownerId === session.user.id;
    let hasAdminRole = false;

    if (!isOwner) {
      const userRole = await db.query.fanPageRoles.findFirst({
        where: and(
          eq(fanPageRoles.fanPageId, pageId),
          eq(fanPageRoles.userId, session.user.id),
          eq(fanPageRoles.role, 'admin')
        ),
      });
      hasAdminRole = !!userRole;
    }

    if (!isOwner && !hasAdminRole) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Get current date ranges
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get total followers
    const totalFollowersResult = await db
      .select({ count: count() })
      .from(fanPageFollowers)
      .where(eq(fanPageFollowers.fanPageId, pageId));

    // Get new followers this month
    const newFollowersThisMonthResult = await db
      .select({ count: count() })
      .from(fanPageFollowers)
      .where(and(
        eq(fanPageFollowers.fanPageId, pageId),
        sql`${fanPageFollowers.createdAt} >= ${startOfMonth.toISOString()}`
      ));

    // Get new followers last month
    const newFollowersLastMonthResult = await db
      .select({ count: count() })
      .from(fanPageFollowers)
      .where(and(
        eq(fanPageFollowers.fanPageId, pageId),
        sql`${fanPageFollowers.createdAt} >= ${startOfLastMonth.toISOString()}`,
        sql`${fanPageFollowers.createdAt} <= ${endOfLastMonth.toISOString()}`
      ));

    // Get total posts
    const totalPostsResult = await db
      .select({ count: count() })
      .from(fanPagePosts)
      .where(eq(fanPagePosts.fanPageId, pageId));

    // Get posts this month
    const postsThisMonthResult = await db
      .select({ count: count() })
      .from(fanPagePosts)
      .where(and(
        eq(fanPagePosts.fanPageId, pageId),
        sql`${fanPagePosts.createdAt} >= ${startOfMonth.toISOString()}`
      ));

    // Get total messages
    const totalMessagesResult = await db
      .select({ count: count() })
      .from(fanPageMessages)
      .where(eq(fanPageMessages.fanPageId, pageId));

    // Get unread messages
    const unreadMessagesResult = await db
      .select({ count: count() })
      .from(fanPageMessages)
      .where(and(
        eq(fanPageMessages.fanPageId, pageId),
        eq(fanPageMessages.read, false)
      ));

    // Get total likes across all posts
    const totalLikesResult = await db
      .select({ count: count() })
      .from(fanPagePostLikes)
      .innerJoin(fanPagePosts, eq(fanPagePostLikes.fanPagePostId, fanPagePosts.id))
      .where(eq(fanPagePosts.fanPageId, pageId));

    // Get total comments across all posts
    const totalCommentsResult = await db
      .select({ count: count() })
      .from(fanPagePostComments)
      .innerJoin(fanPagePosts, eq(fanPagePostComments.postId, fanPagePosts.id))
      .where(eq(fanPagePosts.fanPageId, pageId));

    // Get engagement this week
    const likesThisWeekResult = await db
      .select({ count: count() })
      .from(fanPagePostLikes)
      .innerJoin(fanPagePosts, eq(fanPagePostLikes.fanPagePostId, fanPagePosts.id))
      .where(and(
        eq(fanPagePosts.fanPageId, pageId),
        sql`${fanPagePostLikes.createdAt} >= ${startOfWeek.toISOString()}`
      ));

    const commentsThisWeekResult = await db
      .select({ count: count() })
      .from(fanPagePostComments)
      .innerJoin(fanPagePosts, eq(fanPagePostComments.postId, fanPagePosts.id))
      .where(and(
        eq(fanPagePosts.fanPageId, pageId),
        sql`${fanPagePostComments.createdAt} >= ${startOfWeek.toISOString()}`
      ));

    // Calculate growth rates
    const currentFollowers = totalFollowersResult[0]?.count || 0;
    const newFollowersThisMonth = newFollowersThisMonthResult[0]?.count || 0;
    const newFollowersLastMonth = newFollowersLastMonthResult[0]?.count || 0;

    const followerGrowthRate = newFollowersLastMonth > 0
      ? ((newFollowersThisMonth - newFollowersLastMonth) / newFollowersLastMonth) * 100
      : newFollowersThisMonth > 0 ? 100 : 0;

    const totalEngagement = (totalLikesResult[0]?.count || 0) + (totalCommentsResult[0]?.count || 0);
    const engagementRate = currentFollowers > 0 ? (totalEngagement / currentFollowers) * 100 : 0;

    const stats = {
      overview: {
        totalFollowers: currentFollowers,
        totalPosts: totalPostsResult[0]?.count || 0,
        totalMessages: totalMessagesResult[0]?.count || 0,
        unreadMessages: unreadMessagesResult[0]?.count || 0,
        totalLikes: totalLikesResult[0]?.count || 0,
        totalComments: totalCommentsResult[0]?.count || 0,
        engagementRate: Math.round(engagementRate * 10) / 10,
      },
      growth: {
        newFollowersThisMonth,
        newFollowersLastMonth,
        followerGrowthRate: Math.round(followerGrowthRate * 10) / 10,
        postsThisMonth: postsThisMonthResult[0]?.count || 0,
      },
      engagement: {
        likesThisWeek: likesThisWeekResult[0]?.count || 0,
        commentsThisWeek: commentsThisWeekResult[0]?.count || 0,
        totalEngagement,
      },
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
