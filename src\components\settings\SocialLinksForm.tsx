"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { SettingsLayout } from "./SettingsLayout";
import { Button } from "@/components/ui/Button";
import { 
  GlobeAltIcon, 
  LinkIcon,
} from "@heroicons/react/24/outline";

interface UserData {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  username?: string | null;
  bio?: string | null;
  location?: string | null;
  birthday?: string | null;
  coverImage?: string | null;
  website?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  instagram?: string | null;
  linkedin?: string | null;
  youtube?: string | null;
}

interface SocialLinksFormProps {
  userId: string;
}

const socialPlatforms = [
  {
    name: 'website',
    label: 'Website',
    placeholder: 'https://example.com',
    icon: GlobeAltIcon,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
  },
  {
    name: 'facebook',
    label: 'Facebook',
    placeholder: 'https://facebook.com/username',
    icon: LinkIcon,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    name: 'twitter',
    label: 'Twitter / X',
    placeholder: 'https://twitter.com/username',
    icon: LinkIcon,
    color: 'text-sky-600',
    bgColor: 'bg-sky-100',
  },
  {
    name: 'instagram',
    label: 'Instagram',
    placeholder: 'https://instagram.com/username',
    icon: LinkIcon,
    color: 'text-pink-600',
    bgColor: 'bg-pink-100',
  },
  {
    name: 'linkedin',
    label: 'LinkedIn',
    placeholder: 'https://linkedin.com/in/username',
    icon: LinkIcon,
    color: 'text-blue-700',
    bgColor: 'bg-blue-100',
  },
  {
    name: 'youtube',
    label: 'YouTube',
    placeholder: 'https://youtube.com/channel/channelid',
    icon: LinkIcon,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
  },
];

export function SocialLinksForm({ userId }: SocialLinksFormProps) {
  const { data: session } = useSession();
  const router = useRouter();

  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    website: "",
    facebook: "",
    twitter: "",
    instagram: "",
    linkedin: "",
    youtube: "",
  });

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        
        if (response.ok) {
          const data = await response.json();
          setUserData(data);
          setFormData({
            website: data.website || "",
            facebook: data.facebook || "",
            twitter: data.twitter || "",
            instagram: data.instagram || "",
            linkedin: data.linkedin || "",
            youtube: data.youtube || "",
          });
        } else {
          setError("Failed to load social links data");
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setError("An error occurred while loading your social links");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateUrl = (url: string): boolean => {
    if (!url.trim()) return true; // Empty URLs are allowed
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Validate URLs
    const invalidUrls: string[] = [];
    Object.entries(formData).forEach(([platform, url]) => {
      if (url.trim() && !validateUrl(url)) {
        invalidUrls.push(platform);
      }
    });

    if (invalidUrls.length > 0) {
      setError(`Invalid URL format for: ${invalidUrls.join(', ')}`);
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          website: formData.website.trim() || undefined,
          facebook: formData.facebook.trim() || undefined,
          twitter: formData.twitter.trim() || undefined,
          instagram: formData.instagram.trim() || undefined,
          linkedin: formData.linkedin.trim() || undefined,
          youtube: formData.youtube.trim() || undefined,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to update social links");
      }

      setSuccess("Social links updated successfully!");
      
      // Refresh user data
      const updatedResponse = await fetch(`/api/users/${userId}`);
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json();
        setUserData(updatedData);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

    } catch (error) {
      console.error("Error updating social links:", error);
      setError(error instanceof Error ? error.message : "Failed to update social links");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (userData) {
      setFormData({
        website: userData.website || "",
        facebook: userData.facebook || "",
        twitter: userData.twitter || "",
        instagram: userData.instagram || "",
        linkedin: userData.linkedin || "",
        youtube: userData.youtube || "",
      });
    }
    setError(null);
    setSuccess(null);
  };

  if (isLoading) {
    return (
      <SettingsLayout
        title="Social Links"
        description="Connect your social media accounts and websites."
      >
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </SettingsLayout>
    );
  }

  return (
    <SettingsLayout
      title="Social Links"
      description="Connect your social media accounts and websites."
    >
      <form onSubmit={handleSubmit}>
        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">Success</h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>{success}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6">
          {socialPlatforms.map((platform) => {
            const IconComponent = platform.icon;
            const value = formData[platform.name as keyof typeof formData];
            const isValidUrl = !value.trim() || validateUrl(value);

            return (
              <div key={platform.name}>
                <label 
                  htmlFor={platform.name} 
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  <div className="flex items-center space-x-2">
                    <div className={`p-1.5 rounded-lg ${platform.bgColor}`}>
                      <IconComponent className={`h-4 w-4 ${platform.color}`} />
                    </div>
                    <span>{platform.label}</span>
                  </div>
                </label>
                <div className="mt-1">
                  <input
                    type="url"
                    name={platform.name}
                    id={platform.name}
                    value={value}
                    onChange={handleInputChange}
                    className={`block w-full rounded-md shadow-sm sm:text-sm ${
                      !isValidUrl
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    }`}
                    placeholder={platform.placeholder}
                  />
                </div>
                {!isValidUrl && (
                  <p className="mt-1 text-xs text-red-600">
                    Please enter a valid URL
                  </p>
                )}
                {value.trim() && isValidUrl && (
                  <p className="mt-1 text-xs text-gray-500">
                    <a 
                      href={value} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      Preview link
                    </a>
                  </p>
                )}
              </div>
            );
          })}
        </div>

        <div className="pt-6">
          <div className="flex justify-end space-x-3">
            <Button 
              type="button"
              variant="outline" 
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </div>
      </form>
    </SettingsLayout>
  );
}
