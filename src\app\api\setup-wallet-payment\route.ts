import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { paymentGateways } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export async function POST() {
  try {
    console.log('Setting up wallet payment gateway...');

    // First, try to update the enum to include wallet type
    try {
      await db.execute(sql`
        ALTER TABLE payment_gateways
        MODIFY COLUMN type ENUM(
          'stripe',
          'paypal',
          'sslcommerz',
          'bkash',
          'nagad',
          'rocket',
          'bank',
          'uddoktapay',
          'manual',
          'wallet'
        ) NOT NULL
      `);
      console.log('✅ Updated payment gateway enum to include wallet');
    } catch (enumError) {
      console.log('ℹ️ Enum might already include wallet type:', enumError);
    }

    // Check if wallet gateway already exists
    const existingWallet = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.name, 'wallet')
    });

    if (existingWallet) {
      console.log('Wallet gateway already exists:', existingWallet);

      // Make sure it's active
      await db.update(paymentGateways)
        .set({ isActive: true, sortOrder: 1 })
        .where(eq(paymentGateways.name, 'wallet'));

      return NextResponse.json({
        success: true,
        message: "Wallet payment gateway already exists and is now active",
        data: existingWallet
      });
    }

    // Add wallet payment gateway using raw SQL to avoid type issues
    const walletId = uuidv4();
    await db.execute(sql`
      INSERT INTO payment_gateways (
        id, name, displayName, type, isActive, config,
        depositFee, depositFixedFee, minDeposit, maxDeposit,
        currency, sortOrder, createdAt, updatedAt
      ) VALUES (
        ${walletId},
        'wallet',
        'General Wallet Balance',
        'wallet',
        1,
        '{"description": "Pay using your general wallet balance", "instantPayment": true, "requiresBalance": true}',
        '0.00',
        '0.00',
        '0.01',
        '999999.99',
        'USD',
        1,
        NOW(),
        NOW()
      )
    `);

    console.log('Wallet gateway added successfully with ID:', walletId);

    // Fetch the created gateway
    const newWallet = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.id, walletId)
    });

    return NextResponse.json({
      success: true,
      message: "Wallet payment gateway added successfully",
      data: newWallet
    });

  } catch (error) {
    console.error("Error setting up wallet gateway:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to setup wallet gateway",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
