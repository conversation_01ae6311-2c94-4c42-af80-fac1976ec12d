"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

interface SubscriptionStatus {
  hasSubscription: boolean;
  isActive: boolean;
  plan: {
    id: string;
    name: string;
    displayName: string;
    description: string;
    price: string;
    currency: string;
    billingCycle: string;
    features: string[];
    maxPosts: number;
    maxStorage: number;
    maxGroups: number;
    canCreateFanPages: boolean;
    canCreateStores: boolean;
    canMonetizeBlogs: boolean;
    prioritySupport: boolean;
  } | null;
  subscription: {
    id: string;
    status: string;
    startDate: string;
    endDate: string;
    nextBillingDate: string | null;
    autoRenew: boolean;
  } | null;
  daysRemaining: number | null;
  nextBillingDate: string | null;
}

interface FeatureLimits {
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
}

interface UsageLimits {
  posts: {
    current: number;
    limit: number;
    percentage: number;
    isWithinLimit: boolean;
  };
  storage: {
    current: number;
    limit: number;
    percentage: number;
    isWithinLimit: boolean;
  };
  groups: {
    current: number;
    limit: number;
    percentage: number;
    isWithinLimit: boolean;
  };
}

export function useSubscription() {
  const { data: session } = useSession();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [featureLimits, setFeatureLimits] = useState<FeatureLimits | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user?.id) {
      fetchSubscriptionData();
    } else {
      setLoading(false);
    }
  }, [session?.user?.id]);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/user-subscription');
      const result = await response.json();

      if (result.success) {
        setSubscriptionStatus(result.data);
        
        // Extract feature limits from plan
        if (result.data.plan) {
          setFeatureLimits({
            maxPosts: result.data.plan.maxPosts,
            maxStorage: result.data.plan.maxStorage,
            maxGroups: result.data.plan.maxGroups,
            canCreateFanPages: result.data.plan.canCreateFanPages,
            canCreateStores: result.data.plan.canCreateStores,
            canMonetizeBlogs: result.data.plan.canMonetizeBlogs,
            prioritySupport: result.data.plan.prioritySupport,
          });
        }
      } else {
        setError(result.message || 'Failed to fetch subscription data');
      }
    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError('Failed to fetch subscription data');
    } finally {
      setLoading(false);
    }
  };

  const canAccessFeature = (feature: string): boolean => {
    if (!subscriptionStatus?.isActive || !subscriptionStatus.plan) {
      return false;
    }

    const plan = subscriptionStatus.plan;

    switch (feature) {
      case 'create_fan_pages':
        return plan.canCreateFanPages;
      case 'create_stores':
        return plan.canCreateStores;
      case 'monetize_blogs':
        return plan.canMonetizeBlogs;
      case 'priority_support':
        return plan.prioritySupport;
      case 'unlimited_posts':
        return plan.maxPosts === -1;
      case 'unlimited_storage':
        return plan.maxStorage === -1;
      case 'unlimited_groups':
        return plan.maxGroups === -1;
      default:
        return false;
    }
  };

  const getFeatureLimit = (feature: 'posts' | 'storage' | 'groups'): number => {
    if (!featureLimits) return 0;

    switch (feature) {
      case 'posts':
        return featureLimits.maxPosts;
      case 'storage':
        return featureLimits.maxStorage;
      case 'groups':
        return featureLimits.maxGroups;
      default:
        return 0;
    }
  };

  const isFeatureUnlimited = (feature: 'posts' | 'storage' | 'groups'): boolean => {
    return getFeatureLimit(feature) === -1;
  };

  const isPremiumUser = (): boolean => {
    return subscriptionStatus?.isActive && 
           subscriptionStatus.plan?.name !== 'free';
  };

  const isProUser = (): boolean => {
    return subscriptionStatus?.isActive && 
           subscriptionStatus.plan?.name === 'pro';
  };

  const getPlanDisplayName = (): string => {
    return subscriptionStatus?.plan?.displayName || 'Free';
  };

  const getDaysRemaining = (): number | null => {
    return subscriptionStatus?.daysRemaining || null;
  };

  const getNextBillingDate = (): string | null => {
    return subscriptionStatus?.nextBillingDate || null;
  };

  const refresh = () => {
    if (session?.user?.id) {
      fetchSubscriptionData();
    }
  };

  return {
    // Data
    subscriptionStatus,
    featureLimits,
    loading,
    error,

    // Helper functions
    canAccessFeature,
    getFeatureLimit,
    isFeatureUnlimited,
    isPremiumUser,
    isProUser,
    getPlanDisplayName,
    getDaysRemaining,
    getNextBillingDate,
    refresh,

    // Convenience flags
    isActive: subscriptionStatus?.isActive || false,
    hasSubscription: subscriptionStatus?.hasSubscription || false,
    plan: subscriptionStatus?.plan,
    subscription: subscriptionStatus?.subscription,
  };
}

// Hook for checking usage limits
export function useUsageLimits() {
  const { data: session } = useSession();
  const [usageLimits, setUsageLimits] = useState<UsageLimits | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (session?.user?.id) {
      fetchUsageLimits();
    } else {
      setLoading(false);
    }
  }, [session?.user?.id]);

  const fetchUsageLimits = async () => {
    try {
      setLoading(true);

      // This would be implemented with actual usage tracking API
      // For now, we'll use mock data
      const mockUsage = {
        posts: { current: 5, limit: 10, percentage: 50, isWithinLimit: true },
        storage: { current: 50, limit: 100, percentage: 50, isWithinLimit: true },
        groups: { current: 1, limit: 3, percentage: 33, isWithinLimit: true },
      };

      setUsageLimits(mockUsage);
    } catch (error) {
      console.error('Error fetching usage limits:', error);
    } finally {
      setLoading(false);
    }
  };

  const isNearLimit = (resource: 'posts' | 'storage' | 'groups', threshold = 80): boolean => {
    if (!usageLimits) return false;
    return usageLimits[resource].percentage >= threshold;
  };

  const isAtLimit = (resource: 'posts' | 'storage' | 'groups'): boolean => {
    if (!usageLimits) return false;
    return !usageLimits[resource].isWithinLimit;
  };

  const getRemainingUsage = (resource: 'posts' | 'storage' | 'groups'): number => {
    if (!usageLimits) return 0;
    const usage = usageLimits[resource];
    return Math.max(0, usage.limit - usage.current);
  };

  return {
    usageLimits,
    loading,
    isNearLimit,
    isAtLimit,
    getRemainingUsage,
    refresh: fetchUsageLimits,
  };
}

// Feature gate component
interface FeatureGateProps {
  feature: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
  showUpgradePrompt?: boolean;
}

export function FeatureGate({ 
  feature, 
  fallback, 
  children, 
  showUpgradePrompt = true 
}: FeatureGateProps) {
  const { canAccessFeature, loading } = useSubscription();

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded"></div>;
  }

  if (!canAccessFeature(feature)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showUpgradePrompt) {
      return (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Premium Feature
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>This feature requires a premium subscription.</p>
              </div>
              <div className="mt-4">
                <div className="-mx-2 -my-1.5 flex">
                  <a
                    href="/manage-subscription"
                    className="bg-blue-50 px-2 py-1.5 rounded-md text-sm font-medium text-blue-800 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-50 focus:ring-blue-600"
                  >
                    Upgrade Now
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return null;
  }

  return <>{children}</>;
}
