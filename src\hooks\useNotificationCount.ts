"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useFeatureFlags } from '@/lib/features/flags';
import { useRealtimeNotifications } from './useRealtimeNotifications';

export interface NotificationItem {
  id: string;
  type: 'like' | 'comment' | 'share' | 'follow' | 'mention' | 'message' | 'system';
  title: string;
  message: string;
  avatar?: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export interface MessageThread {
  id: string;
  participantId: string;
  participantName: string;
  participantAvatar?: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  isOnline?: boolean;
}

interface NotificationCounts {
  total: number;
  unread: number;
  byType: Record<string, number>;
  priority: {
    high: number;
    medium: number;
    low: number;
  };
}

interface MessageCounts {
  total: number;
  unread: number;
  threads: number;
}

export function useNotificationCount() {
  const { data: session } = useSession();
  const { shouldUseRealtime, shouldUsePolling, shouldUseHybridMode } = useFeatureFlags(session?.user?.id);

  // Legacy state for polling fallback
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [messages, setMessages] = useState<MessageThread[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pollingRef = useRef<NodeJS.Timeout>();

  // Real-time notifications hook
  const realtimeNotifications = useRealtimeNotifications({
    enableSound: true,
    enableBrowserNotifications: true,
    fallbackToPolling: true,
    pollingInterval: 30000
  });

  // Determine which system to use
  const useRealtime = shouldUseRealtime('realtimeNotifications');
  const usePolling = shouldUsePolling();
  const useHybrid = shouldUseHybridMode();

  // Use real-time data if available, otherwise fall back to polling data
  const activeNotifications = useRealtime ? realtimeNotifications.notifications.map(n => ({
    id: n.id,
    type: n.type as NotificationItem['type'],
    title: n.title || '',
    message: n.message || '',
    avatar: undefined,
    timestamp: n.timestamp,
    read: n.read,
    priority: n.priority,
    actionUrl: n.actionUrl,
    metadata: n.metadata
  })) : notifications;

  // Calculate notification counts
  const notificationCounts: NotificationCounts = useRealtime ? realtimeNotifications.notificationCounts : {
    total: notifications.length,
    unread: notifications.filter(n => !n.read).length,
    byType: notifications.reduce((acc, n) => {
      acc[n.type] = (acc[n.type] || 0) + (n.read ? 0 : 1);
      return acc;
    }, {} as Record<string, number>),
    priority: {
      high: notifications.filter(n => !n.read && n.priority === 'high').length,
      medium: notifications.filter(n => !n.read && n.priority === 'medium').length,
      low: notifications.filter(n => !n.read && n.priority === 'low').length,
    }
  };

  // Calculate message counts
  const messageCounts: MessageCounts = {
    total: messages.reduce((sum, thread) => sum + thread.unreadCount, 0),
    unread: messages.filter(thread => thread.unreadCount > 0).length,
    threads: messages.length
  };

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/notifications');
      if (!response.ok) throw new Error('Failed to fetch notifications');
      
      const data = await response.json();
      setNotifications(data.notifications || []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setError('Failed to load notifications');
    }
  }, [session?.user?.id]);

  // Fetch messages
  const fetchMessages = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/messages/threads');
      if (!response.ok) throw new Error('Failed to fetch messages');
      
      const data = await response.json();
      setMessages(data.threads || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    }
  }, [session?.user?.id]);

  // Mark notification as read
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
        );
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllNotificationsAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'POST'
      });
      
      if (response.ok) {
        setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, []);

  // Mark message thread as read
  const markMessageThreadAsRead = useCallback(async (threadId: string) => {
    try {
      const response = await fetch(`/api/messages/threads/${threadId}/read`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setMessages(prev => 
          prev.map(m => m.id === threadId ? { ...m, unreadCount: 0 } : m)
        );
      }
    } catch (error) {
      console.error('Error marking message thread as read:', error);
    }
  }, []);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }, []);

  // Refresh data
  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await Promise.all([fetchNotifications(), fetchMessages()]);
    } finally {
      setIsLoading(false);
    }
  }, [fetchNotifications, fetchMessages]);

  // Setup polling for real-time updates (only if not using real-time or in hybrid mode)
  useEffect(() => {
    if (!session?.user?.id) return;

    // Always do initial fetch
    if (!useRealtime || useHybrid) {
      refresh();
    }

    // Setup polling only if needed
    if (usePolling && (!useRealtime || useHybrid)) {
      pollingRef.current = setInterval(() => {
        fetchNotifications();
        fetchMessages();
      }, 30000);
    }

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, [session?.user?.id, useRealtime, usePolling, useHybrid, refresh, fetchNotifications, fetchMessages]);

  // Listen for real-time updates (if WebSocket is available)
  useEffect(() => {
    const handleNotificationUpdate = (event: CustomEvent) => {
      const { type, data } = event.detail;
      
      if (type === 'new_notification') {
        setNotifications(prev => [data, ...prev]);
      } else if (type === 'new_message') {
        setMessages(prev => {
          const existingThread = prev.find(m => m.participantId === data.senderId);
          if (existingThread) {
            return prev.map(m => 
              m.participantId === data.senderId 
                ? { 
                    ...m, 
                    lastMessage: data.content,
                    timestamp: data.timestamp,
                    unreadCount: m.unreadCount + 1
                  }
                : m
            );
          } else {
            return [{
              id: data.threadId,
              participantId: data.senderId,
              participantName: data.senderName,
              participantAvatar: data.senderAvatar,
              lastMessage: data.content,
              timestamp: data.timestamp,
              unreadCount: 1
            }, ...prev];
          }
        });
      }
    };

    window.addEventListener('notification_update', handleNotificationUpdate as EventListener);
    
    return () => {
      window.removeEventListener('notification_update', handleNotificationUpdate as EventListener);
    };
  }, []);

  return {
    // Data - use real-time data if available
    notifications: activeNotifications,
    messages,
    notificationCounts,
    messageCounts,

    // State - combine real-time and polling states
    isLoading: useRealtime ? realtimeNotifications.isLoading : isLoading,
    error: useRealtime ? realtimeNotifications.error : error,

    // Actions - use real-time actions if available
    markNotificationAsRead: useRealtime ? realtimeNotifications.markAsRead : markNotificationAsRead,
    markAllNotificationsAsRead: useRealtime ? realtimeNotifications.markAllAsRead : markAllNotificationsAsRead,
    markMessageThreadAsRead,
    deleteNotification: useRealtime ? realtimeNotifications.deleteNotification : deleteNotification,
    refresh: useRealtime ? realtimeNotifications.refresh : refresh,

    // Utilities
    hasUnreadNotifications: notificationCounts.unread > 0,
    hasUnreadMessages: messageCounts.unread > 0,
    totalUnread: notificationCounts.unread + messageCounts.total,

    // Real-time specific data
    connectionStatus: useRealtime ? realtimeNotifications.connectionStatus : null,
    isConnected: useRealtime ? realtimeNotifications.isConnected : false,
    latency: useRealtime ? realtimeNotifications.latency : 0,

    // Feature flags for debugging
    featureFlags: {
      useRealtime,
      usePolling,
      useHybrid
    }
  };
}
