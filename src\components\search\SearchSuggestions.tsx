"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  MagnifyingGlassIcon,
  UserIcon,
  UsersIcon,
  NewspaperIcon,
  CalendarIcon,
  DocumentTextIcon,
  ClockIcon,
  ArrowTrendingUpIcon
} from "@heroicons/react/24/outline";

interface SearchResult {
  id: string;
  type: "user" | "group" | "page" | "event" | "blog" | "post";
  title: string;
  subtitle?: string;
  image?: string | null;
  url: string;
}

interface SearchSuggestionsProps {
  query: string;
  onSelect: () => void;
  onSearch: (query: string) => void;
}

export function SearchSuggestions({ query, onSelect, onSearch }: SearchSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const router = useRouter();

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem("recentSearches");
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Fetch suggestions when query changes
  useEffect(() => {
    if (query.length < 2) {
      setSuggestions([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    const timeoutId = setTimeout(async () => {
      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}&limit=5`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            const results: SearchResult[] = [];

            // Add users
            data.data.users.slice(0, 2).forEach((user: any) => {
              results.push({
                id: user.id,
                type: "user",
                title: user.name,
                subtitle: `@${user.username}`,
                image: user.image,
                url: `/user/${user.username}`
              });
            });

            // Add groups
            data.data.groups.slice(0, 2).forEach((group: any) => {
              results.push({
                id: group.id,
                type: "group",
                title: group.name,
                subtitle: group.description,
                image: group.coverImage,
                url: `/groups/${group.id}`
              });
            });

            // Add fan pages
            data.data.fanPages.slice(0, 2).forEach((page: any) => {
              results.push({
                id: page.id,
                type: "page",
                title: page.name,
                subtitle: page.description,
                image: page.profileImage,
                url: `/pages/${page.username}`
              });
            });

            // Add events
            data.data.events.slice(0, 1).forEach((event: any) => {
              results.push({
                id: event.id,
                type: "event",
                title: event.name,
                subtitle: event.location,
                image: event.coverImage,
                url: `/events/${event.id}`
              });
            });

            // Add blogs
            data.data.blogs.slice(0, 1).forEach((blog: any) => {
              results.push({
                id: blog.id,
                type: "blog",
                title: blog.title,
                subtitle: blog.author.name,
                image: blog.author.image,
                url: `/blogs/${blog.slug}`
              });
            });

            setSuggestions(results);
          }
        }
      } catch (error) {
        console.error("Error fetching suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query]);

  const getIcon = (type: string) => {
    switch (type) {
      case "user":
        return UserIcon;
      case "group":
        return UsersIcon;
      case "page":
        return NewspaperIcon;
      case "event":
        return CalendarIcon;
      case "blog":
        return DocumentTextIcon;
      default:
        return MagnifyingGlassIcon;
    }
  };

  const handleSuggestionClick = (suggestion: SearchResult) => {
    onSelect();
    router.push(suggestion.url);
  };

  const handleSearchAll = () => {
    if (query.trim()) {
      // Save to recent searches
      const newRecentSearches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
      setRecentSearches(newRecentSearches);
      localStorage.setItem("recentSearches", JSON.stringify(newRecentSearches));

      onSelect();
      onSearch(query);
    }
  };

  const handleRecentSearchClick = (searchTerm: string) => {
    onSelect();
    onSearch(searchTerm);
  };

  if (query.length < 2 && recentSearches.length === 0) {
    return null;
  }

  return (
    <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto">
      {query.length >= 2 && (
        <>
          {/* Search All Results */}
          <button
            onClick={handleSearchAll}
            className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 flex items-center space-x-3"
          >
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            <span className="text-sm text-gray-900">
              Search for "<span className="font-medium">{query}</span>"
            </span>
          </button>

          {/* Suggestions */}
          {isLoading ? (
            <div className="px-4 py-3 text-center text-sm text-gray-500">
              Searching...
            </div>
          ) : suggestions.length > 0 ? (
            <div>
              {suggestions.map((suggestion) => {
                const Icon = getIcon(suggestion.type);
                return (
                  <button
                    key={`${suggestion.type}-${suggestion.id}`}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3"
                  >
                    <div className="flex-shrink-0">
                      {suggestion.image ? (
                        <Image
                          src={suggestion.image}
                          alt={suggestion.title}
                          width={32}
                          height={32}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <Icon className="w-4 h-4 text-gray-500" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {suggestion.title}
                      </p>
                      {suggestion.subtitle && (
                        <p className="text-xs text-gray-500 truncate">
                          {suggestion.subtitle}
                        </p>
                      )}
                    </div>
                    <div className="flex-shrink-0">
                      <Icon className="w-4 h-4 text-gray-400" />
                    </div>
                  </button>
                );
              })}
            </div>
          ) : null}
        </>
      )}

      {/* Recent Searches */}
      {query.length < 2 && recentSearches.length > 0 && (
        <div>
          <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-100 flex items-center">
            <ClockIcon className="w-4 h-4 mr-2" />
            Recent Searches
          </div>
          {recentSearches.map((searchTerm, index) => (
            <button
              key={index}
              onClick={() => handleRecentSearchClick(searchTerm)}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3"
            >
              <ClockIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-900">{searchTerm}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
