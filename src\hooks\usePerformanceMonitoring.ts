"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { useFeatureFlags } from "@/lib/features/flags";

export interface PerformanceMetrics {
  // Connection metrics
  connectionLatency: number;
  reconnectionCount: number;
  connectionUptime: number;
  lastConnectionTime: number;
  
  // Message metrics
  messagesSent: number;
  messagesReceived: number;
  messageDeliveryTime: number;
  failedMessages: number;
  
  // Notification metrics
  notificationsSent: number;
  notificationsReceived: number;
  notificationDeliveryTime: number;
  
  // Real-time metrics
  averageLatency: number;
  maxLatency: number;
  minLatency: number;
  latencyHistory: number[];
  
  // Error metrics
  connectionErrors: number;
  messageErrors: number;
  notificationErrors: number;
  
  // Performance scores
  connectionScore: number; // 0-100
  deliveryScore: number; // 0-100
  overallScore: number; // 0-100
}

export interface ConnectionStatus {
  isConnected: boolean;
  isAuthenticated: boolean;
  connectionType: "websocket" | "polling" | "hybrid";
  lastPing: number;
  serverLatency: number;
  clientLatency: number;
}

interface UsePerformanceMonitoringOptions {
  enableLatencyTracking?: boolean;
  enableConnectionMonitoring?: boolean;
  enableErrorTracking?: boolean;
  reportingInterval?: number; // milliseconds
  maxLatencyHistory?: number;
}

export function usePerformanceMonitoring(options: UsePerformanceMonitoringOptions = {}) {
  const { data: session } = useSession();
  const { shouldTrackLatency, shouldReportConnectionStatus } = useFeatureFlags(session?.user?.id);
  
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    connectionLatency: 0,
    reconnectionCount: 0,
    connectionUptime: 0,
    lastConnectionTime: 0,
    messagesSent: 0,
    messagesReceived: 0,
    messageDeliveryTime: 0,
    failedMessages: 0,
    notificationsSent: 0,
    notificationsReceived: 0,
    notificationDeliveryTime: 0,
    averageLatency: 0,
    maxLatency: 0,
    minLatency: Infinity,
    latencyHistory: [],
    connectionErrors: 0,
    messageErrors: 0,
    notificationErrors: 0,
    connectionScore: 100,
    deliveryScore: 100,
    overallScore: 100
  });

  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isAuthenticated: false,
    connectionType: "polling",
    lastPing: 0,
    serverLatency: 0,
    clientLatency: 0
  });

  const startTimeRef = useRef<number>(Date.now());
  const reportingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const latencyMeasurements = useRef<number[]>([]);

  const {
    enableLatencyTracking = true,
    enableConnectionMonitoring = true,
    enableErrorTracking = true,
    reportingInterval = 30000, // 30 seconds
    maxLatencyHistory = 100
  } = options;

  const isEnabled = shouldTrackLatency() || shouldReportConnectionStatus();

  // Record latency measurement
  const recordLatency = useCallback((latency: number) => {
    if (!enableLatencyTracking || !isEnabled) return;

    latencyMeasurements.current.push(latency);
    
    setMetrics(prev => {
      const newHistory = [...prev.latencyHistory, latency].slice(-maxLatencyHistory);
      const avgLatency = newHistory.reduce((sum, l) => sum + l, 0) / newHistory.length;
      
      return {
        ...prev,
        averageLatency: avgLatency,
        maxLatency: Math.max(prev.maxLatency, latency),
        minLatency: Math.min(prev.minLatency, latency),
        latencyHistory: newHistory,
        connectionLatency: latency
      };
    });
  }, [enableLatencyTracking, isEnabled, maxLatencyHistory]);

  // Record connection event
  const recordConnectionEvent = useCallback((event: "connected" | "disconnected" | "reconnected" | "error") => {
    if (!enableConnectionMonitoring || !isEnabled) return;

    const now = Date.now();
    
    setMetrics(prev => {
      const updates: Partial<PerformanceMetrics> = {};
      
      switch (event) {
        case "connected":
          updates.lastConnectionTime = now;
          break;
        case "reconnected":
          updates.reconnectionCount = prev.reconnectionCount + 1;
          updates.lastConnectionTime = now;
          break;
        case "error":
          updates.connectionErrors = prev.connectionErrors + 1;
          break;
      }
      
      return { ...prev, ...updates };
    });

    if (event === "connected" || event === "reconnected") {
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: true,
        lastPing: now
      }));
    } else if (event === "disconnected") {
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: false
      }));
    }
  }, [enableConnectionMonitoring, isEnabled]);

  // Record message event
  const recordMessageEvent = useCallback((event: "sent" | "received" | "failed", deliveryTime?: number) => {
    if (!isEnabled) return;

    setMetrics(prev => {
      const updates: Partial<PerformanceMetrics> = {};
      
      switch (event) {
        case "sent":
          updates.messagesSent = prev.messagesSent + 1;
          break;
        case "received":
          updates.messagesReceived = prev.messagesReceived + 1;
          if (deliveryTime) {
            updates.messageDeliveryTime = (prev.messageDeliveryTime + deliveryTime) / 2;
          }
          break;
        case "failed":
          updates.failedMessages = prev.failedMessages + 1;
          updates.messageErrors = prev.messageErrors + 1;
          break;
      }
      
      return { ...prev, ...updates };
    });
  }, [isEnabled]);

  // Record notification event
  const recordNotificationEvent = useCallback((event: "sent" | "received" | "failed", deliveryTime?: number) => {
    if (!isEnabled) return;

    setMetrics(prev => {
      const updates: Partial<PerformanceMetrics> = {};
      
      switch (event) {
        case "sent":
          updates.notificationsSent = prev.notificationsSent + 1;
          break;
        case "received":
          updates.notificationsReceived = prev.notificationsReceived + 1;
          if (deliveryTime) {
            updates.notificationDeliveryTime = (prev.notificationDeliveryTime + deliveryTime) / 2;
          }
          break;
        case "failed":
          updates.notificationErrors = prev.notificationErrors + 1;
          break;
      }
      
      return { ...prev, ...updates };
    });
  }, [isEnabled]);

  // Update connection status
  const updateConnectionStatus = useCallback((status: Partial<ConnectionStatus>) => {
    if (!enableConnectionMonitoring || !isEnabled) return;

    setConnectionStatus(prev => ({ ...prev, ...status }));
  }, [enableConnectionMonitoring, isEnabled]);

  // Calculate performance scores
  const calculateScores = useCallback(() => {
    setMetrics(prev => {
      // Connection score based on uptime and latency
      const uptimeScore = Math.min(100, (prev.connectionUptime / (24 * 60 * 60 * 1000)) * 100); // 24h uptime = 100%
      const latencyScore = Math.max(0, 100 - (prev.averageLatency / 10)); // 1000ms = 0%, 0ms = 100%
      const errorScore = Math.max(0, 100 - (prev.connectionErrors * 10)); // Each error -10 points
      const connectionScore = (uptimeScore + latencyScore + errorScore) / 3;

      // Delivery score based on success rate and delivery time
      const totalMessages = prev.messagesSent + prev.notificationsSent;
      const totalErrors = prev.failedMessages + prev.messageErrors + prev.notificationErrors;
      const successRate = totalMessages > 0 ? ((totalMessages - totalErrors) / totalMessages) * 100 : 100;
      const deliveryTimeScore = Math.max(0, 100 - (prev.messageDeliveryTime / 50)); // 5000ms = 0%, 0ms = 100%
      const deliveryScore = (successRate + deliveryTimeScore) / 2;

      // Overall score
      const overallScore = (connectionScore + deliveryScore) / 2;

      return {
        ...prev,
        connectionScore: Math.round(connectionScore),
        deliveryScore: Math.round(deliveryScore),
        overallScore: Math.round(overallScore)
      };
    });
  }, []);

  // Update connection uptime
  useEffect(() => {
    if (!isEnabled || !connectionStatus.isConnected) return;

    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        connectionUptime: Date.now() - startTimeRef.current
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [connectionStatus.isConnected, isEnabled]);

  // Periodic reporting and score calculation
  useEffect(() => {
    if (!isEnabled) return;

    reportingIntervalRef.current = setInterval(() => {
      calculateScores();
      
      // Report metrics to analytics service if needed
      if (shouldReportConnectionStatus()) {
        reportMetrics();
      }
    }, reportingInterval);

    return () => {
      if (reportingIntervalRef.current) {
        clearInterval(reportingIntervalRef.current);
      }
    };
  }, [isEnabled, reportingInterval, calculateScores, shouldReportConnectionStatus]);

  // Report metrics to external service
  const reportMetrics = useCallback(async () => {
    try {
      // This would send metrics to your analytics service
      console.log("Performance Metrics Report:", {
        metrics,
        connectionStatus,
        timestamp: new Date().toISOString(),
        userId: session?.user?.id
      });
      
      // Example: Send to analytics API
      // await fetch('/api/analytics/performance', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ metrics, connectionStatus, userId: session?.user?.id })
      // });
    } catch (error) {
      console.error("Failed to report performance metrics:", error);
    }
  }, [metrics, connectionStatus, session?.user?.id]);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    return {
      status: metrics.overallScore >= 80 ? "excellent" : 
              metrics.overallScore >= 60 ? "good" : 
              metrics.overallScore >= 40 ? "fair" : "poor",
      score: metrics.overallScore,
      latency: metrics.averageLatency,
      uptime: metrics.connectionUptime,
      reliability: metrics.connectionScore,
      delivery: metrics.deliveryScore
    };
  }, [metrics]);

  // Reset metrics
  const resetMetrics = useCallback(() => {
    setMetrics({
      connectionLatency: 0,
      reconnectionCount: 0,
      connectionUptime: 0,
      lastConnectionTime: 0,
      messagesSent: 0,
      messagesReceived: 0,
      messageDeliveryTime: 0,
      failedMessages: 0,
      notificationsSent: 0,
      notificationsReceived: 0,
      notificationDeliveryTime: 0,
      averageLatency: 0,
      maxLatency: 0,
      minLatency: Infinity,
      latencyHistory: [],
      connectionErrors: 0,
      messageErrors: 0,
      notificationErrors: 0,
      connectionScore: 100,
      deliveryScore: 100,
      overallScore: 100
    });
    
    startTimeRef.current = Date.now();
    latencyMeasurements.current = [];
  }, []);

  return {
    // State
    metrics,
    connectionStatus,
    isEnabled,

    // Actions
    recordLatency,
    recordConnectionEvent,
    recordMessageEvent,
    recordNotificationEvent,
    updateConnectionStatus,
    resetMetrics,

    // Utilities
    getPerformanceSummary,
    calculateScores
  };
}
