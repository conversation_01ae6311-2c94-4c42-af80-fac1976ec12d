import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, products, storeReviews, users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and, desc, count, avg } from "drizzle-orm";

const storeUpdateSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  slug: z.string().min(3).max(100).regex(/^[a-zA-Z0-9-]+$/).optional(),
  description: z.string().max(1000).optional(),
  location: z.string().max(255).optional(),
  phone: z.string().max(50).optional(),
  email: z.string().email().max(255).optional(),
  website: z.string().max(255).optional(),
  logo: z.string().url().optional().nullable(),
  banner: z.string().url().optional().nullable(),
});

export async function GET(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const params = await context.params;
    const storeId = params.storeId;

    // Get store details
    const storeDetails = await db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        description: stores.description,
        logo: stores.logo,
        banner: stores.banner,
        location: stores.location,
        phone: stores.phone,
        email: stores.email,
        website: stores.website,
        isVerified: stores.isVerified,
        createdAt: stores.createdAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(stores)
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(eq(stores.id, storeId))
      .limit(1);

    if (storeDetails.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Get product count
    const productCountResult = await db
      .select({ count: count() })
      .from(products)
      .where(eq(products.storeId, storeId));

    // Get average rating
    const ratingResult = await db
      .select({ averageRating: avg(storeReviews.rating) })
      .from(storeReviews)
      .where(eq(storeReviews.storeId, storeId));

    // Get review count
    const reviewCountResult = await db
      .select({ count: count() })
      .from(storeReviews)
      .where(eq(storeReviews.storeId, storeId));

    const store = {
      ...storeDetails[0],
      productCount: productCountResult[0]?.count || 0,
      averageRating: ratingResult[0]?.averageRating || 0,
      reviewCount: reviewCountResult[0]?.count || 0,
    };

    return NextResponse.json(store);
  } catch (error) {
    console.error("Error fetching store:", error);
    return NextResponse.json(
      { message: "Error fetching store" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const storeId = params.storeId;
    const body = await request.json();
    const validatedData = storeUpdateSchema.parse(body);

    // Check if store exists and belongs to the user
    const existingStore = await db
      .select()
      .from(stores)
      .where(
        and(
          eq(stores.id, storeId),
          eq(stores.ownerId, session.user.id)
        )
      )
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found or you don't have permission to update it" },
        { status: 404 }
      );
    }

    // Check if slug is already taken (if updating slug)
    if (validatedData.slug && validatedData.slug !== existingStore[0].slug) {
      const slugExists = await db
        .select({ id: stores.id })
        .from(stores)
        .where(eq(stores.slug, validatedData.slug))
        .limit(1);

      if (slugExists.length > 0) {
        return NextResponse.json(
          { message: "Store slug already exists" },
          { status: 400 }
        );
      }
    }

    // Update store
    await db
      .update(stores)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(stores.id, storeId));

    // Get updated store
    const updatedStore = await db
      .select()
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    return NextResponse.json({
      message: "Store updated successfully",
      store: updatedStore[0],
    });
  } catch (error) {
    console.error("Error updating store:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Error updating store" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const storeId = params.storeId;

    // Check if store exists and belongs to the user
    const existingStore = await db
      .select()
      .from(stores)
      .where(
        and(
          eq(stores.id, storeId),
          eq(stores.ownerId, session.user.id)
        )
      )
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found or you don't have permission to delete it" },
        { status: 404 }
      );
    }

    // Check if store has products
    const productCount = await db
      .select({ count: count() })
      .from(products)
      .where(eq(products.storeId, storeId));

    if (productCount[0]?.count > 0) {
      return NextResponse.json(
        { message: "Cannot delete store with existing products. Please delete all products first." },
        { status: 400 }
      );
    }

    // Delete store (cascade will delete related records including settings)
    await db.delete(stores).where(eq(stores.id, storeId));

    return NextResponse.json({
      message: "Store deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting store:", error);
    return NextResponse.json(
      { message: "Error deleting store" },
      { status: 500 }
    );
  }
}
