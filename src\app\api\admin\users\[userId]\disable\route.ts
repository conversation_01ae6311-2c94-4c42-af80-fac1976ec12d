import { NextResponse } from "next/server";
import { validateApiSession } from "@/lib/utils/session-validation";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Disable user account (Admin only)
export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    // Validate session with admin requirement
    const { response: authError, user: currentUser } = await validateApiSession(true);
    if (authError) return authError;

    const { userId } = await context.params;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Prevent disabling admin users
    if (user.isAdmin) {
      return NextResponse.json(
        { message: "Cannot disable admin users" },
        { status: 400 }
      );
    }

    // Update user status to disabled
    await db.update(users)
      .set({
        status: 'disabled',
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Log the action
    console.log(`Admin ${currentUser.id} disabled user ${userId}`);

    return NextResponse.json({
      success: true,
      message: "User account disabled successfully",
    });
  } catch (error: any) {
    console.error("Error disabling user account:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to disable user account"
      },
      { status: 500 }
    );
  }
}
