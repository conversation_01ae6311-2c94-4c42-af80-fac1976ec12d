import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCommissions } from "@/lib/db/schema";
import { eq, count, sum, sql } from "drizzle-orm";

// Get user's commission statistics
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Total commissions count
    const totalCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(eq(referralCommissions.referrerId, userId));

    // Total earned amount (paid commissions)
    const totalEarnedResult = await db
      .select({ total: sum(referralCommissions.commissionAmount) })
      .from(referralCommissions)
      .where(
        sql`${referralCommissions.referrerId} = ${userId} AND ${referralCommissions.status} = 'paid'`
      );

    // Pending amount (approved but not paid)
    const pendingAmountResult = await db
      .select({ total: sum(referralCommissions.commissionAmount) })
      .from(referralCommissions)
      .where(
        sql`${referralCommissions.referrerId} = ${userId} AND ${referralCommissions.status} IN ('pending', 'approved')`
      );

    // Commission counts by status
    const pendingCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(
        sql`${referralCommissions.referrerId} = ${userId} AND ${referralCommissions.status} = 'pending'`
      );

    const approvedCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(
        sql`${referralCommissions.referrerId} = ${userId} AND ${referralCommissions.status} = 'approved'`
      );

    const paidCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(
        sql`${referralCommissions.referrerId} = ${userId} AND ${referralCommissions.status} = 'paid'`
      );

    // First purchase commissions
    const firstPurchaseCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(
        sql`${referralCommissions.referrerId} = ${userId} AND ${referralCommissions.isFirstPurchase} = true`
      );

    // Recurring purchase commissions
    const recurringCommissionsResult = await db
      .select({ count: count() })
      .from(referralCommissions)
      .where(
        sql`${referralCommissions.referrerId} = ${userId} AND ${referralCommissions.isFirstPurchase} = false`
      );

    const stats = {
      totalCommissions: totalCommissionsResult[0]?.count || 0,
      totalEarned: parseFloat(totalEarnedResult[0]?.total || '0'),
      pendingAmount: parseFloat(pendingAmountResult[0]?.total || '0'),
      pendingCommissions: pendingCommissionsResult[0]?.count || 0,
      approvedCommissions: approvedCommissionsResult[0]?.count || 0,
      paidCommissions: paidCommissionsResult[0]?.count || 0,
      firstPurchaseCommissions: firstPurchaseCommissionsResult[0]?.count || 0,
      recurringCommissions: recurringCommissionsResult[0]?.count || 0,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Error fetching user commission statistics:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch commission statistics"
      },
      { status: 500 }
    );
  }
}
