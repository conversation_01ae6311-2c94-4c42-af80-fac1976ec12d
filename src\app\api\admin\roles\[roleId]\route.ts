import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { adminRoles, adminPermissions, adminRolePermissions, users } from "@/lib/db/schema";
import { eq, and, ne, inArray } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const roleUpdateSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional(),
});

// Get a specific role with its permissions
export async function GET(
  req: Request,
  context: { params: Promise<{ roleId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { roleId } = params;

    // Get the role
    const role = await db.query.adminRoles.findFirst({
      where: eq(adminRoles.id, roleId),
      with: {
        permissions: {
          with: {
            permission: true,
          },
        },
      },
    });

    if (!role) {
      return NextResponse.json(
        { message: "Role not found" },
        { status: 404 }
      );
    }

    // Format the response
    const formattedRole = {
      id: role.id,
      name: role.name,
      description: role.description,
      isSystem: role.isSystem,
      createdAt: role.createdAt,
      permissions: role.permissions.map((rp) => rp.permission.code),
    };

    return NextResponse.json(formattedRole, { status: 200 });
  } catch (error) {
    console.error("Error fetching admin role:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a role
export async function PUT(
  req: Request,
  context: { params: Promise<{ roleId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { roleId } = params;
    const body = await req.json();

    // Validate the request body
    const validatedData = roleUpdateSchema.parse(body);

    // Check if the role exists
    const existingRole = await db.query.adminRoles.findFirst({
      where: eq(adminRoles.id, roleId),
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "Role not found" },
        { status: 404 }
      );
    }

    // Check if the role is a system role
    if (existingRole.isSystem) {
      return NextResponse.json(
        { message: "System roles cannot be modified" },
        { status: 403 }
      );
    }

    // Check if the new name already exists (if name is being updated)
    if (validatedData.name && validatedData.name !== existingRole.name) {
      const nameExists = await db.query.adminRoles.findFirst({
        where: and(
          eq(adminRoles.name, validatedData.name),
          ne(adminRoles.id, roleId)
        ),
      });

      if (nameExists) {
        return NextResponse.json(
          { message: "Role name already exists" },
          { status: 400 }
        );
      }
    }

    // Update the role
    const updateData: any = {};
    if (validatedData.name) updateData.name = validatedData.name;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    updateData.updatedAt = new Date();

    await db.update(adminRoles)
      .set(updateData)
      .where(eq(adminRoles.id, roleId));

    // Update permissions if provided
    if (validatedData.permissions) {
      // Delete existing permissions
      await db.delete(adminRolePermissions)
        .where(eq(adminRolePermissions.roleId, roleId));

      // Get permission IDs for the given permission codes
      const permissionRecords = validatedData.permissions && validatedData.permissions.length > 0
        ? await db.query.adminPermissions.findMany({
            where: inArray(adminPermissions.code, validatedData.permissions),
          })
        : [];

      // Create new role-permission associations
      if (permissionRecords.length > 0) {
        const rolePermissions = permissionRecords.map((permission) => ({
          id: uuidv4(),
          roleId: roleId,
          permissionId: permission.id,
          createdAt: new Date(),
        }));

        await db.insert(adminRolePermissions).values(rolePermissions);
      }
    }

    return NextResponse.json(
      { message: "Role updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating admin role:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a role
export async function DELETE(
  req: Request,
  context: { params: Promise<{ roleId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { roleId } = params;

    // Check if the role exists
    const existingRole = await db.query.adminRoles.findFirst({
      where: eq(adminRoles.id, roleId),
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "Role not found" },
        { status: 404 }
      );
    }

    // Check if the role is a system role
    if (existingRole.isSystem) {
      return NextResponse.json(
        { message: "System roles cannot be deleted" },
        { status: 403 }
      );
    }

    // Check if any users are using this role
    const usersWithRole = await db.query.users.findFirst({
      where: eq(users.adminRoleId, roleId),
    });

    if (usersWithRole) {
      return NextResponse.json(
        { message: "Cannot delete role that is assigned to users" },
        { status: 400 }
      );
    }

    // Delete role permissions first (due to foreign key constraints)
    await db.delete(adminRolePermissions)
      .where(eq(adminRolePermissions.roleId, roleId));

    // Delete the role
    await db.delete(adminRoles)
      .where(eq(adminRoles.id, roleId));

    return NextResponse.json(
      { message: "Role deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting admin role:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
