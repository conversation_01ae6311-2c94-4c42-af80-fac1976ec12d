import { db } from "./db";
import { siteSettings } from "./db/schema";
import { eq } from "drizzle-orm";
import { authConfig } from "./config";

interface OAuthConfig {
  googleClientId: string;
  googleClientSecret: string;
  googleOAuthEnabled: boolean;
  githubClientId: string;
  githubClientSecret: string;
  githubOAuthEnabled: boolean;
}

let cachedOAuthConfig: OAuthConfig | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 30 * 1000; // 30 seconds for faster updates

export async function getOAuthConfig(): Promise<OAuthConfig> {
  const now = Date.now();

  // Return cached config if it's still valid
  if (cachedOAuthConfig && (now - lastFetchTime) < CACHE_DURATION) {
    return cachedOAuthConfig;
  }

  try {
    // Fetch OAuth settings from database
    const oauthSettings = await db
      .select()
      .from(siteSettings)
      .where(eq(siteSettings.groupName, 'oauth'));

    // Convert to object format
    const settingsMap = oauthSettings.reduce((acc, setting) => {
      acc[setting.key] = setting.value || '';
      return acc;
    }, {} as Record<string, string>);

    // Create config object with fallbacks to environment variables
    const config: OAuthConfig = {
      googleClientId: settingsMap.google_client_id || authConfig.googleClientId,
      googleClientSecret: settingsMap.google_client_secret || authConfig.googleClientSecret,
      googleOAuthEnabled: settingsMap.google_oauth_enabled === 'true',
      githubClientId: settingsMap.github_client_id || authConfig.githubClientId,
      githubClientSecret: settingsMap.github_client_secret || authConfig.githubClientSecret,
      githubOAuthEnabled: settingsMap.github_oauth_enabled === 'true',
    };

    // Cache the config
    cachedOAuthConfig = config;
    lastFetchTime = now;

    return config;
  } catch (error) {
    console.error('Error fetching OAuth config from database:', error);

    // Fallback to environment variables
    const fallbackConfig: OAuthConfig = {
      googleClientId: authConfig.googleClientId,
      googleClientSecret: authConfig.googleClientSecret,
      googleOAuthEnabled: !!(authConfig.googleClientId && authConfig.googleClientSecret),
      githubClientId: authConfig.githubClientId,
      githubClientSecret: authConfig.githubClientSecret,
      githubOAuthEnabled: !!(authConfig.githubClientId && authConfig.githubClientSecret),
    };

    return fallbackConfig;
  }
}

// Function to invalidate cache (call this when OAuth settings are updated)
export function invalidateOAuthCache(): void {
  cachedOAuthConfig = null;
  lastFetchTime = 0;
}


