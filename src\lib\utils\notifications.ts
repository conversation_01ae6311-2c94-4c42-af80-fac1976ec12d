interface NotificationData {
  id: string;
  type: string;
  recipientId: string;
  senderId: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image: string;
  };
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

import { emitNotification as emitToPartyKit, createNotificationPayload } from '@/lib/partykit/server-utils';

// Enhanced notification emission with PartyKit integration
export async function emitNotification(notification: NotificationData) {
  try {
    // Create PartyKit payload
    const payload = createNotificationPayload(notification, notification.sender);

    // Emit to PartyKit for real-time delivery
    const success = await emitToPartyKit(payload);

    if (success) {
      console.log('📡 Real-time notification sent:', notification.type);
    } else {
      console.log('📡 Notification created (real-time failed, using polling fallback):', notification.type);
    }
  } catch (error) {
    console.error('Error emitting real-time notification:', error);
    console.log('📡 Notification created (real-time error, using polling fallback):', notification.type);
  }
}

// Helper function to create notification data
export function createNotificationData(
  type: string,
  recipientId: string,
  senderId: string,
  options: {
    postId?: string;
    commentId?: string;
    messageId?: string;
    friendshipId?: string;
    fanPageId?: string;
    fanPagePostId?: string;
    groupId?: string;
    eventId?: string;
    storeId?: string;
    productId?: string;
    sender?: {
      id: string;
      name: string;
      image: string;
    };
  } = {}
): NotificationData {
  return {
    id: '', // Will be set by the database
    type,
    recipientId,
    senderId,
    read: false,
    createdAt: new Date().toISOString(),
    ...options,
  };
}
