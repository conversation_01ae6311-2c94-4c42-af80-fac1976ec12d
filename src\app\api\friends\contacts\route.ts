import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptions, users } from "@/lib/db/schema";
import { eq, and, like, or, desc, sql } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const search = url.searchParams.get('search');
    const onlineOnly = url.searchParams.get('onlineOnly') === 'true';

    // Get users that the current user is subscribed to (their contacts)
    let contactsQuery = db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
        bio: users.bio,
        location: users.location,
        showOnlineStatus: users.showOnlineStatus,
        updatedAt: users.updatedAt,
        createdAt: users.createdAt,
      })
      .from(subscriptions)
      .innerJoin(users, eq(subscriptions.targetUserId, users.id))
      .where(
        and(
          eq(subscriptions.subscriberId, session.user.id),
          eq(users.isActive, true)
        )
      );

    // Add search filter if provided
    if (search) {
      contactsQuery = contactsQuery.where(
        and(
          eq(subscriptions.subscriberId, session.user.id),
          eq(users.isActive, true),
          or(
            like(users.name, `%${search}%`),
            like(users.username, `%${search}%`)
          )
        )
      );
    }

    // For now, we'll consider users "online" if they've been active in the last 5 minutes
    // This is a simplified approach - in a real app you'd have a proper online status system
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    if (onlineOnly) {
      contactsQuery = contactsQuery.where(
        and(
          eq(subscriptions.subscriberId, session.user.id),
          eq(users.isActive, true),
          sql`${users.updatedAt} > ${fiveMinutesAgo}`,
          eq(users.showOnlineStatus, true)
        )
      );
    }

    // Add ordering, limit and offset
    const contacts = await contactsQuery
      .orderBy(desc(users.updatedAt))
      .limit(limit)
      .offset(offset);

    // Get total count for stats
    const totalCountQuery = await db
      .select({ count: sql<number>`count(*)` })
      .from(subscriptions)
      .innerJoin(users, eq(subscriptions.targetUserId, users.id))
      .where(
        and(
          eq(subscriptions.subscriberId, session.user.id),
          eq(users.isActive, true)
        )
      );

    const totalCount = totalCountQuery[0]?.count || 0;

    // Get online count (users active in last 5 minutes with online status enabled)
    const onlineCountQuery = await db
      .select({ count: sql<number>`count(*)` })
      .from(subscriptions)
      .innerJoin(users, eq(subscriptions.targetUserId, users.id))
      .where(
        and(
          eq(subscriptions.subscriberId, session.user.id),
          eq(users.isActive, true),
          sql`${users.updatedAt} > ${fiveMinutesAgo}`,
          eq(users.showOnlineStatus, true)
        )
      );

    const onlineCount = onlineCountQuery[0]?.count || 0;

    // Format contacts with online status
    const formattedContacts = contacts.map(contact => {
      const isOnline = contact.showOnlineStatus && 
                      contact.updatedAt && 
                      contact.updatedAt > fiveMinutesAgo;

      return {
        id: contact.id,
        name: contact.name,
        username: contact.username,
        image: contact.image,
        bio: contact.bio,
        location: contact.location,
        isOnline,
        lastSeen: contact.updatedAt,
        showOnlineStatus: contact.showOnlineStatus,
      };
    });

    return NextResponse.json({
      contacts: formattedContacts,
      stats: {
        total: totalCount,
        online: onlineCount,
        offline: totalCount - onlineCount,
      },
      pagination: {
        limit,
        hasMore: contacts.length === limit,
      },
    });

  } catch (error) {
    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
