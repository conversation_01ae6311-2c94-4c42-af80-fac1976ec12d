"use client";

import React, { useState } from 'react';
import {
  XMarkIcon,
  Cog6ToothIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowsUpDownIcon,
  PlusIcon,
  MinusIcon
} from '@heroicons/react/24/outline';

interface ToolbarButton {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  group: string;
  enabled: boolean;
  order: number;
}

interface ToolbarCustomizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  toolbarConfig: ToolbarButton[];
  onSave: (config: ToolbarButton[]) => void;
}

export const ToolbarCustomizationModal: React.FC<ToolbarCustomizationModalProps> = ({
  isOpen,
  onClose,
  toolbarConfig,
  onSave
}) => {
  const [config, setConfig] = useState<ToolbarButton[]>([...toolbarConfig]);
  const [selectedGroup, setSelectedGroup] = useState<string>('all');

  const groups = Array.from(new Set(config.map(btn => btn.group)));
  const filteredButtons = selectedGroup === 'all' 
    ? config 
    : config.filter(btn => btn.group === selectedGroup);

  const toggleButton = (id: string) => {
    setConfig(prev => prev.map(btn => 
      btn.id === id ? { ...btn, enabled: !btn.enabled } : btn
    ));
  };

  const moveButton = (id: string, direction: 'up' | 'down') => {
    setConfig(prev => {
      const buttons = [...prev];
      const index = buttons.findIndex(btn => btn.id === id);
      
      if (index === -1) return prev;
      
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      if (newIndex < 0 || newIndex >= buttons.length) return prev;
      
      // Swap the buttons
      [buttons[index], buttons[newIndex]] = [buttons[newIndex], buttons[index]];
      
      // Update order values
      buttons.forEach((btn, idx) => {
        btn.order = idx;
      });
      
      return buttons;
    });
  };

  const resetToDefault = () => {
    setConfig(toolbarConfig.map(btn => ({ ...btn, enabled: true })));
  };

  const handleSave = () => {
    onSave(config);
    onClose();
  };

  const enabledCount = config.filter(btn => btn.enabled).length;
  const totalCount = config.length;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Cog6ToothIcon className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Customize Toolbar</h3>
            <span className="text-sm text-gray-500">
              ({enabledCount}/{totalCount} tools enabled)
            </span>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Sidebar */}
          <div className="w-1/4 border-r border-gray-200 p-4">
            <h4 className="font-medium text-gray-700 mb-3">Filter by Group</h4>
            <div className="space-y-2">
              <button
                onClick={() => setSelectedGroup('all')}
                className={`w-full text-left px-3 py-2 rounded text-sm ${
                  selectedGroup === 'all'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                All Tools ({totalCount})
              </button>
              {groups.map(group => {
                const groupCount = config.filter(btn => btn.group === group).length;
                const enabledInGroup = config.filter(btn => btn.group === group && btn.enabled).length;
                
                return (
                  <button
                    key={group}
                    onClick={() => setSelectedGroup(group)}
                    className={`w-full text-left px-3 py-2 rounded text-sm ${
                      selectedGroup === group
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="capitalize">{group}</span>
                      <span className="text-xs">
                        {enabledInGroup}/{groupCount}
                      </span>
                    </div>
                  </button>
                );
              })}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200">
              <h4 className="font-medium text-gray-700 mb-3">Quick Actions</h4>
              <div className="space-y-2">
                <button
                  onClick={() => setConfig(prev => prev.map(btn => ({ ...btn, enabled: true })))}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-green-600 hover:bg-green-50 rounded"
                >
                  <PlusIcon className="h-4 w-4" />
                  <span>Enable All</span>
                </button>
                <button
                  onClick={() => setConfig(prev => prev.map(btn => ({ ...btn, enabled: false })))}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded"
                >
                  <MinusIcon className="h-4 w-4" />
                  <span>Disable All</span>
                </button>
                <button
                  onClick={resetToDefault}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded"
                >
                  <ArrowsUpDownIcon className="h-4 w-4" />
                  <span>Reset Default</span>
                </button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-4 overflow-y-auto">
            <div className="mb-4">
              <h4 className="font-medium text-gray-700 mb-2">
                {selectedGroup === 'all' ? 'All Toolbar Tools' : `${selectedGroup.charAt(0).toUpperCase() + selectedGroup.slice(1)} Tools`}
              </h4>
              <p className="text-sm text-gray-500">
                Toggle tools on/off and reorder them by dragging or using the arrow buttons.
              </p>
            </div>

            <div className="space-y-2">
              {filteredButtons
                .sort((a, b) => a.order - b.order)
                .map((button, index) => (
                <div
                  key={button.id}
                  className={`flex items-center justify-between p-3 border rounded-lg transition-colors ${
                    button.enabled
                      ? 'border-gray-200 bg-white'
                      : 'border-gray-100 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => toggleButton(button.id)}
                      className={`p-1 rounded ${
                        button.enabled
                          ? 'text-green-600 hover:bg-green-100'
                          : 'text-gray-400 hover:bg-gray-100'
                      }`}
                    >
                      {button.enabled ? (
                        <EyeIcon className="h-4 w-4" />
                      ) : (
                        <EyeSlashIcon className="h-4 w-4" />
                      )}
                    </button>
                    
                    <div className={`p-2 rounded ${button.enabled ? 'bg-gray-100' : 'bg-gray-200'}`}>
                      <button.icon className={`h-4 w-4 ${button.enabled ? 'text-gray-700' : 'text-gray-400'}`} />
                    </div>
                    
                    <div>
                      <div className={`font-medium ${button.enabled ? 'text-gray-800' : 'text-gray-500'}`}>
                        {button.title}
                      </div>
                      <div className="text-xs text-gray-500 capitalize">
                        {button.group} group
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => moveButton(button.id, 'up')}
                      disabled={index === 0}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Move up"
                    >
                      <ArrowsUpDownIcon className="h-4 w-4 rotate-180" />
                    </button>
                    <button
                      onClick={() => moveButton(button.id, 'down')}
                      disabled={index === filteredButtons.length - 1}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Move down"
                    >
                      <ArrowsUpDownIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Preview Panel */}
          <div className="w-1/3 border-l border-gray-200 p-4 bg-gray-50">
            <h4 className="font-medium text-gray-700 mb-3">Toolbar Preview</h4>
            <div className="bg-white border border-gray-200 rounded p-3">
              <div className="flex flex-wrap gap-1">
                {config
                  .filter(btn => btn.enabled)
                  .sort((a, b) => a.order - b.order)
                  .map((button, index, enabledButtons) => {
                    const isLastInGroup = index === enabledButtons.length - 1 || 
                      enabledButtons[index + 1]?.group !== button.group;
                    
                    return (
                      <React.Fragment key={button.id}>
                        <div className="p-1 hover:bg-gray-100 rounded" title={button.title}>
                          <button.icon className="h-4 w-4 text-gray-600" />
                        </div>
                        {isLastInGroup && index < enabledButtons.length - 1 && (
                          <div className="w-px h-6 bg-gray-300 mx-1" />
                        )}
                      </React.Fragment>
                    );
                  })}
              </div>
            </div>
            
            <div className="mt-4 text-xs text-gray-500">
              <p className="mb-2">This is how your toolbar will look with the current settings.</p>
              <div className="space-y-1">
                <div>• Enabled tools: {enabledCount}</div>
                <div>• Disabled tools: {totalCount - enabledCount}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={resetToDefault}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded"
          >
            Reset
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default ToolbarCustomizationModal;
