import { MainLayout } from "@/components/layout/MainLayout";
import { PublicLayout } from "@/components/layout/PublicLayout";
import { getCurrentUser } from "@/lib/utils/auth";
import { ModernPostForm } from "@/components/feed/ModernPostForm";
import { NewsFeed } from "@/components/feed/NewsFeed";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { FeedErrorBoundary, SidebarErrorBoundary } from "@/components/error/ErrorBoundary";
import Link from "next/link";

export const dynamic = 'force-dynamic';

export default async function HomePage() {
  try {
    const user = await getCurrentUser();

    // If user is not logged in, show landing page
    if (!user) {
      return (
        <PublicLayout>
          {/* Hero Section */}
          <div className="relative isolate overflow-hidden bg-gradient-to-b from-blue-50 to-white">
            {/* Background decorative elements */}
            <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80" aria-hidden="true">
              <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-400 to-indigo-600 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]" style={{ clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)' }}></div>
            </div>

            <div className="mx-auto max-w-7xl px-6 pb-24 pt-10 sm:pb-32 lg:flex lg:px-8 lg:py-40">
              <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8">
                <div className="mt-24 sm:mt-32 lg:mt-16">
                  <div className="inline-flex animate-pulse space-x-6">
                    <span className="rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 px-3 py-1 text-sm font-semibold leading-6 text-white ring-1 ring-inset ring-blue-600/10">
                      What&apos;s new
                    </span>
                    <span className="inline-flex items-center space-x-2 text-sm font-medium leading-6 text-gray-600">
                      <span>Just launched</span>
                    </span>
                  </div>
                </div>
                <h1 className="mt-10 text-4xl font-extrabold tracking-tight sm:text-6xl bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700">
                  Connect with friends and the world around you
                </h1>
                <p className="mt-6 text-lg leading-8 text-gray-600">
                  HIFNF is a social media platform that helps you connect and share with the people in your life. Join today to discover meaningful content and conversations.
                </p>
                <div className="mt-10 flex flex-col sm:flex-row items-center gap-x-6 gap-y-4">
                  <Link
                    href="/register"
                    className="w-full sm:w-auto rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-5 py-3 text-center text-sm font-semibold text-white shadow-lg shadow-blue-500/20 hover:shadow-blue-500/40 hover:scale-105 transition-all duration-300 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                  >
                    Get started for free
                  </Link>
                  <Link
                    href="/login"
                    className="w-full sm:w-auto flex items-center justify-center text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-300"
                  >
                    Already have an account? Log in <span className="ml-1 transition-transform duration-300 group-hover:translate-x-1" aria-hidden="true">→</span>
                  </Link>
                </div>

                {/* Social proof */}
                <div className="mt-10 flex items-center gap-x-6 text-sm text-gray-500">
                  <div className="flex -space-x-2">
                    <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="User" className="h-8 w-8 rounded-full ring-2 ring-white" />
                    <img src="https://randomuser.me/api/portraits/men/44.jpg" alt="User" className="h-8 w-8 rounded-full ring-2 ring-white" />
                    <img src="https://randomuser.me/api/portraits/women/55.jpg" alt="User" className="h-8 w-8 rounded-full ring-2 ring-white" />
                    <img src="https://randomuser.me/api/portraits/men/21.jpg" alt="User" className="h-8 w-8 rounded-full ring-2 ring-white" />
                  </div>
                  <div>Join <span className="font-semibold text-blue-600">10,000+</span> users already on HIFNF</div>
                </div>
              </div>
              <div className="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32">
                <div className="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
                  <div className="-m-2 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-2 ring-1 ring-inset ring-blue-900/10 lg:-m-4 lg:rounded-2xl lg:p-4 shadow-2xl transition-all duration-500 hover:shadow-blue-500/20">
                    <img
                      src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
                      alt="HIFNF social platform screenshot"
                      width={2432}
                      height={1442}
                      className="w-[76rem] rounded-md shadow-2xl ring-1 ring-blue-900/10 transition-transform duration-500 hover:scale-[1.01]"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom decorative element */}
            <div className="absolute inset-x-0 bottom-0 -z-10 transform-gpu overflow-hidden blur-3xl" aria-hidden="true">
              <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-400 to-indigo-600 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" style={{ clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)' }}></div>
            </div>
          </div>

          {/* Features Section */}
          <div className="bg-gradient-to-b from-white to-blue-50 py-24 sm:py-32">
            <div className="mx-auto max-w-7xl px-6 lg:px-8">
              <div className="mx-auto max-w-2xl lg:text-center">
                <h2 className="inline-flex items-center justify-center rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold leading-6 text-blue-600 ring-1 ring-inset ring-blue-600/10">Connect Faster</h2>
                <p className="mt-6 text-3xl font-bold tracking-tight sm:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700">
                  Everything you need to stay connected
                </p>
                <p className="mt-6 text-lg leading-8 text-gray-600">
                  HIFNF provides a comprehensive platform for social interaction, content sharing, and community building.
                </p>
              </div>

              <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
                <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                  {/* Feature 1 */}
                  <div className="group relative overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl hover:ring-blue-500 hover:-translate-y-1">
                    <div className="h-2 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                    <div className="p-8">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-6 group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300">
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Share Updates</h3>
                      <p className="mt-4 text-base leading-7 text-gray-600">
                        Share your thoughts, photos, and videos with friends and family in real-time. Keep everyone updated on your latest adventures.
                      </p>
                    </div>
                  </div>

                  {/* Feature 2 */}
                  <div className="group relative overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl hover:ring-blue-500 hover:-translate-y-1">
                    <div className="h-2 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                    <div className="p-8">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-6 group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300">
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Advanced Privacy</h3>
                      <p className="mt-4 text-base leading-7 text-gray-600">
                        Control who sees your content with advanced privacy settings and granular controls. Your data, your rules.
                      </p>
                    </div>
                  </div>

                  {/* Feature 3 */}
                  <div className="group relative overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl hover:ring-blue-500 hover:-translate-y-1">
                    <div className="h-2 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                    <div className="p-8">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-6 group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300">
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M7.864 4.243A7.5 7.5 0 0119.5 10.5c0 2.92-.556 5.709-1.568 8.268M5.742 6.364A7.465 7.465 0 004.5 10.5a7.464 7.464 0 01-1.15 3.993m1.989 3.559A11.209 11.209 0 008.25 10.5a3.75 3.75 0 117.5 0c0 .527-.021 1.049-.064 1.565M12 10.5a14.94 14.94 0 01-3.6 9.75m6.633-4.596a18.666 18.666 0 01-2.485 5.33" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Real-time Messaging</h3>
                      <p className="mt-4 text-base leading-7 text-gray-600">
                        Connect with friends through instant messaging and stay in touch wherever you are. Never miss an important conversation.
                      </p>
                    </div>
                  </div>

                  {/* Feature 4 */}
                  <div className="group relative overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl hover:ring-blue-500 hover:-translate-y-1">
                    <div className="h-2 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                    <div className="p-8">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-6 group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300">
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Community Groups</h3>
                      <p className="mt-4 text-base leading-7 text-gray-600">
                        Join groups based on your interests and connect with like-minded people. Discover communities that share your passions.
                      </p>
                    </div>
                  </div>

                  {/* Feature 5 */}
                  <div className="group relative overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl hover:ring-blue-500 hover:-translate-y-1">
                    <div className="h-2 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                    <div className="p-8">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-6 group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300">
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Smart Notifications</h3>
                      <p className="mt-4 text-base leading-7 text-gray-600">
                        Stay updated with intelligent notifications that keep you informed about the activities that matter most to you.
                      </p>
                    </div>
                  </div>

                  {/* Feature 6 */}
                  <div className="group relative overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl hover:ring-blue-500 hover:-translate-y-1">
                    <div className="h-2 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                    <div className="p-8">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-6 group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300">
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Personalized Feed</h3>
                      <p className="mt-4 text-base leading-7 text-gray-600">
                        Experience a news feed tailored to your interests and connections, ensuring you never miss important updates.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Testimonials Section */}
          <div className="bg-white py-24 sm:py-32">
            <div className="mx-auto max-w-7xl px-6 lg:px-8">
              <div className="mx-auto max-w-xl text-center">
                <h2 className="text-lg font-semibold leading-8 tracking-tight text-blue-600">Testimonials</h2>
                <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  Loved by users worldwide
                </p>
              </div>
              <div className="mx-auto mt-16 flow-root max-w-2xl sm:mt-20 lg:mx-0 lg:max-w-none">
                <div className="-mt-8 sm:-mx-4 sm:columns-2 sm:text-[0] lg:columns-3">
                  {/* Testimonial 1 */}
                  <div className="pt-8 sm:inline-block sm:w-full sm:px-4">
                    <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6 ring-1 ring-gray-200 hover:bg-blue-50 hover:ring-blue-500 transition-all duration-300">
                      <blockquote className="text-gray-700">
                        <p>&quot;HIFNF has completely changed how I stay in touch with friends and family. The interface is intuitive, and I love how easy it is to share moments from my life.&quot;</p>
                      </blockquote>
                      <figcaption className="mt-6 flex items-center gap-x-4">
                        <img className="h-10 w-10 rounded-full bg-gray-50" src="https://randomuser.me/api/portraits/women/32.jpg" alt="" />
                        <div>
                          <div className="font-semibold text-gray-900">Sarah Johnson</div>
                          <div className="text-gray-600">@sarahj</div>
                        </div>
                      </figcaption>
                    </figure>
                  </div>

                  {/* Testimonial 2 */}
                  <div className="pt-8 sm:inline-block sm:w-full sm:px-4">
                    <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6 ring-1 ring-gray-200 hover:bg-blue-50 hover:ring-blue-500 transition-all duration-300">
                      <blockquote className="text-gray-700">
                        <p>&quot;The privacy controls on HIFNF are next level. I can share exactly what I want with exactly who I want. It&apos;s refreshing to have that kind of control.&quot;</p>
                      </blockquote>
                      <figcaption className="mt-6 flex items-center gap-x-4">
                        <img className="h-10 w-10 rounded-full bg-gray-50" src="https://randomuser.me/api/portraits/men/44.jpg" alt="" />
                        <div>
                          <div className="font-semibold text-gray-900">Michael Chen</div>
                          <div className="text-gray-600">@mikechen</div>
                        </div>
                      </figcaption>
                    </figure>
                  </div>

                  {/* Testimonial 3 */}
                  <div className="pt-8 sm:inline-block sm:w-full sm:px-4">
                    <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6 ring-1 ring-gray-200 hover:bg-blue-50 hover:ring-blue-500 transition-all duration-300">
                      <blockquote className="text-gray-700">
                        <p>&quot;I&apos;ve found so many communities that share my interests on HIFNF. The groups feature has connected me with people I would never have met otherwise.&quot;</p>
                      </blockquote>
                      <figcaption className="mt-6 flex items-center gap-x-4">
                        <img className="h-10 w-10 rounded-full bg-gray-50" src="https://randomuser.me/api/portraits/women/55.jpg" alt="" />
                        <div>
                          <div className="font-semibold text-gray-900">Priya Patel</div>
                          <div className="text-gray-600">@priyap</div>
                        </div>
                      </figcaption>
                    </figure>
                  </div>

                  {/* Testimonial 4 */}
                  <div className="pt-8 sm:inline-block sm:w-full sm:px-4">
                    <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6 ring-1 ring-gray-200 hover:bg-blue-50 hover:ring-blue-500 transition-all duration-300">
                      <blockquote className="text-gray-700">
                        <p>&quot;The messaging system is fantastic. I love being able to chat with friends without switching to another app. Everything I need is right here.&quot;</p>
                      </blockquote>
                      <figcaption className="mt-6 flex items-center gap-x-4">
                        <img className="h-10 w-10 rounded-full bg-gray-50" src="https://randomuser.me/api/portraits/men/21.jpg" alt="" />
                        <div>
                          <div className="font-semibold text-gray-900">James Wilson</div>
                          <div className="text-gray-600">@jwilson</div>
                        </div>
                      </figcaption>
                    </figure>
                  </div>

                  {/* Testimonial 5 */}
                  <div className="pt-8 sm:inline-block sm:w-full sm:px-4">
                    <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6 ring-1 ring-gray-200 hover:bg-blue-50 hover:ring-blue-500 transition-all duration-300">
                      <blockquote className="text-gray-700">
                        <p>&quot;As a content creator, I appreciate how HIFNF makes it easy to share my work and connect with my audience. The engagement is much better than other platforms.&quot;</p>
                      </blockquote>
                      <figcaption className="mt-6 flex items-center gap-x-4">
                        <img className="h-10 w-10 rounded-full bg-gray-50" src="https://randomuser.me/api/portraits/women/67.jpg" alt="" />
                        <div>
                          <div className="font-semibold text-gray-900">Elena Rodriguez</div>
                          <div className="text-gray-600">@elenacreates</div>
                        </div>
                      </figcaption>
                    </figure>
                  </div>

                  {/* Testimonial 6 */}
                  <div className="pt-8 sm:inline-block sm:w-full sm:px-4">
                    <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6 ring-1 ring-gray-200 hover:bg-blue-50 hover:ring-blue-500 transition-all duration-300">
                      <blockquote className="text-gray-700">
                        <p>&quot;The personalized feed is spot on. I always see content that&apos;s relevant to me, and I&apos;ve discovered so many interesting people to follow.&quot;</p>
                      </blockquote>
                      <figcaption className="mt-6 flex items-center gap-x-4">
                        <img className="h-10 w-10 rounded-full bg-gray-50" src="https://randomuser.me/api/portraits/men/76.jpg" alt="" />
                        <div>
                          <div className="font-semibold text-gray-900">David Kim</div>
                          <div className="text-gray-600">@davidk</div>
                        </div>
                      </figcaption>
                    </figure>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 py-24 sm:py-32">
            <div className="mx-auto max-w-7xl px-6 lg:px-8">
              <div className="mx-auto max-w-2xl lg:max-w-none">
                <div className="text-center">
                  <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                    Trusted by millions worldwide
                  </h2>
                  <p className="mt-4 text-lg leading-8 text-blue-100">
                    Join our growing community and experience the difference
                  </p>
                </div>
                <dl className="mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4">
                  <div className="flex flex-col bg-white/5 backdrop-blur-sm p-8">
                    <dt className="text-sm font-semibold leading-6 text-blue-100">Active Users</dt>
                    <dd className="order-first text-3xl font-semibold tracking-tight text-white">10M+</dd>
                  </div>
                  <div className="flex flex-col bg-white/5 backdrop-blur-sm p-8">
                    <dt className="text-sm font-semibold leading-6 text-blue-100">Communities</dt>
                    <dd className="order-first text-3xl font-semibold tracking-tight text-white">250K+</dd>
                  </div>
                  <div className="flex flex-col bg-white/5 backdrop-blur-sm p-8">
                    <dt className="text-sm font-semibold leading-6 text-blue-100">Countries</dt>
                    <dd className="order-first text-3xl font-semibold tracking-tight text-white">150+</dd>
                  </div>
                  <div className="flex flex-col bg-white/5 backdrop-blur-sm p-8">
                    <dt className="text-sm font-semibold leading-6 text-blue-100">Messages Sent Daily</dt>
                    <dd className="order-first text-3xl font-semibold tracking-tight text-white">500M+</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-white">
            <div className="mx-auto max-w-7xl py-24 sm:px-6 sm:py-32 lg:px-8">
              <div className="relative isolate overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16">
                <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">
                  Ready to dive in?<br />
                  Start your journey today.
                </h2>
                <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100">
                  Join millions of users already connecting and sharing on HIFNF. Sign up is free and only takes a minute.
                </p>
                <div className="mt-10 flex items-center justify-center gap-x-6">
                  <Link
                    href="/register"
                    className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-blue-600 shadow-sm hover:bg-blue-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-all duration-300 hover:scale-105"
                  >
                    Get started for free
                  </Link>
                  <Link href="/login" className="text-sm font-semibold leading-6 text-white hover:text-blue-100 transition-colors duration-300">
                    Already have an account? Log in <span aria-hidden="true">→</span>
                  </Link>
                </div>
                <svg
                  viewBox="0 0 1024 1024"
                  className="absolute left-1/2 top-1/2 -z-10 h-[64rem] w-[64rem] -translate-x-1/2 -translate-y-1/2 [mask-image:radial-gradient(closest-side,white,transparent)]"
                  aria-hidden="true"
                >
                  <circle cx={512} cy={512} r={512} fill="url(#gradient)" fillOpacity="0.15" />
                  <defs>
                    <radialGradient id="gradient">
                      <stop stopColor="white" />
                      <stop offset={1} stopColor="white" />
                    </radialGradient>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        </PublicLayout>
      );
    }

    // If user is logged in, show the regular home page
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-2 py-3 sm:px-4 sm:py-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-3 lg:gap-0">
            {/* Left sidebar - Hidden on mobile, 20% on desktop */}
            <div className="hidden lg:block lg:w-[20%] mb-5 lg:mb-0">
              <SidebarErrorBoundary>
                <LeftSidebar />
              </SidebarErrorBoundary>
              {/* This is an empty div that takes up the same space as the fixed sidebar */}
              <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
            </div>

            {/* Gap between left sidebar and news feed - 5% */}
            <div className="hidden lg:block lg:w-[5%]"></div>

            {/* Main content - Full width on mobile, 41% (530px) on desktop */}
            <div className="w-full lg:w-[41%] space-y-3 sm:space-y-5 mb-5 lg:mb-0">
              {/* Create Post Form */}
              <div className="transform transition-all duration-300 hover:translate-y-[-2px]">
                <FeedErrorBoundary>
                  <ModernPostForm />
                </FeedErrorBoundary>
              </div>

              {/* News Feed */}
              <div className="pb-6 sm:pb-10">
                <FeedErrorBoundary>
                  <NewsFeed />
                </FeedErrorBoundary>
              </div>
            </div>

            {/* Gap between news feed and right sidebar - 3% */}
            <div className="hidden lg:block lg:w-[3%]"></div>

            {/* Right sidebar - Hidden on mobile, 26% on desktop */}
            <div className="hidden lg:block lg:w-[26%]">
              <SidebarErrorBoundary>
                <RightSidebar />
              </SidebarErrorBoundary>
              {/* This is an empty div that takes up the same space as the fixed sidebar */}
              <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  } catch (error) {
    console.error("Error in HomePage:", error);
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4 text-center">
        <h1 className="mb-4 text-2xl font-bold text-red-600">Database Connection Error</h1>
        <p className="mb-6 max-w-md text-gray-600">
          There was an error connecting to the database. Please make sure your MySQL database is running and properly configured.
        </p>
        <div className="rounded-lg bg-white p-6 shadow">
          <h2 className="mb-4 text-lg font-semibold text-gray-900">Setup Instructions</h2>
          <ol className="list-decimal space-y-2 text-left text-gray-600">
            <li>Make sure MySQL is installed and running</li>
            <li>Check your .env file has the correct database credentials</li>
            <li>Run <code className="rounded bg-gray-100 px-2 py-1 text-sm font-mono">npm run setup-db</code> to set up the database tables</li>
            <li>Restart the application</li>
          </ol>
        </div>
      </div>
    );
  }


}
