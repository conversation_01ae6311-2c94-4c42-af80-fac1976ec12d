import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogMonetization } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { MonetizationService } from "@/lib/monetization/monetizationService";
import { z } from "zod";

const monetizationRequestSchema = z.object({});

// Get blog monetization status
export async function GET(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;
    const session = await getServerSession(authOptions);

    // Get blog
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: {
        id: true,
        title: true,
        authorId: true,
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user is the author or admin
    if (!session?.user || (session.user.id !== blog.authorId && !session.user.isAdmin)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get monetization status
    const monetization = await MonetizationService.getBlogMonetizationStatus(blog.id);
    const config = await MonetizationService.getMonetizationConfig();

    return NextResponse.json({
      success: true,
      data: {
        blog: {
          id: blog.id,
          title: blog.title,
        },
        monetization,
        config,
      },
    });
  } catch (error: any) {
    console.error("Error fetching blog monetization status:", error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || "Failed to fetch monetization status"
      },
      { status: 500 }
    );
  }
}

// Request monetization for blog
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get blog
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: {
        id: true,
        title: true,
        authorId: true,
        status: true,
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user is the author
    if (session.user.id !== blog.authorId) {
      return NextResponse.json(
        { message: "Only the author can request monetization" },
        { status: 403 }
      );
    }

    // Check if blog is published
    if (blog.status !== 'published') {
      return NextResponse.json(
        { message: "Only published blogs can be monetized" },
        { status: 400 }
      );
    }

    // Check if monetization is globally enabled
    const isGloballyEnabled = await MonetizationService.isMonetizationEnabled();
    if (!isGloballyEnabled) {
      return NextResponse.json(
        { message: "Monetization is currently disabled by the administrator" },
        { status: 400 }
      );
    }

    const body = await req.json();
    const validatedData = monetizationRequestSchema.parse(body);

    // Request monetization
    const monetizationId = await MonetizationService.requestMonetization({
      blogId: blog.id,
      authorId: session.user.id
    });

    return NextResponse.json({
      success: true,
      message: "Monetization request submitted successfully",
      data: {
        monetizationId,
        status: "pending",
      },
    });
  } catch (error: any) {
    console.error("Error requesting blog monetization:", error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || "Failed to request monetization"
      },
      { status: 500 }
    );
  }
}

// Update monetization settings (author only)
export async function PATCH(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get blog
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: {
        id: true,
        title: true,
        authorId: true,
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user is the author
    if (session.user.id !== blog.authorId) {
      return NextResponse.json(
        { message: "Only the author can update monetization settings" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { isEnabled } = body;

    // Get current monetization
    const monetization = await MonetizationService.getBlogMonetizationStatus(blog.id);

    if (!monetization || monetization.status !== 'approved') {
      return NextResponse.json(
        { message: "Blog monetization is not approved" },
        { status: 400 }
      );
    }

    // Update monetization settings
    await db
      .update(blogMonetization)
      .set({
        isEnabled: isEnabled,
        updatedAt: new Date(),
      })
      .where(eq(blogMonetization.id, monetization.id));

    return NextResponse.json({
      success: true,
      message: "Monetization settings updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating blog monetization:", error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || "Failed to update monetization settings"
      },
      { status: 500 }
    );
  }
}
