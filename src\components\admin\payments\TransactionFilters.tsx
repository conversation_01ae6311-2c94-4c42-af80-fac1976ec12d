"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { 
  XMarkIcon,
  FunnelIcon,
  ArrowPathIcon
} from "@heroicons/react/24/outline";

interface TransactionFiltersProps {
  filters: {
    search: string;
    type: string;
    status: string;
    gateway: string;
    dateFrom: string;
    dateTo: string;
    sort: string;
    order: string;
  };
  onFilterChange: (filters: Partial<TransactionFiltersProps['filters']>) => void;
  onClose: () => void;
}

export function TransactionFilters({
  filters,
  onFilterChange,
  onClose,
}: TransactionFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);
  const [gateways, setGateways] = useState<string[]>([]);

  useEffect(() => {
    fetchGateways();
  }, []);

  const fetchGateways = async () => {
    try {
      const response = await fetch('/api/admin/wallet/gateways');
      const data = await response.json();
      
      if (data.success) {
        const gatewayNames = data.data.map((gateway: any) => gateway.name);
        setGateways(gatewayNames);
      }
    } catch (error) {
      console.error('Error fetching gateways:', error);
    }
  };

  const handleLocalFilterChange = (key: string, value: string) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    onFilterChange(localFilters);
  };

  const handleResetFilters = () => {
    const resetFilters = {
      search: "",
      type: "",
      status: "",
      gateway: "",
      dateFrom: "",
      dateTo: "",
      sort: "createdAt",
      order: "desc",
    };
    setLocalFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <FunnelIcon className="h-5 w-5 text-gray-400 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Advanced Filters</h3>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
        >
          <XMarkIcon className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Search
          </label>
          <Input
            type="text"
            placeholder="Transaction ID, reference, note..."
            value={localFilters.search}
            onChange={(e) => handleLocalFilterChange('search', e.target.value)}
          />
        </div>

        {/* Transaction Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Transaction Type
          </label>
          <select
            value={localFilters.type}
            onChange={(e) => handleLocalFilterChange('type', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            <option value="deposit">Deposit</option>
            <option value="send">Send Money</option>
            <option value="receive">Receive Money</option>
            <option value="cashout">Cashout</option>
            <option value="internal_transfer">Internal Transfer</option>
            <option value="earning">Earning</option>
            <option value="withdraw">Withdraw</option>
          </select>
        </div>

        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            value={localFilters.status}
            onChange={(e) => handleLocalFilterChange('status', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Payment Gateway */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Payment Gateway
          </label>
          <select
            value={localFilters.gateway}
            onChange={(e) => handleLocalFilterChange('gateway', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Gateways</option>
            {gateways.map((gateway) => (
              <option key={gateway} value={gateway}>
                {gateway}
              </option>
            ))}
          </select>
        </div>

        {/* Date From */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date From
          </label>
          <Input
            type="date"
            value={localFilters.dateFrom}
            onChange={(e) => handleLocalFilterChange('dateFrom', e.target.value)}
          />
        </div>

        {/* Date To */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date To
          </label>
          <Input
            type="date"
            value={localFilters.dateTo}
            onChange={(e) => handleLocalFilterChange('dateTo', e.target.value)}
          />
        </div>

        {/* Sort By */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sort By
          </label>
          <select
            value={localFilters.sort}
            onChange={(e) => handleLocalFilterChange('sort', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="createdAt">Date Created</option>
            <option value="amount">Amount</option>
            <option value="status">Status</option>
            <option value="type">Type</option>
          </select>
        </div>

        {/* Sort Order */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sort Order
          </label>
          <select
            value={localFilters.order}
            onChange={(e) => handleLocalFilterChange('order', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="desc">Descending</option>
            <option value="asc">Ascending</option>
          </select>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
        <Button
          variant="outline"
          onClick={handleResetFilters}
        >
          <ArrowPathIcon className="h-4 w-4 mr-2" />
          Reset Filters
        </Button>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            onClick={handleApplyFilters}
          >
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
}
