import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { likes } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { and, eq } from "drizzle-orm";

export async function POST(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the post ID from the context params
    const params = await context.params;
    const { postId } = params;

    // Check if the user has already disliked the post
    const existingDislike = await db.query.likes.findFirst({
      where: and(
        eq(likes.postId, postId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'dislike')
      ),
    });

    if (existingDislike) {
      // Remove the dislike
      await db
        .delete(likes)
        .where(
          and(
            eq(likes.postId, postId),
            eq(likes.userId, session.user.id),
            eq(likes.type, 'dislike')
          )
        );

      return NextResponse.json(
        { message: "Post undisliked successfully" },
        { status: 200 }
      );
    }

    // Check if the user has already liked the post
    const existingLike = await db.query.likes.findFirst({
      where: and(
        eq(likes.postId, postId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'like')
      ),
    });

    // If the user has liked the post, remove the like
    if (existingLike) {
      await db
        .delete(likes)
        .where(
          and(
            eq(likes.postId, postId),
            eq(likes.userId, session.user.id),
            eq(likes.type, 'like')
          )
        );
    }

    // Add the dislike
    await db.insert(likes).values({
      id: uuidv4(),
      userId: session.user.id,
      postId,
      commentId: null,
      type: 'dislike',
    });

    return NextResponse.json(
      { message: "Post disliked successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error disliking post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
