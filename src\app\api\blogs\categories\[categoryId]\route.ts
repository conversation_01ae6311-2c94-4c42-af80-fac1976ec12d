import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogCategories, blogs } from "@/lib/db/schema";
import { z } from "zod";
import { eq, sql } from "drizzle-orm";

const categoryUpdateSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  slug: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "Invalid color format").optional(),
});

// Get a single blog category
export async function GET(
  req: Request,
  context: { params: Promise<{ categoryId: string }> }
) {
  try {
    const params = await context.params;
    const { categoryId } = params;

    // Fetch category with blog count
    const category = await db.query.blogCategories.findFirst({
      where: eq(blogCategories.id, categoryId),
    });

    if (!category) {
      return NextResponse.json(
        { message: "Category not found" },
        { status: 404 }
      );
    }

    // Get blog count for this category
    const blogCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(eq(blogs.categoryId, category.id));

    const categoryWithCount = {
      ...category,
      _count: {
        blogs: blogCount[0]?.count || 0,
      },
    };

    return NextResponse.json(categoryWithCount);
  } catch (error) {
    console.error("Error fetching blog category:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a blog category
export async function PUT(
  req: Request,
  context: { params: Promise<{ categoryId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { categoryId } = params;
    const body = await req.json();
    const validatedData = categoryUpdateSchema.parse(body);

    // Check if category exists
    const existingCategory = await db.query.blogCategories.findFirst({
      where: eq(blogCategories.id, categoryId),
    });

    if (!existingCategory) {
      return NextResponse.json(
        { message: "Category not found" },
        { status: 404 }
      );
    }

    // Check if slug is being updated and if it conflicts with existing categories
    if (validatedData.slug && validatedData.slug !== existingCategory.slug) {
      const slugConflict = await db.query.blogCategories.findFirst({
        where: eq(blogCategories.slug, validatedData.slug),
      });

      if (slugConflict) {
        return NextResponse.json(
          { message: "Category with this slug already exists" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    if (validatedData.name !== undefined) updateData.name = validatedData.name;
    if (validatedData.slug !== undefined) updateData.slug = validatedData.slug;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.color !== undefined) updateData.color = validatedData.color;

    // Update the category
    await db.update(blogCategories)
      .set(updateData)
      .where(eq(blogCategories.id, categoryId));

    return NextResponse.json({
      message: "Category updated successfully",
    });
  } catch (error) {
    console.error("Error updating blog category:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a blog category
export async function DELETE(
  req: Request,
  context: { params: Promise<{ categoryId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { categoryId } = params;

    // Check if category exists
    const existingCategory = await db.query.blogCategories.findFirst({
      where: eq(blogCategories.id, categoryId),
    });

    if (!existingCategory) {
      return NextResponse.json(
        { message: "Category not found" },
        { status: 404 }
      );
    }

    // Check if category has any blogs
    const blogCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(eq(blogs.categoryId, categoryId));

    if (blogCount[0]?.count > 0) {
      return NextResponse.json(
        {
          message: `Cannot delete category with ${blogCount[0].count} blog(s). Move or delete the blogs first.`
        },
        { status: 400 }
      );
    }

    // Delete the category
    await db.delete(blogCategories).where(eq(blogCategories.id, categoryId));

    return NextResponse.json({
      message: "Category deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting blog category:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
