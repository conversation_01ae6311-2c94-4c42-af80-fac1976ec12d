"use client";

import { useState, useEffect } from "react";
import { 
  CreditCardIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  XMarkIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { format } from "date-fns";

interface BillingTransaction {
  id: string;
  subscriptionId: string;
  userId: string;
  planId: string;
  type: 'payment' | 'refund' | 'upgrade' | 'downgrade' | 'cancellation';
  amount: string;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentGateway: string | null;
  gatewayTransactionId: string | null;
  description: string | null;
  processedAt: string | null;
  createdAt: string;
}

interface BillingHistoryProps {
  limit?: number;
}

export function BillingHistory({ limit = 10 }: BillingHistoryProps) {
  const [transactions, setTransactions] = useState<BillingTransaction[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchBillingHistory();
  }, [limit]);

  const fetchBillingHistory = async () => {
    try {
      const response = await fetch(`/api/billing-history?limit=${limit}`);
      const result = await response.json();

      if (result.success) {
        setTransactions(result.data);
      } else {
        toast.error('Failed to fetch billing history');
      }
    } catch (error) {
      console.error('Error fetching billing history:', error);
      toast.error('Failed to fetch billing history');
    } finally {
      setLoading(false);
    }
  };

  const getTransactionIcon = (type: string, status: string) => {
    if (status === 'failed') {
      return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />;
    }
    if (status === 'pending') {
      return <ClockIcon className="h-5 w-5 text-yellow-500" />;
    }
    if (status === 'cancelled') {
      return <XMarkIcon className="h-5 w-5 text-gray-500" />;
    }

    switch (type) {
      case 'payment':
        return <CreditCardIcon className="h-5 w-5 text-green-500" />;
      case 'refund':
        return <ArrowUpIcon className="h-5 w-5 text-blue-500" />;
      case 'upgrade':
        return <ArrowUpIcon className="h-5 w-5 text-purple-500" />;
      case 'downgrade':
        return <ArrowDownIcon className="h-5 w-5 text-orange-500" />;
      case 'cancellation':
        return <XMarkIcon className="h-5 w-5 text-red-500" />;
      default:
        return <CheckCircleIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium";
    
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'failed':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'cancelled':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const formatTransactionType = (type: string) => {
    switch (type) {
      case 'payment':
        return 'Payment';
      case 'refund':
        return 'Refund';
      case 'upgrade':
        return 'Plan Upgrade';
      case 'downgrade':
        return 'Plan Downgrade';
      case 'cancellation':
        return 'Cancellation';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  const formatAmount = (amount: string, currency: string, type: string) => {
    const value = parseFloat(amount);
    const symbol = currency === 'USD' ? '$' : currency;
    
    if (type === 'refund') {
      return `+${symbol}${value.toFixed(2)}`;
    }
    
    return `${symbol}${value.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8">
        <CreditCardIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No billing history</h3>
        <p className="text-gray-500">
          Your billing history will appear here once you subscribe to a paid plan.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Billing History</h3>
        <span className="text-sm text-gray-500">
          {transactions.length} transaction{transactions.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {transactions.map((transaction) => (
            <li key={transaction.id}>
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      {getTransactionIcon(transaction.type, transaction.status)}
                    </div>
                    <div className="ml-4">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-gray-900">
                          {formatTransactionType(transaction.type)}
                        </p>
                        <span className={`ml-2 ${getStatusBadge(transaction.status)}`}>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </span>
                      </div>
                      <div className="mt-1">
                        <p className="text-sm text-gray-600">
                          {transaction.description || `${formatTransactionType(transaction.type)} transaction`}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {format(new Date(transaction.createdAt), 'MMM dd, yyyy • h:mm a')}
                          {transaction.processedAt && transaction.processedAt !== transaction.createdAt && (
                            <span className="ml-2">
                              • Processed {format(new Date(transaction.processedAt), 'MMM dd, yyyy • h:mm a')}
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm font-medium ${
                      transaction.type === 'refund' ? 'text-green-600' : 'text-gray-900'
                    }`}>
                      {formatAmount(transaction.amount, transaction.currency, transaction.type)}
                    </p>
                    {transaction.paymentGateway && (
                      <p className="text-xs text-gray-500 mt-1">
                        via {transaction.paymentGateway.charAt(0).toUpperCase() + transaction.paymentGateway.slice(1)}
                      </p>
                    )}
                    {transaction.gatewayTransactionId && (
                      <p className="text-xs text-gray-400 mt-1">
                        ID: {transaction.gatewayTransactionId.slice(-8)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>

      {transactions.length >= limit && (
        <div className="text-center">
          <button
            onClick={() => fetchBillingHistory()}
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            Load more transactions
          </button>
        </div>
      )}
    </div>
  );
}
