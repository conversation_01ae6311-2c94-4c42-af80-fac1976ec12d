import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageFollowers } from "@/lib/db/schema";
import { like, or, eq, desc, count, sql, and } from "drizzle-orm";

// GET /api/fan-pages/search - Enhanced search fan pages
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);

    // Search parameters
    const query = searchParams.get("q") || "";
    const category = searchParams.get("category") || "";

    // Pagination
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "12"), 50); // Max 50 results
    const offset = (page - 1) * limit;

    // Build base query with isFollowing field
    let searchQuery = db.select({
      id: fanPages.id,
      name: fanPages.name,
      username: fanPages.username,
      category: fanPages.category,
      description: fanPages.description,
      profileImage: fanPages.profileImage,
      coverImage: fanPages.coverImage,
      isVerified: fanPages.isVerified,
      followerCount: fanPages.followerCount,
      postCount: fanPages.postCount,
      location: fanPages.location,
      website: fanPages.website,
      createdAt: fanPages.createdAt,
      isFollowing: session?.user?.id
        ? sql<boolean>`EXISTS(
            SELECT 1 FROM ${fanPageFollowers}
            WHERE ${fanPageFollowers.fanPageId} = ${fanPages.id}
            AND ${fanPageFollowers.userId} = ${session.user.id}
          )`
        : sql<boolean>`false`,
    }).from(fanPages);

    // Build where conditions
    let whereConditions = [eq(fanPages.isActive, true)];

    // Apply search filters
    if (query.trim()) {
      const searchTerm = query.trim();
      whereConditions.push(
        or(
          like(fanPages.name, `%${searchTerm}%`),
          like(fanPages.username, `%${searchTerm}%`),
          like(fanPages.description, `%${searchTerm}%`),
          like(fanPages.location, `%${searchTerm}%`)
        )
      );
    }

    // Category filter
    if (category && category !== "all") {
      whereConditions.push(eq(fanPages.category, category as any));
    }



    // Apply all where conditions
    if (whereConditions.length > 0) {
      searchQuery = searchQuery.where(and(...whereConditions));
    }

    // Apply sorting - prioritize verified pages, then followers, then creation date
    searchQuery = searchQuery.orderBy(
      desc(fanPages.isVerified),
      desc(fanPages.followerCount),
      desc(fanPages.createdAt)
    );

    // Execute search with pagination
    const results = await searchQuery
      .limit(limit)
      .offset(offset);

    // Get total count for pagination (only if we have results or it's the first page)
    let total = 0;
    if (results.length > 0 || page === 1) {
      const countQuery = db
        .select({ count: count() })
        .from(fanPages);

      if (whereConditions.length > 0) {
        countQuery.where(and(...whereConditions));
      }

      const totalResult = await countQuery;
      total = totalResult[0]?.count || 0;
    }

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      pages: results,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
      filters: {
        query: query.trim(),
        category,
      },
      count: results.length,
    });

  } catch (error) {
    console.error("Error searching fan pages:", error);
    return NextResponse.json(
      { error: "Failed to search fan pages" },
      { status: 500 }
    );
  }
}
