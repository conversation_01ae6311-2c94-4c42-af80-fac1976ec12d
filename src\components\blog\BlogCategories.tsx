"use client";

import { useState } from "react";
import Link from "next/link";
import { Badge } from "@/components/ui/Badge";
import { 
  ComputerDesktopIcon,
  PaintBrushIcon,
  BriefcaseIcon,
  HeartIcon,
  GlobeAltIcon,
  CameraIcon,
  BookOpenIcon,
  MusicalNoteIcon
} from "@heroicons/react/24/outline";

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  _count: {
    blogs: number;
  };
}

interface BlogCategoriesProps {
  categories: Category[];
  selectedCategory?: string | null;
  onCategorySelect?: (categoryId: string | null) => void;
  variant?: "grid" | "list" | "chips";
  showCounts?: boolean;
}

const categoryIcons: Record<string, React.ComponentType<any>> = {
  technology: ComputerDesktopIcon,
  design: PaintBrushIcon,
  business: BriefcaseIcon,
  lifestyle: HeartIcon,
  travel: GlobeAltIcon,
  photography: CameraIcon,
  education: BookOpenIcon,
  music: MusicalNoteIcon,
};

export function BlogCategories({ 
  categories, 
  selectedCategory,
  onCategorySelect,
  variant = "grid",
  showCounts = true 
}: BlogCategoriesProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

  const getCategoryIcon = (slug: string) => {
    const IconComponent = categoryIcons[slug.toLowerCase()] || ComputerDesktopIcon;
    return IconComponent;
  };

  if (variant === "chips") {
    return (
      <div className="flex flex-wrap gap-3">
        <button
          onClick={() => onCategorySelect?.(null)}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            !selectedCategory
              ? "bg-blue-600 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          All Categories
        </button>
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategorySelect?.(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category.id
                ? "text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            style={{
              backgroundColor: selectedCategory === category.id ? category.color : undefined
            }}
          >
            {category.name} {showCounts && `(${category._count.blogs})`}
          </button>
        ))}
      </div>
    );
  }

  if (variant === "list") {
    return (
      <div className="space-y-2">
        <button
          onClick={() => onCategorySelect?.(null)}
          className={`w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-colors ${
            !selectedCategory
              ? "bg-blue-50 text-blue-700 border border-blue-200"
              : "hover:bg-gray-50"
          }`}
        >
          <span className="font-medium">All Categories</span>
          {showCounts && (
            <span className="text-sm text-gray-500">
              {categories.reduce((sum, cat) => sum + cat._count.blogs, 0)}
            </span>
          )}
        </button>
        
        {categories.map((category) => {
          const IconComponent = getCategoryIcon(category.slug);
          return (
            <button
              key={category.id}
              onClick={() => onCategorySelect?.(category.id)}
              className={`w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-colors ${
                selectedCategory === category.id
                  ? "text-white"
                  : "hover:bg-gray-50"
              }`}
              style={{
                backgroundColor: selectedCategory === category.id ? category.color : undefined
              }}
            >
              <div className="flex items-center space-x-3">
                <IconComponent className="h-5 w-5" />
                <span className="font-medium">{category.name}</span>
              </div>
              {showCounts && (
                <span className={`text-sm ${
                  selectedCategory === category.id ? "text-white/80" : "text-gray-500"
                }`}>
                  {category._count.blogs}
                </span>
              )}
            </button>
          );
        })}
      </div>
    );
  }

  // Grid variant (default)
  return (
    <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
      {categories.map((category) => {
        const IconComponent = getCategoryIcon(category.slug);
        return (
          <button
            key={category.id}
            onClick={() => onCategorySelect?.(category.id)}
            onMouseEnter={() => setHoveredCategory(category.id)}
            onMouseLeave={() => setHoveredCategory(null)}
            className={`group relative flex flex-col items-center p-6 rounded-xl transition-all duration-300 ${
              selectedCategory === category.id
                ? "text-white"
                : "bg-white hover:shadow-md"
            }`}
            style={{
              backgroundColor: selectedCategory === category.id ? category.color : undefined,
              borderColor: hoveredCategory === category.id ? category.color : undefined
            }}
          >
            {/* Icon */}
            <div 
              className={`mb-3 p-3 rounded-full transition-colors ${
                selectedCategory === category.id
                  ? "bg-white/20"
                  : "bg-gray-100 group-hover:bg-opacity-80"
              }`}
              style={{
                backgroundColor: hoveredCategory === category.id && selectedCategory !== category.id 
                  ? `${category.color}20` 
                  : undefined
              }}
            >
              <IconComponent 
                className={`h-6 w-6 transition-colors ${
                  selectedCategory === category.id
                    ? "text-white"
                    : hoveredCategory === category.id
                    ? "text-white"
                    : "text-gray-600"
                }`}
                style={{
                  color: hoveredCategory === category.id && selectedCategory !== category.id 
                    ? category.color 
                    : undefined
                }}
              />
            </div>
            
            {/* Category Name */}
            <h3 className={`text-sm font-medium text-center mb-1 ${
              selectedCategory === category.id ? "text-white" : "text-gray-900"
            }`}>
              {category.name}
            </h3>
            
            {/* Blog Count */}
            {showCounts && (
              <p className={`text-xs ${
                selectedCategory === category.id ? "text-white/80" : "text-gray-500"
              }`}>
                {category._count.blogs} {category._count.blogs === 1 ? 'story' : 'stories'}
              </p>
            )}
            
            {/* Description on hover */}
            {category.description && hoveredCategory === category.id && (
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap z-10">
                {category.description}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-gray-900"></div>
              </div>
            )}
          </button>
        );
      })}
    </div>
  );
}
