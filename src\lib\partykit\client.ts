"use client";

import PartySocket from "partysocket";
import { getSession } from "next-auth/react";

export interface PartyKitConfig {
  host: string;
  party: string;
  room: string;
  enableFallback?: boolean;
}

export interface ConnectionStatus {
  connected: boolean;
  authenticated: boolean;
  latency: number;
  lastPing: number;
  reconnectAttempts: number;
}

export class PartyKitClient {
  private socket: PartySocket | null = null;
  private config: PartyKitConfig;
  private status: ConnectionStatus = {
    connected: false,
    authenticated: false,
    latency: 0,
    lastPing: 0,
    reconnectAttempts: 0
  };
  private messageQueue: any[] = [];
  private eventListeners = new Map<string, Set<Function>>();
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second

  constructor(config: PartyKitConfig) {
    this.config = config;
  }

  async connect(): Promise<boolean> {
    try {
      if (this.socket?.readyState === WebSocket.OPEN) {
        return true;
      }

      this.socket = new PartySocket({
        host: this.config.host,
        party: this.config.party,
        room: this.config.room,
        protocols: ["partykit-protocol"]
      });

      return new Promise((resolve, reject) => {
        if (!this.socket) {
          reject(new Error("Failed to create socket"));
          return;
        }

        const timeout = setTimeout(() => {
          reject(new Error("Connection timeout"));
        }, 10000);

        this.socket.onopen = () => {
          clearTimeout(timeout);
          this.status.connected = true;
          this.status.reconnectAttempts = 0;
          this.emit("connected");
          this.authenticate();
          this.startPingInterval();
          resolve(true);
        };

        this.socket.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.socket.onclose = () => {
          clearTimeout(timeout);
          this.status.connected = false;
          this.status.authenticated = false;
          this.emit("disconnected");
          this.stopPingInterval();
          this.handleReconnect();
        };

        this.socket.onerror = (error) => {
          clearTimeout(timeout);
          console.error("PartyKit connection error:", error);
          this.emit("error", error);
          reject(error);
        };
      });
    } catch (error) {
      console.error("Failed to connect to PartyKit:", error);
      return false;
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.stopPingInterval();
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    this.status.connected = false;
    this.status.authenticated = false;
  }

  send(message: any): boolean {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      if (this.config.enableFallback) {
        this.messageQueue.push(message);
        return false;
      }
      throw new Error("Socket not connected");
    }

    try {
      // Use message batching for optimization
      const { messageBatcher } = require('./optimization');
      const roomId = this.config.room;

      const wasBatched = messageBatcher.addMessage(roomId, message, (messages: any[]) => {
        // Send batched messages
        if (messages.length === 1) {
          this.socket!.send(JSON.stringify(messages[0]));
        } else {
          this.socket!.send(JSON.stringify({
            type: "batch",
            messages: messages,
            timestamp: new Date().toISOString()
          }));
        }
      });

      return true;
    } catch (error) {
      console.error("Failed to send message:", error);
      return false;
    }
  }

  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data?: any) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  private async authenticate() {
    try {
      const session = await getSession();
      if (session?.user) {
        // Extract JWT token from session
        const token = (session as any).accessToken || session.user.id;
        
        this.send({
          type: "auth",
          token: token,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error("Authentication failed:", error);
      this.emit("auth_error", error);
    }
  }

  private handleMessage(event: MessageEvent) {
    try {
      const data = JSON.parse(event.data);
      const now = Date.now();

      switch (data.type) {
        case "connection_ack":
        case "chat_server_ready":
        case "notification_server_ready":
          this.emit("ready", data);
          break;

        case "auth_success":
        case "chat_auth_success":
        case "notification_auth_success":
          this.status.authenticated = true;
          this.emit("authenticated", data);
          this.flushMessageQueue();
          break;

        case "auth_error":
        case "chat_auth_error":
        case "notification_auth_error":
          this.status.authenticated = false;
          this.emit("auth_error", data);
          break;

        case "pong":
          this.status.latency = now - this.status.lastPing;
          this.emit("latency_update", this.status.latency);
          break;

        case "error":
          this.emit("error", data);
          break;

        default:
          this.emit("message", data);
          this.emit(data.type, data);
      }
    } catch (error) {
      console.error("Failed to parse message:", error);
    }
  }

  private startPingInterval() {
    this.pingInterval = setInterval(() => {
      if (this.socket?.readyState === WebSocket.OPEN) {
        this.status.lastPing = Date.now();
        this.send({ type: "ping", timestamp: this.status.lastPing });
      }
    }, 30000); // Ping every 30 seconds
  }

  private stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private handleReconnect() {
    if (this.status.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit("max_reconnect_attempts");
      return;
    }

    this.status.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.status.reconnectAttempts - 1);

    this.reconnectTimeout = setTimeout(async () => {
      console.log(`Attempting to reconnect (${this.status.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      try {
        await this.connect();
      } catch (error) {
        console.error("Reconnection failed:", error);
      }
    }, delay);
  }

  private flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  getStatus(): ConnectionStatus {
    return { ...this.status };
  }

  isConnected(): boolean {
    return this.status.connected && this.status.authenticated;
  }
}

// Singleton instances for different PartyKit servers with optimization
export class PartyKitManager {
  private static instances = new Map<string, PartyKitClient>();

  static getClient(type: "chat" | "notifications" | "main", room: string): PartyKitClient {
    const key = `${type}-${room}`;

    // Use connection pool for optimization
    const { connectionPool, roomOptimizer } = require('./optimization');

    return connectionPool.getConnection(key, () => {
      const config: PartyKitConfig = {
        host: process.env.NEXT_PUBLIC_PARTYKIT_HOST || "localhost:1999",
        party: type,
        room: room,
        enableFallback: true
      };

      const client = new PartyKitClient(config);

      // Track room usage for optimization
      if (typeof window !== 'undefined') {
        const userId = this.getCurrentUserId();
        if (userId) {
          roomOptimizer.joinRoom(userId, room);
        }
      }

      return client;
    });
  }

  static disconnectClient(type: "chat" | "notifications" | "main", room: string) {
    const key = `${type}-${room}`;
    const { connectionPool, roomOptimizer } = require('./optimization');

    // Track room leaving for optimization
    if (typeof window !== 'undefined') {
      const userId = this.getCurrentUserId();
      if (userId) {
        roomOptimizer.leaveRoom(userId, room);
      }
    }

    connectionPool.removeConnection(key);
  }

  static disconnectAll() {
    this.instances.forEach(client => client.disconnect());
    this.instances.clear();

    const { connectionPool } = require('./optimization');
    connectionPool.destroy();
  }

  static getOptimizationStats() {
    const { partyKitOptimizer } = require('./optimization');
    return partyKitOptimizer.getOptimizationReport();
  }

  private static getCurrentUserId(): string | null {
    // This would integrate with your auth system
    // For now, we'll try to get it from session storage or a global state
    try {
      if (typeof window !== 'undefined') {
        const session = JSON.parse(sessionStorage.getItem('session') || '{}');
        return session.user?.id || null;
      }
    } catch {
      return null;
    }
    return null;
  }
}
