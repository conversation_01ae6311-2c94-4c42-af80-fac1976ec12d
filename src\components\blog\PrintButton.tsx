"use client";

import { PrinterIcon } from "@heroicons/react/24/outline";

// Simple markdown to HTML converter for printing
const convertMarkdownToHTML = (markdown: string): string => {
  let html = markdown;

  // Headers (H1-H6)
  html = html.replace(/^###### (.*$)/gim, '<h6>$1</h6>');
  html = html.replace(/^##### (.*$)/gim, '<h5>$1</h5>');
  html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

  // Code blocks
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

  // Bold
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // Italic
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Inline code
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');

  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" rel="nofollow noopener noreferrer">$1</a>');

  // Images
  html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" />');

  // Bullet lists
  html = html.replace(/^\- (.*$)/gim, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

  // Numbered lists
  html = html.replace(/^\d+\. (.*$)/gim, '<li>$1</li>');

  // Blockquotes
  html = html.replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>');

  // Line breaks and paragraphs
  html = html.replace(/\n\n/g, '</p><p>');
  html = html.replace(/\n/g, '<br />');

  // Wrap in paragraphs
  if (html && !html.startsWith('<')) {
    html = '<p>' + html + '</p>';
  }

  return html;
};

interface PrintButtonProps {
  title: string;
  author: string;
  content: string;
}

export function PrintButton({ title, author, content }: PrintButtonProps) {
  const handlePrint = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Generate print-friendly HTML
    const printHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            body {
              font-family: 'Times New Roman', serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            h1 {
              color: #2563eb;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 10px;
              margin-bottom: 20px;
            }
            h2 {
              color: #374151;
              margin-top: 30px;
              margin-bottom: 15px;
            }
            h3 {
              color: #4b5563;
              margin-top: 25px;
              margin-bottom: 10px;
            }
            p {
              margin-bottom: 15px;
              text-align: justify;
            }
            .header {
              text-align: center;
              margin-bottom: 40px;
              border-bottom: 1px solid #e5e7eb;
              padding-bottom: 20px;
            }
            .author {
              color: #6b7280;
              font-style: italic;
              margin-top: 10px;
            }
            .print-date {
              color: #9ca3af;
              font-size: 12px;
              margin-top: 10px;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${title}</h1>
            <div class="author">By ${author}</div>
            <div class="print-date">Printed on ${new Date().toLocaleDateString()}</div>
          </div>
          <div class="content">
            ${convertMarkdownToHTML(content)}
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printHTML);
    printWindow.document.close();
    
    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
  };

  return (
    <button
      onClick={handlePrint}
      className="p-3 text-gray-600 hover:text-blue-600 transition-all duration-300 hover:bg-blue-50 rounded-full group"
      title="Print this post"
    >
      <PrinterIcon className="w-6 h-6 group-hover:scale-110 transition-transform" />
    </button>
  );
}
