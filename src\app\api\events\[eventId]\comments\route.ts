import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventComments, eventAttendees, users, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, desc } from "drizzle-orm";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";

const commentSchema = z.object({
  content: z.string().min(1).max(5000),
  images: z.array(z.any()).optional(),
  videos: z.array(z.any()).optional(),
});

// Get all comments for an event
export async function GET(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Fetch comments first
    const commentsData = await db.query.eventComments.findMany({
      where: eq(eventComments.eventId, eventId),
      orderBy: desc(eventComments.createdAt),
    });

    // Then fetch user data for each comment
    const comments = await Promise.all(
      commentsData.map(async (comment) => {
        // Use a direct SQL query to avoid collation issues
        const [userData] = await db.execute(
          `SELECT id, name, username, image FROM users WHERE id = ? LIMIT 1`,
          [comment.userId]
        );

        const user = userData.length > 0 ? userData[0] : {
          id: comment.userId,
          name: "Unknown User",
          username: null,
          image: null,
        };

        return {
          id: comment.id,
          content: comment.content,
          images: comment.images,
          videos: comment.videos,
          createdAt: comment.createdAt,
          user,
        };
      })
    );

    return NextResponse.json(comments);
  } catch (error) {
    console.error("Error fetching event comments:", error);

    // Provide more detailed error message
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string };

        if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
        } else if (dbError.sqlMessage) {
          // Log the SQL error but don't expose it to the client
          console.error("SQL Error:", dbError.sqlMessage);
        }
      }
    }

    return NextResponse.json(
      { message: errorMessage },
      { status: statusCode }
    );
  }
}

// Add a comment to an event
export async function POST(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;

    // Parse form data
    const formData = await req.formData();
    const content = formData.get('content') as string;
    const imageFiles = formData.getAll('images') as File[];
    const videoFiles = formData.getAll('videos') as File[];

    // Validate content
    if (!content || content.trim().length === 0) {
      return NextResponse.json(
        { message: "Comment content is required" },
        { status: 400 }
      );
    }

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the user is attending the event or is the host
    const isHost = event.hostId === session.user.id;
    const isAttending = await db.query.eventAttendees.findFirst({
      where: and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.userId, session.user.id)
      ),
    });

    if (!isHost && !isAttending) {
      return NextResponse.json(
        { message: "You must be attending the event to comment" },
        { status: 403 }
      );
    }

    // Upload images and videos to Cloudinary if any
    let imageUrls: string[] = [];
    let videoUrls: string[] = [];

    if (imageFiles.length > 0) {
      imageUrls = await uploadMultipleToCloudinary(imageFiles);
    }

    if (videoFiles.length > 0) {
      videoUrls = await uploadMultipleToCloudinary(videoFiles);
    }

    // Generate a new UUID for the comment
    const commentId = uuidv4();

    // Create the comment
    await db
      .insert(eventComments)
      .values({
        id: commentId,
        eventId,
        userId: session.user.id,
        content,
        images: imageUrls.length > 0 ? imageUrls : null,
        videos: videoUrls.length > 0 ? videoUrls : null,
      });

    // Fetch the new comment
    const newComment = await db.query.eventComments.findFirst({
      where: eq(eventComments.id, commentId),
    });

    if (!newComment) {
      return NextResponse.json(
        { message: "Failed to create comment" },
        { status: 500 }
      );
    }

    // Notify the event host if the commenter is not the host
    if (event.hostId !== session.user.id) {
      await db.insert(notifications).values({
        id: uuidv4(),
        recipientId: event.hostId,
        type: "event_comment",
        senderId: session.user.id,
        eventId,
        read: false,
      });
    }

    // Get the user information for the response
    const user = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
      columns: {
        id: true,
        name: true,
        username: true,
        image: true,
      },
    });

    return NextResponse.json({
      ...newComment,
      user,
    });
  } catch (error) {
    console.error("Error creating event comment:", error);

    // Provide more detailed error message
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string };

        if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
        } else if (dbError.sqlMessage) {
          // Log the SQL error but don't expose it to the client
          console.error("SQL Error:", dbError.sqlMessage);
        }
      }
    }

    return NextResponse.json(
      { message: errorMessage },
      { status: statusCode }
    );
  }
}
