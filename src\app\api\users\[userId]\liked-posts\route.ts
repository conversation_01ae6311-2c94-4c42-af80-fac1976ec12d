import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, likes, users, comments } from "@/lib/db/schema";
import { eq, desc, count, sql } from "drizzle-orm";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get posts liked by the user
    const likedPosts = await db
      .select({
        post: {
          id: posts.id,
          content: posts.content,
          images: posts.images,
          videos: posts.videos,
          backgroundColor: posts.backgroundColor,
          feeling: posts.feeling,
          activity: posts.activity,
          location: posts.location,
          formattedContent: posts.formattedContent,
          createdAt: posts.createdAt,
        },
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
        likedAt: likes.createdAt,
      })
      .from(likes)
      .innerJoin(posts, eq(likes.postId, posts.id))
      .innerJoin(users, eq(posts.userId, users.id))
      .where(eq(likes.userId, userId))
      .orderBy(desc(likes.createdAt))
      .limit(limit)
      .offset(offset);

    // Get counts for each post
    const postsWithCounts = await Promise.all(
      likedPosts.map(async (item) => {
        const [likesCount, dislikesCount, commentsCount] = await Promise.all([
          db.select({ count: count() }).from(likes).where(and(eq(likes.postId, item.post.id), eq(likes.type, 'like'))),
          db.select({ count: count() }).from(likes).where(and(eq(likes.postId, item.post.id), eq(likes.type, 'dislike'))),
          db.select({ count: count() }).from(comments).where(eq(comments.postId, item.post.id)),
        ]);

        // Check if current user liked/disliked this post
        const [userLike, userDislike] = await Promise.all([
          db.query.likes.findFirst({
            where: and(eq(likes.postId, item.post.id), eq(likes.userId, session.user.id), eq(likes.type, 'like'))
          }),
          db.query.likes.findFirst({
            where: and(eq(likes.postId, item.post.id), eq(likes.userId, session.user.id), eq(likes.type, 'dislike'))
          }),
        ]);

        return {
          id: item.post.id,
          content: item.post.content,
          images: item.post.images,
          videos: item.post.videos,
          backgroundColor: item.post.backgroundColor,
          feeling: item.post.feeling,
          activity: item.post.activity,
          location: item.post.location,
          formattedContent: item.post.formattedContent,
          createdAt: item.post.createdAt.toISOString(),
          user: item.user,
          _count: {
            likes: likesCount[0]?.count || 0,
            dislikes: dislikesCount[0]?.count || 0,
            comments: commentsCount[0]?.count || 0,
            shares: 0, // TODO: Implement shares if needed
          },
          liked: !!userLike,
          disliked: !!userDislike,
        };
      })
    );

    // Check if there are more posts
    const totalLikedPosts = await db
      .select({ count: count() })
      .from(likes)
      .where(eq(likes.userId, userId));

    const hasMore = offset + limit < (totalLikedPosts[0]?.count || 0);

    return NextResponse.json({
      posts: postsWithCounts,
      hasMore,
      total: totalLikedPosts[0]?.count || 0
    });

  } catch (error) {
    console.error("Error fetching liked posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
