import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventAttendees, users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and, count } from "drizzle-orm";

const eventUpdateSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  description: z.string().max(5000).optional().nullable(),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
  location: z.string().max(255).optional().nullable(),
  isOnline: z.boolean().optional(),
  onlineLink: z.string().url().optional().nullable(),
  visibility: z.enum(["public", "private", "friends"]).optional(),
  category: z.string().max(100).optional().nullable(),
  coverImage: z.string().url().optional().nullable(),
  hostId: z.string().optional(),
});

// Get a specific event with admin details
export async function GET(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { eventId } = params;

    // Fetch the event directly to avoid collation issues
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Fetch host data separately
    let hostData = null;
    try {
      hostData = await db.query.users.findFirst({
        where: eq(users.id, event.hostId),
        columns: {
          id: true,
          name: true,
          username: true,
          image: true,
        },
      });
    } catch (error) {
      console.error("Error fetching host data:", error);
    }

    // Get attendee counts separately
    let attendeeCounts = {
      going: 0,
      interested: 0,
      not_going: 0
    };

    try {
      // Get all attendees for this event
      const attendees = await db.query.eventAttendees.findMany({
        where: eq(eventAttendees.eventId, eventId),
      });

      // Count attendees by status
      if (attendees && attendees.length > 0) {
        attendeeCounts.going = attendees.filter(a => a.status === "going").length;
        attendeeCounts.interested = attendees.filter(a => a.status === "interested").length;
        attendeeCounts.not_going = attendees.filter(a => a.status === "not_going").length;
      }
    } catch (error) {
      console.error("Error fetching attendee counts:", error);
    }

    // Format the response
    const formattedEvent = {
      ...event,
      host: hostData ? {
        id: hostData.id,
        name: hostData.name,
        username: hostData.username,
        image: hostData.image,
      } : undefined,
      attendeeCounts,
    };

    return NextResponse.json(formattedEvent);
  } catch (error) {
    console.error("Error fetching event:", error);
    return NextResponse.json(
      { message: "Failed to fetch event" },
      { status: 500 }
    );
  }
}

// Update an event as admin
export async function PUT(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { eventId } = params;
    const body = await req.json();
    const validatedData = eventUpdateSchema.parse(body);

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // If hostId is provided, check if the user exists
    if (validatedData.hostId) {
      const userExists = await db.query.users.findFirst({
        where: eq(users.id, validatedData.hostId),
      });

      if (!userExists) {
        return NextResponse.json(
          { message: "Host user not found" },
          { status: 404 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (validatedData.name !== undefined) updateData.name = validatedData.name;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.startTime !== undefined) updateData.startTime = new Date(validatedData.startTime);
    if (validatedData.endTime !== undefined) updateData.endTime = new Date(validatedData.endTime);
    if (validatedData.location !== undefined) updateData.location = validatedData.location;
    if (validatedData.isOnline !== undefined) updateData.isOnline = validatedData.isOnline;
    if (validatedData.onlineLink !== undefined) updateData.onlineLink = validatedData.onlineLink;
    if (validatedData.visibility !== undefined) updateData.visibility = validatedData.visibility;
    if (validatedData.category !== undefined) updateData.category = validatedData.category;
    if (validatedData.coverImage !== undefined) updateData.coverImage = validatedData.coverImage;
    if (validatedData.hostId !== undefined) updateData.hostId = validatedData.hostId;

    // Update the event
    await db.update(events)
      .set(updateData)
      .where(eq(events.id, eventId));

    // Fetch the updated event
    const updatedEvent = await db.query.events.findFirst({
      where: eq(events.id, eventId),
      with: {
        host: true,
      },
    });

    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error("Error updating event:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Failed to update event" },
      { status: 500 }
    );
  }
}

// Delete an event as admin
export async function DELETE(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { eventId } = params;

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Delete all attendees first (to maintain referential integrity)
    await db.delete(eventAttendees)
      .where(eq(eventAttendees.eventId, eventId));

    // Delete the event
    await db.delete(events)
      .where(eq(events.id, eventId));

    return NextResponse.json({ message: "Event deleted successfully" });
  } catch (error) {
    console.error("Error deleting event:", error);
    return NextResponse.json(
      { message: "Failed to delete event" },
      { status: 500 }
    );
  }
}
