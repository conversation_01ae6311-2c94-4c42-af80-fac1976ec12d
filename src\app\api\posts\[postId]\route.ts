import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts } from "@/lib/db/schema";
import { z } from "zod";
import { eq } from "drizzle-orm";
import { deleteMediaFiles } from "@/lib/storage/imageStorage";

const postUpdateSchema = z.object({
  content: z.string().max(5000).optional(),
  images: z.array(z.string()).optional(),
  videos: z.array(z.string()).optional(),
  privacy: z.enum(["public", "subscribers", "private"]).optional(),
  backgroundColor: z.string().nullable().optional(),
  feeling: z.string().nullable().optional(),
  activity: z.string().nullable().optional(),
  location: z.string().nullable().optional(),
  formattedContent: z.boolean().optional(),
}).refine(data => {
  // At least one field must be provided for update
  return Object.keys(data).some(key => data[key as keyof typeof data] !== undefined);
}, {
  message: "At least one field must be provided for update",
});

// Get a single post
export async function GET(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Ensure params is properly awaited in Next.js
    const params = await context.params;
    const { postId } = params;

    // Fetch the post from the database
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        sharedPost: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
          },
        },
        shares: true,
      },
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Check if the current user has liked or disliked this post
    const userLike = post.likes.find(like =>
      like.userId === session.user.id && like.type === 'like'
    );

    const userDislike = post.likes.find(like =>
      like.userId === session.user.id && like.type === 'dislike'
    );

    // Count likes and dislikes
    const likesCount = post.likes.filter(like => like.type === 'like').length;
    const dislikesCount = post.likes.filter(like => like.type === 'dislike').length;

    // Format the post for the frontend
    const formattedPost = {
      id: post.id,
      content: post.content,
      images: post.images,
      videos: post.videos,
      privacy: post.privacy,
      backgroundColor: post.backgroundColor,
      feeling: post.feeling,
      activity: post.activity,
      location: post.location,
      formattedContent: post.formattedContent,
      createdAt: post.createdAt.toISOString(),
      user: post.user,
      _count: {
        likes: likesCount,
        dislikes: dislikesCount,
        comments: post.comments.length,
        shares: post.shares.length,
      },
      liked: !!userLike,
      disliked: !!userDislike,
      sharedPost: post.sharedPost ? {
        id: post.sharedPost.id,
        content: post.sharedPost.content,
        images: post.sharedPost.images,
        videos: post.sharedPost.videos,
        backgroundColor: post.sharedPost.backgroundColor,
        feeling: post.sharedPost.feeling,
        activity: post.sharedPost.activity,
        location: post.sharedPost.location,
        formattedContent: post.sharedPost.formattedContent,
        createdAt: post.sharedPost.createdAt.toISOString(),
        user: post.sharedPost.user,
      } : null,
    };

    return NextResponse.json(formattedPost);
  } catch (error) {
    console.error("Error fetching post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a post
export async function PATCH(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    const params = await context.params;
    const { postId } = params;
    const body = await req.json();

    if (process.env.NODE_ENV === 'development') {
      console.log('Regular post update - Received body:', JSON.stringify(body, null, 2));
    }

    const validatedData = postUpdateSchema.parse(body);

    if (process.env.NODE_ENV === 'development') {
      console.log('Regular post update - Validated data:', JSON.stringify(validatedData, null, 2));
    }

    // Filter valid URLs for images and videos
    const validImages = validatedData.images?.filter(img => {
      try {
        new URL(img);
        return true;
      } catch {
        return false;
      }
    });

    const validVideos = validatedData.videos?.filter(video => {
      try {
        new URL(video);
        return true;
      } catch {
        return false;
      }
    });

    // Check if the post exists and belongs to the user
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    if (post.userId !== session.user.id) {
      return NextResponse.json(
        { message: "You are not authorized to update this post" },
        { status: 403 }
      );
    }

    // Update the post
    await db
      .update(posts)
      .set({
        content: validatedData.content !== undefined ? validatedData.content : post.content,
        images: validImages !== undefined ? validImages : post.images,
        videos: validVideos !== undefined ? validVideos : post.videos,
        privacy: validatedData.privacy !== undefined ? validatedData.privacy : post.privacy,
        backgroundColor: validatedData.backgroundColor !== undefined ? validatedData.backgroundColor : post.backgroundColor,
        feeling: validatedData.feeling !== undefined ? validatedData.feeling : post.feeling,
        activity: validatedData.activity !== undefined ? validatedData.activity : post.activity,
        location: validatedData.location !== undefined ? validatedData.location : post.location,
        formattedContent: validatedData.formattedContent !== undefined ? validatedData.formattedContent : post.formattedContent,
        updatedAt: new Date(),
      })
      .where(eq(posts.id, postId));

    return NextResponse.json(
      { message: "Post updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating post:", error);

    if (error instanceof z.ZodError) {
      if (process.env.NODE_ENV === 'development') {
        console.log("Validation errors:", error.errors);
      }
      return NextResponse.json(
        {
          message: "Invalid input data",
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a post
export async function DELETE(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    const params = await context.params;
    const { postId } = params;

    // Check if the post exists and belongs to the user
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    if (post.userId !== session.user.id) {
      return NextResponse.json(
        { message: "You are not authorized to delete this post" },
        { status: 403 }
      );
    }

    // Delete associated media files from storage before deleting the post
    let mediaDeleteResult = null;
    if (post.images || post.videos) {
      try {
        console.log(`🗑️ Deleting media files for post ${postId}...`);
        mediaDeleteResult = await deleteMediaFiles(
          post.images || [],
          post.videos || []
        );

        if (process.env.NODE_ENV === 'development') {
          console.log('Media deletion result:', mediaDeleteResult);
        }
      } catch (error) {
        console.error('Error deleting media files:', error);
        // Continue with post deletion even if media deletion fails
        // This prevents orphaned database records
      }
    }

    // Delete the post from database
    await db
      .delete(posts)
      .where(eq(posts.id, postId));

    // Prepare response with media deletion info
    const response: any = {
      message: "Post deleted successfully",
      postId: postId
    };

    if (mediaDeleteResult) {
      response.mediaDeleted = {
        success: mediaDeleteResult.success,
        deletedCount: mediaDeleteResult.deletedImages.length,
        failedCount: mediaDeleteResult.failedImages.length,
      };

      if (mediaDeleteResult.failedImages.length > 0) {
        console.warn(`⚠️ Some media files could not be deleted:`, mediaDeleteResult.failedImages);
        response.mediaDeleted.warnings = mediaDeleteResult.errors;
      }
    }

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error("Error deleting post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
