import { db } from "@/lib/db";
import { blogViews, blogs } from "@/lib/db/schema";
import { eq, and, sql, gte, desc, count } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export interface ViewTrackingData {
  blogId: string;
  userId?: string;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
  fingerprint?: string;
  referrer?: string;
  country?: string;
  city?: string;
  device?: string;
  browser?: string;
  os?: string;
  viewDuration?: number;
  scrollDepth?: number;
}

export interface ViewAnalytics {
  totalViews: number;
  uniqueViews: number;
  todayViews: number;
  weeklyViews: number;
  monthlyViews: number;
  averageViewDuration: number;
  averageScrollDepth: number;
  topReferrers: Array<{ referrer: string; count: number }>;
  deviceBreakdown: Array<{ device: string; count: number }>;
  countryBreakdown: Array<{ country: string; count: number }>;
  hourlyViews: Array<{ hour: number; count: number }>;
}

export class ViewTrackingService {
  // Track a blog view
  static async trackView(data: ViewTrackingData): Promise<void> {
    try {
      // Check if blog exists
      const blog = await db.query.blogs.findFirst({
        where: eq(blogs.id, data.blogId),
        columns: { id: true, status: true }
      });

      if (!blog || blog.status !== 'published') {
        return;
      }

      // Detect if it's a bot
      const isBot = this.detectBot(data.userAgent);
      
      // Check if this is a unique view
      const isUnique = await this.isUniqueView(data);

      // Create view record
      const viewId = uuidv4();
      await db.insert(blogViews).values({
        id: viewId,
        blogId: data.blogId,
        userId: data.userId || null,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        sessionId: data.sessionId,
        fingerprint: data.fingerprint || null,
        referrer: data.referrer || null,
        country: data.country || null,
        city: data.city || null,
        device: data.device || this.detectDevice(data.userAgent),
        browser: data.browser || this.detectBrowser(data.userAgent),
        os: data.os || this.detectOS(data.userAgent),
        isBot,
        isUnique,
        viewDuration: data.viewDuration || 0,
        scrollDepth: data.scrollDepth || 0,
      });

      // Update blog view count (only for non-bot, unique views)
      if (!isBot && isUnique) {
        await db
          .update(blogs)
          .set({
            viewCount: sql`${blogs.viewCount} + 1`
          })
          .where(eq(blogs.id, data.blogId));
      }

    } catch (error) {
      console.error('Error tracking view:', error);
    }
  }

  // Update view duration and scroll depth
  static async updateViewMetrics(
    sessionId: string, 
    blogId: string, 
    viewDuration: number, 
    scrollDepth: number
  ): Promise<void> {
    try {
      await db
        .update(blogViews)
        .set({
          viewDuration,
          scrollDepth,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(blogViews.sessionId, sessionId),
            eq(blogViews.blogId, blogId)
          )
        );
    } catch (error) {
      console.error('Error updating view metrics:', error);
    }
  }

  // Check if this is a unique view
  private static async isUniqueView(data: ViewTrackingData): Promise<boolean> {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    // Check by user ID if available
    if (data.userId) {
      const existingView = await db.query.blogViews.findFirst({
        where: and(
          eq(blogViews.blogId, data.blogId),
          eq(blogViews.userId, data.userId),
          gte(blogViews.createdAt, twentyFourHoursAgo)
        ),
        columns: { id: true }
      });
      return !existingView;
    }

    // Check by IP + fingerprint for anonymous users
    const conditions = [
      eq(blogViews.blogId, data.blogId),
      eq(blogViews.ipAddress, data.ipAddress),
      gte(blogViews.createdAt, twentyFourHoursAgo)
    ];

    if (data.fingerprint) {
      conditions.push(eq(blogViews.fingerprint, data.fingerprint));
    }

    const existingView = await db.query.blogViews.findFirst({
      where: and(...conditions),
      columns: { id: true }
    });

    return !existingView;
  }

  // Detect if user agent is a bot
  private static detectBot(userAgent: string): boolean {
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /googlebot/i, /bingbot/i, /slurp/i, /duckduckbot/i,
      /baiduspider/i, /yandexbot/i, /facebookexternalhit/i,
      /twitterbot/i, /linkedinbot/i, /whatsapp/i,
      /telegram/i, /skype/i, /zoom/i
    ];

    return botPatterns.some(pattern => pattern.test(userAgent));
  }

  // Detect device type
  private static detectDevice(userAgent: string): string {
    if (/mobile/i.test(userAgent)) return 'mobile';
    if (/tablet|ipad/i.test(userAgent)) return 'tablet';
    return 'desktop';
  }

  // Detect browser
  private static detectBrowser(userAgent: string): string {
    if (/chrome/i.test(userAgent) && !/edge/i.test(userAgent)) return 'Chrome';
    if (/firefox/i.test(userAgent)) return 'Firefox';
    if (/safari/i.test(userAgent) && !/chrome/i.test(userAgent)) return 'Safari';
    if (/edge/i.test(userAgent)) return 'Edge';
    if (/opera/i.test(userAgent)) return 'Opera';
    return 'Other';
  }

  // Detect operating system
  private static detectOS(userAgent: string): string {
    if (/windows/i.test(userAgent)) return 'Windows';
    if (/macintosh|mac os x/i.test(userAgent)) return 'macOS';
    if (/linux/i.test(userAgent)) return 'Linux';
    if (/android/i.test(userAgent)) return 'Android';
    if (/iphone|ipad|ipod/i.test(userAgent)) return 'iOS';
    return 'Other';
  }

  // Get comprehensive analytics for a blog
  static async getBlogAnalytics(blogId: string): Promise<ViewAnalytics> {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Total views
    const totalViewsResult = await db
      .select({ count: count() })
      .from(blogViews)
      .where(and(eq(blogViews.blogId, blogId), eq(blogViews.isBot, false)));

    // Unique views
    const uniqueViewsResult = await db
      .select({ count: count() })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        eq(blogViews.isUnique, true)
      ));

    // Today's views
    const todayViewsResult = await db
      .select({ count: count() })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        gte(blogViews.createdAt, todayStart)
      ));

    // Weekly views
    const weeklyViewsResult = await db
      .select({ count: count() })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        gte(blogViews.createdAt, weekStart)
      ));

    // Monthly views
    const monthlyViewsResult = await db
      .select({ count: count() })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        gte(blogViews.createdAt, monthStart)
      ));

    // Average view duration
    const avgDurationResult = await db
      .select({ 
        avgDuration: sql<number>`AVG(${blogViews.viewDuration})` 
      })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        sql`${blogViews.viewDuration} > 0`
      ));

    // Average scroll depth
    const avgScrollResult = await db
      .select({ 
        avgScroll: sql<number>`AVG(${blogViews.scrollDepth})` 
      })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        sql`${blogViews.scrollDepth} > 0`
      ));

    // Top referrers
    const topReferrersResult = await db
      .select({
        referrer: blogViews.referrer,
        count: count()
      })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        sql`${blogViews.referrer} IS NOT NULL AND ${blogViews.referrer} != ''`
      ))
      .groupBy(blogViews.referrer)
      .orderBy(desc(count()))
      .limit(10);

    // Device breakdown
    const deviceBreakdownResult = await db
      .select({
        device: blogViews.device,
        count: count()
      })
      .from(blogViews)
      .where(and(eq(blogViews.blogId, blogId), eq(blogViews.isBot, false)))
      .groupBy(blogViews.device)
      .orderBy(desc(count()));

    // Country breakdown
    const countryBreakdownResult = await db
      .select({
        country: blogViews.country,
        count: count()
      })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        sql`${blogViews.country} IS NOT NULL`
      ))
      .groupBy(blogViews.country)
      .orderBy(desc(count()))
      .limit(10);

    // Hourly views for today
    const hourlyViewsResult = await db
      .select({
        hour: sql<number>`HOUR(${blogViews.createdAt})`,
        count: count()
      })
      .from(blogViews)
      .where(and(
        eq(blogViews.blogId, blogId),
        eq(blogViews.isBot, false),
        gte(blogViews.createdAt, todayStart)
      ))
      .groupBy(sql`HOUR(${blogViews.createdAt})`)
      .orderBy(sql`HOUR(${blogViews.createdAt})`);

    return {
      totalViews: totalViewsResult[0]?.count || 0,
      uniqueViews: uniqueViewsResult[0]?.count || 0,
      todayViews: todayViewsResult[0]?.count || 0,
      weeklyViews: weeklyViewsResult[0]?.count || 0,
      monthlyViews: monthlyViewsResult[0]?.count || 0,
      averageViewDuration: Math.round(avgDurationResult[0]?.avgDuration || 0),
      averageScrollDepth: Math.round(avgScrollResult[0]?.avgScroll || 0),
      topReferrers: topReferrersResult.map(r => ({
        referrer: r.referrer || 'Direct',
        count: r.count
      })),
      deviceBreakdown: deviceBreakdownResult.map(d => ({
        device: d.device || 'Unknown',
        count: d.count
      })),
      countryBreakdown: countryBreakdownResult.map(c => ({
        country: c.country || 'Unknown',
        count: c.count
      })),
      hourlyViews: hourlyViewsResult.map(h => ({
        hour: h.hour,
        count: h.count
      }))
    };
  }

  // Get trending blogs based on recent views
  static async getTrendingBlogs(limit: number = 10): Promise<Array<{
    blogId: string;
    title: string;
    slug: string;
    viewCount: number;
    recentViews: number;
    trendScore: number;
  }>> {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const trendingBlogs = await db
      .select({
        blogId: blogViews.blogId,
        title: blogs.title,
        slug: blogs.slug,
        viewCount: blogs.viewCount,
        recentViews: count(),
        trendScore: sql<number>`
          (COUNT(*) * 0.7) + 
          (${blogs.viewCount} * 0.3) + 
          (COUNT(DISTINCT ${blogViews.userId}) * 0.5)
        `
      })
      .from(blogViews)
      .innerJoin(blogs, eq(blogViews.blogId, blogs.id))
      .where(and(
        eq(blogViews.isBot, false),
        gte(blogViews.createdAt, twentyFourHoursAgo),
        eq(blogs.status, 'published')
      ))
      .groupBy(blogViews.blogId, blogs.title, blogs.slug, blogs.viewCount)
      .orderBy(desc(sql`trendScore`))
      .limit(limit);

    return trendingBlogs.map(blog => ({
      blogId: blog.blogId,
      title: blog.title,
      slug: blog.slug,
      viewCount: blog.viewCount || 0,
      recentViews: blog.recentViews,
      trendScore: Math.round(blog.trendScore * 100) / 100
    }));
  }

  // Get view statistics for multiple blogs
  static async getBulkViewStats(blogIds: string[]): Promise<Map<string, {
    totalViews: number;
    uniqueViews: number;
    todayViews: number;
  }>> {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    const stats = await db
      .select({
        blogId: blogViews.blogId,
        totalViews: count(),
        uniqueViews: sql<number>`COUNT(CASE WHEN ${blogViews.isUnique} = 1 THEN 1 END)`,
        todayViews: sql<number>`COUNT(CASE WHEN ${blogViews.createdAt} >= ${todayStart} THEN 1 END)`
      })
      .from(blogViews)
      .where(and(
        sql`${blogViews.blogId} IN (${blogIds.map(id => `'${id}'`).join(',')})`,
        eq(blogViews.isBot, false)
      ))
      .groupBy(blogViews.blogId);

    const statsMap = new Map();
    stats.forEach(stat => {
      statsMap.set(stat.blogId, {
        totalViews: stat.totalViews,
        uniqueViews: stat.uniqueViews,
        todayViews: stat.todayViews
      });
    });

    return statsMap;
  }
}
