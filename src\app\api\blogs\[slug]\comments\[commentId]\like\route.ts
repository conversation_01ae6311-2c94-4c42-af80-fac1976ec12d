import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogComments, likes } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

// Toggle like on a blog comment
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string; commentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { commentId } = params;

    // Check if the comment exists
    const comment = await db.query.blogComments.findFirst({
      where: eq(blogComments.id, commentId),
      columns: { id: true },
    });

    if (!comment) {
      return NextResponse.json(
        { message: "Comment not found" },
        { status: 404 }
      );
    }

    // Check if user has already liked this comment
    const existingLike = await db.query.likes.findFirst({
      where: and(
        eq(likes.commentId, commentId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'like')
      ),
    });

    if (existingLike) {
      // Unlike: remove the like
      await db.delete(likes).where(eq(likes.id, existingLike.id));
      
      return NextResponse.json({
        message: "Comment unliked successfully",
        liked: false,
      });
    } else {
      // Like: add the like
      const likeId = uuidv4();
      await db.insert(likes).values({
        id: likeId,
        userId: session.user.id,
        postId: null,
        commentId,
        type: 'like',
      });

      return NextResponse.json({
        message: "Comment liked successfully",
        liked: true,
      });
    }
  } catch (error) {
    console.error("Error toggling blog comment like:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
