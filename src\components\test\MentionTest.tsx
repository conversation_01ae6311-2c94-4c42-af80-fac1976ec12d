"use client";

import { renderTextWithMentions } from "@/lib/utils/mention-utils";

export function MentionTest() {
  const testTexts = [
    "Hello @john how are you?",
    "Hey @jane doe, check this out!",
    "@admin please help me with this issue",
    "Thanks @user123 for the help!",
    "Multiple mentions: @alice @bob @charlie",
    "No mentions in this text",
    "@single_word and @multiple words here"
  ];

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm border">
      <h3 className="text-lg font-semibold mb-4">Mention Test</h3>
      <div className="space-y-3">
        {testTexts.map((text, index) => (
          <div key={index} className="p-3 bg-gray-50 rounded border">
            <div className="text-sm text-gray-600 mb-1">Input: {text}</div>
            <div className="text-sm">Output: {renderTextWithMentions(text)}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
