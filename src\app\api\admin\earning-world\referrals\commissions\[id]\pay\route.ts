import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCommissions, wallets, walletTransactions } from "@/lib/db/schema";
import { eq, sql, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// Process commission payment
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const commissionId = params.id;

    // Get the commission
    const commission = await db.query.referralCommissions.findFirst({
      where: eq(referralCommissions.id, commissionId),
    });

    if (!commission) {
      return NextResponse.json(
        { message: "Commission not found" },
        { status: 404 }
      );
    }

    if (commission.status !== 'approved') {
      return NextResponse.json(
        { message: "Commission must be approved before payment" },
        { status: 400 }
      );
    }

    if (commission.paidAt) {
      return NextResponse.json(
        { message: "Commission already paid" },
        { status: 400 }
      );
    }

    // Get or create wallet for the referrer
    let wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, commission.referrerId),
    });

    if (!wallet) {
      // Create wallet if it doesn't exist
      await db.insert(wallets).values({
        id: uuidv4(),
        userId: commission.referrerId,
        generalBalance: '0.00',
        earningBalance: '0.00',
        totalDeposited: '0.00',
        totalWithdrawn: '0.00',
        totalSent: '0.00',
        totalReceived: '0.00',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      wallet = await db.query.wallets.findFirst({
        where: eq(wallets.userId, commission.referrerId),
      });
    }

    if (!wallet) {
      return NextResponse.json(
        { message: "Failed to create or find wallet" },
        { status: 500 }
      );
    }

    // Start transaction
    await db.transaction(async (tx) => {
      // Update wallet earning balance
      await tx
        .update(wallets)
        .set({
          earningBalance: sql`${wallets.earningBalance} + ${commission.commissionAmount}`,
          updatedAt: new Date(),
        })
        .where(eq(wallets.id, wallet.id));

      // Create transaction record
      const transactionId = uuidv4();
      const commissionAmountStr = typeof commission.commissionAmount === 'string'
        ? commission.commissionAmount
        : commission.commissionAmount.toString();

      await tx.insert(walletTransactions).values({
        id: transactionId,
        userId: commission.referrerId,
        type: 'earning',
        amount: commissionAmountStr,
        fee: '0.00',
        netAmount: commissionAmountStr,
        walletType: 'earning',
        status: 'completed',
        reference: `commission_${commission.id}`,
        note: `Commission payment for ${commission.planName} referral`,
        metadata: {
          commissionId: commission.id,
          planName: commission.planName,
          type: 'commission_payment',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Mark commission as paid
      await tx
        .update(referralCommissions)
        .set({
          status: 'paid',
          paidAt: new Date(),
          walletTransactionId: transactionId,
          updatedAt: new Date(),
        })
        .where(eq(referralCommissions.id, commissionId));
    });

    return NextResponse.json({
      success: true,
      message: "Commission payment processed successfully",
    });

  } catch (error) {
    console.error("Error processing commission payment:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to process commission payment"
      },
      { status: 500 }
    );
  }
}
