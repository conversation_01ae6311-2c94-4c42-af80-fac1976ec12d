import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referrals, referralCodes, wallets } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";

// Update referral status
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { status } = await request.json();
    const referralId = params.id;

    if (!['pending', 'completed', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { message: "Invalid status" },
        { status: 400 }
      );
    }

    // Get the referral
    const referral = await db.query.referrals.findFirst({
      where: eq(referrals.id, referralId),
    });

    if (!referral) {
      return NextResponse.json(
        { message: "Referral not found" },
        { status: 404 }
      );
    }

    // Update referral status
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (status === 'completed') {
      updateData.completedAt = new Date();
    }

    await db
      .update(referrals)
      .set(updateData)
      .where(eq(referrals.id, referralId));

    // If status changed to completed, update referral code statistics
    if (status === 'completed' && referral.status !== 'completed') {
      await db
        .update(referralCodes)
        .set({
          totalEarnings: sql`${referralCodes.totalEarnings} + ${referral.rewardAmount}`,
          updatedAt: new Date(),
        })
        .where(eq(referralCodes.userId, referral.referrerId));
    }

    // If status changed from completed to something else, update referral code statistics
    if (status !== 'completed' && referral.status === 'completed') {
      await db
        .update(referralCodes)
        .set({
          totalEarnings: sql`${referralCodes.totalEarnings} - ${referral.rewardAmount}`,
          updatedAt: new Date(),
        })
        .where(eq(referralCodes.userId, referral.referrerId));
    }

    return NextResponse.json({
      success: true,
      message: "Referral status updated successfully",
    });

  } catch (error) {
    console.error("Error updating referral status:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update referral status"
      },
      { status: 500 }
    );
  }
}
