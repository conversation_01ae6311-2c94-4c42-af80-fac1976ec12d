import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

// Get user wallet settings
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has PIN
    const hasPin = await WalletService.hasPin(session.user.id);

    // Get PIN creation date if exists
    let pinSetAt = null;
    if (hasPin) {
      try {
        const pinInfo = await WalletService.getPinInfo(session.user.id);
        pinSetAt = pinInfo?.createdAt;
      } catch (error) {
        // PIN info not available
      }
    }

    // Get user's payment methods (for deposits/purchases)
    const paymentMethods = await WalletService.getUserPaymentMethods(session.user.id, 'payment');

    // Get user's payout methods (for withdrawals)
    const payoutMethods = await WalletService.getUserPaymentMethods(session.user.id, 'payout');

    // Get wallet settings (daily limits)
    const dailyLimits = await WalletService.getDailyLimits();

    // Mock notification settings (you can implement these in database)
    const settings = {
      hasPin,
      pinSetAt,
      emailNotifications: true,
      smsNotifications: false,
      transactionAlerts: true,
      dailyLimits: {
        deposit: dailyLimits.dailyDepositLimit || "10000.00",
        send: dailyLimits.dailySendLimit || "5000.00",
        cashout: dailyLimits.dailyCashoutLimit || "3000.00",
      },
      paymentMethods: paymentMethods || [],
      payoutMethods: payoutMethods || [],
    };

    return NextResponse.json({
      success: true,
      settings,
    });
  } catch (error: any) {
    console.error("Error fetching wallet settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch settings"
      },
      { status: 500 }
    );
  }
}

// Update notification settings
export async function PATCH(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { emailNotifications, smsNotifications, transactionAlerts } = body;

    // Here you would update the user's notification preferences in the database
    // For now, we'll just return success
    // await UserService.updateNotificationSettings(session.user.id, {
    //   emailNotifications,
    //   smsNotifications,
    //   transactionAlerts
    // });

    return NextResponse.json({
      success: true,
      message: "Settings updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update settings"
      },
      { status: 500 }
    );
  }
}
