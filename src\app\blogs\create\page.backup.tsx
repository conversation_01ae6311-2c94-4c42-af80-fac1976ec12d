"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { uploadToCloudinary } from "@/lib/cloudinary";
import {
  PhotoIcon,
  XMarkIcon,
  EyeIcon,
  DocumentTextIcon,
  TagIcon,
  ClockIcon,
  ArrowLeftIcon,
  PlusIcon
} from "@heroicons/react/24/outline";

interface Category {
  id: string;
  name: string;
  color: string;
}

export default function CreateBlogPage() {
  const router = useRouter();

  return (
    <MainLayout>
      <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        <h1>Create Blog - Minimal Test</h1>
        <p>This is a minimal test version to check if the component works.</p>
      </div>
    </MainLayout>
  );
}
