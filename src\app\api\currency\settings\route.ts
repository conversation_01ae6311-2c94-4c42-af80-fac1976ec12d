import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { siteSettings } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function GET() {
  try {
    // Fetch currency settings from database
    const currencySettings = await db
      .select()
      .from(siteSettings)
      .where(eq(siteSettings.groupName, 'currency'));

    // Convert to object format
    const settingsObject = currencySettings.reduce((acc, setting) => {
      acc[setting.key] = {
        value: setting.value,
        type: setting.type,
        label: setting.label,
        description: setting.description,
      };
      return acc;
    }, {} as Record<string, any>);

    // Return formatted settings
    const formattedSettings = {
      default_currency: settingsObject.default_currency?.value || 'USD',
      currency_symbol: settingsObject.currency_symbol?.value || '$',
      currency_position: settingsObject.currency_position?.value || 'before',
      decimal_places: parseInt(settingsObject.decimal_places?.value) || 2,
      thousands_separator: settingsObject.thousands_separator?.value || ',',
      decimal_separator: settingsObject.decimal_separator?.value || '.',
      supported_currencies: settingsObject.supported_currencies?.value || 'USD,EUR,GBP,BDT,INR',
      auto_currency_detection: settingsObject.auto_currency_detection?.value === 'true',
      currency_conversion_api: settingsObject.currency_conversion_api?.value || '',
    };

    return NextResponse.json(formattedSettings);
  } catch (error) {
    console.error('Error fetching currency settings:', error);

    // Return default settings on error
    return NextResponse.json({
      default_currency: 'USD',
      currency_symbol: '$',
      currency_position: 'before',
      decimal_places: 2,
      thousands_separator: ',',
      decimal_separator: '.',
      supported_currencies: 'USD,EUR,GBP,BDT,INR',
      auto_currency_detection: false,
      currency_conversion_api: '',
    });
  }
}
