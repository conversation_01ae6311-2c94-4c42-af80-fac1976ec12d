"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import {
  ArrowLeftIcon,
  ShieldCheckIcon,
  AdjustmentsHorizontalIcon,
  BellIcon,
  ClockIcon,
  UserGroupIcon,
  GlobeAltIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";

export default function AdminFeedSettingsPage() {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const [settings, setSettings] = useState({
    // Content filtering settings
    enableContentFiltering: true,
    filterProfanity: true,
    filterSensitiveContent: true,
    filterSpam: true,

    // Feed algorithm settings
    prioritizeRecentContent: true,
    prioritizeEngagement: true,
    prioritizeFriendsContent: true,
    maxPostsPerUser: 5,

    // Privacy settings
    defaultPostPrivacy: "public",
    allowPublicPosts: true,
    allowSubscribersPosts: true,
    allowPrivatePosts: true,

    // Notification settings
    notifyOnReports: true,
    notifyOnHighEngagement: true,
    reportThreshold: 3,

    // Moderation settings
    enableAutoModeration: true,
    requireApprovalForReportedPosts: true,
    autoHideReportedContent: true,
    moderationTeamEmails: "<EMAIL>, <EMAIL>",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSettings({
      ...settings,
      [name]: value,
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSettings({
      ...settings,
      [name]: checked,
    });
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSettings({
      ...settings,
      [name]: parseInt(value, 10),
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSaving(true);

    try {
      // In a real implementation, this would be an API call to save settings
      // For now, we'll just simulate with a timeout
      setTimeout(() => {
        alert("Feed settings saved successfully");
        setIsSaving(false);
      }, 1000);
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("Failed to save settings");
      setIsSaving(false);
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="outline"
            className="mr-4"
            onClick={() => router.push("/admin/posts")}
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Posts
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Feed Settings</h1>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <form onSubmit={handleSubmit}>
          <div className="p-6">
            {/* Content Filtering Section */}
            <div className="mb-8">
              <h2 className="mb-4 flex items-center text-lg font-medium text-gray-900">
                <ShieldCheckIcon className="mr-2 h-5 w-5 text-blue-500" />
                Content Filtering
              </h2>
              <div className="rounded-md bg-gray-50 p-4">
                <div className="mb-4">
                  <div className="flex items-center">
                    <input
                      id="enableContentFiltering"
                      name="enableContentFiltering"
                      type="checkbox"
                      checked={settings.enableContentFiltering}
                      onChange={handleCheckboxChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="enableContentFiltering" className="ml-2 block text-sm font-medium text-gray-700">
                      Enable content filtering
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Automatically filter posts and comments for inappropriate content
                  </p>
                </div>

                <div className="ml-6 space-y-3">
                  <div className="flex items-center">
                    <input
                      id="filterProfanity"
                      name="filterProfanity"
                      type="checkbox"
                      checked={settings.filterProfanity}
                      onChange={handleCheckboxChange}
                      disabled={!settings.enableContentFiltering}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <label htmlFor="filterProfanity" className={`ml-2 block text-sm ${settings.enableContentFiltering ? 'text-gray-700' : 'text-gray-400'}`}>
                      Filter profanity and offensive language
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="filterSensitiveContent"
                      name="filterSensitiveContent"
                      type="checkbox"
                      checked={settings.filterSensitiveContent}
                      onChange={handleCheckboxChange}
                      disabled={!settings.enableContentFiltering}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <label htmlFor="filterSensitiveContent" className={`ml-2 block text-sm ${settings.enableContentFiltering ? 'text-gray-700' : 'text-gray-400'}`}>
                      Filter sensitive content (violence, adult content)
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="filterSpam"
                      name="filterSpam"
                      type="checkbox"
                      checked={settings.filterSpam}
                      onChange={handleCheckboxChange}
                      disabled={!settings.enableContentFiltering}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <label htmlFor="filterSpam" className={`ml-2 block text-sm ${settings.enableContentFiltering ? 'text-gray-700' : 'text-gray-400'}`}>
                      Filter spam and promotional content
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Feed Algorithm Section */}
            <div className="mb-8">
              <h2 className="mb-4 flex items-center text-lg font-medium text-gray-900">
                <AdjustmentsHorizontalIcon className="mr-2 h-5 w-5 text-blue-500" />
                Feed Algorithm
              </h2>
              <div className="rounded-md bg-gray-50 p-4">
                <div className="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <div className="flex items-center">
                      <input
                        id="prioritizeRecentContent"
                        name="prioritizeRecentContent"
                        type="checkbox"
                        checked={settings.prioritizeRecentContent}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="prioritizeRecentContent" className="ml-2 block text-sm font-medium text-gray-700">
                        Prioritize recent content
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Show newer posts higher in the feed
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center">
                      <input
                        id="prioritizeEngagement"
                        name="prioritizeEngagement"
                        type="checkbox"
                        checked={settings.prioritizeEngagement}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="prioritizeEngagement" className="ml-2 block text-sm font-medium text-gray-700">
                        Prioritize high engagement
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Show posts with more likes and comments higher in the feed
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center">
                      <input
                        id="prioritizeFriendsContent"
                        name="prioritizeFriendsContent"
                        type="checkbox"
                        checked={settings.prioritizeFriendsContent}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="prioritizeFriendsContent" className="ml-2 block text-sm font-medium text-gray-700">
                        Prioritize friends' content
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Show posts from friends higher in the feed
                    </p>
                  </div>

                  <div>
                    <label htmlFor="maxPostsPerUser" className="block text-sm font-medium text-gray-700">
                      Max posts per user in feed
                    </label>
                    <input
                      type="number"
                      name="maxPostsPerUser"
                      id="maxPostsPerUser"
                      min="1"
                      max="20"
                      value={settings.maxPostsPerUser}
                      onChange={handleNumberChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Limit the number of posts from a single user in the feed
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Privacy Settings Section */}
            <div className="mb-8">
              <h2 className="mb-4 flex items-center text-lg font-medium text-gray-900">
                <LockClosedIcon className="mr-2 h-5 w-5 text-blue-500" />
                Privacy Settings
              </h2>
              <div className="rounded-md bg-gray-50 p-4">
                <div className="mb-4">
                  <label htmlFor="defaultPostPrivacy" className="block text-sm font-medium text-gray-700">
                    Default post privacy
                  </label>
                  <select
                    id="defaultPostPrivacy"
                    name="defaultPostPrivacy"
                    value={settings.defaultPostPrivacy}
                    onChange={handleInputChange}
                    className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="public">Public</option>
                    <option value="subscribers">Subscribers</option>
                    <option value="private">Private</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    Default privacy setting for new posts
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      id="allowPublicPosts"
                      name="allowPublicPosts"
                      type="checkbox"
                      checked={settings.allowPublicPosts}
                      onChange={handleCheckboxChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="allowPublicPosts" className="ml-2 block text-sm font-medium text-gray-700">
                      Allow public posts
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="allowSubscribersPosts"
                      name="allowSubscribersPosts"
                      type="checkbox"
                      checked={settings.allowSubscribersPosts}
                      onChange={handleCheckboxChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="allowSubscribersPosts" className="ml-2 block text-sm font-medium text-gray-700">
                      Allow subscribers-only posts
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="allowPrivatePosts"
                      name="allowPrivatePosts"
                      type="checkbox"
                      checked={settings.allowPrivatePosts}
                      onChange={handleCheckboxChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="allowPrivatePosts" className="ml-2 block text-sm font-medium text-gray-700">
                      Allow private posts
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Notification Settings Section */}
            <div className="mb-8">
              <h2 className="mb-4 flex items-center text-lg font-medium text-gray-900">
                <BellIcon className="mr-2 h-5 w-5 text-blue-500" />
                Notification Settings
              </h2>
              <div className="rounded-md bg-gray-50 p-4">
                <div className="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <div className="flex items-center">
                      <input
                        id="notifyOnReports"
                        name="notifyOnReports"
                        type="checkbox"
                        checked={settings.notifyOnReports}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="notifyOnReports" className="ml-2 block text-sm font-medium text-gray-700">
                        Notify on reported content
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Send notifications when content is reported
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center">
                      <input
                        id="notifyOnHighEngagement"
                        name="notifyOnHighEngagement"
                        type="checkbox"
                        checked={settings.notifyOnHighEngagement}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="notifyOnHighEngagement" className="ml-2 block text-sm font-medium text-gray-700">
                        Notify on high engagement
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Send notifications for posts with unusually high engagement
                    </p>
                  </div>

                  <div>
                    <label htmlFor="reportThreshold" className="block text-sm font-medium text-gray-700">
                      Report threshold
                    </label>
                    <input
                      type="number"
                      name="reportThreshold"
                      id="reportThreshold"
                      min="1"
                      max="20"
                      value={settings.reportThreshold}
                      onChange={handleNumberChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Number of reports before content is flagged for review
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Moderation Settings Section */}
            <div className="mb-8">
              <h2 className="mb-4 flex items-center text-lg font-medium text-gray-900">
                <ShieldCheckIcon className="mr-2 h-5 w-5 text-blue-500" />
                Moderation Settings
              </h2>
              <div className="rounded-md bg-gray-50 p-4">
                <div className="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <div className="flex items-center">
                      <input
                        id="enableAutoModeration"
                        name="enableAutoModeration"
                        type="checkbox"
                        checked={settings.enableAutoModeration}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="enableAutoModeration" className="ml-2 block text-sm font-medium text-gray-700">
                        Enable auto-moderation
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Automatically moderate content based on rules
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center">
                      <input
                        id="requireApprovalForReportedPosts"
                        name="requireApprovalForReportedPosts"
                        type="checkbox"
                        checked={settings.requireApprovalForReportedPosts}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="requireApprovalForReportedPosts" className="ml-2 block text-sm font-medium text-gray-700">
                        Require approval for reported posts
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Reported posts need admin approval to remain visible
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center">
                      <input
                        id="autoHideReportedContent"
                        name="autoHideReportedContent"
                        type="checkbox"
                        checked={settings.autoHideReportedContent}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="autoHideReportedContent" className="ml-2 block text-sm font-medium text-gray-700">
                        Auto-hide reported content
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Automatically hide content that exceeds report threshold
                    </p>
                  </div>

                  <div>
                    <label htmlFor="moderationTeamEmails" className="block text-sm font-medium text-gray-700">
                      Moderation team emails
                    </label>
                    <textarea
                      id="moderationTeamEmails"
                      name="moderationTeamEmails"
                      rows={2}
                      value={settings.moderationTeamEmails}
                      onChange={handleInputChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Enter email addresses separated by commas"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Email addresses to receive moderation notifications
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 bg-gray-50 px-4 py-3 text-right sm:px-6">
            <Button
              type="button"
              variant="outline"
              className="mr-3"
              onClick={() => router.push("/admin/posts")}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? <Spinner size="sm" className="mr-2" /> : null}
              Save Settings
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
