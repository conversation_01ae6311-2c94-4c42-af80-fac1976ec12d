import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralSettings } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Get referral settings
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const settings = await db.query.referralSettings.findFirst({
      where: eq(referralSettings.id, 'default'),
    });

    const defaultSettings = {
      id: 'default',
      isEnabled: true,
      rewardAmount: "5.00",
      minPayoutThreshold: "10.00",
      requiresVerification: false,
      maxReferralsPerUser: 100,
      rewardBothUsers: false,
      referredUserReward: "0.00",
    };

    return NextResponse.json({
      success: true,
      data: settings || defaultSettings,
    });

  } catch (error) {
    console.error("Error fetching referral settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch referral settings"
      },
      { status: 500 }
    );
  }
}

// Update referral settings
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      isEnabled,
      rewardAmount,
      minPayoutThreshold,
      requiresVerification,
      maxReferralsPerUser,
      rewardBothUsers,
      referredUserReward
    } = body;

    // Validate input
    if (typeof isEnabled !== 'boolean') {
      return NextResponse.json(
        { message: "Invalid isEnabled value" },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(rewardAmount)) || parseFloat(rewardAmount) < 0) {
      return NextResponse.json(
        { message: "Invalid reward amount" },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(minPayoutThreshold)) || parseFloat(minPayoutThreshold) < 0) {
      return NextResponse.json(
        { message: "Invalid minimum payout threshold" },
        { status: 400 }
      );
    }

    if (isNaN(parseInt(maxReferralsPerUser)) || parseInt(maxReferralsPerUser) < 1) {
      return NextResponse.json(
        { message: "Invalid max referrals per user" },
        { status: 400 }
      );
    }

    // Check if settings exist
    const existingSettings = await db.query.referralSettings.findFirst({
      where: eq(referralSettings.id, 'default'),
    });

    const settingsData = {
      isEnabled,
      rewardAmount: parseFloat(rewardAmount).toFixed(2),
      minPayoutThreshold: parseFloat(minPayoutThreshold).toFixed(2),
      requiresVerification: requiresVerification || false,
      maxReferralsPerUser: parseInt(maxReferralsPerUser),
      rewardBothUsers: rewardBothUsers || false,
      referredUserReward: rewardBothUsers ? parseFloat(referredUserReward || '0').toFixed(2) : '0.00',
      updatedAt: new Date(),
    };

    if (existingSettings) {
      // Update existing settings
      await db
        .update(referralSettings)
        .set(settingsData)
        .where(eq(referralSettings.id, 'default'));
    } else {
      // Insert new settings
      await db.insert(referralSettings).values({
        id: 'default',
        ...settingsData,
        createdAt: new Date(),
      });
    }

    return NextResponse.json({
      success: true,
      message: "Referral settings updated successfully",
      data: {
        id: 'default',
        ...settingsData,
      },
    });

  } catch (error) {
    console.error("Error updating referral settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update referral settings"
      },
      { status: 500 }
    );
  }
}
