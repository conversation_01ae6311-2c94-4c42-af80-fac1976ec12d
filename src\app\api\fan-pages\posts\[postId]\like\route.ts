import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePostLikes, fanPagePosts, fanPages } from "@/lib/db/schema";
import { eq, and, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    postId: string;
  }>;
}

// POST /api/fan-pages/posts/[postId]/like - Toggle like on fan page post
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the post exists
    const postResult = await db
      .select({
        id: fanPagePosts.id,
        fanPageId: fanPagePosts.fanPageId,
        likeCount: fanPagePosts.likeCount,
      })
      .from(fanPagePosts)
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    if (postResult.length === 0) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    const post = postResult[0];

    // Check if user has already liked this post
    const existingLike = await db
      .select()
      .from(fanPagePostLikes)
      .where(
        and(
          eq(fanPagePostLikes.fanPagePostId, postId),
          eq(fanPagePostLikes.userId, session.user.id),
          eq(fanPagePostLikes.type, 'like')
        )
      )
      .limit(1);

    let action: 'liked' | 'unliked';
    let newLikeCount: number;

    if (existingLike.length > 0) {
      // Unlike the post
      await db
        .delete(fanPagePostLikes)
        .where(
          and(
            eq(fanPagePostLikes.fanPagePostId, postId),
            eq(fanPagePostLikes.userId, session.user.id),
            eq(fanPagePostLikes.type, 'like')
          )
        );

      // Decrease like count
      newLikeCount = Math.max(0, post.likeCount - 1);
      await db
        .update(fanPagePosts)
        .set({ likeCount: newLikeCount })
        .where(eq(fanPagePosts.id, postId));

      action = 'unliked';
    } else {
      // Check if user has disliked this post and remove dislike
      const existingDislike = await db
        .select()
        .from(fanPagePostLikes)
        .where(
          and(
            eq(fanPagePostLikes.fanPagePostId, postId),
            eq(fanPagePostLikes.userId, session.user.id),
            eq(fanPagePostLikes.type, 'angry') // Using 'angry' as dislike
          )
        )
        .limit(1);

      if (existingDislike.length > 0) {
        // Remove existing dislike
        await db
          .delete(fanPagePostLikes)
          .where(
            and(
              eq(fanPagePostLikes.fanPagePostId, postId),
              eq(fanPagePostLikes.userId, session.user.id),
              eq(fanPagePostLikes.type, 'angry')
            )
          );
      }

      // Like the post
      await db.insert(fanPagePostLikes).values({
        id: uuidv4(),
        userId: session.user.id,
        fanPagePostId: postId,
        type: 'like',
      });

      // Increase like count
      newLikeCount = post.likeCount + 1;
      await db
        .update(fanPagePosts)
        .set({ likeCount: newLikeCount })
        .where(eq(fanPagePosts.id, postId));

      action = 'liked';
    }

    return NextResponse.json({
      success: true,
      action,
      likeCount: newLikeCount,
    });

  } catch (error) {
    console.error("Error toggling like on fan page post:", error);
    return NextResponse.json(
      { error: "Failed to toggle like" },
      { status: 500 }
    );
  }
}
