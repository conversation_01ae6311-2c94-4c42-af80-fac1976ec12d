"use client";

import { useState, useEffect } from "react";
import { BlogCard } from "@/components/blog/BlogCard";
import { MonetizationStatusBadge } from "@/components/blog/MonetizationStatusBadge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { 
  DocumentTextIcon,
  EyeIcon,
  CurrencyDollarIcon,
  PencilIcon,
  TrashIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  coverImage?: string | null;
  readTime?: number | null;
  viewCount?: number;
  publishedAt?: string | null;
  status: string;
  author: {
    id: string;
    name: string;
    image?: string | null;
    username?: string | null;
  };
  category?: {
    id: string;
    name: string;
    color: string;
  } | null;
  tags?: string[] | null;
  _count?: {
    likes: number;
    comments: number;
  };
  isLiked?: boolean;
  isBookmarked?: boolean;
  monetization?: {
    id: string;
    isEnabled: boolean;
    isApproved: boolean;
    status: 'pending' | 'approved' | 'rejected' | 'suspended';
    cprRate: string;
    totalEarnings: string;
    rejectionReason?: string;
  } | null;
}

interface AuthorBlogListProps {
  authorId: string;
  currentUserId?: string | null;
  onLike?: (blogId: string) => void;
  onBookmark?: (blogId: string) => void;
  onShare?: (blogId: string) => void;
  onEdit?: (blogId: string, slug: string) => void;
  onDelete?: (blogId: string, slug: string) => void;
}

export function AuthorBlogList({
  authorId,
  currentUserId,
  onLike,
  onBookmark,
  onShare,
  onEdit,
  onDelete
}: AuthorBlogListProps) {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'published' | 'draft' | 'monetized'>('all');

  useEffect(() => {
    fetchBlogs();
  }, [authorId, filter]);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        authorId,
        status: filter === 'all' ? '' : filter === 'monetized' ? 'published' : filter,
        includeMonetization: 'true'
      });

      const response = await fetch(`/api/blogs?${params}`);
      if (response.ok) {
        const data = await response.json();
        let filteredBlogs = data.blogs;
        
        if (filter === 'monetized') {
          filteredBlogs = data.blogs.filter((blog: Blog) => blog.monetization);
        }
        
        setBlogs(filteredBlogs);
      }
    } catch (error) {
      console.error("Error fetching blogs:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusCounts = () => {
    const published = blogs.filter(blog => blog.status === 'published').length;
    const draft = blogs.filter(blog => blog.status === 'draft').length;
    const monetized = blogs.filter(blog => blog.monetization).length;
    
    return { published, draft, monetized };
  };

  const { published, draft, monetized } = getStatusCounts();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-8">
          <Spinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DocumentTextIcon className="h-5 w-5" />
            Your Blogs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              All ({blogs.length})
            </Button>
            <Button
              variant={filter === 'published' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('published')}
            >
              Published ({published})
            </Button>
            <Button
              variant={filter === 'draft' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('draft')}
            >
              Drafts ({draft})
            </Button>
            <Button
              variant={filter === 'monetized' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('monetized')}
            >
              <CurrencyDollarIcon className="h-4 w-4 mr-1" />
              Monetized ({monetized})
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Blog List */}
      {blogs.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filter === 'all' ? 'No blogs found' : 
               filter === 'monetized' ? 'No monetized blogs' :
               `No ${filter} blogs`}
            </h3>
            <p className="text-gray-600 mb-4">
              {filter === 'all' ? 'Start writing your first blog!' :
               filter === 'monetized' ? 'Request monetization for your published blogs.' :
               `You don't have any ${filter} blogs yet.`}
            </p>
            {filter === 'all' && (
              <Link href="/blogs/create">
                <Button>
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Write Your First Blog
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {blogs.map((blog) => (
            <Card key={blog.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        <Link 
                          href={`/blogs/${blog.slug}`}
                          className="hover:text-blue-600 transition-colors"
                        >
                          {blog.title}
                        </Link>
                      </h3>
                      <Badge variant={blog.status === 'published' ? 'success' : 'secondary'}>
                        {blog.status}
                      </Badge>
                      {blog.monetization && (
                        <MonetizationStatusBadge 
                          status={blog.monetization.status}
                          size="sm"
                        />
                      )}
                    </div>
                    
                    {blog.excerpt && (
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {blog.excerpt}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <EyeIcon className="h-4 w-4 mr-1" />
                        {blog.viewCount || 0} views
                      </div>
                      {blog._count && (
                        <>
                          <div>{blog._count.likes} likes</div>
                          <div>{blog._count.comments} comments</div>
                        </>
                      )}
                      {blog.monetization?.status === 'approved' && (
                        <div className="text-green-600 font-medium">
                          <CurrencyDollarIcon className="h-4 w-4 inline mr-1" />
                          ${parseFloat(blog.monetization.totalEarnings).toFixed(2)} earned
                        </div>
                      )}
                    </div>
                    
                    {blog.monetization?.status === 'rejected' && blog.monetization.rejectionReason && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                        <strong>Rejection Reason:</strong> {blog.monetization.rejectionReason}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit?.(blog.id, blog.slug)}
                    >
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDelete?.(blog.id, blog.slug)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
