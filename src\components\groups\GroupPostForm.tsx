"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import { useDropzone } from "react-dropzone";
import { XMarkIcon, PhotoIcon, VideoCameraIcon, FaceSmileIcon, MapPinIcon } from "@heroicons/react/24/outline";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";
import EmojiPicker from 'emoji-picker-react';

interface GroupPostFormProps {
  groupId: string;
  groupName: string;
  userImage: string | null;
  isAdmin: boolean;
  isModerator: boolean;
  onPostCreated?: () => void;
}

export function GroupPostForm({
  groupId,
  groupName,
  userImage,
  isAdmin,
  isModerator,
  onPostCreated,
}: GroupPostFormProps) {
  const router = useRouter();
  const [content, setContent] = useState("");
  const [images, setImages] = useState<File[]>([]);
  const [videos, setVideos] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [location, setLocation] = useState("");
  const [isAnnouncement, setIsAnnouncement] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { getRootProps: getImageRootProps, getInputProps: getImageInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif"],
    },
    maxFiles: 5,
    onDrop: (acceptedFiles) => {
      setImages((prev) => [...prev, ...acceptedFiles].slice(0, 5));
    },
  });

  const { getRootProps: getVideoRootProps, getInputProps: getVideoInputProps } = useDropzone({
    accept: {
      "video/*": [".mp4", ".mov", ".avi"],
    },
    maxFiles: 2,
    onDrop: (acceptedFiles) => {
      setVideos((prev) => [...prev, ...acceptedFiles].slice(0, 2));
    },
  });

  const handleEmojiClick = (emojiData: any) => {
    const emoji = emojiData.emoji;
    const textarea = textareaRef.current;

    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + emoji + content.substring(end);
      setContent(newContent);

      // Set cursor position after the inserted emoji
      setTimeout(() => {
        textarea.selectionStart = start + emoji.length;
        textarea.selectionEnd = start + emoji.length;
        textarea.focus();
      }, 0);
    } else {
      setContent((prev) => prev + emoji);
    }
  };

  const removeImage = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
  };

  const removeVideo = (index: number) => {
    setVideos((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!content.trim() && images.length === 0 && videos.length === 0) {
      setError("Please add some content to your post");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Upload images if any
      let uploadedImages: string[] = [];
      if (images.length > 0) {
        uploadedImages = await uploadMultipleToCloudinary(images);
      }

      // Upload videos if any
      let uploadedVideos: string[] = [];
      if (videos.length > 0) {
        uploadedVideos = await uploadMultipleToCloudinary(videos);
      }

      // Create the post
      const response = await fetch(`/api/groups/${groupId}/posts`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content,
          images: uploadedImages,
          videos: uploadedVideos,
          location: location || undefined,
          isAnnouncement,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create post");
      }

      // Reset form
      setContent("");
      setImages([]);
      setVideos([]);
      setLocation("");
      setIsAnnouncement(false);
      setShowEmojiPicker(false);

      // Refresh the page or call the callback
      if (onPostCreated) {
        onPostCreated();
      } else {
        router.refresh();
      }
    } catch (error) {
      console.error("Error creating post:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="rounded-xl bg-white p-4 shadow-sm transition-all duration-200 hover:shadow-md">
      <form onSubmit={handleSubmit}>
        <div className="flex space-x-3">
          <div className="flex-shrink-0">
            {userImage ? (
              <Image
                src={userImage}
                alt="User"
                width={40}
                height={40}
                className="h-10 w-10 rounded-full border border-gray-200"
              />
            ) : (
              <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center">
                <span className="text-white text-sm font-bold">U</span>
              </div>
            )}
          </div>
          <div className="min-w-0 flex-1">
            {!isExpanded ? (
              <div
                onClick={() => setIsExpanded(true)}
                className="rounded-full bg-gray-100 px-4 py-2.5 text-gray-500 hover:bg-gray-200 cursor-pointer transition-colors duration-200"
              >
                <span>Write something to {groupName}...</span>
              </div>
            ) : (
              <>
                <div className="mb-3">
                  <textarea
                    ref={textareaRef}
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder={`What's on your mind, ${groupName}?`}
                    className="block w-full rounded-lg border-0 p-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                    rows={4}
                    autoFocus
                  />
                </div>

                {/* Media previews */}
                {(images.length > 0 || videos.length > 0) && (
                  <div className="mb-4 grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4">
                    {images.map((image, index) => (
                      <div key={`image-${index}`} className="relative h-28 rounded-lg overflow-hidden shadow-sm">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Preview ${index}`}
                          className="h-full w-full object-cover"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute top-1.5 right-1.5 rounded-full bg-black/60 p-1 text-white hover:bg-black/80 transition-colors duration-200"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                    {videos.map((video, index) => (
                      <div key={`video-${index}`} className="relative h-28 rounded-lg overflow-hidden shadow-sm bg-gray-100">
                        <video
                          src={URL.createObjectURL(video)}
                          className="h-full w-full object-cover"
                        />
                        <div className="absolute inset-0 flex items-center justify-center bg-black/10">
                          <VideoCameraIcon className="h-8 w-8 text-white drop-shadow-md" />
                        </div>
                        <button
                          type="button"
                          onClick={() => removeVideo(index)}
                          className="absolute top-1.5 right-1.5 rounded-full bg-black/60 p-1 text-white hover:bg-black/80 transition-colors duration-200"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Location input */}
                {location && (
                  <div className="mb-3 flex items-center rounded-lg bg-blue-50 px-3 py-2 text-sm text-blue-700">
                    <MapPinIcon className="mr-1.5 h-4 w-4 text-blue-500" />
                    <span className="flex-1">{location}</span>
                    <button
                      type="button"
                      onClick={() => setLocation("")}
                      className="ml-1 text-blue-400 hover:text-blue-600 transition-colors duration-200"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                )}

                {/* Announcement checkbox for admins/moderators */}
                {(isAdmin || isModerator) && (
                  <div className="mb-4 flex items-center rounded-lg bg-yellow-50 px-3 py-2">
                    <input
                      type="checkbox"
                      id="announcement"
                      checked={isAnnouncement}
                      onChange={(e) => setIsAnnouncement(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                    />
                    <label htmlFor="announcement" className="ml-2 block text-sm text-yellow-700">
                      Post as announcement (notifies all members)
                    </label>
                  </div>
                )}

                {error && (
                  <div className="mb-4 rounded-lg bg-red-50 p-3 text-sm text-red-600">
                    {error}
                  </div>
                )}

                <div className="flex items-center justify-between border-t border-gray-200 pt-3">
                  <div className="flex flex-wrap gap-1">
                    <button
                      type="button"
                      className="rounded-full p-2 text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
                      {...getImageRootProps()}
                    >
                      <PhotoIcon className="h-5 w-5" />
                      <input {...getImageInputProps()} />
                    </button>
                    <button
                      type="button"
                      className="rounded-full p-2 text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
                      {...getVideoRootProps()}
                    >
                      <VideoCameraIcon className="h-5 w-5" />
                      <input {...getVideoInputProps()} />
                    </button>
                    <div className="relative">
                      <button
                        type="button"
                        className="rounded-full p-2 text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                      >
                        <FaceSmileIcon className="h-5 w-5" />
                      </button>
                      {showEmojiPicker && (
                        <div className="absolute bottom-12 left-0 z-10 shadow-xl rounded-lg overflow-hidden">
                          <EmojiPicker onEmojiClick={handleEmojiClick} />
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      className="rounded-full p-2 text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
                      onClick={() => {
                        const location = prompt("Enter your location:");
                        if (location) setLocation(location);
                      }}
                    >
                      <MapPinIcon className="h-5 w-5" />
                    </button>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setIsExpanded(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      size="sm"
                      isLoading={isSubmitting}
                      disabled={isSubmitting || (!content.trim() && images.length === 0 && videos.length === 0)}
                    >
                      Post
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
