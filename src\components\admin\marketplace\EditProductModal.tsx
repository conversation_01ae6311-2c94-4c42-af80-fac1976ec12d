"use client";

import { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Select } from "@/components/ui/Select";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { XMarkIcon, PhotoIcon } from "@heroicons/react/24/outline";

interface Product {
  id: string;
  title: string;
  description: string | null;
  price: number;
  condition: "new" | "like_new" | "good" | "fair" | "poor";
  category: string;
  location: string | null;
  photos: string[] | null;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  store: {
    id: string;
    name: string;
    logo: string | null;
    ownerId: string;
  };
  owner?: {
    id: string;
    name: string;
    image: string | null;
  };
}

interface EditProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
  onProductUpdated: () => void;
}

export function EditProductModal({
  isOpen,
  onClose,
  product,
  onProductUpdated,
}: EditProductModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    price: 0,
    condition: "new" as "new" | "like_new" | "good" | "fair" | "poor",
    category: "",
    location: "",
  });

  // Load product data when modal opens
  useEffect(() => {
    if (product) {
      setFormData({
        title: product.title,
        description: product.description || "",
        price: product.price / 100, // Convert cents to dollars for display
        condition: product.condition,
        category: product.category,
        location: product.location || "",
      });
    }
  }, [product]);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "price" ? parseFloat(value) : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!product) return;

    setIsLoading(true);

    try {
      // Convert price back to cents for API
      const apiData = {
        ...formData,
        price: Math.round(formData.price * 100),
      };

      const response = await fetch(`/api/admin/marketplace/products/${product.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update product");
      }

      toast.success("Product updated successfully");
      onProductUpdated();
      onClose();
    } catch (error) {
      console.error("Error updating product:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update product"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      maxWidth="2xl"
      className="p-0"
    >
      <div className="relative max-h-[90vh] w-full overflow-y-auto p-8">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Edit Product</h2>
            <button
              type="button"
              onClick={onClose}
              className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {product ? (
            <form onSubmit={handleSubmit}>
              <div className="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="sm:col-span-2">
                  <label
                    htmlFor="title"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Product Title
                  </label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    required
                    placeholder="Enter product title"
                  />
                </div>

                <div className="sm:col-span-2">
                  <label
                    htmlFor="description"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Description
                  </label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                    placeholder="Enter product description"
                  />
                </div>

                <div>
                  <label
                    htmlFor="price"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Price ($)
                  </label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.price}
                    onChange={handleChange}
                    required
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label
                    htmlFor="condition"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Condition
                  </label>
                  <Select
                    id="condition"
                    name="condition"
                    value={formData.condition}
                    onChange={handleChange}
                    required
                  >
                    <option value="new">New</option>
                    <option value="like_new">Like New</option>
                    <option value="good">Good</option>
                    <option value="fair">Fair</option>
                    <option value="poor">Poor</option>
                  </Select>
                </div>

                <div>
                  <label
                    htmlFor="category"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Category
                  </label>
                  <Select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                  >
                    <option value="Electronics">Electronics</option>
                    <option value="Clothing & Accessories">Clothing & Accessories</option>
                    <option value="Home & Garden">Home & Garden</option>
                    <option value="Toys & Games">Toys & Games</option>
                    <option value="Sports & Outdoors">Sports & Outdoors</option>
                  </Select>
                </div>

                <div>
                  <label
                    htmlFor="location"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Location
                  </label>
                  <Input
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleChange}
                    placeholder="Enter location"
                  />
                </div>

                {/* Product photos preview */}
                <div className="sm:col-span-2">
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Product Photos
                  </label>
                  <div className="mt-1 flex flex-wrap gap-2">
                    {product.photos && product.photos.length > 0 ? (
                      product.photos.map((photo, index) => (
                        <div
                          key={index}
                          className="relative h-20 w-20 overflow-hidden rounded-md border border-gray-200"
                        >
                          <Image
                            src={photo}
                            alt={`Product photo ${index + 1}`}
                            width={80}
                            height={80}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ))
                    ) : (
                      <div className="flex h-20 w-20 items-center justify-center rounded-md border border-gray-200 bg-gray-50">
                        <PhotoIcon className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Photos cannot be edited here. Use the product edit page for photo management.
                  </p>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <Button
                  type="button"
                  onClick={onClose}
                  variant="outline"
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Spinner size="sm" className="mr-2" /> Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </div>
            </form>
          ) : (
            <div className="flex h-64 items-center justify-center">
              <Spinner size="lg" />
            </div>
          )}
      </div>
    </Modal>
  );
}
