import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

/**
 * Application configuration
 * This file centralizes all configuration settings for the application
 */

// Database configuration
export const dbConfig = {
  host: process.env.DATABASE_HOST || 'localhost',
  username: process.env.DATABASE_USERNAME || 'root',
  password: process.env.DATABASE_PASSWORD || '',
  database: process.env.DATABASE_NAME || 'hifnf_db',
  port: parseInt(process.env.DATABASE_PORT || '3306'),
  // Connection pool configuration - Optimized for high performance
  pool: {
    waitForConnections: true,
    connectionLimit: parseInt(process.env.DATABASE_CONNECTION_LIMIT || '50'), // বৃদ্ধি করা হয়েছে
    maxIdle: parseInt(process.env.DATABASE_MAX_IDLE || '20'), // Idle connections বৃদ্ধি
    idleTimeout: parseInt(process.env.DATABASE_IDLE_TIMEOUT || '60000'), // 1 minute
    queueLimit: parseInt(process.env.DATABASE_QUEUE_LIMIT || '100'), // Queue limit add করা
    enableKeepAlive: true,
    keepAliveInitialDelay: 0,
    // Only valid MySQL2 pool options are used
  },
};

// Server configuration
export const serverConfig = {
  port: parseInt(process.env.PORT || '3000'),
  hostname: process.env.HOSTNAME || 'localhost',
  nodeEnv: process.env.NODE_ENV || 'development',
  isDev: process.env.NODE_ENV !== 'production',
};

// NextAuth configuration
export const authConfig = {
  nextAuthUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  nextAuthSecret: process.env.NEXTAUTH_SECRET || 'your-nextauth-secret-key-change-this-in-production',

  // OAuth providers
  googleClientId: process.env.GOOGLE_CLIENT_ID || '',
  googleClientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  githubClientId: process.env.GITHUB_CLIENT_ID || '',
  githubClientSecret: process.env.GITHUB_CLIENT_SECRET || '',
};

// Cloudinary configuration
export const cloudinaryConfig = {
  cloudName: process.env.CLOUDINARY_CLOUD_NAME || 'dlmmjsbsh',
  apiKey: process.env.CLOUDINARY_API_KEY || '',
  apiSecret: process.env.CLOUDINARY_API_SECRET || '',
  url: process.env.CLOUDINARY_URL || '',
};

// Application configuration
export const appConfig = {
  name: 'HIFNF',
  description: 'A social media platform to connect with friends and share your life',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  version: '0.1.0',
};

// Dynamic URL helper for client and server side
export const getAppUrl = (req?: any): string => {
  // If NEXT_PUBLIC_APP_URL is set, use it
  if (process.env.NEXT_PUBLIC_APP_URL) {
    return process.env.NEXT_PUBLIC_APP_URL;
  }

  // For client side (browser)
  if (typeof window !== 'undefined') {
    return `${window.location.protocol}//${window.location.host}`;
  }

  // For server side with request object
  if (req) {
    const protocol = req.headers['x-forwarded-proto'] ||
                    (req.connection?.encrypted ? 'https' : 'http');
    const host = req.headers['x-forwarded-host'] ||
                req.headers.host ||
                'localhost:3001';
    return `${protocol}://${host}`;
  }

  // Fallback for server side without request
  return process.env.NEXTAUTH_URL || 'http://localhost:3001';
};

// Image optimization configuration
export const imageConfig = {
  remotePatterns: [
    {
      protocol: 'https',
      hostname: 'res.cloudinary.com',
      pathname: '/**',
    },
    {
      protocol: 'https',
      hostname: 'images.unsplash.com',
      pathname: '/**',
    },
    {
      protocol: 'https',
      hostname: 'i.pravatar.cc',
      pathname: '/**',
    },
  ],
  formats: ['image/avif', 'image/webp'],
  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  minimumCacheTTL: 60,
  dangerouslyAllowSVG: true,
  contentDispositionType: 'attachment',
  contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
};

// Image placeholder configuration (separate from Next.js config)
export const placeholderConfig = {
  defaultPlaceholder: 'blur',
  defaultBlurDataURL: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiB2aWV3Qm94PSIwIDAgNDAwIDQwMCI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2YxZjVmOSIvPjwvc3ZnPg==',
};

// Next.js experimental features
export const experimentalConfig = {
  // Enable optimizations for improved performance
  optimizePackageImports: ['react', 'react-dom', 'react-icons', '@headlessui/react', '@heroicons/react'],
  // Enable serverActions for better server-side functionality
  serverActions: {
    allowedOrigins: ['localhost:3000']
  },
};

// Export default configuration object that includes all config sections
const config = {
  db: dbConfig,
  server: serverConfig,
  auth: authConfig,
  cloudinary: cloudinaryConfig,
  app: appConfig,
  image: imageConfig,
  placeholder: placeholderConfig,
  experimental: experimentalConfig,
};

export default config;
