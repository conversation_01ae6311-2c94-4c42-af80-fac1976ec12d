import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPageMessages, fanPages, users, notifications } from "@/lib/db/schema";
import { eq, and, desc, sql, inArray } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { emitMessage, createMessagePayload } from "@/lib/partykit/server-utils";
import { emitNotification } from "@/lib/utils/notifications";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

const sendMessageSchema = z.object({
  content: z.string().min(1).max(5000),
  isReply: z.boolean().optional().default(false), // true if this is a reply from page owner
  recipientId: z.string().optional(), // user ID to reply to (required if isReply is true)
});

// GET - Fetch messages for a fan page
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;

    // Verify fan page exists and user has access
    const page = await db.query.fanPages.findFirst({
      where: eq(fanPages.id, pageId),
    });

    if (!page) {
      return NextResponse.json({ error: "Fan page not found" }, { status: 404 });
    }

    // Check if user is the page owner or has permission to view messages
    const isOwner = page.ownerId === session.user.id;
    if (!isOwner) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Get messages with sender info
    const messages = await db
      .select({
        id: fanPageMessages.id,
        fanPageId: fanPageMessages.fanPageId,
        senderId: fanPageMessages.senderId,
        content: fanPageMessages.content,
        isFromPage: fanPageMessages.isFromPage,
        read: fanPageMessages.read,
        createdAt: fanPageMessages.createdAt,
        sender: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPageMessages)
      .leftJoin(users, eq(fanPageMessages.senderId, users.id))
      .where(eq(fanPageMessages.fanPageId, pageId))
      .orderBy(desc(fanPageMessages.createdAt))
      .limit(50);

    console.log(`Found ${messages.length} messages for page ${pageId}:`, messages);

    return NextResponse.json({
      success: true,
      data: messages
    });

  } catch (error) {
    console.error("Error fetching fan page messages:", error);
    return NextResponse.json(
      { error: "Failed to fetch messages" },
      { status: 500 }
    );
  }
}

// POST - Send a message to a fan page
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;
    const body = await request.json();
    const { content, isReply, recipientId } = sendMessageSchema.parse(body);

    // Verify fan page exists and is active
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, pageId),
        eq(fanPages.isActive, true)
      ),
    });

    if (!page) {
      return NextResponse.json({ error: "Fan page not found" }, { status: 404 });
    }

    // Check if this is a reply from page owner
    const isPageOwner = page.ownerId === session.user.id;

    if (isReply && !isPageOwner) {
      return NextResponse.json({ error: "Only page owners can send replies" }, { status: 403 });
    }

    if (isReply && !recipientId) {
      return NextResponse.json({ error: "Recipient ID is required for replies" }, { status: 400 });
    }

    // Create message
    const messageId = uuidv4();
    await db.insert(fanPageMessages).values({
      id: messageId,
      fanPageId: pageId,
      senderId: session.user.id,
      content,
      isFromPage: isReply && isPageOwner, // Mark as from page if it's a reply from page owner
    });

    // Create notification and emit Socket.io events
    if (isReply && isPageOwner && recipientId) {
      // Notify the user that page replied
      await db.insert(notifications).values({
        id: uuidv4(),
        recipientId: recipientId,
        type: "fan_page_reply",
        senderId: session.user.id,
        fanPageId: pageId,
      });

      // Emit real-time fan page message update
      try {
        const messagePayload = createMessagePayload({
          id: messageId,
          senderId: session.user.id,
          content,
          fanPageId: pageId,
          createdAt: new Date().toISOString()
        }, "fanpage");
        await emitMessage(messagePayload);
      } catch (error) {
        console.error("Failed to emit real-time fan page message:", error);
      }
    } else if (!isReply) {
      // Notify page owner of new message
      await db.insert(notifications).values({
        id: uuidv4(),
        recipientId: page.ownerId,
        type: "fan_page_message",
        senderId: session.user.id,
        fanPageId: pageId,
      });
    }

    // Fetch the created message with sender info
    const createdMessage = await db
      .select({
        id: fanPageMessages.id,
        content: fanPageMessages.content,
        isFromPage: fanPageMessages.isFromPage,
        read: fanPageMessages.read,
        createdAt: fanPageMessages.createdAt,
        sender: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPageMessages)
      .leftJoin(users, eq(fanPageMessages.senderId, users.id))
      .where(eq(fanPageMessages.id, messageId))
      .limit(1);

    return NextResponse.json({
      message: "Message sent successfully",
      data: createdMessage[0],
    });

  } catch (error) {
    console.error("Error sending fan page message:", error);
    return NextResponse.json(
      { error: "Failed to send message" },
      { status: 500 }
    );
  }
}

const markAsReadSchema = z.object({
  messageIds: z.array(z.string()).optional(),
  read: z.boolean().default(true),
});

// PUT - Mark messages as read
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;
    const body = await request.json();
    const { messageIds, read } = markAsReadSchema.parse(body);

    // Verify user is the page owner
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, pageId),
        eq(fanPages.ownerId, session.user.id)
      ),
    });

    if (!page) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    if (messageIds && messageIds.length > 0) {
      // Mark specific messages as read (only user messages, not page replies)
      await db
        .update(fanPageMessages)
        .set({ read })
        .where(
          and(
            eq(fanPageMessages.fanPageId, pageId),
            inArray(fanPageMessages.id, messageIds),
            eq(fanPageMessages.isFromPage, false) // Only mark user messages as read
          )
        );
    } else {
      // Mark all unread user messages as read (not page replies)
      await db
        .update(fanPageMessages)
        .set({ read })
        .where(
          and(
            eq(fanPageMessages.fanPageId, pageId),
            eq(fanPageMessages.read, false),
            eq(fanPageMessages.isFromPage, false) // Only mark user messages as read
          )
        );
    }

    return NextResponse.json({
      success: true,
      message: "Messages updated successfully"
    });

  } catch (error) {
    console.error("Error marking messages as read:", error);
    return NextResponse.json(
      { error: "Failed to mark messages as read" },
      { status: 500 }
    );
  }
}
