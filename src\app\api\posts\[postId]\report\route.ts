import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, postReports } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and } from "drizzle-orm";

const reportSchema = z.object({
  reason: z.enum(["spam", "harassment", "inappropriate_content", "violence", "misinformation", "other"]),
  description: z.string().max(1000).optional(),
});

export async function POST(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { postId } = params;
    const body = await req.json();
    const { reason, description } = reportSchema.parse(body);

    // Check if the post exists
    const existingPost = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!existingPost) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Prevent reporting own posts
    if (existingPost.userId === session.user.id) {
      return NextResponse.json(
        { message: "Cannot report your own post" },
        { status: 400 }
      );
    }

    // Check if the user has already reported this post
    const existingReport = await db.query.postReports.findFirst({
      where: and(
        eq(postReports.postId, postId),
        eq(postReports.reporterId, session.user.id),
        eq(postReports.status, "pending")
      ),
    });

    if (existingReport) {
      return NextResponse.json(
        { message: "You have already reported this post" },
        { status: 400 }
      );
    }

    // Create the report
    const reportId = uuidv4();
    await db.insert(postReports).values({
      id: reportId,
      postId: postId,
      reporterId: session.user.id,
      reason,
      description: description || null,
      status: "pending",
    });

    // Mark the post as reported
    await db.update(posts)
      .set({
        isReported: true,
        moderationStatus: "pending",
      })
      .where(eq(posts.id, postId));

    return NextResponse.json({
      message: "Post reported successfully",
      reportId,
    });
  } catch (error) {
    console.error("Error reporting post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
