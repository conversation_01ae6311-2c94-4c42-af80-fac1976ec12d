"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { Spinner } from "@/components/ui/Spinner";
import { PostCard } from "@/components/feed/PostCard";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";
import { UserCard, GroupCard, FanPageCard, EventCard, BlogCard, SearchFilters, SearchFiltersType } from "@/components/search";
import { Post } from "@/types/post";
import {
  UserGroupIcon,
  UsersIcon,
  CalendarIcon,
  NewspaperIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon
} from "@heroicons/react/24/outline";

/*interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  user: {
    id: string;
    name: string;
    image: string | null;
  };
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
}*/

interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  image: string | null;
  bio: string | null;
}

interface Group {
  id: string;
  name: string;
  description: string | null;
  category: string | null;
  visibility: string;
  coverImage: string | null;
  createdAt: string;
}

interface FanPage {
  id: string;
  name: string;
  username: string;
  description: string | null;
  category: string | null;
  profileImage: string | null;
  coverImage: string | null;
  createdAt: string;
}

interface Event {
  id: string;
  name: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  category: string | null;
  coverImage: string | null;
}

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  author: {
    id: string;
    name: string;
    username: string;
    image: string | null;
  };
  category: {
    id: string;
    name: string;
    color: string;
  } | null;
  createdAt: string;
}

interface SearchResults {
  posts: Post[];
  users: User[];
  groups: Group[];
  fanPages: FanPage[];
  events: Event[];
  blogs: Blog[];
  total: number;
}

function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams?.get("q") || "";
  const type = searchParams?.get("type") || "all";

  const [isLoading, setIsLoading] = useState(true);
  const [results, setResults] = useState<SearchResults>({
    posts: [],
    users: [],
    groups: [],
    fanPages: [],
    events: [],
    blogs: [],
    total: 0
  });
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("all");
  const [filters, setFilters] = useState<SearchFiltersType>({});

  useEffect(() => {
    async function fetchSearchResults() {
      if (!query) {
        setResults({
          posts: [],
          users: [],
          groups: [],
          fanPages: [],
          events: [],
          blogs: [],
          total: 0
        });
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Build query parameters
        const params = new URLSearchParams({
          q: query,
          type: activeTab,
          limit: "20"
        });

        // Add filters if they exist
        if (filters.category) params.append("category", filters.category);
        if (filters.location) params.append("location", filters.location);
        if (filters.sortBy) params.append("sortBy", filters.sortBy);
        if (filters.sortOrder) params.append("sortOrder", filters.sortOrder);

        const response = await fetch(`/api/search?${params.toString()}`);

        if (!response.ok) {
          throw new Error("Failed to fetch search results");
        }

        const data = await response.json();

        if (data.success) {
          // Process posts to ensure they have all required fields
          const processedData = {
            ...data.data,
            posts: data.data.posts.map((post: any) => ({
              ...post,
              type: post.type || 'user_post',
              fanPage: post.fanPage || null,
              group: post.group || null,
              user: post.user || null
            }))
          };
          setResults(processedData);
        } else {
          throw new Error(data.message || "Failed to fetch search results");
        }
      } catch (err) {
        console.error("Error searching:", err);
        setError("Failed to perform search. Please try again.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchSearchResults();
  }, [query, activeTab, filters]);

  const handleFiltersChange = (newFilters: SearchFiltersType) => {
    setFilters(newFilters);
  };

  const handleLike = async (postId: string) => {
    try {
      // Call the API to like/unlike the post
      const response = await fetch(`/api/posts/${postId}/like`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to like post");
      }

      // Update the state optimistically
      setResults((prevResults) => ({
        ...prevResults,
        posts: prevResults.posts.map((post) => {
          if (post.id === postId) {
            const newLiked = !post.liked;
            // If post was disliked and now being liked, remove the dislike
            const wasDisliked = post.disliked;
            return {
              ...post,
              liked: newLiked,
              disliked: wasDisliked ? false : post.disliked,
              _count: {
                ...post._count,
                likes: post._count.likes + (newLiked ? 1 : -1),
                dislikes: wasDisliked ? post._count.dislikes - 1 : post._count.dislikes,
              },
            };
          }
          return post;
        })
      }));
    } catch (error) {
      console.error("Error liking post:", error);
    }
  };

  const handleDislike = async (postId: string) => {
    try {
      // Call the API to dislike/undislike the post
      const response = await fetch(`/api/posts/${postId}/dislike`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to dislike post");
      }

      // Update the state optimistically
      setResults((prevResults) => ({
        ...prevResults,
        posts: prevResults.posts.map((post) => {
          if (post.id === postId) {
            const newDisliked = !post.disliked;
            // If post was liked and now being disliked, remove the like
            const wasLiked = post.liked;
            return {
              ...post,
              disliked: newDisliked,
              liked: wasLiked ? false : post.liked,
              _count: {
                ...post._count,
                dislikes: post._count.dislikes + (newDisliked ? 1 : -1),
                likes: wasLiked ? post._count.likes - 1 : post._count.likes,
              },
            };
          }
          return post;
        })
      }));
    } catch (error) {
      console.error("Error disliking post:", error);
    }
  };

  const tabs = [
    { id: "all", name: "All", icon: MagnifyingGlassIcon, count: results.total },
    { id: "posts", name: "Posts", icon: DocumentTextIcon, count: results.posts.length },
    { id: "users", name: "People", icon: UserGroupIcon, count: results.users.length },
    { id: "groups", name: "Groups", icon: UsersIcon, count: results.groups.length },
    { id: "pages", name: "Pages", icon: NewspaperIcon, count: results.fanPages.length },
    { id: "events", name: "Events", icon: CalendarIcon, count: results.events.length },
    { id: "blogs", name: "Blogs", icon: DocumentTextIcon, count: results.blogs.length },
  ];

  const renderResults = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <Spinner />
        </div>
      );
    }

    if (error) {
      return (
        <div className="rounded-xl bg-red-50 p-6 text-center">
          <p className="text-red-700">{error}</p>
        </div>
      );
    }

    if (results.total === 0) {
      return (
        <div className="rounded-xl bg-white p-8 text-center shadow-sm">
          <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-500 text-lg mb-2">
            No results found for "{query}"
          </p>
          <p className="text-gray-400 text-sm">
            Try different keywords or check your spelling
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Posts */}
        {(activeTab === "all" || activeTab === "posts") && results.posts.length > 0 && (
          <div>
            {activeTab === "all" && (
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Posts ({results.posts.length})
              </h3>
            )}
            <div className="space-y-4">
              {results.posts.map((post) => (
                <div key={post.id} className="transform transition-all duration-300 hover:translate-y-[-2px]">
                  <PostCard
                    post={post}
                    onLike={() => handleLike(post.id)}
                    onDislike={() => handleDislike(post.id)}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Users */}
        {(activeTab === "all" || activeTab === "users") && results.users.length > 0 && (
          <div>
            {activeTab === "all" && (
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <UserGroupIcon className="h-5 w-5 mr-2" />
                People ({results.users.length})
              </h3>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {results.users.map((user) => (
                <UserCard key={user.id} user={user} />
              ))}
            </div>
          </div>
        )}

        {/* Groups */}
        {(activeTab === "all" || activeTab === "groups") && results.groups.length > 0 && (
          <div>
            {activeTab === "all" && (
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <UsersIcon className="h-5 w-5 mr-2" />
                Groups ({results.groups.length})
              </h3>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {results.groups.map((group) => (
                <GroupCard key={group.id} group={group} />
              ))}
            </div>
          </div>
        )}

        {/* Fan Pages */}
        {(activeTab === "all" || activeTab === "pages") && results.fanPages.length > 0 && (
          <div>
            {activeTab === "all" && (
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <NewspaperIcon className="h-5 w-5 mr-2" />
                Pages ({results.fanPages.length})
              </h3>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {results.fanPages.map((page) => (
                <FanPageCard key={page.id} page={page} />
              ))}
            </div>
          </div>
        )}

        {/* Events */}
        {(activeTab === "all" || activeTab === "events") && results.events.length > 0 && (
          <div>
            {activeTab === "all" && (
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <CalendarIcon className="h-5 w-5 mr-2" />
                Events ({results.events.length})
              </h3>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {results.events.map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
            </div>
          </div>
        )}

        {/* Blogs */}
        {(activeTab === "all" || activeTab === "blogs") && results.blogs.length > 0 && (
          <div>
            {activeTab === "all" && (
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Blogs ({results.blogs.length})
              </h3>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {results.blogs.map((blog) => (
                <BlogCard key={blog.id} blog={blog} />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-5 lg:grid-cols-4">
          {/* Left sidebar */}
          <LeftSidebar />

          {/* Main content */}
          <div className="lg:col-span-2 space-y-5">
            {/* Header */}
            <div className="rounded-xl bg-white p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 mb-2">
                    Search Results for "{query}"
                  </h1>
                  <p className="text-sm text-gray-500">
                    {results.total} {results.total === 1 ? "result" : "results"} found
                  </p>
                </div>
                <SearchFilters
                  onFiltersChange={handleFiltersChange}
                  initialFilters={filters}
                />
              </div>
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-xl shadow-sm">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`${
                          activeTab === tab.id
                            ? "border-blue-500 text-blue-600"
                            : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                        } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors`}
                      >
                        <Icon className="h-4 w-4 mr-2" />
                        {tab.name}
                        {tab.count > 0 && (
                          <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                            {tab.count}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </nav>
              </div>

              {/* Results */}
              <div className="p-6">
                {renderResults()}
              </div>
            </div>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={<MainLayout><div className="flex justify-center items-center h-screen"><Spinner size="lg" /></div></MainLayout>}>
      <SearchContent />
    </Suspense>
  );
}
