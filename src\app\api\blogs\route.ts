import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogCategories, users, blogLikes, blogComments, blogBookmarks, blogMonetization } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, desc, like, or, and, sql } from "drizzle-orm";
import { MonetizationService } from "@/lib/monetization/monetizationService";

const blogSchema = z.object({
  title: z.string().min(1).max(500),
  content: z.string().min(1),
  excerpt: z.string().max(1000).optional(),
  coverImage: z.string().url().optional().nullable(),
  categoryId: z.string().optional().nullable(),
  tags: z.array(z.string()).optional(),
  status: z.enum(["draft", "published"]).default("draft"),
  readTime: z.number().optional(),
  featured: z.boolean().optional().default(false),
  seoTitle: z.string().max(255).optional(),
  seoDescription: z.string().max(1000).optional(),
  requestMonetization: z.boolean().optional().default(false),
});

// Get all blogs with filtering and pagination
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const search = url.searchParams.get("search") || "";
    const category = url.searchParams.get("category") || "";
    const featured = url.searchParams.get("featured") === "true";
    const status = url.searchParams.get("status") || "published";
    const authorId = url.searchParams.get("authorId") || "";

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (status) {
      conditions.push(eq(blogs.status, status as "draft" | "published" | "archived"));
    }

    if (search) {
      conditions.push(
        or(
          like(blogs.title, `%${search}%`),
          like(blogs.excerpt, `%${search}%`),
          like(blogs.content, `%${search}%`)
        )
      );
    }

    if (category) {
      conditions.push(eq(blogs.categoryId, category));
    }

    if (featured) {
      conditions.push(eq(blogs.featured, true));
    }

    if (authorId) {
      conditions.push(eq(blogs.authorId, authorId));
    }

    // Fetch blogs from database
    const allBlogs = await db.query.blogs.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: [desc(blogs.createdAt)],
      limit: limit,
      offset: offset,
      with: {
        author: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        category: true,
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        bookmarks: true,
        monetization: true,
      },
    });

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const totalCount = totalCountResult[0]?.count || 0;

    // Format blogs for frontend
    const formattedBlogs = allBlogs.map((blog) => {
      const isLiked = session?.user ? blog.likes.some(like => like.userId === session.user.id) : false;
      const isBookmarked = session?.user ? blog.bookmarks.some(bookmark => bookmark.userId === session.user.id) : false;

      // Include monetization data only for the author
      const isAuthor = session?.user && blog.authorId === session.user.id;
      const monetizationData = isAuthor && blog.monetization ? {
        id: blog.monetization.id,
        isEnabled: blog.monetization.isEnabled,
        isApproved: blog.monetization.isApproved,
        status: blog.monetization.isApproved ? 'approved' :
                blog.monetization.rejectedAt ? 'rejected' : 'pending',
        cprRate: blog.monetization.cprRate,
        totalEarnings: blog.monetization.totalEarnings,
        rejectionReason: blog.monetization.rejectionReason,
      } : null;

      return {
        id: blog.id,
        title: blog.title,
        slug: blog.slug,
        excerpt: blog.excerpt,
        coverImage: blog.coverImage,
        readTime: blog.readTime,
        viewCount: blog.viewCount,
        publishedAt: blog.publishedAt?.toISOString(),
        featured: blog.featured,
        author: blog.author,
        category: blog.category,
        tags: blog.tags,
        _count: {
          likes: blog.likes.length,
          comments: blog.comments.length,
        },
        isLiked,
        isBookmarked,
        monetization: monetizationData,
      };
    });

    return NextResponse.json({
      blogs: formattedBlogs,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new blog
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = blogSchema.parse(body);

    // Generate slug from title
    const slug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");

    // Check if slug already exists
    const existingBlog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
    });

    let finalSlug = slug;
    if (existingBlog) {
      // Add timestamp to make it unique
      finalSlug = `${slug}-${Date.now()}`;
    }

    const blogId = uuidv4();

    // Calculate read time (rough estimate: 200 words per minute)
    const wordCount = validatedData.content.split(/\s+/).length;
    const readTime = Math.ceil(wordCount / 200);

    // Insert blog into database
    await db.insert(blogs).values({
      id: blogId,
      title: validatedData.title,
      slug: finalSlug,
      content: validatedData.content,
      excerpt: validatedData.excerpt || null,
      coverImage: validatedData.coverImage || null,
      authorId: session.user.id,
      categoryId: validatedData.categoryId || null,
      tags: validatedData.tags || null,
      status: validatedData.status,
      readTime: validatedData.readTime || readTime,
      featured: validatedData.featured || false,
      seoTitle: validatedData.seoTitle || null,
      seoDescription: validatedData.seoDescription || null,
      publishedAt: validatedData.status === "published" ? new Date() : null,
    });

    // Handle monetization request if requested and blog is published
    if (validatedData.requestMonetization && validatedData.status === "published") {
      try {
        // Check if monetization is globally enabled
        const isGloballyEnabled = await MonetizationService.isMonetizationEnabled();
        if (isGloballyEnabled) {
          await MonetizationService.requestMonetization({
            blogId,
            authorId: session.user.id
          });
        } else {
          console.warn("Monetization request ignored - globally disabled");
        }
      } catch (monetizationError) {
        console.error("Error requesting monetization:", monetizationError);
        // Don't fail the blog creation if monetization request fails
      }
    }

    return NextResponse.json(
      {
        message: validatedData.requestMonetization && validatedData.status === "published"
          ? "Blog created successfully and monetization request submitted"
          : "Blog created successfully",
        id: blogId,
        slug: finalSlug,
        monetizationRequested: validatedData.requestMonetization && validatedData.status === "published"
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating blog:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
