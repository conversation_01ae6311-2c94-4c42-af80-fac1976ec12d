import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "./ui-variables.css";
import { SessionProvider } from "@/components/providers/SessionProvider";
import { UiSettingsProvider } from "@/components/ui/UiSettingsProvider";
import { HydrationProvider } from "@/components/providers/HydrationProvider";
import { ErrorBoundary } from "@/components/error/ErrorBoundary";
import { ToastProvider } from "@/contexts/ToastContext";

// Use Inter as a fallback font since <PERSON><PERSON><PERSON> is having issues
const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "HIFNF - Connect with friends",
  description: "A social media platform to connect with friends and share your life",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <ErrorBoundary>
          <SessionProvider>
            <UiSettingsProvider>
              <ToastProvider>
                <HydrationProvider>{children}</HydrationProvider>
              </ToastProvider>
            </UiSettingsProvider>
          </SessionProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
