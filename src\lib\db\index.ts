import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import * as schema from './schema';
import { dbConfig } from '../config';

// Create a MySQL connection pool with optimized configuration
const pool = mysql.createPool({
  host: dbConfig.host,
  user: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  port: dbConfig.port,
  // Connection pool settings - Optimized for high performance (only valid MySQL2 options)
  waitForConnections: dbConfig.pool.waitForConnections,
  connectionLimit: dbConfig.pool.connectionLimit,
  maxIdle: dbConfig.pool.maxIdle,
  idleTimeout: dbConfig.pool.idleTimeout,
  queueLimit: dbConfig.pool.queueLimit,
  enableKeepAlive: dbConfig.pool.enableKeepAlive,
  keepAliveInitialDelay: dbConfig.pool.keepAliveInitialDelay,
});

// Create a Drizzle ORM instance
export const db = drizzle(pool, { schema, mode: 'default' });
