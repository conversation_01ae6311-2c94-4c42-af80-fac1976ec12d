"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { GiftIcon, UserIcon, CakeIcon } from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { useTabVisibilityCache } from "@/hooks/useTabVisibilityCache";

interface BirthdayFriend {
  id: string;
  name: string;
  username: string;
  image: string | null;
  birthday: string;
  birthdayMonth: number;
  birthdayDay: number;
  isToday: boolean;
  daysUntil: number;
  age: number | null;
}

interface BirthdaysResponse {
  birthdays: BirthdayFriend[];
  type: string;
  count: number;
  today: {
    month: number;
    day: number;
  };
}

export function Birthdays() {
  const { data: session } = useSession();
  const [birthdays, setBirthdays] = useState<BirthdayFriend[]>([]);
  const [loading, setLoading] = useState(true);

  // API fetch function with better error handling
  const fetchBirthdaysFromAPI = useCallback(async () => {
    try {
      const response = await fetch('/api/users/birthdays?type=all&limit=5');
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Birthday API error:', response.status, errorText);

        // Handle specific error cases
        if (response.status === 401) {
          throw new Error('Authentication required');
        } else if (response.status === 500) {
          throw new Error('Server error - please try again later');
        } else {
          throw new Error(`Failed to fetch birthdays: ${response.status}`);
        }
      }
      const data: BirthdaysResponse = await response.json();
      return data.birthdays || [];
    } catch (error) {
      console.error('Error in fetchBirthdaysFromAPI:', error);
      throw error;
    }
  }, []);

  // Use tab visibility cache for birthdays data
  const { fetchWithCache } = useTabVisibilityCache({
    cacheKey: `birthdays-${session?.user?.id || 'anonymous'}`,
    fetchFunction: fetchBirthdaysFromAPI,
    cacheDuration: 10 * 60 * 1000, // 10 minutes
    dependencies: [session?.user?.id],
    enabled: !!session?.user
  });

  const fetchBirthdays = useCallback(async () => {
    try {
      setLoading(true);
      const data = await fetchWithCache();
      setBirthdays(data || []);
    } catch (error) {
      console.error('Error fetching birthdays:', error);
      // Set empty array on error to hide the section
      setBirthdays([]);
    } finally {
      setLoading(false);
    }
  }, [fetchWithCache]);

  useEffect(() => {
    if (session?.user) {
      fetchBirthdays();
    }
  }, [session?.user, fetchBirthdays]);

  const formatBirthdayMessage = (friend: BirthdayFriend) => {
    if (friend.isToday) {
      return `${friend.name}'s birthday is today!`;
    } else if (friend.daysUntil === 1) {
      return `${friend.name}'s birthday is tomorrow`;
    } else if (friend.daysUntil <= 7) {
      return `${friend.name}'s birthday is in ${friend.daysUntil} days`;
    } else {
      return `${friend.name}'s birthday is on ${getMonthName(friend.birthdayMonth)} ${friend.birthdayDay}`;
    }
  };

  const getMonthName = (month: number) => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  };

  if (loading) {
    return (
      <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">
            Birthdays
          </h2>
        </div>
        <div className="space-y-3">
          {[1, 2].map((i) => (
            <div key={i} className="flex items-center p-1 animate-pulse">
              <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
              <div className="ml-3 flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Hide section completely if no birthdays available (including error and empty states)
  if (birthdays.length === 0) {
    return null;
  }

  return (
    <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base font-semibold text-gray-900">
          Birthdays
        </h2>
        {birthdays.length > 3 && (
          <Link href="/friends/birthdays" className="text-sm text-blue-500 hover:text-blue-600 hover:underline transition-colors duration-200">
            See All
          </Link>
        )}
      </div>
      <div className="space-y-3">
        {birthdays.slice(0, 3).map((friend) => (
          <div key={friend.id} className="flex items-center p-1 hover:bg-gray-50 rounded-lg transition-colors duration-200">
            <div className="relative h-10 w-10 flex-shrink-0">
              {friend.image ? (
                <OptimizedImage
                  src={friend.image}
                  alt={friend.name}
                  width={40}
                  height={40}
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="h-10 w-10 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-gray-500" />
                </div>
              )}

              {/* Birthday indicator */}
              <div className={`absolute -bottom-1 -right-1 h-5 w-5 rounded-full flex items-center justify-center shadow-sm ${
                friend.isToday
                  ? 'bg-red-500 text-white'
                  : 'bg-blue-500 text-white'
              }`}>
                {friend.isToday ? (
                  <CakeIcon className="h-3 w-3" />
                ) : (
                  <GiftIcon className="h-3 w-3" />
                )}
              </div>
            </div>

            <div className="ml-3 flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <Link
                  href={`/user/${friend.username}`}
                  className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200 truncate"
                >
                  {friend.name}
                </Link>
                {friend.isToday && (
                  <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full font-medium">
                    Today!
                  </span>
                )}
              </div>
              <p className="text-xs text-gray-500 truncate">
                {formatBirthdayMessage(friend)}
              </p>
              {friend.age && friend.isToday && (
                <p className="text-xs text-blue-600 font-medium">
                  Turning {friend.age} years old
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Quick action for today's birthdays */}
      {birthdays.some(f => f.isToday) && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <Link
            href="/friends/birthdays?filter=today"
            className="flex items-center justify-center w-full px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200"
          >
            <CakeIcon className="h-4 w-4 mr-2" />
            Send Birthday Wishes
          </Link>
        </div>
      )}
    </div>
  );
}
