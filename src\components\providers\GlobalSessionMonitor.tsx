"use client";

import { useEffect, useCallback, useRef } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";

interface SessionMonitorProps {
  children: React.ReactNode;
}

export function GlobalSessionMonitor({ children }: SessionMonitorProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isCheckingRef = useRef(false);

  const showAccountDisabledModal = useCallback((reason: string, status: string) => {
    // Create and show a modal popup
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50';
    modal.innerHTML = `
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6 animate-pulse">
        <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <div class="text-center">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Account ${status === 'suspended' ? 'Suspended' : 'Disabled'}</h3>
          <p class="text-gray-600 mb-4">${reason || `Your account has been ${status}. Please contact support for assistance.`}</p>
          <p class="text-sm text-gray-500 mb-6">You will be automatically logged out in a few seconds.</p>
          <div class="flex justify-center">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
      if (document.body.contains(modal)) {
        document.body.removeChild(modal);
      }
    }, 3000);
    
    return modal;
  }, []);

  const checkSessionStatus = useCallback(async () => {
    // Prevent multiple simultaneous checks
    if (isCheckingRef.current || !session?.user?.id || status !== "authenticated") {
      return;
    }

    isCheckingRef.current = true;

    try {
      const response = await fetch('/api/auth/session-status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store', // Ensure fresh data
      });

      if (!response.ok) {
        if (response.status === 401) {
          const data = await response.json();
          
          if (data.code === 'ACCOUNT_DISABLED') {
            console.log('🚨 Account disabled detected:', data);
            
            // Clear the interval immediately
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            
            // Show modal popup
            const modal = showAccountDisabledModal(
              data.message || "Your account has been disabled or suspended", 
              data.status || 'disabled'
            );
            
            // Show toast notification
            toast.error(data.message || "Your account has been disabled or suspended", {
              duration: 5000,
              style: {
                background: '#FEF2F2',
                border: '1px solid #EF4444',
                color: '#991B1B',
              },
            });
            
            // Wait 3 seconds then sign out and redirect
            setTimeout(async () => {
              try {
                await signOut({ redirect: false });
                
                // Remove modal if still exists
                if (document.body.contains(modal)) {
                  document.body.removeChild(modal);
                }
                
                // Redirect based on user type
                if (session.user.isAdmin) {
                  router.push('/admin/login?error=account_disabled');
                } else {
                  router.push('/login?error=account_disabled');
                }
              } catch (error) {
                console.error('Error during logout:', error);
                // Force reload as fallback
                window.location.href = '/login?error=account_disabled';
              }
            }, 3000);
            
            return false;
          }
        }
      } else {
        // Session is valid, continue monitoring
        const data = await response.json();
        console.log('✅ Session check passed:', data.user);
      }
      
      return true;
    } catch (error) {
      console.error('Session validation error:', error);
      // Don't invalidate on network errors, but log them
      return true;
    } finally {
      isCheckingRef.current = false;
    }
  }, [session?.user?.id, session?.user?.isAdmin, status, router, showAccountDisabledModal]);

  useEffect(() => {
    if (status === "authenticated" && session?.user?.id) {
      console.log('🔍 Starting global session monitoring for user:', session.user.id);
      
      // Initial check after 2 seconds
      const initialTimeout = setTimeout(() => {
        checkSessionStatus();
      }, 2000);
      
      // Set up interval checking every 15 seconds for real-time monitoring
      intervalRef.current = setInterval(checkSessionStatus, 15000);
      
      return () => {
        clearTimeout(initialTimeout);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    } else {
      // Clear interval if user is not authenticated
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  }, [status, session?.user?.id, checkSessionStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  return <>{children}</>;
}
