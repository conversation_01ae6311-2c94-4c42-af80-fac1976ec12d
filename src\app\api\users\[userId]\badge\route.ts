import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { userSubscriptions, subscriptionPlans } from "@/lib/db/schema";
import { eq, and, desc } from "drizzle-orm";

type RouteParams = {
  params: Promise<{ userId: string }>;
};

// GET /api/users/[userId]/badge - Get user's highest priority badge
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { success: false, message: "User ID is required" },
        { status: 400 }
      );
    }

    // Get user's active subscriptions with plan details, ordered by badge priority
    const activeSubscriptions = await db
      .select({
        subscription: userSubscriptions,
        plan: {
          id: subscriptionPlans.id,
          displayName: subscriptionPlans.displayName,
          badgeType: subscriptionPlans.badgeType,
          badgeColor: subscriptionPlans.badgeColor,
          customBadgeUrl: subscriptionPlans.customBadgeUrl,
          badgePriority: subscriptionPlans.badgePriority,
        },
      })
      .from(userSubscriptions)
      .innerJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id))
      .where(
        and(
          eq(userSubscriptions.userId, userId),
          eq(userSubscriptions.status, 'active'),
          eq(subscriptionPlans.isActive, true)
        )
      )
      .orderBy(
        desc(subscriptionPlans.badgePriority), // Highest priority first
        desc(userSubscriptions.startDate) // Most recent if same priority
      );

    if (activeSubscriptions.length === 0) {
      return NextResponse.json(
        { success: false, message: "No active subscription found" },
        { status: 404 }
      );
    }

    // Get the highest priority subscription
    const topSubscription = activeSubscriptions[0];
    const plan = topSubscription.plan;

    // If badge type is 'none', return no badge
    if (plan.badgeType === 'none') {
      return NextResponse.json(
        { success: false, message: "No badge configured for this plan" },
        { status: 404 }
      );
    }

    const badgeData = {
      badgeType: plan.badgeType,
      badgeColor: plan.badgeColor,
      customBadgeUrl: plan.customBadgeUrl,
      planName: plan.displayName,
      badgePriority: plan.badgePriority,
    };

    return NextResponse.json({
      success: true,
      badge: badgeData,
    });
  } catch (error) {
    console.error("Error fetching user badge:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Internal server error" 
      },
      { status: 500 }
    );
  }
}
