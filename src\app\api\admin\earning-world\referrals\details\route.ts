import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referrals, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Get detailed referral information for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all referrals with user information
    const referralDetailsResult = await db
      .select({
        id: referrals.id,
        referrerId: referrals.referrerId,
        referredUserId: referrals.referredUserId,
        referralCode: referrals.referralCode,
        status: referrals.status,
        rewardAmount: referrals.rewardAmount,
        paidAt: referrals.paidAt,
        completedAt: referrals.completedAt,
        metadata: referrals.metadata,
        createdAt: referrals.createdAt,
        updatedAt: referrals.updatedAt,
        referrerName: users.name,
        referrerEmail: users.email,
        referrerUsername: users.username,
      })
      .from(referrals)
      .innerJoin(users, eq(referrals.referrerId, users.id))
      .orderBy(referrals.createdAt);

    // Get referred user information separately
    const referredUsersResult = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
        username: users.username,
      })
      .from(users);

    // Create a map for referred users
    const referredUsersMap = new Map(
      referredUsersResult.map(user => [user.id, user])
    );

    // Combine data
    const referralDetails = referralDetailsResult.map(referral => {
      const referredUser = referredUsersMap.get(referral.referredUserId);
      
      return {
        id: referral.id,
        referrer: {
          id: referral.referrerId,
          name: referral.referrerName || 'Unknown User',
          email: referral.referrerEmail || '',
          username: referral.referrerUsername || '',
        },
        referred: {
          id: referral.referredUserId,
          name: referredUser?.name || 'Unknown User',
          email: referredUser?.email || '',
          username: referredUser?.username || '',
        },
        referralCode: referral.referralCode,
        status: referral.status,
        rewardAmount: parseFloat(referral.rewardAmount),
        createdAt: referral.createdAt.toISOString(),
        completedAt: referral.completedAt?.toISOString(),
        paidAt: referral.paidAt?.toISOString(),
        metadata: referral.metadata,
      };
    });

    return NextResponse.json({
      success: true,
      data: referralDetails,
    });

  } catch (error) {
    console.error("Error fetching referral details:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch referral details"
      },
      { status: 500 }
    );
  }
}
