import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get Node.js memory usage
    const memoryUsage = process.memoryUsage();
    
    // Convert bytes to MB
    const formatMemory = (bytes: number) => ({
      bytes,
      mb: Math.round(bytes / 1024 / 1024 * 100) / 100,
      percentage: Math.round((bytes / (1024 * 1024 * 1024)) * 100 * 100) / 100 // Assuming 1GB as reference
    });

    // Get system information
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
      pid: process.pid,
    };

    // Memory usage details
    const memory = {
      rss: formatMemory(memoryUsage.rss), // Resident Set Size
      heapTotal: formatMemory(memoryUsage.heapTotal), // Total heap allocated
      heapUsed: formatMemory(memoryUsage.heapUsed), // Heap actually used
      external: formatMemory(memoryUsage.external), // External memory usage
      arrayBuffers: formatMemory(memoryUsage.arrayBuffers || 0), // ArrayBuffer memory
    };

    // Calculate memory efficiency metrics
    const heapUtilization = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    const totalMemoryUsage = memoryUsage.rss;
    
    // Memory health assessment
    const getMemoryHealth = () => {
      const heapUsedMB = memory.heapUsed.mb;
      const heapUtilizationPercent = heapUtilization;
      
      if (heapUsedMB > 512 || heapUtilizationPercent > 85) {
        return {
          status: 'critical',
          message: 'High memory usage detected',
          recommendations: [
            'Consider restarting the application',
            'Check for memory leaks',
            'Optimize heavy operations',
            'Implement memory cleanup'
          ]
        };
      } else if (heapUsedMB > 256 || heapUtilizationPercent > 70) {
        return {
          status: 'warning',
          message: 'Elevated memory usage',
          recommendations: [
            'Monitor memory usage closely',
            'Consider optimizing data structures',
            'Implement garbage collection hints'
          ]
        };
      } else {
        return {
          status: 'healthy',
          message: 'Memory usage is within normal limits',
          recommendations: []
        };
      }
    };

    const health = getMemoryHealth();

    // Performance metrics
    const performance = {
      heapUtilization: Math.round(heapUtilization * 100) / 100,
      memoryEfficiency: Math.round((1 - (memoryUsage.heapUsed / memoryUsage.rss)) * 100 * 100) / 100,
      uptimeFormatted: formatUptime(systemInfo.uptime),
    };

    // Memory optimization suggestions
    const optimizationSuggestions = [];
    
    if (heapUtilization > 70) {
      optimizationSuggestions.push('Consider implementing memory pooling');
      optimizationSuggestions.push('Review and optimize large object allocations');
    }
    
    if (memory.external.mb > 100) {
      optimizationSuggestions.push('Check external memory usage (buffers, etc.)');
    }
    
    if (memory.arrayBuffers.mb > 50) {
      optimizationSuggestions.push('Review ArrayBuffer usage and cleanup');
    }

    const response = {
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        system: systemInfo,
        memory,
        performance,
        health,
        optimizationSuggestions,
        limits: {
          recommended: {
            heapUsed: '256 MB',
            heapUtilization: '70%',
            rss: '512 MB'
          },
          critical: {
            heapUsed: '512 MB',
            heapUtilization: '85%',
            rss: '1 GB'
          }
        }
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error("Error fetching memory usage:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch memory usage",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// Helper function to format uptime
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

// Force garbage collection endpoint (development only)
export async function POST() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { message: "Garbage collection is only available in development mode" },
        { status: 403 }
      );
    }

    const beforeMemory = process.memoryUsage();
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    } else {
      return NextResponse.json(
        { 
          success: false,
          message: "Garbage collection not available. Start Node.js with --expose-gc flag." 
        },
        { status: 400 }
      );
    }

    const afterMemory = process.memoryUsage();
    
    const memoryFreed = {
      heapUsed: beforeMemory.heapUsed - afterMemory.heapUsed,
      rss: beforeMemory.rss - afterMemory.rss,
    };

    return NextResponse.json({
      success: true,
      message: "Garbage collection completed",
      data: {
        before: beforeMemory,
        after: afterMemory,
        freed: {
          heapUsed: `${Math.round(memoryFreed.heapUsed / 1024 / 1024 * 100) / 100} MB`,
          rss: `${Math.round(memoryFreed.rss / 1024 / 1024 * 100) / 100} MB`,
        }
      }
    });

  } catch (error) {
    console.error("Error during garbage collection:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to perform garbage collection",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
