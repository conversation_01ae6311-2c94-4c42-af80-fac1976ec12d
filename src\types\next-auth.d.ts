import NextAuth from "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      isAdmin?: boolean;
      role?: string;
      adminRoleId?: string | null;
      adminRoleName?: string | null;
      status?: string;
      isActive?: boolean;
    };
  }

  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    isAdmin?: boolean;
    role?: string;
    adminRoleId?: string | null;
    status?: string;
    isActive?: boolean;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    isAdmin?: boolean;
    role?: string;
    adminRoleId?: string | null;
    adminRoleName?: string | null;
    status?: string;
    isActive?: boolean;
  }
}
