import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePostComments, fanPagePostCommentLikes } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    commentId: string;
  }>;
}

// POST /api/fan-pages/posts/comments/[commentId]/dislike - Dislike/Undislike a fan page comment
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { commentId } = await params;

    // Check if comment exists
    const comment = await db
      .select()
      .from(fanPagePostComments)
      .where(eq(fanPagePostComments.id, commentId))
      .limit(1);

    if (comment.length === 0) {
      return NextResponse.json(
        { error: "Comment not found" },
        { status: 404 }
      );
    }

    // Check if user has already disliked this comment
    // Note: Fan page comments use a separate likes table with different structure
    // We'll need to add a dislike field or use a different approach
    // For now, let's use the existing like system and track dislikes separately

    // Check existing like/dislike status
    const existingLike = await db
      .select()
      .from(fanPagePostCommentLikes)
      .where(
        and(
          eq(fanPagePostCommentLikes.commentId, commentId),
          eq(fanPagePostCommentLikes.userId, session.user.id)
        )
      )
      .limit(1);

    // For fan page comments, we need to implement a different approach
    // since the current schema doesn't support dislikes
    // We'll return a message indicating this feature needs schema update
    
    return NextResponse.json(
      { 
        message: "Dislike feature for fan page comments requires schema update",
        error: "Feature not implemented for fan page comments"
      },
      { status: 501 }
    );

  } catch (error) {
    console.error("Error disliking fan page comment:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
