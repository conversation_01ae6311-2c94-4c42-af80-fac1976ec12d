import { db } from "@/lib/db";
import {
  blogMonetization,
  blogReads,
  blogEarnings,
  monetizationSettings,
  blogs,
  wallets,
  walletTransactions
} from "@/lib/db/schema";
import { eq, and, sql, gte, lte, desc } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { WalletService } from "@/lib/wallet/walletService";

export interface MonetizationConfig {
  cprRate: number; // Rate per 1000 reads
  minPayoutThreshold: number;
  minReadDuration: number; // in seconds
  isEnabled: boolean;
}

export interface ReadTrackingData {
  blogId: string;
  userId?: string;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
  referrer?: string;
  readDuration: number;
}

export interface MonetizationRequest {
  blogId: string;
  authorId: string;
  reason?: string;
}

export interface MonetizationApproval {
  monetizationId: string;
  adminId: string;
  approved: boolean;
  reason?: string;
}

export class MonetizationService {
  // Get global monetization settings
  static async getMonetizationConfig(): Promise<MonetizationConfig> {
    const settings = await db.query.monetizationSettings.findMany({
      where: eq(monetizationSettings.isActive, true),
    });

    const config: MonetizationConfig = {
      cprRate: 1.0, // Default $1 per 1000 reads
      minPayoutThreshold: 10.0, // Default $10 minimum payout
      minReadDuration: 120, // Default 2 minutes
      isEnabled: true,
    };

    settings.forEach(setting => {
      switch (setting.settingKey) {
        case 'cpr_rate':
          config.cprRate = parseFloat(setting.settingValue);
          break;
        case 'min_payout_threshold':
          config.minPayoutThreshold = parseFloat(setting.settingValue);
          break;
        case 'min_read_duration':
          config.minReadDuration = parseInt(setting.settingValue);
          break;
        case 'monetization_enabled':
          config.isEnabled = setting.settingValue === 'true';
          break;
      }
    });

    return config;
  }

  // Check if monetization is globally enabled
  static async isMonetizationEnabled(): Promise<boolean> {
    const config = await this.getMonetizationConfig();
    return config.isEnabled;
  }

  // Update monetization settings
  static async updateMonetizationConfig(config: Partial<MonetizationConfig>): Promise<void> {
    const updates = [];

    if (config.cprRate !== undefined) {
      updates.push({
        settingKey: 'cpr_rate',
        settingValue: config.cprRate.toString(),
        dataType: 'number' as const,
        description: 'Cost per 1000 reads in USD',
      });
    }

    if (config.minPayoutThreshold !== undefined) {
      updates.push({
        settingKey: 'min_payout_threshold',
        settingValue: config.minPayoutThreshold.toString(),
        dataType: 'number' as const,
        description: 'Minimum payout threshold in USD',
      });
    }

    if (config.minReadDuration !== undefined) {
      updates.push({
        settingKey: 'min_read_duration',
        settingValue: config.minReadDuration.toString(),
        dataType: 'number' as const,
        description: 'Minimum read duration in seconds',
      });
    }

    if (config.isEnabled !== undefined) {
      updates.push({
        settingKey: 'monetization_enabled',
        settingValue: config.isEnabled.toString(),
        dataType: 'boolean' as const,
        description: 'Enable/disable blog monetization system',
      });
    }

    for (const update of updates) {
      const existing = await db.query.monetizationSettings.findFirst({
        where: eq(monetizationSettings.settingKey, update.settingKey),
      });

      if (existing) {
        await db
          .update(monetizationSettings)
          .set({
            settingValue: update.settingValue,
            updatedAt: new Date(),
          })
          .where(eq(monetizationSettings.id, existing.id));
      } else {
        await db.insert(monetizationSettings).values({
          id: uuidv4(),
          ...update,
        });
      }
    }
  }

  // Request monetization for a blog
  static async requestMonetization(data: MonetizationRequest): Promise<string> {
    const config = await this.getMonetizationConfig();

    if (!config.isEnabled) {
      throw new Error('Monetization is currently disabled');
    }

    // Check if blog exists and belongs to author
    const blog = await db.query.blogs.findFirst({
      where: and(
        eq(blogs.id, data.blogId),
        eq(blogs.authorId, data.authorId)
      ),
    });

    if (!blog) {
      throw new Error('Blog not found or unauthorized');
    }

    // Check if monetization already exists
    const existing = await db.query.blogMonetization.findFirst({
      where: eq(blogMonetization.blogId, data.blogId),
    });

    if (existing) {
      if (existing.status === 'pending') {
        throw new Error('Monetization request already pending');
      }
      if (existing.status === 'approved') {
        throw new Error('Blog is already monetized');
      }
      // If rejected or suspended, allow new request
    }

    const monetizationId = uuidv4();

    if (existing) {
      // Update existing record
      await db
        .update(blogMonetization)
        .set({
          status: 'pending',
          isEnabled: true,
          isApproved: false,
          rejectedAt: null,
          rejectedBy: null,
          rejectionReason: null,
          cprRate: config.cprRate.toString(),
          updatedAt: new Date(),
        })
        .where(eq(blogMonetization.id, existing.id));

      return existing.id;
    } else {
      // Create new record
      await db.insert(blogMonetization).values({
        id: monetizationId,
        blogId: data.blogId,
        isEnabled: true,
        isApproved: false,
        status: 'pending',
        cprRate: config.cprRate.toString(),
      });

      return monetizationId;
    }
  }

  // Approve/Reject monetization request
  static async processMonetizationRequest(data: MonetizationApproval): Promise<void> {
    const monetization = await db.query.blogMonetization.findFirst({
      where: eq(blogMonetization.id, data.monetizationId),
    });

    if (!monetization) {
      throw new Error('Monetization request not found');
    }

    if (monetization.status !== 'pending') {
      throw new Error('Monetization request is not pending');
    }

    if (data.approved) {
      await db
        .update(blogMonetization)
        .set({
          status: 'approved',
          isApproved: true,
          approvedAt: new Date(),
          approvedBy: data.adminId,
          rejectedAt: null,
          rejectedBy: null,
          rejectionReason: null,
          updatedAt: new Date(),
        })
        .where(eq(blogMonetization.id, data.monetizationId));
    } else {
      await db
        .update(blogMonetization)
        .set({
          status: 'rejected',
          isApproved: false,
          rejectedAt: new Date(),
          rejectedBy: data.adminId,
          rejectionReason: data.reason || 'No reason provided',
          approvedAt: null,
          approvedBy: null,
          updatedAt: new Date(),
        })
        .where(eq(blogMonetization.id, data.monetizationId));
    }
  }

  // Track blog read
  static async trackRead(data: ReadTrackingData): Promise<void> {
    const config = await this.getMonetizationConfig();

    if (!config.isEnabled) {
      return; // Don't track if monetization is disabled
    }

    // Check if blog is monetized and approved
    const monetization = await db.query.blogMonetization.findFirst({
      where: and(
        eq(blogMonetization.blogId, data.blogId),
        eq(blogMonetization.status, 'approved'),
        eq(blogMonetization.isEnabled, true)
      ),
    });

    if (!monetization) {
      return; // Don't track if blog is not monetized
    }

    // Check for duplicate reads (same user/IP within 24 hours)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    let isUnique = true;

    if (data.userId) {
      const existingRead = await db.query.blogReads.findFirst({
        where: and(
          eq(blogReads.blogId, data.blogId),
          eq(blogReads.userId, data.userId),
          gte(blogReads.createdAt, yesterday)
        ),
      });
      isUnique = !existingRead;
    } else {
      const existingRead = await db.query.blogReads.findFirst({
        where: and(
          eq(blogReads.blogId, data.blogId),
          eq(blogReads.ipAddress, data.ipAddress),
          gte(blogReads.createdAt, yesterday)
        ),
      });
      isUnique = !existingRead;
    }

    const isQualified = data.readDuration >= config.minReadDuration;
    const readId = uuidv4();

    // Insert read record
    await db.insert(blogReads).values({
      id: readId,
      blogId: data.blogId,
      userId: data.userId || null,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      readDuration: data.readDuration,
      isUnique,
      isQualified,
      sessionId: data.sessionId,
      referrer: data.referrer || null,
    });

    // Update monetization stats
    if (isUnique && isQualified) {
      await db
        .update(blogMonetization)
        .set({
          totalReads: sql`${blogMonetization.totalReads} + 1`,
          uniqueReads: sql`${blogMonetization.uniqueReads} + 1`,
          updatedAt: new Date(),
        })
        .where(eq(blogMonetization.id, monetization.id));

      // Check if we need to process earnings (every 1000 reads)
      const newUniqueReads = monetization.uniqueReads + 1;
      if (newUniqueReads % 1000 === 0) {
        await this.processEarnings(data.blogId, monetization.id);
      }
    } else if (isUnique) {
      await db
        .update(blogMonetization)
        .set({
          totalReads: sql`${blogMonetization.totalReads} + 1`,
          updatedAt: new Date(),
        })
        .where(eq(blogMonetization.id, monetization.id));
    }
  }

  // Process earnings for 1000 qualified reads
  static async processEarnings(blogId: string, monetizationId: string): Promise<void> {
    const monetization = await db.query.blogMonetization.findFirst({
      where: eq(blogMonetization.id, monetizationId),
      with: {
        blog: true,
      },
    });

    if (!monetization || !monetization.blog) {
      return;
    }

    const config = await this.getMonetizationConfig();
    const earningAmount = parseFloat(monetization.cprRate);
    const earningId = uuidv4();

    // Create earning record
    await db.insert(blogEarnings).values({
      id: earningId,
      blogId,
      authorId: monetization.blog.authorId,
      readCount: 1000,
      qualifiedReads: 1000,
      cprRate: monetization.cprRate,
      earningAmount: earningAmount.toFixed(2),
      status: 'pending',
      periodStart: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
      periodEnd: new Date(),
    });

    // Add to author's earning wallet
    await WalletService.updateWalletBalance(
      monetization.blog.authorId,
      'earning',
      earningAmount.toFixed(2),
      'add'
    );

    // Create wallet transaction
    await WalletService.createTransaction(monetization.blog.authorId, {
      type: 'earning',
      amount: earningAmount.toFixed(2),
      walletType: 'earning',
      reference: `Blog CPR earning for ${monetization.blog.title}`,
      note: `Earned $${earningAmount} for 1000 qualified reads`,
      metadata: {
        blogId,
        earningId,
        readCount: 1000,
        cprRate: monetization.cprRate,
      },
    });

    // Update monetization total earnings
    await db
      .update(blogMonetization)
      .set({
        totalEarnings: sql`${blogMonetization.totalEarnings} + ${earningAmount}`,
        updatedAt: new Date(),
      })
      .where(eq(blogMonetization.id, monetizationId));

    // Mark earning as paid
    await db
      .update(blogEarnings)
      .set({
        status: 'paid',
        paidAt: new Date(),
      })
      .where(eq(blogEarnings.id, earningId));
  }

  // Get monetization requests for admin
  static async getMonetizationRequests(status?: string, limit = 50, offset = 0) {
    const whereCondition = status
      ? eq(blogMonetization.status, status as any)
      : undefined;

    return await db.query.blogMonetization.findMany({
      where: whereCondition,
      with: {
        blog: {
          columns: {
            id: true,
            title: true,
            slug: true,
            authorId: true,
            viewCount: true,
            publishedAt: true,
          },
          with: {
            author: {
              columns: {
                id: true,
                name: true,
                email: true,
                username: true,
                image: true,
              },
            },
          },
        },
        approver: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
        rejector: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: [desc(blogMonetization.createdAt)],
      limit,
      offset,
    });
  }

  // Get author's monetization stats
  static async getAuthorStats(authorId: string) {
    const monetizedBlogs = await db.query.blogMonetization.findMany({
      where: and(
        eq(blogMonetization.status, 'approved'),
        eq(blogMonetization.isEnabled, true)
      ),
      with: {
        blog: {
          where: eq(blogs.authorId, authorId),
          columns: {
            id: true,
            title: true,
            slug: true,
            viewCount: true,
          },
        },
      },
    });

    const totalEarnings = monetizedBlogs.reduce(
      (sum, m) => sum + parseFloat(m.totalEarnings),
      0
    );

    const totalReads = monetizedBlogs.reduce(
      (sum, m) => sum + (m.uniqueReads || 0),
      0
    );

    return {
      monetizedBlogs: monetizedBlogs.filter(m => m.blog),
      totalEarnings,
      totalReads,
      averageCPR: monetizedBlogs.length > 0
        ? monetizedBlogs.reduce((sum, m) => sum + parseFloat(m.cprRate), 0) / monetizedBlogs.length
        : 0,
    };
  }

  // Get blog monetization status
  static async getBlogMonetizationStatus(blogId: string) {
    return await db.query.blogMonetization.findFirst({
      where: eq(blogMonetization.blogId, blogId),
      with: {
        approver: {
          columns: {
            id: true,
            name: true,
          },
        },
        rejector: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });
  }
}
