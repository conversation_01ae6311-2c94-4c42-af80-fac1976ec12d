import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCommissionSettings } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// Get referral commission settings
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const settings = await db.query.referralCommissionSettings.findFirst({
      where: eq(referralCommissionSettings.id, 'default'),
    });

    const defaultSettings = {
      id: 'default',
      isEnabled: true,
      firstPurchaseEnabled: true,
      recurringPurchaseEnabled: false,
      defaultCommissionType: 'percentage' as const,
      defaultCommissionValue: '10.00',
      defaultRecurringCommissionValue: '5.00',
      maxCommissionAmount: '100.00',
      eligibilityPeriodDays: 30,
      minSubscriptionAmount: '1.00',
      requireActiveReferrer: true,
      maxCommissionsPerReferral: 0,
    };

    return NextResponse.json({
      success: true,
      data: settings || defaultSettings,
    });

  } catch (error) {
    console.error("Error fetching commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch commission settings"
      },
      { status: 500 }
    );
  }
}

// Update referral commission settings
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      isEnabled,
      firstPurchaseEnabled,
      recurringPurchaseEnabled,
      defaultCommissionType,
      defaultCommissionValue,
      defaultRecurringCommissionValue,
      maxCommissionAmount,
      eligibilityPeriodDays,
      minSubscriptionAmount,
      requireActiveReferrer,
      maxCommissionsPerReferral
    } = body;

    // Validate input
    if (typeof isEnabled !== 'boolean') {
      return NextResponse.json(
        { message: "Invalid isEnabled value" },
        { status: 400 }
      );
    }

    if (!['fixed', 'percentage'].includes(defaultCommissionType)) {
      return NextResponse.json(
        { message: "Invalid commission type" },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(defaultCommissionValue)) || parseFloat(defaultCommissionValue) < 0) {
      return NextResponse.json(
        { message: "Invalid commission value" },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(maxCommissionAmount)) || parseFloat(maxCommissionAmount) < 0) {
      return NextResponse.json(
        { message: "Invalid max commission amount" },
        { status: 400 }
      );
    }

    if (isNaN(eligibilityPeriodDays) || eligibilityPeriodDays < 0) {
      return NextResponse.json(
        { message: "Invalid eligibility period" },
        { status: 400 }
      );
    }

    // Check if settings exist
    const existingSettings = await db.query.referralCommissionSettings.findFirst({
      where: eq(referralCommissionSettings.id, 'default'),
    });

    const settingsData = {
      isEnabled,
      firstPurchaseEnabled,
      recurringPurchaseEnabled,
      defaultCommissionType,
      defaultCommissionValue,
      defaultRecurringCommissionValue,
      maxCommissionAmount,
      eligibilityPeriodDays,
      minSubscriptionAmount,
      requireActiveReferrer,
      maxCommissionsPerReferral,
      updatedAt: new Date(),
    };

    if (existingSettings) {
      // Update existing settings
      await db
        .update(referralCommissionSettings)
        .set(settingsData)
        .where(eq(referralCommissionSettings.id, 'default'));
    } else {
      // Create new settings
      await db.insert(referralCommissionSettings).values({
        id: 'default',
        ...settingsData,
        createdAt: new Date(),
      });
    }

    return NextResponse.json({
      success: true,
      message: "Commission settings updated successfully",
    });

  } catch (error) {
    console.error("Error updating commission settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update commission settings"
      },
      { status: 500 }
    );
  }
}
