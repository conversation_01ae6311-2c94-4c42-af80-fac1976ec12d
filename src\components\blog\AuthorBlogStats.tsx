"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Spinner } from "@/components/ui/Spinner";
import { MonetizationStatusBadge } from "@/components/blog/MonetizationStatusBadge";
import { 
  CurrencyDollarIcon, 
  DocumentTextIcon,
  EyeIcon,
  ClockIcon
} from "@heroicons/react/24/outline";

interface AuthorBlogStatsProps {
  authorId: string;
}

interface BlogStats {
  totalBlogs: number;
  publishedBlogs: number;
  draftBlogs: number;
  totalViews: number;
  monetizedBlogs: number;
  pendingMonetization: number;
  totalEarnings: string;
  recentBlogs: Array<{
    id: string;
    title: string;
    slug: string;
    status: string;
    viewCount: number;
    monetization?: {
      status: 'pending' | 'approved' | 'rejected' | 'suspended';
      totalEarnings: string;
    } | null;
  }>;
}

export function AuthorBlogStats({ authorId }: AuthorBlogStatsProps) {
  const [stats, setStats] = useState<BlogStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, [authorId]);

  const fetchStats = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/author/blog-stats?authorId=${authorId}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Error fetching blog stats:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DocumentTextIcon className="h-5 w-5" />
            Your Blog Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalBlogs}</div>
              <div className="text-sm text-blue-700">Total Blogs</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.publishedBlogs}</div>
              <div className="text-sm text-green-700">Published</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{stats.draftBlogs}</div>
              <div className="text-sm text-yellow-700">Drafts</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.totalViews.toLocaleString()}</div>
              <div className="text-sm text-purple-700">Total Views</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monetization Stats */}
      {(stats.monetizedBlogs > 0 || stats.pendingMonetization > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CurrencyDollarIcon className="h-5 w-5" />
              Monetization Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{stats.monetizedBlogs}</div>
                <div className="text-sm text-green-700">Monetized Blogs</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{stats.pendingMonetization}</div>
                <div className="text-sm text-yellow-700">Pending Approval</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">${parseFloat(stats.totalEarnings).toFixed(2)}</div>
                <div className="text-sm text-blue-700">Total Earnings</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Blogs with Monetization Status */}
      {stats.recentBlogs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Blogs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentBlogs.map((blog) => (
                <div
                  key={blog.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
                >
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 truncate">{blog.title}</h4>
                    <div className="flex items-center gap-3 mt-1">
                      <Badge variant={blog.status === 'published' ? 'success' : 'secondary'}>
                        {blog.status}
                      </Badge>
                      <div className="flex items-center text-sm text-gray-500">
                        <EyeIcon className="h-4 w-4 mr-1" />
                        {blog.viewCount} views
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {blog.monetization && (
                      <MonetizationStatusBadge 
                        status={blog.monetization.status}
                        size="sm"
                      />
                    )}
                    {blog.monetization?.status === 'approved' && (
                      <div className="text-sm text-green-600 font-medium">
                        ${parseFloat(blog.monetization.totalEarnings).toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
