"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { toast } from "react-hot-toast";
import {
  Cog6ToothIcon,
  BanknotesIcon,
  ArrowPathIcon,
  CheckIcon,
  XMarkIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

interface WalletSettings {
  // Feature toggles
  deposit_enabled: boolean;
  withdraw_enabled: boolean;
  send_enabled: boolean;
  cashout_enabled: boolean;
  internal_transfer_enabled: boolean;

  // Fee settings
  // Deposit fees
  deposit_fee_percentage: string;
  deposit_fee_fixed: string;
  deposit_min_amount: string;
  deposit_max_amount: string;

  // Withdraw fees
  withdraw_fee_percentage: string;
  withdraw_fee_fixed: string;
  withdraw_min_amount: string;
  withdraw_max_amount: string;

  // Send money fees
  send_fee_percentage: string;
  send_fee_fixed: string;
  send_min_amount: string;
  send_max_amount: string;

  // Transfer fees (internal transfers)
  transfer_fee_percentage: string;
  transfer_fee_fixed: string;
  transfer_min_amount: string;
  transfer_max_amount: string;

  // Daily limits
  daily_deposit_limit: string;
  daily_send_limit: string;
  daily_cashout_limit: string;
  daily_withdraw_limit: string;

  // Processing settings
  auto_approve_withdrawals: boolean;
  withdrawal_processing_time: string;
  require_pin_for_withdrawals: boolean;
  require_pin_for_transfers: boolean;
}

export default function AdminWalletSettingsPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("fees");
  const [settings, setSettings] = useState<WalletSettings>({
    // Feature toggles
    deposit_enabled: true,
    withdraw_enabled: true,
    send_enabled: true,
    cashout_enabled: true,
    internal_transfer_enabled: true,

    // Fee settings
    // Deposit fees
    deposit_fee_percentage: "0.00",
    deposit_fee_fixed: "0.00",
    deposit_min_amount: "1.00",
    deposit_max_amount: "10000.00",

    // Withdraw fees
    withdraw_fee_percentage: "2.00",
    withdraw_fee_fixed: "0.00",
    withdraw_min_amount: "10.00",
    withdraw_max_amount: "10000.00",

    // Send money fees
    send_fee_percentage: "1.00",
    send_fee_fixed: "0.00",
    send_min_amount: "1.00",
    send_max_amount: "5000.00",

    // Transfer fees (internal transfers)
    transfer_fee_percentage: "0.50",
    transfer_fee_fixed: "0.00",
    transfer_min_amount: "1.00",
    transfer_max_amount: "5000.00",

    // Daily limits
    daily_deposit_limit: "10000.00",
    daily_send_limit: "5000.00",
    daily_cashout_limit: "3000.00",
    daily_withdraw_limit: "5000.00",

    // Processing settings
    auto_approve_withdrawals: false,
    withdrawal_processing_time: "24",
    require_pin_for_withdrawals: true,
    require_pin_for_transfers: true,
  });

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/wallet/settings');
      const result = await response.json();

      if (result.success) {
        setSettings(result.data);
      } else {
        toast.error(result.message || "Failed to fetch wallet settings");
      }
    } catch (error) {
      console.error("Error fetching wallet settings:", error);
      toast.error("Failed to fetch wallet settings");
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/admin/wallet/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Wallet settings saved successfully!");
      } else {
        toast.error(result.message || "Failed to save wallet settings");
      }
    } catch (error) {
      console.error("Error saving wallet settings:", error);
      toast.error("Failed to save wallet settings");
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (key: keyof WalletSettings, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const calculateFeeExample = (amount: string, feePercentage: string, feeFixed: string) => {
    const amt = parseFloat(amount) || 0;
    const percentage = parseFloat(feePercentage) || 0;
    const fixed = parseFloat(feeFixed) || 0;

    const percentageFee = (amt * percentage) / 100;
    const totalFee = percentageFee + fixed;
    const netAmount = amt - totalFee;

    return {
      totalFee: totalFee.toFixed(2),
      netAmount: netAmount.toFixed(2),
    };
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const tabs = [
    { id: "fees", name: "Fee Settings", icon: BanknotesIcon },
    { id: "features", name: "Features", icon: Cog6ToothIcon },
    { id: "limits", name: "Daily Limits", icon: InformationCircleIcon },
    { id: "processing", name: "Processing", icon: ArrowPathIcon },
  ];

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <ArrowPathIcon className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading wallet settings...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Wallet Settings</h1>
            <p className="text-gray-600 mt-1">
              Configure wallet features, fees, and limits
            </p>
          </div>
          <Button
            onClick={saveSettings}
            disabled={saving}
            className="flex items-center"
          >
            {saving ? (
              <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
            ) : (
              <CheckIcon className="h-5 w-5 mr-2" />
            )}
            {saving ? "Saving..." : "Save Settings"}
          </Button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          {activeTab === "fees" && (
            <div className="space-y-8">
              {/* Deposit Fees */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Deposit Fees</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Percentage Fee (%)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="10"
                      value={settings.deposit_fee_percentage}
                      onChange={(e) => handleInputChange('deposit_fee_percentage', e.target.value)}
                      placeholder="0.00"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Percentage fee charged on deposit amount
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fixed Fee ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.deposit_fee_fixed}
                      onChange={(e) => handleInputChange('deposit_fee_fixed', e.target.value)}
                      placeholder="0.00"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Fixed fee added to each deposit
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.deposit_min_amount}
                      onChange={(e) => handleInputChange('deposit_min_amount', e.target.value)}
                      placeholder="1.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Maximum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.deposit_max_amount}
                      onChange={(e) => handleInputChange('deposit_max_amount', e.target.value)}
                      placeholder="10000.00"
                    />
                  </div>
                </div>

                {/* Fee Calculator for Deposits */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Deposit Fee Calculator</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Example: $100 deposit</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("100", settings.deposit_fee_percentage, settings.deposit_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Example: $500 deposit</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("500", settings.deposit_fee_percentage, settings.deposit_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Example: $1000 deposit</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("1000", settings.deposit_fee_percentage, settings.deposit_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Withdrawal Fees */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Withdrawal Fees</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Percentage Fee (%)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="10"
                      value={settings.withdraw_fee_percentage}
                      onChange={(e) => handleInputChange('withdraw_fee_percentage', e.target.value)}
                      placeholder="2.00"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Percentage fee charged on withdrawal amount
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fixed Fee ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.withdraw_fee_fixed}
                      onChange={(e) => handleInputChange('withdraw_fee_fixed', e.target.value)}
                      placeholder="0.00"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Fixed fee added to each withdrawal
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.withdraw_min_amount}
                      onChange={(e) => handleInputChange('withdraw_min_amount', e.target.value)}
                      placeholder="10.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Maximum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.withdraw_max_amount}
                      onChange={(e) => handleInputChange('withdraw_max_amount', e.target.value)}
                      placeholder="10000.00"
                    />
                  </div>
                </div>

                {/* Fee Calculator */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Fee Calculator</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Example: $100 withdrawal</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("100", settings.withdraw_fee_percentage, settings.withdraw_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Example: $500 withdrawal</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("500", settings.withdraw_fee_percentage, settings.withdraw_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Example: $1000 withdrawal</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("1000", settings.withdraw_fee_percentage, settings.withdraw_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Send Money Fees */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Send Money Fees</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Percentage Fee (%)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="10"
                      value={settings.send_fee_percentage}
                      onChange={(e) => handleInputChange('send_fee_percentage', e.target.value)}
                      placeholder="1.00"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Percentage fee charged on send money transactions
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fixed Fee ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.send_fee_fixed}
                      onChange={(e) => handleInputChange('send_fee_fixed', e.target.value)}
                      placeholder="0.00"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Fixed fee added to each send money transaction
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.send_min_amount}
                      onChange={(e) => handleInputChange('send_min_amount', e.target.value)}
                      placeholder="1.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Maximum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.send_max_amount}
                      onChange={(e) => handleInputChange('send_max_amount', e.target.value)}
                      placeholder="5000.00"
                    />
                  </div>
                </div>

                {/* Fee Calculator for Send Money */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Send Money Fee Calculator</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Example: $100 send</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("100", settings.send_fee_percentage, settings.send_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Example: $500 send</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("500", settings.send_fee_percentage, settings.send_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Example: $1000 send</span>
                      <div className="mt-1">
                        {(() => {
                          const calc = calculateFeeExample("1000", settings.send_fee_percentage, settings.send_fee_fixed);
                          return (
                            <div>
                              <div>Fee: ${calc.totalFee}</div>
                              <div className="font-medium">Net: ${calc.netAmount}</div>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Internal Transfer Fees */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Internal Transfer Fees</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Percentage Fee (%)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="10"
                      value={settings.transfer_fee_percentage}
                      onChange={(e) => handleInputChange('transfer_fee_percentage', e.target.value)}
                      placeholder="0.50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fixed Fee ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.transfer_fee_fixed}
                      onChange={(e) => handleInputChange('transfer_fee_fixed', e.target.value)}
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.transfer_min_amount}
                      onChange={(e) => handleInputChange('transfer_min_amount', e.target.value)}
                      placeholder="1.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Maximum Amount ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={settings.transfer_max_amount}
                      onChange={(e) => handleInputChange('transfer_max_amount', e.target.value)}
                      placeholder="5000.00"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "features" && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Feature Controls</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[
                  { key: 'deposit_enabled', label: 'Enable Deposits', description: 'Allow users to deposit money into their wallets' },
                  { key: 'withdraw_enabled', label: 'Enable Withdrawals', description: 'Allow users to withdraw money from earning wallet' },
                  { key: 'send_enabled', label: 'Enable Transfers', description: 'Allow users to send money to other users' },
                  { key: 'cashout_enabled', label: 'Enable Cashouts', description: 'Allow users to cashout through agents' },
                  { key: 'internal_transfer_enabled', label: 'Enable Internal Transfers', description: 'Allow transfers between earning and general wallet' },
                ].map((feature) => (
                  <div key={feature.key} className="flex items-start space-x-3">
                    <div className="flex items-center h-5">
                      <input
                        type="checkbox"
                        checked={settings[feature.key as keyof WalletSettings] as boolean}
                        onChange={(e) => handleInputChange(feature.key as keyof WalletSettings, e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="min-w-0 flex-1">
                      <label className="text-sm font-medium text-gray-900">
                        {feature.label}
                      </label>
                      <p className="text-sm text-gray-500">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === "limits" && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Daily Transaction Limits</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Daily Deposit Limit ($)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.daily_deposit_limit}
                    onChange={(e) => handleInputChange('daily_deposit_limit', e.target.value)}
                    placeholder="10000.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Daily Send Limit ($)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.daily_send_limit}
                    onChange={(e) => handleInputChange('daily_send_limit', e.target.value)}
                    placeholder="5000.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Daily Cashout Limit ($)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.daily_cashout_limit}
                    onChange={(e) => handleInputChange('daily_cashout_limit', e.target.value)}
                    placeholder="3000.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Daily Withdrawal Limit ($)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.daily_withdraw_limit}
                    onChange={(e) => handleInputChange('daily_withdraw_limit', e.target.value)}
                    placeholder="5000.00"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === "processing" && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Processing Settings</h3>

              <div className="space-y-6">
                <div className="flex items-start space-x-3">
                  <div className="flex items-center h-5">
                    <input
                      type="checkbox"
                      checked={settings.auto_approve_withdrawals}
                      onChange={(e) => handleInputChange('auto_approve_withdrawals', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                  <div className="min-w-0 flex-1">
                    <label className="text-sm font-medium text-gray-900">
                      Auto-approve Withdrawals
                    </label>
                    <p className="text-sm text-gray-500">
                      Automatically approve withdrawal requests without manual review
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Withdrawal Processing Time (hours)
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max="168"
                    value={settings.withdrawal_processing_time}
                    onChange={(e) => handleInputChange('withdrawal_processing_time', e.target.value)}
                    placeholder="24"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Expected processing time shown to users
                  </p>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex items-center h-5">
                    <input
                      type="checkbox"
                      checked={settings.require_pin_for_withdrawals}
                      onChange={(e) => handleInputChange('require_pin_for_withdrawals', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                  <div className="min-w-0 flex-1">
                    <label className="text-sm font-medium text-gray-900">
                      Require PIN for Withdrawals
                    </label>
                    <p className="text-sm text-gray-500">
                      Users must enter their wallet PIN to process withdrawals
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex items-center h-5">
                    <input
                      type="checkbox"
                      checked={settings.require_pin_for_transfers}
                      onChange={(e) => handleInputChange('require_pin_for_transfers', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                  <div className="min-w-0 flex-1">
                    <label className="text-sm font-medium text-gray-900">
                      Require PIN for Transfers
                    </label>
                    <p className="text-sm text-gray-500">
                      Users must enter their wallet PIN to send money to other users
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
