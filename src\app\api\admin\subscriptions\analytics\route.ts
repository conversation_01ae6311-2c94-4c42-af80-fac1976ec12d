import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { 
  userSubscriptions, 
  subscriptionPlans, 
  subscriptionTransactions,
  users 
} from "@/lib/db/schema";
import { eq, and, gte, lte, desc, count, sum, sql } from "drizzle-orm";
import { z } from "zod";

const querySchema = z.object({
  range: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
});

// GET - Fetch subscription analytics
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const { range } = querySchema.parse(Object.fromEntries(searchParams));

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (range) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    // Overview metrics
    const [
      totalRevenueResult,
      monthlyRevenueResult,
      totalSubscriptionsResult,
      activeSubscriptionsResult,
    ] = await Promise.all([
      // Total revenue from completed transactions
      db
        .select({ 
          total: sql<number>`COALESCE(SUM(CAST(${subscriptionTransactions.amount} AS DECIMAL(10,2))), 0)` 
        })
        .from(subscriptionTransactions)
        .where(
          and(
            eq(subscriptionTransactions.status, 'completed'),
            eq(subscriptionTransactions.type, 'payment'),
            gte(subscriptionTransactions.createdAt, startDate)
          )
        ),

      // Monthly revenue (last 30 days)
      db
        .select({ 
          total: sql<number>`COALESCE(SUM(CAST(${subscriptionTransactions.amount} AS DECIMAL(10,2))), 0)` 
        })
        .from(subscriptionTransactions)
        .where(
          and(
            eq(subscriptionTransactions.status, 'completed'),
            eq(subscriptionTransactions.type, 'payment'),
            gte(subscriptionTransactions.createdAt, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
          )
        ),

      // Total subscriptions
      db
        .select({ count: count() })
        .from(userSubscriptions)
        .where(gte(userSubscriptions.createdAt, startDate)),

      // Active subscriptions
      db
        .select({ count: count() })
        .from(userSubscriptions)
        .where(eq(userSubscriptions.status, 'active')),
    ]);

    // Calculate churn and retention rates
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    const [cancelledSubscriptions, totalActiveAtStart] = await Promise.all([
      db
        .select({ count: count() })
        .from(userSubscriptions)
        .where(
          and(
            eq(userSubscriptions.status, 'cancelled'),
            gte(userSubscriptions.cancelledAt, thirtyDaysAgo)
          )
        ),
      
      db
        .select({ count: count() })
        .from(userSubscriptions)
        .where(
          and(
            eq(userSubscriptions.status, 'active'),
            lte(userSubscriptions.startDate, thirtyDaysAgo)
          )
        ),
    ]);

    const churnRate = totalActiveAtStart[0].count > 0 
      ? (cancelledSubscriptions[0].count / totalActiveAtStart[0].count) * 100 
      : 0;
    const retentionRate = 100 - churnRate;

    // Revenue by plan
    const revenueByPlan = await db
      .select({
        planName: subscriptionPlans.displayName,
        revenue: sql<number>`COALESCE(SUM(CAST(${subscriptionTransactions.amount} AS DECIMAL(10,2))), 0)`,
        subscriptions: count(userSubscriptions.id),
      })
      .from(subscriptionTransactions)
      .innerJoin(subscriptionPlans, eq(subscriptionTransactions.planId, subscriptionPlans.id))
      .innerJoin(userSubscriptions, eq(subscriptionTransactions.subscriptionId, userSubscriptions.id))
      .where(
        and(
          eq(subscriptionTransactions.status, 'completed'),
          eq(subscriptionTransactions.type, 'payment'),
          gte(subscriptionTransactions.createdAt, startDate)
        )
      )
      .groupBy(subscriptionPlans.id, subscriptionPlans.displayName)
      .orderBy(desc(sql`revenue`));

    // Calculate percentages for revenue by plan
    const totalRevenue = totalRevenueResult[0].total;
    const revenueByPlanWithPercentage = revenueByPlan.map(plan => ({
      ...plan,
      percentage: totalRevenue > 0 ? (plan.revenue / totalRevenue) * 100 : 0,
    }));

    // Subscription growth (monthly data)
    const subscriptionGrowth = await db
      .select({
        month: sql<string>`DATE_FORMAT(${userSubscriptions.createdAt}, '%Y-%m')`,
        newSubscriptions: count(userSubscriptions.id),
      })
      .from(userSubscriptions)
      .where(gte(userSubscriptions.createdAt, startDate))
      .groupBy(sql`DATE_FORMAT(${userSubscriptions.createdAt}, '%Y-%m')`)
      .orderBy(sql`month`);

    // Get cancelled subscriptions by month
    const cancelledByMonth = await db
      .select({
        month: sql<string>`DATE_FORMAT(${userSubscriptions.cancelledAt}, '%Y-%m')`,
        cancelledSubscriptions: count(userSubscriptions.id),
      })
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.status, 'cancelled'),
          gte(userSubscriptions.cancelledAt, startDate)
        )
      )
      .groupBy(sql`DATE_FORMAT(${userSubscriptions.cancelledAt}, '%Y-%m')`)
      .orderBy(sql`month`);

    // Combine growth data
    const growthData = subscriptionGrowth.map(growth => {
      const cancelled = cancelledByMonth.find(c => c.month === growth.month);
      return {
        month: growth.month,
        newSubscriptions: growth.newSubscriptions,
        cancelledSubscriptions: cancelled?.cancelledSubscriptions || 0,
        netGrowth: growth.newSubscriptions - (cancelled?.cancelledSubscriptions || 0),
      };
    });

    // Payment metrics
    const [totalTransactions, failedTransactions] = await Promise.all([
      db
        .select({ count: count() })
        .from(subscriptionTransactions)
        .where(
          and(
            eq(subscriptionTransactions.type, 'payment'),
            gte(subscriptionTransactions.createdAt, startDate)
          )
        ),
      
      db
        .select({ count: count() })
        .from(subscriptionTransactions)
        .where(
          and(
            eq(subscriptionTransactions.type, 'payment'),
            eq(subscriptionTransactions.status, 'failed'),
            gte(subscriptionTransactions.createdAt, startDate)
          )
        ),
    ]);

    const successRate = totalTransactions[0].count > 0 
      ? ((totalTransactions[0].count - failedTransactions[0].count) / totalTransactions[0].count) * 100 
      : 0;

    // Top performing plans
    const topPlans = await db
      .select({
        planName: subscriptionPlans.displayName,
        subscriptions: count(userSubscriptions.id),
        revenue: sql<number>`COALESCE(SUM(CAST(${subscriptionPlans.price} AS DECIMAL(10,2))), 0)`,
      })
      .from(userSubscriptions)
      .innerJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id))
      .where(eq(userSubscriptions.status, 'active'))
      .groupBy(subscriptionPlans.id, subscriptionPlans.displayName, subscriptionPlans.price)
      .orderBy(desc(count(userSubscriptions.id)))
      .limit(5);

    // Calculate additional metrics
    const averageRevenuePerUser = activeSubscriptionsResult[0].count > 0 
      ? totalRevenue / activeSubscriptionsResult[0].count 
      : 0;

    const averageTransactionValue = totalTransactions[0].count > 0 
      ? totalRevenue / totalTransactions[0].count 
      : 0;

    const analytics = {
      overview: {
        totalRevenue: totalRevenue,
        monthlyRevenue: monthlyRevenueResult[0].total,
        totalSubscriptions: totalSubscriptionsResult[0].count,
        activeSubscriptions: activeSubscriptionsResult[0].count,
        churnRate,
        retentionRate,
        averageRevenuePerUser,
        conversionRate: 85.2, // This would need to be calculated based on your signup/conversion funnel
      },
      revenueByPlan: revenueByPlanWithPercentage,
      subscriptionGrowth: growthData,
      paymentMetrics: {
        successRate,
        failedPayments: failedTransactions[0].count,
        totalTransactions: totalTransactions[0].count,
        averageTransactionValue,
      },
      topPlans,
    };

    return NextResponse.json({
      success: true,
      analytics,
    });
  } catch (error) {
    console.error("Error fetching subscription analytics:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid query parameters",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch subscription analytics",
      },
      { status: 500 }
    );
  }
}
