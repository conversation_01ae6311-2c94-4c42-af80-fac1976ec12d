"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Switch } from "@/components/ui/Switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import {
  CurrencyDollarIcon,
  ClockIcon,
  CogIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";

interface MonetizationConfig {
  cprRate: number;
  minPayoutThreshold: number;
  minReadDuration: number;
  isEnabled: boolean;
}

export function MonetizationSettings() {
  const [config, setConfig] = useState<MonetizationConfig>({
    cprRate: 1.0,
    minPayoutThreshold: 10.0,
    minReadDuration: 120,
    isEnabled: true,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/monetization/settings');
      const data = await response.json();

      if (data.success) {
        setConfig(data.data);
      } else {
        toast.error(data.message || 'Failed to fetch settings');
      }
    } catch (error) {
      console.error('Error fetching monetization settings:', error);
      toast.error('Failed to fetch settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/admin/monetization/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Settings updated successfully');
      } else {
        toast.error(data.message || 'Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating monetization settings:', error);
      toast.error('Failed to update settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: keyof MonetizationConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CogIcon className="h-5 w-5" />
            Monetization Settings
          </CardTitle>
          <CardDescription>
            Configure global settings for blog monetization system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* System Enable/Disable */}
          <div className={`flex items-center justify-between p-4 border rounded-lg ${
            config.isEnabled ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
          }`}>
            <div>
              <Label className="text-base font-medium flex items-center gap-2">
                {config.isEnabled ? (
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                ) : (
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                )}
                Enable Monetization System
              </Label>
              <p className={`text-sm mt-1 ${
                config.isEnabled ? 'text-green-700' : 'text-red-700'
              }`}>
                {config.isEnabled
                  ? 'Authors can request monetization for their blog posts'
                  : 'Monetization is disabled - authors cannot request monetization'
                }
              </p>
              {!config.isEnabled && (
                <p className="text-xs text-red-600 mt-1 font-medium">
                  ⚠️ Disabling will prevent new monetization requests and hide monetization options
                </p>
              )}
            </div>
            <Switch
              checked={config.isEnabled}
              onCheckedChange={(checked) => handleInputChange('isEnabled', checked)}
            />
          </div>

          {/* CPR Rate */}
          <div className="space-y-2">
            <Label htmlFor="cprRate" className="flex items-center gap-2">
              <CurrencyDollarIcon className="h-4 w-4" />
              CPR Rate (USD per 1000 reads)
            </Label>
            <Input
              id="cprRate"
              type="number"
              step="0.01"
              min="0"
              max="100"
              value={config.cprRate}
              onChange={(e) => handleInputChange('cprRate', parseFloat(e.target.value) || 0)}
              placeholder="1.00"
            />
            <p className="text-sm text-gray-600">
              Amount paid to authors for every 1000 qualified reads
            </p>
          </div>

          {/* Minimum Payout Threshold */}
          <div className="space-y-2">
            <Label htmlFor="minPayoutThreshold" className="flex items-center gap-2">
              <CurrencyDollarIcon className="h-4 w-4" />
              Minimum Payout Threshold (USD)
            </Label>
            <Input
              id="minPayoutThreshold"
              type="number"
              step="0.01"
              min="0"
              max="10000"
              value={config.minPayoutThreshold}
              onChange={(e) => handleInputChange('minPayoutThreshold', parseFloat(e.target.value) || 0)}
              placeholder="10.00"
            />
            <p className="text-sm text-gray-600">
              Minimum amount authors must earn before they can request payout
            </p>
          </div>

          {/* Minimum Read Duration */}
          <div className="space-y-2">
            <Label htmlFor="minReadDuration" className="flex items-center gap-2">
              <ClockIcon className="h-4 w-4" />
              Minimum Read Duration (seconds)
            </Label>
            <Input
              id="minReadDuration"
              type="number"
              min="10"
              max="3600"
              value={config.minReadDuration}
              onChange={(e) => handleInputChange('minReadDuration', isNaN(parseInt(e.target.value)) ? 0 : parseInt(e.target.value))}
              placeholder="120"
            />
            <p className="text-sm text-gray-600">
              Minimum time a user must spend reading to count as a qualified read
            </p>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4">
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2"
            >
              {isSaving ? (
                <Spinner size="sm" />
              ) : (
                <CheckCircleIcon className="h-4 w-4" />
              )}
              {isSaving ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Settings Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Current Configuration</CardTitle>
          <CardDescription>
            Preview of current monetization settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                ${config.cprRate.toFixed(2)}
              </div>
              <div className="text-sm text-green-700">Per 1000 reads</div>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                ${config.minPayoutThreshold.toFixed(2)}
              </div>
              <div className="text-sm text-blue-700">Min payout</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {Math.floor(config.minReadDuration / 60)}m {config.minReadDuration % 60}s
              </div>
              <div className="text-sm text-purple-700">Min read time</div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className={`text-2xl font-bold ${config.isEnabled ? 'text-green-600' : 'text-red-600'}`}>
                {config.isEnabled ? 'Enabled' : 'Disabled'}
              </div>
              <div className="text-sm text-gray-700">System status</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
