import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { walletTransactions, users, agents } from "@/lib/db/schema";
import { count, eq, and, or, like, desc, asc, gte, lte, ne } from "drizzle-orm";
import { z } from "zod";

const querySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(20),
  search: z.string().optional(),
  type: z.string().optional(),
  status: z.string().optional(),
  gateway: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sort: z.string().optional().default("createdAt"),
  order: z.string().optional().default("desc"),
  excludeDeposits: z.string().optional(),
  walletType: z.string().optional(),
});

// Get all transactions for admin
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const searchParams = Object.fromEntries(url.searchParams);
    const validatedParams = querySchema.parse(searchParams);

    const {
      page,
      limit,
      search,
      type,
      status,
      gateway,
      dateFrom,
      dateTo,
      sort,
      order,
      excludeDeposits,
      walletType
    } = validatedParams;

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any[] = [];

    if (search) {
      whereConditions.push(
        or(
          like(walletTransactions.reference, `%${search}%`),
          like(walletTransactions.gatewayTransactionId, `%${search}%`),
          like(walletTransactions.note, `%${search}%`)
        )
      );
    }

    if (type) {
      whereConditions.push(eq(walletTransactions.type, type as any));
    }

    if (status) {
      whereConditions.push(eq(walletTransactions.status, status as any));
    }

    if (gateway) {
      whereConditions.push(eq(walletTransactions.paymentGateway, gateway));
    }

    if (dateFrom) {
      whereConditions.push(gte(walletTransactions.createdAt, new Date(dateFrom)));
    }

    if (dateTo) {
      whereConditions.push(lte(walletTransactions.createdAt, new Date(dateTo)));
    }

    // Exclude deposits if requested
    if (excludeDeposits === 'true') {
      whereConditions.push(ne(walletTransactions.type, 'deposit'));
    }

    // Filter by wallet type if requested
    if (walletType) {
      whereConditions.push(eq(walletTransactions.walletType, walletType as any));
    }

    // Build order by
    const orderBy = order === "asc" ? asc : desc;
    let sortColumn;
    switch (sort) {
      case "amount":
        sortColumn = walletTransactions.amount;
        break;
      case "status":
        sortColumn = walletTransactions.status;
        break;
      case "type":
        sortColumn = walletTransactions.type;
        break;
      default:
        sortColumn = walletTransactions.createdAt;
    }

    // Get transactions with user and agent details
    const transactions = await db.query.walletTransactions.findMany({
      where: whereConditions.length > 0 ? and(...whereConditions) : undefined,
      orderBy: [orderBy(sortColumn)],
      limit,
      offset,
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
        toUser: {
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
          },
        },
        toAgent: {
          columns: {
            id: true,
            name: true,
            serviceType: true,
            phone: true,
          },
        },
      },
    });

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data: {
        transactions,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
        },
      },
    });
  } catch (error: any) {
    console.error("Error fetching admin transactions:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch transactions",
        error: error.message
      },
      { status: 500 }
    );
  }
}
