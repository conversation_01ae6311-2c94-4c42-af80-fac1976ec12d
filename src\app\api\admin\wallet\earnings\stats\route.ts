import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { walletTransactions, wallets } from "@/lib/db/schema";
import { count, eq, and, sum, desc, gte } from "drizzle-orm";

// Get earning wallet statistics for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get today's date for filtering
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Total earnings (all earning transactions)
    const totalEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      );

    // Total withdrawals from earning wallet
    const totalWithdrawalsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'withdraw'),
          eq(walletTransactions.walletType, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      );

    // Total internal transfers from earning wallet
    const totalTransfersResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'internal_transfer'),
          eq(walletTransactions.fromWalletType, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      );

    // Pending earning transactions count
    const pendingEarningsResult = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.walletType, 'earning'),
          eq(walletTransactions.status, 'pending')
        )
      );

    // Completed earning transactions count
    const completedEarningsResult = await db
      .select({ count: count() })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.walletType, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      );

    // Today's earnings
    const todayEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, today)
        )
      );

    // Total users with earning balance > 0
    const totalUsersResult = await db
      .select({ count: count() })
      .from(wallets)
      .where(gte(wallets.earningBalance, '0.01'));

    // Calculate average earning per user
    const totalEarnings = parseFloat(totalEarningsResult[0]?.total || '0');
    const totalUsers = totalUsersResult[0]?.count || 1;
    const averageEarning = totalUsers > 0 ? totalEarnings / totalUsers : 0;

    const stats = {
      totalEarnings: totalEarnings,
      totalWithdrawals: parseFloat(totalWithdrawalsResult[0]?.total || '0'),
      totalTransfers: parseFloat(totalTransfersResult[0]?.total || '0'),
      pendingEarnings: pendingEarningsResult[0]?.count || 0,
      completedEarnings: completedEarningsResult[0]?.count || 0,
      todayEarnings: parseFloat(todayEarningsResult[0]?.total || '0'),
      totalUsers: totalUsers,
      averageEarning: averageEarning,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error: any) {
    console.error("Error fetching earning stats:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch earning statistics" 
      },
      { status: 500 }
    );
  }
}
