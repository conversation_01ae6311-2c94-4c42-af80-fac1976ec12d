"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { SettingsLayout } from "./SettingsLayout";
import { Button } from "@/components/ui/Button";
import { ProfilePhotoUploader } from "@/components/profile/ProfilePhotoUploader";
import { CoverPhotoUploader } from "@/components/profile/CoverPhotoUploader";
import { PhotoIcon, CameraIcon } from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";

interface UserData {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  username?: string | null;
  bio?: string | null;
  location?: string | null;
  birthday?: string | null;
  coverImage?: string | null;
}

interface ProfileSettingsFormProps {
  userId: string;
}

export function ProfileSettingsForm({ userId }: ProfileSettingsFormProps) {
  const { data: session, update } = useSession();
  const router = useRouter();

  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showProfileUploader, setShowProfileUploader] = useState(false);
  const [showCoverUploader, setShowCoverUploader] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    username: "",
    bio: "",
    location: "",
    birthday: "",
  });

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/users/${userId}`);

        if (response.ok) {
          const data = await response.json();
          setUserData(data);
          setFormData({
            name: data.name || "",
            username: data.username || "",
            bio: data.bio || "",
            location: data.location || "",
            birthday: data.birthday ? new Date(data.birthday).toISOString().split('T')[0] : "",
          });
        } else {
          setError("Failed to load profile data");
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setError("An error occurred while loading your profile");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Basic validation
    if (formData.name.trim().length < 2) {
      setError("Name must be at least 2 characters long");
      setIsSubmitting(false);
      return;
    }

    if (formData.username.trim().length > 0 && formData.username.trim().length < 3) {
      setError("Username must be at least 3 characters long");
      setIsSubmitting(false);
      return;
    }

    if (formData.username && !/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      setError("Username can only contain letters, numbers, and underscores");
      setIsSubmitting(false);
      return;
    }

    if (formData.bio.length > 500) {
      setError("Bio cannot exceed 500 characters");
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name.trim() || undefined,
          username: formData.username.trim() || undefined,
          bio: formData.bio.trim() || undefined,
          location: formData.location.trim() || undefined,
          birthday: formData.birthday || undefined,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to update profile");
      }

      // Update session if name changed
      if (formData.name !== userData?.name) {
        await update({
          ...session,
          user: {
            ...session?.user,
            name: formData.name,
          },
        });
      }

      setSuccess("Profile updated successfully!");

      // Refresh user data
      const updatedResponse = await fetch(`/api/users/${userId}`);
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json();
        setUserData(updatedData);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

    } catch (error) {
      console.error("Error updating profile:", error);
      setError(error instanceof Error ? error.message : "Failed to update profile");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (userData) {
      setFormData({
        name: userData.name || "",
        username: userData.username || "",
        bio: userData.bio || "",
        location: userData.location || "",
        birthday: userData.birthday ? new Date(userData.birthday).toISOString().split('T')[0] : "",
      });
    }
    setError(null);
    setSuccess(null);
  };

  const handlePhotoUpdate = async () => {
    // Refresh user data after photo update
    try {
      const response = await fetch(`/api/users/${userId}`);
      if (response.ok) {
        const updatedData = await response.json();
        setUserData(updatedData);

        // Update session with new image
        await update({
          ...session,
          user: {
            ...session?.user,
            image: updatedData.image,
          },
        });
      }
    } catch (error) {
      console.error("Error refreshing user data:", error);
    }
  };

  if (isLoading) {
    return (
      <SettingsLayout
        title="Profile Settings"
        description="Manage your profile information and visibility."
      >
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </SettingsLayout>
    );
  }

  return (
    <SettingsLayout
      title="Profile Settings"
      description="Manage your profile information and visibility."
    >
      {/* Profile Photos Section */}
      <div className="mb-8">
        <h4 className="text-sm font-medium text-gray-900 mb-4">Profile Photos</h4>

        {/* Cover Photo */}
        <div className="relative mb-6">
          <div className="relative h-32 w-full rounded-lg overflow-hidden bg-gray-200">
            {userData?.coverImage ? (
              <OptimizedImage
                src={userData.coverImage}
                alt="Cover photo"
                fill
                className="object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-gradient-to-r from-blue-400 to-purple-500">
                <PhotoIcon className="h-12 w-12 text-white" />
              </div>
            )}
            <button
              onClick={() => setShowCoverUploader(true)}
              className="absolute bottom-2 right-2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50 transition-colors"
            >
              <CameraIcon className="h-4 w-4 text-gray-600" />
            </button>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Recommended size: 1200x400px
          </p>
        </div>

        {/* Profile Photo */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="h-20 w-20 rounded-full overflow-hidden bg-gray-200">
              {userData?.image ? (
                <OptimizedImage
                  src={userData.image}
                  alt="Profile photo"
                  width={80}
                  height={80}
                  className="object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-300">
                  <span className="text-gray-600 text-xl font-medium">
                    {userData?.name?.charAt(0)?.toUpperCase() || "?"}
                  </span>
                </div>
              )}
            </div>
            <button
              onClick={() => setShowProfileUploader(true)}
              className="absolute -bottom-1 -right-1 bg-white rounded-full p-1.5 shadow-lg hover:bg-gray-50 transition-colors"
            >
              <CameraIcon className="h-3 w-3 text-gray-600" />
            </button>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">Profile Picture</p>
            <p className="text-xs text-gray-500">Recommended size: 400x400px</p>
          </div>
        </div>
      </div>

      {/* Profile Information Form */}
      <form onSubmit={handleSubmit}>
        <div>
          <h4 className="text-sm font-medium text-gray-900">Profile Information</h4>

          {/* Error Message */}
          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mt-4 bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">Success</h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>{success}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-4 space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name *
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`block w-full rounded-md shadow-sm sm:text-sm ${
                    formData.name.trim().length < 2 && formData.name.length > 0
                      ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                      : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  }`}
                  placeholder="Enter your full name"
                  required
                />
              </div>
              {formData.name.trim().length < 2 && formData.name.length > 0 && (
                <p className="mt-1 text-xs text-red-600">
                  Name must be at least 2 characters long
                </p>
              )}
            </div>

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                Username
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="username"
                  id="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  className={`block w-full rounded-md shadow-sm sm:text-sm ${
                    (formData.username.length > 0 && formData.username.length < 3) ||
                    (formData.username && !/^[a-zA-Z0-9_]+$/.test(formData.username))
                      ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                      : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  }`}
                  placeholder="Enter your username"
                />
              </div>
              {formData.username.length > 0 && formData.username.length < 3 && (
                <p className="mt-1 text-xs text-red-600">
                  Username must be at least 3 characters long
                </p>
              )}
              {formData.username && !/^[a-zA-Z0-9_]+$/.test(formData.username) && (
                <p className="mt-1 text-xs text-red-600">
                  Username can only contain letters, numbers, and underscores
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                This will be used for your profile URL: hifnf.com/user/{formData.username || "username"}
              </p>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div className="mt-1">
                <input
                  type="email"
                  name="email"
                  id="email"
                  value={userData?.email || ""}
                  className="block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm sm:text-sm"
                  disabled
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Email cannot be changed.
              </p>
            </div>

            <div>
              <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                Bio
              </label>
              <div className="mt-1">
                <textarea
                  id="bio"
                  name="bio"
                  rows={3}
                  value={formData.bio}
                  onChange={handleInputChange}
                  className={`block w-full rounded-md shadow-sm sm:text-sm ${
                    formData.bio.length > 500
                      ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                      : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  }`}
                  placeholder="Tell us about yourself"
                  maxLength={500}
                />
              </div>
              <p className={`mt-1 text-xs ${
                formData.bio.length > 500 ? "text-red-600" : "text-gray-500"
              }`}>
                {formData.bio.length}/500 characters
              </p>
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                Location
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="location"
                  id="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  placeholder="City, Country"
                />
              </div>
            </div>

            <div>
              <label htmlFor="birthday" className="block text-sm font-medium text-gray-700">
                Birthday
              </label>
              <div className="mt-1">
                <input
                  type="date"
                  name="birthday"
                  id="birthday"
                  value={formData.birthday}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="pt-5">
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </div>
      </form>

      {/* Photo Upload Modals */}
      <ProfilePhotoUploader
        isOpen={showProfileUploader}
        onClose={() => setShowProfileUploader(false)}
        currentPhotoUrl={userData?.image || null}
        onSuccess={handlePhotoUpdate}
      />

      <CoverPhotoUploader
        isOpen={showCoverUploader}
        onClose={() => setShowCoverUploader(false)}
        currentCoverUrl={userData?.coverImage || null}
        onSuccess={handlePhotoUpdate}
      />
    </SettingsLayout>
  );
}
