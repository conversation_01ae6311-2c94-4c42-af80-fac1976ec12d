"use client";

import { cn } from "@/lib/utils";
import {
  CheckIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
} from "@heroicons/react/24/outline";

export type MessageStatus = 'optimistic' | 'sending' | 'sent' | 'delivered' | 'read' | 'failed' | 'retrying';

interface MessageStatusIndicatorProps {
  status: MessageStatus;
  timestamp?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  retryCount?: number;
  onRetry?: () => void;
}

export function MessageStatusIndicator({
  status,
  timestamp,
  showText = false,
  size = 'sm',
  className,
  retryCount = 0,
  onRetry,
}: MessageStatusIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'optimistic':
        return {
          icon: ClockIcon,
          color: 'text-gray-300',
          bgColor: 'bg-gray-50',
          text: '',
          animate: '',
        };
      case 'sending':
        return {
          icon: ArrowPathIcon,
          color: 'text-blue-400',
          bgColor: 'bg-blue-50',
          text: 'Sending...',
          animate: 'animate-spin',
        };
      case 'sent':
        return {
          icon: CheckIcon,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          text: 'Sent',
          animate: '',
        };
      case 'delivered':
        return {
          icon: CheckCircleIcon,
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          text: 'Delivered',
          animate: '',
        };
      case 'read':
        return {
          icon: CheckCircleIcon,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          text: 'Read',
          animate: '',
        };
      case 'failed':
        return {
          icon: ExclamationTriangleIcon,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          text: 'Failed',
          animate: '',
        };
      case 'retrying':
        return {
          icon: ArrowPathIcon,
          color: 'text-orange-500',
          bgColor: 'bg-orange-50',
          text: 'Retrying...',
          animate: 'animate-spin',
        };
      default:
        return {
          icon: CheckIcon,
          color: 'text-gray-400',
          bgColor: 'bg-gray-100',
          text: 'Unknown',
          animate: '',
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          icon: 'h-3 w-3',
          container: 'p-1',
          text: 'text-xs',
        };
      case 'md':
        return {
          icon: 'h-4 w-4',
          container: 'p-1.5',
          text: 'text-sm',
        };
      case 'lg':
        return {
          icon: 'h-5 w-5',
          container: 'p-2',
          text: 'text-base',
        };
      default:
        return {
          icon: 'h-3 w-3',
          container: 'p-1',
          text: 'text-xs',
        };
    }
  };

  const config = getStatusConfig();
  const sizeClasses = getSizeClasses();
  const IconComponent = config.icon;

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (showText) {
    return (
      <div className={cn(
        "flex items-center space-x-2",
        sizeClasses.text,
        config.color,
        className
      )}>
        <div className={cn(
          "rounded-full flex items-center justify-center",
          config.bgColor,
          sizeClasses.container
        )}>
          <IconComponent className={cn(
            sizeClasses.icon,
            config.color,
            config.animate
          )} />
        </div>
        <span className="font-medium">{config.text}</span>
        {timestamp && (
          <span className="text-gray-400">
            • {formatTimestamp(timestamp)}
          </span>
        )}
      </div>
    );
  }

  return (
    <div 
      className={cn(
        "inline-flex items-center justify-center rounded-full",
        config.bgColor,
        sizeClasses.container,
        className
      )}
      title={`${config.text}${timestamp ? ` • ${formatTimestamp(timestamp)}` : ''}`}
    >
      <IconComponent className={cn(
        sizeClasses.icon,
        config.color,
        config.animate
      )} />
    </div>
  );
}

// Utility function to determine message status
export function getMessageStatus(message: any): MessageStatus {
  if (message.failed) return 'failed';
  if (message.retrying) return 'retrying';
  if (message.read) return 'read';
  if (message.delivered) return 'delivered';
  if (message.sent) return 'sent';
  if (message.sending) return 'sending';
  
  // Fallback logic based on timestamps and read status
  if (message.read) return 'read';
  if (message.createdAt) return 'sent';
  
  return 'sent';
}

// Batch status indicator for multiple messages
interface BatchMessageStatusProps {
  messages: any[];
  className?: string;
}

export function BatchMessageStatus({ messages, className }: BatchMessageStatusProps) {
  const statusCounts = messages.reduce((acc, message) => {
    const status = getMessageStatus(message);
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<MessageStatus, number>);

  const totalMessages = messages.length;
  const readCount = statusCounts.read || 0;
  const failedCount = statusCounts.failed || 0;

  if (failedCount > 0) {
    return (
      <div className={cn("flex items-center space-x-1 text-red-500 text-xs", className)}>
        <ExclamationTriangleIcon className="h-3 w-3" />
        <span>{failedCount} failed</span>
      </div>
    );
  }

  if (readCount === totalMessages) {
    return (
      <div className={cn("flex items-center space-x-1 text-green-500 text-xs", className)}>
        <CheckCircleIcon className="h-3 w-3" />
        <span>All read</span>
      </div>
    );
  }

  if (readCount > 0) {
    return (
      <div className={cn("flex items-center space-x-1 text-blue-500 text-xs", className)}>
        <CheckCircleIcon className="h-3 w-3" />
        <span>{readCount}/{totalMessages} read</span>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-1 text-gray-500 text-xs", className)}>
      <CheckIcon className="h-3 w-3" />
      <span>Delivered</span>
    </div>
  );
}
