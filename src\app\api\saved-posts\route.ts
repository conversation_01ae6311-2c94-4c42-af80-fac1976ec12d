import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { savedPosts, posts, users, likes, comments } from "@/lib/db/schema";
import { eq, desc, count, sql } from "drizzle-orm";

// GET /api/saved-posts - Get all saved posts for the current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get pagination parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20); // Max 20 posts
    const offset = (page - 1) * limit;

    // Get saved posts with full post details
    // Use DISTINCT to prevent duplicate posts if there are multiple saved entries for the same post
    const savedPostsData = await db
      .selectDistinct({
        savedPost: savedPosts,
        post: {
          id: posts.id,
          content: posts.content,
          images: posts.images,
          videos: posts.videos,
          privacy: posts.privacy,
          backgroundColor: posts.backgroundColor,
          feeling: posts.feeling,
          activity: posts.activity,
          location: posts.location,
          formattedContent: posts.formattedContent,
          createdAt: posts.createdAt,
          updatedAt: posts.updatedAt,
          userId: posts.userId,
          groupId: posts.groupId,
          sharedPostId: posts.sharedPostId,
        },
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
        likeCount: sql<number>`(
          SELECT COUNT(*) FROM ${likes}
          WHERE ${likes.postId} = ${posts.id}
          AND ${likes.type} = 'like'
        )`.as('likeCount'),
        dislikeCount: sql<number>`(
          SELECT COUNT(*) FROM ${likes}
          WHERE ${likes.postId} = ${posts.id}
          AND ${likes.type} = 'dislike'
        )`.as('dislikeCount'),
        commentCount: sql<number>`(
          SELECT COUNT(*) FROM ${comments}
          WHERE ${comments.postId} = ${posts.id}
        )`.as('commentCount'),
        shareCount: sql<number>`(
          SELECT COUNT(*) FROM ${posts} as shared_posts
          WHERE shared_posts.sharedPostId = ${posts.id}
        )`.as('shareCount'),
        liked: sql<boolean>`(
          SELECT COUNT(*) > 0 FROM ${likes}
          WHERE ${likes.postId} = ${posts.id}
          AND ${likes.userId} = ${session.user.id}
          AND ${likes.type} = 'like'
        )`.as('liked'),
        disliked: sql<boolean>`(
          SELECT COUNT(*) > 0 FROM ${likes}
          WHERE ${likes.postId} = ${posts.id}
          AND ${likes.userId} = ${session.user.id}
          AND ${likes.type} = 'dislike'
        )`.as('disliked'),
      })
      .from(savedPosts)
      .innerJoin(posts, eq(savedPosts.postId, posts.id))
      .innerJoin(users, eq(posts.userId, users.id))
      .where(eq(savedPosts.userId, session.user.id))
      .orderBy(desc(savedPosts.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(savedPosts)
      .innerJoin(posts, eq(savedPosts.postId, posts.id))
      .where(eq(savedPosts.userId, session.user.id));

    const totalCount = totalCountResult[0]?.count || 0;
    const hasMore = offset + savedPostsData.length < totalCount;

    // Format the response to match the expected Post type
    const formattedPosts = savedPostsData.map((item) => ({
      id: item.post.id,
      content: item.post.content,
      images: item.post.images,
      videos: item.post.videos,
      privacy: item.post.privacy,
      backgroundColor: item.post.backgroundColor,
      feeling: item.post.feeling,
      activity: item.post.activity,
      location: item.post.location,
      formattedContent: item.post.formattedContent,
      createdAt: item.post.createdAt.toISOString(),
      updatedAt: item.post.updatedAt?.toISOString() || null,
      userId: item.post.userId,
      groupId: item.post.groupId,
      sharedPostId: item.post.sharedPostId,
      user: item.user,
      _count: {
        likes: item.likeCount,
        dislikes: item.dislikeCount,
        comments: item.commentCount,
        shares: item.shareCount,
      },
      liked: item.liked,
      disliked: item.disliked,
      savedAt: item.savedPost.createdAt.toISOString(),
    }));

    return NextResponse.json({
      posts: formattedPosts,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore,
      },
    });
  } catch (error) {
    console.error("Error fetching saved posts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
