import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, users, fanPageFollowers, fanPagePosts } from "@/lib/db/schema";
import { eq, like, desc, count, and, gte, sql } from "drizzle-orm";

// GET /api/admin/fan-pages - Get all fan pages for admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0 || !user[0].isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "all";
    const status = searchParams.get("status") || "all";
    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = [];

    if (search) {
      whereConditions.push(
        sql`(${fanPages.name} LIKE ${`%${search}%`} OR ${fanPages.username} LIKE ${`%${search}%`})`
      );
    }

    if (category !== "all") {
      // Validate category is one of the allowed enum values
      const validCategories = [
        'musician', 'actor', 'brand', 'business', 'organization',
        'public_figure', 'artist', 'writer', 'athlete', 'politician',
        'entertainment', 'media', 'community', 'cause', 'other'
      ] as const;

      if (validCategories.includes(category as any)) {
        whereConditions.push(eq(fanPages.category, category as typeof validCategories[number]));
      }
    }

    if (status === "active") {
      whereConditions.push(eq(fanPages.isActive, true));
    } else if (status === "inactive") {
      whereConditions.push(eq(fanPages.isActive, false));
    } else if (status === "verified") {
      whereConditions.push(eq(fanPages.isVerified, true));
    } else if (status === "unverified") {
      whereConditions.push(eq(fanPages.isVerified, false));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get fan pages with owner info
    const pagesResult = await db
      .select({
        id: fanPages.id,
        name: fanPages.name,
        username: fanPages.username,
        category: fanPages.category,
        description: fanPages.description,
        profileImage: fanPages.profileImage,
        coverImage: fanPages.coverImage,
        isVerified: fanPages.isVerified,
        isActive: fanPages.isActive,
        followerCount: fanPages.followerCount,
        postCount: fanPages.postCount,
        createdAt: fanPages.createdAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPages)
      .leftJoin(users, eq(fanPages.ownerId, users.id))
      .where(whereClause)
      .orderBy(desc(fanPages.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      fanPages: pagesResult,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching admin fan pages:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
