import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogMonetization } from "@/lib/db/schema";
import { eq, desc, and, count, sum } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    const authorId = session.user.id;

    // Get all blogs with monetization data
    const userBlogs = await db.query.blogs.findMany({
      where: eq(blogs.authorId, authorId),
      orderBy: [desc(blogs.createdAt)],
      limit,
      offset,
      with: {
        monetization: true,
      },
    });

    // Get total count
    const totalCountResult = await db
      .select({ count: count() })
      .from(blogs)
      .where(eq(blogs.authorId, authorId));

    const totalCount = totalCountResult[0]?.count || 0;

    // Format blogs with monetization status
    const formattedBlogs = userBlogs.map(blog => {
      let monetizationStatus = 'not_requested';
      let totalEarnings = "0.00";

      if (blog.monetization) {
        if (blog.monetization.isApproved) {
          monetizationStatus = 'approved';
        } else if (blog.monetization.rejectedAt) {
          monetizationStatus = 'rejected';
        } else {
          monetizationStatus = 'pending';
        }
        totalEarnings = blog.monetization.totalEarnings || "0.00";
      }

      return {
        id: blog.id,
        title: blog.title,
        slug: blog.slug,
        status: blog.status,
        viewCount: blog.viewCount || 0,
        createdAt: blog.createdAt,
        updatedAt: blog.updatedAt,
        monetization: {
          status: monetizationStatus,
          totalEarnings: totalEarnings,
          isEnabled: blog.monetization?.isEnabled || false,
          cprRate: blog.monetization?.cprRate || "0.00",
          totalReads: blog.monetization?.totalReads || 0,
          uniqueReads: blog.monetization?.uniqueReads || 0,
          rejectionReason: blog.monetization?.rejectionReason || null,
        },
      };
    });

    // Get summary stats
    const totalBlogs = totalCount;
    const publishedBlogs = userBlogs.filter(blog => blog.status === 'published').length;
    const draftBlogs = userBlogs.filter(blog => blog.status === 'draft').length;
    const monetizedBlogs = userBlogs.filter(blog => 
      blog.monetization?.isApproved === true
    ).length;
    const pendingMonetization = userBlogs.filter(blog => 
      blog.monetization && !blog.monetization.isApproved && !blog.monetization.rejectedAt
    ).length;
    const totalViews = userBlogs.reduce((sum, blog) => sum + (blog.viewCount || 0), 0);

    // Calculate total earnings
    const totalEarningsResult = await db
      .select({ total: sum(blogMonetization.totalEarnings) })
      .from(blogMonetization)
      .innerJoin(blogs, eq(blogs.id, blogMonetization.blogId))
      .where(
        and(
          eq(blogs.authorId, authorId),
          eq(blogMonetization.isApproved, true)
        )
      );

    const totalEarnings = totalEarningsResult[0]?.total || "0.00";

    return NextResponse.json({
      success: true,
      data: {
        blogs: formattedBlogs,
        stats: {
          totalBlogs,
          publishedBlogs,
          draftBlogs,
          totalViews,
          monetizedBlogs,
          pendingMonetization,
          totalEarnings: totalEarnings.toString(),
        },
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount,
        },
      },
    });

  } catch (error) {
    console.error("Error fetching blogs monetization data:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch blogs monetization data" 
      },
      { status: 500 }
    );
  }
}
