"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON>bsContent } from "@/components/ui/Tabs";
import { toast } from "react-hot-toast";
import Image from "next/image";
import Link from "next/link";
import { formatDate } from "@/lib/utils";
import { EditEventModal } from "@/components/admin/events/EditEventModal";
import { DeleteEventModal } from "@/components/admin/events/DeleteEventModal";
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  CalendarIcon,
  MapPinIcon,
  GlobeAltIcon,
  UserGroupIcon,
  LockClosedIcon,
  ClockIcon,
  CheckIcon,
  XMarkIcon,
  UserIcon,
  UsersIcon,
  ChatBubbleLeftIcon,
} from "@heroicons/react/24/outline";

interface Event {
  id: string;
  name: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  isOnline: boolean;
  onlineLink: string | null;
  visibility: "public" | "private" | "friends";
  category: string | null;
  coverImage: string | null;
  hostId: string;
  createdAt: string;
  updatedAt: string;
  host?: {
    id: string;
    name: string;
    username: string | null;
    image: string | null;
  };
  attendeeCounts?: {
    going: number;
    interested: number;
    not_going: number;
  };
}

interface Attendee {
  id: string;
  status: "going" | "interested" | "not_going";
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    username: string | null;
    image: string | null;
  };
}

export default function AdminEventDetailPage() {
  const router = useRouter();
  const params = useParams();
  const eventId = params?.eventId as string;

  const [isLoading, setIsLoading] = useState(true);
  const [event, setEvent] = useState<Event | null>(null);
  const [attendees, setAttendees] = useState<Attendee[]>([]);
  const [activeTab, setActiveTab] = useState("details");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAttendeeLoading, setIsAttendeeLoading] = useState(false);
  const [attendeeFilter, setAttendeeFilter] = useState<"all" | "going" | "interested" | "not_going">("all");

  useEffect(() => {
    fetchEvent();
  }, [eventId]);

  useEffect(() => {
    if (event) {
      fetchAttendees();
    }
  }, [event, attendeeFilter]);

  const fetchEvent = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/events/${eventId}`);

      if (!response.ok) {
        // Try to get more detailed error information
        let errorMessage = "Failed to fetch event";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      if (!data || !data.id) {
        throw new Error("Invalid event data received");
      }
      setEvent(data);
    } catch (error) {
      console.error("Error fetching event:", error);
      toast.error(error instanceof Error ? error.message : "Failed to load event");

      // Fallback to regular events API if admin API fails
      try {
        const fallbackResponse = await fetch(`/api/events/${eventId}`);
        if (fallbackResponse.ok) {
          const data = await fallbackResponse.json();
          if (data && data.id) {
            setEvent(data);
          } else {
            router.push('/admin/events');
          }
        } else {
          router.push('/admin/events');
        }
      } catch (fallbackError) {
        console.error("Fallback error:", fallbackError);
        router.push('/admin/events');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAttendees = async () => {
    setIsAttendeeLoading(true);
    try {
      const url = attendeeFilter === "all"
        ? `/api/admin/events/${eventId}/attendees`
        : `/api/admin/events/${eventId}/attendees?status=${attendeeFilter}`;

      const response = await fetch(url);

      if (!response.ok) {
        // Try to get more detailed error information
        let errorMessage = "Failed to fetch attendees";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      setAttendees(data.attendees || []);
    } catch (error) {
      console.error("Error fetching attendees:", error);
      toast.error(error instanceof Error ? error.message : "Failed to load attendees");
      setAttendees([]);
    } finally {
      setIsAttendeeLoading(false);
    }
  };

  const handleEditSuccess = () => {
    toast.success("Event updated successfully");
    fetchEvent();
    setIsEditModalOpen(false);
  };

  const handleDeleteSuccess = () => {
    toast.success("Event deleted successfully");
    router.push("/admin/events");
  };

  const handleRemoveAttendee = async (attendeeId: string) => {
    try {
      const attendee = attendees.find(a => a.id === attendeeId);
      if (!attendee) return;

      const response = await fetch(`/api/admin/events/${eventId}/attendees`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: attendee.user.id,
          action: "remove",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to remove attendee");
      }

      toast.success("Attendee removed successfully");
      fetchAttendees();
      fetchEvent(); // Refresh event to update attendee counts
    } catch (error) {
      console.error("Error removing attendee:", error);
      toast.error("Failed to remove attendee");
    }
  };

  const handleUpdateAttendeeStatus = async (attendeeId: string, status: "going" | "interested" | "not_going") => {
    try {
      const attendee = attendees.find(a => a.id === attendeeId);
      if (!attendee) return;

      const response = await fetch(`/api/admin/events/${eventId}/attendees`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: attendee.user.id,
          action: "update",
          status,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update attendee status");
      }

      toast.success("Attendee status updated successfully");
      fetchAttendees();
      fetchEvent(); // Refresh event to update attendee counts
    } catch (error) {
      console.error("Error updating attendee status:", error);
      toast.error("Failed to update attendee status");
    }
  };

  const getEventStatusBadge = (event: Event) => {
    const now = new Date();
    const startTime = new Date(event.startTime);
    const endTime = new Date(event.endTime);

    if (startTime > now) {
      return (
        <Badge variant="primary" className="flex items-center">
          <ClockIcon className="mr-1 h-3 w-3" />
          Upcoming
        </Badge>
      );
    } else if (startTime <= now && endTime >= now) {
      return (
        <Badge variant="success" className="flex items-center">
          <CheckIcon className="mr-1 h-3 w-3" />
          Ongoing
        </Badge>
      );
    } else {
      return (
        <Badge variant="default" className="flex items-center">
          <XMarkIcon className="mr-1 h-3 w-3" />
          Past
        </Badge>
      );
    }
  };

  const getVisibilityBadge = (visibility: string) => {
    switch (visibility) {
      case "public":
        return (
          <Badge variant="success" className="flex items-center">
            <GlobeAltIcon className="mr-1 h-3 w-3" />
            Public
          </Badge>
        );
      case "private":
        return (
          <Badge variant="danger" className="flex items-center">
            <LockClosedIcon className="mr-1 h-3 w-3" />
            Private
          </Badge>
        );
      case "friends":
        return (
          <Badge variant="warning" className="flex items-center">
            <UserGroupIcon className="mr-1 h-3 w-3" />
            Friends
          </Badge>
        );
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "going":
        return (
          <Badge variant="success" className="flex items-center">
            <CheckIcon className="mr-1 h-3 w-3" />
            Going
          </Badge>
        );
      case "interested":
        return (
          <Badge variant="warning" className="flex items-center">
            <ClockIcon className="mr-1 h-3 w-3" />
            Interested
          </Badge>
        );
      case "not_going":
        return (
          <Badge variant="danger" className="flex items-center">
            <XMarkIcon className="mr-1 h-3 w-3" />
            Not Going
          </Badge>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!event) {
    return (
      <AdminLayout>
        <div className="flex h-64 flex-col items-center justify-center">
          <h2 className="text-xl font-semibold text-gray-700">Event not found</h2>
          <p className="mt-2 text-gray-500">The event you're looking for doesn't exist or has been deleted.</p>
          <Button
            onClick={() => router.push("/admin/events")}
            className="mt-4"
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Events
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            onClick={() => router.push("/admin/events")}
            variant="outline"
            className="mr-4"
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Event Details</h1>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => setIsEditModalOpen(true)}
            variant="outline"
          >
            <PencilIcon className="mr-2 h-5 w-5" />
            Edit
          </Button>
          <Button
            onClick={() => setIsDeleteModalOpen(true)}
            variant="danger"
          >
            <TrashIcon className="mr-2 h-5 w-5" />
            Delete
          </Button>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white shadow">
        {/* Event header */}
        <div className="relative h-48 w-full bg-gray-200">
          {event.coverImage ? (
            <Image
              src={event.coverImage}
              alt={event.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-blue-100">
              <CalendarIcon className="h-16 w-16 text-blue-500" />
            </div>
          )}
        </div>

        <div className="p-6">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{event.name}</h2>
              <div className="mt-2 flex flex-wrap gap-2">
                {getEventStatusBadge(event)}
                {getVisibilityBadge(event.visibility)}
                {event.category && (
                  <Badge variant="outline">{event.category}</Badge>
                )}
              </div>
            </div>
            <div className="mt-4 flex flex-col items-start md:items-end md:mt-0">
              <div className="flex items-center text-gray-700">
                <CalendarIcon className="mr-2 h-5 w-5" />
                <span>
                  {new Date(event.startTime).toLocaleDateString('en-US', { dateStyle: 'full' })}
                </span>
              </div>
              <div className="mt-1 flex items-center text-gray-700">
                <ClockIcon className="mr-2 h-5 w-5" />
                <span>
                  {new Date(event.startTime).toLocaleTimeString('en-US', { timeStyle: 'short' })} - {new Date(event.endTime).toLocaleTimeString('en-US', { timeStyle: 'short' })}
                </span>
              </div>
              <div className="mt-1 flex items-center text-gray-700">
                {event.isOnline ? (
                  <>
                    <GlobeAltIcon className="mr-2 h-5 w-5" />
                    <span>Online Event</span>
                  </>
                ) : event.location ? (
                  <>
                    <MapPinIcon className="mr-2 h-5 w-5" />
                    <span>{event.location}</span>
                  </>
                ) : (
                  <>
                    <MapPinIcon className="mr-2 h-5 w-5" />
                    <span className="text-gray-400">No location specified</span>
                  </>
                )}
              </div>
              {event.host && (
                <div className="mt-1 flex items-center text-gray-700">
                  <UserIcon className="mr-2 h-5 w-5" />
                  <span>
                    Hosted by{" "}
                    <Link
                      href={`/admin/users/${event.host.id}`}
                      className="text-blue-600 hover:underline"
                    >
                      {event.host.name}
                    </Link>
                  </span>
                </div>
              )}
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
            <TabsList className="mb-6">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="attendees">
                Attendees ({(event.attendeeCounts?.going || 0) + (event.attendeeCounts?.interested || 0)})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Description</h3>
                <div className="mt-2 whitespace-pre-wrap text-gray-700">
                  {event.description || <span className="text-gray-400">No description provided</span>}
                </div>
              </div>

              {event.isOnline && event.onlineLink && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Online Link</h3>
                  <div className="mt-2">
                    <a
                      href={event.onlineLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {event.onlineLink}
                    </a>
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-lg font-medium text-gray-900">Attendance</h3>
                <div className="mt-2 grid grid-cols-3 gap-4">
                  <div className="rounded-lg border border-gray-200 p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {event.attendeeCounts?.going || 0}
                    </div>
                    <div className="text-sm text-gray-500">Going</div>
                  </div>
                  <div className="rounded-lg border border-gray-200 p-4 text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {event.attendeeCounts?.interested || 0}
                    </div>
                    <div className="text-sm text-gray-500">Interested</div>
                  </div>
                  <div className="rounded-lg border border-gray-200 p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {event.attendeeCounts?.not_going || 0}
                    </div>
                    <div className="text-sm text-gray-500">Not Going</div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900">Event Information</h3>
                <div className="mt-2 overflow-hidden rounded-lg border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="divide-y divide-gray-200">
                      <tr>
                        <td className="bg-gray-50 px-4 py-2 text-sm font-medium text-gray-500">
                          Event ID
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900">
                          {event.id}
                        </td>
                      </tr>
                      <tr>
                        <td className="bg-gray-50 px-4 py-2 text-sm font-medium text-gray-500">
                          Created At
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900">
                          {new Date(event.createdAt).toLocaleDateString('en-US', { dateStyle: 'medium' })} {new Date(event.createdAt).toLocaleTimeString('en-US', { timeStyle: 'short' })}
                        </td>
                      </tr>
                      <tr>
                        <td className="bg-gray-50 px-4 py-2 text-sm font-medium text-gray-500">
                          Last Updated
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900">
                          {new Date(event.updatedAt).toLocaleDateString('en-US', { dateStyle: 'medium' })} {new Date(event.updatedAt).toLocaleTimeString('en-US', { timeStyle: 'short' })}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="attendees">
              <div className="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Attendees</h3>
                  <p className="text-sm text-gray-500">
                    Manage people attending this event
                  </p>
                </div>
                <div className="mt-2 sm:mt-0">
                  <select
                    value={attendeeFilter}
                    onChange={(e) => setAttendeeFilter(e.target.value as any)}
                    className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="all">All Attendees</option>
                    <option value="going">Going</option>
                    <option value="interested">Interested</option>
                    <option value="not_going">Not Going</option>
                  </select>
                </div>
              </div>

              {isAttendeeLoading ? (
                <div className="flex h-32 items-center justify-center">
                  <Spinner size="md" />
                </div>
              ) : attendees.length === 0 ? (
                <div className="flex h-32 flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-4 text-center">
                  <UsersIcon className="h-8 w-8 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500">
                    No attendees found for this event
                  </p>
                </div>
              ) : (
                <div className="overflow-hidden rounded-lg border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          User
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Status
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Joined
                        </th>
                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {attendees.map((attendee) => (
                        <tr key={attendee.id} className="hover:bg-gray-50">
                          <td className="whitespace-nowrap px-4 py-4">
                            <div className="flex items-center">
                              <div className="h-10 w-10 flex-shrink-0">
                                {attendee.user.image ? (
                                  <Image
                                    src={attendee.user.image}
                                    alt={attendee.user.name}
                                    width={40}
                                    height={40}
                                    className="h-10 w-10 rounded-full object-cover"
                                  />
                                ) : (
                                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                                    <UserIcon className="h-6 w-6" />
                                  </div>
                                )}
                              </div>
                              <div className="ml-4">
                                <div className="font-medium text-gray-900">
                                  {attendee.user.name}
                                </div>
                                {attendee.user.username && (
                                  <div className="text-xs text-gray-500">
                                    @{attendee.user.username}
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-4 py-4">
                            {getStatusBadge(attendee.status)}
                          </td>
                          <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500">
                            {new Date(attendee.createdAt).toLocaleDateString('en-US', { dateStyle: 'medium' })}
                          </td>
                          <td className="whitespace-nowrap px-4 py-4 text-right text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <select
                                value={attendee.status}
                                onChange={(e) => handleUpdateAttendeeStatus(attendee.id, e.target.value as any)}
                                className="rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              >
                                <option value="going">Going</option>
                                <option value="interested">Interested</option>
                                <option value="not_going">Not Going</option>
                              </select>
                              <Button
                                onClick={() => handleRemoveAttendee(attendee.id)}
                                variant="danger"
                                size="sm"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Modals */}
      {event && (
        <>
          <EditEventModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSuccess={handleEditSuccess}
            event={event}
          />

          <DeleteEventModal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            onSuccess={handleDeleteSuccess}
            event={event}
          />
        </>
      )}
    </AdminLayout>
  );
}
