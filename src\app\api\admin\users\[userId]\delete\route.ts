import { NextResponse } from "next/server";
import { validateApiSession } from "@/lib/utils/session-validation";
import { db } from "@/lib/db";
import {
  users,
  posts,
  comments,
  likes,
  subscriptions,
  messages,
  accounts,
  sessions,
  wallets,
  walletTransactions,
  savedPosts,
  notifications
} from "@/lib/db/schema";
import { eq, or } from "drizzle-orm";
import { z } from "zod";

const deleteSchema = z.object({
  reason: z.string().min(1, "Deletion reason is required"),
});

// Delete user account (Admin only)
export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    // Validate session with admin requirement
    const { response: authError, user: currentUser } = await validateApiSession(true);
    if (authError) return authError;

    const { userId } = await context.params;
    const body = await req.json();

    // Validate request body
    const validatedData = deleteSchema.parse(body);

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Prevent deleting admin users
    if (user.isAdmin) {
      return NextResponse.json(
        { message: "Cannot delete admin users" },
        { status: 400 }
      );
    }

    // Prevent self-deletion
    if (userId === currentUser.id) {
      return NextResponse.json(
        { message: "Cannot delete your own account" },
        { status: 400 }
      );
    }

    // PERMANENT DELETION - Delete all related data first, then the user
    console.log(`Starting permanent deletion of user ${userId}`);

    try {
      // Step 1: Delete user's sessions first (to log them out immediately)
      console.log('Deleting user sessions...');
      await db.delete(sessions).where(eq(sessions.userId, userId));

      // Step 2: Delete user's OAuth accounts
      console.log('Deleting user accounts...');
      await db.delete(accounts).where(eq(accounts.userId, userId));

      // Step 3: Delete user's posts and related data
      console.log('Deleting user posts and related data...');
      const userPosts = await db.query.posts.findMany({
        where: eq(posts.userId, userId),
        columns: { id: true }
      });

      const postIds = userPosts.map(post => post.id);
      console.log(`Found ${postIds.length} posts to delete`);

      if (postIds.length > 0) {
        // Delete comments on user's posts
        console.log('Deleting comments on user posts...');
        await db.delete(comments).where(
          or(...postIds.map(postId => eq(comments.postId, postId)))
        );

        // Delete likes on user's posts
        console.log('Deleting likes on user posts...');
        await db.delete(likes).where(
          or(...postIds.map(postId => eq(likes.postId, postId)))
        );

        // Delete saved posts references
        console.log('Deleting saved post references...');
        await db.delete(savedPosts).where(
          or(...postIds.map(postId => eq(savedPosts.postId, postId)))
        );
      }

      // Step 4: Delete user's own posts
      console.log('Deleting user posts...');
      await db.delete(posts).where(eq(posts.userId, userId));

      // Step 5: Delete user's comments on other posts
      console.log('Deleting user comments...');
      await db.delete(comments).where(eq(comments.userId, userId));

      // Step 6: Delete user's likes
      console.log('Deleting user likes...');
      await db.delete(likes).where(eq(likes.userId, userId));

      // Step 7: Delete user's subscriptions
      console.log('Deleting user subscriptions...');
      await db.delete(subscriptions).where(
        or(
          eq(subscriptions.subscriberId, userId),
          eq(subscriptions.targetUserId, userId)
        )
      );

      // Step 8: Delete user's messages
      console.log('Deleting user messages...');
      await db.delete(messages).where(
        or(
          eq(messages.senderId, userId),
          eq(messages.receiverId, userId)
        )
      );

      // Step 9: Delete user's wallet transactions
      console.log('Deleting wallet transactions...');
      await db.delete(walletTransactions).where(
        or(
          eq(walletTransactions.userId, userId),
          eq(walletTransactions.toUserId, userId)
        )
      );

      // Step 10: Delete user's wallet
      console.log('Deleting user wallet...');
      await db.delete(wallets).where(eq(wallets.userId, userId));

      // Step 11: Delete user's saved posts
      console.log('Deleting saved posts...');
      await db.delete(savedPosts).where(eq(savedPosts.userId, userId));

      // Step 12: Delete user's notifications (sent and received)
      console.log('Deleting notifications...');
      await db.delete(notifications).where(
        or(
          eq(notifications.recipientId, userId),
          eq(notifications.senderId, userId)
        )
      );

      // Step 13: Finally, delete the user account permanently
      console.log('Deleting user account...');
      await db.delete(users).where(eq(users.id, userId));

      console.log('✅ User deletion completed successfully');

    } catch (deletionError) {
      console.error('❌ Error during deletion process:', deletionError);
      console.error('Error details:', {
        message: deletionError.message,
        stack: deletionError.stack,
        name: deletionError.name
      });
      throw deletionError;
    }

    // Log the action
    console.log(`Admin ${currentUser.id} PERMANENTLY deleted user ${userId} for: ${validatedData.reason}`);

    return NextResponse.json({
      success: true,
      message: "User account permanently deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting user account:", error);
    console.error("Error stack:", error.stack);
    console.error("Error message:", error.message);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid request data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: `Failed to delete user account: ${error.message}`,
        error: error.toString()
      },
      { status: 500 }
    );
  }
}
