import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { messages, users, notifications, fanPageMessages, fanPages } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { or, and, eq, desc, asc, sql } from "drizzle-orm";
import { emitMessage, createMessagePayload } from "@/lib/partykit/server-utils";
import { emitNotification } from "@/lib/utils/notifications";

const messageSchema = z.object({
  receiverId: z.string(),
  content: z.string().min(1).max(5000),
});

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (userId) {
      // Check if this is a fan page conversation
      if (userId.startsWith('fanpage_')) {
        const fanPageId = userId.replace('fanpage_', '');

        // Check if user is the owner of this fan page
        const pageOwnerCheck = await db.query.fanPages.findFirst({
          where: and(
            eq(fanPages.id, fanPageId),
            eq(fanPages.ownerId, session.user.id)
          ),
        });

        // If user is the page owner, don't show in personal messages
        if (pageOwnerCheck) {
          return NextResponse.json({
            error: "Page owner messages should be accessed through Fan Page Messages section"
          }, { status: 403 });
        }

        // Get fan page messages for this conversation (only if user is NOT the page owner)
        const fanPageConversation = await db
          .select({
            id: fanPageMessages.id,
            senderId: fanPageMessages.senderId,
            receiverId: sql<string>`${fanPageMessages.fanPageId}`,
            content: fanPageMessages.content,
            read: fanPageMessages.read,
            createdAt: fanPageMessages.createdAt,
            isFromPage: fanPageMessages.isFromPage,
            sender: {
              id: users.id,
              name: users.name,
              image: users.image,
            },
            receiver: {
              id: fanPages.id,
              name: fanPages.name,
              image: fanPages.profileImage,
            },
          })
          .from(fanPageMessages)
          .leftJoin(users, eq(fanPageMessages.senderId, users.id))
          .leftJoin(fanPages, eq(fanPageMessages.fanPageId, fanPages.id))
          .where(
            and(
              eq(fanPageMessages.fanPageId, fanPageId),
              or(
                // Messages sent by the user to this fan page
                and(
                  eq(fanPageMessages.senderId, session.user.id),
                  eq(fanPageMessages.isFromPage, false)
                ),
                // Messages sent by this fan page to the user
                and(
                  eq(fanPageMessages.isFromPage, true),
                  sql`EXISTS (
                    SELECT 1 FROM fan_page_messages fpm2
                    WHERE fpm2.fanPageId = ${fanPageId}
                    AND fpm2.senderId = ${session.user.id}
                  )`
                )
              )
            )
          )
          .orderBy(asc(fanPageMessages.createdAt));

        return NextResponse.json(fanPageConversation);
      } else {
        // Get conversation with specific user
        const conversation = await db.query.messages.findMany({
          where: or(
            and(
              eq(messages.senderId, session.user.id),
              eq(messages.receiverId, userId)
            ),
            and(
              eq(messages.senderId, userId),
              eq(messages.receiverId, session.user.id)
            )
          ),
          orderBy: [asc(messages.createdAt)],
          with: {
            sender: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
            receiver: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        });

        return NextResponse.json(conversation);
      }
    } else {
      // Get all conversations including fan page messages
      const userMessages = await db.query.messages.findMany({
        where: or(
          eq(messages.senderId, session.user.id),
          eq(messages.receiverId, session.user.id)
        ),
        orderBy: [desc(messages.createdAt)],
        with: {
          sender: {
            columns: {
              id: true,
              name: true,
              image: true,
            },
          },
          receiver: {
            columns: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });

      // Get fan page messages where user is involved (but NOT as page owner)
      const fanPageMessagesData = await db
        .select({
          id: fanPageMessages.id,
          senderId: fanPageMessages.senderId,
          receiverId: sql<string>`${fanPageMessages.fanPageId}`, // Use fanPageId as receiverId for consistency
          content: fanPageMessages.content,
          read: fanPageMessages.read,
          createdAt: fanPageMessages.createdAt,
          isFromPage: fanPageMessages.isFromPage,
          fanPageId: fanPageMessages.fanPageId,
          sender: {
            id: users.id,
            name: users.name,
            image: users.image,
          },
          fanPage: {
            id: fanPages.id,
            name: fanPages.name,
            profileImage: fanPages.profileImage,
            ownerId: fanPages.ownerId,
          },
        })
        .from(fanPageMessages)
        .leftJoin(users, eq(fanPageMessages.senderId, users.id))
        .leftJoin(fanPages, eq(fanPageMessages.fanPageId, fanPages.id))
        .where(
          and(
            // Exclude messages from pages owned by current user
            sql`${fanPages.ownerId} != ${session.user.id}`,
            or(
              // Messages sent by the user to fan pages (that they don't own)
              and(
                eq(fanPageMessages.senderId, session.user.id),
                eq(fanPageMessages.isFromPage, false)
              ),
              // Messages sent by fan pages to the user (replies from pages they don't own)
              and(
                eq(fanPageMessages.isFromPage, true),
                // Check if user has sent messages to this page (has conversation)
                sql`EXISTS (
                  SELECT 1 FROM fan_page_messages fpm2
                  WHERE fpm2.fanPageId = ${fanPageMessages.fanPageId}
                  AND fpm2.senderId = ${session.user.id}
                )`
              )
            )
          )
        )
        .orderBy(desc(fanPageMessages.createdAt));

      // Group messages by conversation
      const conversations = new Map();

      // Process regular user messages with better deduplication
      userMessages.forEach((message) => {
        const otherUser =
          message.senderId === session.user.id
            ? message.receiver
            : message.sender;

        // Skip if user data is incomplete
        if (!otherUser || !otherUser.id) {
          console.warn('Skipping message with incomplete user data:', message.id);
          return;
        }

        if (!conversations.has(otherUser.id)) {
          conversations.set(otherUser.id, {
            user: otherUser,
            lastMessage: message,
            unreadCount: 0,
            type: 'user', // Mark as user conversation
          });
        } else {
          // Update with the latest message (since messages are ordered by desc)
          const existing = conversations.get(otherUser.id);
          if (new Date(message.createdAt) > new Date(existing.lastMessage.createdAt)) {
            existing.lastMessage = message;
          }
        }
      });

      // Process fan page messages with better validation
      const fanPageConversations = new Map();
      fanPageMessagesData.forEach((message) => {
        const fanPageId = message.fanPageId;

        // Skip if fan page data is incomplete
        if (!fanPageId || !message.fanPage) {
          console.warn('Skipping fan page message with incomplete data:', message.id);
          return;
        }

        if (!fanPageConversations.has(fanPageId)) {
          fanPageConversations.set(fanPageId, {
            user: {
              id: fanPageId,
              name: message.fanPage.name || 'Unknown Page',
              image: message.fanPage.profileImage || null,
            },
            lastMessage: {
              id: message.id,
              senderId: message.senderId,
              receiverId: fanPageId,
              content: message.content,
              read: message.read,
              createdAt: message.createdAt,
              sender: message.sender || {
                id: message.senderId,
                name: 'Unknown User',
                image: null,
              },
              receiver: {
                id: fanPageId,
                name: message.fanPage.name || 'Unknown Page',
                image: message.fanPage.profileImage || null,
              },
            },
            unreadCount: 0,
            type: 'fanpage', // Mark as fan page conversation
            fanPageId: fanPageId,
          });
        } else {
          // Update with the latest message
          const existing = fanPageConversations.get(fanPageId);
          if (new Date(message.createdAt) > new Date(existing.lastMessage.createdAt)) {
            existing.lastMessage = {
              id: message.id,
              senderId: message.senderId,
              receiverId: fanPageId,
              content: message.content,
              read: message.read,
              createdAt: message.createdAt,
              sender: message.sender || {
                id: message.senderId,
                name: 'Unknown User',
                image: null,
              },
              receiver: {
                id: fanPageId,
                name: message.fanPage.name || 'Unknown Page',
                image: message.fanPage.profileImage || null,
              },
            };
          }
        }
      });

      // Merge fan page conversations with regular conversations
      fanPageConversations.forEach((conversation, fanPageId) => {
        conversations.set(`fanpage_${fanPageId}`, conversation);
      });

      // Count unread messages for each conversation with better error handling
      const conversationEntries = Array.from(conversations.entries());

      await Promise.all(conversationEntries.map(async ([conversationId, conversation]) => {
        try {
          if (conversation.type === 'user') {
            // Regular user conversation - count unread messages from the other user
            const unreadMessages = await db
              .select({ count: sql<number>`count(*)` })
              .from(messages)
              .where(
                and(
                  eq(messages.senderId, conversation.user.id),
                  eq(messages.receiverId, session.user.id),
                  eq(messages.read, false)
                )
              );
            conversation.unreadCount = unreadMessages[0]?.count || 0;
          } else if (conversation.type === 'fanpage') {
            // Fan page conversation - count unread messages from the page
            const unreadFanPageMessages = await db
              .select({ count: sql<number>`count(*)` })
              .from(fanPageMessages)
              .where(
                and(
                  eq(fanPageMessages.fanPageId, conversation.fanPageId),
                  eq(fanPageMessages.isFromPage, true), // Messages from page to user
                  eq(fanPageMessages.read, false),
                  // Only count if user has conversation with this page
                  sql`EXISTS (
                    SELECT 1 FROM fan_page_messages fpm2
                    WHERE fpm2.fanPageId = ${conversation.fanPageId}
                    AND fpm2.senderId = ${session.user.id}
                  )`
                )
              );
            conversation.unreadCount = unreadFanPageMessages[0]?.count || 0;
          }
        } catch (error) {
          console.error(`Error counting unread messages for conversation ${conversationId}:`, error);
          conversation.unreadCount = 0; // Default to 0 on error
        }
      }));

      // Sort conversations by last message time and return
      const sortedConversations = Array.from(conversations.values())
        .sort((a, b) => {
          const timeA = new Date(a.lastMessage.createdAt).getTime();
          const timeB = new Date(b.lastMessage.createdAt).getTime();
          return timeB - timeA; // Most recent first
        });

      console.log(`Returning ${sortedConversations.length} conversations`);
      return NextResponse.json(sortedConversations);
    }
  } catch (error) {
    console.error("Error fetching messages:", error);

    // Provide more detailed error information
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string };

        if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
          console.error("Collation mismatch error:", dbError);
        } else if (dbError.sqlMessage) {
          // Log the SQL error but don't expose it to the client
          console.error("SQL Error:", dbError.sqlMessage);
          errorMessage = "Database query failed";
        }
      }
    }

    return NextResponse.json(
      {
        message: errorMessage,
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: statusCode }
    );
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required",
          message: "Please log in to send messages"
        },
        { status: 401 }
      );
    }

    const body = await req.json();

    // Validate input
    const validation = messageSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid input",
          message: "Please check your message content",
          details: validation.error.errors
        },
        { status: 400 }
      );
    }

    const { receiverId, content } = validation.data;

    // Prevent sending message to self
    if (receiverId === session.user.id) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid recipient",
          message: "Cannot send message to yourself"
        },
        { status: 400 }
      );
    }

    // Check if receiver exists
    const receiver = await db.query.users.findFirst({
      where: eq(users.id, receiverId),
      columns: { id: true, name: true, isActive: true }
    });

    if (!receiver) {
      return NextResponse.json(
        {
          success: false,
          error: "Recipient not found",
          message: "The user you're trying to message doesn't exist"
        },
        { status: 404 }
      );
    }

    if (!receiver.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: "Recipient unavailable",
          message: "This user is currently unavailable"
        },
        { status: 403 }
      );
    }

    const messageId = uuidv4();

    // Insert the message into the database
    await db.insert(messages).values({
      id: messageId,
      senderId: session.user.id,
      receiverId,
      content: content.trim(),
    });

    // Create a notification for the recipient
    await db.insert(notifications).values({
      id: uuidv4(),
      recipientId: receiverId,
      type: "message",
      senderId: session.user.id,
      messageId,
    });

    // Fetch the created message with sender info
    const createdMessage = await db.query.messages.findFirst({
      where: eq(messages.id, messageId),
      with: {
        sender: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
        receiver: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    // Emit real-time message update
    if (createdMessage) {
      try {
        const messagePayload = createMessagePayload(createdMessage, "direct");
        await emitMessage(messagePayload);
      } catch (error) {
        console.error("Failed to emit real-time message:", error);
      }
    }

    return NextResponse.json(
      {
        success: true,
        message: "Message sent successfully",
        data: createdMessage,
        timestamp: new Date().toISOString()
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error sending message:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Validation error",
          message: "Invalid input data",
          details: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Server error",
        message: "Something went wrong. Please try again.",
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Mark messages as read
export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { senderId } = z.object({ senderId: z.string() }).parse(body);

    // Mark all messages from senderId to current user as read
    await db.update(messages)
      .set({ read: true })
      .where(
        and(
          eq(messages.senderId, senderId),
          eq(messages.receiverId, session.user.id),
          eq(messages.read, false)
        )
      );

    return NextResponse.json(
      { message: "Messages marked as read" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error marking messages as read:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
