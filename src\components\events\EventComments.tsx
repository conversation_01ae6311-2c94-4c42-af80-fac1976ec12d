"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { formatTimeAgo } from "@/lib/utils";
import { CommentTime } from "@/components/ui/TimeDisplay";
import { PhotoIcon, PaperAirplaneIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useDropzone } from "react-dropzone";
import { parseTextContent, renderParsedContent } from "@/lib/utils/link-utils";

interface Comment {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username: string | null;
    image: string | null;
  };
}

interface EventCommentsProps {
  eventId: string;
}

// Helper function to render comment content with both links and mentions
const renderCommentContent = (content: string) => {
  const parsedContent = parseTextContent(content);
  return renderParsedContent(parsedContent, {
    showLinkPreviews: false,
    linkClassName: "text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200",
    mentionClassName: "text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
  });
};

export function EventComments({ eventId }: EventCommentsProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newComment, setNewComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [images, setImages] = useState<File[]>([]);
  const [showImageUpload, setShowImageUpload] = useState(false);
  const commentInputRef = useRef<HTMLTextAreaElement>(null);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif"],
    },
    maxFiles: 5,
    onDrop: (acceptedFiles) => {
      setImages((prev) => [...prev, ...acceptedFiles].slice(0, 5));
    },
  });

  useEffect(() => {
    const fetchComments = async () => {
      if (!eventId) {
        setError("Event ID is missing");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log(`Fetching comments for event: ${eventId}`);
        const response = await fetch(`/api/events/${eventId}/comments`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || "Failed to fetch comments");
        }

        const data = await response.json();
        console.log("Comments data received:", data);
        setComments(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error("Error fetching comments:", err);
        // Don't show the error to the user, just show an empty comments list
        setComments([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchComments();
  }, [eventId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!session?.user || !eventId || (!newComment.trim() && images.length === 0)) {
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("content", newComment || "📷"); // Use a camera emoji if only uploading images

      images.forEach((image) => {
        formData.append("images", image);
      });

      console.log(`Submitting comment for event: ${eventId}`);
      const response = await fetch(`/api/events/${eventId}/comments`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to add comment");
      }

      const newCommentData = await response.json();
      console.log("New comment data received:", newCommentData);

      // Add the new comment to the list
      if (newCommentData && newCommentData.id) {
        setComments((prev) => [newCommentData, ...prev]);
      }

      // Reset form
      setNewComment("");
      setImages([]);
      setShowImageUpload(false);
    } catch (err) {
      console.error("Error adding comment:", err);
      setError(err instanceof Error ? err.message : "Failed to add comment. Please try again.");
      // Clear error after 5 seconds
      setTimeout(() => setError(null), 5000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeImage = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-10">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-4 text-red-800">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      {/* Comment form */}
      {session?.user && (
        <form onSubmit={handleSubmit} className="mb-8">
          <div className="flex space-x-3">
            <div className="flex-shrink-0">
              {session.user.image ? (
                <Image
                  src={session.user.image}
                  alt={session.user.name || "User"}
                  width={40}
                  height={40}
                  className="h-10 w-10 rounded-full"
                />
              ) : (
                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500 text-sm font-medium">
                    {session.user.name?.charAt(0) || "U"}
                  </span>
                </div>
              )}
            </div>
            <div className="min-w-0 flex-1">
              <div className="rounded-lg border border-gray-300 shadow-sm overflow-hidden focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500">
                <textarea
                  ref={commentInputRef}
                  rows={3}
                  name="comment"
                  id="comment"
                  className="block w-full resize-none border-0 py-3 px-4 focus:outline-none focus:ring-0 sm:text-sm"
                  placeholder="Write a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                />

                {showImageUpload && (
                  <div className="p-3 border-t border-gray-200">
                    <div
                      {...getRootProps()}
                      className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center cursor-pointer hover:bg-gray-50"
                    >
                      <input {...getInputProps()} />
                      <PhotoIcon className="mx-auto h-8 w-8 text-gray-400" />
                      <p className="mt-1 text-sm text-gray-500">
                        Drag and drop images, or click to select
                      </p>
                    </div>

                    {images.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {images.map((image, index) => (
                          <div key={index} className="relative">
                            <div className="h-16 w-16 rounded-md bg-gray-100 flex items-center justify-center overflow-hidden">
                              <img
                                src={URL.createObjectURL(image)}
                                alt={`Preview ${index}`}
                                className="h-full w-full object-cover"
                              />
                            </div>
                            <button
                              type="button"
                              className="absolute -top-2 -right-2 rounded-full bg-white shadow p-0.5"
                              onClick={() => removeImage(index)}
                            >
                              <XMarkIcon className="h-4 w-4 text-gray-500" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="mt-2 flex justify-between">
                <div>
                  <button
                    type="button"
                    className="inline-flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100"
                    onClick={() => setShowImageUpload(!showImageUpload)}
                  >
                    <PhotoIcon className="h-5 w-5 mr-1 text-gray-500" />
                    Photo
                  </button>
                </div>
                <Button
                  type="submit"
                  disabled={isSubmitting || (!newComment.trim() && images.length === 0)}
                >
                  {isSubmitting ? (
                    <Spinner size="sm" className="mr-2" />
                  ) : (
                    <PaperAirplaneIcon className="h-5 w-5 mr-1" />
                  )}
                  Comment
                </Button>
              </div>
            </div>
          </div>
        </form>
      )}

      {/* Comments list */}
      {comments.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No comments yet. Be the first to comment!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {comments.map((comment) => (
            <div key={comment.id} className="flex space-x-3">
              <div className="flex-shrink-0">
                {comment.user.image ? (
                  <Image
                    src={comment.user.image}
                    alt={comment.user.name}
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded-full"
                  />
                ) : (
                  <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-500 text-sm font-medium">
                      {comment.user.name.charAt(0)}
                    </span>
                  </div>
                )}
              </div>
              <div>
                <div className="bg-gray-50 rounded-lg px-4 py-3">
                  <div className="flex items-center justify-between">
                    <Link
                      href={`/user/${comment.user.username || comment.user.id}`}
                      className="font-medium text-gray-900 hover:text-blue-600 hover:underline cursor-pointer"
                    >
                      {comment.user.name}
                    </Link>
                    <CommentTime
                      date={comment.createdAt}
                      className="text-xs text-gray-500"
                      autoUpdate={true}
                    />
                  </div>
                  <div className="mt-1 text-sm text-gray-700">
                    {renderCommentContent(comment.content)}
                  </div>

                  {comment.images && comment.images.length > 0 && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {comment.images.map((image, index) => (
                        <div key={index} className="relative">
                          <div className="h-24 w-24 rounded-md bg-gray-100 flex items-center justify-center overflow-hidden">
                            <Image
                              src={image}
                              alt={`Image ${index}`}
                              width={96}
                              height={96}
                              className="h-full w-full object-cover"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
