import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  wallets,
  walletTransactions,
  referralCommissions,
  referrals
} from "@/lib/db/schema";
import { eq, and, or, sum, count, desc, sql } from "drizzle-orm";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get wallet data
    const wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    // Get all referral commissions for this user
    const allCommissions = await db.query.referralCommissions.findMany({
      where: eq(referralCommissions.referrerId, userId),
      orderBy: [desc(referralCommissions.createdAt)],
    });

    // Get commission totals by status
    const paidCommissions = await db
      .select({ total: sum(referralCommissions.commissionAmount), count: count() })
      .from(referralCommissions)
      .where(
        and(
          eq(referralCommissions.referrerId, userId),
          eq(referralCommissions.status, 'paid')
        )
      );

    const approvedCommissions = await db
      .select({ total: sum(referralCommissions.commissionAmount), count: count() })
      .from(referralCommissions)
      .where(
        and(
          eq(referralCommissions.referrerId, userId),
          eq(referralCommissions.status, 'approved')
        )
      );

    const allCommissionsTotal = await db
      .select({ total: sum(referralCommissions.commissionAmount), count: count() })
      .from(referralCommissions)
      .where(eq(referralCommissions.referrerId, userId));

    // Get wallet transactions related to referrals
    const referralWalletTransactions = await db.query.walletTransactions.findMany({
      where: and(
        eq(walletTransactions.userId, userId),
        eq(walletTransactions.type, 'earning'),
        or(
          sql`${walletTransactions.reference} LIKE '%referral%'`,
          sql`JSON_EXTRACT(metadata, '$.type') = 'referral_commission'`,
          sql`${walletTransactions.description} LIKE '%referral%'`
        )
      ),
      orderBy: [desc(walletTransactions.createdAt)],
    });

    // Get all earning transactions
    const allEarningTransactions = await db.query.walletTransactions.findMany({
      where: and(
        eq(walletTransactions.userId, userId),
        eq(walletTransactions.type, 'earning')
      ),
      orderBy: [desc(walletTransactions.createdAt)],
      limit: 20,
    });

    // Get referral statistics
    const referralStats = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.referrerId, userId));

    return NextResponse.json({
      success: true,
      data: {
        userId,
        wallet: {
          generalBalance: wallet?.generalBalance || '0',
          earningBalance: wallet?.earningBalance || '0',
        },
        commissions: {
          all: allCommissions.map(c => ({
            id: c.id,
            planName: c.planName,
            commissionAmount: parseFloat(c.commissionAmount),
            status: c.status,
            createdAt: c.createdAt.toISOString(),
            approvedAt: c.approvedAt?.toISOString(),
            paidAt: c.paidAt?.toISOString(),
          })),
          totals: {
            paid: {
              amount: parseFloat(paidCommissions[0]?.total || '0'),
              count: paidCommissions[0]?.count || 0,
            },
            approved: {
              amount: parseFloat(approvedCommissions[0]?.total || '0'),
              count: approvedCommissions[0]?.count || 0,
            },
            all: {
              amount: parseFloat(allCommissionsTotal[0]?.total || '0'),
              count: allCommissionsTotal[0]?.count || 0,
            },
          },
        },
        walletTransactions: {
          referralRelated: referralWalletTransactions.map(t => ({
            id: t.id,
            amount: parseFloat(t.amount),
            reference: t.reference,
            description: t.description,
            metadata: t.metadata,
            status: t.status,
            createdAt: t.createdAt.toISOString(),
          })),
          allEarnings: allEarningTransactions.map(t => ({
            id: t.id,
            amount: parseFloat(t.amount),
            reference: t.reference,
            description: t.description,
            metadata: t.metadata,
            status: t.status,
            createdAt: t.createdAt.toISOString(),
          })),
        },
        referralStats: {
          totalReferrals: referralStats[0]?.count || 0,
        },
      },
    });

  } catch (error) {
    console.error("Error fetching debug earning data:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch debug earning data",
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
