"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import {
  BanknotesIcon,
  UsersIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CreditCardIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface WalletStats {
  totalUsers: number;
  totalDeposited: string;
  totalWithdrawn: string;
  totalTransferred: string;
  pendingTransactions: number;
  activeAgents: number;
  todayDeposits: string;
  todayWithdrawals: string;
  todayTransfers: string;
}

export default function AdminWalletPage() {
  const [stats, setStats] = useState<WalletStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchWalletStats();
  }, []);

  const fetchWalletStats = async () => {
    try {
      const response = await fetch('/api/admin/wallet/overview');
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      } else {
        toast.error('Failed to fetch wallet statistics');
      }
    } catch (error) {
      console.error('Error fetching wallet stats:', error);
      toast.error('Failed to fetch wallet statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse" />
            ))}
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Wallet System</h1>
            <p className="text-gray-600 mt-1">
              Monitor and manage the wallet system
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => window.location.href = '/admin/wallet/transactions'}
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Wallet Transactions
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/admin/wallet/earnings'}
            >
              <CurrencyDollarIcon className="h-5 w-5 mr-2" />
              Earning Wallet
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/admin/payments'}
            >
              <CreditCardIcon className="h-5 w-5 mr-2" />
              Deposits
            </Button>
            <Button
              onClick={() => window.location.href = '/admin/wallet/settings'}
            >
              Settings
            </Button>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Users with Wallets */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.totalUsers.toLocaleString() || '0'}
                </p>
              </div>
            </div>
          </div>

          {/* Total Deposited */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowTrendingUpIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Deposited</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${parseFloat(stats?.totalDeposited || '0').toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          {/* Total Withdrawn */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowTrendingDownIcon className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Withdrawn</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${parseFloat(stats?.totalWithdrawn || '0').toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          {/* Total Transferred */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Transferred</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${parseFloat(stats?.totalTransferred || '0').toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          {/* Pending Transactions */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Transactions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.pendingTransactions || '0'}
                </p>
              </div>
            </div>
          </div>

          {/* Active Agents */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BanknotesIcon className="h-8 w-8 text-indigo-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Agents</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.activeAgents || '0'}
                </p>
              </div>
            </div>
          </div>

          {/* Today's Deposits */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Deposits</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${parseFloat(stats?.todayDeposits || '0').toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          {/* Today's Withdrawals */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowTrendingDownIcon className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Withdrawals</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${parseFloat(stats?.todayWithdrawals || '0').toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => window.location.href = '/admin/wallet/transactions'}
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Manage Transactions
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => window.location.href = '/admin/wallet/agents'}
            >
              <UsersIcon className="h-5 w-5 mr-2" />
              Manage Agents
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => window.location.href = '/admin/wallet/gateways'}
            >
              <CurrencyDollarIcon className="h-5 w-5 mr-2" />
              Payment Gateways
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => window.location.href = '/admin/wallet/settings'}
            >
              <BanknotesIcon className="h-5 w-5 mr-2" />
              System Settings
            </Button>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/admin/wallet/transactions'}
            >
              View All
            </Button>
          </div>

          <div className="text-center py-8 text-gray-500">
            <ChartBarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Recent transactions will appear here</p>
            <p className="text-sm mt-1">Configure the wallet system to start seeing activity</p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
