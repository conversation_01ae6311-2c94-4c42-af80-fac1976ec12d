import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageFollowers, notifications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// POST /api/fan-pages/[pageId]/follow - Follow a fan page
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if fan page exists
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];

    // Check if already following
    const existingFollow = await db
      .select()
      .from(fanPageFollowers)
      .where(
        and(
          eq(fanPageFollowers.userId, session.user.id),
          eq(fanPageFollowers.fanPageId, pageId)
        )
      )
      .limit(1);

    if (existingFollow.length > 0) {
      return NextResponse.json(
        { error: "Already following this page" },
        { status: 400 }
      );
    }

    // Create follow relationship and update follower count in parallel
    const [followResult] = await Promise.all([
      db.insert(fanPageFollowers).values({
        id: uuidv4(),
        userId: session.user.id,
        fanPageId: pageId,
      }),
      db
        .update(fanPages)
        .set({
          followerCount: page.followerCount + 1,
          updatedAt: new Date(),
        })
        .where(eq(fanPages.id, pageId)),
    ]);

    // Create notification asynchronously (don't wait for it)
    db.insert(notifications).values({
      id: uuidv4(),
      recipientId: page.ownerId,
      type: "fan_page_follow",
      senderId: session.user.id,
      fanPageId: pageId,
    }).catch(error => {
      console.error("Error creating follow notification:", error);
    });

    return NextResponse.json({
      message: "Successfully followed the page",
      isFollowing: true,
    });

  } catch (error) {
    console.error("Error following fan page:", error);
    return NextResponse.json(
      { error: "Failed to follow page" },
      { status: 500 }
    );
  }
}

// DELETE /api/fan-pages/[pageId]/follow - Unfollow a fan page
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if fan page exists
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];

    // Check if currently following
    const existingFollow = await db
      .select()
      .from(fanPageFollowers)
      .where(
        and(
          eq(fanPageFollowers.userId, session.user.id),
          eq(fanPageFollowers.fanPageId, pageId)
        )
      )
      .limit(1);

    if (existingFollow.length === 0) {
      return NextResponse.json(
        { error: "Not following this page" },
        { status: 400 }
      );
    }

    // Remove follow relationship and update follower count in parallel
    await Promise.all([
      db
        .delete(fanPageFollowers)
        .where(
          and(
            eq(fanPageFollowers.userId, session.user.id),
            eq(fanPageFollowers.fanPageId, pageId)
          )
        ),
      db
        .update(fanPages)
        .set({
          followerCount: Math.max(0, page.followerCount - 1),
          updatedAt: new Date(),
        })
        .where(eq(fanPages.id, pageId)),
    ]);

    return NextResponse.json({
      message: "Successfully unfollowed the page",
      isFollowing: false,
    });

  } catch (error) {
    console.error("Error unfollowing fan page:", error);
    return NextResponse.json(
      { error: "Failed to unfollow page" },
      { status: 500 }
    );
  }
}
