import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, or, and } from "drizzle-orm";

const profileUpdateSchema = z.object({
  name: z.string().min(2).max(50).optional(),
  username: z.string().min(3).max(30).regex(/^[a-zA-Z0-9_]+$/).optional(),
  bio: z.string().max(500).optional(),
  location: z.string().max(100).optional(),
  birthday: z.string().optional().transform(val => val ? new Date(val) : null),
  coverImage: z.string().url().optional(),
  image: z.string().url().optional(),
  // Work & Education
  work: z.string().max(255).optional().or(z.literal('')),
  education: z.string().max(255).optional().or(z.literal('')),
  // Social links
  website: z.string().url().optional().or(z.literal('')),
  facebook: z.string().url().optional().or(z.literal('')),
  twitter: z.string().url().optional().or(z.literal('')),
  instagram: z.string().url().optional().or(z.literal('')),
  linkedin: z.string().url().optional().or(z.literal('')),
  youtube: z.string().url().optional().or(z.literal('')),
  // Privacy settings
  profileVisibility: z.enum(['public', 'subscribers', 'private']).optional(),
  showEmail: z.boolean().optional(),
  showPhone: z.boolean().optional(),
  showBirthday: z.boolean().optional(),
  showLocation: z.boolean().optional(),
  allowFriendRequests: z.enum(['everyone', 'friends-of-friends', 'nobody']).optional(),
  defaultPostPrivacy: z.enum(['public', 'friends', 'private']).optional(),
  allowTagging: z.boolean().optional(),
  allowMessagesFrom: z.enum(['everyone', 'friends', 'nobody']).optional(),
  showOnlineStatus: z.boolean().optional(),
  allowSearchByEmail: z.boolean().optional(),
  allowSearchByPhone: z.boolean().optional(),
});

export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;

    // Fetch the user profile from the database
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        name: true,
        username: true,
        email: true,
        image: true,
        coverImage: true,
        bio: true,
        location: true,
        birthday: true,
        website: true,
        facebook: true,
        twitter: true,
        instagram: true,
        linkedin: true,
        youtube: true,
        profileVisibility: true,
        showEmail: true,
        showPhone: true,
        showBirthday: true,
        showLocation: true,
        allowFriendRequests: true,
        defaultPostPrivacy: true,
        allowTagging: true,
        allowMessagesFrom: true,
        showOnlineStatus: true,
        allowSearchByEmail: true,
        allowSearchByPhone: true,
        createdAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }



    // Create a new user object without email if not the current user
    const userResponse = { ...user };

    // For privacy, only return email if it's the current user
    if (userId !== session.user.id) {
      // @ts-expect-error - We know email exists on the user object
      userResponse.email = undefined;
    }

    console.log('API response user data:', userResponse);
    return NextResponse.json(userResponse);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;

    // Only allow users to update their own profile
    if (userId !== session.user.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const validatedData = profileUpdateSchema.parse(body);

    // Check if username is being updated and if it's already taken
    if (validatedData.username) {
      const existingUser = await db.query.users.findFirst({
        where: eq(users.username, validatedData.username),
        columns: { id: true },
      });

      if (existingUser && existingUser.id !== userId) {
        return NextResponse.json(
          { message: "This username is already taken" },
          { status: 400 }
        );
      }
    }

    // Update the user profile in the database
    await db
      .update(users)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Return the updated profile data
    return NextResponse.json(
      {
        message: "Profile updated successfully",
        data: validatedData
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating user profile:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
