import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { OnThisDayMemories } from "@/components/memories/OnThisDayMemories";
import { RecentMemories } from "@/components/memories/RecentMemories";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { SidebarErrorBoundary } from "@/components/error/ErrorBoundary";

export default async function MemoriesPage() {
  await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row">
          {/* Left sidebar - 20% */}
          <div className="w-full lg:w-[20%] mb-5 lg:mb-0">
            <SidebarErrorBoundary>
              <LeftSidebar />
            </SidebarErrorBoundary>
            {/* This is an empty div that takes up the same space as the fixed sidebar */}
            <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
          </div>

          {/* Gap between left sidebar and main content - 5% */}
          <div className="hidden lg:block lg:w-[5%]"></div>

          {/* Main content - 70% */}
          <div className="w-full lg:w-[70%] space-y-6">

            {/* Today's memories section */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="bg-gradient-to-r from-amber-50 to-orange-50 px-6 py-4 border-b border-amber-100">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-amber-100 rounded-full">
                    <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900">
                    🎆 On This Day
                  </h2>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  See what happened on this day in previous years
                </p>
              </div>
              <div className="p-6">
                <OnThisDayMemories />
              </div>
            </div>

            {/* Recent memories section */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-green-100">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900">
                    📸 Recent Memories
                  </h2>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Your recent posts and moments worth remembering
                </p>
              </div>
              <div className="p-6">
                <RecentMemories />
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
