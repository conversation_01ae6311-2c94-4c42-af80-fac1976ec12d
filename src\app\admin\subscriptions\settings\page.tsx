"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Switch } from "@/components/ui/Switch";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { toast } from "react-hot-toast";
import {
  BellIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
  Cog6ToothIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";

interface NotificationSettings {
  emailNotifications: {
    enabled: boolean;
    subscriptionCreated: boolean;
    subscriptionActivated: boolean;
    subscriptionCancelled: boolean;
    subscriptionExpired: boolean;
    paymentFailed: boolean;
    paymentSuccessful: boolean;
    subscriptionRenewed: boolean;
  };
  pushNotifications: {
    enabled: boolean;
    subscriptionCreated: boolean;
    subscriptionActivated: boolean;
    subscriptionCancelled: boolean;
    subscriptionExpired: boolean;
    paymentFailed: boolean;
    paymentSuccessful: boolean;
    subscriptionRenewed: boolean;
  };
  adminNotifications: {
    enabled: boolean;
    newSubscriptions: boolean;
    paymentFailures: boolean;
    cancellations: boolean;
    chargebacks: boolean;
    emailAlerts: boolean;
  };
  emailTemplates: {
    subscriptionWelcome: string;
    paymentFailed: string;
    subscriptionCancelled: string;
    subscriptionExpired: string;
  };
}

export default function SubscriptionSettingsPage() {
  const [settings, setSettings] = useState<NotificationSettings>({
    emailNotifications: {
      enabled: true,
      subscriptionCreated: true,
      subscriptionActivated: true,
      subscriptionCancelled: true,
      subscriptionExpired: true,
      paymentFailed: true,
      paymentSuccessful: false,
      subscriptionRenewed: true,
    },
    pushNotifications: {
      enabled: true,
      subscriptionCreated: false,
      subscriptionActivated: true,
      subscriptionCancelled: true,
      subscriptionExpired: true,
      paymentFailed: true,
      paymentSuccessful: false,
      subscriptionRenewed: false,
    },
    adminNotifications: {
      enabled: true,
      newSubscriptions: true,
      paymentFailures: true,
      cancellations: true,
      chargebacks: true,
      emailAlerts: true,
    },
    emailTemplates: {
      subscriptionWelcome: "Welcome to {{planName}}! Your subscription is now active.",
      paymentFailed: "We couldn't process your payment for {{planName}}. Please update your payment method.",
      subscriptionCancelled: "Your {{planName}} subscription has been cancelled.",
      subscriptionExpired: "Your {{planName}} subscription has expired. Renew now to continue.",
    },
  });

  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/subscriptions/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data.settings || settings);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load notification settings');
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setIsSaving(true);
      
      const response = await fetch('/api/admin/subscriptions/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings }),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      toast.success('Notification settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save notification settings');
    } finally {
      setIsSaving(false);
    }
  };

  const updateEmailNotification = (key: keyof NotificationSettings['emailNotifications'], value: boolean) => {
    setSettings(prev => ({
      ...prev,
      emailNotifications: {
        ...prev.emailNotifications,
        [key]: value,
      },
    }));
  };

  const updatePushNotification = (key: keyof NotificationSettings['pushNotifications'], value: boolean) => {
    setSettings(prev => ({
      ...prev,
      pushNotifications: {
        ...prev.pushNotifications,
        [key]: value,
      },
    }));
  };

  const updateAdminNotification = (key: keyof NotificationSettings['adminNotifications'], value: boolean) => {
    setSettings(prev => ({
      ...prev,
      adminNotifications: {
        ...prev.adminNotifications,
        [key]: value,
      },
    }));
  };

  const updateEmailTemplate = (key: keyof NotificationSettings['emailTemplates'], value: string) => {
    setSettings(prev => ({
      ...prev,
      emailTemplates: {
        ...prev.emailTemplates,
        [key]: value,
      },
    }));
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Subscription Notification Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Configure how and when users receive subscription-related notifications
        </p>
      </div>

      <div className="space-y-6">
        {/* Email Notifications */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <EnvelopeIcon className="h-6 w-6 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">Email Notifications</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Enable Email Notifications</h3>
                <p className="text-sm text-gray-500">Send email notifications for subscription events</p>
              </div>
              <Switch
                checked={settings.emailNotifications.enabled}
                onChange={(checked) => updateEmailNotification('enabled', checked)}
              />
            </div>

            {settings.emailNotifications.enabled && (
              <div className="ml-4 space-y-3 border-l-2 border-gray-200 pl-4">
                {Object.entries(settings.emailNotifications).map(([key, value]) => {
                  if (key === 'enabled') return null;
                  
                  const labels = {
                    subscriptionCreated: 'Subscription Created',
                    subscriptionActivated: 'Subscription Activated',
                    subscriptionCancelled: 'Subscription Cancelled',
                    subscriptionExpired: 'Subscription Expired',
                    paymentFailed: 'Payment Failed',
                    paymentSuccessful: 'Payment Successful',
                    subscriptionRenewed: 'Subscription Renewed',
                  };

                  return (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">
                        {labels[key as keyof typeof labels]}
                      </span>
                      <Switch
                        checked={value}
                        onChange={(checked) => updateEmailNotification(key as any, checked)}
                      />
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </Card>

        {/* Push Notifications */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <DevicePhoneMobileIcon className="h-6 w-6 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Push Notifications</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Enable Push Notifications</h3>
                <p className="text-sm text-gray-500">Send push notifications for subscription events</p>
              </div>
              <Switch
                checked={settings.pushNotifications.enabled}
                onChange={(checked) => updatePushNotification('enabled', checked)}
              />
            </div>

            {settings.pushNotifications.enabled && (
              <div className="ml-4 space-y-3 border-l-2 border-gray-200 pl-4">
                {Object.entries(settings.pushNotifications).map(([key, value]) => {
                  if (key === 'enabled') return null;
                  
                  const labels = {
                    subscriptionCreated: 'Subscription Created',
                    subscriptionActivated: 'Subscription Activated',
                    subscriptionCancelled: 'Subscription Cancelled',
                    subscriptionExpired: 'Subscription Expired',
                    paymentFailed: 'Payment Failed',
                    paymentSuccessful: 'Payment Successful',
                    subscriptionRenewed: 'Subscription Renewed',
                  };

                  return (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">
                        {labels[key as keyof typeof labels]}
                      </span>
                      <Switch
                        checked={value}
                        onChange={(checked) => updatePushNotification(key as any, checked)}
                      />
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </Card>

        {/* Admin Notifications */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <BellIcon className="h-6 w-6 text-orange-600" />
            <h2 className="text-lg font-semibold text-gray-900">Admin Notifications</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Enable Admin Notifications</h3>
                <p className="text-sm text-gray-500">Notify admins about important subscription events</p>
              </div>
              <Switch
                checked={settings.adminNotifications.enabled}
                onChange={(checked) => updateAdminNotification('enabled', checked)}
              />
            </div>

            {settings.adminNotifications.enabled && (
              <div className="ml-4 space-y-3 border-l-2 border-gray-200 pl-4">
                {Object.entries(settings.adminNotifications).map(([key, value]) => {
                  if (key === 'enabled') return null;
                  
                  const labels = {
                    newSubscriptions: 'New Subscriptions',
                    paymentFailures: 'Payment Failures',
                    cancellations: 'Cancellations',
                    chargebacks: 'Chargebacks',
                    emailAlerts: 'Email Alerts',
                  };

                  return (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">
                        {labels[key as keyof typeof labels]}
                      </span>
                      <Switch
                        checked={value}
                        onChange={(checked) => updateAdminNotification(key as any, checked)}
                      />
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </Card>

        {/* Email Templates */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Cog6ToothIcon className="h-6 w-6 text-purple-600" />
            <h2 className="text-lg font-semibold text-gray-900">Email Templates</h2>
          </div>
          
          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              Customize email templates. Use {{planName}}, {{userName}}, {{endDate}} as placeholders.
            </p>
            
            {Object.entries(settings.emailTemplates).map(([key, value]) => {
              const labels = {
                subscriptionWelcome: 'Welcome Email',
                paymentFailed: 'Payment Failed Email',
                subscriptionCancelled: 'Cancellation Email',
                subscriptionExpired: 'Expiration Email',
              };

              return (
                <div key={key}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {labels[key as keyof typeof labels]}
                  </label>
                  <Textarea
                    value={value}
                    onChange={(e) => updateEmailTemplate(key as any, e.target.value)}
                    rows={3}
                    placeholder="Enter email template..."
                  />
                </div>
              );
            })}
          </div>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={saveSettings} disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>
    </AdminLayout>
  );
}
