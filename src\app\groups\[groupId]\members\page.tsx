import Link from "next/link";
import Image from "next/image";
import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { db } from "@/lib/db";
import { groups, groupMembers, users } from "@/lib/db/schema";
import { eq, and, desc, or } from "drizzle-orm";
import { formatTimeAgo } from "@/lib/utils";
import {
  UsersIcon,
  ChevronLeftIcon,
  MagnifyingGlassIcon,
  ShieldCheckIcon,
  UserPlusIcon,
  UserMinusIcon,
  FunnelIcon
} from "@heroicons/react/24/outline";

// Import components
import { GroupHeader } from "@/components/groups/GroupHeader";
import { GroupTabs } from "@/components/groups/GroupTabs";
import { GroupNotFound } from "@/components/groups/GroupNotFound";

interface GroupMembersPageProps {
  params: Promise<{
    groupId: string;
  }>;
}

export default async function GroupMembersPage({ params }: GroupMembersPageProps) {
  const user = await requireAuth();
  const resolvedParams = await params;
  const { groupId } = resolvedParams;

  // Fetch the group
  const group = await db.select({
    id: groups.id,
    name: groups.name,
    description: groups.description,
    visibility: groups.visibility,
    coverImage: groups.coverImage,
    category: groups.category,
    creatorId: groups.creatorId,
    createdAt: groups.createdAt,
    updatedAt: groups.updatedAt
  })
  .from(groups)
  .where(eq(groups.id, groupId))
  .limit(1)
  .then(results => results[0]);

  if (!group) {
    // If group doesn't exist, show a nice error message
    return (
      <MainLayout>
        <GroupNotFound />
      </MainLayout>
    );
  }

  // Get user's role in the group
  const userMembership = await db.query.groupMembers.findFirst({
    where: and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.userId, user.id)
    ),
  });

  // Check if user has access to the group
  const isPublic = group.visibility === "public";
  const isPrivateVisible = group.visibility === "private-visible";
  const isMember = userMembership && (userMembership.role === "admin" || userMembership.role === "moderator" || userMembership.role === "member");
  const isAdmin = userMembership && userMembership.role === "admin";
  const isModerator = userMembership && userMembership.role === "moderator";
  const isCreator = group && group.creatorId === user.id;

  // For private-hidden groups, only members can see the members
  if (group.visibility === "private-hidden" && !isMember && !isCreator) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Link href="/groups">
              <Button variant="outline" size="sm">
                <ChevronLeftIcon className="mr-1 h-4 w-4" />
                Back to Groups
              </Button>
            </Link>
          </div>

          <div className="rounded-lg bg-white p-8 text-center shadow">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
              <UsersIcon className="h-8 w-8 text-yellow-600" />
            </div>
            <h1 className="mt-4 text-xl font-bold text-gray-900">
              Private Group
            </h1>
            <p className="mt-4 text-sm text-gray-500">
              This is a private group. You need to be a member to view its members.
            </p>
            <div className="mt-4">
              <Link href={`/groups/${groupId}`}>
                <Button>
                  View Group
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Fetch all members
  const allMembers = await db.select({
    member: groupMembers,
    user: users,
  })
  .from(groupMembers)
  .innerJoin(users, eq(groupMembers.userId, users.id))
  .where(eq(groupMembers.groupId, groupId))
  .orderBy(desc(groupMembers.createdAt));

  // Separate members by role
  const admins = allMembers.filter(m => m.member.role === "admin");
  const moderators = allMembers.filter(m => m.member.role === "moderator");
  const regularMembers = allMembers.filter(m => m.member.role === "member");
  const pendingMembers = allMembers.filter(m => m.member.role === "pending");

  return (
    <MainLayout>
      {/* Hero section with cover image */}
      <GroupHeader
        group={group}
        membersCount={allMembers.length}
        isCreator={isCreator}
        isAdmin={isAdmin || false}
        isMember={isMember || false}
        isPending={false}
        isPublic={isPublic}
        isPrivateVisible={isPrivateVisible}
      />

      {/* Tabs navigation */}
      <GroupTabs groupId={groupId} activeTab="members" showSettings={isCreator || isAdmin} />

      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Page title and stats */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Members
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              {allMembers.length} total members
            </p>
          </div>

          {(isAdmin || isCreator) && (
            <div className="mt-4 sm:mt-0">
              <Button>
                <UserPlusIcon className="mr-1.5 h-5 w-5" />
                Invite Members
              </Button>
            </div>
          )}
        </div>

        {/* Search and filters */}
        <div className="mb-6 rounded-xl bg-white p-4 shadow-sm">
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="search"
                placeholder="Search members..."
                className="pl-10"
              />
            </div>

            <div>
              <Button variant="outline" size="sm">
                <FunnelIcon className="mr-1.5 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>
        </div>

        {/* Members list */}
        <div className="space-y-8">
          {/* Admins section */}
          {admins.length > 0 && (
            <div>
              <h2 className="mb-4 text-lg font-medium text-gray-900 flex items-center">
                <ShieldCheckIcon className="mr-2 h-5 w-5 text-blue-600" />
                Admins
              </h2>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {admins.map(({ member, user: memberUser }) => (
                  <div key={member.id} className="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm">
                    <div className="flex items-center">
                      <div className="h-12 w-12 flex-shrink-0">
                        {memberUser.image ? (
                          <Image
                            src={memberUser.image}
                            alt={memberUser.name || "User"}
                            width={48}
                            height={48}
                            className="h-12 w-12 rounded-full"
                          />
                        ) : (
                          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white">
                            {memberUser.name?.charAt(0).toUpperCase() || "U"}
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <Link href={`/user/${memberUser.username || memberUser.id}`} className="text-sm font-medium text-gray-900 hover:underline">
                          {memberUser.name}
                        </Link>
                        <div className="flex items-center">
                          <span className="text-xs text-gray-500">
                            Joined {formatTimeAgo(member.createdAt)}
                          </span>
                          <span className="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                            <ShieldCheckIcon className="mr-0.5 h-3 w-3" />
                            Admin
                          </span>
                        </div>
                      </div>
                    </div>

                    {(isCreator && memberUser.id !== user.id) && (
                      <Button variant="outline" size="sm">
                        <UserMinusIcon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Moderators section */}
          {moderators.length > 0 && (
            <div>
              <h2 className="mb-4 text-lg font-medium text-gray-900 flex items-center">
                <ShieldCheckIcon className="mr-2 h-5 w-5 text-green-600" />
                Moderators
              </h2>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {moderators.map(({ member, user: memberUser }) => (
                  <div key={member.id} className="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm">
                    <div className="flex items-center">
                      <div className="h-12 w-12 flex-shrink-0">
                        {memberUser.image ? (
                          <Image
                            src={memberUser.image}
                            alt={memberUser.name || "User"}
                            width={48}
                            height={48}
                            className="h-12 w-12 rounded-full"
                          />
                        ) : (
                          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-teal-600 text-white">
                            {memberUser.name?.charAt(0).toUpperCase() || "U"}
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <Link href={`/user/${memberUser.username || memberUser.id}`} className="text-sm font-medium text-gray-900 hover:underline">
                          {memberUser.name}
                        </Link>
                        <div className="flex items-center">
                          <span className="text-xs text-gray-500">
                            Joined {formatTimeAgo(member.createdAt)}
                          </span>
                          <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                            <ShieldCheckIcon className="mr-0.5 h-3 w-3" />
                            Moderator
                          </span>
                        </div>
                      </div>
                    </div>

                    {(isAdmin || isCreator) && memberUser.id !== user.id && (
                      <Button variant="outline" size="sm">
                        <UserMinusIcon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Regular members section */}
          {regularMembers.length > 0 && (
            <div>
              <h2 className="mb-4 text-lg font-medium text-gray-900 flex items-center">
                <UsersIcon className="mr-2 h-5 w-5 text-gray-600" />
                Members
              </h2>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {regularMembers.map(({ member, user: memberUser }) => (
                  <div key={member.id} className="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm">
                    <div className="flex items-center">
                      <div className="h-12 w-12 flex-shrink-0">
                        {memberUser.image ? (
                          <Image
                            src={memberUser.image}
                            alt={memberUser.name || "User"}
                            width={48}
                            height={48}
                            className="h-12 w-12 rounded-full"
                          />
                        ) : (
                          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                            {memberUser.name?.charAt(0).toUpperCase() || "U"}
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <Link href={`/user/${memberUser.username || memberUser.id}`} className="text-sm font-medium text-gray-900 hover:underline">
                          {memberUser.name}
                        </Link>
                        <div className="flex items-center">
                          <span className="text-xs text-gray-500">
                            Joined {formatTimeAgo(member.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {(isAdmin || isModerator || isCreator) && memberUser.id !== user.id && (
                      <div className="flex space-x-2">
                        {(isAdmin || isCreator) && (
                          <Button variant="outline" size="sm">
                            Promote
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <UserMinusIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Pending members section (only visible to admins and moderators) */}
          {(isAdmin || isModerator || isCreator) && pendingMembers.length > 0 && (
            <div>
              <h2 className="mb-4 text-lg font-medium text-gray-900 flex items-center">
                <UserPlusIcon className="mr-2 h-5 w-5 text-yellow-600" />
                Pending Requests
              </h2>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {pendingMembers.map(({ member, user: memberUser }) => (
                  <div key={member.id} className="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm">
                    <div className="flex items-center">
                      <div className="h-12 w-12 flex-shrink-0">
                        {memberUser.image ? (
                          <Image
                            src={memberUser.image}
                            alt={memberUser.name || "User"}
                            width={48}
                            height={48}
                            className="h-12 w-12 rounded-full"
                          />
                        ) : (
                          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                            {memberUser.name?.charAt(0).toUpperCase() || "U"}
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <Link href={`/user/${memberUser.username || memberUser.id}`} className="text-sm font-medium text-gray-900 hover:underline">
                          {memberUser.name}
                        </Link>
                        <div className="flex items-center">
                          <span className="text-xs text-gray-500">
                            Requested {formatTimeAgo(member.createdAt)}
                          </span>
                          <span className="ml-2 inline-flex items-center rounded-full bg-yellow-100 px-2 py-0.5 text-xs font-medium text-yellow-800">
                            Pending
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm">
                        Approve
                      </Button>
                      <Button variant="outline" size="sm">
                        Reject
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Empty state */}
          {allMembers.length === 0 && (
            <div className="rounded-lg bg-white p-8 text-center shadow">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                <UsersIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h2 className="mt-4 text-lg font-medium text-gray-900">
                No members yet
              </h2>
              <p className="mt-2 text-sm text-gray-500">
                This group doesn't have any members yet.
              </p>
              {(isAdmin || isCreator) && (
                <div className="mt-4">
                  <Button>
                    <UserPlusIcon className="mr-1.5 h-5 w-5" />
                    Invite Members
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
