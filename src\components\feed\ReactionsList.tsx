"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { HandThumbUpIcon, HandThumbDownIcon } from "@heroicons/react/24/solid";
import { cn } from "@/lib/utils";

interface User {
  id: string;
  name: string;
  username?: string | null;
  image?: string | null;
}

interface Reaction {
  id: string;
  type: 'like' | 'dislike';
  createdAt: string;
  user: User;
}

interface ReactionsData {
  likes: Reaction[];
  dislikes: Reaction[];
  total: number;
}

interface ReactionsListProps {
  postId: string;
  maxDisplay?: number;
  showType?: 'all' | 'like' | 'dislike';
  className?: string;
}

export function ReactionsList({ 
  postId, 
  maxDisplay = 3, 
  showType = 'all',
  className = "" 
}: ReactionsListProps) {
  const [reactions, setReactions] = useState<ReactionsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReactions = async () => {
      try {
        setLoading(true);
        const url = showType !== 'all' 
          ? `/api/posts/${postId}/reactions?type=${showType}`
          : `/api/posts/${postId}/reactions`;
        
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error('Failed to fetch reactions');
        }

        const data = await response.json();
        setReactions(data.reactions);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchReactions();
  }, [postId, showType]);

  if (loading) {
    return (
      <div className={cn("flex items-center space-x-1", className)}>
        <div className="animate-pulse flex space-x-1">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-6 w-6 bg-gray-200 rounded-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error || !reactions) {
    return null;
  }

  // Check if there are any reactions
  if (!reactions || (reactions.likes.length === 0 && reactions.dislikes.length === 0)) {
    return null;
  }

  // Combine and sort reactions by creation time
  const allReactions = [...reactions.likes, ...reactions.dislikes]
    .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
    .slice(0, maxDisplay);

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      <div className="flex -space-x-1">
        {allReactions.map((reaction) => (
          <div key={reaction.id} className="relative group">
            <Link href={`/user/${reaction.user.username || reaction.user.id}`}>
              <div className="relative">
                {reaction.user.image ? (
                  <Image
                    src={reaction.user.image}
                    alt={reaction.user.name}
                    width={24}
                    height={24}
                    className="h-6 w-6 rounded-full border-2 border-white hover:scale-110 transition-transform duration-200"
                  />
                ) : (
                  <div className="h-6 w-6 rounded-full border-2 border-white bg-gray-300 flex items-center justify-center hover:scale-110 transition-transform duration-200">
                    <span className="text-xs font-medium text-gray-600">
                      {reaction.user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                
                {/* Reaction type indicator */}
                <div className={cn(
                  "absolute -bottom-1 -right-1 h-3 w-3 rounded-full border border-white flex items-center justify-center",
                  reaction.type === 'like' 
                    ? "bg-blue-500" 
                    : "bg-red-500"
                )}>
                  {reaction.type === 'like' ? (
                    <HandThumbUpIcon className="h-2 w-2 text-white" />
                  ) : (
                    <HandThumbDownIcon className="h-2 w-2 text-white" />
                  )}
                </div>
              </div>
            </Link>
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
              {reaction.user.name} {reaction.type === 'like' ? 'liked' : 'disliked'} this
            </div>
          </div>
        ))}
      </div>
      
      {reactions.total > maxDisplay && (
        <span className="text-xs text-gray-500 ml-2">
          +{reactions.total - maxDisplay} more
        </span>
      )}
    </div>
  );
}
