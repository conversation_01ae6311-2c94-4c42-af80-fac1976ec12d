import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { CommandLineIcon } from "@heroicons/react/24/outline";

export default async function KeyboardShortcutsPage() {
  await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex items-center mb-6">
          <CommandLineIcon className="h-8 w-8 text-blue-600 mr-3" />
          <h1 className="text-2xl font-bold text-gray-900">
            Keyboard Shortcuts
          </h1>
        </div>

        <p className="mb-8 text-gray-600">
          Use these keyboard shortcuts to navigate HIFNF more efficiently. Keyboard shortcuts help you save time and enhance your experience.
        </p>

        {/* Navigation Shortcuts */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Navigation
          </h2>
          
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Go to News Feed</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">g</kbd>
                  <span className="text-gray-400">+</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">h</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Go to Friends</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">g</kbd>
                  <span className="text-gray-400">+</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">f</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Go to Messages</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">g</kbd>
                  <span className="text-gray-400">+</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">m</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Go to Notifications</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">g</kbd>
                  <span className="text-gray-400">+</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">n</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Go to Profile</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">g</kbd>
                  <span className="text-gray-400">+</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">p</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Go to Settings</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">g</kbd>
                  <span className="text-gray-400">+</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">s</kbd>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* News Feed Shortcuts */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            News Feed
          </h2>
          
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Create new post</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">p</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Like post</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">l</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Comment on post</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">c</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Share post</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">s</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Save post</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">b</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Scroll to next post</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">j</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Scroll to previous post</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">k</kbd>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Messages Shortcuts */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Messages
          </h2>
          
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">New message</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">n</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Search messages</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">/</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Next conversation</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">↓</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Previous conversation</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">↑</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Send message</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">Enter</kbd>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* General Shortcuts */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            General
          </h2>
          
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Search</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">/</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Help</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">?</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Close dialogs</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">Esc</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Refresh page</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">r</kbd>
                </div>
                
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Show this shortcuts page</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">Shift</kbd>
                  <span className="text-gray-400">+</span>
                  <kbd className="inline-flex items-center rounded border border-gray-200 px-2 py-1 text-xs font-medium text-gray-400 bg-gray-50">/</kbd>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Note: Keyboard shortcuts may vary depending on your browser and operating system.</p>
        </div>
      </div>
    </MainLayout>
  );
}
