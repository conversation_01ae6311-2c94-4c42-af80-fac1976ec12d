"use client";

// Haptic feedback utility for mobile devices
export class HapticFeedback {
  private static isSupported(): boolean {
    return typeof window !== 'undefined' && 'vibrate' in navigator;
  }

  private static isIOS(): boolean {
    return typeof window !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  private static isAndroid(): boolean {
    return typeof window !== 'undefined' && /Android/.test(navigator.userAgent);
  }

  /**
   * Light haptic feedback for subtle interactions
   */
  static light(): void {
    if (!this.isSupported()) return;

    try {
      // iOS-style light feedback
      if (this.isIOS()) {
        navigator.vibrate(10);
      }
      // Android-style light feedback
      else if (this.isAndroid()) {
        navigator.vibrate(25);
      }
      // Generic light feedback
      else {
        navigator.vibrate(15);
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Medium haptic feedback for standard interactions
   */
  static medium(): void {
    if (!this.isSupported()) return;

    try {
      // iOS-style medium feedback
      if (this.isIOS()) {
        navigator.vibrate(20);
      }
      // Android-style medium feedback
      else if (this.isAndroid()) {
        navigator.vibrate(50);
      }
      // Generic medium feedback
      else {
        navigator.vibrate(30);
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Heavy haptic feedback for important interactions
   */
  static heavy(): void {
    if (!this.isSupported()) return;

    try {
      // iOS-style heavy feedback
      if (this.isIOS()) {
        navigator.vibrate(40);
      }
      // Android-style heavy feedback
      else if (this.isAndroid()) {
        navigator.vibrate(100);
      }
      // Generic heavy feedback
      else {
        navigator.vibrate(60);
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Success haptic feedback
   */
  static success(): void {
    if (!this.isSupported()) return;

    try {
      // Double tap pattern for success
      navigator.vibrate([30, 50, 30]);
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Error haptic feedback
   */
  static error(): void {
    if (!this.isSupported()) return;

    try {
      // Triple tap pattern for error
      navigator.vibrate([50, 100, 50, 100, 50]);
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Warning haptic feedback
   */
  static warning(): void {
    if (!this.isSupported()) return;

    try {
      // Long-short pattern for warning
      navigator.vibrate([100, 50, 30]);
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Selection haptic feedback for UI selections
   */
  static selection(): void {
    if (!this.isSupported()) return;

    try {
      // Very light feedback for selections
      navigator.vibrate(5);
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Custom haptic pattern
   */
  static custom(pattern: number | number[]): void {
    if (!this.isSupported()) return;

    try {
      navigator.vibrate(pattern);
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }
}

// Convenience functions
export const haptic = {
  light: () => HapticFeedback.light(),
  medium: () => HapticFeedback.medium(),
  heavy: () => HapticFeedback.heavy(),
  success: () => HapticFeedback.success(),
  error: () => HapticFeedback.error(),
  warning: () => HapticFeedback.warning(),
  selection: () => HapticFeedback.selection(),
  custom: (pattern: number | number[]) => HapticFeedback.custom(pattern)
};
