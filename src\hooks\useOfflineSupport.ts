"use client";

import { useState, useEffect, useCallback } from 'react';

interface OfflineData {
  posts: any[];
  lastSync: number;
  pendingActions: Array<{
    id: string;
    type: 'like' | 'dislike' | 'comment' | 'share' | 'post';
    data: any;
    timestamp: number;
  }>;
}

export function useOfflineSupport() {
  const [isOnline, setIsOnline] = useState(true);
  const [offlineData, setOfflineData] = useState<OfflineData>({
    posts: [],
    lastSync: 0,
    pendingActions: []
  });

  // Check online status
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Initial check
    updateOnlineStatus();

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // Load offline data from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem('hifnf_offline_data');
      if (stored) {
        setOfflineData(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Failed to load offline data:', error);
    }
  }, []);

  // Save offline data to localStorage
  const saveOfflineData = useCallback((data: OfflineData) => {
    try {
      localStorage.setItem('hifnf_offline_data', JSON.stringify(data));
      setOfflineData(data);
    } catch (error) {
      console.error('Failed to save offline data:', error);
    }
  }, []);

  // Cache posts for offline viewing
  const cachePosts = useCallback((posts: any[]) => {
    const newData = {
      ...offlineData,
      posts: posts.slice(0, 50), // Cache only recent 50 posts
      lastSync: Date.now()
    };
    saveOfflineData(newData);
  }, [offlineData, saveOfflineData]);

  // Add pending action when offline
  const addPendingAction = useCallback((type: string, data: any) => {
    if (isOnline) return; // Don't queue if online

    const action = {
      id: `${type}_${Date.now()}_${Math.random()}`,
      type: type as any,
      data,
      timestamp: Date.now()
    };

    const newData = {
      ...offlineData,
      pendingActions: [...offlineData.pendingActions, action]
    };
    saveOfflineData(newData);
  }, [isOnline, offlineData, saveOfflineData]);

  // Sync pending actions when back online
  const syncPendingActions = useCallback(async () => {
    if (!isOnline || offlineData.pendingActions.length === 0) return;

    const actionsToSync = [...offlineData.pendingActions];
    
    try {
      // Process each pending action
      for (const action of actionsToSync) {
        try {
          // Here you would make the actual API calls
          // For now, we'll just simulate success
          console.log('Syncing action:', action);
          
          // Remove successful action from pending
          const newData = {
            ...offlineData,
            pendingActions: offlineData.pendingActions.filter(a => a.id !== action.id)
          };
          saveOfflineData(newData);
        } catch (error) {
          console.error('Failed to sync action:', action, error);
          // Keep failed actions in queue for retry
        }
      }
    } catch (error) {
      console.error('Failed to sync pending actions:', error);
    }
  }, [isOnline, offlineData, saveOfflineData]);

  // Auto-sync when coming back online
  useEffect(() => {
    if (isOnline && offlineData.pendingActions.length > 0) {
      syncPendingActions();
    }
  }, [isOnline, syncPendingActions, offlineData.pendingActions.length]);

  // Get cached posts for offline viewing
  const getCachedPosts = useCallback(() => {
    return offlineData.posts;
  }, [offlineData.posts]);

  // Check if data is stale
  const isDataStale = useCallback(() => {
    const staleThreshold = 30 * 60 * 1000; // 30 minutes
    return Date.now() - offlineData.lastSync > staleThreshold;
  }, [offlineData.lastSync]);

  // Clear offline data
  const clearOfflineData = useCallback(() => {
    const emptyData: OfflineData = {
      posts: [],
      lastSync: 0,
      pendingActions: []
    };
    saveOfflineData(emptyData);
  }, [saveOfflineData]);

  return {
    isOnline,
    offlineData,
    cachePosts,
    addPendingAction,
    syncPendingActions,
    getCachedPosts,
    isDataStale,
    clearOfflineData,
    hasPendingActions: offlineData.pendingActions.length > 0
  };
}
