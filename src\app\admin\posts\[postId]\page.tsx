"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  FlagIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  UserIcon,
  ClockIcon,
  GlobeAltIcon,
  MapPinIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  ShareIcon,
} from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";
import { formatTimeAgo } from "@/lib/utils";
import { DetailedTime } from "@/components/ui/TimeDisplay";

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  };
  _count: {
    likes: number;
    comments: number;
    shares: number;
  };
  reported?: boolean;
  reports?: {
    id: string;
    reason: string;
    details: string;
    createdAt: string;
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  }[];
  comments?: {
    id: string;
    content: string;
    createdAt: string;
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  }[];
}

export default function AdminPostDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [post, setPost] = useState<Post | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("details");

  useEffect(() => {
    fetchPost();
  }, []);

  const fetchPost = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/posts/${params?.postId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch post");
      }

      const data = await response.json();
      setPost(data);
    } catch (error) {
      console.error("Error fetching post:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeletePost = async () => {
    if (!confirm("Are you sure you want to delete this post?")) return;

    try {
      const response = await fetch(`/api/admin/posts/${post?.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete post");
      }

      alert("Post deleted successfully");
      router.push("/admin/posts");
    } catch (error) {
      console.error("Error deleting post:", error);
      alert("Failed to delete post");
    }
  };

  const handleApprovePost = async () => {
    try {
      const response = await fetch(`/api/admin/posts/${post?.id}/moderate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "approve",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to approve post");
      }

      setPost(post ? { ...post, reported: false } : null);
      alert("Post approved successfully");
    } catch (error) {
      console.error("Error approving post:", error);
      alert("Failed to approve post");
    }
  };

  const handleRejectPost = async () => {
    if (!confirm("Are you sure you want to reject this post?")) return;

    try {
      const response = await fetch(`/api/admin/posts/${post?.id}/moderate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "reject",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to reject post");
      }

      alert("Post rejected successfully");
      router.push("/admin/posts");
    } catch (error) {
      console.error("Error rejecting post:", error);
      alert("Failed to reject post");
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm("Are you sure you want to delete this comment?")) return;

    try {
      // In a real implementation, this would be an API call
      if (post && post.comments) {
        setPost({
          ...post,
          comments: post.comments.filter(comment => comment.id !== commentId),
        });
      }
      alert("Comment deleted successfully");
    } catch (error) {
      console.error("Error deleting comment:", error);
      alert("Failed to delete comment");
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!post) {
    return (
      <AdminLayout>
        <div className="flex h-64 flex-col items-center justify-center">
          <p className="text-lg font-medium text-gray-900">Post not found</p>
          <Button className="mt-4" onClick={() => router.push("/admin/posts")}>
            Back to Posts
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="outline"
            className="mr-4"
            onClick={() => router.push("/admin/posts")}
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Posts
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Post Details</h1>
        </div>
        <div className="flex space-x-2">
          <Link href={`/admin/posts/${post.id}/edit`}>
            <Button variant="outline">
              <PencilIcon className="mr-2 h-5 w-5" />
              Edit
            </Button>
          </Link>
          <Button variant="danger" onClick={handleDeletePost}>
            <TrashIcon className="mr-2 h-5 w-5" />
            Delete
          </Button>
          {post.reported && (
            <>
              <Button variant="primary" onClick={handleApprovePost}>
                <CheckCircleIcon className="mr-2 h-5 w-5" />
                Approve
              </Button>
              <Button variant="danger" onClick={handleRejectPost}>
                <XCircleIcon className="mr-2 h-5 w-5" />
                Reject
              </Button>
            </>
          )}
        </div>
      </div>

      {post.reported && (
        <div className="mb-6 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                This post has been reported {post.reports?.length} times
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  Please review this post for potential violations of community guidelines.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setActiveTab("details")}
            className={`${
              activeTab === "details"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
            } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
          >
            Post Details
          </button>
          <button
            onClick={() => setActiveTab("comments")}
            className={`${
              activeTab === "comments"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
            } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
          >
            Comments ({post._count.comments})
          </button>
          {post.reported && (
            <button
              onClick={() => setActiveTab("reports")}
              className={`${
                activeTab === "reports"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
              } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
            >
              Reports ({post.reports?.length})
            </button>
          )}
        </nav>
      </div>

      {/* Tab content */}
      {activeTab === "details" && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Post content */}
          <div className="lg:col-span-2">
            <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
              <div className="p-6">
                {/* Post header */}
                <div className="mb-4 flex items-center">
                  <div className="h-10 w-10 flex-shrink-0">
                    {post.user.image ? (
                      <Image
                        src={post.user.image}
                        alt={post.user.name}
                        width={40}
                        height={40}
                        className="h-10 w-10 rounded-full"
                      />
                    ) : (
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                        <span className="text-sm font-medium text-gray-500">
                          {post.user.name.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">
                      {post.user.name}
                      {post.user.username && (
                        <span className="ml-1 text-gray-500">@{post.user.username}</span>
                      )}
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <ClockIcon className="mr-1 h-3 w-3" />
                      <DetailedTime
                        date={post.createdAt}
                        autoUpdate={false}
                      />
                      <span className="mx-1">•</span>
                      <GlobeAltIcon className="mr-1 h-3 w-3" />
                      {post.privacy}
                      {post.location && (
                        <>
                          <span className="mx-1">•</span>
                          <MapPinIcon className="mr-1 h-3 w-3" />
                          {post.location}
                        </>
                      )}
                      {post.feeling && (
                        <>
                          <span className="mx-1">•</span>
                          Feeling {post.feeling}
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Post content */}
                <div className="mb-4">
                  <p className="text-gray-900">{post.content}</p>
                </div>

                {/* Post images */}
                {post.images && post.images.length > 0 && (
                  <div className="mb-4 grid grid-cols-1 gap-2">
                    {post.images.map((image, index) => (
                      <div key={index} className="overflow-hidden rounded-lg">
                        <OptimizedImage
                          src={image}
                          alt={`Post image ${index + 1}`}
                          width={800}
                          height={600}
                          className="h-auto w-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}

                {/* Post videos */}
                {post.videos && post.videos.length > 0 && (
                  <div className="mb-4 grid grid-cols-1 gap-2">
                    {post.videos.map((video, index) => (
                      <div key={index} className="overflow-hidden rounded-lg">
                        <video
                          src={video}
                          controls
                          className="h-auto w-full"
                        />
                      </div>
                    ))}
                  </div>
                )}

                {/* Post engagement */}
                <div className="flex items-center justify-between border-t border-gray-200 pt-4">
                  <div className="flex space-x-4">
                    <span className="flex items-center text-sm text-gray-500">
                      <HeartIcon className="mr-1.5 h-5 w-5 text-red-500" />
                      {post._count.likes} likes
                    </span>
                    <span className="flex items-center text-sm text-gray-500">
                      <ChatBubbleLeftIcon className="mr-1.5 h-5 w-5 text-blue-500" />
                      {post._count.comments} comments
                    </span>
                    <span className="flex items-center text-sm text-gray-500">
                      <ShareIcon className="mr-1.5 h-5 w-5 text-green-500" />
                      {post._count.shares} shares
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Post metadata */}
          <div>
            <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
              <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
                <h3 className="text-sm font-medium text-gray-900">Post Information</h3>
              </div>
              <div className="px-4 py-5 sm:p-6">
                <dl className="grid grid-cols-1 gap-x-4 gap-y-6">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Post ID</dt>
                    <dd className="mt-1 text-sm text-gray-900">{post.id}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(post.createdAt).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Author</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <Link href={`/admin/users/${post.user.id}`} className="text-blue-600 hover:text-blue-900">
                        {post.user.name} ({post.user.id})
                      </Link>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Privacy</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                          post.privacy === "public"
                            ? "bg-green-100 text-green-800"
                            : post.privacy === "friends"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {post.privacy}
                      </span>
                    </dd>
                  </div>
                  {post.location && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Location</dt>
                      <dd className="mt-1 text-sm text-gray-900">{post.location}</dd>
                    </div>
                  )}
                  {post.feeling && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Feeling</dt>
                      <dd className="mt-1 text-sm text-gray-900">{post.feeling}</dd>
                    </div>
                  )}
                  {post.activity && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Activity</dt>
                      <dd className="mt-1 text-sm text-gray-900">{post.activity}</dd>
                    </div>
                  )}
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Status</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {post.reported ? (
                        <span className="inline-flex rounded-full bg-red-100 px-2 text-xs font-semibold leading-5 text-red-800">
                          Reported
                        </span>
                      ) : (
                        <span className="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">
                          Active
                        </span>
                      )}
                    </dd>
                  </div>
                </dl>
              </div>
              <div className="border-t border-gray-200 px-4 py-4">
                <Link href={`/posts/${post.id}`} target="_blank" className="text-sm font-medium text-blue-600 hover:text-blue-900">
                  View on site →
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === "comments" && (
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
            <h3 className="text-sm font-medium text-gray-900">Comments ({post._count.comments})</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {post.comments && post.comments.length > 0 ? (
              post.comments.map((comment) => (
                <div key={comment.id} className="p-4">
                  <div className="flex">
                    <div className="mr-3 flex-shrink-0">
                      {comment.user.image ? (
                        <Image
                          src={comment.user.image}
                          alt={comment.user.name}
                          width={40}
                          height={40}
                          className="h-10 w-10 rounded-full"
                        />
                      ) : (
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                          <span className="text-sm font-medium text-gray-500">
                            {comment.user.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex-grow">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium text-gray-900">{comment.user.name}</span>
                          <span className="ml-2 text-xs text-gray-500">
                            {formatTimeAgo(comment.createdAt)}
                          </span>
                        </div>
                        <button
                          onClick={() => handleDeleteComment(comment.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Comment"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                      <p className="mt-1 text-sm text-gray-700">{comment.content}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="flex h-32 items-center justify-center p-4">
                <p className="text-sm text-gray-500">No comments on this post</p>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === "reports" && post.reported && (
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
            <h3 className="text-sm font-medium text-gray-900">Reports ({post.reports?.length})</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {post.reports && post.reports.length > 0 ? (
              post.reports.map((report) => (
                <div key={report.id} className="p-4">
                  <div className="flex">
                    <div className="mr-3 flex-shrink-0">
                      {report.user.image ? (
                        <Image
                          src={report.user.image}
                          alt={report.user.name}
                          width={40}
                          height={40}
                          className="h-10 w-10 rounded-full"
                        />
                      ) : (
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                          <span className="text-sm font-medium text-gray-500">
                            {report.user.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex-grow">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium text-gray-900">{report.user.name}</span>
                          <span className="ml-2 text-xs text-gray-500">
                            {formatTimeAgo(report.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="mt-1">
                        <span className="inline-flex rounded-full bg-red-100 px-2 text-xs font-semibold leading-5 text-red-800">
                          {report.reason}
                        </span>
                      </div>
                      <p className="mt-2 text-sm text-gray-700">{report.details}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="flex h-32 items-center justify-center p-4">
                <p className="text-sm text-gray-500">No reports found</p>
              </div>
            )}
          </div>
          <div className="border-t border-gray-200 bg-gray-50 px-4 py-3">
            <div className="flex justify-end space-x-2">
              <Button variant="primary" onClick={handleApprovePost}>
                <CheckCircleIcon className="mr-2 h-5 w-5" />
                Approve Post
              </Button>
              <Button variant="danger" onClick={handleRejectPost}>
                <XCircleIcon className="mr-2 h-5 w-5" />
                Reject Post
              </Button>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
