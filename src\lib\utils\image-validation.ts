/**
 * Image validation utilities for file uploads
 */

export interface ImageValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

export interface ImageValidationOptions {
  maxSizeBytes?: number;
  allowedTypes?: string[];
  maxWidth?: number;
  maxHeight?: number;
  minWidth?: number;
  minHeight?: number;
}

const DEFAULT_OPTIONS: Required<ImageValidationOptions> = {
  maxSizeBytes: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  maxWidth: 4096,
  maxHeight: 4096,
  minWidth: 50,
  minHeight: 50,
};

/**
 * Validates a single image file
 * @param file The file to validate
 * @param options Validation options
 * @returns Validation result
 */
export async function validateImageFile(
  file: File,
  options: ImageValidationOptions = {}
): Promise<ImageValidationResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const warnings: string[] = [];

  // Check if file exists
  if (!file) {
    return {
      isValid: false,
      error: 'No file provided'
    };
  }

  // Check file size
  if (file.size === 0) {
    return {
      isValid: false,
      error: 'File is empty'
    };
  }

  if (file.size > opts.maxSizeBytes) {
    return {
      isValid: false,
      error: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(opts.maxSizeBytes)})`
    };
  }

  // Check file type
  if (!opts.allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type "${file.type}" is not allowed. Allowed types: ${opts.allowedTypes.join(', ')}`
    };
  }

  // Check file name
  if (!file.name || file.name.trim().length === 0) {
    warnings.push('File has no name');
  }

  // Validate image dimensions (if possible)
  try {
    const dimensions = await getImageDimensions(file);
    
    if (dimensions.width > opts.maxWidth || dimensions.height > opts.maxHeight) {
      warnings.push(`Image dimensions (${dimensions.width}x${dimensions.height}) exceed recommended maximum (${opts.maxWidth}x${opts.maxHeight})`);
    }
    
    if (dimensions.width < opts.minWidth || dimensions.height < opts.minHeight) {
      return {
        isValid: false,
        error: `Image dimensions (${dimensions.width}x${dimensions.height}) are below minimum required (${opts.minWidth}x${opts.minHeight})`
      };
    }
  } catch (error) {
    warnings.push('Could not validate image dimensions');
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Validates multiple image files
 * @param files Array of files to validate
 * @param options Validation options
 * @returns Array of validation results
 */
export async function validateImageFiles(
  files: File[],
  options: ImageValidationOptions = {}
): Promise<ImageValidationResult[]> {
  if (!files || files.length === 0) {
    return [];
  }

  const validationPromises = files.map(file => validateImageFile(file, options));
  return Promise.all(validationPromises);
}

/**
 * Gets image dimensions from a file
 * @param file The image file
 * @returns Promise with width and height
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
}

/**
 * Formats file size in human readable format
 * @param bytes File size in bytes
 * @returns Formatted string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Checks if all validation results are valid
 * @param results Array of validation results
 * @returns True if all are valid
 */
export function areAllValidationResultsValid(results: ImageValidationResult[]): boolean {
  return results.every(result => result.isValid);
}

/**
 * Gets all validation errors from results
 * @param results Array of validation results
 * @returns Array of error messages
 */
export function getValidationErrors(results: ImageValidationResult[]): string[] {
  return results
    .filter(result => !result.isValid && result.error)
    .map(result => result.error!);
}

/**
 * Gets all validation warnings from results
 * @param results Array of validation results
 * @returns Array of warning messages
 */
export function getValidationWarnings(results: ImageValidationResult[]): string[] {
  return results
    .filter(result => result.warnings && result.warnings.length > 0)
    .flatMap(result => result.warnings!);
}
