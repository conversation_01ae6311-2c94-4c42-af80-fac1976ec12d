import { NextResponse } from "next/server";
import { executeQuery } from "@/lib/db/mysql";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import bcrypt from "bcryptjs";
import { ReferralService } from "@/lib/referral/referralService";

const registerSchema = z.object({
  name: z.string().min(2),
  username: z.string().min(3).max(30).regex(/^[a-zA-Z0-9_]+$/),
  email: z.string().email(),
  dateOfBirth: z.string().min(1), // Make it required to match frontend
  password: z.string().min(6),
  referralCode: z.string().optional(), // Simple optional string
});

export async function POST(req: Request) {
  try {
    const body = await req.json();

    // Preprocess data to handle null values
    const processedBody = {
      ...body,
      referralCode: body.referralCode === null ? undefined : body.referralCode
    };

    const { name, username, email, dateOfBirth, password, referralCode } = registerSchema.parse(processedBody);

    // Check if user with this email already exists
    const existingEmailUsers = await executeQuery<{ id: string; email: string }[]>(
      "SELECT * FROM users WHERE email = ?",
      [email]
    );

    if (existingEmailUsers.length > 0) {
      return NextResponse.json(
        { message: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Check if user with this username already exists
    const existingUsernameUsers = await executeQuery<{ id: string; username: string }[]>(
      "SELECT * FROM users WHERE username = ?",
      [username]
    );

    if (existingUsernameUsers.length > 0) {
      return NextResponse.json(
        { message: "This username is already taken" },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const userId = uuidv4();

    // Convert dateOfBirth to MySQL datetime format if provided
    const birthdayValue = dateOfBirth ? new Date(dateOfBirth).toISOString().slice(0, 19).replace('T', ' ') : null;

    await executeQuery(
      "INSERT INTO users (id, name, username, email, birthday, password, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())",
      [userId, name, username, email, birthdayValue, hashedPassword]
    );

    // Process referral if provided
    if (referralCode) {
      try {
        await ReferralService.processReferralSignup(referralCode, userId);
      } catch (error) {
        console.error("Error processing referral:", error);
        // Don't fail registration if referral processing fails
      }
    }

    return NextResponse.json(
      { message: "User registered successfully" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Registration error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    // More detailed error message for debugging
    const errorMessage = error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { message: "Internal server error", error: errorMessage },
      { status: 500 }
    );
  }
}
