import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { SubscriptionService } from "@/lib/subscription/subscriptionService";
import { z } from "zod";

const createSubscriptionSchema = z.object({
  planId: z.string(),
  paymentMethod: z.string().optional(),
  billingCycle: z.enum(['monthly', 'yearly']).optional(),
  skipPayment: z.boolean().optional(),
});

const cancelSubscriptionSchema = z.object({
  subscriptionId: z.string(),
  reason: z.string().optional(),
});

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const subscriptionStatus = await SubscriptionService.getUserSubscriptionStatus(session.user.id);

    return NextResponse.json({
      success: true,
      data: subscriptionStatus,
    });
  } catch (error) {
    console.error("Error fetching user subscription:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { planId, paymentMethod, billingCycle, skipPayment } = createSubscriptionSchema.parse(body);

    // Check if user already has an active subscription
    const currentStatus = await SubscriptionService.getUserSubscriptionStatus(session.user.id);
    
    if (currentStatus.hasSubscription && currentStatus.isActive && currentStatus.subscription) {
      return NextResponse.json(
        { message: "User already has an active subscription. Use upgrade endpoint to change plans." },
        { status: 400 }
      );
    }

    const subscriptionId = await SubscriptionService.createSubscription({
      userId: session.user.id,
      planId,
      paymentMethod,
      billingCycle,
    });

    // If skipPayment is true (for free plans), activate subscription immediately
    if (skipPayment) {
      await SubscriptionService.activateSubscription(subscriptionId);

      return NextResponse.json({
        success: true,
        data: {
          subscriptionId,
          message: "Subscription activated successfully.",
          activated: true,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        subscriptionId,
        message: "Subscription created successfully. Please complete payment to activate.",
      },
    });
  } catch (error) {
    console.error("Error creating subscription:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { subscriptionId, reason } = cancelSubscriptionSchema.parse(body);

    // Verify the subscription belongs to the user
    const subscription = await SubscriptionService.getUserSubscription(session.user.id);
    
    if (!subscription || subscription.id !== subscriptionId) {
      return NextResponse.json(
        { message: "Subscription not found or access denied" },
        { status: 404 }
      );
    }

    await SubscriptionService.cancelSubscription(subscriptionId, reason);

    return NextResponse.json({
      success: true,
      message: "Subscription cancelled successfully",
    });
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
