import React from 'react';
import Link from 'next/link';
import { LinkPreview } from '@/components/ui/LinkPreview';

// Enhanced URL regex that matches various URL formats
const URL_REGEX = /(https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*)?(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)/gi;

// Simple URL regex for basic detection
const SIMPLE_URL_REGEX = /(https?:\/\/[^\s]+)/g;

export interface ParsedContent {
  type: 'text' | 'link' | 'mention';
  content: string;
  url?: string;
}

/**
 * Parse text content and identify URLs, mentions, etc.
 */
export function parseTextContent(text: string): ParsedContent[] {
  const parts: ParsedContent[] = [];
  let lastIndex = 0;

  // First, handle mentions (updated to match mention-utils.tsx regex)
  const mentionRegex = /@([a-zA-Z0-9_]+)/g;
  let match;

  // Create a copy of text to work with
  let workingText = text;
  const mentions: { start: number; end: number; username: string }[] = [];

  // Find all mentions
  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push({
      start: match.index,
      end: match.index + match[0].length,
      username: match[1]
    });
  }

  // Find all URLs
  const urls: { start: number; end: number; url: string }[] = [];
  const urlRegex = new RegExp(URL_REGEX);
  
  while ((match = urlRegex.exec(text)) !== null) {
    urls.push({
      start: match.index,
      end: match.index + match[0].length,
      url: match[0]
    });
  }

  // Combine and sort all matches by position
  const allMatches = [
    ...mentions.map(m => ({ ...m, type: 'mention' as const })),
    ...urls.map(u => ({ ...u, type: 'url' as const }))
  ].sort((a, b) => a.start - b.start);

  lastIndex = 0;

  for (const match of allMatches) {
    // Add text before the match
    if (match.start > lastIndex) {
      const textBefore = text.slice(lastIndex, match.start);
      if (textBefore) {
        parts.push({
          type: 'text',
          content: textBefore
        });
      }
    }

    // Add the match
    if (match.type === 'mention') {
      parts.push({
        type: 'mention',
        content: `@${(match as any).username}`
      });
    } else if (match.type === 'url') {
      parts.push({
        type: 'link',
        content: (match as any).url,
        url: (match as any).url
      });
    }

    lastIndex = match.end;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    const remainingText = text.slice(lastIndex);
    if (remainingText) {
      parts.push({
        type: 'text',
        content: remainingText
      });
    }
  }

  return parts;
}

/**
 * Render parsed content with clickable links and mentions
 */
export function renderParsedContent(
  parts: ParsedContent[], 
  options: {
    showLinkPreviews?: boolean;
    linkClassName?: string;
    mentionClassName?: string;
  } = {}
): React.ReactNode[] {
  const {
    showLinkPreviews = false,
    linkClassName = "text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200",
    mentionClassName = "text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200 cursor-pointer"
  } = options;

  const elements: React.ReactNode[] = [];
  const linkPreviews: string[] = [];

  parts.forEach((part, index) => {
    switch (part.type) {
      case 'text':
        elements.push(
          <span key={`text-${index}`}>
            {part.content}
          </span>
        );
        break;

      case 'link':
        if (part.url) {
          elements.push(
            <Link
              key={`link-${index}`}
              href={part.url}
              target="_blank"
              rel="noopener noreferrer"
              className={linkClassName}
              onClick={(e) => e.stopPropagation()}
            >
              {part.content}
            </Link>
          );

          // Collect unique URLs for previews
          if (showLinkPreviews && !linkPreviews.includes(part.url)) {
            linkPreviews.push(part.url);
          }
        }
        break;

      case 'mention':
        const username = part.content.slice(1); // Remove @ symbol
        elements.push(
          <Link
            key={`mention-${index}`}
            href={`/user/${encodeURIComponent(username)}`}
            className={`${mentionClassName} cursor-pointer relative z-10`}
            onClick={(e) => e.stopPropagation()}
          >
            {part.content}
          </Link>
        );
        break;

      default:
        elements.push(
          <span key={`unknown-${index}`}>
            {part.content}
          </span>
        );
    }
  });

  // Add link previews if requested
  if (showLinkPreviews && linkPreviews.length > 0) {
    linkPreviews.forEach((url, index) => {
      elements.push(
        <LinkPreview
          key={`preview-${index}`}
          url={url}
          className="mt-3"
        />
      );
    });
  }

  return elements;
}

/**
 * Simple function to detect if text contains URLs
 */
export function containsUrls(text: string): boolean {
  return SIMPLE_URL_REGEX.test(text);
}

/**
 * Extract all URLs from text
 */
export function extractUrls(text: string): string[] {
  const matches = text.match(SIMPLE_URL_REGEX);
  return matches || [];
}

/**
 * Check if a string is a valid URL
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Normalize URL (add https if missing)
 */
export function normalizeUrl(url: string): string {
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }
  return url;
}
