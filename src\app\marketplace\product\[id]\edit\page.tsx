import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { products, stores } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { notFound, redirect } from "next/navigation";
import { ProductForm } from "@/components/marketplace/ProductForm";

export default async function EditProductPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const user = await requireAuth();
  const resolvedParams = await params;

  // Fetch the product
  const productDetails = await db
    .select({
      id: products.id,
      title: products.title,
      description: products.description,
      price: products.price,
      condition: products.item_condition,
      category: products.category,
      location: products.location,
      photos: products.photos,
      storeId: products.storeId,
    })
    .from(products)
    .where(eq(products.id, resolvedParams.id))
    .limit(1);

  if (productDetails.length === 0) {
    notFound();
  }

  const product = productDetails[0];

  // Check if user owns the store that has this product
  const storeCheck = await db
    .select({
      id: stores.id,
      name: stores.name,
    })
    .from(stores)
    .where(
      and(
        eq(stores.id, product.storeId),
        eq(stores.ownerId, user.id)
      )
    )
    .limit(1);

  if (storeCheck.length === 0) {
    // User doesn't own this store, redirect to marketplace
    redirect("/marketplace");
  }

  const store = storeCheck[0];

  return (
    <MainLayout>
      <div className="mx-auto max-w-3xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
            Edit Product
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Update your product in {store.name}
          </p>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-6">
            <ProductForm
              storeId={store.id}
              product={{
                id: product.id,
                title: product.title,
                description: product.description,
                price: product.price,
                condition: product.condition,
                category: product.category,
                location: product.location,
                photos: product.photos,
              }}
            />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
