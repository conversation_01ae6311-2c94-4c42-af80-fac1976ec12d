import type { Config } from 'drizzle-kit';
import { defineConfig } from 'drizzle-kit';
import { dbConfig } from './src/lib/config';

export default defineConfig({
  schema: './src/lib/db/schema.ts',
  out: './drizzle',
  dialect: 'mysql',
  dbCredentials: {
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  },
});
