import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePosts, fanPages } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";
import { deleteMediaFiles } from "@/lib/storage/imageStorage";

const updatePostSchema = z.object({
  content: z.string().max(5000).optional(),
  images: z.array(z.string()).optional(),
  videos: z.array(z.string()).optional(),
  type: z.enum(['text', 'image', 'video', 'link', 'event']).optional(),
}).refine(data => {
  // At least one field must be provided for update
  return Object.keys(data).length > 0;
}, {
  message: "At least one field must be provided for update",
});

interface RouteParams {
  params: Promise<{
    postId: string;
  }>;
}

// GET /api/fan-pages/posts/[postId] - Get specific fan page post
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;

    if (!postId || postId.trim() === '') {
      return NextResponse.json(
        { error: "Invalid post ID" },
        { status: 400 }
      );
    }

    // Get post with fan page info
    const postResult = await db
      .select({
        id: fanPagePosts.id,
        content: fanPagePosts.content,
        images: fanPagePosts.images,
        videos: fanPagePosts.videos,
        type: fanPagePosts.type,
        likeCount: fanPagePosts.likeCount,
        commentCount: fanPagePosts.commentCount,
        shareCount: fanPagePosts.shareCount,
        viewCount: fanPagePosts.viewCount,
        createdAt: fanPagePosts.createdAt,
        updatedAt: fanPagePosts.updatedAt,
        fanPage: {
          id: fanPages.id,
          name: fanPages.name,
          username: fanPages.username,
          profileImage: fanPages.profileImage,
          isVerified: fanPages.isVerified,
          ownerId: fanPages.ownerId,
        },
      })
      .from(fanPagePosts)
      .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    if (postResult.length === 0) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      post: postResult[0],
    });

  } catch (error) {
    console.error("Error fetching fan page post:", error);
    return NextResponse.json(
      { error: "Failed to fetch post" },
      { status: 500 }
    );
  }
}

// PATCH /api/fan-pages/posts/[postId] - Update fan page post
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!postId || postId.trim() === '') {
      return NextResponse.json(
        { error: "Invalid post ID" },
        { status: 400 }
      );
    }

    // Check if post exists and get fan page info
    const postResult = await db
      .select({
        id: fanPagePosts.id,
        fanPageId: fanPagePosts.fanPageId,
        fanPage: {
          id: fanPages.id,
          ownerId: fanPages.ownerId,
        },
      })
      .from(fanPagePosts)
      .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    if (postResult.length === 0) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    const post = postResult[0];

    // Check if user is the owner of the fan page
    if (post.fanPage?.ownerId !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to edit this post" },
        { status: 403 }
      );
    }

    const body = await request.json();

    if (process.env.NODE_ENV === 'development') {
      console.log('Fan page post update - Received body:', JSON.stringify(body, null, 2));
    }

    const validatedData = updatePostSchema.parse(body);

    if (process.env.NODE_ENV === 'development') {
      console.log('Fan page post update - Validated data:', JSON.stringify(validatedData, null, 2));
    }

    // Update the post
    await db
      .update(fanPagePosts)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(fanPagePosts.id, postId));

    // Fetch updated post with fan page info
    const updatedPost = await db
      .select({
        id: fanPagePosts.id,
        content: fanPagePosts.content,
        images: fanPagePosts.images,
        videos: fanPagePosts.videos,
        type: fanPagePosts.type,
        likeCount: fanPagePosts.likeCount,
        commentCount: fanPagePosts.commentCount,
        shareCount: fanPagePosts.shareCount,
        viewCount: fanPagePosts.viewCount,
        createdAt: fanPagePosts.createdAt,
        updatedAt: fanPagePosts.updatedAt,
        fanPage: {
          id: fanPages.id,
          name: fanPages.name,
          username: fanPages.username,
          profileImage: fanPages.profileImage,
          isVerified: fanPages.isVerified,
        },
      })
      .from(fanPagePosts)
      .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    return NextResponse.json({
      message: "Post updated successfully",
      post: updatedPost[0],
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      if (process.env.NODE_ENV === 'development') {
        console.log("Fan page post validation errors:", error.errors);
      }
      return NextResponse.json(
        {
          message: "Invalid input data",
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    console.error("Error updating fan page post:", error);
    return NextResponse.json(
      { message: "Failed to update post" },
      { status: 500 }
    );
  }
}

// DELETE /api/fan-pages/posts/[postId] - Delete fan page post
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!postId || postId.trim() === '') {
      return NextResponse.json(
        { error: "Invalid post ID" },
        { status: 400 }
      );
    }

    // Check if post exists and get fan page info with media files
    const postResult = await db
      .select({
        id: fanPagePosts.id,
        fanPageId: fanPagePosts.fanPageId,
        images: fanPagePosts.images,
        videos: fanPagePosts.videos,
        fanPage: {
          id: fanPages.id,
          ownerId: fanPages.ownerId,
          postCount: fanPages.postCount,
        },
      })
      .from(fanPagePosts)
      .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    if (postResult.length === 0) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    const post = postResult[0];

    // Check if user is the owner of the fan page
    if (post.fanPage?.ownerId !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to delete this post" },
        { status: 403 }
      );
    }

    // Delete associated media files from storage before deleting the post
    let mediaDeleteResult = null;
    if (post.images || post.videos) {
      try {
        console.log(`🗑️ Deleting media files for fan page post ${postId}...`);
        mediaDeleteResult = await deleteMediaFiles(
          post.images || [],
          post.videos || []
        );

        if (process.env.NODE_ENV === 'development') {
          console.log('Fan page media deletion result:', mediaDeleteResult);
        }
      } catch (error) {
        console.error('Error deleting fan page media files:', error);
        // Continue with post deletion even if media deletion fails
      }
    }

    // Delete the post from database
    await db
      .delete(fanPagePosts)
      .where(eq(fanPagePosts.id, postId));

    // Update post count
    if (post.fanPage?.postCount && post.fanPage.postCount > 0) {
      await db
        .update(fanPages)
        .set({
          postCount: post.fanPage.postCount - 1,
          updatedAt: new Date(),
        })
        .where(eq(fanPages.id, post.fanPageId));
    }

    // Prepare response with media deletion info
    const response: any = {
      message: "Post deleted successfully",
      postId: postId
    };

    if (mediaDeleteResult) {
      response.mediaDeleted = {
        success: mediaDeleteResult.success,
        deletedCount: mediaDeleteResult.deletedImages.length,
        failedCount: mediaDeleteResult.failedImages.length,
      };

      if (mediaDeleteResult.failedImages.length > 0) {
        console.warn(`⚠️ Some fan page media files could not be deleted:`, mediaDeleteResult.failedImages);
        response.mediaDeleted.warnings = mediaDeleteResult.errors;
      }
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error("Error deleting fan page post:", error);
    return NextResponse.json(
      { error: "Failed to delete post" },
      { status: 500 }
    );
  }
}
