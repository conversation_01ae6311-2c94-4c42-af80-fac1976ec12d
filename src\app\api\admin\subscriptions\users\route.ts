import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { userSubscriptions, subscriptionPlans, users } from "@/lib/db/schema";
import { eq, and, or, like, desc, asc, count } from "drizzle-orm";
import { z } from "zod";

const querySchema = z.object({
  search: z.string().optional(),
  status: z.enum(['active', 'cancelled', 'expired', 'pending', 'suspended']).optional(),
  planId: z.string().optional(),
  paymentMethod: z.string().optional(),
  sortBy: z.string().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.string().transform(val => parseInt(val) || 1).default('1'),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).default('20'),
});

// GET - Fetch user subscriptions with filtering and pagination
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    // Build where conditions
    const whereConditions = [];

    if (query.status) {
      whereConditions.push(eq(userSubscriptions.status, query.status));
    }

    if (query.planId) {
      whereConditions.push(eq(userSubscriptions.planId, query.planId));
    }

    if (query.paymentMethod) {
      whereConditions.push(eq(userSubscriptions.paymentMethod, query.paymentMethod));
    }

    // Build search conditions for user name/email
    let searchConditions = [];
    if (query.search) {
      searchConditions = [
        like(users.name, `%${query.search}%`),
        like(users.email, `%${query.search}%`),
      ];
    }

    // Determine sort order
    const sortField = query.sortBy === 'user.name' ? users.name :
                     query.sortBy === 'plan.displayName' ? subscriptionPlans.displayName :
                     userSubscriptions.createdAt;
    
    const orderBy = query.sortOrder === 'asc' ? asc(sortField) : desc(sortField);

    // Get total count for pagination
    const totalCountQuery = db
      .select({ count: count() })
      .from(userSubscriptions)
      .innerJoin(users, eq(userSubscriptions.userId, users.id))
      .innerJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id));

    if (whereConditions.length > 0) {
      totalCountQuery.where(and(...whereConditions));
    }

    if (searchConditions.length > 0) {
      if (whereConditions.length > 0) {
        totalCountQuery.where(and(...whereConditions, or(...searchConditions)));
      } else {
        totalCountQuery.where(or(...searchConditions));
      }
    }

    const [{ count: totalCount }] = await totalCountQuery;

    // Get subscriptions with user and plan data
    let subscriptionsQuery = db
      .select({
        id: userSubscriptions.id,
        userId: userSubscriptions.userId,
        planId: userSubscriptions.planId,
        status: userSubscriptions.status,
        startDate: userSubscriptions.startDate,
        endDate: userSubscriptions.endDate,
        nextBillingDate: userSubscriptions.nextBillingDate,
        cancelledAt: userSubscriptions.cancelledAt,
        cancelReason: userSubscriptions.cancelReason,
        autoRenew: userSubscriptions.autoRenew,
        paymentMethod: userSubscriptions.paymentMethod,
        lastPaymentDate: userSubscriptions.lastPaymentDate,
        createdAt: userSubscriptions.createdAt,
        updatedAt: userSubscriptions.updatedAt,
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
        plan: {
          id: subscriptionPlans.id,
          name: subscriptionPlans.name,
          displayName: subscriptionPlans.displayName,
          price: subscriptionPlans.price,
          currency: subscriptionPlans.currency,
          billingCycle: subscriptionPlans.billingCycle,
        },
      })
      .from(userSubscriptions)
      .innerJoin(users, eq(userSubscriptions.userId, users.id))
      .innerJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id))
      .orderBy(orderBy)
      .limit(query.limit)
      .offset((query.page - 1) * query.limit);

    // Apply filters
    if (whereConditions.length > 0 && searchConditions.length > 0) {
      subscriptionsQuery = subscriptionsQuery.where(and(...whereConditions, or(...searchConditions)));
    } else if (whereConditions.length > 0) {
      subscriptionsQuery = subscriptionsQuery.where(and(...whereConditions));
    } else if (searchConditions.length > 0) {
      subscriptionsQuery = subscriptionsQuery.where(or(...searchConditions));
    }

    const subscriptions = await subscriptionsQuery;

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / query.limit);
    const hasNextPage = query.page < totalPages;
    const hasPrevPage = query.page > 1;

    return NextResponse.json({
      success: true,
      subscriptions,
      pagination: {
        currentPage: query.page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit: query.limit,
      },
    });
  } catch (error) {
    console.error("Error fetching user subscriptions:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid query parameters",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch user subscriptions",
      },
      { status: 500 }
    );
  }
}
