import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageSettings, fanPageFollowers } from "@/lib/db/schema";
import { eq, like, desc, count, sql, and, or } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const createFanPageSchema = z.object({
  name: z.string().min(2).max(255),
  username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_]+$/, "Username can only contain letters, numbers, and underscores"),
  category: z.enum([
    'musician', 'actor', 'brand', 'business', 'organization',
    'public_figure', 'artist', 'writer', 'athlete', 'politician',
    'entertainment', 'media', 'community', 'cause', 'other'
  ]),
  description: z.string().max(1000).optional(),
  website: z.string().url().optional().or(z.literal("")),
  email: z.string().email().optional().or(z.literal("")),
  phone: z.string().max(50).optional(),
  location: z.string().max(255).optional(),
});

// GET /api/fan-pages - Get all fan pages with search and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const offset = (page - 1) * limit;

    // Build query with optional isFollowing field
    let query = db.select({
      id: fanPages.id,
      name: fanPages.name,
      username: fanPages.username,
      category: fanPages.category,
      description: fanPages.description,
      profileImage: fanPages.profileImage,
      coverImage: fanPages.coverImage,
      isVerified: fanPages.isVerified,
      followerCount: fanPages.followerCount,
      postCount: fanPages.postCount,
      createdAt: fanPages.createdAt,
      isFollowing: session?.user?.id
        ? sql<boolean>`EXISTS(
            SELECT 1 FROM ${fanPageFollowers}
            WHERE ${fanPageFollowers.fanPageId} = ${fanPages.id}
            AND ${fanPageFollowers.userId} = ${session.user.id}
          )`
        : sql<boolean>`false`,
    }).from(fanPages);

    // Build where conditions
    let whereConditions = [eq(fanPages.isActive, true)];

    // Apply search filters - enhanced search across multiple fields
    if (search) {
      const searchTerm = search.trim();
      whereConditions.push(
        or(
          like(fanPages.name, `%${searchTerm}%`),
          like(fanPages.username, `%${searchTerm}%`),
          like(fanPages.description, `%${searchTerm}%`),
          like(fanPages.location, `%${searchTerm}%`)
        )
      );
    }

    if (category && category !== "all") {
      whereConditions.push(eq(fanPages.category, category as any));
    }

    // Apply all where conditions
    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Apply pagination and ordering
    const pages = await query
      .orderBy(desc(fanPages.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    let totalQuery = db
      .select({ count: count() })
      .from(fanPages);

    if (whereConditions.length > 0) {
      totalQuery = totalQuery.where(and(...whereConditions));
    }

    const totalResult = await totalQuery;

    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      pages,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error("Error fetching fan pages:", error);
    return NextResponse.json(
      { error: "Failed to fetch fan pages" },
      { status: 500 }
    );
  }
}

// POST /api/fan-pages - Create a new fan page
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createFanPageSchema.parse(body);

    // Check if username is already taken
    const existingPage = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.username, validatedData.username))
      .limit(1);

    if (existingPage.length > 0) {
      return NextResponse.json(
        { error: "Username is already taken" },
        { status: 400 }
      );
    }

    // Create the fan page
    const pageId = uuidv4();
    const settingsId = uuidv4();

    await db.insert(fanPages).values({
      id: pageId,
      ownerId: session.user.id,
      name: validatedData.name,
      username: validatedData.username,
      category: validatedData.category,
      description: validatedData.description || null,
      website: validatedData.website || null,
      email: validatedData.email || null,
      phone: validatedData.phone || null,
      location: validatedData.location || null,
    });

    // Create default settings for the page
    await db.insert(fanPageSettings).values({
      id: settingsId,
      fanPageId: pageId,
    });

    // Fetch the created page
    const newPage = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    return NextResponse.json({
      message: "Fan page created successfully",
      page: newPage[0],
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating fan page:", error);
    return NextResponse.json(
      { error: "Failed to create fan page" },
      { status: 500 }
    );
  }
}
