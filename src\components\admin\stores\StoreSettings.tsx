"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Switch } from "@/components/ui/Switch";
import { Select } from "@/components/ui/Select";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import {
  EyeIcon,
  BellAlertIcon,
  ShoppingBagIcon,
  TagIcon,
  GlobeAltIcon,
  EnvelopeIcon,
} from "@heroicons/react/24/outline";

interface StoreSettings {
  id: string;
  storeId: string;
  visibility: "public" | "private";
  showOutOfStock: boolean;
  showProductViews: boolean;
  emailNotifications: boolean;
  productViewNotifications: boolean;
  createdAt: string;
  updatedAt: string;
}

interface StoreSettingsProps {
  storeId: string;
}

export function StoreSettings({ storeId }: StoreSettingsProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [settings, setSettings] = useState<StoreSettings | null>(null);
  const [formValues, setFormValues] = useState({
    visibility: "public",
    showOutOfStock: true,
    showProductViews: true,
    emailNotifications: true,
    productViewNotifications: false,
  });

  useEffect(() => {
    fetchSettings();
  }, [storeId]);

  useEffect(() => {
    if (settings) {
      setFormValues({
        visibility: settings.visibility,
        showOutOfStock: settings.showOutOfStock,
        showProductViews: settings.showProductViews,
        emailNotifications: settings.emailNotifications,
        productViewNotifications: settings.productViewNotifications,
      });
    }
  }, [settings]);

  const fetchSettings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/stores/${storeId}/settings`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch store settings: ${response.status}`);
      }

      const data = await response.json();
      setSettings(data.settings);
    } catch (error) {
      console.error("Error fetching store settings:", error);
      toast.error("Failed to load store settings");
      
      // Create default settings if none exist
      setSettings({
        id: "",
        storeId,
        visibility: "public",
        showOutOfStock: true,
        showProductViews: true,
        emailNotifications: true,
        productViewNotifications: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      const response = await fetch(`/api/admin/stores/${storeId}/settings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formValues),
      });

      if (!response.ok) {
        throw new Error("Failed to save settings");
      }

      const data = await response.json();
      setSettings(data.settings);
      toast.success("Store settings saved successfully");
    } catch (error) {
      console.error("Error saving store settings:", error);
      toast.error("Failed to save store settings");
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (key: string, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Store Settings</h2>
        <Button 
          onClick={handleSaveSettings} 
          disabled={isSaving}
        >
          {isSaving && <Spinner size="sm" className="mr-2" />}
          Save Settings
        </Button>
      </div>

      <div className="rounded-lg border border-gray-200 bg-white">
        <div className="border-b border-gray-200 px-6 py-4">
          <h3 className="text-base font-medium text-gray-900">Visibility & Display</h3>
        </div>
        <div className="divide-y divide-gray-200">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <GlobeAltIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Store Visibility</p>
                <p className="text-sm text-gray-500">
                  Control who can see this store in the marketplace
                </p>
              </div>
            </div>
            <div>
              <Select
                value={formValues.visibility}
                onChange={(e) => handleChange("visibility", e.target.value)}
                className="w-32"
              >
                <option value="public">Public</option>
                <option value="private">Private</option>
              </Select>
            </div>
          </div>

          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <ShoppingBagIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Show Out of Stock Products</p>
                <p className="text-sm text-gray-500">
                  Display products that are out of stock in the store
                </p>
              </div>
            </div>
            <div>
              <Switch
                checked={formValues.showOutOfStock}
                onCheckedChange={(checked) => handleChange("showOutOfStock", checked)}
              />
            </div>
          </div>

          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <EyeIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Show Product Views</p>
                <p className="text-sm text-gray-500">
                  Display view count on product listings
                </p>
              </div>
            </div>
            <div>
              <Switch
                checked={formValues.showProductViews}
                onCheckedChange={(checked) => handleChange("showProductViews", checked)}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg border border-gray-200 bg-white">
        <div className="border-b border-gray-200 px-6 py-4">
          <h3 className="text-base font-medium text-gray-900">Notifications</h3>
        </div>
        <div className="divide-y divide-gray-200">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <EnvelopeIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Email Notifications</p>
                <p className="text-sm text-gray-500">
                  Send email notifications for store activities
                </p>
              </div>
            </div>
            <div>
              <Switch
                checked={formValues.emailNotifications}
                onCheckedChange={(checked) => handleChange("emailNotifications", checked)}
              />
            </div>
          </div>

          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <BellAlertIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Product View Notifications</p>
                <p className="text-sm text-gray-500">
                  Notify when products receive views
                </p>
              </div>
            </div>
            <div>
              <Switch
                checked={formValues.productViewNotifications}
                onCheckedChange={(checked) => handleChange("productViewNotifications", checked)}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button 
          onClick={handleSaveSettings} 
          disabled={isSaving}
        >
          {isSaving && <Spinner size="sm" className="mr-2" />}
          Save Settings
        </Button>
      </div>
    </div>
  );
}
