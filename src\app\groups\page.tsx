import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import {
  UsersIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon,
  Squares2X2Icon,
  ListBulletIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  GlobeAltIcon,
  LockClosedIcon
} from "@heroicons/react/24/outline";
import { CreateGroupButton } from "@/components/groups/CreateGroupButton";
import { db } from "@/lib/db";
import { groups, groupMembers } from "@/lib/db/schema";
import { eq, and, desc, count } from "drizzle-orm";

export default async function GroupsPage() {
  const user = await requireAuth();

  // Get some sample groups for the UI
  const featuredGroups = await db.select({
    group: {
      id: groups.id,
      name: groups.name,
      description: groups.description,
      visibility: groups.visibility,
      coverImage: groups.coverImage,
      category: groups.category,
      creatorId: groups.creatorId,
      createdAt: groups.createdAt,
      updatedAt: groups.updatedAt
    },
    memberCount: count(groupMembers.id).as('memberCount'),
  })
  .from(groups)
  .leftJoin(groupMembers, eq(groups.id, groupMembers.groupId))
  .where(eq(groups.visibility, 'public'))
  .groupBy(groups.id)
  .orderBy(desc(groups.createdAt))
  .limit(6);

  // Get total groups count
  const totalGroups = await db.select({ count: count() }).from(groups);
  const groupsCount = totalGroups[0]?.count || 0;

  // Get user's groups count
  const userGroupsCount = await db.select({ count: count() })
    .from(groupMembers)
    .where(eq(groupMembers.userId, user.id));
  const joinedGroupsCount = userGroupsCount[0]?.count || 0;

  return (
    <MainLayout>
      {/* Hero section with stats */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">
                Discover Communities
              </h1>
              <p className="mt-3 text-lg">
                Connect with people who share your interests. Join existing groups or create your own community.
              </p>
              <div className="mt-6">
                <CreateGroupButton />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 sm:grid-cols-3">
              <div className="rounded-lg bg-white/10 backdrop-blur-sm p-4 text-center">
                <div className="text-3xl font-bold">{groupsCount}</div>
                <div className="text-sm font-medium text-white/80">Total Groups</div>
              </div>
              <div className="rounded-lg bg-white/10 backdrop-blur-sm p-4 text-center">
                <div className="text-3xl font-bold">{joinedGroupsCount}</div>
                <div className="text-sm font-medium text-white/80">Your Groups</div>
              </div>
              <div className="rounded-lg bg-white/10 backdrop-blur-sm p-4 text-center">
                <div className="text-3xl font-bold">24/7</div>
                <div className="text-sm font-medium text-white/80">Active Community</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Search and filters */}
        <div className="mb-8 bg-white rounded-xl shadow-sm p-4">
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
            <div className="relative flex-1">
              <form action="/groups" method="get" className="w-full">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="search"
                    name="search"
                    placeholder="Search groups..."
                    className="w-full rounded-lg border border-gray-300 pl-10 p-2.5 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
              </form>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 whitespace-nowrap">View:</span>
              <div className="inline-flex rounded-lg shadow-sm">
                <button className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-l-lg hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-500">
                  <Squares2X2Icon className="h-4 w-4 mr-1" />
                  Grid
                </button>
                <button className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-r-lg hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-500">
                  <ListBulletIcon className="h-4 w-4 mr-1" />
                  List
                </button>
              </div>

              <button className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-blue-500">
                <FunnelIcon className="h-4 w-4 mr-1" />
                Filter
              </button>
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-2">
            <a href="/groups?filter=all" className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800 hover:bg-blue-200">
              All Groups
            </a>
            <a href="/groups?filter=joined" className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200">
              Your Groups
            </a>
            <a href="/groups?filter=created" className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200">
              Created by You
            </a>
            <a href="/groups?filter=discover" className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200">
              Discover
            </a>
            <a href="/groups?category=technology" className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200">
              Technology
            </a>
            <a href="/groups?category=art" className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200">
              Art & Design
            </a>
            <a href="/groups?category=business" className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200">
              Business
            </a>
          </div>
        </div>

        {/* Featured groups section */}
        <div className="mb-10">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Featured Groups</h2>
            <a href="/groups?filter=discover" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              View all
            </a>
          </div>

          {featuredGroups.length > 0 ? (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {featuredGroups.map((item) => (
                <div key={item.group.id} className="group overflow-hidden rounded-xl bg-white shadow-md transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px]">
                  <a href={`/groups/${item.group.id}`} className="block">
                    <div className="relative h-40 w-full bg-gradient-to-r from-blue-500 to-indigo-600 overflow-hidden">
                      {item.group.coverImage ? (
                        <img
                          src={item.group.coverImage}
                          alt={item.group.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center">
                          <UsersIcon className="h-16 w-16 text-white opacity-50" />
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-60"></div>

                      {/* Badge for visibility */}
                      <div className="absolute top-3 left-3 flex items-center rounded-full bg-black/30 backdrop-blur-sm px-2 py-1">
                        {item.group.visibility === 'public' ? (
                          <GlobeAltIcon className="h-3.5 w-3.5 text-white" />
                        ) : (
                          <LockClosedIcon className="h-3.5 w-3.5 text-white" />
                        )}
                        <span className="ml-1 text-xs font-medium text-white">
                          {item.group.visibility === 'public' ? "Public" :
                           item.group.visibility === 'private-visible' ? "Private (Visible)" : "Private (Hidden)"}
                        </span>
                      </div>

                      {/* Category badge */}
                      {item.group.category && (
                        <div className="absolute top-3 right-3 rounded-full bg-white/90 backdrop-blur-sm px-2 py-1">
                          <span className="text-xs font-medium text-gray-800">{item.group.category}</span>
                        </div>
                      )}

                      {/* Group name overlay */}
                      <div className="absolute bottom-0 left-0 right-0 p-4">
                        <h3 className="text-xl font-bold text-white line-clamp-1 drop-shadow-sm">{item.group.name}</h3>
                        <div className="mt-1 flex items-center text-xs text-white/90">
                          <span>{item.memberCount} {Number(item.memberCount) === 1 ? "member" : "members"}</span>
                        </div>
                      </div>
                    </div>
                  </a>

                  <div className="p-4">
                    {item.group.description && (
                      <p className="text-sm text-gray-600 line-clamp-2 mb-4">
                        {item.group.description}
                      </p>
                    )}

                    <a href={`/groups/${item.group.id}`}>
                      <Button size="sm" className="w-full rounded-full">
                        View Group
                      </Button>
                    </a>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-lg border border-gray-200 bg-white p-8 text-center shadow">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                <UsersIcon className="h-8 w-8 text-blue-600" />
              </div>
              <p className="mt-4 text-sm text-gray-500">
                No featured groups available yet.
              </p>
              <p className="mt-2 text-sm text-gray-500">
                Be the first to create a group and connect with others!
              </p>
              <div className="mt-4">
                <CreateGroupButton />
              </div>
            </div>
          )}
        </div>

        {/* Create your own group CTA */}
        <div className="rounded-xl bg-gradient-to-r from-purple-600 to-indigo-600 p-8 text-white shadow-lg">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 items-center">
            <div>
              <h2 className="text-2xl font-bold">Create Your Own Community</h2>
              <p className="mt-2 text-white/90">
                Start a group around your interests, hobbies, or expertise. Connect with like-minded people and build a thriving community.
              </p>
            </div>
            <div className="flex justify-center md:justify-end">
              <CreateGroupButton />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
