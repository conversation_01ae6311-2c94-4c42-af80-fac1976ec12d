import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  users,
  wallets,
  walletTransactions,
  referrals,
  blogs,
  blogMonetization,
  blogEarnings
} from "@/lib/db/schema";
import { eq, and, sql, desc, asc, like, gte, lte, count, sum, or } from "drizzle-orm";

// Get user earnings data for admin
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const minEarnings = searchParams.get('minEarnings') || '';
    const maxEarnings = searchParams.get('maxEarnings') || '';
    const sortBy = searchParams.get('sortBy') || 'totalEarnings';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = [];
    
    // Search filter
    if (search) {
      whereConditions.push(
        or(
          like(users.name, `%${search}%`),
          like(users.username, `%${search}%`),
          like(users.email, `%${search}%`)
        )
      );
    }

    // Status filter
    if (status !== 'all') {
      whereConditions.push(eq(users.status, status));
    }

    // Get users with their earning data
    const usersQuery = db
      .select({
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          email: users.email,
          avatar: users.avatar,
          status: users.status,
          createdAt: users.createdAt,
          lastLoginAt: users.lastLoginAt,
        },
        earningWallet: {
          balance: wallets.balance,
        }
      })
      .from(users)
      .leftJoin(wallets, and(
        eq(wallets.userId, users.id),
        eq(wallets.type, 'earning')
      ))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .limit(limit)
      .offset(offset);

    // Apply sorting
    if (sortBy === 'totalEarnings' || sortBy === 'earningWalletBalance') {
      usersQuery.orderBy(
        sortOrder === 'desc' 
          ? desc(wallets.balance) 
          : asc(wallets.balance)
      );
    } else if (sortBy === 'joinedDate') {
      usersQuery.orderBy(
        sortOrder === 'desc' 
          ? desc(users.createdAt) 
          : asc(users.createdAt)
      );
    } else if (sortBy === 'lastActivity') {
      usersQuery.orderBy(
        sortOrder === 'desc' 
          ? desc(users.lastLoginAt) 
          : asc(users.lastLoginAt)
      );
    }

    const usersResult = await usersQuery;

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(users)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Get additional data for each user
    const enrichedUsers = await Promise.all(
      usersResult.map(async (userResult) => {
        const userId = userResult.user.id;

        // Get total earnings from transactions
        const totalEarningsResult = await db
          .select({ total: sum(walletTransactions.amount) })
          .from(walletTransactions)
          .where(
            and(
              eq(walletTransactions.userId, userId),
              eq(walletTransactions.type, 'earning'),
              eq(walletTransactions.status, 'completed')
            )
          );

        // Get referral data
        const referralStatsResult = await db
          .select({ 
            total: count(),
            completed: count(sql`CASE WHEN ${referrals.status} = 'completed' THEN 1 END`)
          })
          .from(referrals)
          .where(eq(referrals.referrerId, userId));

        // Get referral earnings
        const referralEarningsResult = await db
          .select({ total: sum(walletTransactions.amount) })
          .from(walletTransactions)
          .where(
            and(
              eq(walletTransactions.userId, userId),
              eq(walletTransactions.type, 'earning'),
              eq(walletTransactions.status, 'completed'),
              sql`${walletTransactions.reference} LIKE '%referral%'`
            )
          );

        // Get blog data
        const blogStatsResult = await db
          .select({ 
            total: count(),
            monetized: count(sql`CASE WHEN ${blogMonetization.isApproved} = true THEN 1 END`)
          })
          .from(blogs)
          .leftJoin(blogMonetization, eq(blogs.id, blogMonetization.blogId))
          .where(eq(blogs.authorId, userId));

        // Get blog earnings
        const blogEarningsResult = await db
          .select({ total: sum(blogEarnings.earningAmount) })
          .from(blogEarnings)
          .where(
            and(
              eq(blogEarnings.authorId, userId),
              eq(blogEarnings.status, 'paid')
            )
          );

        const totalEarnings = parseFloat(totalEarningsResult[0]?.total || '0');
        const referralStats = referralStatsResult[0] || { total: 0, completed: 0 };
        const referralEarnings = parseFloat(referralEarningsResult[0]?.total || '0');
        const blogStats = blogStatsResult[0] || { total: 0, monetized: 0 };
        const blogEarnings = parseFloat(blogEarningsResult[0]?.total || '0');

        return {
          id: userResult.user.id,
          name: userResult.user.name || 'Unknown User',
          username: userResult.user.username || '',
          email: userResult.user.email || '',
          avatar: userResult.user.avatar,
          earningWalletBalance: parseFloat(userResult.earningWallet?.balance || '0'),
          totalEarnings,
          referralEarnings,
          blogEarnings,
          totalReferrals: referralStats.total,
          completedReferrals: referralStats.completed,
          totalBlogs: blogStats.total,
          monetizedBlogs: blogStats.monetized,
          joinedDate: userResult.user.createdAt?.toISOString() || '',
          lastActivity: userResult.user.lastLoginAt?.toISOString() || '',
          status: userResult.user.status || 'inactive',
          earningRank: 0 // Will be calculated after sorting
        };
      })
    );

    // Apply earnings filters if specified
    let filteredUsers = enrichedUsers;
    if (minEarnings) {
      const minAmount = parseFloat(minEarnings);
      filteredUsers = filteredUsers.filter(user => user.totalEarnings >= minAmount);
    }
    if (maxEarnings) {
      const maxAmount = parseFloat(maxEarnings);
      filteredUsers = filteredUsers.filter(user => user.totalEarnings <= maxAmount);
    }

    // Calculate earning ranks
    const sortedByEarnings = [...filteredUsers].sort((a, b) => b.totalEarnings - a.totalEarnings);
    sortedByEarnings.forEach((user, index) => {
      user.earningRank = index + 1;
    });

    return NextResponse.json({
      success: true,
      data: {
        users: filteredUsers,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages
        }
      },
    });

  } catch (error) {
    console.error("Error fetching user earnings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch user earnings data"
      },
      { status: 500 }
    );
  }
}
