import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptionTransactions, userSubscriptions } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const refundSchema = z.object({
  reason: z.string().min(1, "Refund reason is required"),
  amount: z.string().optional(), // Optional partial refund amount
});

// POST - Process refund for transaction
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { reason, amount } = refundSchema.parse(body);

    // Get the original transaction
    const originalTransaction = await db.query.subscriptionTransactions.findFirst({
      where: eq(subscriptionTransactions.id, params.id),
    });

    if (!originalTransaction) {
      return NextResponse.json(
        { message: "Transaction not found" },
        { status: 404 }
      );
    }

    if (originalTransaction.status !== 'completed') {
      return NextResponse.json(
        {
          success: false,
          message: "Only completed transactions can be refunded",
        },
        { status: 400 }
      );
    }

    if (originalTransaction.type !== 'payment') {
      return NextResponse.json(
        {
          success: false,
          message: "Only payment transactions can be refunded",
        },
        { status: 400 }
      );
    }

    // Check if already refunded
    const existingRefund = await db.query.subscriptionTransactions.findFirst({
      where: eq(subscriptionTransactions.gatewayTransactionId, `refund_${originalTransaction.id}`),
    });

    if (existingRefund) {
      return NextResponse.json(
        {
          success: false,
          message: "This transaction has already been refunded",
        },
        { status: 400 }
      );
    }

    // Calculate refund amount
    const originalAmount = parseFloat(originalTransaction.amount);
    const refundAmount = amount ? parseFloat(amount) : originalAmount;

    if (refundAmount > originalAmount) {
      return NextResponse.json(
        {
          success: false,
          message: "Refund amount cannot exceed original transaction amount",
        },
        { status: 400 }
      );
    }

    // Get the subscription details
    const subscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, originalTransaction.subscriptionId),
    });

    if (!subscription) {
      return NextResponse.json(
        { message: "Associated subscription not found" },
        { status: 404 }
      );
    }

    // Create refund transaction record
    const refundTransactionId = uuidv4();
    await db.insert(subscriptionTransactions).values({
      id: refundTransactionId,
      subscriptionId: originalTransaction.subscriptionId,
      userId: originalTransaction.userId,
      planId: originalTransaction.planId,
      type: 'refund',
      amount: refundAmount.toFixed(2),
      currency: originalTransaction.currency,
      status: 'completed', // We'll mark as completed since this is admin-initiated
      paymentGateway: originalTransaction.paymentGateway,
      gatewayTransactionId: `refund_${originalTransaction.id}`,
      description: `Refund for transaction ${originalTransaction.id}: ${reason}`,
      processedAt: new Date(),
      metadata: JSON.stringify({
        originalTransactionId: originalTransaction.id,
        refundReason: reason,
        adminInitiated: true,
        adminUserId: session.user.id,
        partialRefund: refundAmount < originalAmount,
      }),
    });

    // In a real implementation, you would integrate with the payment gateway
    // to process the actual refund. For now, we'll simulate it.
    
    // If this is a full refund, consider cancelling the subscription
    if (refundAmount === originalAmount) {
      // Update subscription status to cancelled
      await db
        .update(userSubscriptions)
        .set({
          status: 'cancelled',
          cancelledAt: new Date(),
          cancelReason: `Refunded: ${reason}`,
          autoRenew: false,
        })
        .where(eq(userSubscriptions.id, originalTransaction.subscriptionId));
    }

    // In a real implementation with payment gateways:
    /*
    try {
      let refundResult;
      
      switch (originalTransaction.paymentGateway) {
        case 'stripe':
          // Process Stripe refund
          refundResult = await processStripeRefund(originalTransaction.gatewayTransactionId, refundAmount);
          break;
        case 'paypal':
          // Process PayPal refund
          refundResult = await processPayPalRefund(originalTransaction.gatewayTransactionId, refundAmount);
          break;
        case 'wallet':
          // Process wallet refund (credit back to user's wallet)
          refundResult = await processWalletRefund(originalTransaction.userId, refundAmount);
          break;
        default:
          throw new Error('Unsupported payment gateway for refunds');
      }

      if (!refundResult.success) {
        // Update refund transaction as failed
        await db
          .update(subscriptionTransactions)
          .set({
            status: 'failed',
            description: `Refund failed: ${refundResult.error}`,
          })
          .where(eq(subscriptionTransactions.id, refundTransactionId));

        return NextResponse.json(
          {
            success: false,
            message: `Refund failed: ${refundResult.error}`,
          },
          { status: 400 }
        );
      }

      // Update with gateway refund ID
      await db
        .update(subscriptionTransactions)
        .set({
          gatewayTransactionId: refundResult.refundId,
        })
        .where(eq(subscriptionTransactions.id, refundTransactionId));
    } catch (refundError) {
      // Update refund transaction as failed
      await db
        .update(subscriptionTransactions)
        .set({
          status: 'failed',
          description: `Refund processing failed: ${refundError instanceof Error ? refundError.message : 'Unknown error'}`,
        })
        .where(eq(subscriptionTransactions.id, refundTransactionId));

      return NextResponse.json(
        {
          success: false,
          message: "Failed to process refund with payment gateway",
        },
        { status: 500 }
      );
    }
    */

    return NextResponse.json({
      success: true,
      message: `Refund of ${originalTransaction.currency} ${refundAmount.toFixed(2)} processed successfully`,
      refundTransactionId,
      refundAmount: refundAmount.toFixed(2),
    });
  } catch (error) {
    console.error("Error processing refund:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to process refund",
      },
      { status: 500 }
    );
  }
}
