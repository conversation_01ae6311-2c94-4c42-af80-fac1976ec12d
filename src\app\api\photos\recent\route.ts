import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, users } from "@/lib/db/schema";
import { eq, desc, sql, isNotNull, and } from "drizzle-orm";

// GET /api/photos/recent - Get recent photos from user's network
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "6");

    // Get recent posts with images from user's network (including own posts)
    const recentPhotos = await db
      .select({
        id: posts.id,
        images: posts.images,
        content: posts.content,
        createdAt: posts.createdAt,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
      })
      .from(posts)
      .leftJoin(users, eq(posts.userId, users.id))
      .where(
        and(
          isNotNull(posts.images),
          sql`JSON_LENGTH(${posts.images}) > 0`
        )
      )
      .orderBy(desc(posts.createdAt))
      .limit(limit * 3); // Get more to extract individual photos

    // Extract individual photos with metadata
    const photos: Array<{
      id: string;
      url: string;
      postId: string;
      postContent: string;
      createdAt: string;
      user: {
        id: string;
        name: string;
        username: string;
        image: string | null;
      };
    }> = [];

    recentPhotos.forEach(post => {
      if (post.images && Array.isArray(post.images)) {
        post.images.forEach((imageUrl: string, index: number) => {
          if (photos.length < limit) {
            photos.push({
              id: `${post.id}-${index}`,
              url: imageUrl,
              postId: post.id,
              postContent: post.content || '',
              createdAt: post.createdAt,
              user: post.user,
            });
          }
        });
      }
    });

    return NextResponse.json({
      photos: photos.slice(0, limit),
    });

  } catch (error) {
    console.error("Error fetching recent photos:", error);
    return NextResponse.json(
      { error: "Failed to fetch recent photos" },
      { status: 500 }
    );
  }
}
