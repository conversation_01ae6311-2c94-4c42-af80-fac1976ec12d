import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { products, stores, storeFollows, users } from "@/lib/db/schema";
import { eq, and, desc, inArray } from "drizzle-orm";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { ProductCard } from "@/components/marketplace/ProductCard";
import { StoreCard } from "@/components/marketplace/StoreCard";
import { ChevronLeftIcon, BuildingStorefrontIcon } from "@heroicons/react/24/outline";

export default async function FollowingPage() {
  const user = await requireAuth();

  // Get stores the user is following
  const followedStores = await db
    .select({ storeId: storeFollows.storeId })
    .from(storeFollows)
    .where(eq(storeFollows.userId, user.id));

  const followedStoreIds = followedStores.map(store => store.storeId);

  // If user isn't following any stores, show empty state
  if (followedStoreIds.length === 0) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Link href="/marketplace" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900">
              <ChevronLeftIcon className="mr-1 h-4 w-4" />
              Back to Marketplace
            </Link>
          </div>

          <h1 className="mb-6 text-2xl font-bold text-gray-900">
            Following
          </h1>

          <div className="rounded-lg bg-white p-8 text-center shadow">
            <BuildingStorefrontIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">You're not following any stores yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Follow stores to see their products here.
            </p>
            <div className="mt-6">
              <Link href="/marketplace?view=stores">
                <Button>
                  Browse Stores
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Get store details
  const followedStoresDetails = await db
    .select({
      id: stores.id,
      name: stores.name,
      slug: stores.slug,
      logo: stores.logo,
      banner: stores.banner,
      isVerified: stores.isVerified,
      owner: {
        id: users.id,
        name: users.name,
      },
    })
    .from(stores)
    .leftJoin(users, eq(stores.ownerId, users.id))
    .where(inArray(stores.id, followedStoreIds))
    .orderBy(desc(stores.name))
    .limit(4);

  // Get latest products from followed stores
  const latestProducts = await db
    .select({
      id: products.id,
      title: products.title,
      price: products.price,
      condition: products.item_condition,
      photos: products.photos,
      createdAt: products.createdAt,
      store: {
        id: stores.id,
        name: stores.name,
        logo: stores.logo,
      },
    })
    .from(products)
    .leftJoin(stores, eq(products.storeId, stores.id))
    .where(inArray(products.storeId, followedStoreIds))
    .orderBy(desc(products.createdAt))
    .limit(12);

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link href="/marketplace" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900">
            <ChevronLeftIcon className="mr-1 h-4 w-4" />
            Back to Marketplace
          </Link>
        </div>

        <h1 className="mb-6 text-2xl font-bold text-gray-900">
          Following
        </h1>

        {/* Followed Stores */}
        <div className="mb-8">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Stores You Follow
            </h2>
            {followedStoresDetails.length > 4 && (
              <Link href="/marketplace/following/stores" className="text-sm text-blue-600 hover:text-blue-800">
                View all
              </Link>
            )}
          </div>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {followedStoresDetails.map((store) => (
              <StoreCard key={store.id} store={store} />
            ))}
          </div>
        </div>

        {/* Latest Products */}
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Latest Products from Followed Stores
            </h2>
          </div>

          {latestProducts.length > 0 ? (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {latestProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="rounded-lg bg-white p-8 text-center shadow">
              <BuildingStorefrontIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No products available</h3>
              <p className="mt-1 text-sm text-gray-500">
                The stores you follow haven't listed any products yet.
              </p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
