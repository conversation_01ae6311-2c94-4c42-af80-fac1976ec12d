/**
 * Security utilities for referral system
 * Provides input validation, sanitization, and security checks
 */

export class ReferralSecurity {
  // Valid characters for referral codes
  private static readonly VALID_CODE_CHARS = /^[A-Z0-9]+$/;
  
  // Rate limiting storage (in production, use Redis)
  private static rateLimitStore = new Map<string, { count: number; resetTime: number }>();

  /**
   * Validate referral code format and security
   */
  static validateReferralCode(code: string): { isValid: boolean; error?: string } {
    if (!code) {
      return { isValid: false, error: 'Referral code is required' };
    }

    // Remove any whitespace and convert to uppercase
    const cleanCode = code.trim().toUpperCase();

    // Length validation
    if (cleanCode.length < 3 || cleanCode.length > 10) {
      return { isValid: false, error: 'Referral code must be 3-10 characters long' };
    }

    // Character validation
    if (!this.VALID_CODE_CHARS.test(cleanCode)) {
      return { isValid: false, error: 'Referral code can only contain letters and numbers' };
    }

    // Check for suspicious patterns
    if (this.containsSuspiciousPatterns(cleanCode)) {
      return { isValid: false, error: 'Invalid referral code format' };
    }

    return { isValid: true };
  }

  /**
   * Validate user ID format and security
   */
  static validateUserId(userId: string): { isValid: boolean; error?: string } {
    if (!userId) {
      return { isValid: false, error: 'User ID is required' };
    }

    // Basic format validation
    if (typeof userId !== 'string' || userId.length < 10 || userId.length > 255) {
      return { isValid: false, error: 'Invalid user ID format' };
    }

    // Check for SQL injection patterns
    if (this.containsSQLInjectionPatterns(userId)) {
      return { isValid: false, error: 'Invalid user ID format' };
    }

    return { isValid: true };
  }

  /**
   * Sanitize referral code input
   */
  static sanitizeReferralCode(code: string): string {
    if (!code) return '';
    
    return code
      .trim()
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '') // Remove any non-alphanumeric characters
      .substring(0, 10); // Limit length
  }

  /**
   * Check for suspicious patterns in referral codes
   */
  private static containsSuspiciousPatterns(code: string): boolean {
    const suspiciousPatterns = [
      /(.)\1{4,}/, // Repeated characters (5+ times)
      /^(ADMIN|ROOT|TEST|DEBUG|NULL|UNDEFINED)/, // Reserved words
      /^(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE)/, // SQL keywords
      /[<>'"&]/, // HTML/Script injection characters
    ];

    return suspiciousPatterns.some(pattern => pattern.test(code));
  }

  /**
   * Check for SQL injection patterns
   * Improved to avoid false positives with legitimate content
   */
  private static containsSQLInjectionPatterns(input: string): boolean {
    const sqlPatterns = [
      // SQL injection with quotes and comments
      /(\'|\\\'|;|\\;|--)/,
      // SQL keywords in suspicious contexts
      /[\s\+\-\*\/\(\)\=\<\>\!\&\|]+(union|select|insert|update|delete|drop|create|alter|exec|execute)[\s\+\-\*\/\(\)\=\<\>\!\&\|]+/i,
      // Script injection patterns
      /(script|javascript|vbscript|onload|onerror|onclick)/i,
      // Basic XSS patterns
      /[<>"']/,
    ];

    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Rate limiting for referral operations
   */
  static checkRateLimit(identifier: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    const now = Date.now();
    const key = `referral_${identifier}`;
    
    // Clean up expired entries
    this.cleanupRateLimit();
    
    const current = this.rateLimitStore.get(key);
    
    if (!current) {
      this.rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (now > current.resetTime) {
      this.rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (current.count >= maxRequests) {
      return false;
    }
    
    current.count++;
    return true;
  }

  /**
   * Clean up expired rate limit entries
   */
  private static cleanupRateLimit(): void {
    const now = Date.now();
    for (const [key, value] of this.rateLimitStore.entries()) {
      if (now > value.resetTime) {
        this.rateLimitStore.delete(key);
      }
    }
  }

  /**
   * Validate referral signup request
   */
  static validateReferralSignup(referralCode: string, newUserId: string): { 
    isValid: boolean; 
    errors: string[] 
  } {
    const errors: string[] = [];

    // Validate referral code
    const codeValidation = this.validateReferralCode(referralCode);
    if (!codeValidation.isValid) {
      errors.push(codeValidation.error!);
    }

    // Validate user ID
    const userValidation = this.validateUserId(newUserId);
    if (!userValidation.isValid) {
      errors.push(userValidation.error!);
    }

    // Check rate limiting
    if (!this.checkRateLimit(`signup_${newUserId}`, 5, 300000)) { // 5 attempts per 5 minutes
      errors.push('Too many referral attempts. Please try again later.');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate reward processing request
   */
  static validateRewardProcessing(referralId: string): { isValid: boolean; error?: string } {
    if (!referralId) {
      return { isValid: false, error: 'Referral ID is required' };
    }

    // Basic UUID format validation
    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidPattern.test(referralId)) {
      return { isValid: false, error: 'Invalid referral ID format' };
    }

    // Check rate limiting for reward processing
    if (!this.checkRateLimit(`reward_${referralId}`, 3, 60000)) { // 3 attempts per minute
      return { isValid: false, error: 'Too many reward processing attempts' };
    }

    return { isValid: true };
  }

  /**
   * Log security events
   */
  static logSecurityEvent(event: string, details: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    };

    // In production, send to security monitoring service
    console.warn('SECURITY EVENT:', logEntry);
  }

  /**
   * Generate secure referral code
   */
  static generateSecureCode(userId: string): string {
    if (!userId || userId.length < 2) {
      throw new Error('Invalid userId for secure code generation');
    }

    // Use cryptographically secure random if available
    const getRandomValues = () => {
      if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        const array = new Uint8Array(4);
        crypto.getRandomValues(array);
        return Array.from(array).map(b => b.toString(36)).join('');
      } else {
        // Fallback for Node.js
        return Math.random().toString(36).substring(2, 6);
      }
    };

    const userPart = userId.substring(0, 2).toUpperCase();
    const randomPart = getRandomValues().toUpperCase();
    const timestampPart = Date.now().toString(36).slice(-2).toUpperCase();
    
    return `${userPart}${timestampPart}${randomPart}`.substring(0, 8);
  }
}
