import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { referralCodes, referrals, users } from "@/lib/db/schema";
import { eq, count, sum, sql } from "drizzle-orm";

// Get referral users for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all users with referral codes and their statistics
    const referralUsersResult = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
        username: users.username,
        referralCode: referralCodes.code,
        totalReferrals: referralCodes.totalReferrals,
        totalEarnings: referralCodes.totalEarnings,
        isActive: referralCodes.isActive,
        createdAt: referralCodes.createdAt,
        updatedAt: referralCodes.updatedAt,
      })
      .from(referralCodes)
      .innerJoin(users, eq(referralCodes.userId, users.id))
      .orderBy(referralCodes.totalEarnings);

    // Get completed referrals count for each user
    const completedReferralsResult = await db
      .select({
        referrerId: referrals.referrerId,
        completedCount: count(),
      })
      .from(referrals)
      .where(eq(referrals.status, 'completed'))
      .groupBy(referrals.referrerId);

    // Get pending referrals count for each user
    const pendingReferralsResult = await db
      .select({
        referrerId: referrals.referrerId,
        pendingCount: count(),
      })
      .from(referrals)
      .where(eq(referrals.status, 'pending'))
      .groupBy(referrals.referrerId);

    // Get paid earnings for each user
    const paidEarningsResult = await db
      .select({
        referrerId: referrals.referrerId,
        paidEarnings: sum(referrals.rewardAmount),
      })
      .from(referrals)
      .where(sql`${referrals.status} = 'completed' AND ${referrals.paidAt} IS NOT NULL`)
      .groupBy(referrals.referrerId);

    // Get pending earnings for each user
    const pendingEarningsResult = await db
      .select({
        referrerId: referrals.referrerId,
        pendingEarnings: sum(referrals.rewardAmount),
      })
      .from(referrals)
      .where(sql`${referrals.status} = 'completed' AND ${referrals.paidAt} IS NULL`)
      .groupBy(referrals.referrerId);

    // Create lookup maps
    const completedReferralsMap = new Map(
      completedReferralsResult.map(item => [item.referrerId, item.completedCount])
    );
    const pendingReferralsMap = new Map(
      pendingReferralsResult.map(item => [item.referrerId, item.pendingCount])
    );
    const paidEarningsMap = new Map(
      paidEarningsResult.map(item => [item.referrerId, parseFloat(item.paidEarnings || '0')])
    );
    const pendingEarningsMap = new Map(
      pendingEarningsResult.map(item => [item.referrerId, parseFloat(item.pendingEarnings || '0')])
    );

    // Combine data
    const referralUsers = referralUsersResult.map(user => ({
      id: user.id,
      name: user.name || 'Unknown User',
      email: user.email || '',
      username: user.username || '',
      referralCode: user.referralCode,
      totalReferrals: user.totalReferrals || 0,
      completedReferrals: completedReferralsMap.get(user.id) || 0,
      pendingReferrals: pendingReferralsMap.get(user.id) || 0,
      totalEarnings: parseFloat(user.totalEarnings || '0'),
      paidEarnings: paidEarningsMap.get(user.id) || 0,
      pendingEarnings: pendingEarningsMap.get(user.id) || 0,
      isActive: user.isActive || false,
      createdAt: user.createdAt.toISOString(),
      lastActivity: user.updatedAt.toISOString(),
    }));

    return NextResponse.json({
      success: true,
      data: referralUsers,
    });

  } catch (error) {
    console.error("Error fetching referral users:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch referral users"
      },
      { status: 500 }
    );
  }
}
