"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { MainLayout } from "@/components/layout/MainLayout";
import { BlogCard } from "@/components/blog/BlogCard";
import { BlogBreadcrumb } from "@/components/blog/BlogBreadcrumb";
import { ReadingProgress } from "@/components/blog/ReadingProgress";
import { SocialShare } from "@/components/blog/SocialShare";
import { TableOfContents } from "@/components/blog/TableOfContents";
import { PrintButton } from "@/components/blog/PrintButton";
import MarkdownPreview from "@/components/blog/MarkdownPreview";
import { CommentSection } from "@/components/feed/CommentSection";

import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { formatTimeAgo } from "@/lib/utils";
import { DetailedTime } from "@/components/ui/TimeDisplay";
import { useViewTracking } from "@/hooks/useViewTracking";
import {
  HeartIcon,
  ChatBubbleLeftIcon,
  BookmarkIcon,
  ClockIcon,
  EyeIcon,
  ArrowLeftIcon,
  UserPlusIcon,
  CheckIcon
} from "@heroicons/react/24/outline";
import {
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid
} from "@heroicons/react/24/solid";

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string | null;
  coverImage?: string | null;
  readTime?: number | null;
  viewCount?: number;
  publishedAt?: string | null;
  author: {
    id: string;
    name: string;
    image?: string | null;
    username?: string | null;
    bio?: string | null;
    followersCount?: number;
    isFollowing?: boolean;
  };
  category?: {
    id: string;
    name: string;
    slug: string;
    color: string;
  } | null;
  tags?: string[] | null;
  _count?: {
    likes: number;
    comments: number;
  };
  isLiked?: boolean;
  isBookmarked?: boolean;
}



export default function BlogPostPage() {
  const params = useParams();
  const slug = params?.slug as string;
  const { data: session } = useSession();

  if (!slug) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Blog post not found</h1>
          </div>
        </div>
      </MainLayout>
    );
  }

  const [blog, setBlog] = useState<BlogPost | null>(null);
  const [relatedBlogs, setRelatedBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [likesCount, setLikesCount] = useState(0);

  // Enhanced view and read tracking hook
  const { readDuration, isQualified, viewDuration, scrollDepth } = useViewTracking({
    blogSlug: slug,
    isEnabled: blog?.status === 'published',
    minReadDuration: 120, // 2 minutes
  });

  // Fetch blog data from API
  const fetchBlog = async () => {
    try {
      setLoading(true);

      const response = await fetch(`/api/blogs/${slug}`);
      if (response.ok) {
        const data = await response.json();
        setBlog(data);
        setIsLiked(data.isLiked || false);
        setIsBookmarked(data.isBookmarked || false);
        setIsFollowing(data.author.isFollowing || false);
        setLikesCount(data._count.likes || 0);
      } else {
        console.error("Failed to fetch blog");
      }
    } catch (error) {
      console.error("Error fetching blog:", error);
    } finally {
      setLoading(false);
    }
  };



  // Fetch related blogs
  const fetchRelatedBlogs = async () => {
    try {
      const response = await fetch(`/api/blogs?limit=3&status=published`);
      if (response.ok) {
        const data = await response.json();
        setRelatedBlogs(data.blogs.filter((b: BlogPost) => b.slug !== slug).slice(0, 3));
      } else {
        console.error("Failed to fetch related blogs");
      }
    } catch (error) {
      console.error("Error fetching related blogs:", error);
    }
  };

  useEffect(() => {
    if (slug) {
      fetchBlog();
      fetchRelatedBlogs();
    }
  }, [slug]);

  // Remove all mock data and replace with empty state
  useEffect(() => {
    // This useEffect is now empty as we fetch data from API
  }, []);

  // Handle like toggle
  const handleLike = async () => {
    if (!blog) return;

    try {
      const response = await fetch(`/api/blogs/${blog.slug}/like`, {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        setIsLiked(data.isLiked);
        setLikesCount(prev => data.isLiked ? prev + 1 : prev - 1);
      }
    } catch (error) {
      console.error("Error toggling like:", error);
    }
  };

  // Handle bookmark toggle
  const handleBookmark = async () => {
    if (!blog) return;

    try {
      const response = await fetch(`/api/blogs/${blog.slug}/bookmark`, {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        setIsBookmarked(data.isBookmarked);
      }
    } catch (error) {
      console.error("Error toggling bookmark:", error);
    }
  };



  // Handle follow toggle
  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    // TODO: Implement follow API call
  };



  if (loading) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!blog) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Blog post not found</h1>
            <p className="text-gray-600 mb-6">The blog post you're looking for doesn't exist.</p>
            <Link href="/blogs">
              <Button>
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to News/Blogs
              </Button>
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <ReadingProgress />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <article className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
          {/* Breadcrumb Navigation */}
          <BlogBreadcrumb
            category={blog.category}
            title={blog.title}
          />

          {/* Back Button */}
          <div className="mb-8">
            <Link href="/blogs">
              <Button variant="ghost" size="sm" className="hover:bg-white/80 backdrop-blur-sm">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to News/Blogs
              </Button>
            </Link>
          </div>

          {/* Cover Image */}
          {blog.coverImage && (
            <div className="relative h-64 sm:h-80 lg:h-96 rounded-3xl overflow-hidden mb-10 shadow-2xl group">
              <Image
                src={blog.coverImage}
                alt={blog.title}
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
            </div>
          )}

          {/* Category Badge */}
          {blog.category && (
            <div className="mb-6">
              <Badge
                variant="primary"
                className="text-white px-4 py-2 text-sm font-medium shadow-lg"
                style={{ backgroundColor: blog.category.color }}
              >
                {blog.category.name}
              </Badge>
            </div>
          )}

          {/* Title */}
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-8 leading-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text">
            {blog.title}
          </h1>

          {/* Author Section */}
          <div className="bg-white rounded-2xl p-6 mb-10 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative w-16 h-16 rounded-full overflow-hidden bg-gray-200 flex-shrink-0 ring-4 ring-white shadow-lg">
                  {blog.author.image ? (
                    <Image
                      src={blog.author.image}
                      alt={blog.author.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-xl">
                      {blog.author.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <Link
                      href={`/user/${blog.author.username || blog.author.id}`}
                      className="hover:text-blue-600 transition-colors"
                    >
                      <h3 className="font-bold text-gray-900 text-lg hover:text-blue-600 transition-colors">{blog.author.name}</h3>
                    </Link>
                    {blog.author.username && (
                      <span className="text-gray-500 text-sm">@{blog.author.username}</span>
                    )}
                  </div>
                  {blog.author.bio && (
                    <p className="text-gray-600 mb-2">{blog.author.bio}</p>
                  )}
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    {blog.publishedAt ? (
                      <DetailedTime
                        date={blog.publishedAt}
                        className="font-medium"
                        autoUpdate={false}
                      />
                    ) : (
                      <span className="font-medium">Draft</span>
                    )}
                    {blog.readTime && (
                      <>
                        <span>•</span>
                        <div className="flex items-center">
                          <ClockIcon className="w-4 h-4 mr-1" />
                          <span>{blog.readTime} min read</span>
                        </div>
                      </>
                    )}
                    <span>•</span>
                    <div className="flex items-center">
                      <EyeIcon className="w-4 h-4 mr-1" />
                      <span>{blog.viewCount || 0} views</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  variant={isFollowing ? "outline" : "primary"}
                  size="sm"
                  onClick={handleFollow}
                  className="shadow-md hover:shadow-lg transition-shadow"
                >
                  {isFollowing ? (
                    <>
                      <CheckIcon className="h-4 w-4 mr-1" />
                      Following
                    </>
                  ) : (
                    <>
                      <UserPlusIcon className="h-4 w-4 mr-1" />
                      Follow
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Table of Contents */}
          <TableOfContents content={blog.content} />

          {/* Content */}
          <div className="bg-white rounded-2xl p-8 mb-10 shadow-lg border border-gray-100">
            <div className="prose prose-lg max-w-none">
              <MarkdownPreview content={blog.content} />
            </div>
          </div>

          {/* Tags */}
          {blog.tags && blog.tags.length > 0 && (
            <div className="mb-10">
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                <div className="flex flex-wrap gap-3">
                  {blog.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 text-sm rounded-full hover:from-blue-100 hover:to-purple-100 transition-all cursor-pointer border border-blue-200 shadow-sm"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}



          {/* Read Progress Indicator */}
          {readDuration > 0 && (
            <div className="bg-white rounded-2xl p-4 mb-6 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4" />
                  <span>Reading time: {Math.floor(readDuration / 60)}m {readDuration % 60}s</span>
                </div>
                {isQualified && (
                  <div className="flex items-center gap-1 text-green-600">
                    <CheckIcon className="h-4 w-4" />
                    <span>Qualified read</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Reaction Bar */}
          <div className="bg-white rounded-2xl p-6 mb-10 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-8">
                <button
                  onClick={handleLike}
                  className="flex items-center space-x-3 text-gray-600 hover:text-red-500 transition-all duration-300 group"
                >
                  {isLiked ? (
                    <HeartIconSolid className="w-7 h-7 text-red-500 group-hover:scale-110 transition-transform" />
                  ) : (
                    <HeartIcon className="w-7 h-7 group-hover:scale-110 transition-transform" />
                  )}
                  <span className="font-semibold text-lg">{likesCount}</span>
                </button>

                <div className="flex items-center space-x-3 text-gray-600">
                  <ChatBubbleLeftIcon className="w-7 h-7" />
                  <span className="font-semibold text-lg">{blog._count?.comments || 0}</span>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <button
                  onClick={handleBookmark}
                  className="p-3 text-gray-600 hover:text-blue-600 transition-all duration-300 hover:bg-blue-50 rounded-full group"
                >
                  {isBookmarked ? (
                    <BookmarkIconSolid className="w-6 h-6 text-blue-600 group-hover:scale-110 transition-transform" />
                  ) : (
                    <BookmarkIcon className="w-6 h-6 group-hover:scale-110 transition-transform" />
                  )}
                </button>

                <SocialShare
                  title={blog.title}
                  url={typeof window !== 'undefined' ? window.location.href : ''}
                  description={blog.excerpt || undefined}
                />

                <PrintButton
                  title={blog.title}
                  author={blog.author.name}
                  content={blog.content}
                />
              </div>
            </div>
          </div>

          {/* Comments Section */}
          <div className="bg-white rounded-2xl p-8 mb-12 shadow-lg border border-gray-100">
            <CommentSection
              postId={blog.id}
              postType="blog_post"
              blogSlug={blog.slug}
            />
          </div>



          {/* Related Blogs */}
          {relatedBlogs.length > 0 && (
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Stories</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 auto-rows-fr">
                {relatedBlogs.map((relatedBlog) => (
                  <BlogCard
                    key={relatedBlog.id}
                    blog={relatedBlog}
                    variant="compact"
                  />
                ))}
              </div>
            </div>
          )}
        </article>
      </div>
    </MainLayout>
  );
}
