"use client";

import { 
  UserCircleIcon, 
  MapPinIcon, 
  CalendarIcon, 
  PencilIcon,
  GlobeAltIcon,
  BriefcaseIcon,
  AcademicCapIcon,
  CakeIcon
} from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";

interface ProfileAboutProps {
  userData: {
    id: string;
    name: string;
    username: string;
    bio: string | null;
    location: string | null;
    birthday: string | null;
    createdAt: string;
    website?: string | null;
    facebook?: string | null;
    twitter?: string | null;
    instagram?: string | null;
    linkedin?: string | null;
    youtube?: string | null;
    work?: string | null;
    education?: string | null;
  };
  isOwnProfile: boolean;
  onEdit: () => void;
}

export function ProfileAbout({ userData, isOwnProfile, onEdit }: ProfileAboutProps) {
  const socialLinks = [
    { key: 'website', label: 'Website', value: userData.website, icon: GlobeAltIcon },
    { key: 'facebook', label: 'Facebook', value: userData.facebook, icon: GlobeAltIcon },
    { key: 'twitter', label: 'Twitter', value: userData.twitter, icon: GlobeAltIcon },
    { key: 'instagram', label: 'Instagram', value: userData.instagram, icon: GlobeAltIcon },
    { key: 'linkedin', label: 'LinkedIn', value: userData.linkedin, icon: GlobeAltIcon },
    { key: 'youtube', label: 'YouTube', value: userData.youtube, icon: GlobeAltIcon },
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatBirthday = (birthday: string) => {
    return new Date(birthday).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Basic Information */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
          {isOwnProfile && (
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
              className="text-gray-600 hover:text-blue-600"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <UserCircleIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">Bio</p>
                <p className="mt-1 text-sm text-gray-900">
                  {userData.bio || "No bio added yet."}
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">Location</p>
                <p className="mt-1 text-sm text-gray-900">
                  {userData.location || "Not specified"}
                </p>
              </div>
            </div>

            {userData.birthday && (
              <div className="flex items-start space-x-3">
                <CakeIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500">Birthday</p>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatBirthday(userData.birthday)}
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CalendarIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">Joined</p>
                <p className="mt-1 text-sm text-gray-900">
                  {formatDate(userData.createdAt)}
                </p>
              </div>
            </div>

            {userData.work && (
              <div className="flex items-start space-x-3">
                <BriefcaseIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500">Work</p>
                  <p className="mt-1 text-sm text-gray-900">{userData.work}</p>
                </div>
              </div>
            )}

            {userData.education && (
              <div className="flex items-start space-x-3">
                <AcademicCapIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500">Education</p>
                  <p className="mt-1 text-sm text-gray-900">{userData.education}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Social Links */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Social Links</h2>
          {isOwnProfile && (
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
              className="text-gray-600 hover:text-blue-600"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {socialLinks.map((link) => {
            if (!link.value) return null;
            
            const Icon = link.icon;
            return (
              <div key={link.key} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Icon className="h-5 w-5 text-gray-400 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-500">{link.label}</p>
                  <a 
                    href={link.value} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 truncate block"
                  >
                    {link.value}
                  </a>
                </div>
              </div>
            );
          })}
        </div>

        {socialLinks.every(link => !link.value) && (
          <div className="text-center py-8">
            <GlobeAltIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No social links added yet.</p>
            {isOwnProfile && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="mt-4"
              >
                Add Social Links
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
