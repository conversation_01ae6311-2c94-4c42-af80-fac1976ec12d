"use client";

import { useState } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import {
  XMarkIcon,
  PhoneIcon,
  VideoCameraIcon,
  BellIcon,
  BellSlashIcon,
  ArchiveBoxIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  PhotoIcon,
  DocumentIcon,
  LinkIcon
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

interface User {
  id: string;
  name: string;
  image?: string;
  isOnline?: boolean;
  lastSeen?: string;
}

interface ConversationInfoProps {
  user: User;
  onClose: () => void;
  onArchive: () => void;
  onDelete: () => void;
  onBlock: () => void;
}

export function ConversationInfo({
  user,
  onClose,
  onArchive,
  onDelete,
  onBlock
}: ConversationInfoProps) {
  const [isNotificationEnabled, setIsNotificationEnabled] = useState(true);
  const [activeTab, setActiveTab] = useState<'media' | 'files' | 'links'>('media');

  return (
    <div className="bg-white flex flex-col h-full overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Conversation Info</h3>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto conversation-info-scroll">
        {/* User info */}
        <div className="p-6 text-center border-b border-gray-200">
        <div className="relative inline-block">
          {user.image ? (
            <Image
              src={user.image}
              alt={user.name}
              width={80}
              height={80}
              className="w-20 h-20 rounded-full object-cover"
            />
          ) : (
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <span className="text-2xl font-semibold text-white">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          {user.isOnline && (
            <span className="absolute bottom-1 right-1 w-5 h-5 bg-green-500 rounded-full ring-2 ring-white" />
          )}
        </div>

        <h4 className="mt-3 text-xl font-semibold text-gray-900">{user.name}</h4>
        <p className="text-sm text-gray-500">
          {user.isOnline ? (
            <span className="text-green-600 font-medium">Online</span>
          ) : user.lastSeen ? (
            `Last seen ${user.lastSeen}`
          ) : (
            "Offline"
          )}
        </p>

        {/* Action buttons */}
        <div className="flex justify-center space-x-4 mt-4">
          <button className="p-3 bg-blue-100 rounded-full hover:bg-blue-200 transition-colors">
            <PhoneIcon className="h-5 w-5 text-blue-600" />
          </button>
          <button className="p-3 bg-blue-100 rounded-full hover:bg-blue-200 transition-colors">
            <VideoCameraIcon className="h-5 w-5 text-blue-600" />
          </button>
        </div>
      </div>

      {/* Settings */}
      <div className="p-4 border-b border-gray-200">
        <div className="space-y-3">
          {/* Notifications */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {isNotificationEnabled ? (
                <BellIcon className="h-5 w-5 text-gray-600" />
              ) : (
                <BellSlashIcon className="h-5 w-5 text-gray-600" />
              )}
              <span className="text-sm font-medium text-gray-900">Notifications</span>
            </div>
            <button
              onClick={() => setIsNotificationEnabled(!isNotificationEnabled)}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                isNotificationEnabled ? "bg-blue-600" : "bg-gray-200"
              )}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  isNotificationEnabled ? "translate-x-6" : "translate-x-1"
                )}
              />
            </button>
          </div>
        </div>
      </div>

        {/* Shared content */}
        <div className="border-b border-gray-200">
          <div className="p-4">
            <h5 className="text-sm font-medium text-gray-900 mb-3">Shared Content</h5>

            {/* Tabs */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              {(['media', 'files', 'links'] as const).map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={cn(
                    "flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                    activeTab === tab
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  )}
                >
                  {tab === 'media' && 'Media'}
                  {tab === 'files' && 'Files'}
                  {tab === 'links' && 'Links'}
                </button>
              ))}
            </div>
          </div>

          {/* Content area */}
          <div className="p-4 max-h-64 overflow-y-auto conversation-info-scroll">
          {activeTab === 'media' && (
            <div className="grid grid-cols-3 gap-2">
              {/* Placeholder for media */}
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].map((i) => (
                <div key={i} className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
                  <PhotoIcon className="h-6 w-6 text-gray-400" />
                </div>
              ))}
            </div>
          )}

          {activeTab === 'files' && (
            <div className="space-y-2">
              {/* Placeholder for files */}
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                <div key={i} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                  <DocumentIcon className="h-8 w-8 text-gray-400" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">Document {i}.pdf</p>
                    <p className="text-xs text-gray-500">{(Math.random() * 5 + 1).toFixed(1)} MB • {Math.floor(Math.random() * 30 + 1)} days ago</p>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'links' && (
            <div className="space-y-2">
              {/* Placeholder for links */}
              {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                <div key={i} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                  <LinkIcon className="h-8 w-8 text-gray-400" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">https://example.com/article-{i}</p>
                    <p className="text-xs text-gray-500">Shared {Math.floor(Math.random() * 7 + 1)} days ago</p>
                  </div>
                </div>
              ))}
            </div>
          )}
          </div>
        </div>
      </div>

      {/* Danger zone */}
      <div className="p-4 border-t border-gray-200 space-y-2">
        <Button
          onClick={onArchive}
          variant="outline"
          className="w-full justify-start"
        >
          <ArchiveBoxIcon className="h-4 w-4 mr-2" />
          Archive Conversation
        </Button>

        <Button
          onClick={onBlock}
          variant="outline"
          className="w-full justify-start text-orange-600 border-orange-200 hover:bg-orange-50"
        >
          <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
          Block User
        </Button>

        <Button
          onClick={onDelete}
          variant="danger"
          className="w-full justify-start"
        >
          <TrashIcon className="h-4 w-4 mr-2" />
          Delete Conversation
        </Button>
      </div>
    </div>
  );
}
