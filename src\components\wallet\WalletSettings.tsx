"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { PinChangeModal } from "./PinChangeModal";
import { PaymentMethodsManager } from "./PaymentMethodsManager";
import {
  ShieldCheckIcon,
  CreditCardIcon,
  BellIcon,
  ChartBarIcon,
  KeyIcon,
  Cog6ToothIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  BanknotesIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface WalletSettingsProps {
  userId: string;
}

interface UserSettings {
  hasPin: boolean;
  pinSetAt?: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  transactionAlerts: boolean;
  dailyLimits: {
    deposit: string;
    send: string;
    cashout: string;
  };
  paymentMethods: any[]; // For deposits and purchases
  payoutMethods: any[];  // For withdrawals
}

export function WalletSettings({ userId }: WalletSettingsProps) {
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [showPinChangeModal, setShowPinChangeModal] = useState(false);
  const [showPaymentMethodsModal, setShowPaymentMethodsModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'security' | 'payment-methods' | 'payout-methods' | 'notifications' | 'limits'>('security');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/wallet/settings');
      const data = await response.json();

      if (data.success) {
        setSettings(data.settings);
      } else {
        toast.error('Failed to load settings');
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const updateNotificationSetting = async (key: string, value: boolean) => {
    try {
      const response = await fetch('/api/wallet/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          [key]: value,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSettings(prev => prev ? { ...prev, [key]: value } : null);
        toast.success('Setting updated successfully');
      } else {
        toast.error('Failed to update setting');
      }
    } catch (error) {
      console.error('Error updating setting:', error);
      toast.error('Failed to update setting');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
            <div className="h-6 bg-gray-300 rounded w-1/4 mb-4" />
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded w-3/4" />
              <div className="h-4 bg-gray-300 rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">Failed to load wallet settings</p>
        <Button onClick={fetchSettings} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  const tabs = [
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'payment-methods', name: 'Payment Methods', icon: CreditCardIcon },
    { id: 'payout-methods', name: 'Payout Methods', icon: BanknotesIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'limits', name: 'Account Limits', icon: ChartBarIcon },
  ] as const;

  return (
    <div className="flex flex-col lg:flex-row gap-6">
      {/* Left Sidebar Navigation */}
      <div className="lg:w-64 flex-shrink-0">
        <div className="bg-white rounded-lg border border-gray-200 p-4 lg:sticky lg:top-6">
          <h3 className="text-sm font-medium text-gray-900 mb-4 hidden lg:block">Settings</h3>

          {/* Mobile: Horizontal scroll navigation */}
          <div className="lg:hidden mb-4">
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-shrink-0 flex flex-col items-center px-3 py-2 text-xs font-medium rounded-md transition-colors min-w-[80px] ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-5 w-5 mb-1" />
                    <span className="text-center leading-tight">{tab.name}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Desktop: Vertical navigation */}
          <nav className="space-y-1 hidden lg:block">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span className="truncate">{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 min-w-0">

        {/* Security Tab */}
        {activeTab === 'security' && (
          <div className="space-y-6">
          {/* PIN Management */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <KeyIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Wallet PIN</h3>
                  <p className="text-sm text-gray-600">
                    Secure your wallet transactions with a 4-digit PIN
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {settings.hasPin ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircleIcon className="h-5 w-5 mr-1" />
                    <span className="text-sm font-medium">Active</span>
                  </div>
                ) : (
                  <div className="flex items-center text-yellow-600">
                    <ExclamationTriangleIcon className="h-5 w-5 mr-1" />
                    <span className="text-sm font-medium">Not Set</span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                {settings.hasPin ? (
                  <div className="text-sm text-gray-600">
                    <p>PIN was set on {settings.pinSetAt ? new Date(settings.pinSetAt).toLocaleDateString() : 'Unknown'}</p>
                    <p className="mt-1">Change your PIN regularly for better security</p>
                  </div>
                ) : (
                  <div className="text-sm text-gray-600">
                    <p>Set up a PIN to secure your wallet transactions</p>
                    <p className="mt-1">You'll need this PIN for all wallet operations</p>
                  </div>
                )}
              </div>
              <Button
                onClick={() => setShowPinChangeModal(true)}
                variant={settings.hasPin ? "outline" : "default"}
              >
                {settings.hasPin ? 'Change PIN' : 'Set PIN'}
              </Button>
            </div>
          </div>

          {/* Security Tips */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <ShieldCheckIcon className="h-6 w-6 text-blue-600 mr-3" />
              <h3 className="text-lg font-semibold text-blue-900">Security Tips</h3>
            </div>
            <ul className="space-y-2 text-sm text-blue-800">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                Never share your wallet PIN with anyone
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                Use a unique PIN that's different from your other accounts
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                Change your PIN regularly for better security
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                Log out from shared devices after using your wallet
              </li>
            </ul>
          </div>
          </div>
        )}

        {/* Payment Methods Tab */}
        {activeTab === 'payment-methods' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <CreditCardIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Payment Methods</h3>
                    <p className="text-sm text-gray-600">
                      Add payment methods for deposits and app purchases
                    </p>
                  </div>
                </div>
                <Button onClick={() => setShowPaymentMethodsModal(true)}>
                  Add Method
                </Button>
              </div>

              <PaymentMethodsManager
                paymentMethods={settings.paymentMethods}
                onUpdate={fetchSettings}
                methodType="payment"
              />
            </div>

            {/* Payment Methods Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <CreditCardIcon className="h-6 w-6 text-blue-600 mr-3" />
                <h3 className="text-lg font-semibold text-blue-900">Payment Methods Usage</h3>
              </div>
              <ul className="space-y-2 text-sm text-blue-800">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Add funds to your General Wallet
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Purchase premium features and subscriptions
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Buy products from marketplace
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Secure and encrypted payment processing
                </li>
              </ul>
            </div>
          </div>
        )}

        {/* Payout Methods Tab */}
        {activeTab === 'payout-methods' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <BanknotesIcon className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Payout Methods</h3>
                    <p className="text-sm text-gray-600">
                      Add payout methods to withdraw from your Earning Wallet
                    </p>
                  </div>
                </div>
                <Button onClick={() => setShowPaymentMethodsModal(true)}>
                  Add Method
                </Button>
              </div>

              <PaymentMethodsManager
                paymentMethods={settings.payoutMethods}
                onUpdate={fetchSettings}
                methodType="payout"
              />
            </div>

            {/* Payout Methods Info */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <BanknotesIcon className="h-6 w-6 text-green-600 mr-3" />
                <h3 className="text-lg font-semibold text-green-900">Payout Methods Usage</h3>
              </div>
              <ul className="space-y-2 text-sm text-green-800">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Withdraw earnings from your Earning Wallet
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Receive cashout payments through agents
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Bank transfers and mobile banking
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                  Fast and secure withdrawal processing
                </li>
              </ul>
            </div>
          </div>
        )}

        {/* Notifications Tab */}
        {activeTab === 'notifications' && (
          <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <BellIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Notification Preferences</h3>
                <p className="text-sm text-gray-600">
                  Choose how you want to be notified about wallet activities
                </p>
              </div>
            </div>

            <div className="space-y-4">
              {/* Email Notifications */}
              <div className="flex items-center justify-between py-3 border-b border-gray-200">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                  <p className="text-sm text-gray-600">Receive wallet updates via email</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.emailNotifications}
                    onChange={(e) => updateNotificationSetting('emailNotifications', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* SMS Notifications */}
              <div className="flex items-center justify-between py-3 border-b border-gray-200">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">SMS Notifications</h4>
                  <p className="text-sm text-gray-600">Receive important alerts via SMS</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.smsNotifications}
                    onChange={(e) => updateNotificationSetting('smsNotifications', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Transaction Alerts */}
              <div className="flex items-center justify-between py-3">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Transaction Alerts</h4>
                  <p className="text-sm text-gray-600">Get notified for all wallet transactions</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.transactionAlerts}
                    onChange={(e) => updateNotificationSetting('transactionAlerts', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>
          </div>
        )}

        {/* Account Limits Tab */}
        {activeTab === 'limits' && (
          <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <ChartBarIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Daily Transaction Limits</h3>
                <p className="text-sm text-gray-600">
                  Your current daily transaction limits
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Deposit Limit */}
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <BanknotesIcon className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-900">Deposit</span>
                  </div>
                </div>
                <p className="text-2xl font-bold text-blue-900">${settings.dailyLimits.deposit}</p>
                <p className="text-xs text-blue-700 mt-1">Daily limit</p>
              </div>

              {/* Send Limit */}
              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <BanknotesIcon className="h-5 w-5 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-green-900">Send</span>
                  </div>
                </div>
                <p className="text-2xl font-bold text-green-900">${settings.dailyLimits.send}</p>
                <p className="text-xs text-green-700 mt-1">Daily limit</p>
              </div>

              {/* Cashout Limit */}
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <BanknotesIcon className="h-5 w-5 text-orange-600 mr-2" />
                    <span className="text-sm font-medium text-orange-900">Cashout</span>
                  </div>
                </div>
                <p className="text-2xl font-bold text-orange-900">${settings.dailyLimits.cashout}</p>
                <p className="text-xs text-orange-700 mt-1">Daily limit</p>
              </div>
            </div>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <ClockIcon className="h-5 w-5 text-gray-600 mr-2" />
                <span className="text-sm text-gray-700">
                  Limits reset daily at 12:00 AM UTC. Contact support to request limit increases.
                </span>
              </div>
            </div>
          </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <PinChangeModal
        isOpen={showPinChangeModal}
        onClose={() => setShowPinChangeModal(false)}
        onSuccess={() => {
          setShowPinChangeModal(false);
          fetchSettings();
        }}
        hasExistingPin={settings.hasPin}
      />
    </div>
  );
}
