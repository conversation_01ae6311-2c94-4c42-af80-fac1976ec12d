import { NextResponse } from "next/server";
import { validateApiSession } from "@/lib/utils/session-validation";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

const suspendSchema = z.object({
  reason: z.string().min(1, "Suspension reason is required"),
});

// Suspend user account (Admin only)
export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    // Validate session with admin requirement
    const { response: authError, user: currentUser } = await validateApiSession(true);
    if (authError) return authError;

    const { userId } = await context.params;
    const body = await req.json();

    // Validate request body
    const validatedData = suspendSchema.parse(body);

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Prevent suspending admin users
    if (user.isAdmin) {
      return NextResponse.json(
        { message: "Cannot suspend admin users" },
        { status: 400 }
      );
    }

    // Update user status to suspended
    await db.update(users)
      .set({
        status: 'suspended',
        isActive: false,
        suspendedAt: new Date(),
        suspendedReason: validatedData.reason,
        suspendedBy: currentUser.id,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Log the action
    console.log(`Admin ${currentUser.id} suspended user ${userId} for: ${validatedData.reason}`);

    return NextResponse.json({
      success: true,
      message: "User account suspended successfully",
    });
  } catch (error: any) {
    console.error("Error suspending user account:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid request data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to suspend user account"
      },
      { status: 500 }
    );
  }
}
