"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { toast } from "react-hot-toast";
import {
  XMarkIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  ShieldExclamationIcon,
  KeyIcon,
  BanknotesIcon,
} from "@heroicons/react/24/outline";

interface WalletSettings {
  isActive: boolean;
  hasPin: boolean;
  pinSetAt: string | null;
  dailyLimits: {
    deposit: string;
    send: string;
    cashout: string;
  };
  balances: {
    general: string;
    earning: string;
  };
  totals: {
    deposited: string;
    withdrawn: string;
    sent: string;
    received: string;
  };
}

interface WalletSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
  onSuccess: () => void;
}

export function WalletSettingsModal({
  isOpen,
  onClose,
  userId,
  userName,
  onSuccess
}: WalletSettingsModalProps) {
  const [settings, setSettings] = useState<WalletSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    isActive: true,
    dailyDepositLimit: "",
    dailySendLimit: "",
    dailyCashoutLimit: "",
  });

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/users/${userId}/wallet/settings`);

      if (!response.ok) {
        throw new Error("Failed to fetch wallet settings");
      }

      const data = await response.json();
      setSettings(data.data);
      setFormData({
        isActive: data.data.isActive,
        dailyDepositLimit: data.data.dailyLimits.deposit,
        dailySendLimit: data.data.dailyLimits.send,
        dailyCashoutLimit: data.data.dailyLimits.cashout,
      });
    } catch (error) {
      console.error("Error fetching wallet settings:", error);
      toast.error("Failed to fetch wallet settings");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchSettings();
    }
  }, [isOpen, userId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);

      const response = await fetch(`/api/admin/users/${userId}/wallet/settings`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isActive: formData.isActive,
          dailyDepositLimit: parseFloat(formData.dailyDepositLimit),
          dailySendLimit: parseFloat(formData.dailySendLimit),
          dailyCashoutLimit: parseFloat(formData.dailyCashoutLimit),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to update wallet settings");
      }

      toast.success("Wallet settings updated successfully");
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Error updating wallet settings:", error);
      toast.error(error.message || "Failed to update wallet settings");
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-full">
              <Cog6ToothIcon className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Wallet Settings
              </h2>
              <p className="text-sm text-gray-600">
                Manage {userName}'s wallet settings
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : settings ? (
            <div className="p-6 space-y-6">
              {/* Current Status */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Current Status</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    {settings.isActive ? (
                      <ShieldCheckIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <ShieldExclamationIcon className="h-5 w-5 text-red-500" />
                    )}
                    <span className="text-sm text-gray-700">
                      Wallet {settings.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <KeyIcon className="h-5 w-5 text-blue-500" />
                    <span className="text-sm text-gray-700">
                      PIN {settings.hasPin ? 'Set' : 'Not Set'}
                    </span>
                    {settings.hasPin && (
                      <Badge variant="success" className="text-xs">
                        {new Date(settings.pinSetAt!).toLocaleDateString()}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Balances */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Current Balances</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-white rounded border">
                    <BanknotesIcon className="h-6 w-6 text-blue-500 mx-auto mb-1" />
                    <p className="text-lg font-semibold text-gray-900">
                      ৳{parseFloat(settings.balances.general).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-600">General Wallet</p>
                  </div>
                  <div className="text-center p-3 bg-white rounded border">
                    <BanknotesIcon className="h-6 w-6 text-green-500 mx-auto mb-1" />
                    <p className="text-lg font-semibold text-gray-900">
                      ৳{parseFloat(settings.balances.earning).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-600">Earning Wallet</p>
                  </div>
                </div>
              </div>

              {/* Transaction Totals */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Transaction Totals</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Deposited:</span>
                      <span className="text-sm font-medium text-green-600">
                        ৳{parseFloat(settings.totals.deposited).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Received:</span>
                      <span className="text-sm font-medium text-green-600">
                        ৳{parseFloat(settings.totals.received).toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Withdrawn:</span>
                      <span className="text-sm font-medium text-red-600">
                        ৳{parseFloat(settings.totals.withdrawn).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Sent:</span>
                      <span className="text-sm font-medium text-red-600">
                        ৳{parseFloat(settings.totals.sent).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Settings Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                <h3 className="text-sm font-medium text-gray-900">Update Settings</h3>

                {/* Wallet Status */}
                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={(e) => handleInputChange('isActive', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Wallet Active</span>
                  </label>
                  <p className="text-xs text-gray-500 mt-1">
                    When inactive, user cannot perform any wallet transactions
                  </p>
                </div>

                {/* Daily Limits */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Daily Deposit Limit
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        ৳
                      </span>
                      <input
                        type="number"
                        value={formData.dailyDepositLimit}
                        onChange={(e) => handleInputChange('dailyDepositLimit', e.target.value)}
                        min="0"
                        step="0.01"
                        className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Daily Send Limit
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        ৳
                      </span>
                      <input
                        type="number"
                        value={formData.dailySendLimit}
                        onChange={(e) => handleInputChange('dailySendLimit', e.target.value)}
                        min="0"
                        step="0.01"
                        className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Daily Cashout Limit
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        ৳
                      </span>
                      <input
                        type="number"
                        value={formData.dailyCashoutLimit}
                        onChange={(e) => handleInputChange('dailyCashoutLimit', e.target.value)}
                        min="0"
                        step="0.01"
                        className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isSaving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      "Save Settings"
                    )}
                  </Button>
                </div>
              </form>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">Failed to load wallet settings</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
