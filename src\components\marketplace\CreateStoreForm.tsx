"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { useDropzone } from "react-dropzone";
import { PhotoIcon } from "@heroicons/react/24/outline";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";

const storeSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(255, "Name must be less than 255 characters"),
  slug: z
    .string()
    .min(3, "Slug must be at least 3 characters")
    .max(100, "Slug must be less than 100 characters")
    .regex(/^[a-zA-Z0-9-]+$/, "Slug can only contain letters, numbers, and hyphens"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  location: z.string().max(255, "Location must be less than 255 characters").optional(),
});

type StoreFormData = z.infer<typeof storeSchema>;

export function CreateStoreForm() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [bannerPreview, setBannerPreview] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<StoreFormData>({
    resolver: zodResolver(storeSchema),
  });

  // Generate slug from name
  const watchName = watch("name", "");
  const generateSlug = () => {
    return watchName
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^a-z0-9-]/g, "")
      .substring(0, 100);
  };

  // Logo dropzone
  const { getRootProps: getLogoRootProps, getInputProps: getLogoInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setLogoFile(file);
        setLogoPreview(URL.createObjectURL(file));
      }
    },
  });

  // Banner dropzone
  const { getRootProps: getBannerRootProps, getInputProps: getBannerInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setBannerFile(file);
        setBannerPreview(URL.createObjectURL(file));
      }
    },
  });

  const onSubmit = async (data: StoreFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Upload images to Cloudinary if provided
      let logoUrl = null;
      let bannerUrl = null;

      if (logoFile) {
        const logoUrls = await uploadMultipleToCloudinary([logoFile]);
        if (logoUrls.length > 0) {
          logoUrl = logoUrls[0];
        }
      }

      if (bannerFile) {
        const bannerUrls = await uploadMultipleToCloudinary([bannerFile]);
        if (bannerUrls.length > 0) {
          bannerUrl = bannerUrls[0];
        }
      }

      // Create store
      const response = await fetch("/api/marketplace/stores", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          logo: logoUrl,
          banner: bannerUrl,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to create store");
      }

      // Redirect to store dashboard
      router.push("/my-store/dashboard");
      router.refresh();
    } catch (err) {
      console.error("Error creating store:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div className="sm:col-span-2">
          <Input
            label="Store Name *"
            placeholder="Enter your store name"
            error={errors.name?.message}
            {...register("name")}
          />
        </div>

        <div className="sm:col-span-2">
          <Input
            label="Store URL Slug *"
            placeholder="your-store-name"
            error={errors.slug?.message}
            {...register("slug", {
              onChange: (e) => {
                if (!e.target.value) {
                  e.target.value = generateSlug();
                }
              },
            })}
          />
          <p className="mt-1 text-xs text-gray-500">
            This will be used in your store URL: /store/<span className="font-mono">{watch("slug") || "your-store-slug"}</span>
          </p>
        </div>

        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Store Description
          </label>
          <textarea
            className="w-full rounded-md border border-gray-300 px-3 py-2 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
            placeholder="Describe your store and what you sell"
            {...register("description")}
          ></textarea>
          {errors.description && (
            <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
          )}
        </div>

        <div>
          <Input
            label="Location"
            placeholder="City, State, Country"
            error={errors.location?.message}
            {...register("location")}
          />
        </div>

        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Store Logo
          </label>
          <div
            {...getLogoRootProps()}
            className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pb-6 pt-5"
          >
            <div className="space-y-1 text-center">
              <input {...getLogoInputProps()} />
              {logoPreview ? (
                <div className="mx-auto h-32 w-32 overflow-hidden rounded-full">
                  <img
                    src={logoPreview}
                    alt="Logo preview"
                    className="h-full w-full object-cover"
                  />
                </div>
              ) : (
                <>
                  <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="text-sm text-gray-600">
                    <span className="font-medium text-blue-600 hover:text-blue-500">
                      Upload a logo
                    </span>{" "}
                    or drag and drop
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="sm:col-span-2">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Store Banner
          </label>
          <div
            {...getBannerRootProps()}
            className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pb-6 pt-5"
          >
            <div className="space-y-1 text-center">
              <input {...getBannerInputProps()} />
              {bannerPreview ? (
                <div className="mx-auto h-32 w-full overflow-hidden rounded-md">
                  <img
                    src={bannerPreview}
                    alt="Banner preview"
                    className="h-full w-full object-cover"
                  />
                </div>
              ) : (
                <>
                  <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="text-sm text-gray-600">
                    <span className="font-medium text-blue-600 hover:text-blue-500">
                      Upload a banner
                    </span>{" "}
                    or drag and drop
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" isLoading={isSubmitting}>
          Create Store
        </Button>
      </div>
    </form>
  );
}
