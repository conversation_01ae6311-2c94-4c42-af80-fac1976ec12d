import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { z } from "zod";

const calculateFeeSchema = z.object({
  amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Amount must be a positive number"),
});

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = calculateFeeSchema.parse(body);

    // Check if deposits are enabled
    const depositEnabled = await WalletService.getWalletSetting('deposit_enabled');
    if (depositEnabled === 'false') {
      return NextResponse.json(
        { 
          success: false,
          message: "Deposits are currently disabled" 
        },
        { status: 400 }
      );
    }

    // Get deposit settings
    const minAmount = parseFloat(await WalletService.getWalletSetting('deposit_min_amount') || '1.00');
    const maxAmount = parseFloat(await WalletService.getWalletSetting('deposit_max_amount') || '10000.00');
    const feePercentage = parseFloat(await WalletService.getWalletSetting('deposit_fee_percentage') || '0.00');
    const feeFixed = parseFloat(await WalletService.getWalletSetting('deposit_fee_fixed') || '0.00');

    const amount = parseFloat(validatedData.amount);

    // Validate amount limits
    if (amount < minAmount) {
      return NextResponse.json(
        { 
          success: false,
          message: `Minimum deposit amount is $${minAmount.toFixed(2)}` 
        },
        { status: 400 }
      );
    }

    if (amount > maxAmount) {
      return NextResponse.json(
        { 
          success: false,
          message: `Maximum deposit amount is $${maxAmount.toFixed(2)}` 
        },
        { status: 400 }
      );
    }

    // Calculate fees
    const percentageFee = (amount * feePercentage) / 100;
    const totalFee = percentageFee + feeFixed;
    const netAmount = amount - totalFee;

    return NextResponse.json({
      success: true,
      data: {
        amount: amount.toFixed(2),
        feePercentage: feePercentage.toFixed(2),
        feeFixed: feeFixed.toFixed(2),
        percentageFee: percentageFee.toFixed(2),
        totalFee: totalFee.toFixed(2),
        netAmount: netAmount.toFixed(2),
        limits: {
          min: minAmount.toFixed(2),
          max: maxAmount.toFixed(2),
        },
      },
    });
  } catch (error: any) {
    console.error("Error calculating deposit fee:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid input data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: "Failed to calculate deposit fee" 
      },
      { status: 500 }
    );
  }
}
