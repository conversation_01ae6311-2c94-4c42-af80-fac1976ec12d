import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogCategories, blogs } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, sql } from "drizzle-orm";

const categorySchema = z.object({
  name: z.string().min(1).max(100),
  slug: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "Invalid color format"),
});

// Get all blog categories
export async function GET() {
  try {
    // Fetch categories with blog count
    const categories = await db.query.blogCategories.findMany({
      orderBy: [blogCategories.name],
    });

    // Get blog count for each category
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const blogCount = await db
          .select({ count: sql<number>`count(*)` })
          .from(blogs)
          .where(eq(blogs.categoryId, category.id));

        return {
          ...category,
          _count: {
            blogs: blogCount[0]?.count || 0,
          },
        };
      })
    );

    return NextResponse.json(categoriesWithCount);
  } catch (error) {
    console.error("Error fetching blog categories:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new blog category
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // For now, only allow admins to create categories
    // You can modify this based on your requirements
    if (!session.user.isAdmin) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const validatedData = categorySchema.parse(body);

    // Check if category with same name or slug exists
    const existingCategory = await db.query.blogCategories.findFirst({
      where: eq(blogCategories.slug, validatedData.slug),
    });

    if (existingCategory) {
      return NextResponse.json(
        { message: "Category with this slug already exists" },
        { status: 400 }
      );
    }

    const categoryId = uuidv4();

    // Insert category into database
    await db.insert(blogCategories).values({
      id: categoryId,
      name: validatedData.name,
      slug: validatedData.slug,
      description: validatedData.description || null,
      color: validatedData.color,
    });

    return NextResponse.json(
      { 
        message: "Category created successfully", 
        id: categoryId 
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating blog category:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
