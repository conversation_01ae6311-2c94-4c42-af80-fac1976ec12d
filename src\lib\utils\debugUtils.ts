/**
 * Debug and logging utilities for messaging system
 */

export interface DebugInfo {
  timestamp: string;
  component: string;
  action: string;
  data?: any;
  error?: any;
}

class MessageDebugger {
  private logs: DebugInfo[] = [];
  private maxLogs = 100;

  log(component: string, action: string, data?: any, error?: any) {
    const debugInfo: DebugInfo = {
      timestamp: new Date().toISOString(),
      component,
      action,
      data,
      error
    };

    this.logs.push(debugInfo);

    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console log in development
    if (process.env.NODE_ENV === 'development') {
      const logLevel = error ? 'error' : 'info';
      console[logLevel](`[${component}] ${action}`, data || '', error || '');
    }
  }

  getLogs(): DebugInfo[] {
    return [...this.logs];
  }

  getLogsForComponent(component: string): DebugInfo[] {
    return this.logs.filter(log => log.component === component);
  }

  clearLogs() {
    this.logs = [];
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Global debugger instance
export const messageDebugger = new MessageDebugger();

// Helper functions for common debug scenarios
export const debugConversationFetch = (data: any, error?: any) => {
  messageDebugger.log('ConversationFetch', 'API_CALL', data, error);
};

export const debugMessageFetch = (conversationId: string, data: any, error?: any) => {
  messageDebugger.log('MessageFetch', `CONVERSATION_${conversationId}`, data, error);
};

export const debugMessageSend = (conversationId: string, content: string, result?: any, error?: any) => {
  messageDebugger.log('MessageSend', `TO_${conversationId}`, { content, result }, error);
};

export const debugSocketEvent = (eventType: string, data: any) => {
  messageDebugger.log('Socket', eventType, data);
};

export const debugConversationUpdate = (action: string, conversationId: string, data?: any) => {
  messageDebugger.log('ConversationUpdate', `${action}_${conversationId}`, data);
};

export const debugValidation = (type: 'message' | 'conversation', data: any, isValid: boolean, errors?: string[]) => {
  messageDebugger.log('Validation', `${type.toUpperCase()}_VALIDATION`, { 
    data, 
    isValid, 
    errors 
  }, !isValid ? new Error(`Invalid ${type}`) : undefined);
};

// Performance monitoring
export class PerformanceMonitor {
  private timers: Map<string, number> = new Map();

  start(operation: string): void {
    this.timers.set(operation, performance.now());
  }

  end(operation: string): number {
    const startTime = this.timers.get(operation);
    if (!startTime) {
      console.warn(`No start time found for operation: ${operation}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(operation);

    messageDebugger.log('Performance', operation, { duration: `${duration.toFixed(2)}ms` });
    
    return duration;
  }

  measure<T>(operation: string, fn: () => T): T {
    this.start(operation);
    try {
      const result = fn();
      this.end(operation);
      return result;
    } catch (error) {
      this.end(operation);
      throw error;
    }
  }

  async measureAsync<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    this.start(operation);
    try {
      const result = await fn();
      this.end(operation);
      return result;
    } catch (error) {
      this.end(operation);
      throw error;
    }
  }
}

export const performanceMonitor = new PerformanceMonitor();

// Error boundary helper
export const handleMessagingError = (error: any, context: string, additionalData?: any) => {
  const errorInfo = {
    message: error?.message || 'Unknown error',
    stack: error?.stack,
    context,
    additionalData,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server'
  };

  messageDebugger.log('Error', context, errorInfo, error);

  // In production, you might want to send this to an error reporting service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error reporting service
    // errorReportingService.captureException(error, errorInfo);
  }

  return errorInfo;
};

// Health check utilities
export const checkMessagingSystemHealth = () => {
  const health = {
    timestamp: new Date().toISOString(),
    logs: messageDebugger.getLogs().length,
    recentErrors: messageDebugger.getLogs()
      .filter(log => log.error && Date.now() - new Date(log.timestamp).getTime() < 300000) // Last 5 minutes
      .length,
    components: {
      conversations: messageDebugger.getLogsForComponent('ConversationFetch').length > 0,
      messages: messageDebugger.getLogsForComponent('MessageFetch').length > 0,
      socket: messageDebugger.getLogsForComponent('Socket').length > 0,
    }
  };

  messageDebugger.log('HealthCheck', 'SYSTEM_STATUS', health);
  return health;
};
