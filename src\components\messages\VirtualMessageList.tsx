"use client";

import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { cn } from "@/lib/utils";
import { messagePerformanceMonitor } from "@/lib/monitoring/messagePerformance";

interface VirtualItem {
  id: string;
  index: number;
  height: number;
  offsetTop: number;
}

interface VirtualMessageListProps {
  messages: any[];
  renderMessage: (message: any, index: number) => React.ReactNode;
  className?: string;
  itemHeight?: number;
  overscan?: number;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoading?: boolean;
  threshold?: number;
}

export function VirtualMessageList({
  messages,
  renderMessage,
  className = "",
  itemHeight = 80,
  overscan = 5,
  onLoadMore,
  hasMore = false,
  isLoading = false,
  threshold = 200,
}: VirtualMessageListProps) {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const [itemHeights, setItemHeights] = useState<Map<string, number>>(new Map());
  const [isScrolling, setIsScrolling] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const measurementCache = useRef<Map<string, number>>(new Map());
  const lastScrollTop = useRef(0);
  const scrollDirection = useRef<'up' | 'down'>('down');

  // Calculate virtual items
  const virtualItems = useMemo(() => {
    const items: VirtualItem[] = [];
    let offsetTop = 0;

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const cachedHeight = itemHeights.get(message.id) || itemHeight;
      
      items.push({
        id: message.id,
        index: i,
        height: cachedHeight,
        offsetTop,
      });

      offsetTop += cachedHeight;
    }

    return items;
  }, [messages, itemHeights, itemHeight]);

  // Calculate total height
  const totalHeight = virtualItems.length > 0 
    ? virtualItems[virtualItems.length - 1].offsetTop + virtualItems[virtualItems.length - 1].height
    : 0;

  // Calculate visible range
  const visibleRange = useMemo(() => {
    if (virtualItems.length === 0) return { start: 0, end: 0 };

    const start = Math.max(0, 
      virtualItems.findIndex(item => item.offsetTop + item.height >= scrollTop) - overscan
    );
    
    const end = Math.min(virtualItems.length - 1,
      virtualItems.findIndex(item => item.offsetTop > scrollTop + containerHeight) + overscan
    );

    return { start, end: end === -1 ? virtualItems.length - 1 : end };
  }, [virtualItems, scrollTop, containerHeight, overscan]);

  // Get visible items
  const visibleItems = virtualItems.slice(visibleRange.start, visibleRange.end + 1);

  // Handle scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    const newScrollTop = target.scrollTop;
    
    // Track scroll direction
    scrollDirection.current = newScrollTop > lastScrollTop.current ? 'down' : 'up';
    lastScrollTop.current = newScrollTop;
    
    setScrollTop(newScrollTop);
    setIsScrolling(true);

    // Performance tracking
    const scrollStart = Date.now();
    messagePerformanceMonitor.trackUIResponse('scroll', Date.now() - scrollStart);

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Set scroll end timeout
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);

    // Load more messages when scrolling up near the top
    if (hasMore && !isLoading && newScrollTop < threshold && scrollDirection.current === 'up') {
      onLoadMore?.();
    }
  }, [hasMore, isLoading, threshold, onLoadMore]);

  // Measure item height
  const measureItem = useCallback((id: string, element: HTMLElement) => {
    const height = element.getBoundingClientRect().height;
    
    if (height > 0 && height !== itemHeights.get(id)) {
      setItemHeights(prev => new Map(prev).set(id, height));
      measurementCache.current.set(id, height);
    }
  }, [itemHeights]);

  // Resize observer for container
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        setContainerHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(container);
    
    // Initial measurement
    setContainerHeight(container.clientHeight);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Auto-scroll to bottom for new messages
  const scrollToBottom = useCallback(() => {
    const container = containerRef.current;
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  }, []);

  // Scroll to specific message
  const scrollToMessage = useCallback((messageId: string) => {
    const container = containerRef.current;
    const item = virtualItems.find(item => item.id === messageId);
    
    if (container && item) {
      container.scrollTop = item.offsetTop;
    }
  }, [virtualItems]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-auto",
        className
      )}
      onScroll={handleScroll}
      style={{ height: '100%' }}
    >
      {/* Loading indicator at top */}
      {isLoading && (
        <div className="absolute top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm p-2">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-sm text-gray-600">Loading messages...</span>
          </div>
        </div>
      )}

      {/* Virtual container */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems.map((item) => {
          const message = messages[item.index];
          
          return (
            <div
              key={item.id}
              style={{
                position: 'absolute',
                top: item.offsetTop,
                left: 0,
                right: 0,
                height: item.height,
              }}
              ref={(el) => {
                if (el) {
                  measureItem(item.id, el);
                }
              }}
            >
              {renderMessage(message, item.index)}
            </div>
          );
        })}
      </div>

      {/* Scroll to bottom button */}
      {scrollTop > 200 && (
        <button
          onClick={scrollToBottom}
          className="fixed bottom-20 right-4 z-20 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 shadow-lg transition-all duration-200"
          title="Scroll to bottom"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      )}
    </div>
  );
}

// Hook for virtual scrolling
export function useVirtualScroll(
  items: any[],
  containerHeight: number,
  itemHeight: number = 80
) {
  const [scrollTop, setScrollTop] = useState(0);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 });

  useEffect(() => {
    const overscan = 5;
    const start = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(items.length - 1, start + visibleCount + overscan * 2);

    setVisibleRange({ start, end });
  }, [scrollTop, containerHeight, itemHeight, items.length]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  const visibleItems = items.slice(visibleRange.start, visibleRange.end + 1);
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    visibleRange,
  };
}

// Performance optimized message item component
export function MessageItem({ 
  message, 
  index, 
  isVisible = true 
}: { 
  message: any; 
  index: number; 
  isVisible?: boolean;
}) {
  const itemRef = useRef<HTMLDivElement>(null);

  // Intersection observer for lazy loading
  useEffect(() => {
    if (!isVisible || !itemRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Track when message becomes visible
            messagePerformanceMonitor.trackUIResponse('message_visible', 0);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(itemRef.current);

    return () => {
      observer.disconnect();
    };
  }, [isVisible]);

  if (!isVisible) {
    return <div style={{ height: '80px' }} />; // Placeholder
  }

  return (
    <div ref={itemRef} className="message-item">
      {/* Message content would go here */}
      <div className="p-4 border-b border-gray-100">
        <p>{message.content}</p>
        <span className="text-xs text-gray-500">
          {new Date(message.createdAt).toLocaleTimeString()}
        </span>
      </div>
    </div>
  );
}
