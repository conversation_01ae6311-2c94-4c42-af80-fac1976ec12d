import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventAttendees, users } from "@/lib/db/schema";
import { z } from "zod";
import { eq, and, count } from "drizzle-orm";

const eventUpdateSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  description: z.string().max(5000).optional().nullable(),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
  location: z.string().max(255).optional().nullable(),
  isOnline: z.boolean().optional(),
  onlineLink: z.string().url().optional().nullable(),
  visibility: z.enum(["public", "private", "friends"]).optional(),
  category: z.string().max(100).optional().nullable(),
  coverImage: z.string().url().optional().nullable(),
});

// Get a specific event
export async function GET(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;

    // Fetch the event directly from the database
    const eventData = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!eventData) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Fetch the host information separately
    const hostData = await db.query.users.findFirst({
      where: eq(users.id, eventData.hostId),
      columns: {
        id: true,
        name: true,
        username: true,
        image: true,
      },
    });

    if (!hostData) {
      return NextResponse.json(
        { message: "Host not found" },
        { status: 404 }
      );
    }

    // Combine event and host data
    const event = {
      ...eventData,
      host: hostData,
    };

    console.log("Event data retrieved:", JSON.stringify(event, null, 2));

    // Get attendee counts by status
    const goingCount = await db
      .select({ count: count() })
      .from(eventAttendees)
      .where(and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.status, "going")
      ));

    const interestedCount = await db
      .select({ count: count() })
      .from(eventAttendees)
      .where(and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.status, "interested")
      ));

    const notGoingCount = await db
      .select({ count: count() })
      .from(eventAttendees)
      .where(and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.status, "not_going")
      ));

    // Format attendee counts
    const formattedCounts = {
      going: Number(goingCount[0]?.count || 0),
      interested: Number(interestedCount[0]?.count || 0),
      not_going: Number(notGoingCount[0]?.count || 0),
    };

    console.log("Attendee counts retrieved:", JSON.stringify(formattedCounts, null, 2));

    // Check if the current user is attending
    const userAttendance = await db.query.eventAttendees.findFirst({
      where: and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.userId, session.user.id)
      ),
      columns: {
        status: true,
      },
    });

    console.log("User attendance retrieved:", userAttendance ? JSON.stringify(userAttendance, null, 2) : "null");

    return NextResponse.json({
      ...event,
      attendeeCounts: formattedCounts,
      userAttendance: userAttendance ? userAttendance.status : null,
    });
  } catch (error) {
    console.error("Error fetching event:", error);
    // Log more detailed error information
    if (error instanceof Error) {
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
    }

    // Provide more detailed error message
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string };

        if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
        } else if (dbError.sqlMessage) {
          // Log the SQL error but don't expose it to the client
          console.error("SQL Error:", dbError.sqlMessage);
        }
      }
    }

    return NextResponse.json(
      { message: errorMessage },
      { status: statusCode }
    );
  }
}

// Update an event
export async function PUT(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;
    const body = await req.json();
    const validatedData = eventUpdateSchema.parse(body);

    // Check if the event exists and the user is the host
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    if (event.hostId !== session.user.id) {
      return NextResponse.json(
        { message: "You are not authorized to update this event" },
        { status: 403 }
      );
    }

    // Update the event
    const updateData: any = {};

    if (validatedData.name) updateData.name = validatedData.name;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.startTime) updateData.startTime = new Date(validatedData.startTime);
    if (validatedData.endTime) updateData.endTime = new Date(validatedData.endTime);
    if (validatedData.location !== undefined) updateData.location = validatedData.location;
    if (validatedData.isOnline !== undefined) updateData.isOnline = validatedData.isOnline;
    if (validatedData.onlineLink !== undefined) updateData.onlineLink = validatedData.onlineLink;
    if (validatedData.visibility) updateData.visibility = validatedData.visibility;
    if (validatedData.category !== undefined) updateData.category = validatedData.category;
    if (validatedData.coverImage !== undefined) updateData.coverImage = validatedData.coverImage;

    // Update the event
    await db
      .update(events)
      .set(updateData)
      .where(eq(events.id, eventId));

    // Fetch the updated event
    const updatedEventData = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!updatedEventData) {
      return NextResponse.json(
        { message: "Failed to update event" },
        { status: 500 }
      );
    }

    // Fetch host data separately
    const hostData = await db.query.users.findFirst({
      where: eq(users.id, updatedEventData.hostId),
      columns: {
        id: true,
        name: true,
        username: true,
        image: true,
      },
    });

    // Combine event and host data
    const updatedEvent = {
      ...updatedEventData,
      host: hostData,
    };

    if (!updatedEvent) {
      return NextResponse.json(
        { message: "Failed to update event" },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error("Error updating event:", error);

    if (error instanceof z.ZodError) {
      // Format Zod errors to be more user-friendly
      const formattedErrors = error.errors.map(err => {
        const field = err.path.join('.') || 'unknown';
        return `${field}: ${err.message}`;
      });

      return NextResponse.json(
        {
          message: "Validation error",
          errors: error.errors,
          formattedErrors
        },
        { status: 400 }
      );
    }

    // Provide more detailed error message
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string };

        if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
        } else if (dbError.sqlMessage) {
          // Log the SQL error but don't expose it to the client
          console.error("SQL Error:", dbError.sqlMessage);
        }
      }
    }

    return NextResponse.json(
      { message: errorMessage },
      { status: statusCode }
    );
  }
}

// Delete an event
export async function DELETE(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { eventId } = params;

    // Check if the event exists and the user is the host
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    if (event.hostId !== session.user.id) {
      return NextResponse.json(
        { message: "You are not authorized to delete this event" },
        { status: 403 }
      );
    }

    // Delete the event
    await db.delete(events).where(eq(events.id, eventId));

    return NextResponse.json({ message: "Event deleted successfully" });
  } catch (error) {
    console.error("Error deleting event:", error);

    // Provide more detailed error message
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string };

        if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
        } else if (dbError.sqlMessage) {
          // Log the SQL error but don't expose it to the client
          console.error("SQL Error:", dbError.sqlMessage);
        }
      }
    }

    return NextResponse.json(
      { message: errorMessage },
      { status: statusCode }
    );
  }
}
