"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";

interface Photo {
  id: string;
  url: string;
  postId: string;
  postContent: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username: string;
    image: string | null;
  };
}

export function RecentPhotos() {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchRecentPhotos();
  }, []);

  const fetchRecentPhotos = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/photos/recent?limit=6');
      
      if (!response.ok) {
        throw new Error('Failed to fetch photos');
      }

      const data = await response.json();
      setPhotos(data.photos || []);

    } catch (error) {
      console.error('Error fetching recent photos:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch photos');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">
            Recent Photos
          </h2>
          <Link href="/photos" className="text-sm text-blue-500 hover:text-blue-600 hover:underline transition-colors duration-200">
            See All
          </Link>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="aspect-square rounded-lg bg-gray-200 animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">
            Recent Photos
          </h2>
          <Link href="/photos" className="text-sm text-blue-500 hover:text-blue-600 hover:underline transition-colors duration-200">
            See All
          </Link>
        </div>
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">Unable to load photos</p>
        </div>
      </div>
    );
  }

  if (photos.length === 0) {
    return (
      <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">
            Recent Photos
          </h2>
          <Link href="/photos" className="text-sm text-blue-500 hover:text-blue-600 hover:underline transition-colors duration-200">
            See All
          </Link>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="aspect-square rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer group">
              <div className="h-full w-full flex items-center justify-center text-gray-400 group-hover:text-gray-600 transition-colors duration-300">
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          ))}
        </div>
        <div className="text-center mt-3">
          <p className="text-xs text-gray-500">No recent photos</p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base font-semibold text-gray-900">
          Recent Photos
        </h2>
        <Link href="/photos" className="text-sm text-blue-500 hover:text-blue-600 hover:underline transition-colors duration-200">
          See All
        </Link>
      </div>
      <div className="grid grid-cols-3 gap-2">
        {photos.map((photo) => (
          <Link
            key={photo.id}
            href={`/user/${photo.user.username}/posts/${photo.postId}`}
            className="group relative aspect-square rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
          >
            <OptimizedImage
              src={photo.url}
              alt={`Photo by ${photo.user.name}`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              <div className="absolute bottom-0 left-0 right-0 p-2">
                <div className="flex items-center space-x-1">
                  <div className="h-4 w-4 rounded-full overflow-hidden">
                    {photo.user.image ? (
                      <OptimizedImage
                        src={photo.user.image}
                        alt={photo.user.name}
                        width={16}
                        height={16}
                        className="object-cover"
                      />
                    ) : (
                      <div className="h-full w-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                        <span className="text-xs font-bold text-white">
                          {photo.user.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <span className="text-xs text-white font-medium truncate">
                    {photo.user.name}
                  </span>
                </div>
              </div>
            </div>
          </Link>
        ))}
        
        {/* Fill remaining slots with placeholders if needed */}
        {photos.length < 6 && [...Array(6 - photos.length)].map((_, i) => (
          <div key={`placeholder-${i}`} className="aspect-square rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden shadow-sm">
            <div className="h-full w-full flex items-center justify-center text-gray-400">
              <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
