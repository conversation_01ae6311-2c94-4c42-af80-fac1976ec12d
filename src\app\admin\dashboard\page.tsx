"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";

import {
  UsersIcon,
  NewspaperIcon,
  UserGroupIcon,
  BuildingStorefrontIcon,
  CalendarIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  Cog6ToothIcon,
  FlagIcon,
  ChartBarIcon,
  StarIcon,
  CreditCardIcon,
  RectangleStackIcon,
  DocumentChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from "@heroicons/react/24/outline";

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  userGrowthRate: number;
  totalPosts: number;
  totalBlogs: number;
  publishedBlogs: number;
  postGrowthRate: number;
  totalGroups: number;
  totalFanPages: number;
  totalEvents: number;
  totalProducts: number;
  totalReports: number;
  pendingReports: number;
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowthRate: number;
  activeSubscriptions: number;
  subscriptionRevenue: number;
  totalReferrals: number;
  completedReferrals: number;
  monetizedBlogs: number;
  blogEarnings: number;

  // Content Engagement
  totalBlogViews: number;
  totalPostLikes: number;
  totalComments: number;

  // User Segmentation
  verifiedUsers: number;
  premiumUsers: number;

  // Financial Breakdown
  totalWithdrawals: number;
  pendingWithdrawals: number;

  // Weekly Growth
  newUsersLast7Days: number;
  newUsersPrevious7Days: number;
  weeklyUserGrowthRate: number;
  postsLast7Days: number;
  postsPrevious7Days: number;
  weeklyPostGrowthRate: number;

  // Engagement Rates
  userEngagementRate: number;
  blogPublishRate: number;
  referralCompletionRate: number;
  subscriptionConversionRate: number;

  systemHealth: {
    serverUptime: number;
    databaseLoad: number;
    storageUsage: number;
    serverStatus: string;
    databaseStatus: string;
    storageStatus: string;
  };
}

interface Activity {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  timeAgo: string;
  icon: string;
  color: string;
}

export default function AdminDashboardPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // For now, use mock data to ensure dashboard works
      const mockStats: DashboardStats = {
        totalUsers: 1250,
        activeUsers: 876,
        newUsersThisMonth: 145,
        userGrowthRate: 12.5,
        totalPosts: 5432,
        totalBlogs: 89,
        publishedBlogs: 76,
        postGrowthRate: 8.3,
        totalGroups: 128,
        totalFanPages: 156,
        totalEvents: 89,
        totalProducts: 342,
        totalReports: 15,
        pendingReports: 8,
        totalRevenue: 12580,
        monthlyRevenue: 2850,
        revenueGrowthRate: 15.2,
        activeSubscriptions: 324,
        subscriptionRevenue: 1950,
        totalReferrals: 456,
        completedReferrals: 298,
        monetizedBlogs: 45,
        blogEarnings: 1250,
        totalBlogViews: 45678,
        totalPostLikes: 12345,
        totalComments: 6789,
        verifiedUsers: 234,
        premiumUsers: 156,
        totalWithdrawals: 8500,
        pendingWithdrawals: 450,
        newUsersLast7Days: 89,
        newUsersPrevious7Days: 76,
        weeklyUserGrowthRate: 17.1,
        postsLast7Days: 234,
        postsPrevious7Days: 198,
        weeklyPostGrowthRate: 18.2,
        userEngagementRate: 70.1,
        blogPublishRate: 85.4,
        referralCompletionRate: 65.4,
        subscriptionConversionRate: 12.5,
        systemHealth: {
          serverUptime: 99.2,
          databaseLoad: 15,
          storageUsage: 68,
          serverStatus: 'healthy',
          databaseStatus: 'optimal',
          storageStatus: 'moderate'
        }
      };

      const mockActivities: Activity[] = [
        {
          id: '1',
          type: 'user_registration',
          title: 'New user registered',
          description: 'John Doe created an account',
          timestamp: new Date().toISOString(),
          timeAgo: '2 minutes ago',
          icon: 'user',
          color: 'blue'
        },
        {
          id: '2',
          type: 'post_created',
          title: 'New post created',
          description: 'Jane Smith published a new post',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          timeAgo: '5 minutes ago',
          icon: 'document',
          color: 'green'
        },
        {
          id: '3',
          type: 'blog_published',
          title: 'New blog published',
          description: 'Mike Johnson published "How to Code Better"',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          timeAgo: '10 minutes ago',
          icon: 'document-text',
          color: 'purple'
        },
        {
          id: '4',
          type: 'subscription_created',
          title: 'New subscription',
          description: 'Sarah Wilson subscribed to Premium plan',
          timestamp: new Date(Date.now() - 900000).toISOString(),
          timeAgo: '15 minutes ago',
          icon: 'credit-card',
          color: 'indigo'
        },
        {
          id: '5',
          type: 'report_submitted',
          title: 'New report submitted',
          description: 'Content reported for inappropriate material',
          timestamp: new Date(Date.now() - 1200000).toISOString(),
          timeAgo: '20 minutes ago',
          icon: 'flag',
          color: 'red'
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      setStats(mockStats);
      setActivities(mockActivities);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
  };

  const statCards = stats ? [
    {
      title: "Total Users",
      value: stats.totalUsers.toLocaleString(),
      icon: <UsersIcon className="h-6 w-6 text-white" />,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      change: { value: Math.abs(stats.userGrowthRate), isPositive: stats.userGrowthRate >= 0 },
    },
    {
      title: "Active Users",
      value: stats.activeUsers.toLocaleString(),
      icon: <UsersIcon className="h-6 w-6 text-white" />,
      color: "text-green-600",
      bgColor: "bg-green-100",
      change: { value: Math.round((stats.activeUsers / stats.totalUsers) * 100), isPositive: true },
    },
    {
      title: "Total Posts",
      value: stats.totalPosts.toLocaleString(),
      icon: <NewspaperIcon className="h-6 w-6 text-white" />,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      change: { value: Math.abs(stats.postGrowthRate), isPositive: stats.postGrowthRate >= 0 },
    },
    {
      title: "Published Blogs",
      value: stats.publishedBlogs.toLocaleString(),
      icon: <NewspaperIcon className="h-6 w-6 text-white" />,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      change: { value: Math.round((stats.publishedBlogs / Math.max(stats.totalBlogs, 1)) * 100), isPositive: true },
    },
    {
      title: "Total Groups",
      value: stats.totalGroups.toLocaleString(),
      icon: <UserGroupIcon className="h-6 w-6 text-white" />,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
      change: { value: 5, isPositive: true },
    },
    {
      title: "Total Fan Pages",
      value: stats.totalFanPages.toLocaleString(),
      icon: <StarIcon className="h-6 w-6 text-white" />,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      change: { value: 12, isPositive: true },
    },
    {
      title: "Total Products",
      value: stats.totalProducts.toLocaleString(),
      icon: <BuildingStorefrontIcon className="h-6 w-6 text-white" />,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
      change: { value: 15, isPositive: true },
    },
    {
      title: "Total Events",
      value: stats.totalEvents.toLocaleString(),
      icon: <CalendarIcon className="h-6 w-6 text-white" />,
      color: "text-pink-600",
      bgColor: "bg-pink-100",
      change: { value: 3, isPositive: true },
    },
    {
      title: "Total Revenue",
      value: `$${stats.totalRevenue.toLocaleString()}`,
      icon: <BanknotesIcon className="h-6 w-6 text-white" />,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100",
      change: { value: Math.abs(stats.revenueGrowthRate), isPositive: stats.revenueGrowthRate >= 0 },
    },
    {
      title: "Pending Reports",
      value: stats.pendingReports.toLocaleString(),
      icon: <ShieldCheckIcon className="h-6 w-6 text-white" />,
      color: "text-red-600",
      bgColor: "bg-red-100",
      change: { value: Math.round((stats.pendingReports / Math.max(stats.totalReports, 1)) * 100), isPositive: false },
    },
    {
      title: "Active Subscriptions",
      value: stats.activeSubscriptions.toLocaleString(),
      icon: <CreditCardIcon className="h-6 w-6 text-white" />,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      change: { value: 15, isPositive: true },
    },
    {
      title: "Monthly Revenue",
      value: `$${stats.monthlyRevenue.toLocaleString()}`,
      icon: <BanknotesIcon className="h-6 w-6 text-white" />,
      color: "text-green-600",
      bgColor: "bg-green-100",
      change: { value: Math.abs(stats.revenueGrowthRate), isPositive: stats.revenueGrowthRate >= 0 },
    },
  ] : [];

  const quickActions = [
    {
      label: "User Management",
      description: "Manage users, roles, and permissions",
      href: "/admin/users",
      icon: <UsersIcon className="h-6 w-6 text-blue-600" />,
      color: "bg-blue-50 text-blue-700 hover:bg-blue-100",
    },
    {
      label: "Earning World",
      description: "Manage earning system and monetization",
      href: "/admin/earning-world",
      icon: <BanknotesIcon className="h-6 w-6 text-emerald-600" />,
      color: "bg-emerald-50 text-emerald-700 hover:bg-emerald-100",
    },
    {
      label: "Content Moderation",
      description: "Review and moderate reported content",
      href: "/admin/reports",
      icon: <FlagIcon className="h-6 w-6 text-red-600" />,
      color: "bg-red-50 text-red-700 hover:bg-red-100",
    },
    {
      label: "Blog Management",
      description: "Manage blogs and monetization",
      href: "/admin/blogs",
      icon: <NewspaperIcon className="h-6 w-6 text-orange-600" />,
      color: "bg-orange-50 text-orange-700 hover:bg-orange-100",
    },
    {
      label: "Subscription Plans",
      description: "Manage subscription plans and pricing",
      href: "/admin/subscriptions/plans",
      icon: <RectangleStackIcon className="h-6 w-6 text-blue-600" />,
      color: "bg-blue-50 text-blue-700 hover:bg-blue-100",
    },
    {
      label: "Analytics",
      description: "View detailed site analytics",
      href: "/admin/analytics",
      icon: <ChartBarIcon className="h-6 w-6 text-purple-600" />,
      color: "bg-purple-50 text-purple-700 hover:bg-purple-100",
    },
    {
      label: "Site Settings",
      description: "Configure general site settings",
      href: "/admin/settings",
      icon: <Cog6ToothIcon className="h-6 w-6 text-gray-600" />,
      color: "bg-gray-50 text-gray-700 hover:bg-gray-100",
    },
    {
      label: "Wallet System",
      description: "Manage wallet transactions and balances",
      href: "/admin/wallet",
      icon: <BanknotesIcon className="h-6 w-6 text-green-600" />,
      color: "bg-green-50 text-green-700 hover:bg-green-100",
    },
  ];

  // Helper function to get activity icon
  const getActivityIcon = (iconType: string, color: string) => {
    const iconClass = `h-5 w-5 text-${color}-600`;

    switch (iconType) {
      case 'user':
        return <UsersIcon className={iconClass} />;
      case 'document':
        return <NewspaperIcon className={iconClass} />;
      case 'document-text':
        return <NewspaperIcon className={iconClass} />;
      case 'flag':
        return <FlagIcon className={iconClass} />;
      case 'credit-card':
        return <CreditCardIcon className={iconClass} />;
      case 'banknotes':
        return <BanknotesIcon className={iconClass} />;
      case 'user-group':
        return <UserGroupIcon className={iconClass} />;
      default:
        return <ChartBarIcon className={iconClass} />;
    }
  };

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'optimal':
        return 'bg-green-100 text-green-800';
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to get storage bar color
  const getStorageBarColor = (usage: number) => {
    if (usage < 50) return 'bg-green-500';
    if (usage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-2 text-sm text-gray-500">
            Welcome back! Here's an overview of your social media platform.
          </p>
        </div>
        <div className="hidden md:block">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className={`mr-2 h-5 w-5 text-gray-500 ${refreshing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {refreshing ? 'Refreshing...' : 'Refresh Data'}
            </button>
            <button className="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
              <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              Export Data
            </button>
            <button
              onClick={() => window.location.href = '/admin/reports'}
              className="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
            >
              <FlagIcon className="mr-2 h-5 w-5" />
              View Reports
            </button>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      ) : stats ? (
        <>
          {/* Stats Grid */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {statCards.map((card, index) => (
              <div
                key={index}
                className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200"
              >
                <div className="p-6">
                  <div className="flex items-center">
                    <div className={`flex-shrink-0 rounded-md p-3 ${card.bgColor}`}>
                      {card.icon}
                    </div>
                    <div className="ml-4 flex-1">
                      <p className="text-sm font-medium text-gray-500">{card.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                      <div className="flex items-center mt-1">
                        <span
                          className={`inline-flex items-center text-sm font-medium ${
                            card.change.isPositive ? "text-green-600" : "text-red-600"
                          }`}
                        >
                          {card.change.isPositive ? (
                            <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
                          ) : (
                            <ArrowTrendingDownIcon className="h-4 w-4 mr-1" />
                          )}
                          {card.change.value}%
                        </span>
                        <span className="text-xs text-gray-500 ml-1">vs last month</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Additional Stats - Earning World */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900">Earning World Overview</h2>
              <div className="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <UserGroupIcon className="h-8 w-8 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Total Referrals</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalReferrals.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{stats.completedReferrals} completed</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <NewspaperIcon className="h-8 w-8 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Monetized Blogs</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.monetizedBlogs.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">Blog monetization</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <BanknotesIcon className="h-8 w-8 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Blog Earnings</p>
                        <p className="text-2xl font-bold text-gray-900">${stats.blogEarnings.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">Total paid out</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <CreditCardIcon className="h-8 w-8 text-indigo-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Subscription Revenue</p>
                        <p className="text-2xl font-bold text-gray-900">${stats.subscriptionRevenue.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">This month</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Content Engagement Statistics */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900">Content Engagement</h2>
              <div className="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Total Blog Views</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalBlogViews.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">All time views</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Total Post Likes</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalPostLikes.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">User interactions</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Total Comments</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalComments.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">User discussions</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* User Segmentation */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900">User Segmentation</h2>
              <div className="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Verified Users</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.verifiedUsers.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{((stats.verifiedUsers / stats.totalUsers) * 100).toFixed(1)}% of total</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <StarIcon className="h-8 w-8 text-yellow-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Premium Users</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.premiumUsers.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{stats.subscriptionConversionRate.toFixed(1)}% conversion</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <UsersIcon className="h-8 w-8 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Active Users</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.activeUsers.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{stats.userEngagementRate.toFixed(1)}% engagement</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ChartBarIcon className="h-8 w-8 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Blog Publish Rate</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.blogPublishRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-500">Published vs drafts</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Recent Activity & Quick Actions Grid */}
          <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Recent Activity - Takes 2/3 of the space on large screens */}
            <div className="lg:col-span-2">
              <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
              <div className="mt-4 overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                <div className="p-6">
                  {activities.length > 0 ? (
                    <div className="flex flex-col space-y-4 max-h-96 overflow-y-auto">
                      {activities.slice(0, 10).map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <div className={`flex h-10 w-10 items-center justify-center rounded-full bg-${activity.color}-100`}>
                              {getActivityIcon(activity.icon, activity.color)}
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-gray-900">{activity.title}</p>
                            <p className="text-sm text-gray-500 truncate">{activity.description}</p>
                            <p className="text-xs text-gray-400">{activity.timeAgo}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-400 mb-2">
                        <ChartBarIcon className="h-12 w-12 mx-auto" />
                      </div>
                      <p className="text-gray-500">No recent activity</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Quick Actions - Takes 1/3 of the space on large screens */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Quick Actions</h2>
              <div className="mt-4 overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                <div className="p-6">
                  <div className="space-y-3">
                    {quickActions.map((action, index) => (
                      <button
                        key={index}
                        onClick={() => window.location.href = action.href}
                        className={`w-full text-left p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors duration-200 ${action.color}`}
                      >
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            {action.icon}
                          </div>
                          <div className="ml-4">
                            <h3 className="text-sm font-medium">{action.label}</h3>
                            <p className="text-xs text-gray-500 mt-1">{action.description}</p>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Weekly Growth Trends */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900">Weekly Growth Trends</h2>
              <div className="mt-4 grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Last 7 Days</span>
                        <span className="text-lg font-semibold text-gray-900">{stats.newUsersLast7Days.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Previous 7 Days</span>
                        <span className="text-lg font-semibold text-gray-900">{stats.newUsersPrevious7Days.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between pt-2 border-t">
                        <span className="text-sm font-medium text-gray-700">Growth Rate</span>
                        <span className={`text-lg font-bold ${stats.weeklyUserGrowthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {stats.weeklyUserGrowthRate >= 0 ? '+' : ''}{stats.weeklyUserGrowthRate.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Content Growth</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Posts Last 7 Days</span>
                        <span className="text-lg font-semibold text-gray-900">{stats.postsLast7Days.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Posts Previous 7 Days</span>
                        <span className="text-lg font-semibold text-gray-900">{stats.postsPrevious7Days.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between pt-2 border-t">
                        <span className="text-sm font-medium text-gray-700">Growth Rate</span>
                        <span className={`text-lg font-bold ${stats.weeklyPostGrowthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {stats.weeklyPostGrowthRate >= 0 ? '+' : ''}{stats.weeklyPostGrowthRate.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Financial Overview */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900">Financial Overview</h2>
              <div className="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <div className="overflow-hidden rounded-lg bg-gradient-to-r from-green-500 to-green-600 shadow-sm">
                  <div className="p-6 text-white">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <BanknotesIcon className="h-8 w-8" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium opacity-90">Total Revenue</p>
                        <p className="text-2xl font-bold">${stats.totalRevenue.toLocaleString()}</p>
                        <p className="text-xs opacity-75">All time earnings</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 shadow-sm">
                  <div className="p-6 text-white">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <CreditCardIcon className="h-8 w-8" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium opacity-90">Monthly Revenue</p>
                        <p className="text-2xl font-bold">${stats.monthlyRevenue.toLocaleString()}</p>
                        <p className="text-xs opacity-75">{stats.revenueGrowthRate >= 0 ? '+' : ''}{stats.revenueGrowthRate.toFixed(1)}% growth</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 shadow-sm">
                  <div className="p-6 text-white">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium opacity-90">Total Withdrawals</p>
                        <p className="text-2xl font-bold">${stats.totalWithdrawals.toLocaleString()}</p>
                        <p className="text-xs opacity-75">Paid to users</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-gradient-to-r from-orange-500 to-orange-600 shadow-sm">
                  <div className="p-6 text-white">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium opacity-90">Pending Withdrawals</p>
                        <p className="text-2xl font-bold">${stats.pendingWithdrawals.toLocaleString()}</p>
                        <p className="text-xs opacity-75">Awaiting processing</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* System Status */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900">System Status</h2>
              <div className="mt-4 overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                <div className="p-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Server Status</p>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(stats.systemHealth.serverStatus)}`}>
                          {stats.systemHealth.serverStatus.charAt(0).toUpperCase() + stats.systemHealth.serverStatus.slice(1)}
                        </span>
                      </div>
                      <div className="mt-2 h-2 w-full overflow-hidden rounded-full bg-gray-200">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: `${stats.systemHealth.serverUptime}%` }}></div>
                      </div>
                      <p className="mt-1 text-xs text-gray-500">{stats.systemHealth.serverUptime}% uptime in the last 30 days</p>
                    </div>

                    <div>
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Database Status</p>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(stats.systemHealth.databaseStatus)}`}>
                          {stats.systemHealth.databaseStatus.charAt(0).toUpperCase() + stats.systemHealth.databaseStatus.slice(1)}
                        </span>
                      </div>
                      <div className="mt-2 h-2 w-full overflow-hidden rounded-full bg-gray-200">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: `${100 - stats.systemHealth.databaseLoad}%` }}></div>
                      </div>
                      <p className="mt-1 text-xs text-gray-500">Current load: {stats.systemHealth.databaseLoad}%</p>
                    </div>

                    <div>
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Storage Usage</p>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(stats.systemHealth.storageStatus)}`}>
                          {stats.systemHealth.storageStatus.charAt(0).toUpperCase() + stats.systemHealth.storageStatus.slice(1)}
                        </span>
                      </div>
                      <div className="mt-2 h-2 w-full overflow-hidden rounded-full bg-gray-200">
                        <div className={`h-2 rounded-full ${getStorageBarColor(stats.systemHealth.storageUsage)}`} style={{ width: `${stats.systemHealth.storageUsage}%` }}></div>
                      </div>
                      <p className="mt-1 text-xs text-gray-500">{stats.systemHealth.storageUsage}% of allocated storage used</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Performance Metrics */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900">Performance Metrics</h2>
              <div className="mt-4 grid grid-cols-1 gap-6 lg:grid-cols-3">
                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Engagement Rates</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">User Engagement</span>
                        <div className="flex items-center">
                          <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${Math.min(stats.userEngagementRate, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-semibold">{stats.userEngagementRate.toFixed(1)}%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Blog Publish Rate</span>
                        <div className="flex items-center">
                          <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{ width: `${Math.min(stats.blogPublishRate, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-semibold">{stats.blogPublishRate.toFixed(1)}%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Referral Completion</span>
                        <div className="flex items-center">
                          <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-purple-600 h-2 rounded-full"
                              style={{ width: `${Math.min(stats.referralCompletionRate, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-semibold">{stats.referralCompletionRate.toFixed(1)}%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Subscription Conversion</span>
                        <div className="flex items-center">
                          <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-indigo-600 h-2 rounded-full"
                              style={{ width: `${Math.min(stats.subscriptionConversionRate, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-semibold">{stats.subscriptionConversionRate.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Content Statistics</h3>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600">{(stats.totalBlogViews / Math.max(stats.publishedBlogs, 1)).toFixed(0)}</div>
                        <div className="text-sm text-gray-500">Avg Views per Blog</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-600">{(stats.totalPostLikes / Math.max(stats.totalPosts, 1)).toFixed(1)}</div>
                        <div className="text-sm text-gray-500">Avg Likes per Post</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-600">{(stats.totalComments / Math.max(stats.totalPosts, 1)).toFixed(1)}</div>
                        <div className="text-sm text-gray-500">Avg Comments per Post</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg bg-white shadow-sm border border-gray-100">
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Metrics</h3>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-emerald-600">${(stats.totalRevenue / Math.max(stats.totalUsers, 1)).toFixed(2)}</div>
                        <div className="text-sm text-gray-500">Revenue per User</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600">${(stats.subscriptionRevenue / Math.max(stats.activeSubscriptions, 1)).toFixed(2)}</div>
                        <div className="text-sm text-gray-500">Avg Subscription Value</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-orange-600">{((stats.totalRevenue - stats.totalWithdrawals) / Math.max(stats.totalRevenue, 1) * 100).toFixed(1)}%</div>
                        <div className="text-sm text-gray-500">Revenue Retention</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg className="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load dashboard</h3>
            <p className="text-gray-600 mb-4">There was an error loading the dashboard data.</p>
            <button
              onClick={handleRefresh}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
