import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { desc, eq, and, or, like, count, inArray } from "drizzle-orm";

const groupSchema = z.object({
  name: z.string().min(2).max(255),
  description: z.string().max(1000).optional(),
  visibility: z.enum(["public", "private-visible", "private-hidden"]).default("public"),
  category: z.string().max(100).optional(),
  rules: z.string().max(1000).optional(),
  coverImage: z.string().url().optional().nullable(),
  postPermission: z.enum(["all-members", "admin-only"]).default("all-members"),
});

// Get groups with optional filtering
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const filter = url.searchParams.get("filter") || "all"; // all, joined, created, discover
    const search = url.searchParams.get("search") || "";
    const category = url.searchParams.get("category") || "";
    const visibility = url.searchParams.get("visibility") || "";

    // Build the query based on the filter
    let allGroups: any[] = [];

    if (filter === "joined") {
      // Get the IDs of groups the user has joined
      const joinedGroups = await db.select({ groupId: groupMembers.groupId })
        .from(groupMembers)
        .where(and(
          eq(groupMembers.userId, session.user.id),
          eq(groupMembers.role, "member")
        ));

      const joinedGroupIds = joinedGroups.map(g => g.groupId);

      if (joinedGroupIds.length > 0) {
        allGroups = await db.query.groups.findMany({
          where: inArray(groups.id, joinedGroupIds),
          orderBy: desc(groups.createdAt),
        });
      } else {
        allGroups = [];
      }
    } else if (filter === "created") {
      // Groups created by the user
      allGroups = await db.query.groups.findMany({
        where: eq(groups.creatorId, session.user.id),
        orderBy: desc(groups.createdAt),
      });
    } else {
      // All groups or discover filter
      // Apply search filter if provided
      const conditions = [];

      if (search) {
        conditions.push(
          or(
            like(groups.name, `%${search}%`),
            like(groups.description || "", `%${search}%`)
          )
        );
      }

      // Apply category filter if provided
      if (category) {
        conditions.push(eq(groups.category, category));
      }

      // Apply visibility filter if provided
      if (visibility) {
        conditions.push(eq(groups.visibility, visibility as "public" | "private-visible" | "private-hidden"));
      }

      // For discover filter, only show public groups
      if (filter === "discover") {
        conditions.push(eq(groups.visibility, "public"));
      }

      allGroups = await db.query.groups.findMany({
        where: conditions.length > 0 ? and(...conditions) : undefined,
        orderBy: desc(groups.createdAt),
      });
    }

    // For each group, get the member count and user's role
    const groupsWithDetails = await Promise.all(allGroups.map(async (group) => {
      // Get member count
      const memberCountResult = await db.select({ count: count() })
        .from(groupMembers)
        .where(and(
          eq(groupMembers.groupId, group.id),
          eq(groupMembers.role, "member")
        ));

      const memberCount = memberCountResult[0]?.count || 0;

      // Get user's role in the group
      const userMembership = await db.query.groupMembers.findFirst({
        where: and(
          eq(groupMembers.groupId, group.id),
          eq(groupMembers.userId, session.user.id)
        ),
      });

      return {
        ...group,
        memberCount,
        userRole: userMembership?.role || null,
      };
    }));

    return NextResponse.json(groupsWithDetails);
  } catch (error) {
    console.error("Error fetching groups:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new group
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = groupSchema.parse(body);

    // Create the group
    const groupId = uuidv4();
    await db.insert(groups).values({
      id: groupId,
      name: validatedData.name,
      description: validatedData.description || null,
      visibility: validatedData.visibility,
      category: validatedData.category || null,
      rules: validatedData.rules || null,
      coverImage: validatedData.coverImage || null,
      postPermission: validatedData.postPermission,
      creatorId: session.user.id,
    });

    // Add the creator as an admin
    const membershipId = uuidv4();
    await db.insert(groupMembers).values({
      id: membershipId,
      groupId,
      userId: session.user.id,
      role: "admin",
    });

    // Get the created group
    const createdGroup = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    return NextResponse.json(createdGroup);
  } catch (error) {
    console.error("Error creating group:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
