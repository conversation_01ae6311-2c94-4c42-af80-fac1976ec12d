/**
 * Memory Optimization Utilities
 * Helps reduce memory usage and improve performance
 */

import { lazy, ComponentType } from 'react';

/**
 * Lazy load components with error boundary
 */
export function lazyLoad<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: ComponentType
): T {
  const LazyComponent = lazy(importFunc);
  
  return LazyComponent as T;
}

/**
 * Debounce function to reduce memory usage from frequent calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function to limit execution frequency
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Memory-efficient array chunking
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * Clean up object references to prevent memory leaks
 */
export function cleanupObject(obj: Record<string, any>): void {
  Object.keys(obj).forEach(key => {
    delete obj[key];
  });
}

/**
 * Memory-efficient deep clone (for small objects only)
 */
export function lightClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as T;
  if (obj instanceof Array) return obj.map(item => lightClone(item)) as T;
  
  const cloned = {} as T;
  Object.keys(obj as any).forEach(key => {
    (cloned as any)[key] = lightClone((obj as any)[key]);
  });
  
  return cloned;
}

/**
 * Memory-efficient pagination helper
 */
export class MemoryEfficientPaginator<T> {
  private items: T[] = [];
  private pageSize: number;
  private currentPage: number = 0;

  constructor(pageSize: number = 20) {
    this.pageSize = pageSize;
  }

  setItems(items: T[]): void {
    this.items = items;
    this.currentPage = 0;
  }

  getCurrentPage(): T[] {
    const start = this.currentPage * this.pageSize;
    const end = start + this.pageSize;
    return this.items.slice(start, end);
  }

  nextPage(): T[] | null {
    if ((this.currentPage + 1) * this.pageSize >= this.items.length) {
      return null;
    }
    this.currentPage++;
    return this.getCurrentPage();
  }

  previousPage(): T[] | null {
    if (this.currentPage === 0) {
      return null;
    }
    this.currentPage--;
    return this.getCurrentPage();
  }

  hasNextPage(): boolean {
    return (this.currentPage + 1) * this.pageSize < this.items.length;
  }

  hasPreviousPage(): boolean {
    return this.currentPage > 0;
  }

  getTotalPages(): number {
    return Math.ceil(this.items.length / this.pageSize);
  }

  getCurrentPageNumber(): number {
    return this.currentPage + 1;
  }
}

/**
 * Memory usage monitor (development only)
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private measurements: Array<{ timestamp: number; usage: number }> = [];
  private maxMeasurements = 100;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  measure(): void {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      const usage = memory.usedJSHeapSize / memory.totalJSHeapSize;
      
      this.measurements.push({
        timestamp: Date.now(),
        usage: usage * 100
      });

      // Keep only recent measurements
      if (this.measurements.length > this.maxMeasurements) {
        this.measurements.shift();
      }

      // Warn if memory usage is high
      if (usage > 0.8) {
        console.warn(`High memory usage detected: ${(usage * 100).toFixed(1)}%`);
      }
    }
  }

  getAverageUsage(): number {
    if (this.measurements.length === 0) return 0;
    
    const sum = this.measurements.reduce((acc, measurement) => acc + measurement.usage, 0);
    return sum / this.measurements.length;
  }

  getLatestUsage(): number {
    if (this.measurements.length === 0) return 0;
    return this.measurements[this.measurements.length - 1].usage;
  }

  clearMeasurements(): void {
    this.measurements = [];
  }
}

/**
 * Cleanup utility for React components
 */
export function useMemoryCleanup(cleanupFn: () => void): void {
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', cleanupFn);
    
    return () => {
      window.removeEventListener('beforeunload', cleanupFn);
    };
  }
}

/**
 * Memory-efficient image loading
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve();
      // Clean up reference
      img.onload = null;
      img.onerror = null;
    };
    img.onerror = () => {
      reject(new Error(`Failed to load image: ${src}`));
      // Clean up reference
      img.onload = null;
      img.onerror = null;
    };
    img.src = src;
  });
}

/**
 * Memory-efficient event listener management
 */
export class EventListenerManager {
  private listeners: Array<{
    element: EventTarget;
    event: string;
    handler: EventListener;
    options?: boolean | AddEventListenerOptions;
  }> = [];

  add(
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ): void {
    element.addEventListener(event, handler, options);
    this.listeners.push({ element, event, handler, options });
  }

  remove(element: EventTarget, event: string, handler: EventListener): void {
    element.removeEventListener(event, handler);
    this.listeners = this.listeners.filter(
      listener => !(listener.element === element && listener.event === event && listener.handler === handler)
    );
  }

  removeAll(): void {
    this.listeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.listeners = [];
  }
}

// Export singleton instances
export const memoryMonitor = MemoryMonitor.getInstance();
export const eventListenerManager = new EventListenerManager();
