import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const gateways = await WalletService.getActivePaymentGateways();

    // Remove sensitive config data before sending to client
    const sanitizedGateways = gateways.map(gateway => ({
      id: gateway.id,
      name: gateway.name,
      displayName: gateway.displayName,
      type: gateway.type,
      depositFee: gateway.depositFee,
      depositFixedFee: gateway.depositFixedFee,
      minDeposit: gateway.minDeposit,
      maxDeposit: gateway.maxDeposit,
      currency: gateway.currency,
    }));

    return NextResponse.json({
      success: true,
      data: sanitizedGateways,
    });
  } catch (error: any) {
    console.error("Error fetching payment gateways:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch payment gateways" 
      },
      { status: 500 }
    );
  }
}
