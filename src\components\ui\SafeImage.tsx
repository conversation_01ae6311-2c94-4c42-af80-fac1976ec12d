"use client";

import { useState } from 'react';
import Image from 'next/image';

interface SafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  priority?: boolean;
  onError?: () => void;
}

// List of allowed domains that can use Next.js Image directly
const ALLOWED_DOMAINS = [
  'res.cloudinary.com',
  'images.unsplash.com',
  'i.pravatar.cc'
];

export function SafeImage({ 
  src, 
  alt, 
  width, 
  height, 
  fill, 
  className, 
  sizes,
  priority,
  onError 
}: SafeImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check if the image is from an allowed domain
  const isAllowedDomain = ALLOWED_DOMAINS.some(domain => 
    src.includes(domain)
  );

  const handleError = () => {
    setImageError(true);
    setIsLoading(false);
    onError?.();
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  // If image failed to load, show placeholder
  if (imageError) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={fill ? undefined : { width, height }}
      >
        <span className="text-gray-400 text-sm">Image unavailable</span>
      </div>
    );
  }

  // If it's from an allowed domain, use Next.js Image
  if (isAllowedDomain) {
    return (
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        fill={fill}
        className={`${className} ${isLoading ? 'animate-pulse' : ''}`}
        sizes={sizes}
        priority={priority}
        onError={handleError}
        onLoad={handleLoad}
      />
    );
  }

  // For external images, use regular img tag with proxy
  const proxyUrl = `/api/image-proxy?${new URLSearchParams({
    url: src,
    w: width?.toString() || '800',
    q: '75'
  }).toString()}`;

  return (
    <img
      src={proxyUrl}
      alt={alt}
      className={`${className} ${isLoading ? 'animate-pulse' : ''}`}
      style={fill ? { objectFit: 'cover', width: '100%', height: '100%' } : { width, height }}
      onError={handleError}
      onLoad={handleLoad}
      loading={priority ? 'eager' : 'lazy'}
    />
  );
}
