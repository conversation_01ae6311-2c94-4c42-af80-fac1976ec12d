import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Mock message threads data
const mockMessageThreads = [
  {
    id: 'thread-1',
    participantId: 'user-123',
    participantName: '<PERSON>',
    participantAvatar: '/avatars/alice.jpg',
    lastMessage: 'Hey! Did you see the latest update?',
    timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
    unreadCount: 3,
    isOnline: true
  },
  {
    id: 'thread-2',
    participantId: 'user-456',
    participantName: '<PERSON>',
    participantAvatar: '/avatars/bob.jpg',
    lastMessage: 'Thanks for sharing that article!',
    timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
    unreadCount: 1,
    isOnline: false
  },
  {
    id: 'thread-3',
    participantId: 'user-789',
    participantName: '<PERSON>',
    participantAvatar: '/avatars/carol.jpg',
    lastMessage: 'Let\'s catch up soon 😊',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    unreadCount: 0,
    isOnline: true
  },
  {
    id: 'thread-4',
    participantId: 'user-101',
    participantName: 'David Wilson',
    participantAvatar: '/avatars/david.jpg',
    lastMessage: 'Great photos from your trip!',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
    unreadCount: 2,
    isOnline: false
  },
  {
    id: 'thread-5',
    participantId: 'user-202',
    participantName: 'Eva Martinez',
    participantAvatar: '/avatars/eva.jpg',
    lastMessage: 'Can you help me with the project?',
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
    unreadCount: 0,
    isOnline: true
  }
];

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // In a real app, you would fetch message threads from database
    // filtered by user ID and other criteria
    
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const unreadOnly = searchParams.get('unread') === 'true';

    let threads = [...mockMessageThreads];

    if (unreadOnly) {
      threads = threads.filter(t => t.unreadCount > 0);
    }

    // Sort by timestamp (most recent first)
    threads.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    const paginatedThreads = threads.slice(offset, offset + limit);

    const totalUnreadMessages = threads.reduce((sum, thread) => sum + thread.unreadCount, 0);
    const unreadThreads = threads.filter(thread => thread.unreadCount > 0).length;

    return NextResponse.json({
      threads: paginatedThreads,
      total: threads.length,
      unreadMessages: totalUnreadMessages,
      unreadThreads: unreadThreads,
      hasMore: offset + limit < threads.length
    });

  } catch (error) {
    console.error('Error fetching message threads:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
