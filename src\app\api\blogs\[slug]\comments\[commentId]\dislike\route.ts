import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { likes, blogComments } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    slug: string;
    commentId: string;
  }>;
}

// POST /api/blogs/[slug]/comments/[commentId]/dislike - Dislike/Undislike a blog comment
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { commentId } = await params;

    // Check if comment exists
    const comment = await db.query.blogComments.findFirst({
      where: eq(blogComments.id, commentId),
      columns: {
        id: true,
        userId: true,
        blogId: true,
      },
    });

    if (!comment) {
      return NextResponse.json(
        { message: "Comment not found" },
        { status: 404 }
      );
    }

    // Check if user has already disliked this comment
    const existingDislike = await db.query.likes.findFirst({
      where: and(
        eq(likes.commentId, commentId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'dislike')
      ),
    });

    if (existingDislike) {
      // Undislike: remove the dislike
      await db
        .delete(likes)
        .where(
          and(
            eq(likes.commentId, commentId),
            eq(likes.userId, session.user.id),
            eq(likes.type, 'dislike')
          )
        );

      return NextResponse.json(
        { message: "Comment undisliked successfully", disliked: false },
        { status: 200 }
      );
    }

    // Check if user has already liked this comment
    const existingLike = await db.query.likes.findFirst({
      where: and(
        eq(likes.commentId, commentId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'like')
      ),
    });

    // If user has liked the comment, remove the like first
    if (existingLike) {
      await db
        .delete(likes)
        .where(
          and(
            eq(likes.commentId, commentId),
            eq(likes.userId, session.user.id),
            eq(likes.type, 'like')
          )
        );
    }

    // Add the dislike
    await db.insert(likes).values({
      id: uuidv4(),
      userId: session.user.id,
      postId: null,
      commentId,
      type: 'dislike',
    });

    return NextResponse.json(
      { message: "Comment disliked successfully", disliked: true },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error disliking blog comment:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
