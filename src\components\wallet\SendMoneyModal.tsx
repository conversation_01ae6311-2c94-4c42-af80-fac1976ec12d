"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, UserIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { PinInputField } from "@/components/ui/PinInput";
import { sendMoneySchema, type SendMoneyFormData } from "@/lib/wallet/validation";
import { toast } from "react-hot-toast";
import Image from "next/image";

interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  image?: string;
}

interface SendMoneyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function SendMoneyModal({ isOpen, onClose, onSuccess }: SendMoneyModalProps) {
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [step, setStep] = useState<'search' | 'amount' | 'confirm'>('search');
  const [feeCalculation, setFeeCalculation] = useState<any>(null);
  const [calculatingFee, setCalculatingFee] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<SendMoneyFormData>({
    resolver: zodResolver(sendMoneySchema),
  });

  const amount = watch("amount");
  const note = watch("note");
  const pin = watch("pin");

  // Search for users
  const searchUsers = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    try {
      const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}`);
      const data = await response.json();

      if (data.success) {
        setSearchResults(data.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const selectUser = (user: User) => {
    setSelectedUser(user);
    setValue("recipient", user.username);
    setStep('amount');
  };

  const proceedToConfirm = () => {
    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }
    if (!feeCalculation) {
      toast.error('Please wait for fee calculation to complete');
      return;
    }
    if (!feeCalculation.hasSufficientBalance) {
      toast.error(`Insufficient balance. You need $${feeCalculation.totalAmountNeeded}`);
      return;
    }
    setStep('confirm');
  };

  // Calculate fees using API
  const calculateFees = async (amount: string) => {
    if (!amount || parseFloat(amount) <= 0) {
      setFeeCalculation(null);
      return;
    }

    setCalculatingFee(true);
    try {
      const response = await fetch('/api/wallet/send/calculate-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      });

      const result = await response.json();

      if (result.success) {
        setFeeCalculation(result.data);
      } else {
        setFeeCalculation(null);
        if (result.message) {
          toast.error(result.message);
        }
      }
    } catch (error) {
      console.error('Error calculating fee:', error);
      setFeeCalculation(null);
    } finally {
      setCalculatingFee(false);
    }
  };

  // Calculate fees when amount changes
  useEffect(() => {
    if (amount && step === 'amount') {
      const timeoutId = setTimeout(() => {
        calculateFees(amount);
      }, 500); // Debounce API calls

      return () => clearTimeout(timeoutId);
    } else {
      setFeeCalculation(null);
    }
  }, [amount, step]);

  const onSubmit = async (data: SendMoneyFormData) => {
    if (!selectedUser) {
      toast.error('Please select a recipient');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/wallet/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipient: data.recipient,
          amount: data.amount,
          note: data.note,
          pin: data.pin,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Money sent successfully!');
        onSuccess();
        handleClose();
      } else {
        toast.error(result.message || 'Failed to send money');
      }
    } catch (error) {
      console.error('Error sending money:', error);
      toast.error('Failed to send money');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedUser(null);
    setSearchResults([]);
    setStep('search');
    onClose();
  };

  const goBack = () => {
    if (step === 'confirm') {
      setStep('amount');
    } else if (step === 'amount') {
      setStep('search');
      setSelectedUser(null);
    }
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full bg-white rounded-lg shadow-xl">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              {step !== 'search' && (
                <button
                  onClick={goBack}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <DialogTitle className="text-lg font-semibold text-gray-900">
                Send Money
              </DialogTitle>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6">
            {/* Step 1: Search Recipients */}
            {step === 'search' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Find Recipient
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      type="text"
                      placeholder="Search by username, email, or phone"
                      className="pl-10"
                      onChange={(e) => searchUsers(e.target.value)}
                    />
                  </div>
                </div>

                {/* Search Results */}
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {searchLoading && (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    </div>
                  )}

                  {searchResults.map((user) => (
                    <button
                      key={user.id}
                      onClick={() => selectUser(user)}
                      className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
                    >
                      {user.image ? (
                        <Image
                          src={user.image}
                          alt={user.name}
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <UserIcon className="h-5 w-5 text-gray-500" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">@{user.username}</div>
                      </div>
                    </button>
                  ))}

                  {searchResults.length === 0 && !searchLoading && (
                    <div className="text-center py-8 text-gray-500">
                      <UserIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                      <p>Search for users to send money</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Step 2: Enter Amount */}
            {step === 'amount' && selectedUser && (
              <div className="space-y-6">
                {/* Selected User */}
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  {selectedUser.image ? (
                    <Image
                      src={selectedUser.image}
                      alt={selectedUser.name}
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <UserIcon className="h-5 w-5 text-gray-500" />
                    </div>
                  )}
                  <div>
                    <div className="font-medium text-gray-900">{selectedUser.name}</div>
                    <div className="text-sm text-gray-500">@{selectedUser.username}</div>
                  </div>
                </div>

                {/* Amount Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Amount to Send
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <Input
                      {...register("amount")}
                      type="number"
                      step="0.01"
                      min="1"
                      max="5000"
                      placeholder="0.00"
                      className="pl-7"
                    />
                  </div>
                  {errors.amount && (
                    <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
                  )}
                </div>

                {/* Fee Breakdown */}
                {amount && (
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <h4 className="font-medium text-gray-900">Transaction Summary</h4>
                    {calculatingFee ? (
                      <div className="flex items-center justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span className="ml-2 text-sm text-gray-600">Calculating fees...</span>
                      </div>
                    ) : feeCalculation ? (
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Send Amount:</span>
                          <span className="font-medium">${feeCalculation.amount}</span>
                        </div>
                        {parseFloat(feeCalculation.percentageFee) > 0 && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Percentage Fee ({feeCalculation.feePercentage}%):</span>
                            <span className="font-medium">${feeCalculation.percentageFee}</span>
                          </div>
                        )}
                        {parseFloat(feeCalculation.feeFixed) > 0 && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Fixed Fee:</span>
                            <span className="font-medium">${feeCalculation.feeFixed}</span>
                          </div>
                        )}
                        {parseFloat(feeCalculation.totalFee) > 0 && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Total Fee:</span>
                            <span className="font-medium text-red-600">${feeCalculation.totalFee}</span>
                          </div>
                        )}
                        <div className="flex justify-between border-t border-gray-200 pt-1">
                          <span className="text-gray-900 font-medium">Total Deducted:</span>
                          <span className="font-bold text-lg text-red-600">${feeCalculation.totalAmountNeeded}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Recipient Receives:</span>
                          <span className="font-medium text-green-600">${feeCalculation.recipientReceives}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Your Balance:</span>
                          <span className={`font-medium ${
                            feeCalculation.hasSufficientBalance ? 'text-green-600' : 'text-red-600'
                          }`}>
                            ${feeCalculation.currentBalance}
                          </span>
                        </div>
                        {!feeCalculation.hasSufficientBalance && (
                          <div className="text-xs text-red-600 mt-2">
                            Insufficient balance. You need ${feeCalculation.totalAmountNeeded}
                          </div>
                        )}
                        <div className="text-xs text-gray-500 mt-2">
                          Min: ${feeCalculation.limits.min} - Max: ${feeCalculation.limits.max}
                        </div>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 text-center py-2">
                        Enter an amount to see fee calculation
                      </div>
                    )}
                  </div>
                )}

                {/* Note */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Note (Optional)
                  </label>
                  <textarea
                    {...register("note")}
                    rows={3}
                    placeholder="Add a note for this transfer..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.note && (
                    <p className="text-sm text-red-600 mt-1">{errors.note.message}</p>
                  )}
                </div>

                <Button
                  onClick={proceedToConfirm}
                  className="w-full"
                  disabled={!amount || parseFloat(amount) <= 0}
                >
                  Continue
                </Button>
              </div>
            )}

            {/* Step 3: Confirm & PIN */}
            {step === 'confirm' && selectedUser && (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Transaction Summary */}
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <h4 className="font-medium text-gray-900">Transaction Summary</h4>

                  <div className="flex items-center space-x-3">
                    {selectedUser.image ? (
                      <Image
                        src={selectedUser.image}
                        alt={selectedUser.name}
                        width={32}
                        height={32}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <UserIcon className="h-4 w-4 text-gray-500" />
                      </div>
                    )}
                    <div>
                      <div className="font-medium text-gray-900">{selectedUser.name}</div>
                      <div className="text-sm text-gray-500">@{selectedUser.username}</div>
                    </div>
                  </div>

                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount:</span>
                      <span className="font-medium">${parseFloat(amount || "0").toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Transfer Fee:</span>
                      <span className="font-medium">$0.00</span>
                    </div>
                    <div className="flex justify-between border-t border-gray-200 pt-1">
                      <span className="text-gray-900 font-medium">Total:</span>
                      <span className="font-bold">${parseFloat(amount || "0").toFixed(2)}</span>
                    </div>
                  </div>

                  {note && (
                    <div>
                      <span className="text-sm text-gray-600">Note:</span>
                      <p className="text-sm text-gray-900 mt-1">{note}</p>
                    </div>
                  )}
                </div>

                {/* PIN Input */}
                <PinInputField
                  label="Enter your wallet PIN"
                  description="Enter your 4-digit PIN to confirm this transaction"
                  value={pin || ""}
                  onChange={(value) => setValue("pin", value)}
                  error={errors.pin?.message}
                  required
                  length={4}
                  autoFocus
                />

                {/* Submit Button */}
                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={goBack}
                    className="flex-1"
                    disabled={loading}
                  >
                    Back
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={loading || !pin || pin.length !== 4}
                    isLoading={loading}
                  >
                    {loading ? 'Sending...' : 'Send Money'}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
