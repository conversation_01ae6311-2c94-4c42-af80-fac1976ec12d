"use client";

import { Card } from "@/components/ui/Card";
import { DocumentIcon } from "@heroicons/react/24/outline";

interface UserPagesProps {
  userId: string;
}

export function UserPages({ userId }: UserPagesProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">User Pages</h3>
          <p className="text-sm text-gray-600">
            Pages feature is currently not available
          </p>
        </div>
      </div>

      {/* Content */}
      <Card className="p-8">
        <div className="text-center">
          <DocumentIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            Pages Feature Unavailable
          </h3>
          <p className="mt-2 text-sm text-gray-500">
            The pages functionality has been disabled and is not currently available in this system.
          </p>
        </div>
      </Card>
    </div>
  );
}
