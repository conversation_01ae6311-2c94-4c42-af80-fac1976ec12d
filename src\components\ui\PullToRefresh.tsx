"use client";

import { ReactNode } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { usePullToRefresh } from '@/hooks/usePullToRefresh';
import { cn } from '@/lib/utils';

interface PullToRefreshProps {
  onRefresh: () => Promise<void> | void;
  children: ReactNode;
  threshold?: number;
  resistance?: number;
  enabled?: boolean;
  className?: string;
}

export function PullToRefresh({
  onRefresh,
  children,
  threshold = 80,
  resistance = 2.5,
  enabled = true,
  className
}: PullToRefreshProps) {
  const {
    pullToRefreshProps,
    refreshIndicatorProps,
    isRefreshing,
    isPulling,
    canRefresh
  } = usePullToRefresh({
    onRefresh,
    threshold,
    resistance,
    enabled
  });

  return (
    <div className={cn("relative", className)}>
      {/* Refresh Indicator */}
      <div 
        className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full z-50 flex flex-col items-center justify-center h-16 w-16"
        {...refreshIndicatorProps}
      >
        <div className={cn(
          "flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200",
          canRefresh ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-500",
          isRefreshing && "animate-spin"
        )}>
          <ArrowPathIcon className="h-5 w-5" />
        </div>
        <div className="mt-1 text-xs font-medium text-gray-600">
          {isRefreshing ? "Refreshing..." : canRefresh ? "Release to refresh" : "Pull to refresh"}
        </div>
      </div>

      {/* Content Container */}
      <div {...pullToRefreshProps}>
        {children}
      </div>
    </div>
  );
}
