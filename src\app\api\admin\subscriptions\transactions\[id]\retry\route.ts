import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptionTransactions, userSubscriptions } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { PaymentService } from "@/lib/payment/paymentService";
import { v4 as uuidv4 } from "uuid";

// POST - Retry failed transaction
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Get the original transaction
    const originalTransaction = await db.query.subscriptionTransactions.findFirst({
      where: eq(subscriptionTransactions.id, params.id),
    });

    if (!originalTransaction) {
      return NextResponse.json(
        { message: "Transaction not found" },
        { status: 404 }
      );
    }

    if (originalTransaction.status !== 'failed') {
      return NextResponse.json(
        {
          success: false,
          message: "Only failed transactions can be retried",
        },
        { status: 400 }
      );
    }

    // Get the subscription details
    const subscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, originalTransaction.subscriptionId),
    });

    if (!subscription) {
      return NextResponse.json(
        { message: "Associated subscription not found" },
        { status: 404 }
      );
    }

    // Create a new transaction for the retry
    const newTransactionId = uuidv4();
    await db.insert(subscriptionTransactions).values({
      id: newTransactionId,
      subscriptionId: originalTransaction.subscriptionId,
      userId: originalTransaction.userId,
      planId: originalTransaction.planId,
      type: originalTransaction.type,
      amount: originalTransaction.amount,
      currency: originalTransaction.currency,
      status: 'pending',
      paymentGateway: originalTransaction.paymentGateway,
      description: `Retry of transaction ${originalTransaction.id}`,
      metadata: JSON.stringify({
        originalTransactionId: originalTransaction.id,
        retryAttempt: true,
        adminInitiated: true,
        adminUserId: session.user.id,
      }),
    });

    // Attempt to process the payment again
    try {
      const paymentResult = await PaymentService.processPayment({
        amount: originalTransaction.amount,
        currency: originalTransaction.currency,
        gatewayId: originalTransaction.paymentGateway || 'stripe', // Default to stripe if not specified
        userId: originalTransaction.userId,
        metadata: {
          subscriptionId: originalTransaction.subscriptionId,
          transactionId: newTransactionId,
          planId: originalTransaction.planId,
          type: 'subscription_payment_retry',
          originalTransactionId: originalTransaction.id,
        },
      });

      if (paymentResult.success) {
        // Update the new transaction as completed
        await db
          .update(subscriptionTransactions)
          .set({
            status: 'completed',
            processedAt: new Date(),
            gatewayTransactionId: paymentResult.transactionId,
          })
          .where(eq(subscriptionTransactions.id, newTransactionId));

        // If this was a subscription payment, activate the subscription
        if (originalTransaction.type === 'payment') {
          await db
            .update(userSubscriptions)
            .set({
              status: 'active',
              lastPaymentDate: new Date(),
            })
            .where(eq(userSubscriptions.id, originalTransaction.subscriptionId));
        }

        return NextResponse.json({
          success: true,
          message: "Transaction retry successful",
          transactionId: newTransactionId,
        });
      } else {
        // Update the new transaction as failed
        await db
          .update(subscriptionTransactions)
          .set({
            status: 'failed',
            description: `Retry failed: ${paymentResult.error}`,
          })
          .where(eq(subscriptionTransactions.id, newTransactionId));

        return NextResponse.json(
          {
            success: false,
            message: `Transaction retry failed: ${paymentResult.error}`,
          },
          { status: 400 }
        );
      }
    } catch (paymentError) {
      // Update the new transaction as failed
      await db
        .update(subscriptionTransactions)
        .set({
          status: 'failed',
          description: `Retry failed: ${paymentError instanceof Error ? paymentError.message : 'Unknown error'}`,
        })
        .where(eq(subscriptionTransactions.id, newTransactionId));

      throw paymentError;
    }
  } catch (error) {
    console.error("Error retrying transaction:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to retry transaction",
      },
      { status: 500 }
    );
  }
}
