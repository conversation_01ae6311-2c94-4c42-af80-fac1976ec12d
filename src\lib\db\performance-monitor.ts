import { pool } from './mysql';

interface PoolStats {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  queuedRequests: number;
  connectionLimit: number;
  maxIdle: number;
}

interface DatabasePerformanceMetrics {
  poolStats: PoolStats;
  queryStats: {
    slowQueries: number;
    totalQueries: number;
    avgQueryTime: number;
  };
  connectionStats: {
    connectionsCreated: number;
    connectionsDestroyed: number;
    connectionErrors: number;
  };
  timestamp: Date;
}

class DatabasePerformanceMonitor {
  private static instance: DatabasePerformanceMonitor;
  private queryTimes: number[] = [];
  private slowQueryThreshold = 1000; // 1 second
  private maxQueryTimeHistory = 1000;

  private constructor() {}

  static getInstance(): DatabasePerformanceMonitor {
    if (!DatabasePerformanceMonitor.instance) {
      DatabasePerformanceMonitor.instance = new DatabasePerformanceMonitor();
    }
    return DatabasePerformanceMonitor.instance;
  }

  /**
   * Get current database pool statistics
   */
  async getPoolStats(): Promise<PoolStats> {
    try {
      // Get pool configuration
      const config = pool.config;
      
      // Note: mysql2 doesn't expose all internal stats directly
      // These are approximations based on available information
      return {
        totalConnections: config.connectionLimit || 50,
        activeConnections: 0, // Would need custom tracking
        idleConnections: 0, // Would need custom tracking
        queuedRequests: 0, // Would need custom tracking
        connectionLimit: config.connectionLimit || 50,
        maxIdle: config.maxIdle || 20,
      };
    } catch (error) {
      console.error('Error getting pool stats:', error);
      throw error;
    }
  }

  /**
   * Get MySQL server status and variables
   */
  async getServerStats(): Promise<any> {
    try {
      const [statusRows] = await pool.execute('SHOW STATUS');
      const [variableRows] = await pool.execute('SHOW VARIABLES');
      
      const status: Record<string, any> = {};
      const variables: Record<string, any> = {};
      
      (statusRows as any[]).forEach((row: any) => {
        status[row.Variable_name] = row.Value;
      });
      
      (variableRows as any[]).forEach((row: any) => {
        variables[row.Variable_name] = row.Value;
      });
      
      return {
        connections: {
          current: parseInt(status.Threads_connected || '0'),
          max: parseInt(variables.max_connections || '0'),
          total: parseInt(status.Connections || '0'),
        },
        queries: {
          total: parseInt(status.Queries || '0'),
          slow: parseInt(status.Slow_queries || '0'),
          qps: parseFloat(status.Queries_per_second_avg || '0'),
        },
        memory: {
          used: parseInt(status.Memory_used || '0'),
          max: parseInt(variables.max_heap_table_size || '0'),
        },
        uptime: parseInt(status.Uptime || '0'),
      };
    } catch (error) {
      console.error('Error getting server stats:', error);
      throw error;
    }
  }

  /**
   * Record query execution time
   */
  recordQueryTime(executionTime: number): void {
    this.queryTimes.push(executionTime);
    
    // Keep only recent query times
    if (this.queryTimes.length > this.maxQueryTimeHistory) {
      this.queryTimes.shift();
    }
  }

  /**
   * Get query performance statistics
   */
  getQueryStats() {
    if (this.queryTimes.length === 0) {
      return {
        slowQueries: 0,
        totalQueries: 0,
        avgQueryTime: 0,
        minQueryTime: 0,
        maxQueryTime: 0,
      };
    }

    const slowQueries = this.queryTimes.filter(time => time > this.slowQueryThreshold).length;
    const totalQueries = this.queryTimes.length;
    const avgQueryTime = this.queryTimes.reduce((sum, time) => sum + time, 0) / totalQueries;
    const minQueryTime = Math.min(...this.queryTimes);
    const maxQueryTime = Math.max(...this.queryTimes);

    return {
      slowQueries,
      totalQueries,
      avgQueryTime: Math.round(avgQueryTime * 100) / 100,
      minQueryTime: Math.round(minQueryTime * 100) / 100,
      maxQueryTime: Math.round(maxQueryTime * 100) / 100,
    };
  }

  /**
   * Get comprehensive performance metrics
   */
  async getPerformanceMetrics(): Promise<DatabasePerformanceMetrics> {
    try {
      const poolStats = await this.getPoolStats();
      const queryStats = this.getQueryStats();
      
      return {
        poolStats,
        queryStats,
        connectionStats: {
          connectionsCreated: 0, // Would need custom tracking
          connectionsDestroyed: 0, // Would need custom tracking
          connectionErrors: 0, // Would need custom tracking
        },
        timestamp: new Date(),
      };
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      throw error;
    }
  }

  /**
   * Check database health
   */
  async checkHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const serverStats = await this.getServerStats();
      const queryStats = this.getQueryStats();
      
      const issues: string[] = [];
      const recommendations: string[] = [];
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // Check connection usage
      const connectionUsage = (serverStats.connections.current / serverStats.connections.max) * 100;
      if (connectionUsage > 80) {
        issues.push(`High connection usage: ${connectionUsage.toFixed(1)}%`);
        recommendations.push('Consider increasing max_connections or optimizing connection pooling');
        status = 'warning';
      }

      // Check slow queries
      if (queryStats.slowQueries > queryStats.totalQueries * 0.1) {
        issues.push(`High slow query ratio: ${((queryStats.slowQueries / queryStats.totalQueries) * 100).toFixed(1)}%`);
        recommendations.push('Optimize slow queries and add proper indexes');
        status = 'warning';
      }

      // Check average query time
      if (queryStats.avgQueryTime > 500) {
        issues.push(`High average query time: ${queryStats.avgQueryTime}ms`);
        recommendations.push('Review and optimize database queries');
        if (status !== 'critical') status = 'warning';
      }

      if (queryStats.avgQueryTime > 2000) {
        status = 'critical';
      }

      return { status, issues, recommendations };
    } catch (error) {
      return {
        status: 'critical',
        issues: ['Database health check failed'],
        recommendations: ['Check database connectivity and configuration'],
      };
    }
  }

  /**
   * Set slow query threshold
   */
  setSlowQueryThreshold(threshold: number): void {
    this.slowQueryThreshold = threshold;
  }
}

// Export singleton instance
export const dbPerformanceMonitor = DatabasePerformanceMonitor.getInstance();

// Enhanced query execution with performance monitoring
export async function executeQueryWithMonitoring<T>(
  query: string, 
  params: any[] = []
): Promise<T> {
  const startTime = Date.now();
  
  try {
    const [rows] = await pool.execute(query, params);
    const executionTime = Date.now() - startTime;
    
    // Record performance metrics
    dbPerformanceMonitor.recordQueryTime(executionTime);
    
    // Log slow queries
    if (executionTime > 1000) {
      console.warn(`Slow query detected (${executionTime}ms):`, query.substring(0, 100));
    }
    
    return rows as T;
  } catch (error) {
    const executionTime = Date.now() - startTime;
    dbPerformanceMonitor.recordQueryTime(executionTime);
    
    console.error('Database query error:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
}
