import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPageMessages, fanPages, users } from "@/lib/db/schema";
import { eq, and, count, desc, sql } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId]/messages/stats - Get message statistics for a fan page
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;

    // Verify user owns this fan page
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, pageId),
        eq(fanPages.ownerId, session.user.id),
        eq(fanPages.isActive, true)
      ),
    });

    if (!page) {
      return NextResponse.json({ error: "Fan page not found or access denied" }, { status: 404 });
    }

    // Get total message count
    const totalMessagesResult = await db
      .select({ count: count() })
      .from(fanPageMessages)
      .where(eq(fanPageMessages.fanPageId, pageId));

    // Get unread user message count (not page replies)
    const unreadMessagesResult = await db
      .select({ count: count() })
      .from(fanPageMessages)
      .where(and(
        eq(fanPageMessages.fanPageId, pageId),
        eq(fanPageMessages.read, false),
        eq(fanPageMessages.isFromPage, false) // Only count unread user messages
      ));

    // Get unique senders count (conversation count)
    const uniqueSendersResult = await db
      .select({
        count: sql<number>`COUNT(DISTINCT ${fanPageMessages.senderId})`
      })
      .from(fanPageMessages)
      .where(eq(fanPageMessages.fanPageId, pageId));

    // Get recent messages for response time calculation
    const recentMessages = await db.query.fanPageMessages.findMany({
      where: eq(fanPageMessages.fanPageId, pageId),
      orderBy: [desc(fanPageMessages.createdAt)],
      limit: 100,
      with: {
        sender: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Calculate average response time (simplified - time between consecutive messages from different senders)
    let totalResponseTime = 0;
    let responseCount = 0;

    for (let i = 1; i < recentMessages.length; i++) {
      const currentMessage = recentMessages[i - 1];
      const previousMessage = recentMessages[i];

      // If current message is from page owner and previous is from a user
      if (currentMessage.senderId === session.user.id && previousMessage.senderId !== session.user.id) {
        const responseTime = new Date(currentMessage.createdAt).getTime() - new Date(previousMessage.createdAt).getTime();
        totalResponseTime += responseTime;
        responseCount++;
      }
    }

    const averageResponseTime = responseCount > 0 ? Math.round(totalResponseTime / responseCount / 1000 / 60) : 0; // in minutes

    // Get messages from last 24 hours
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const recentMessagesResult = await db
      .select({ count: count() })
      .from(fanPageMessages)
      .where(and(
        eq(fanPageMessages.fanPageId, pageId),
        sql`${fanPageMessages.createdAt} >= ${yesterday.toISOString()}`
      ));

    const stats = {
      totalMessages: totalMessagesResult[0]?.count || 0,
      unreadMessages: unreadMessagesResult[0]?.count || 0,
      totalConversations: uniqueSendersResult[0]?.count || 0,
      averageResponseTime: averageResponseTime, // in minutes
      messagesLast24h: recentMessagesResult[0]?.count || 0,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Error fetching fan page message stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
