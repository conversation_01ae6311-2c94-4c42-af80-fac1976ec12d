import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const balance = await WalletService.getOrCreateWallet(session.user.id);

    return NextResponse.json({
      success: true,
      data: balance,
    });
  } catch (error) {
    console.error("Error fetching wallet balance:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch wallet balance" 
      },
      { status: 500 }
    );
  }
}
