"use client";

import { useState } from "react";
import { OptimizedImage } from "./OptimizedImage";

interface UserBadgeProps {
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor?: string;
  customBadgeUrl?: string;
  planName?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// SVG Icons for ready-made badges
const BadgeIcons = {
  crown: (color: string) => (
    <svg viewBox="0 0 16 16" fill="none" className="w-full h-full">
      <path
        d="M8 2L9.5 5.5L13 4L11 7.5H5L3 4L6.5 5.5L8 2Z"
        fill={color}
        stroke="rgba(255,255,255,0.3)"
        strokeWidth="0.5"
      />
      <path
        d="M4.5 7.5H11.5L12 9H4L4.5 7.5Z"
        fill={color}
        stroke="rgba(255,255,255,0.3)"
        strokeWidth="0.5"
      />
      <circle cx="3" cy="4" r="0.8" fill={color} stroke="rgba(255,255,255,0.3)" strokeWidth="0.3" />
      <circle cx="13" cy="4" r="0.8" fill={color} stroke="rgba(255,255,255,0.3)" strokeWidth="0.3" />
      <circle cx="8" cy="2" r="0.8" fill={color} stroke="rgba(255,255,255,0.3)" strokeWidth="0.3" />
    </svg>
  ),
  
  star: (color: string) => (
    <svg viewBox="0 0 16 16" fill="none" className="w-full h-full">
      <path
        d="M8 1L9.5 5.5L14 5.5L10.5 8.5L12 13L8 10L4 13L5.5 8.5L2 5.5L6.5 5.5L8 1Z"
        fill={color}
        stroke="rgba(255,255,255,0.3)"
        strokeWidth="0.5"
      />
    </svg>
  ),
  
  diamond: (color: string) => (
    <svg viewBox="0 0 16 16" fill="none" className="w-full h-full">
      <path
        d="M8 1L12 5L8 15L4 5L8 1Z"
        fill={color}
        stroke="rgba(255,255,255,0.3)"
        strokeWidth="0.5"
      />
      <path
        d="M4 5L8 1L12 5L8 8L4 5Z"
        fill="rgba(255,255,255,0.2)"
      />
      <path
        d="M6 3L8 1L10 3L8 5L6 3Z"
        fill="rgba(255,255,255,0.3)"
      />
    </svg>
  ),
  
  vip: (color: string) => (
    <svg viewBox="0 0 16 16" fill="none" className="w-full h-full">
      <rect
        x="1"
        y="3"
        width="14"
        height="10"
        rx="2"
        fill={color}
        stroke="rgba(255,255,255,0.3)"
        strokeWidth="0.5"
      />
      <text
        x="8"
        y="9"
        textAnchor="middle"
        fontSize="4"
        fontWeight="bold"
        fill="white"
        fontFamily="Arial, sans-serif"
      >
        VIP
      </text>
    </svg>
  )
};

export function UserBadge({
  badgeType,
  badgeColor = '#3B82F6',
  customBadgeUrl,
  planName,
  size = 'sm',
  className = ''
}: UserBadgeProps) {
  const [imageError, setImageError] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  if (badgeType === 'none') {
    return null;
  }

  const sizeClasses = {
    sm: 'w-5 h-5',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const renderBadge = () => {
    if (badgeType === 'custom' && customBadgeUrl && !imageError) {
      return (
        <OptimizedImage
          src={customBadgeUrl}
          alt={planName || 'Badge'}
          width={20}
          height={20}
          className="w-full h-full object-cover"
          onError={() => setImageError(true)}
        />
      );
    }

    if (badgeType in BadgeIcons) {
      return BadgeIcons[badgeType as keyof typeof BadgeIcons](badgeColor);
    }

    return null;
  };

  return (
    <div
      className={`relative inline-flex items-center justify-center ${sizeClasses[size]} ${className}`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {renderBadge()}
      
      {/* Tooltip */}
      {showTooltip && planName && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50">
          <div className="bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
            {planName}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
          </div>
        </div>
      )}
    </div>
  );
}

// Badge Preview Component for Admin
export function BadgePreview({
  badgeType,
  badgeColor = '#3B82F6',
  customBadgeUrl,
  planName,
  size = 'md'
}: UserBadgeProps) {
  return (
    <div className="flex items-center space-x-2">
      <UserBadge
        badgeType={badgeType}
        badgeColor={badgeColor}
        customBadgeUrl={customBadgeUrl}
        planName={planName}
        size={size}
      />
      <span className="text-sm text-gray-600">
        {badgeType === 'custom' ? 'Custom Badge' : `${badgeType.charAt(0).toUpperCase() + badgeType.slice(1)} Badge`}
      </span>
    </div>
  );
}

// Badge Selector Component for Admin
export function BadgeSelector({
  selectedBadgeType,
  selectedBadgeColor,
  onBadgeTypeChange,
  onBadgeColorChange,
  className = ''
}: {
  selectedBadgeType: string;
  selectedBadgeColor: string;
  onBadgeTypeChange: (type: string) => void;
  onBadgeColorChange: (color: string) => void;
  className?: string;
}) {
  const badgeTypes = [
    { value: 'none', label: 'No Badge' },
    { value: 'crown', label: 'Crown' },
    { value: 'star', label: 'Star' },
    { value: 'diamond', label: 'Diamond' },
    { value: 'vip', label: 'VIP' },
    { value: 'custom', label: 'Custom Upload' }
  ];

  const predefinedColors = [
    '#3B82F6', // Blue
    '#EF4444', // Red
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#8B5CF6', // Purple
    '#EC4899', // Pink
    '#6B7280', // Gray
    '#000000'  // Black
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Badge Type Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Badge Type
        </label>
        <div className="grid grid-cols-3 gap-2">
          {badgeTypes.map((type) => (
            <button
              key={type.value}
              type="button"
              onClick={() => onBadgeTypeChange(type.value)}
              className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                selectedBadgeType === type.value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className="flex flex-col items-center space-y-1">
                {type.value !== 'none' && type.value !== 'custom' && (
                  <UserBadge
                    badgeType={type.value as any}
                    badgeColor={selectedBadgeColor}
                    size="md"
                  />
                )}
                <span>{type.label}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Color Selection (only for ready-made badges) */}
      {selectedBadgeType !== 'none' && selectedBadgeType !== 'custom' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Badge Color
          </label>
          <div className="flex flex-wrap gap-2">
            {predefinedColors.map((color) => (
              <button
                key={color}
                type="button"
                onClick={() => onBadgeColorChange(color)}
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  selectedBadgeColor === color
                    ? 'border-gray-400 scale-110'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                style={{ backgroundColor: color }}
                title={color}
              />
            ))}
            <input
              type="color"
              value={selectedBadgeColor}
              onChange={(e) => onBadgeColorChange(e.target.value)}
              className="w-8 h-8 rounded-full border-2 border-gray-200 cursor-pointer"
              title="Custom color"
            />
          </div>
        </div>
      )}

      {/* Preview */}
      {selectedBadgeType !== 'none' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preview
          </label>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">John Doe</span>
              <UserBadge
                badgeType={selectedBadgeType as any}
                badgeColor={selectedBadgeColor}
                planName="Premium Plan"
                size="sm"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
