import { PublicLayout } from "@/components/layout/PublicLayout";
import Link from "next/link";

export default function AboutPage() {
  return (
    <PublicLayout>
      <div className="bg-white py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:mx-0">
            <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">About HIFNF</h2>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              HIFNF is a social media platform designed to connect people and share experiences in a meaningful way.
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:mt-20 lg:max-w-none lg:grid-cols-3">
            <div className="flex flex-col bg-white p-6 shadow-lg rounded-lg">
              <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">Our Mission</h3>
              <p className="mt-4 flex-auto text-base leading-7 text-gray-600">
                To create a platform that fosters genuine connections and meaningful interactions between people from all walks of life.
              </p>
            </div>
            <div className="flex flex-col bg-white p-6 shadow-lg rounded-lg">
              <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">Our Vision</h3>
              <p className="mt-4 flex-auto text-base leading-7 text-gray-600">
                A world where technology brings people closer together rather than driving them apart, creating communities that transcend physical boundaries.
              </p>
            </div>
            <div className="flex flex-col bg-white p-6 shadow-lg rounded-lg">
              <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">Our Values</h3>
              <p className="mt-4 flex-auto text-base leading-7 text-gray-600">
                Privacy, authenticity, inclusivity, and creating a safe environment for all users to express themselves freely.
              </p>
            </div>
          </div>

          <div className="mt-16 border-t border-gray-200 pt-16">
            <h3 className="text-2xl font-bold tracking-tight text-gray-900">Our Story</h3>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              HIFNF was founded with a simple idea: to create a social platform that puts users first. In a digital landscape dominated by algorithms and advertising, we wanted to build something different—a space where genuine human connection takes center stage.
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Our team of passionate developers and designers work tirelessly to create an experience that feels natural, intuitive, and focused on what matters most: the people you care about and the content that enriches your life.
            </p>
          </div>

          <div className="mt-16 text-center">
            <Link
              href="/register"
              className="rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
            >
              Join Our Community
            </Link>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
}
