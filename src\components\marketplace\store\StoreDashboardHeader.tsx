"use client";

import { useState } from "react";
import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { formatDistanceToNow } from "date-fns";
import {
  PencilIcon,
  Cog6ToothIcon,
} from "@heroicons/react/24/outline";
import { CheckBadgeIcon } from "@heroicons/react/24/solid";

interface StoreDashboardHeaderProps {
  store: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    banner: string | null;
    location: string | null;
    isVerified: boolean | null;
    createdAt: Date;
  };
}

export function StoreDashboardHeader({ store }: StoreDashboardHeaderProps) {
  const [bannerError, setBannerError] = useState(false);
  const [logoError, setLogoError] = useState(false);

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
      <div className="relative h-48 bg-gray-100">
        {store.banner && !bannerError ? (
          <OptimizedImage
            src={store.banner}
            alt={`${store.name} banner`}
            fill
            className="object-cover"
            onError={() => setBannerError(true)}
            customPlaceholder="blur"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-blue-50">
            <span className="text-5xl font-bold text-blue-200">{store.name.charAt(0).toUpperCase()}</span>
          </div>
        )}

        <div className="absolute right-4 top-4 flex space-x-2">
          <Link href="/my-store/edit">
            <Button size="sm" variant="secondary">
              <PencilIcon className="mr-1 h-4 w-4" />
              Edit Store
            </Button>
          </Link>
          <Link href="/my-store/settings">
            <Button size="sm" variant="secondary">
              <Cog6ToothIcon className="mr-1 h-4 w-4" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      <div className="relative px-6 pb-6">
        {/* Horizontal layout with square logo and store name side by side */}
        <div className="flex items-center mt-6">
          {/* Square logo */}
          <div className="h-20 w-20 overflow-hidden rounded-md border-2 border-white bg-white shadow mr-4">
            {store.logo && !logoError ? (
              <OptimizedImage
                src={store.logo}
                alt={store.name}
                width={80}
                height={80}
                className="h-full w-full object-cover"
                onError={() => setLogoError(true)}
                customPlaceholder="blur"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-blue-100">
                <span className="text-3xl font-semibold text-blue-500">
                  {store.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>

          {/* Store name and verification badge */}
          <div>
            <div className="flex items-center">
              <h2 className="mr-2 text-2xl font-bold text-gray-900">{store.name}</h2>
              {store.isVerified === true && (
                <CheckBadgeIcon className="h-6 w-6 text-blue-500" title="Verified Store" />
              )}
            </div>

            <div className="mt-2 flex flex-wrap items-center gap-3 text-xs text-gray-500">
              {store.location && (
                <div className="flex items-center">
                  <span>📍 {store.location}</span>
                </div>
              )}
              <div className="flex items-center">
                <span>🗓️ Created {formatDistanceToNow(new Date(store.createdAt), { addSuffix: true })}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
