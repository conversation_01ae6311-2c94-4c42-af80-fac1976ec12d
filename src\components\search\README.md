# Search Feature Documentation

## Overview
The search feature provides comprehensive search functionality across all content types in the application including posts, users, groups, fan pages, events, and blogs.

## Components

### 1. SearchInput
A reusable search input component with auto-suggestions.

**Props:**
- `placeholder?: string` - Input placeholder text
- `className?: string` - Additional CSS classes
- `showSuggestions?: boolean` - Whether to show search suggestions
- `onSearch?: (query: string) => void` - Callback when search is performed
- `initialValue?: string` - Initial search value

### 2. SearchSuggestions
Displays real-time search suggestions and recent searches.

**Props:**
- `query: string` - Current search query
- `onSelect: () => void` - Callback when suggestion is selected
- `onSearch: (query: string) => void` - Callback to perform search

### 3. SearchFilters
Advanced filtering options for search results.

**Props:**
- `onFiltersChange: (filters: SearchFilters) => void` - Callback when filters change
- `initialFilters?: SearchFilters` - Initial filter values

### 4. Result Cards
Individual card components for different content types:
- `UserCard` - Display user search results
- `GroupCard` - Display group search results
- `FanPageCard` - Display fan page search results
- `EventCard` - Display event search results
- `BlogCard` - Display blog search results

## API Endpoints

### Global Search API
`GET /api/search`

**Query Parameters:**
- `q` - Search query (required, min 2 characters)
- `type` - Content type filter (all, posts, users, groups, pages, events, blogs)
- `category` - Category filter
- `location` - Location filter
- `sortBy` - Sort option (relevance, date, popularity, alphabetical)
- `sortOrder` - Sort order (asc, desc)
- `limit` - Number of results per type

**Response:**
```json
{
  "success": true,
  "data": {
    "posts": [...],
    "users": [...],
    "groups": [...],
    "fanPages": [...],
    "events": [...],
    "blogs": [...],
    "total": 42
  },
  "query": "search term",
  "type": "all"
}
```

## Features

### 1. Real-time Suggestions
- Shows suggestions as user types (debounced)
- Displays recent searches when input is focused
- Quick access to different content types

### 2. Advanced Filtering
- Filter by content type
- Category-based filtering
- Location-based filtering
- Date range filtering
- Multiple sorting options

### 3. Tabbed Results
- Organized results by content type
- Individual tabs for each content type
- Count indicators for each tab

### 4. Responsive Design
- Mobile-optimized search interface
- Touch-friendly interactions
- Adaptive layouts

## Usage Examples

### Basic Search Input
```tsx
import { SearchInput } from '@/components/search';

function MyComponent() {
  return (
    <SearchInput
      placeholder="Search anything..."
      onSearch={(query) => console.log('Searching for:', query)}
    />
  );
}
```

### Search with Filters
```tsx
import { SearchFilters, SearchFiltersType } from '@/components/search';

function MyComponent() {
  const [filters, setFilters] = useState<SearchFiltersType>({});

  return (
    <SearchFilters
      onFiltersChange={setFilters}
      initialFilters={filters}
    />
  );
}
```

### Custom Search Page
```tsx
import { UserCard, GroupCard } from '@/components/search';

function CustomSearchPage() {
  const [results, setResults] = useState([]);

  return (
    <div>
      {results.users.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
      {results.groups.map(group => (
        <GroupCard key={group.id} group={group} />
      ))}
    </div>
  );
}
```

## Performance Considerations

1. **Debounced Search**: API calls are debounced by 300ms to prevent excessive requests
2. **Pagination**: Results are limited to prevent large data transfers
3. **Caching**: Recent searches are cached in localStorage
4. **Lazy Loading**: Suggestions are loaded only when needed

## Accessibility

- Keyboard navigation support
- Screen reader friendly
- ARIA labels and roles
- Focus management
- High contrast support

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Progressive enhancement for older browsers
