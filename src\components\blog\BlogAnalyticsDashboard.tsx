"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/Card";
import { Spinner } from "@/components/ui/Spinner";
import {
  EyeIcon,
  ClockIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  GlobeAltIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";

interface ViewAnalytics {
  totalViews: number;
  uniqueViews: number;
  todayViews: number;
  weeklyViews: number;
  monthlyViews: number;
  averageViewDuration: number;
  averageScrollDepth: number;
  topReferrers: Array<{ referrer: string; count: number }>;
  deviceBreakdown: Array<{ device: string; count: number }>;
  countryBreakdown: Array<{ country: string; count: number }>;
  hourlyViews: Array<{ hour: number; count: number }>;
}

interface BlogAnalyticsDashboardProps {
  blogSlug: string;
  isAuthor?: boolean;
}

export function BlogAnalyticsDashboard({ 
  blogSlug, 
  isAuthor = false 
}: BlogAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<ViewAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/blogs/${blogSlug}/analytics`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch analytics');
        }

        const data = await response.json();
        setAnalytics(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (blogSlug) {
      fetchAnalytics();
    }
  }, [blogSlug]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading analytics: {error}</p>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-600">No analytics data available</p>
      </div>
    );
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case 'mobile':
        return <DevicePhoneMobileIcon className="h-5 w-5" />;
      case 'tablet':
        return <DeviceTabletIcon className="h-5 w-5" />;
      case 'desktop':
        return <ComputerDesktopIcon className="h-5 w-5" />;
      default:
        return <ComputerDesktopIcon className="h-5 w-5" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <EyeIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Views</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.totalViews.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ArrowTrendingUpIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Unique Views</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.uniqueViews.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg. Duration</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatDuration(analytics.averageViewDuration)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg. Scroll</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.averageScrollDepth}%
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Time-based Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Today</h3>
          <p className="text-3xl font-bold text-blue-600">
            {analytics.todayViews.toLocaleString()}
          </p>
          <p className="text-sm text-gray-600">views</p>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">This Week</h3>
          <p className="text-3xl font-bold text-green-600">
            {analytics.weeklyViews.toLocaleString()}
          </p>
          <p className="text-sm text-gray-600">views</p>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">This Month</h3>
          <p className="text-3xl font-bold text-purple-600">
            {analytics.monthlyViews.toLocaleString()}
          </p>
          <p className="text-sm text-gray-600">views</p>
        </Card>
      </div>

      {isAuthor && (
        <>
          {/* Device Breakdown */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Device Breakdown</h3>
            <div className="space-y-3">
              {analytics.deviceBreakdown.map((device, index) => {
                const percentage = analytics.totalViews > 0 
                  ? Math.round((device.count / analytics.totalViews) * 100)
                  : 0;
                
                return (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getDeviceIcon(device.device)}
                      <span className="font-medium capitalize">{device.device}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium w-12 text-right">
                        {percentage}%
                      </span>
                      <span className="text-sm text-gray-600 w-16 text-right">
                        ({device.count})
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>

          {/* Top Referrers */}
          {analytics.topReferrers.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Referrers</h3>
              <div className="space-y-3">
                {analytics.topReferrers.slice(0, 5).map((referrer, index) => {
                  const percentage = analytics.totalViews > 0 
                    ? Math.round((referrer.count / analytics.totalViews) * 100)
                    : 0;
                  
                  return (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                        <span className="font-medium truncate max-w-xs">
                          {referrer.referrer === 'Direct' ? 'Direct Traffic' : referrer.referrer}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {percentage}%
                        </span>
                        <span className="text-sm text-gray-600 w-16 text-right">
                          ({referrer.count})
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>
          )}

          {/* Country Breakdown */}
          {analytics.countryBreakdown.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Countries</h3>
              <div className="space-y-3">
                {analytics.countryBreakdown.slice(0, 5).map((country, index) => {
                  const percentage = analytics.totalViews > 0 
                    ? Math.round((country.count / analytics.totalViews) * 100)
                    : 0;
                  
                  return (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-4 bg-gray-300 rounded-sm flex items-center justify-center">
                          <span className="text-xs">🌍</span>
                        </div>
                        <span className="font-medium">{country.country}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-purple-600 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {percentage}%
                        </span>
                        <span className="text-sm text-gray-600 w-16 text-right">
                          ({country.count})
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>
          )}

          {/* Hourly Views Chart */}
          {analytics.hourlyViews.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Hourly Views</h3>
              <div className="flex items-end space-x-1 h-32">
                {Array.from({ length: 24 }, (_, hour) => {
                  const hourData = analytics.hourlyViews.find(h => h.hour === hour);
                  const views = hourData?.count || 0;
                  const maxViews = Math.max(...analytics.hourlyViews.map(h => h.count), 1);
                  const height = (views / maxViews) * 100;
                  
                  return (
                    <div key={hour} className="flex-1 flex flex-col items-center">
                      <div
                        className="w-full bg-blue-500 rounded-t"
                        style={{ height: `${height}%`, minHeight: views > 0 ? '4px' : '0px' }}
                        title={`${hour}:00 - ${views} views`}
                      />
                      <span className="text-xs text-gray-500 mt-1">
                        {hour}
                      </span>
                    </div>
                  );
                })}
              </div>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
