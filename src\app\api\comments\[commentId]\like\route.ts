import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { likes, comments, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { and, eq } from "drizzle-orm";

export async function POST(
  req: Request,
  context: { params: Promise<{ commentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the comment ID from the context params
    const params = await context.params;
    const { commentId } = params;

    // Check if the user has already liked the comment
    const existingLike = await db.query.likes.findFirst({
      where: and(
        eq(likes.commentId, commentId),
        eq(likes.userId, session.user.id),
        eq(likes.type, 'like')
      ),
    });

    if (existingLike) {
      // Unlike the comment
      await db
        .delete(likes)
        .where(
          and(
            eq(likes.commentId, commentId),
            eq(likes.userId, session.user.id),
            eq(likes.type, 'like')
          )
        );

      return NextResponse.json(
        { message: "Comment unliked successfully" },
        { status: 200 }
      );
    }

    // Get the comment to check the owner
    const comment = await db.query.comments.findFirst({
      where: eq(comments.id, commentId),
      columns: {
        id: true,
        userId: true,
        postId: true,
      },
    });

    if (!comment) {
      return NextResponse.json(
        { message: "Comment not found" },
        { status: 404 }
      );
    }

    // Like the comment
    await db.insert(likes).values({
      id: uuidv4(),
      userId: session.user.id,
      postId: null,
      commentId,
      type: 'like',
    });

    // Create notification for comment owner (if not liking own comment)
    if (comment.userId !== session.user.id) {
      await db.insert(notifications).values({
        id: uuidv4(),
        recipientId: comment.userId,
        type: "like",
        senderId: session.user.id,
        commentId,
        postId: comment.postId,
        read: false,
      });
    }

    return NextResponse.json(
      { message: "Comment liked successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error liking comment:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
