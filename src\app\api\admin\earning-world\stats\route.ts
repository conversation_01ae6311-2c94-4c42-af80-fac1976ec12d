import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  walletTransactions,
  wallets,
  users,
  referrals,
  blogEarnings,
  blogMonetization,
  blogs
} from "@/lib/db/schema";
import { eq, and, sql, desc, gte, lte, count, sum } from "drizzle-orm";

// Get earning world statistics for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get current date ranges
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Total earnings from earning wallet
    const totalEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      );

    // Total users count
    const totalUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.status, 'active'));

    // Active earners (users with earning wallet balance > 0)
    const activeEarnersResult = await db
      .select({ count: count() })
      .from(wallets)
      .where(
        and(
          eq(wallets.type, 'earning'),
          sql`${wallets.balance} > 0`
        )
      );

    // Total referrals
    const totalReferralsResult = await db
      .select({ count: count() })
      .from(referrals);

    // Completed referrals
    const completedReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.status, 'completed'));

    // Pending payouts (earning wallet balances)
    const pendingPayoutsResult = await db
      .select({ total: sum(wallets.balance) })
      .from(wallets)
      .where(eq(wallets.type, 'earning'));

    // Total payouts (completed withdrawals from earning wallet)
    const totalPayoutsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'withdrawal'),
          eq(walletTransactions.walletType, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      );

    // Monthly earnings (current month)
    const monthlyEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, startOfMonth)
        )
      );

    // Last month earnings for growth calculation
    const lastMonthEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, startOfLastMonth),
          lte(walletTransactions.createdAt, endOfLastMonth)
        )
      );

    // Top earners
    const topEarnersResult = await db
      .select({
        userId: wallets.userId,
        totalBalance: wallets.balance,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
        }
      })
      .from(wallets)
      .innerJoin(users, eq(wallets.userId, users.id))
      .where(eq(wallets.type, 'earning'))
      .orderBy(desc(wallets.balance))
      .limit(5);

    // Recent earning activity
    const recentActivityResult = await db
      .select({
        id: walletTransactions.id,
        type: walletTransactions.type,
        amount: walletTransactions.amount,
        createdAt: walletTransactions.createdAt,
        status: walletTransactions.status,
        reference: walletTransactions.reference,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
        }
      })
      .from(walletTransactions)
      .innerJoin(users, eq(walletTransactions.userId, users.id))
      .where(
        and(
          eq(walletTransactions.walletType, 'earning'),
          eq(walletTransactions.status, 'completed')
        )
      )
      .orderBy(desc(walletTransactions.createdAt))
      .limit(10);

    // Earning breakdown by source
    // Referral earnings
    const referralEarningsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'earning'),
          eq(walletTransactions.status, 'completed'),
          sql`${walletTransactions.reference} LIKE '%referral%'`
        )
      );

    // Blog monetization earnings
    const blogEarningsResult = await db
      .select({ total: sum(blogEarnings.earningAmount) })
      .from(blogEarnings)
      .where(eq(blogEarnings.status, 'paid'));

    // Calculate values
    const totalEarnings = parseFloat(totalEarningsResult[0]?.total || '0');
    const totalUsers = totalUsersResult[0]?.count || 0;
    const activeEarners = activeEarnersResult[0]?.count || 0;
    const totalReferrals = totalReferralsResult[0]?.count || 0;
    const completedReferrals = completedReferralsResult[0]?.count || 0;
    const pendingPayouts = parseFloat(pendingPayoutsResult[0]?.total || '0');
    const totalPayouts = parseFloat(totalPayoutsResult[0]?.total || '0');
    const monthlyEarnings = parseFloat(monthlyEarningsResult[0]?.total || '0');
    const lastMonthEarnings = parseFloat(lastMonthEarningsResult[0]?.total || '0');
    
    // Calculate monthly growth
    const monthlyGrowth = lastMonthEarnings > 0 
      ? ((monthlyEarnings - lastMonthEarnings) / lastMonthEarnings) * 100 
      : 0;

    const referralEarnings = parseFloat(referralEarningsResult[0]?.total || '0');
    const blogMonetizationEarnings = parseFloat(blogEarningsResult[0]?.total || '0');
    const otherEarnings = totalEarnings - referralEarnings - blogMonetizationEarnings;

    // Blog monetization detailed stats
    const totalBlogsResult = await db
      .select({ count: count() })
      .from(blogs);

    const monetizedBlogsResult = await db
      .select({ count: count() })
      .from(blogMonetization)
      .where(eq(blogMonetization.isApproved, true));

    const pendingApprovalResult = await db
      .select({ count: count() })
      .from(blogMonetization)
      .where(
        and(
          eq(blogMonetization.isApproved, false),
          sql`${blogMonetization.rejectedAt} IS NULL`
        )
      );

    const totalReadsResult = await db
      .select({ total: sum(blogMonetization.totalReads) })
      .from(blogMonetization)
      .where(eq(blogMonetization.isApproved, true));

    const blogMonetizationStats = {
      totalBlogs: totalBlogsResult[0]?.count || 0,
      monetizedBlogs: monetizedBlogsResult[0]?.count || 0,
      pendingApproval: pendingApprovalResult[0]?.count || 0,
      totalReads: totalReadsResult[0]?.total || 0,
      totalEarnings: blogMonetizationEarnings,
    };

    const stats = {
      totalEarnings,
      totalUsers,
      activeEarners,
      totalReferrals,
      completedReferrals,
      pendingPayouts,
      totalPayouts,
      monthlyEarnings,
      monthlyGrowth,
      blogMonetization: blogMonetizationStats,
      topEarners: topEarnersResult.map(earner => ({
        id: earner.user.id,
        name: earner.user.name || 'Unknown User',
        username: earner.user.username || '',
        totalEarnings: parseFloat(earner.totalBalance),
        earningType: 'Mixed'
      })),
      recentActivity: recentActivityResult.map(activity => ({
        id: activity.id,
        type: activity.type,
        user: activity.user.name || 'Unknown User',
        amount: parseFloat(activity.amount),
        date: activity.createdAt.toISOString().split('T')[0],
        status: activity.status
      })),
      earningBreakdown: {
        referrals: referralEarnings,
        blogMonetization: blogMonetizationEarnings,
        other: Math.max(0, otherEarnings)
      }
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Error fetching earning world stats:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch earning world statistics"
      },
      { status: 500 }
    );
  }
}
