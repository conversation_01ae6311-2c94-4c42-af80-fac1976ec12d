import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { walletSettings } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const walletSettingsSchema = z.object({
  // Feature toggles
  deposit_enabled: z.boolean(),
  withdraw_enabled: z.boolean(),
  send_enabled: z.boolean(),
  cashout_enabled: z.boolean(),
  internal_transfer_enabled: z.boolean(),

  // Fee settings
  // Deposit fees
  deposit_fee_percentage: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0 && num <= 10;
  }, "Deposit fee percentage must be between 0 and 10"),

  deposit_fee_fixed: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, "Deposit fixed fee must be a positive number"),

  deposit_min_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Deposit minimum amount must be positive"),

  deposit_max_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Deposit maximum amount must be positive"),

  // Withdraw fees
  withdraw_fee_percentage: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0 && num <= 10;
  }, "Withdraw fee percentage must be between 0 and 10"),

  withdraw_fee_fixed: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, "Withdraw fixed fee must be a positive number"),

  withdraw_min_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Withdraw minimum amount must be positive"),

  withdraw_max_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Withdraw maximum amount must be positive"),

  // Send money fees
  send_fee_percentage: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0 && num <= 10;
  }, "Send money fee percentage must be between 0 and 10"),

  send_fee_fixed: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, "Send money fixed fee must be a positive number"),

  send_min_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Send money minimum amount must be positive"),

  send_max_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Send money maximum amount must be positive"),

  // Transfer fees (internal transfers)
  transfer_fee_percentage: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0 && num <= 10;
  }, "Transfer fee percentage must be between 0 and 10"),

  transfer_fee_fixed: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, "Transfer fixed fee must be a positive number"),

  transfer_min_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Transfer minimum amount must be positive"),

  transfer_max_amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Transfer maximum amount must be positive"),

  // Daily limits
  daily_deposit_limit: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Daily deposit limit must be positive"),

  daily_send_limit: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Daily send limit must be positive"),

  daily_cashout_limit: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Daily cashout limit must be positive"),

  daily_withdraw_limit: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Daily withdraw limit must be positive"),

  // Processing settings
  auto_approve_withdrawals: z.boolean(),
  withdrawal_processing_time: z.string().refine((val) => {
    const num = parseInt(val);
    return !isNaN(num) && num > 0 && num <= 168;
  }, "Processing time must be between 1 and 168 hours"),

  require_pin_for_withdrawals: z.boolean(),
  require_pin_for_transfers: z.boolean(),
});

// Default wallet settings
const defaultSettings = {
  // Feature toggles
  deposit_enabled: true,
  withdraw_enabled: true,
  send_enabled: true,
  cashout_enabled: true,
  internal_transfer_enabled: true,

  // Fee settings
  // Deposit fees
  deposit_fee_percentage: "0.00",
  deposit_fee_fixed: "0.00",
  deposit_min_amount: "1.00",
  deposit_max_amount: "10000.00",

  // Withdraw fees
  withdraw_fee_percentage: "2.00",
  withdraw_fee_fixed: "0.00",
  withdraw_min_amount: "10.00",
  withdraw_max_amount: "10000.00",

  // Send money fees
  send_fee_percentage: "1.00",
  send_fee_fixed: "0.00",
  send_min_amount: "1.00",
  send_max_amount: "5000.00",

  // Transfer fees (internal transfers)
  transfer_fee_percentage: "0.50",
  transfer_fee_fixed: "0.00",
  transfer_min_amount: "1.00",
  transfer_max_amount: "5000.00",

  // Daily limits
  daily_deposit_limit: "10000.00",
  daily_send_limit: "5000.00",
  daily_cashout_limit: "3000.00",
  daily_withdraw_limit: "5000.00",

  // Processing settings
  auto_approve_withdrawals: false,
  withdrawal_processing_time: "24",
  require_pin_for_withdrawals: true,
  require_pin_for_transfers: true,
};

// Get wallet settings
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all wallet settings
    const settings = await db.query.walletSettings.findMany();

    // Convert to object format
    const settingsObject = settings.reduce((acc, setting) => {
      if (setting.type === 'boolean') {
        acc[setting.key] = setting.value === 'true';
      } else {
        acc[setting.key] = setting.value;
      }
      return acc;
    }, {} as any);

    // Merge with defaults for any missing settings
    const finalSettings = { ...defaultSettings, ...settingsObject };

    return NextResponse.json({
      success: true,
      data: finalSettings,
    });
  } catch (error: any) {
    console.error("Error fetching wallet settings:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch wallet settings"
      },
      { status: 500 }
    );
  }
}

// Update wallet settings
export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = walletSettingsSchema.parse(body);

    // Update each setting
    for (const [key, value] of Object.entries(validatedData)) {
      const stringValue = typeof value === 'boolean' ? value.toString() : value.toString();
      const type = typeof value === 'boolean' ? 'boolean' : 'string';

      // Check if setting exists
      const existingSetting = await db.query.walletSettings.findFirst({
        where: eq(walletSettings.key, key),
      });

      if (existingSetting) {
        // Update existing setting
        await db.update(walletSettings)
          .set({
            value: stringValue,
            type: type as any,
            updatedAt: new Date(),
          })
          .where(eq(walletSettings.key, key));
      } else {
        // Create new setting
        await db.insert(walletSettings).values({
          id: uuidv4(),
          key,
          value: stringValue,
          type: type as any,
          description: getSettingDescription(key),
          category: getSettingCategory(key),
          isSystem: false,
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: "Wallet settings updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating wallet settings:", error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid input data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to update wallet settings"
      },
      { status: 500 }
    );
  }
}

function getSettingDescription(key: string): string {
  const descriptions: Record<string, string> = {
    deposit_enabled: "Enable or disable deposit functionality",
    withdraw_enabled: "Enable or disable withdrawal functionality",
    send_enabled: "Enable or disable money transfer functionality",
    cashout_enabled: "Enable or disable cashout through agents",
    internal_transfer_enabled: "Enable or disable internal wallet transfers",

    deposit_fee_percentage: "Percentage fee charged on deposits",
    deposit_fee_fixed: "Fixed fee charged on deposits",
    deposit_min_amount: "Minimum deposit amount",
    deposit_max_amount: "Maximum deposit amount",

    withdraw_fee_percentage: "Percentage fee charged on withdrawals",
    withdraw_fee_fixed: "Fixed fee charged on withdrawals",
    withdraw_min_amount: "Minimum withdrawal amount",
    withdraw_max_amount: "Maximum withdrawal amount",

    send_fee_percentage: "Percentage fee charged on send money transactions",
    send_fee_fixed: "Fixed fee charged on send money transactions",
    send_min_amount: "Minimum send money amount",
    send_max_amount: "Maximum send money amount",

    transfer_fee_percentage: "Percentage fee charged on internal transfers",
    transfer_fee_fixed: "Fixed fee charged on internal transfers",
    transfer_min_amount: "Minimum internal transfer amount",
    transfer_max_amount: "Maximum internal transfer amount",

    daily_deposit_limit: "Daily deposit limit per user",
    daily_send_limit: "Daily send limit per user",
    daily_cashout_limit: "Daily cashout limit per user",
    daily_withdraw_limit: "Daily withdrawal limit per user",

    auto_approve_withdrawals: "Automatically approve withdrawal requests",
    withdrawal_processing_time: "Expected withdrawal processing time in hours",
    require_pin_for_withdrawals: "Require PIN verification for withdrawals",
    require_pin_for_transfers: "Require PIN verification for transfers",
  };

  return descriptions[key] || "";
}

function getSettingCategory(key: string): string {
  if (key.includes('fee') || key.includes('min_amount') || key.includes('max_amount')) {
    return 'fees';
  }
  if (key.includes('enabled')) {
    return 'features';
  }
  if (key.includes('limit')) {
    return 'limits';
  }
  if (key.includes('approve') || key.includes('processing') || key.includes('require')) {
    return 'processing';
  }
  return 'general';
}
