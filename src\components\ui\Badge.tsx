"use client";

import { cn } from "@/lib/utils";
import { HTMLAttributes, forwardRef } from "react";

export interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {
  variant?: "primary" | "success" | "warning" | "danger" | "default" | "outline" | "secondary" | "destructive" | "blue" | "error" | "info";
  size?: "sm" | "md" | "lg";
}

const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      className,
      children,
      variant = "default",
      size = "sm",
      ...props
    },
    ref
  ) => {
    return (
      <span
        className={cn(
          "inline-flex items-center justify-center rounded-full font-medium",
          {
            "bg-blue-100 text-blue-800": variant === "primary" || variant === "blue",
            "bg-green-100 text-green-800": variant === "success",
            "bg-yellow-100 text-yellow-800": variant === "warning",
            "bg-red-100 text-red-800": variant === "danger" || variant === "destructive" || variant === "error",
            "bg-gray-100 text-gray-800": variant === "default" || variant === "secondary",
            "bg-blue-50 text-blue-600": variant === "info",
            "border border-gray-300 bg-transparent text-gray-700": variant === "outline",
            "px-2 py-0.5 text-xs": size === "sm",
            "px-2.5 py-0.5 text-sm": size === "md",
            "px-3 py-1 text-base": size === "lg",
          },
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = "Badge";

export { Badge };
