"use client";

import { useState, useEffect } from "react";
import {
  DocumentTextIcon,
  InformationCircleIcon,
  PhotoIcon,
  CalendarIcon,
  ShoppingBagIcon,
  ChartBarIcon
} from "@heroicons/react/24/outline";

interface FanPageTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isOwner: boolean;
  userRole: string | null;
  postCount: number;
}

const tabs = [
  {
    id: "posts",
    name: "Posts",
    icon: DocumentTextIcon,
    public: true,
  },
  {
    id: "about",
    name: "About",
    icon: InformationCircleIcon,
    public: true,
  },
  {
    id: "photos",
    name: "Photos",
    icon: PhotoIcon,
    public: true,
  },
  {
    id: "events",
    name: "Events",
    icon: CalendarIcon,
    public: true,
  },
  {
    id: "products",
    name: "Products",
    icon: ShoppingBagIcon,
    public: true,
  },
  {
    id: "insights",
    name: "Insights",
    icon: ChartBarIcon,
    public: false, // Only for page admins
  },
];

export function FanPageTabs({
  activeTab,
  onTabChange,
  isOwner,
  userRole,
  postCount
}: FanPageTabsProps) {
  const canViewInsights = isOwner || userRole === 'admin';

  const visibleTabs = tabs.filter(tab => {
    if (tab.id === "insights") {
      return canViewInsights;
    }
    return tab.public;
  });

  // Add CSS to hide scrollbar
  const hideScrollbarStyle = {
    scrollbarWidth: 'none' as const,
    msOverflowStyle: 'none' as const,
  };

  useEffect(() => {
    // Add CSS to hide webkit scrollbar
    const style = document.createElement('style');
    style.textContent = `
      .hide-webkit-scrollbar::-webkit-scrollbar {
        display: none;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8">
        <nav
          className="-mb-px flex space-x-8 overflow-x-auto hide-webkit-scrollbar"
          style={hideScrollbarStyle}
        >
          {visibleTabs.map((tab) => {
            const isActive = activeTab === tab.id;
            const Icon = tab.icon;

            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                data-tab={tab.id}
                className={`
                  flex items-center space-x-3 py-4 px-2 border-b-3 font-semibold text-sm whitespace-nowrap transition-all duration-300 relative group
                  ${isActive
                    ? 'border-blue-500 text-blue-600 bg-blue-50/50'
                    : 'border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-300 hover:bg-blue-50/30'
                  }
                `}
              >
                <div className={`p-1.5 rounded-lg transition-all duration-300 ${
                  isActive
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-500 group-hover:bg-blue-100 group-hover:text-blue-600'
                }`}>
                  <Icon className="h-4 w-4" />
                </div>
                <span className="font-medium">{tab.name}</span>
                {tab.id === "posts" && (
                  <span className={`
                    inline-flex items-center px-2.5 py-1 rounded-full text-xs font-bold transition-all duration-300
                    ${isActive
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-md'
                      : 'bg-gray-200 text-gray-700 group-hover:bg-blue-200 group-hover:text-blue-800'
                    }
                  `}>
                    {postCount}
                  </span>
                )}

                {/* Active indicator */}
                {isActive && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 rounded-full -mb-1" />
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
