import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  fanPages,
  fanPageFollowers,
  fanPagePosts,
  fanPageMessages,
  fanPagePostLikes,
  fanPagePostComments,
  fanPageRoles,
  users
} from "@/lib/db/schema";
import { eq, and, desc, sql, or } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId]/dashboard/recent-activity - Get recent activity
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = await params;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20");

    // Verify user owns this fan page or has admin role
    const page = await db.query.fanPages.findFirst({
      where: and(
        eq(fanPages.id, pageId),
        eq(fanPages.isActive, true)
      ),
    });

    if (!page) {
      return NextResponse.json({ error: "Fan page not found" }, { status: 404 });
    }

    const isOwner = page.ownerId === session.user.id;
    let hasAdminRole = false;

    if (!isOwner) {
      const userRole = await db.query.fanPageRoles.findFirst({
        where: and(
          eq(fanPageRoles.fanPageId, pageId),
          eq(fanPageRoles.userId, session.user.id),
          eq(fanPageRoles.role, 'admin')
        ),
      });
      hasAdminRole = !!userRole;
    }

    if (!isOwner && !hasAdminRole) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Get recent activities
    const activities: any[] = [];

    // Recent followers (last 10)
    const recentFollowers = await db
      .select({
        id: fanPageFollowers.id,
        createdAt: fanPageFollowers.createdAt,
        user: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPageFollowers)
      .innerJoin(users, eq(fanPageFollowers.userId, users.id))
      .where(eq(fanPageFollowers.fanPageId, pageId))
      .orderBy(desc(fanPageFollowers.createdAt))
      .limit(10);

    recentFollowers.forEach(follower => {
      activities.push({
        id: `follower-${follower.id}`,
        type: 'new_follower',
        createdAt: follower.createdAt,
        user: follower.user,
        message: `${follower.user.name} started following your page`,
      });
    });

    // Recent messages (last 10)
    const recentMessages = await db
      .select({
        id: fanPageMessages.id,
        content: fanPageMessages.content,
        createdAt: fanPageMessages.createdAt,
        isRead: fanPageMessages.read,
        sender: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPageMessages)
      .innerJoin(users, eq(fanPageMessages.senderId, users.id))
      .where(eq(fanPageMessages.fanPageId, pageId))
      .orderBy(desc(fanPageMessages.createdAt))
      .limit(10);

    recentMessages.forEach(message => {
      activities.push({
        id: `message-${message.id}`,
        type: 'new_message',
        createdAt: message.createdAt,
        user: message.sender,
        message: `${message.sender.name} sent you a message`,
        content: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : ''),
        isRead: message.isRead,
      });
    });

    // Recent likes on posts (last 10)
    const recentLikes = await db
      .select({
        id: fanPagePostLikes.id,
        createdAt: fanPagePostLikes.createdAt,
        post: {
          id: fanPagePosts.id,
          content: fanPagePosts.content,
        },
        user: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPagePostLikes)
      .innerJoin(fanPagePosts, eq(fanPagePostLikes.fanPagePostId, fanPagePosts.id))
      .innerJoin(users, eq(fanPagePostLikes.userId, users.id))
      .where(eq(fanPagePosts.fanPageId, pageId))
      .orderBy(desc(fanPagePostLikes.createdAt))
      .limit(10);

    recentLikes.forEach(like => {
      activities.push({
        id: `like-${like.id}`,
        type: 'post_like',
        createdAt: like.createdAt,
        user: like.user,
        message: `${like.user.name} liked your post`,
        postContent: like.post.content.substring(0, 50) + (like.post.content.length > 50 ? '...' : ''),
        postId: like.post.id,
      });
    });

    // Recent comments on posts (last 10)
    const recentComments = await db
      .select({
        id: fanPagePostComments.id,
        content: fanPagePostComments.content,
        createdAt: fanPagePostComments.createdAt,
        post: {
          id: fanPagePosts.id,
          content: fanPagePosts.content,
        },
        user: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(fanPagePostComments)
      .innerJoin(fanPagePosts, eq(fanPagePostComments.postId, fanPagePosts.id))
      .innerJoin(users, eq(fanPagePostComments.userId, users.id))
      .where(eq(fanPagePosts.fanPageId, pageId))
      .orderBy(desc(fanPagePostComments.createdAt))
      .limit(10);

    recentComments.forEach(comment => {
      activities.push({
        id: `comment-${comment.id}`,
        type: 'post_comment',
        createdAt: comment.createdAt,
        user: comment.user,
        message: `${comment.user.name} commented on your post`,
        content: comment.content.substring(0, 100) + (comment.content.length > 100 ? '...' : ''),
        postContent: comment.post.content.substring(0, 50) + (comment.post.content.length > 50 ? '...' : ''),
        postId: comment.post.id,
      });
    });

    // Sort all activities by date and limit
    const sortedActivities = activities
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);

    return NextResponse.json({
      activities: sortedActivities,
      total: sortedActivities.length,
    });
  } catch (error) {
    console.error("Error fetching recent activity:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
