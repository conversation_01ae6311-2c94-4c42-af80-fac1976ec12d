import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, comments } from "@/lib/db/schema";
import { z } from "zod";
import { eq } from "drizzle-orm";
import { deleteMediaFiles } from "@/lib/storage/imageStorage";

const postUpdateSchema = z.object({
  content: z.string().max(5000).optional(),
  images: z.array(z.string().url()).optional(),
  videos: z.array(z.string().url()).optional(),
  privacy: z.enum(["public", "subscribers", "private"]).optional(),
  backgroundColor: z.string().optional(),
  feeling: z.string().optional(),
  activity: z.string().optional(),
  location: z.string().optional(),
  formattedContent: z.boolean().optional(),
});

// Get a single post with admin details
export async function GET(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { postId } = params;

    // Fetch the post from the database
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        sharedPost: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
          },
        },
        shares: true,
      },
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Format the response
    const formattedPost = {
      ...post,
      _count: {
        likes: post.likes.length,
        comments: post.comments.length,
        shares: post.shares.length,
      },
      // We would need to add reported status from a reports table
      // This is simplified for the example
      reported: false,
      reports: [],
    };

    return NextResponse.json(formattedPost);
  } catch (error) {
    console.error("Error fetching post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a post as admin
export async function PATCH(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { postId } = params;
    const body = await req.json();

    // Validate the request body
    const validatedData = postUpdateSchema.parse(body);

    // Check if the post exists
    const existingPost = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!existingPost) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Update the post
    await db.update(posts)
      .set({
        content: validatedData.content !== undefined ? validatedData.content : existingPost.content,
        images: validatedData.images !== undefined ? validatedData.images : existingPost.images,
        videos: validatedData.videos !== undefined ? validatedData.videos : existingPost.videos,
        privacy: validatedData.privacy !== undefined ? validatedData.privacy : existingPost.privacy,
        backgroundColor: validatedData.backgroundColor !== undefined ? validatedData.backgroundColor : existingPost.backgroundColor,
        feeling: validatedData.feeling !== undefined ? validatedData.feeling : existingPost.feeling,
        activity: validatedData.activity !== undefined ? validatedData.activity : existingPost.activity,
        location: validatedData.location !== undefined ? validatedData.location : existingPost.location,
        formattedContent: validatedData.formattedContent !== undefined ? validatedData.formattedContent : existingPost.formattedContent,
        updatedAt: new Date(),
      })
      .where(eq(posts.id, postId));

    return NextResponse.json({
      message: "Post updated successfully",
    });
  } catch (error) {
    console.error("Error updating post:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a post as admin
export async function DELETE(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { postId } = params;

    // Check if the post exists
    const existingPost = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!existingPost) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Delete associated media files from storage before deleting the post
    let mediaDeleteResult = null;
    if (existingPost.images || existingPost.videos) {
      try {
        console.log(`🗑️ Admin deleting media files for post ${postId}...`);
        mediaDeleteResult = await deleteMediaFiles(
          existingPost.images || [],
          existingPost.videos || []
        );

        if (process.env.NODE_ENV === 'development') {
          console.log('Admin media deletion result:', mediaDeleteResult);
        }
      } catch (error) {
        console.error('Error deleting admin post media files:', error);
        // Continue with post deletion even if media deletion fails
      }
    }

    // Delete all comments associated with the post
    await db.delete(comments)
      .where(eq(comments.postId, postId));

    // Delete the post from database
    await db.delete(posts)
      .where(eq(posts.id, postId));

    // Prepare response with media deletion info
    const response: any = {
      message: "Post deleted successfully",
      postId: postId
    };

    if (mediaDeleteResult) {
      response.mediaDeleted = {
        success: mediaDeleteResult.success,
        deletedCount: mediaDeleteResult.deletedImages.length,
        failedCount: mediaDeleteResult.failedImages.length,
      };

      if (mediaDeleteResult.failedImages.length > 0) {
        console.warn(`⚠️ Some admin post media files could not be deleted:`, mediaDeleteResult.failedImages);
        response.mediaDeleted.warnings = mediaDeleteResult.errors;
      }
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error deleting post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
