"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';
import {
  XMarkIcon,
  CheckIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  UserPlusIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { cn } from '@/lib/utils';
import { NotificationTime } from '@/components/ui/TimeDisplay';
import { NotificationData } from '@/hooks/useNotifications';

interface NotificationPreviewProps {
  notification: NotificationData;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onNavigate?: (notification: NotificationData) => void;
  compact?: boolean;
}

export function NotificationPreview({
  notification,
  onMarkAsRead,
  onDelete,
  onNavigate,
  compact = false
}: NotificationPreviewProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    setIsAnimating(true);
    const timer = setTimeout(() => setIsAnimating(false), 300);
    return () => clearTimeout(timer);
  }, []);

  const getNotificationIcon = () => {
    const iconClass = "h-4 w-4";

    switch (notification.type) {
      case 'like':
        return <HeartSolidIcon className={cn(iconClass, "text-red-500")} />;
      case 'comment':
        return <ChatBubbleLeftIcon className={cn(iconClass, "text-blue-500")} />;
      case 'friend_request':
        return <UserPlusIcon className={cn(iconClass, "text-green-500")} />;
      case 'friend_accept':
        return <CheckIcon className={cn(iconClass, "text-green-500")} />;
      case 'message':
        return <EnvelopeIcon className={cn(iconClass, "text-purple-500")} />;
      default:
        return <div className={cn("rounded-full bg-gray-400", iconClass)} />;
    }
  };

  const getNotificationText = () => {
    const senderName = notification.sender?.name || 'Someone';

    switch (notification.type) {
      case 'like':
        return {
          action: 'liked',
          target: 'your post',
          fullText: `${senderName} liked your post`
        };
      case 'comment':
        return {
          action: 'commented on',
          target: 'your post',
          fullText: `${senderName} commented on your post`
        };
      case 'friend_request':
        return {
          action: 'sent you',
          target: 'a friend request',
          fullText: `${senderName} sent you a friend request`
        };
      case 'friend_accept':
        return {
          action: 'accepted',
          target: 'your friend request',
          fullText: `${senderName} accepted your friend request`
        };
      case 'message':
        return {
          action: 'sent you',
          target: 'a message',
          fullText: `${senderName} sent you a message`
        };
      default:
        return {
          action: 'interacted with',
          target: 'your content',
          fullText: `${senderName} interacted with your content`
        };
    }
  };

  const handleClick = () => {
    if (!notification.read) {
      onMarkAsRead(notification.id);
    }
    if (onNavigate) {
      onNavigate(notification);
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(notification.id);
  };

  const notificationText = getNotificationText();
  // Removed timeAgo variable as we'll use NotificationTime component

  if (compact) {
    return (
      <div
        className={cn(
          "flex items-center space-x-3 p-3 hover:bg-gray-50 transition-all duration-200 cursor-pointer",
          !notification.read && "bg-blue-50/50",
          isAnimating && "animate-slideInRight"
        )}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Compact Avatar */}
        <div className="flex-shrink-0 relative">
          {notification.sender?.image ? (
            <Image
              src={notification.sender.image}
              alt={notification.sender.name}
              width={32}
              height={32}
              className="rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-semibold">
              {notification.sender?.name?.charAt(0) || '?'}
            </div>
          )}
          <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-white rounded-full flex items-center justify-center">
            {getNotificationIcon()}
          </div>
        </div>

        {/* Compact Content */}
        <div className="flex-1 min-w-0">
          <p className={cn(
            "text-sm truncate",
            !notification.read ? "font-medium text-gray-900" : "text-gray-700"
          )}>
            {notificationText.fullText}
          </p>
          <NotificationTime
            date={notification.createdAt}
            className="text-xs text-gray-500"
            autoUpdate={true}
          />
        </div>

        {/* Compact Actions */}
        {isHovered && (
          <div className="flex items-center space-x-1">
            {!notification.read && (
              <button
                onClick={handleMarkAsRead}
                className="p-1 rounded-full hover:bg-blue-100 transition-colors"
                title="Mark as read"
              >
                <CheckIcon className="h-3 w-3 text-blue-600" />
              </button>
            )}
            <button
              onClick={handleDelete}
              className="p-1 rounded-full hover:bg-red-100 transition-colors"
              title="Delete"
            >
              <XMarkIcon className="h-3 w-3 text-red-600" />
            </button>
          </div>
        )}

        {!notification.read && (
          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
        )}
      </div>
    );
  }

  return (
    <div
      className={cn(
        "relative p-4 hover:bg-gray-50 transition-all duration-200 cursor-pointer border-l-4",
        !notification.read
          ? "bg-blue-50/30 border-blue-500"
          : "bg-white border-transparent hover:border-gray-200",
        isAnimating && "animate-fadeInUp"
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-start space-x-4">
        {/* Avatar with Icon Overlay */}
        <div className="flex-shrink-0 relative">
          {notification.sender?.image ? (
            <Image
              src={notification.sender.image}
              alt={notification.sender.name}
              width={48}
              height={48}
              className="rounded-full ring-2 ring-white shadow-sm"
            />
          ) : (
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold shadow-sm">
              {notification.sender?.name?.charAt(0) || '?'}
            </div>
          )}

          {/* Notification Type Icon */}
          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-sm border-2 border-white">
            {getNotificationIcon()}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <p className={cn(
                "text-sm leading-5",
                !notification.read
                  ? "text-gray-900 font-semibold"
                  : "text-gray-700"
              )}>
                <span className="font-medium">{notification.sender?.name || 'Someone'}</span>
                {' '}
                <span className="font-normal">{notificationText.action}</span>
                {' '}
                <span className="text-blue-600">{notificationText.target}</span>
              </p>

              <div className="flex items-center space-x-2 mt-1">
                <NotificationTime
                  date={notification.createdAt}
                  className="text-xs text-gray-500"
                  autoUpdate={true}
                />
                {!notification.read && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    New
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            {isHovered && (
              <div className="flex items-center space-x-2 ml-4">
                {!notification.read && (
                  <button
                    onClick={handleMarkAsRead}
                    className="p-2 rounded-full hover:bg-blue-100 transition-colors"
                    title="Mark as read"
                  >
                    <CheckIcon className="h-4 w-4 text-blue-600" />
                  </button>
                )}

                <button
                  onClick={handleDelete}
                  className="p-2 rounded-full hover:bg-red-100 transition-colors"
                  title="Delete notification"
                >
                  <XMarkIcon className="h-4 w-4 text-red-600" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Unread Indicator */}
        {!notification.read && !isHovered && (
          <div className="absolute top-4 right-4">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
          </div>
        )}
      </div>
    </div>
  );
}
