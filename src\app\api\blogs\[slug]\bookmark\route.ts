import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogBookmarks } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

// Toggle bookmark on a blog
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { slug } = params;

    // First, get the blog to find its ID
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user has already bookmarked this blog
    const existingBookmark = await db.query.blogBookmarks.findFirst({
      where: and(
        eq(blogBookmarks.blogId, blog.id),
        eq(blogBookmarks.userId, session.user.id)
      ),
    });

    if (existingBookmark) {
      // Remove bookmark
      await db.delete(blogBookmarks).where(eq(blogBookmarks.id, existingBookmark.id));
      
      return NextResponse.json({
        message: "Blog bookmark removed successfully",
        isBookmarked: false,
      });
    } else {
      // Add bookmark
      const bookmarkId = uuidv4();
      await db.insert(blogBookmarks).values({
        id: bookmarkId,
        userId: session.user.id,
        blogId: blog.id,
      });

      return NextResponse.json({
        message: "Blog bookmarked successfully",
        isBookmarked: true,
      });
    }
  } catch (error) {
    console.error("Error toggling blog bookmark:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
