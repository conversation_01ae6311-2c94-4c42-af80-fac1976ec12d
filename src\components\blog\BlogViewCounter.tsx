"use client";

import { useState, useEffect } from "react";
import { EyeIcon } from "@heroicons/react/24/outline";

interface BlogViewCounterProps {
  initialCount: number;
  blogSlug: string;
  showIcon?: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function BlogViewCounter({ 
  initialCount, 
  blogSlug,
  showIcon = true,
  className = "",
  size = "md"
}: BlogViewCounterProps) {
  const [viewCount, setViewCount] = useState(initialCount);
  const [isAnimating, setIsAnimating] = useState(false);

  // Listen for view count updates (could be from real-time updates)
  useEffect(() => {
    const handleViewUpdate = (event: CustomEvent) => {
      if (event.detail.blogSlug === blogSlug) {
        setViewCount(prev => {
          const newCount = prev + 1;
          setIsAnimating(true);
          setTimeout(() => setIsAnimating(false), 300);
          return newCount;
        });
      }
    };

    window.addEventListener('blogViewUpdate' as any, handleViewUpdate);
    return () => {
      window.removeEventListener('blogViewUpdate' as any, handleViewUpdate);
    };
  }, [blogSlug]);

  const formatViewCount = (count: number): string => {
    if (count < 1000) return count.toString();
    if (count < 1000000) return `${(count / 1000).toFixed(1)}K`;
    return `${(count / 1000000).toFixed(1)}M`;
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "text-xs";
      case "lg":
        return "text-lg";
      default:
        return "text-sm";
    }
  };

  const getIconSize = () => {
    switch (size) {
      case "sm":
        return "h-3 w-3";
      case "lg":
        return "h-5 w-5";
      default:
        return "h-4 w-4";
    }
  };

  return (
    <div className={`flex items-center space-x-1 ${getSizeClasses()} ${className}`}>
      {showIcon && (
        <EyeIcon className={`${getIconSize()} text-gray-500`} />
      )}
      <span 
        className={`
          text-gray-600 font-medium transition-all duration-300
          ${isAnimating ? 'scale-110 text-blue-600' : ''}
        `}
        title={`${viewCount.toLocaleString()} views`}
      >
        {formatViewCount(viewCount)}
      </span>
    </div>
  );
}
