"use client";

import { PartyKitClient } from "./client";

export interface ConnectionPoolConfig {
  maxConnections: number;
  idleTimeout: number; // milliseconds
  reconnectDelay: number; // milliseconds
  maxReconnectAttempts: number;
  connectionTTL: number; // time to live in milliseconds
}

export interface RoomOptimizationConfig {
  maxRoomsPerUser: number;
  roomIdleTimeout: number;
  enableRoomMerging: boolean;
  roomPriorityLevels: string[];
}

export interface MessageBatchingConfig {
  batchSize: number;
  batchTimeout: number; // milliseconds
  enableBatching: boolean;
  priorityMessages: string[]; // message types that skip batching
}

class ConnectionPool {
  private connections = new Map<string, PartyKitClient>();
  private connectionUsage = new Map<string, number>();
  private connectionTimestamps = new Map<string, number>();
  private config: ConnectionPoolConfig;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config: ConnectionPoolConfig) {
    this.config = config;
    this.startCleanupInterval();
  }

  getConnection(key: string, factory: () => PartyKitClient): PartyKitClient {
    // Check if connection exists and is still valid
    if (this.connections.has(key)) {
      const connection = this.connections.get(key)!;
      const timestamp = this.connectionTimestamps.get(key)!;
      
      // Check if connection is still within TTL
      if (Date.now() - timestamp < this.config.connectionTTL) {
        this.connectionUsage.set(key, (this.connectionUsage.get(key) || 0) + 1);
        return connection;
      } else {
        // Connection expired, remove it
        this.removeConnection(key);
      }
    }

    // Check if we've reached max connections
    if (this.connections.size >= this.config.maxConnections) {
      this.evictLeastUsedConnection();
    }

    // Create new connection
    const connection = factory();
    this.connections.set(key, connection);
    this.connectionUsage.set(key, 1);
    this.connectionTimestamps.set(key, Date.now());

    return connection;
  }

  removeConnection(key: string) {
    const connection = this.connections.get(key);
    if (connection) {
      connection.disconnect();
      this.connections.delete(key);
      this.connectionUsage.delete(key);
      this.connectionTimestamps.delete(key);
    }
  }

  private evictLeastUsedConnection() {
    let leastUsedKey = "";
    let leastUsageCount = Infinity;

    for (const [key, usage] of this.connectionUsage.entries()) {
      if (usage < leastUsageCount) {
        leastUsageCount = usage;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.removeConnection(leastUsedKey);
    }
  }

  private startCleanupInterval() {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now();
      const keysToRemove: string[] = [];

      for (const [key, timestamp] of this.connectionTimestamps.entries()) {
        if (now - timestamp > this.config.idleTimeout) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => this.removeConnection(key));
    }, this.config.idleTimeout / 2); // Check every half of idle timeout
  }

  getStats() {
    return {
      totalConnections: this.connections.size,
      connectionUsage: Object.fromEntries(this.connectionUsage),
      oldestConnection: Math.min(...Array.from(this.connectionTimestamps.values())),
      newestConnection: Math.max(...Array.from(this.connectionTimestamps.values()))
    };
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    // Disconnect all connections
    for (const connection of this.connections.values()) {
      connection.disconnect();
    }
    
    this.connections.clear();
    this.connectionUsage.clear();
    this.connectionTimestamps.clear();
  }
}

class RoomOptimizer {
  private userRooms = new Map<string, Set<string>>();
  private roomActivity = new Map<string, number>();
  private config: RoomOptimizationConfig;

  constructor(config: RoomOptimizationConfig) {
    this.config = config;
  }

  canJoinRoom(userId: string, roomId: string): boolean {
    const userRoomSet = this.userRooms.get(userId) || new Set();
    
    // Check if user has reached max rooms
    if (userRoomSet.size >= this.config.maxRoomsPerUser && !userRoomSet.has(roomId)) {
      return false;
    }

    return true;
  }

  joinRoom(userId: string, roomId: string, priority: string = "normal") {
    if (!this.canJoinRoom(userId, roomId)) {
      // Try to leave least active room
      this.leaveLeastActiveRoom(userId);
    }

    let userRoomSet = this.userRooms.get(userId);
    if (!userRoomSet) {
      userRoomSet = new Set();
      this.userRooms.set(userId, userRoomSet);
    }

    userRoomSet.add(roomId);
    this.roomActivity.set(roomId, Date.now());
  }

  leaveRoom(userId: string, roomId: string) {
    const userRoomSet = this.userRooms.get(userId);
    if (userRoomSet) {
      userRoomSet.delete(roomId);
      if (userRoomSet.size === 0) {
        this.userRooms.delete(userId);
      }
    }
  }

  private leaveLeastActiveRoom(userId: string) {
    const userRoomSet = this.userRooms.get(userId);
    if (!userRoomSet || userRoomSet.size === 0) return;

    let leastActiveRoom = "";
    let oldestActivity = Infinity;

    for (const roomId of userRoomSet) {
      const activity = this.roomActivity.get(roomId) || 0;
      if (activity < oldestActivity) {
        oldestActivity = activity;
        leastActiveRoom = roomId;
      }
    }

    if (leastActiveRoom) {
      this.leaveRoom(userId, leastActiveRoom);
    }
  }

  optimizeRooms(): string[] {
    const now = Date.now();
    const roomsToClose: string[] = [];

    for (const [roomId, lastActivity] of this.roomActivity.entries()) {
      if (now - lastActivity > this.config.roomIdleTimeout) {
        roomsToClose.push(roomId);
      }
    }

    // Remove inactive rooms
    roomsToClose.forEach(roomId => {
      this.roomActivity.delete(roomId);
      // Remove room from all users
      for (const userRoomSet of this.userRooms.values()) {
        userRoomSet.delete(roomId);
      }
    });

    return roomsToClose;
  }

  getStats() {
    return {
      totalRooms: this.roomActivity.size,
      totalUserRoomMappings: this.userRooms.size,
      averageRoomsPerUser: Array.from(this.userRooms.values()).reduce((sum, rooms) => sum + rooms.size, 0) / this.userRooms.size || 0,
      oldestRoomActivity: Math.min(...Array.from(this.roomActivity.values())),
      newestRoomActivity: Math.max(...Array.from(this.roomActivity.values()))
    };
  }
}

class MessageBatcher {
  private batches = new Map<string, any[]>();
  private batchTimers = new Map<string, NodeJS.Timeout>();
  private config: MessageBatchingConfig;

  constructor(config: MessageBatchingConfig) {
    this.config = config;
  }

  addMessage(roomId: string, message: any, sendCallback: (messages: any[]) => void): boolean {
    if (!this.config.enableBatching || this.config.priorityMessages.includes(message.type)) {
      // Send immediately for priority messages or if batching is disabled
      sendCallback([message]);
      return true;
    }

    let batch = this.batches.get(roomId);
    if (!batch) {
      batch = [];
      this.batches.set(roomId, batch);
    }

    batch.push(message);

    // Check if batch is full
    if (batch.length >= this.config.batchSize) {
      this.flushBatch(roomId, sendCallback);
      return true;
    }

    // Set timer if not already set
    if (!this.batchTimers.has(roomId)) {
      const timer = setTimeout(() => {
        this.flushBatch(roomId, sendCallback);
      }, this.config.batchTimeout);
      
      this.batchTimers.set(roomId, timer);
    }

    return false; // Message was batched
  }

  private flushBatch(roomId: string, sendCallback: (messages: any[]) => void) {
    const batch = this.batches.get(roomId);
    if (batch && batch.length > 0) {
      sendCallback(batch);
      this.batches.set(roomId, []);
    }

    // Clear timer
    const timer = this.batchTimers.get(roomId);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(roomId);
    }
  }

  flushAll(sendCallback: (roomId: string, messages: any[]) => void) {
    for (const [roomId, batch] of this.batches.entries()) {
      if (batch.length > 0) {
        sendCallback(roomId, batch);
      }
    }
    
    this.batches.clear();
    
    // Clear all timers
    for (const timer of this.batchTimers.values()) {
      clearTimeout(timer);
    }
    this.batchTimers.clear();
  }

  getStats() {
    return {
      totalBatches: this.batches.size,
      totalPendingMessages: Array.from(this.batches.values()).reduce((sum, batch) => sum + batch.length, 0),
      averageBatchSize: Array.from(this.batches.values()).reduce((sum, batch) => sum + batch.length, 0) / this.batches.size || 0,
      activeBatchTimers: this.batchTimers.size
    };
  }
}

// Singleton instances with default configurations
export const connectionPool = new ConnectionPool({
  maxConnections: 10,
  idleTimeout: 300000, // 5 minutes
  reconnectDelay: 1000,
  maxReconnectAttempts: 5,
  connectionTTL: 3600000 // 1 hour
});

export const roomOptimizer = new RoomOptimizer({
  maxRoomsPerUser: 5,
  roomIdleTimeout: 600000, // 10 minutes
  enableRoomMerging: true,
  roomPriorityLevels: ["high", "medium", "low"]
});

export const messageBatcher = new MessageBatcher({
  batchSize: 10,
  batchTimeout: 1000, // 1 second
  enableBatching: true,
  priorityMessages: ["auth", "error", "urgent_notification"]
});

// Optimization manager
export class PartyKitOptimizer {
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startOptimizationLoop();
  }

  private startOptimizationLoop() {
    this.cleanupInterval = setInterval(() => {
      // Optimize rooms
      const closedRooms = roomOptimizer.optimizeRooms();
      if (closedRooms.length > 0) {
        console.log(`Optimized ${closedRooms.length} inactive rooms`);
      }

      // Log optimization stats
      if (process.env.NODE_ENV === 'development') {
        console.log('PartyKit Optimization Stats:', {
          connectionPool: connectionPool.getStats(),
          roomOptimizer: roomOptimizer.getStats(),
          messageBatcher: messageBatcher.getStats()
        });
      }
    }, 60000); // Run every minute
  }

  getOptimizationReport() {
    return {
      timestamp: new Date().toISOString(),
      connectionPool: connectionPool.getStats(),
      roomOptimizer: roomOptimizer.getStats(),
      messageBatcher: messageBatcher.getStats(),
      recommendations: this.generateRecommendations()
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const poolStats = connectionPool.getStats();
    const roomStats = roomOptimizer.getStats();
    const batchStats = messageBatcher.getStats();

    if (poolStats.totalConnections > 8) {
      recommendations.push("Consider reducing connection pool size or increasing cleanup frequency");
    }

    if (roomStats.averageRoomsPerUser > 3) {
      recommendations.push("Users are joining too many rooms, consider implementing room prioritization");
    }

    if (batchStats.averageBatchSize < 3) {
      recommendations.push("Message batching is underutilized, consider increasing batch timeout");
    }

    if (recommendations.length === 0) {
      recommendations.push("All optimization metrics are within acceptable ranges");
    }

    return recommendations;
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    connectionPool.destroy();
  }
}

export const partyKitOptimizer = new PartyKitOptimizer();
