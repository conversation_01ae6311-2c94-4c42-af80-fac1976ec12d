"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/Tabs";
import { Select } from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { toast } from "react-hot-toast";
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  TrashIcon,
  PencilIcon,
  CalendarIcon,
  EyeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  XMarkIcon,
  CheckIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  LockClosedIcon,
  UserGroupIcon,
  MapPinIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { formatTimeAgo, formatDate } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import { CreateEventModal } from "@/components/admin/events/CreateEventModal";
import { EditEventModal } from "@/components/admin/events/EditEventModal";
import { DeleteEventModal } from "@/components/admin/events/DeleteEventModal";

interface Event {
  id: string;
  name: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  isOnline: boolean;
  onlineLink: string | null;
  visibility: "public" | "private" | "friends";
  category: string | null;
  coverImage: string | null;
  hostId: string;
  createdAt: string;
  updatedAt: string;
  host?: {
    id: string;
    name: string;
    username: string | null;
    image: string | null;
  };
  attendeeCounts?: {
    going: number;
    interested: number;
    not_going: number;
  };
}

export default function AdminEventsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [events, setEvents] = useState<Event[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<Event[]>([]);
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    visibility: "all",
    category: "all",
    dateRange: "all",
    status: "all",
  });
  const [sortBy, setSortBy] = useState("startTime");
  const [sortOrder, setSortOrder] = useState("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentEvent, setCurrentEvent] = useState<Event | null>(null);
  const [bulkAction, setBulkAction] = useState("");

  // Fetch events data
  useEffect(() => {
    fetchEvents();
  }, [currentPage, sortBy, sortOrder]);

  // Apply filters and search
  useEffect(() => {
    if (events.length > 0) {
      let result = [...events];

      // Apply search
      if (searchTerm) {
        result = result.filter(
          (event) =>
            event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (event.description &&
              event.description.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply filters
      if (filters.visibility !== "all") {
        result = result.filter((event) => event.visibility === filters.visibility);
      }

      if (filters.category !== "all") {
        result = result.filter((event) => event.category === filters.category);
      }

      // Apply date range filter
      if (filters.dateRange !== "all") {
        const now = new Date();
        let dateLimit;

        switch (filters.dateRange) {
          case "today":
            dateLimit = new Date(now.setDate(now.getDate() - 1));
            break;
          case "week":
            dateLimit = new Date(now.setDate(now.getDate() - 7));
            break;
          case "month":
            dateLimit = new Date(now.setMonth(now.getMonth() - 1));
            break;
          case "year":
            dateLimit = new Date(now.setFullYear(now.getFullYear() - 1));
            break;
          default:
            dateLimit = null;
        }

        if (dateLimit) {
          result = result.filter(
            (event) => new Date(event.createdAt) > dateLimit
          );
        }
      }

      // Apply status filter
      if (filters.status !== "all") {
        const now = new Date();

        switch (filters.status) {
          case "upcoming":
            result = result.filter((event) => new Date(event.startTime) > now);
            break;
          case "ongoing":
            result = result.filter(
              (event) =>
                new Date(event.startTime) <= now &&
                new Date(event.endTime) >= now
            );
            break;
          case "past":
            result = result.filter((event) => new Date(event.endTime) < now);
            break;
          default:
            break;
        }
      }

      setFilteredEvents(result);
    }
  }, [events, searchTerm, filters]);

  const fetchEvents = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/admin/events?page=${currentPage}&limit=10&sort=${sortBy}&order=${sortOrder}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch events");
      }

      const data = await response.json();
      if (data && Array.isArray(data.events)) {
        setEvents(data.events);
        setFilteredEvents(data.events);
        setTotalPages(data.totalPages || 1);
      } else {
        console.error("Invalid response format from admin events API:", data);
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error("Error fetching events:", error);
      toast.error("Failed to load events");

      // Fallback to regular events API if admin API is not available yet
      try {
        const fallbackResponse = await fetch(`/api/events`);
        if (fallbackResponse.ok) {
          const data = await fallbackResponse.json();
          if (data && Array.isArray(data)) {
            // Regular events API returns an array directly, not wrapped in an object
            setEvents(data);
            setFilteredEvents(data);
            setTotalPages(Math.ceil(data.length / 10) || 1);
          } else {
            console.error("Invalid response format from fallback events API:", data);
            // Set empty arrays as a fallback
            setEvents([]);
            setFilteredEvents([]);
            setTotalPages(1);
          }
        } else {
          // Set empty arrays if the fallback request fails
          setEvents([]);
          setFilteredEvents([]);
          setTotalPages(1);
        }
      } catch (fallbackError) {
        console.error("Fallback error:", fallbackError);
        // Set empty arrays if the fallback request throws an error
        setEvents([]);
        setFilteredEvents([]);
        setTotalPages(1);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  const handleSelectEvent = (eventId: string) => {
    setSelectedEvents((prev) =>
      prev.includes(eventId)
        ? prev.filter((id) => id !== eventId)
        : [...prev, eventId]
    );
  };

  const handleSelectAllEvents = () => {
    if (selectedEvents.length === filteredEvents.length) {
      setSelectedEvents([]);
    } else {
      setSelectedEvents(filteredEvents.map((event) => event.id));
    }
  };

  const handleEditEvent = (event: Event) => {
    setCurrentEvent(event);
    setIsEditModalOpen(true);
  };

  const handleDeleteEvent = (event: Event) => {
    setCurrentEvent(event);
    setIsDeleteModalOpen(true);
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedEvents.length === 0) return;

    try {
      if (bulkAction === "delete") {
        const confirmed = window.confirm(
          `Are you sure you want to delete ${selectedEvents.length} events? This action cannot be undone.`
        );

        if (!confirmed) return;

        const response = await fetch("/api/admin/events/bulk", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ eventIds: selectedEvents }),
        });

        if (!response.ok) {
          throw new Error("Failed to delete events");
        }

        toast.success(`${selectedEvents.length} events deleted successfully`);
        fetchEvents();
        setSelectedEvents([]);
      }
    } catch (error) {
      console.error("Error performing bulk action:", error);
      toast.error("Failed to perform bulk action");
    }
  };

  const handleCreateSuccess = () => {
    toast.success("Event created successfully");
    fetchEvents();
    setIsCreateModalOpen(false);
  };

  const handleEditSuccess = () => {
    toast.success("Event updated successfully");
    fetchEvents();
    setIsEditModalOpen(false);
  };

  const handleDeleteSuccess = () => {
    toast.success("Event deleted successfully");
    fetchEvents();
    setIsDeleteModalOpen(false);
  };

  const getVisibilityBadge = (visibility: string) => {
    switch (visibility) {
      case "public":
        return (
          <Badge variant="success" className="flex items-center">
            <GlobeAltIcon className="mr-1 h-3 w-3" />
            Public
          </Badge>
        );
      case "private":
        return (
          <Badge variant="danger" className="flex items-center">
            <LockClosedIcon className="mr-1 h-3 w-3" />
            Private
          </Badge>
        );
      case "friends":
        return (
          <Badge variant="warning" className="flex items-center">
            <UserGroupIcon className="mr-1 h-3 w-3" />
            Friends
          </Badge>
        );
      default:
        return null;
    }
  };

  const getEventStatusBadge = (event: Event) => {
    const now = new Date();
    const startTime = new Date(event.startTime);
    const endTime = new Date(event.endTime);

    if (startTime > now) {
      return (
        <Badge variant="primary" className="flex items-center">
          <ClockIcon className="mr-1 h-3 w-3" />
          Upcoming
        </Badge>
      );
    } else if (startTime <= now && endTime >= now) {
      return (
        <Badge variant="success" className="flex items-center">
          <CheckIcon className="mr-1 h-3 w-3" />
          Ongoing
        </Badge>
      );
    } else {
      return (
        <Badge variant="default" className="flex items-center">
          <XMarkIcon className="mr-1 h-3 w-3" />
          Past
        </Badge>
      );
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Event Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage all events, their settings, and attendees
          </p>
        </div>
        <div className="mt-4 flex space-x-2 sm:mt-0">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <FunnelIcon className="mr-2 h-5 w-5" />
            Filters
          </Button>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <PlusIcon className="mr-2 h-5 w-5" />
            Create Event
          </Button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="mb-6">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
          <div className="relative flex-grow">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search events..."
              className="pl-10"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
          <Button
            onClick={fetchEvents}
            variant="outline"
            className="flex-shrink-0"
          >
            <ArrowPathIcon className="mr-2 h-5 w-5" />
            Refresh
          </Button>
        </div>

        {showFilters && (
          <div className="mt-4 rounded-lg border border-gray-200 bg-white p-4">
            <h3 className="mb-3 font-medium text-gray-700">Filter Events</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Visibility
                </label>
                <Select
                  value={filters.visibility}
                  onChange={(e) => handleFilterChange("visibility", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Visibilities</option>
                  <option value="public">Public</option>
                  <option value="private">Private</option>
                  <option value="friends">Friends</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Category
                </label>
                <Select
                  value={filters.category}
                  onChange={(e) => handleFilterChange("category", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Categories</option>
                  <option value="social">Social</option>
                  <option value="business">Business</option>
                  <option value="education">Education</option>
                  <option value="entertainment">Entertainment</option>
                  <option value="sports">Sports</option>
                  <option value="technology">Technology</option>
                  <option value="health">Health</option>
                  <option value="other">Other</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Created
                </label>
                <Select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange("dateRange", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="year">This Year</option>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Status
                </label>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange("status", e.target.value)}
                  className="w-full"
                >
                  <option value="all">All Statuses</option>
                  <option value="upcoming">Upcoming</option>
                  <option value="ongoing">Ongoing</option>
                  <option value="past">Past</option>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk actions */}
      {selectedEvents.length > 0 && (
        <div className="mb-4 flex items-center justify-between rounded-lg bg-blue-50 p-3">
          <span className="text-sm font-medium text-blue-700">
            {selectedEvents.length} events selected
          </span>
          <div className="flex items-center space-x-2">
            <Select
              value={bulkAction}
              onChange={(e) => setBulkAction(e.target.value)}
              className="text-sm"
            >
              <option value="">Bulk Action</option>
              <option value="delete">Delete Selected</option>
            </Select>
            <Button
              onClick={handleBulkAction}
              disabled={!bulkAction}
              size="sm"
              variant={bulkAction === "delete" ? "danger" : "primary"}
            >
              Apply
            </Button>
          </div>
        </div>
      )}

      {/* Events table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Spinner size="lg" />
          </div>
        ) : filteredEvents.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center p-4 text-center">
            <CalendarIcon className="mb-2 h-12 w-12 text-gray-400" />
            <h3 className="mb-1 text-lg font-medium text-gray-900">No events found</h3>
            <p className="text-gray-500">
              {searchTerm || Object.values(filters).some((v) => v !== "all")
                ? "Try adjusting your search or filters"
                : "Create your first event to get started"}
            </p>
            {(searchTerm || Object.values(filters).some((v) => v !== "all")) && (
              <Button
                onClick={() => {
                  setSearchTerm("");
                  setFilters({
                    visibility: "all",
                    category: "all",
                    dateRange: "all",
                    status: "all",
                  });
                }}
                variant="outline"
                className="mt-4"
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="w-12 px-3 py-3 text-left">
                    <Checkbox
                      checked={
                        selectedEvents.length > 0 &&
                        selectedEvents.length === filteredEvents.length
                      }
                      onChange={handleSelectAllEvents}
                      aria-label="Select all events"
                    />
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("name")}
                    >
                      Event
                      {sortBy === "name" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("startTime")}
                    >
                      Date & Time
                      {sortBy === "startTime" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("location")}
                    >
                      Location
                      {sortBy === "location" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => handleSort("visibility")}
                    >
                      Visibility
                      {sortBy === "visibility" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {filteredEvents.map((event) => (
                  <tr key={event.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-3 py-4">
                      <Checkbox
                        checked={selectedEvents.includes(event.id)}
                        onChange={() => handleSelectEvent(event.id)}
                        aria-label={`Select ${event.name}`}
                      />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          {event.coverImage ? (
                            <Image
                              src={event.coverImage}
                              alt={event.name}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          ) : (
                            <div className="flex h-10 w-10 items-center justify-center rounded-md bg-blue-100 text-blue-600">
                              <CalendarIcon className="h-6 w-6" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {event.name}
                          </div>
                          {event.category && (
                            <div className="text-xs text-gray-500">
                              {event.category}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <div>
                        {new Date(event.startTime).toLocaleDateString('en-US', { dateStyle: 'medium' })}
                      </div>
                      <div className="text-xs">
                        {new Date(event.startTime).toLocaleTimeString('en-US', { timeStyle: 'short' })} - {new Date(event.endTime).toLocaleTimeString('en-US', { timeStyle: 'short' })}
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {event.isOnline ? (
                        <span className="flex items-center text-blue-600">
                          <GlobeAltIcon className="mr-1 h-4 w-4" />
                          Online
                        </span>
                      ) : event.location ? (
                        <span className="flex items-center">
                          <MapPinIcon className="mr-1 h-4 w-4" />
                          {event.location}
                        </span>
                      ) : (
                        <span className="text-gray-400">No location</span>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {getVisibilityBadge(event.visibility)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {getEventStatusBadge(event)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Button
                          onClick={() => router.push(`/admin/events/${event.id}`)}
                          variant="outline"
                          size="sm"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleEditEvent(event)}
                          variant="outline"
                          size="sm"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleDeleteEvent(event)}
                          variant="danger"
                          size="sm"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && filteredEvents.length > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{filteredEvents.length}</span>{" "}
            events
          </div>
          <div className="flex space-x-1">
            <Button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            <Button
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modals */}
      <CreateEventModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />

      {currentEvent && (
        <>
          <EditEventModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSuccess={handleEditSuccess}
            event={currentEvent}
          />

          <DeleteEventModal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            onSuccess={handleDeleteSuccess}
            event={currentEvent}
          />
        </>
      )}
    </AdminLayout>
  );
}
