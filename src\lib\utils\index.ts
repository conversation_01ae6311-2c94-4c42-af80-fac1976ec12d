import { ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import {
  formatDistanceToNow,
  format,
  isSameDay,
  isSameMonth,
  isSameYear,
  differenceInYears,
  isToday,
  isYesterday,
  differenceInMinutes,
  differenceInHours,
  differenceInDays,
  differenceInWeeks,
  differenceInMonths
} from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Enhanced time formatting with multiple options
export function formatTimeAgo(date: Date | string, options?: {
  style?: 'full' | 'short' | 'minimal';
  showSeconds?: boolean;
  maxUnit?: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'year';
}) {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  const now = new Date();
  const { style = 'full', showSeconds = false, maxUnit = 'year' } = options || {};

  const minutes = differenceInMinutes(now, dateObj);
  const hours = differenceInHours(now, dateObj);
  const days = differenceInDays(now, dateObj);
  const weeks = differenceInWeeks(now, dateObj);
  const months = differenceInMonths(now, dateObj);

  // Handle future dates
  if (minutes < 0) {
    return style === 'minimal' ? 'future' : 'in the future';
  }

  // Just now (less than 1 minute)
  if (minutes < 1) {
    if (style === 'minimal') return 'now';
    if (style === 'short') return 'now';
    return showSeconds ? 'just now' : 'just now';
  }

  // Minutes (1-59 minutes)
  if (minutes < 60 && (maxUnit === 'minute' || maxUnit === 'hour' || maxUnit === 'day' || maxUnit === 'week' || maxUnit === 'month' || maxUnit === 'year')) {
    if (style === 'minimal') return `${minutes}m`;
    if (style === 'short') return `${minutes}m ago`;
    return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
  }

  // Hours (1-23 hours)
  if (hours < 24 && (maxUnit === 'hour' || maxUnit === 'day' || maxUnit === 'week' || maxUnit === 'month' || maxUnit === 'year')) {
    if (style === 'minimal') return `${hours}h`;
    if (style === 'short') return `${hours}h ago`;
    return `${hours} hour${hours === 1 ? '' : 's'} ago`;
  }

  // Days (1-6 days)
  if (days < 7 && (maxUnit === 'day' || maxUnit === 'week' || maxUnit === 'month' || maxUnit === 'year')) {
    if (isYesterday(dateObj)) {
      if (style === 'minimal') return '1d';
      if (style === 'short') return 'yesterday';
      return 'yesterday';
    }
    if (style === 'minimal') return `${days}d`;
    if (style === 'short') return `${days}d ago`;
    return `${days} day${days === 1 ? '' : 's'} ago`;
  }

  // Weeks (1-3 weeks)
  if (weeks < 4 && (maxUnit === 'week' || maxUnit === 'month' || maxUnit === 'year')) {
    if (style === 'minimal') return `${weeks}w`;
    if (style === 'short') return `${weeks}w ago`;
    return `${weeks} week${weeks === 1 ? '' : 's'} ago`;
  }

  // Months (1-11 months)
  if (months < 12 && (maxUnit === 'month' || maxUnit === 'year')) {
    if (style === 'minimal') return `${months}mo`;
    if (style === 'short') return `${months}mo ago`;
    return `${months} month${months === 1 ? '' : 's'} ago`;
  }

  // Years
  if (maxUnit === 'year') {
    const years = Math.floor(months / 12);
    if (style === 'minimal') return `${years}y`;
    if (style === 'short') return `${years}y ago`;
    return `${years} year${years === 1 ? '' : 's'} ago`;
  }

  // Fallback to date-fns
  return formatDistanceToNow(dateObj, { addSuffix: true });
}

// Specific formatting for different contexts
export function formatPostTime(date: Date | string) {
  return formatTimeAgo(date, { style: 'short', maxUnit: 'year' });
}

export function formatMessageTime(date: Date | string) {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (isToday(dateObj)) {
    return format(dateObj, 'HH:mm');
  } else if (isYesterday(dateObj)) {
    return 'Yesterday';
  } else if (differenceInDays(new Date(), dateObj) < 7) {
    return format(dateObj, 'EEE');
  } else {
    return format(dateObj, 'MMM d');
  }
}

export function formatCommentTime(date: Date | string) {
  return formatTimeAgo(date, { style: 'short', maxUnit: 'week' });
}

export function formatNotificationTime(date: Date | string) {
  return formatTimeAgo(date, { style: 'short', maxUnit: 'month' });
}

export function formatDetailedTime(date: Date | string) {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (isToday(dateObj)) {
    return `Today at ${format(dateObj, 'HH:mm')}`;
  } else if (isYesterday(dateObj)) {
    return `Yesterday at ${format(dateObj, 'HH:mm')}`;
  } else if (differenceInDays(new Date(), dateObj) < 7) {
    return format(dateObj, 'EEEE \\a\\t HH:mm');
  } else if (isSameYear(dateObj, new Date())) {
    return format(dateObj, 'MMMM d \\a\\t HH:mm');
  } else {
    return format(dateObj, 'MMMM d, yyyy \\a\\t HH:mm');
  }
}

export function generateUsername(name: string) {
  return name
    .toLowerCase()
    .replace(/\s+/g, "")
    .replace(/[^a-z0-9]/g, "")
    .substring(0, 15);
}

export function truncateText(text: string, maxLength: number) {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
}

export function isOnThisDay(date: Date | string, referenceDate: Date = new Date()) {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return isSameDay(dateObj, referenceDate) &&
         isSameMonth(dateObj, referenceDate) &&
         !isSameYear(dateObj, referenceDate);
}

export function getYearsAgo(date: Date | string) {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return differenceInYears(new Date(), dateObj);
}

export function formatMemoryDate(date: Date | string) {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return format(dateObj, "MMMM d, yyyy");
}

export function formatDate(date: Date | string) {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return format(dateObj, "MMMM d, yyyy");
}

export function formatDateMedium(date: Date | string) {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return format(dateObj, "MMM d, yyyy");
}

export function formatTimeShort(date: Date | string) {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return format(dateObj, "HH:mm");
}

export function formatCurrency(amount: number | string, currency: string = "USD") {
  // Convert string to number if needed
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numAmount / 100); // Convert cents to dollars
}
