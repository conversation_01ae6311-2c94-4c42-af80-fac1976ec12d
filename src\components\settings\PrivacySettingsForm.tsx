"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { SettingsLayout } from "./SettingsLayout";
import { Button } from "@/components/ui/Button";
import {
  EyeIcon,
  EyeSlashIcon,
  UserGroupIcon,
  LockClosedIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  ChatBubbleLeftRightIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";

interface UserData {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  username?: string | null;
  bio?: string | null;
  location?: string | null;
  birthday?: string | null;
  coverImage?: string | null;
  website?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  instagram?: string | null;
  linkedin?: string | null;
  youtube?: string | null;
  profileVisibility?: string | null;
  showEmail?: boolean | null;
  showPhone?: boolean | null;
  showBirthday?: boolean | null;
  showLocation?: boolean | null;
  allowFriendRequests?: string | null;
  defaultPostPrivacy?: string | null;
  allowTagging?: boolean | null;
  allowMessagesFrom?: string | null;
  showOnlineStatus?: boolean | null;
  allowSearchByEmail?: boolean | null;
  allowSearchByPhone?: boolean | null;
}

interface PrivacySettingsFormProps {
  userId: string;
}

const privacyOptions = {
  profileVisibility: [
    { value: 'public', label: 'Public', icon: GlobeAltIcon, description: 'Anyone can see your profile' },
    { value: 'subscribers', label: 'Subscribers Only', icon: UserGroupIcon, description: 'Only your subscribers can see your profile' },
    { value: 'private', label: 'Private', icon: LockClosedIcon, description: 'Only you can see your profile' },
  ],
  allowFriendRequests: [
    { value: 'everyone', label: 'Everyone', description: 'Anyone can send you friend requests' },
    { value: 'friends-of-friends', label: 'Friends of Friends', description: 'Only friends of your friends can send requests' },
    { value: 'nobody', label: 'Nobody', description: 'No one can send you friend requests' },
  ],
  defaultPostPrivacy: [
    { value: 'public', label: 'Public', icon: GlobeAltIcon, description: 'Anyone can see your posts' },
    { value: 'subscribers', label: 'Subscribers Only', icon: UserGroupIcon, description: 'Only your subscribers can see your posts' },
    { value: 'private', label: 'Only Me', icon: LockClosedIcon, description: 'Only you can see your posts' },
  ],
  allowMessagesFrom: [
    { value: 'everyone', label: 'Everyone', description: 'Anyone can send you messages' },
    { value: 'subscribers', label: 'Subscribers Only', description: 'Only your subscribers can send you messages' },
    { value: 'nobody', label: 'Nobody', description: 'No one can send you messages' },
  ],
};

export function PrivacySettingsForm({ userId }: PrivacySettingsFormProps) {
  const { data: session } = useSession();
  const router = useRouter();

  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    profileVisibility: "public",
    showEmail: false,
    showPhone: false,
    showBirthday: true,
    showLocation: true,
    allowFriendRequests: "everyone",
    defaultPostPrivacy: "public",
    allowTagging: true,
    allowMessagesFrom: "everyone",
    showOnlineStatus: true,
    allowSearchByEmail: true,
    allowSearchByPhone: false,
  });

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/users/${userId}`);

        if (response.ok) {
          const data = await response.json();
          setUserData(data);
          setFormData({
            profileVisibility: data.profileVisibility || "public",
            showEmail: data.showEmail || false,
            showPhone: data.showPhone || false,
            showBirthday: data.showBirthday !== null ? data.showBirthday : true,
            showLocation: data.showLocation !== null ? data.showLocation : true,
            allowFriendRequests: data.allowFriendRequests || "everyone",
            defaultPostPrivacy: data.defaultPostPrivacy || "public",
            allowTagging: data.allowTagging !== null ? data.allowTagging : true,
            allowMessagesFrom: data.allowMessagesFrom || "everyone",
            showOnlineStatus: data.showOnlineStatus !== null ? data.showOnlineStatus : true,
            allowSearchByEmail: data.allowSearchByEmail !== null ? data.allowSearchByEmail : true,
            allowSearchByPhone: data.allowSearchByPhone || false,
          });
        } else {
          setError("Failed to load privacy settings");
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setError("An error occurred while loading your privacy settings");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          profileVisibility: formData.profileVisibility,
          showEmail: formData.showEmail,
          showPhone: formData.showPhone,
          showBirthday: formData.showBirthday,
          showLocation: formData.showLocation,
          allowFriendRequests: formData.allowFriendRequests,
          defaultPostPrivacy: formData.defaultPostPrivacy,
          allowTagging: formData.allowTagging,
          allowMessagesFrom: formData.allowMessagesFrom,
          showOnlineStatus: formData.showOnlineStatus,
          allowSearchByEmail: formData.allowSearchByEmail,
          allowSearchByPhone: formData.allowSearchByPhone,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to update privacy settings");
      }

      setSuccess("Privacy settings updated successfully!");

      // Refresh user data
      const updatedResponse = await fetch(`/api/users/${userId}`);
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json();
        setUserData(updatedData);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

    } catch (error) {
      console.error("Error updating privacy settings:", error);
      setError(error instanceof Error ? error.message : "Failed to update privacy settings");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (userData) {
      setFormData({
        profileVisibility: userData.profileVisibility || "public",
        showEmail: userData.showEmail || false,
        showPhone: userData.showPhone || false,
        showBirthday: userData.showBirthday !== null && userData.showBirthday !== undefined ? userData.showBirthday : true,
        showLocation: userData.showLocation !== null && userData.showLocation !== undefined ? userData.showLocation : true,
        allowFriendRequests: userData.allowFriendRequests || "everyone",
        defaultPostPrivacy: userData.defaultPostPrivacy || "public",
        allowTagging: userData.allowTagging !== null && userData.allowTagging !== undefined ? userData.allowTagging : true,
        allowMessagesFrom: userData.allowMessagesFrom || "everyone",
        showOnlineStatus: userData.showOnlineStatus !== null && userData.showOnlineStatus !== undefined ? userData.showOnlineStatus : true,
        allowSearchByEmail: userData.allowSearchByEmail !== null && userData.allowSearchByEmail !== undefined ? userData.allowSearchByEmail : true,
        allowSearchByPhone: userData.allowSearchByPhone || false,
      });
    }
    setError(null);
    setSuccess(null);
  };

  if (isLoading) {
    return (
      <SettingsLayout
        title="Privacy Settings"
        description="Control your privacy and visibility on the platform."
      >
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </SettingsLayout>
    );
  }

  return (
    <SettingsLayout
      title="Privacy Settings"
      description="Control your privacy and visibility on the platform."
    >
      <form onSubmit={handleSubmit}>
        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">Success</h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>{success}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Profile Privacy */}
        <div className="space-y-8">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
              <ShieldCheckIcon className="h-5 w-5 mr-2 text-blue-600" />
              Profile Privacy
            </h4>

            <div className="space-y-4">
              <div>
                <label htmlFor="profileVisibility" className="block text-sm font-medium text-gray-700 mb-2">
                  Profile Visibility
                </label>
                <select
                  id="profileVisibility"
                  name="profileVisibility"
                  value={formData.profileVisibility}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  {privacyOptions.profileVisibility.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label} - {option.description}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-3">
                <div className="flex items-start">
                  <div className="flex h-5 items-center">
                    <input
                      id="showEmail"
                      name="showEmail"
                      type="checkbox"
                      checked={formData.showEmail}
                      onChange={handleInputChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="showEmail" className="font-medium text-gray-700">
                      Show email address
                    </label>
                    <p className="text-gray-500">
                      Allow others to see your email address on your profile.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex h-5 items-center">
                    <input
                      id="showPhone"
                      name="showPhone"
                      type="checkbox"
                      checked={formData.showPhone}
                      onChange={handleInputChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="showPhone" className="font-medium text-gray-700">
                      Show phone number
                    </label>
                    <p className="text-gray-500">
                      Allow others to see your phone number on your profile.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex h-5 items-center">
                    <input
                      id="showBirthday"
                      name="showBirthday"
                      type="checkbox"
                      checked={formData.showBirthday}
                      onChange={handleInputChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="showBirthday" className="font-medium text-gray-700">
                      Show birthday
                    </label>
                    <p className="text-gray-500">
                      Allow others to see your birthday on your profile.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex h-5 items-center">
                    <input
                      id="showLocation"
                      name="showLocation"
                      type="checkbox"
                      checked={formData.showLocation}
                      onChange={handleInputChange}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="showLocation" className="font-medium text-gray-700">
                      Show location
                    </label>
                    <p className="text-gray-500">
                      Allow others to see your location on your profile.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Post Privacy */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
              <EyeIcon className="h-5 w-5 mr-2 text-blue-600" />
              Post Privacy
            </h4>

            <div>
              <label htmlFor="defaultPostPrivacy" className="block text-sm font-medium text-gray-700 mb-2">
                Default post privacy
              </label>
              <select
                id="defaultPostPrivacy"
                name="defaultPostPrivacy"
                value={formData.defaultPostPrivacy}
                onChange={handleInputChange}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                {privacyOptions.defaultPostPrivacy.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label} - {option.description}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                This will be the default privacy setting for your new posts.
              </p>
            </div>

            <div className="mt-4">
              <div className="flex items-start">
                <div className="flex h-5 items-center">
                  <input
                    id="allowTagging"
                    name="allowTagging"
                    type="checkbox"
                    checked={formData.allowTagging}
                    onChange={handleInputChange}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="allowTagging" className="font-medium text-gray-700">
                    Allow tagging
                  </label>
                  <p className="text-gray-500">
                    Allow others to tag you in their posts and photos.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Friend Requests */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
              <UserGroupIcon className="h-5 w-5 mr-2 text-blue-600" />
              Friend Requests
            </h4>

            <div>
              <label htmlFor="allowFriendRequests" className="block text-sm font-medium text-gray-700 mb-2">
                Who can send you friend requests?
              </label>
              <select
                id="allowFriendRequests"
                name="allowFriendRequests"
                value={formData.allowFriendRequests}
                onChange={handleInputChange}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                {privacyOptions.allowFriendRequests.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label} - {option.description}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Messages */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
              <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2 text-blue-600" />
              Messages
            </h4>

            <div>
              <label htmlFor="allowMessagesFrom" className="block text-sm font-medium text-gray-700 mb-2">
                Who can send you messages?
              </label>
              <select
                id="allowMessagesFrom"
                name="allowMessagesFrom"
                value={formData.allowMessagesFrom}
                onChange={handleInputChange}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                {privacyOptions.allowMessagesFrom.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label} - {option.description}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Search & Discovery */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
              <MagnifyingGlassIcon className="h-5 w-5 mr-2 text-blue-600" />
              Search & Discovery
            </h4>

            <div className="space-y-3">
              <div className="flex items-start">
                <div className="flex h-5 items-center">
                  <input
                    id="allowSearchByEmail"
                    name="allowSearchByEmail"
                    type="checkbox"
                    checked={formData.allowSearchByEmail}
                    onChange={handleInputChange}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="allowSearchByEmail" className="font-medium text-gray-700">
                    Allow search by email
                  </label>
                  <p className="text-gray-500">
                    Let others find you by searching for your email address.
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex h-5 items-center">
                  <input
                    id="allowSearchByPhone"
                    name="allowSearchByPhone"
                    type="checkbox"
                    checked={formData.allowSearchByPhone}
                    onChange={handleInputChange}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="allowSearchByPhone" className="font-medium text-gray-700">
                    Allow search by phone
                  </label>
                  <p className="text-gray-500">
                    Let others find you by searching for your phone number.
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex h-5 items-center">
                  <input
                    id="showOnlineStatus"
                    name="showOnlineStatus"
                    type="checkbox"
                    checked={formData.showOnlineStatus}
                    onChange={handleInputChange}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="showOnlineStatus" className="font-medium text-gray-700">
                    Show online status
                  </label>
                  <p className="text-gray-500">
                    Let others see when you're online or recently active.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="pt-6">
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </div>
      </form>
    </SettingsLayout>
  );
}
