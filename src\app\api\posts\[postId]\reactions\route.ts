import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { likes, users } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the post ID from the context params
    const params = await context.params;
    const { postId } = params;

    // Get URL search params for filtering
    const url = new URL(req.url);
    const type = url.searchParams.get('type'); // 'like', 'dislike', or null for all

    // Build the where condition
    let whereCondition = eq(likes.postId, postId);
    
    if (type === 'like') {
      whereCondition = and(eq(likes.postId, postId), eq(likes.type, 'like'));
    } else if (type === 'dislike') {
      whereCondition = and(eq(likes.postId, postId), eq(likes.type, 'dislike'));
    }

    // Get users who reacted to the post
    const reactions = await db
      .select({
        id: likes.id,
        type: likes.type,
        createdAt: likes.createdAt,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
      })
      .from(likes)
      .innerJoin(users, eq(likes.userId, users.id))
      .where(whereCondition)
      .orderBy(likes.createdAt);

    // Group reactions by type
    const groupedReactions = {
      likes: reactions.filter(r => r.type === 'like'),
      dislikes: reactions.filter(r => r.type === 'dislike'),
      total: reactions.length
    };

    return NextResponse.json({
      success: true,
      reactions: groupedReactions,
    });

  } catch (error) {
    console.error("Error fetching post reactions:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
