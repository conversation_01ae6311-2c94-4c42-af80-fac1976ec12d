"use client";

import React from 'react';
import {
  DocumentTextIcon,
  ClockIcon,
  EyeIcon,
  ChartBarIcon,
  BookOpenIcon,
  PencilIcon
} from '@heroicons/react/24/outline';

interface StatisticsPanelProps {
  content: string;
  isExpanded?: boolean;
  className?: string;
}

interface TextStatistics {
  wordCount: number;
  charCount: number;
  charCountNoSpaces: number;
  paragraphCount: number;
  sentenceCount: number;
  readingTime: number;
  speakingTime: number;
  averageWordsPerSentence: number;
  averageSentencesPerParagraph: number;
  readabilityScore: number;
  mostCommonWords: { word: string; count: number }[];
}

export const StatisticsPanel: React.FC<StatisticsPanelProps> = ({
  content,
  isExpanded = false,
  className = ""
}) => {
  const calculateStatistics = (text: string): TextStatistics => {
    if (!text.trim()) {
      return {
        wordCount: 0,
        charCount: 0,
        charCountNoSpaces: 0,
        paragraphCount: 0,
        sentenceCount: 0,
        readingTime: 0,
        speakingTime: 0,
        averageWordsPerSentence: 0,
        averageSentencesPerParagraph: 0,
        readabilityScore: 0,
        mostCommonWords: []
      };
    }

    // Basic counts
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    const charCount = text.length;
    const charCountNoSpaces = text.replace(/\s/g, '').length;
    
    // Paragraphs (split by double newlines or more)
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const paragraphCount = paragraphs.length;
    
    // Sentences (split by . ! ?)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const sentenceCount = sentences.length;
    
    // Reading and speaking time
    const readingTime = Math.ceil(wordCount / 200); // 200 words per minute
    const speakingTime = Math.ceil(wordCount / 150); // 150 words per minute
    
    // Averages
    const averageWordsPerSentence = sentenceCount > 0 ? Math.round(wordCount / sentenceCount) : 0;
    const averageSentencesPerParagraph = paragraphCount > 0 ? Math.round(sentenceCount / paragraphCount) : 0;
    
    // Simple readability score (Flesch Reading Ease approximation)
    const averageSentenceLength = sentenceCount > 0 ? wordCount / sentenceCount : 0;
    const averageSyllablesPerWord = 1.5; // Rough estimate
    const readabilityScore = Math.max(0, Math.min(100, 
      206.835 - (1.015 * averageSentenceLength) - (84.6 * averageSyllablesPerWord)
    ));
    
    // Most common words
    const wordFrequency: { [key: string]: number } = {};
    const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they']);
    
    words.forEach(word => {
      const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
      if (cleanWord.length > 2 && !commonWords.has(cleanWord)) {
        wordFrequency[cleanWord] = (wordFrequency[cleanWord] || 0) + 1;
      }
    });
    
    const mostCommonWords = Object.entries(wordFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word, count]) => ({ word, count }));

    return {
      wordCount,
      charCount,
      charCountNoSpaces,
      paragraphCount,
      sentenceCount,
      readingTime,
      speakingTime,
      averageWordsPerSentence,
      averageSentencesPerParagraph,
      readabilityScore,
      mostCommonWords
    };
  };

  const stats = calculateStatistics(content);

  const getReadabilityLevel = (score: number) => {
    if (score >= 90) return { level: 'Very Easy', color: 'text-green-600' };
    if (score >= 80) return { level: 'Easy', color: 'text-green-500' };
    if (score >= 70) return { level: 'Fairly Easy', color: 'text-yellow-500' };
    if (score >= 60) return { level: 'Standard', color: 'text-orange-500' };
    if (score >= 50) return { level: 'Fairly Difficult', color: 'text-red-500' };
    if (score >= 30) return { level: 'Difficult', color: 'text-red-600' };
    return { level: 'Very Difficult', color: 'text-red-700' };
  };

  const readability = getReadabilityLevel(stats.readabilityScore);

  if (!isExpanded) {
    // Compact view
    return (
      <div className={`flex items-center space-x-4 text-xs text-gray-500 ${className}`}>
        <span className="flex items-center">
          <DocumentTextIcon className="h-3 w-3 mr-1" />
          {stats.wordCount} words
        </span>
        <span>{stats.charCount} characters</span>
        <span className="flex items-center">
          <ClockIcon className="h-3 w-3 mr-1" />
          {stats.readingTime} min read
        </span>
      </div>
    );
  }

  // Expanded view
  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
      <h4 className="font-medium text-gray-700 mb-3 flex items-center">
        <ChartBarIcon className="h-4 w-4 mr-2" />
        Document Statistics
      </h4>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        {/* Basic Stats */}
        <div className="text-center p-3 bg-white rounded border">
          <DocumentTextIcon className="h-5 w-5 mx-auto text-blue-500 mb-1" />
          <div className="text-lg font-semibold text-gray-800">{stats.wordCount}</div>
          <div className="text-xs text-gray-500">Words</div>
        </div>
        
        <div className="text-center p-3 bg-white rounded border">
          <PencilIcon className="h-5 w-5 mx-auto text-green-500 mb-1" />
          <div className="text-lg font-semibold text-gray-800">{stats.charCount}</div>
          <div className="text-xs text-gray-500">Characters</div>
        </div>
        
        <div className="text-center p-3 bg-white rounded border">
          <ClockIcon className="h-5 w-5 mx-auto text-orange-500 mb-1" />
          <div className="text-lg font-semibold text-gray-800">{stats.readingTime}</div>
          <div className="text-xs text-gray-500">Min Read</div>
        </div>
        
        <div className="text-center p-3 bg-white rounded border">
          <BookOpenIcon className="h-5 w-5 mx-auto text-purple-500 mb-1" />
          <div className="text-lg font-semibold text-gray-800">{stats.paragraphCount}</div>
          <div className="text-xs text-gray-500">Paragraphs</div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Detailed Stats */}
        <div className="bg-white rounded border p-3">
          <h5 className="font-medium text-gray-700 mb-2">Structure</h5>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Sentences:</span>
              <span className="font-medium">{stats.sentenceCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Chars (no spaces):</span>
              <span className="font-medium">{stats.charCountNoSpaces}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Speaking time:</span>
              <span className="font-medium">{stats.speakingTime} min</span>
            </div>
          </div>
        </div>

        {/* Averages */}
        <div className="bg-white rounded border p-3">
          <h5 className="font-medium text-gray-700 mb-2">Averages</h5>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Words/sentence:</span>
              <span className="font-medium">{stats.averageWordsPerSentence}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Sentences/paragraph:</span>
              <span className="font-medium">{stats.averageSentencesPerParagraph}</span>
            </div>
          </div>
        </div>

        {/* Readability */}
        <div className="bg-white rounded border p-3">
          <h5 className="font-medium text-gray-700 mb-2">Readability</h5>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Level:</span>
            <span className={`font-medium ${readability.color}`}>
              {readability.level}
            </span>
          </div>
          <div className="mt-2">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Score: {Math.round(stats.readabilityScore)}</span>
              <span>100</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.max(0, Math.min(100, stats.readabilityScore))}%` }}
              />
            </div>
          </div>
        </div>

        {/* Most Common Words */}
        <div className="bg-white rounded border p-3">
          <h5 className="font-medium text-gray-700 mb-2">Top Words</h5>
          <div className="space-y-1">
            {stats.mostCommonWords.length > 0 ? (
              stats.mostCommonWords.map((item, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="text-gray-600 capitalize">{item.word}</span>
                  <span className="font-medium">{item.count}</span>
                </div>
              ))
            ) : (
              <p className="text-xs text-gray-500">No significant words found</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatisticsPanel;
