import { NextResponse } from "next/server";
import { validateApiSession } from "@/lib/utils/session-validation";

export async function GET() {
  try {
    const { response: authError, user } = await validateApiSession(false);

    if (authError) {
      return authError;
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        status: user.status,
        isActive: user.isActive,
        isAdmin: user.isAdmin,
        suspendedReason: user.suspendedReason,
        suspendedAt: user.suspendedAt,
        deletedReason: user.deletedReason,
        deletedAt: user.deletedAt
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Session status check error:", error);
    return NextResponse.json(
      {
        message: "Failed to check session status",
        code: "SESSION_CHECK_ERROR"
      },
      { status: 500 }
    );
  }
}
