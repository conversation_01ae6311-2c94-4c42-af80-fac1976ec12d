import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, users, groups, fanPages, events, blogs, subscriptions } from "@/lib/db/schema";
import { or, like, and, ne, eq, desc, sql } from "drizzle-orm";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const query = url.searchParams.get("q");
    const type = url.searchParams.get("type") || "all"; // all, posts, users, groups, pages, events, blogs
    const category = url.searchParams.get("category");
    const location = url.searchParams.get("location");
    const sortBy = url.searchParams.get("sortBy") || "relevance";
    const sortOrder = url.searchParams.get("sortOrder") || "desc";
    const limit = parseInt(url.searchParams.get("limit") || "10");

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        data: {
          posts: [],
          users: [],
          groups: [],
          fanPages: [],
          events: [],
          blogs: [],
          total: 0
        },
      });
    }

    const searchResults: any = {
      posts: [],
      users: [],
      groups: [],
      fanPages: [],
      events: [],
      blogs: [],
      total: 0
    };

    // Search Posts
    if (type === "all" || type === "posts") {
      try {
        // Get user's subscriptions for privacy filtering
        const userSubscriptions = await db.query.subscriptions.findMany({
          where: eq(subscriptions.subscriberId, session.user.id),
          columns: {
            targetUserId: true,
          },
        });

        const subscribedUserIds = userSubscriptions.map(sub => sub.targetUserId);

        const conditions = [
          or(
            like(posts.content, `%${query}%`),
            like(posts.location, `%${query}%`),
            like(posts.feeling, `%${query}%`),
            like(posts.activity, `%${query}%`)
          ),
          // Privacy filtering
          or(
            // Public posts from anyone
            eq(posts.privacy, 'public'),
            // User's own posts (all privacy levels)
            eq(posts.userId, session.user.id),
            // Subscribers-only posts from users the current user is subscribed to
            ...(subscribedUserIds.length > 0 ? [
              and(
                eq(posts.privacy, 'subscribers'),
                sql`${posts.userId} IN (${subscribedUserIds.map(id => `'${id}'`).join(',')})`
              )
            ] : [])
          )
        ];

        // Add location filter if provided
        if (location) {
          conditions.push(like(posts.location, `%${location}%`));
        }

        const postResults = await db.query.posts.findMany({
          where: and(...conditions),
          limit: type === "posts" ? limit : 5,
          orderBy: sortBy === "date" ? [desc(posts.createdAt)] : [desc(posts.createdAt)],
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
            likes: true,
            comments: {
              columns: {
                id: true,
              },
            },
            shares: true,
          },
        });

        searchResults.posts = postResults.map(post => ({
          ...post,
          type: 'user_post' as const,
          fanPage: null,
          group: null,
          createdAt: post.createdAt.toISOString(),
          _count: {
            likes: post.likes?.length || 0,
            dislikes: 0, // You might need to add dislikes table
            comments: post.comments?.length || 0,
            shares: post.shares?.length || 0,
          },
          liked: post.likes?.some(like => like.userId === session.user.id) || false,
          disliked: false, // You might need to add dislikes logic
        }));
      } catch (error) {
        console.error("Error searching posts:", error);
      }
    }

    // Search Users
    if (type === "all" || type === "users") {
      try {
        const userResults = await db.query.users.findMany({
          where: and(
            ne(users.id, session.user.id),
            or(
              like(users.name, `%${query}%`),
              like(users.username, `%${query}%`),
              like(users.email, `%${query}%`)
            )
          ),
          columns: {
            id: true,
            name: true,
            username: true,
            email: true,
            image: true,
            bio: true,
          },
          limit: type === "users" ? limit : 5,
        });

        searchResults.users = userResults;
      } catch (error) {
        console.error("Error searching users:", error);
      }
    }

    // Search Groups
    if (type === "all" || type === "groups") {
      try {
        const conditions = [
          or(
            like(groups.name, `%${query}%`),
            like(groups.description, `%${query}%`),
            like(groups.category, `%${query}%`)
          )
        ];

        // Add category filter if provided
        if (category) {
          conditions.push(like(groups.category, `%${category}%`));
        }

        const groupResults = await db.query.groups.findMany({
          where: and(...conditions),
          limit: type === "groups" ? limit : 5,
          orderBy: sortBy === "date" ? [desc(groups.createdAt)] : [desc(groups.createdAt)],
        });

        searchResults.groups = groupResults;
      } catch (error) {
        console.error("Error searching groups:", error);
      }
    }

    // Search Fan Pages
    if (type === "all" || type === "pages") {
      try {
        const conditions = [
          or(
            like(fanPages.name, `%${query}%`),
            like(fanPages.description, `%${query}%`),
            like(fanPages.category, `%${query}%`)
          )
        ];

        // Add category filter if provided
        if (category) {
          conditions.push(like(fanPages.category, `%${category}%`));
        }

        const pageResults = await db.query.fanPages.findMany({
          where: and(...conditions),
          limit: type === "pages" ? limit : 5,
          orderBy: sortBy === "date" ? [desc(fanPages.createdAt)] : [desc(fanPages.createdAt)],
          columns: {
            id: true,
            name: true,
            username: true,
            description: true,
            category: true,
            profileImage: true,
            coverImage: true,
            createdAt: true,
          },
        });

        searchResults.fanPages = pageResults;
      } catch (error) {
        console.error("Error searching fan pages:", error);
      }
    }

    // Search Events
    if (type === "all" || type === "events") {
      try {
        const conditions = [
          or(
            like(events.name, `%${query}%`),
            like(events.description, `%${query}%`),
            like(events.location, `%${query}%`),
            like(events.category, `%${query}%`)
          )
        ];

        // Add category filter if provided
        if (category) {
          conditions.push(like(events.category, `%${category}%`));
        }

        // Add location filter if provided
        if (location) {
          conditions.push(like(events.location, `%${location}%`));
        }

        const eventResults = await db.query.events.findMany({
          where: and(...conditions),
          limit: type === "events" ? limit : 5,
          orderBy: sortBy === "date" ? [desc(events.startTime)] : [desc(events.startTime)],
        });

        searchResults.events = eventResults;
      } catch (error) {
        console.error("Error searching events:", error);
      }
    }

    // Search Blogs
    if (type === "all" || type === "blogs") {
      try {
        const conditions = [
          eq(blogs.status, "published"),
          or(
            like(blogs.title, `%${query}%`),
            like(blogs.excerpt, `%${query}%`),
            like(blogs.content, `%${query}%`)
          )
        ];

        const blogResults = await db.query.blogs.findMany({
          where: and(...conditions),
          limit: type === "blogs" ? limit : 5,
          orderBy: sortBy === "date" ? [desc(blogs.createdAt)] : [desc(blogs.createdAt)],
          with: {
            author: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
            category: {
              columns: {
                id: true,
                name: true,
                color: true,
              },
            },
          },
        });

        searchResults.blogs = blogResults;
      } catch (error) {
        console.error("Error searching blogs:", error);
      }
    }

    // Calculate total results
    searchResults.total =
      searchResults.posts.length +
      searchResults.users.length +
      searchResults.groups.length +
      searchResults.fanPages.length +
      searchResults.events.length +
      searchResults.blogs.length;

    return NextResponse.json({
      success: true,
      data: searchResults,
      query,
      type,
    });

  } catch (error: any) {
    console.error("Error in global search:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to perform search"
      },
      { status: 500 }
    );
  }
}
