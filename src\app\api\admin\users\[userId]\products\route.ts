import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { products, users, stores } from "@/lib/db/schema";
import { eq, desc, count } from "drizzle-orm";

// Get user's products
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user's products with store data
    const userProducts = await db.select({
      id: products.id,
      name: products.title, // Map title to name for frontend
      title: products.title,
      description: products.description,
      price: products.price,
      condition: products.item_condition,
      status: products.item_condition, // Map condition to status for frontend
      category: products.category,
      location: products.location,
      photos: products.photos,
      images: products.photos, // Map photos to images for frontend
      viewCount: products.viewCount,
      views: products.viewCount, // Map viewCount to views for frontend
      createdAt: products.createdAt,
      updatedAt: products.updatedAt,
      // Store data
      storeName: stores.name,
      storeSlug: stores.slug,
    })
    .from(products)
    .leftJoin(stores, eq(products.storeId, stores.id))
    .where(eq(stores.ownerId, userId))
    .orderBy(desc(products.createdAt))
    .limit(limit)
    .offset(offset);

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(products)
      .leftJoin(stores, eq(products.storeId, stores.id))
      .where(eq(stores.ownerId, userId));

    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      products: userProducts,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user products:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
