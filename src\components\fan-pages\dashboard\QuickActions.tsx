"use client";

import { But<PERSON> } from "@/components/ui/Button";
import {
  PlusIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  UsersIcon,
  PhotoIcon,
  PencilIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface FanPage {
  id: string;
  name: string;
  username: string;
  description: string | null;
  profileImage: string | null;
  coverImage: string | null;
  category: string;
  isVerified: boolean;
  followerCount: number;
  postCount: number;
}

interface QuickActionsProps {
  page: FanPage;
}

export function QuickActions({ page }: QuickActionsProps) {
  const actions = [
    {
      title: "Create Post",
      description: "Share content with your followers",
      icon: <PlusIcon className="h-6 w-6" />,
      href: `/pages/${page.username}?tab=posts&action=create`,
      color: "bg-blue-600 hover:bg-blue-700",
    },
    {
      title: "View Messages",
      description: "Check and respond to messages",
      icon: <ChatBubbleLeftRightIcon className="h-6 w-6" />,
      href: "/messages?tab=fanpage",
      color: "bg-green-600 hover:bg-green-700",
    },
    {
      title: "Manage Followers",
      description: "View and manage your followers",
      icon: <UsersIcon className="h-6 w-6" />,
      href: `/pages/${page.username}?tab=followers`,
      color: "bg-purple-600 hover:bg-purple-700",
    },
    {
      title: "Edit Page",
      description: "Update page information and details",
      icon: <PencilIcon className="h-6 w-6" />,
      href: `/pages/${page.username}/edit`,
      color: "bg-orange-600 hover:bg-orange-700",
    },
    {
      title: "Page Settings",
      description: "Advanced settings and preferences",
      icon: <Cog6ToothIcon className="h-6 w-6" />,
      href: `/pages/${page.username}?tab=settings`,
      color: "bg-gray-600 hover:bg-gray-700",
    },
    {
      title: "View Analytics",
      description: "Detailed insights and analytics",
      icon: <ChartBarIcon className="h-6 w-6" />,
      href: `/pages/${page.username}/dashboard?tab=analytics`,
      color: "bg-indigo-600 hover:bg-indigo-700",
    },
    {
      title: "Manage Photos",
      description: "Upload and organize photos",
      icon: <PhotoIcon className="h-6 w-6" />,
      href: `/pages/${page.username}?tab=photos`,
      color: "bg-pink-600 hover:bg-pink-700",
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {actions.map((action, index) => (
          <Link key={index} href={action.href}>
            <div className="group p-4 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 cursor-pointer">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg text-white ${action.color} group-hover:scale-110 transition-transform duration-200`}>
                  {action.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                    {action.title}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1">{action.description}</p>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
