"use client";

import { Fragment, useState, useRef } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon, PhotoIcon, VideoCameraIcon, FaceSmileIcon, MapPinIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { useDropzone } from "react-dropzone";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";
import { toast } from "react-hot-toast";
import EmojiPicker from 'emoji-picker-react';

interface FanPagePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPostCreated: () => void;
  pageId: string;
  pageName: string;
  pageImage?: string | null;
}

export function FanPagePostModal({
  isOpen,
  onClose,
  onPostCreated,
  pageId,
  pageName,
  pageImage,
}: FanPagePostModalProps) {
  const [content, setContent] = useState("");
  const [images, setImages] = useState<File[]>([]);
  const [videos, setVideos] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [feeling, setFeeling] = useState("");
  const [location, setLocation] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { getRootProps: getImageRootProps, getInputProps: getImageInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif", ".webp"],
    },
    maxFiles: 5,
    onDrop: (acceptedFiles) => {
      setImages((prev) => [...prev, ...acceptedFiles].slice(0, 5));
    },
  });

  const { getRootProps: getVideoRootProps, getInputProps: getVideoInputProps } = useDropzone({
    accept: {
      "video/*": [".mp4", ".webm", ".ogg", ".mov"],
    },
    maxFiles: 2,
    onDrop: (acceptedFiles) => {
      setVideos((prev) => [...prev, ...acceptedFiles].slice(0, 2));
    },
  });

  const handleEmojiClick = (emojiData: any) => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      const newContent = content.substring(0, start) + emojiData.emoji + content.substring(end);
      setContent(newContent);

      // Focus back on textarea and set cursor position after the inserted emoji
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
          const newCursorPos = start + emojiData.emoji.length;
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        }
      }, 10);
    }
    setShowEmojiPicker(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!content.trim() && images.length === 0 && videos.length === 0) {
      toast.error("Please add some content to your post");
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload images and videos to Cloudinary
      const imageUrls: string[] = [];
      const videoUrls: string[] = [];

      if (images.length > 0) {
        const uploadedImageUrls = await uploadMultipleToCloudinary(images);
        imageUrls.push(...uploadedImageUrls);
      }

      if (videos.length > 0) {
        const uploadedVideoUrls = await uploadMultipleToCloudinary(videos);
        videoUrls.push(...uploadedVideoUrls);
      }

      // Determine post type
      let postType = 'text';
      if (imageUrls.length > 0) postType = 'image';
      else if (videoUrls.length > 0) postType = 'video';

      const response = await fetch(`/api/fan-pages/${pageId}/posts`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: content.trim(),
          images: imageUrls,
          videos: videoUrls,
          type: postType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create post");
      }

      // Reset form
      setContent("");
      setImages([]);
      setVideos([]);
      setFeeling("");
      setLocation("");
      setShowEmojiPicker(false);

      toast.success("Post created successfully!");
      onPostCreated();
      onClose();

    } catch (error) {
      console.error("Error creating post:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create post");
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeImage = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
  };

  const removeVideo = (index: number) => {
    setVideos((prev) => prev.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setContent("");
      setImages([]);
      setVideos([]);
      setFeeling("");
      setLocation("");
      setShowEmojiPicker(false);
      onClose();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <Dialog.Title className="text-lg font-semibold text-gray-900">
                    Create Post
                  </Dialog.Title>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    disabled={isSubmitting}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit}>
                  {/* Page Info */}
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 flex-shrink-0 rounded-full overflow-hidden">
                        {pageImage ? (
                          <OptimizedImage
                            src={pageImage}
                            alt={pageName}
                            width={40}
                            height={40}
                            className="rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                            {pageName.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{pageName}</h3>
                        <p className="text-sm text-gray-500">Posting to page</p>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <textarea
                      ref={textareaRef}
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder={`What would you like to share on ${pageName}?`}
                      className="w-full min-h-[120px] p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={isSubmitting}
                    />

                    {/* Feeling/Activity */}
                    {(feeling || location) && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {feeling && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800">
                            😊 {feeling}
                            <button
                              type="button"
                              onClick={() => setFeeling("")}
                              className="ml-2 text-yellow-600 hover:text-yellow-800"
                            >
                              ×
                            </button>
                          </span>
                        )}
                        {location && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                            📍 {location}
                            <button
                              type="button"
                              onClick={() => setLocation("")}
                              className="ml-2 text-green-600 hover:text-green-800"
                            >
                              ×
                            </button>
                          </span>
                        )}
                      </div>
                    )}

                    {/* Image Previews */}
                    {images.length > 0 && (
                      <div className="mt-4">
                        <div className="grid grid-cols-2 gap-2">
                          {images.map((image, index) => (
                            <div key={index} className="relative">
                              <img
                                src={URL.createObjectURL(image)}
                                alt={`Preview ${index + 1}`}
                                className="w-full h-32 object-cover rounded-lg"
                              />
                              <button
                                type="button"
                                onClick={() => removeImage(index)}
                                className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                disabled={isSubmitting}
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Video Previews */}
                    {videos.length > 0 && (
                      <div className="mt-4">
                        <div className="space-y-2">
                          {videos.map((video, index) => (
                            <div key={index} className="relative">
                              <video
                                src={URL.createObjectURL(video)}
                                className="w-full h-48 object-cover rounded-lg"
                                controls
                              />
                              <button
                                type="button"
                                onClick={() => removeVideo(index)}
                                className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                disabled={isSubmitting}
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Media Upload Areas */}
                    <div className="mt-4 grid grid-cols-2 gap-4">
                      {/* Image Upload */}
                      <div {...getImageRootProps()} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors cursor-pointer">
                        <input {...getImageInputProps()} disabled={isSubmitting} />
                        <PhotoIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                        <p className="text-sm text-gray-500">Add Photos</p>
                        <p className="text-xs text-gray-400">Max 5 images</p>
                      </div>

                      {/* Video Upload */}
                      <div {...getVideoRootProps()} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors cursor-pointer">
                        <input {...getVideoInputProps()} disabled={isSubmitting} />
                        <VideoCameraIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                        <p className="text-sm text-gray-500">Add Videos</p>
                        <p className="text-xs text-gray-400">Max 2 videos</p>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="mt-4 flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <button
                          type="button"
                          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
                          disabled={isSubmitting}
                        >
                          <FaceSmileIcon className="h-5 w-5" />
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            const newLocation = prompt("Where are you?");
                            if (newLocation) setLocation(newLocation);
                          }}
                          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
                          disabled={isSubmitting}
                        >
                          <MapPinIcon className="h-5 w-5" />
                        </button>
                      </div>

                      <div className="text-sm text-gray-500">
                        {content.length}/5000
                      </div>
                    </div>

                    {/* Emoji Picker */}
                    {showEmojiPicker && (
                      <div className="mt-4">
                        <EmojiPicker
                          onEmojiClick={handleEmojiClick}
                          width="100%"
                          height={300}
                        />
                      </div>
                    )}
                  </div>

                  {/* Footer */}
                  <div className="p-6 border-t border-gray-200">
                    <div className="flex justify-end space-x-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleClose}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={isSubmitting || (!content.trim() && images.length === 0 && videos.length === 0)}
                        className="min-w-[100px]"
                      >
                        {isSubmitting ? (
                          <div className="flex items-center space-x-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Posting...</span>
                          </div>
                        ) : (
                          "Post"
                        )}
                      </Button>
                    </div>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
