import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { stores, products, storeFollows, storeReviews } from "@/lib/db/schema";
import { eq, and, count, avg, desc } from "drizzle-orm";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { StoreDashboard } from "@/components/marketplace/StoreDashboard";
import { PlusIcon } from "@heroicons/react/24/outline";

export default async function MyStoreDashboardPage() {
  const user = await requireAuth();

  // Check if user has a store
  const userStore = await db
    .select({
      id: stores.id,
      name: stores.name,
      slug: stores.slug,
      description: stores.description,
      logo: stores.logo,
      banner: stores.banner,
      location: stores.location,
      isVerified: stores.isVerified,
      createdAt: stores.createdAt,
    })
    .from(stores)
    .where(eq(stores.ownerId, user.id))
    .limit(1);

  // If user doesn't have a store, redirect to create store page
  if (userStore.length === 0) {
    redirect("/marketplace/create-store");
  }

  const store = userStore[0];

  // Get store stats
  const productCountResult = await db
    .select({ count: count() })
    .from(products)
    .where(eq(products.storeId, store.id));

  const followerCountResult = await db
    .select({ count: count() })
    .from(storeFollows)
    .where(eq(storeFollows.storeId, store.id));

  const reviewCountResult = await db
    .select({ count: count() })
    .from(storeReviews)
    .where(eq(storeReviews.storeId, store.id));

  const ratingResult = await db
    .select({ averageRating: avg(storeReviews.rating) })
    .from(storeReviews)
    .where(eq(storeReviews.storeId, store.id));

  // Get latest products
  const latestProducts = await db
    .select({
      id: products.id,
      title: products.title,
      price: products.price,
      condition: products.item_condition,
      photos: products.photos,
      viewCount: products.viewCount,
      createdAt: products.createdAt,
    })
    .from(products)
    .where(eq(products.storeId, store.id))
    .orderBy(desc(products.createdAt))
    .limit(5);

  const storeData = {
    ...store,
    productCount: productCountResult[0]?.count || 0,
    followerCount: followerCountResult[0]?.count || 0,
    reviewCount: reviewCountResult[0]?.count || 0,
    averageRating: ratingResult[0]?.averageRating || 0,
    products: latestProducts,
  };

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Store Dashboard
          </h1>
          <div className="flex flex-wrap gap-2">
            <Link href={`/store/${store.slug}`}>
              <Button variant="outline">View Public Store</Button>
            </Link>
            <Link href="/marketplace/product/create">
              <Button>
                <PlusIcon className="mr-1 h-5 w-5" />
                Add Product
              </Button>
            </Link>
          </div>
        </div>

        <StoreDashboard store={storeData} />
      </div>
    </MainLayout>
  );
}
