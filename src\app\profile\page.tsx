"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { Spinner } from "@/components/ui/Spinner";

export default function ProfilePage() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const router = useRouter();

  useEffect(() => {
    async function fetchUserProfile() {
      if (!session?.user) return;

      try {
        setIsLoading(true);
        const response = await fetch(`/api/users/${session.user.id}`);

        if (response.ok) {
          const data = await response.json();
          console.log('Fetched user data:', data);
          setUserData(data);

          // Redirect to username-based profile page
          if (data.username) {
            // Use replace instead of push to avoid adding to history stack
            router.replace(`/user/${data.username}`);
          } else {
            console.error('User does not have a username');
            setIsLoading(false);
          }
        } else {
          console.error('Failed to fetch user profile:', response.status);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        setIsLoading(false);
      }
    }

    fetchUserProfile();
  }, [session, router]);

  if (!session?.user) {
    return null; // Or a loading state
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-[calc(100vh-5rem)]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  // If we get here, we're still loading or redirecting
  return (
    <MainLayout>
      <div className="flex justify-center items-center h-[calc(100vh-5rem)]">
        <Spinner />
        <p className="ml-3 text-gray-600">Redirecting to your profile...</p>
      </div>
    </MainLayout>
  );
}
