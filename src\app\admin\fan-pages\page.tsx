"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckBadgeIcon,
  XMarkIcon,
  ChartBarIcon,
  StarIcon,
  PlusIcon,
  UsersIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { CheckBadgeIcon as CheckBadgeIconSolid } from "@heroicons/react/24/solid";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface FanPage {
  id: string;
  name: string;
  username: string;
  category: string;
  description: string | null;
  profileImage: string | null;
  coverImage: string | null;
  isVerified: boolean;
  isActive: boolean;
  followerCount: number;
  postCount: number;
  createdAt: string;
  owner: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface FanPageStats {
  totalPages: number;
  activePages: number;
  verifiedPages: number;
  newPagesThisMonth: number;
}

export default function AdminFanPagesPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [fanPages, setFanPages] = useState<FanPage[]>([]);
  const [stats, setStats] = useState<FanPageStats>({
    totalPages: 0,
    activePages: 0,
    verifiedPages: 0,
    newPagesThisMonth: 0,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [isActionLoading, setIsActionLoading] = useState(false);

  const categories = [
    { value: "all", label: "All Categories" },
    { value: "musician", label: "Musician" },
    { value: "actor", label: "Actor" },
    { value: "brand", label: "Brand" },
    { value: "business", label: "Business" },
    { value: "organization", label: "Organization" },
    { value: "public_figure", label: "Public Figure" },
    { value: "artist", label: "Artist" },
    { value: "writer", label: "Writer" },
    { value: "athlete", label: "Athlete" },
    { value: "politician", label: "Politician" },
    { value: "entertainment", label: "Entertainment" },
    { value: "media", label: "Media" },
    { value: "community", label: "Community" },
    { value: "cause", label: "Cause" },
    { value: "other", label: "Other" },
  ];

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
    { value: "verified", label: "Verified" },
    { value: "unverified", label: "Unverified" },
  ];

  useEffect(() => {
    fetchFanPages();
    fetchStats();
  }, [currentPage, searchTerm, selectedCategory, selectedStatus]);

  const fetchFanPages = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
        search: searchTerm,
        category: selectedCategory,
        status: selectedStatus,
      });

      const response = await fetch(`/api/admin/fan-pages?${params}`);
      if (!response.ok) throw new Error("Failed to fetch fan pages");

      const data = await response.json();
      setFanPages(data.fanPages || []);
    } catch (error) {
      console.error("Error fetching fan pages:", error);
      toast.error("Failed to load fan pages");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/admin/fan-pages/stats");
      if (!response.ok) throw new Error("Failed to fetch stats");

      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  const handleVerifyPage = async (pageId: string, verify: boolean) => {
    try {
      setIsActionLoading(true);
      const response = await fetch(`/api/admin/fan-pages/${pageId}/verify`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isVerified: verify }),
      });

      if (!response.ok) throw new Error("Failed to update verification");

      toast.success(`Page ${verify ? "verified" : "unverified"} successfully`);
      fetchFanPages();
      fetchStats();
    } catch (error) {
      console.error("Error updating verification:", error);
      toast.error("Failed to update verification");
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleToggleStatus = async (pageId: string, isActive: boolean) => {
    try {
      setIsActionLoading(true);
      const response = await fetch(`/api/admin/fan-pages/${pageId}/status`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) throw new Error("Failed to update status");

      toast.success(`Page ${isActive ? "activated" : "deactivated"} successfully`);
      fetchFanPages();
      fetchStats();
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleDeletePage = async (pageId: string) => {
    if (!confirm("Are you sure you want to delete this fan page? This action cannot be undone.")) {
      return;
    }

    try {
      setIsActionLoading(true);
      const response = await fetch(`/api/admin/fan-pages/${pageId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete page");

      toast.success("Fan page deleted successfully");
      fetchFanPages();
      fetchStats();
    } catch (error) {
      console.error("Error deleting page:", error);
      toast.error("Failed to delete page");
    } finally {
      setIsActionLoading(false);
    }
  };

  const filteredPages = fanPages.filter((page) => {
    const matchesSearch = page.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         page.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         page.owner.name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategory === "all" || page.category === selectedCategory;

    const matchesStatus = selectedStatus === "all" ||
                         (selectedStatus === "active" && page.isActive) ||
                         (selectedStatus === "inactive" && !page.isActive) ||
                         (selectedStatus === "verified" && page.isVerified) ||
                         (selectedStatus === "unverified" && !page.isVerified);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const statCards = [
    {
      title: "Total Fan Pages",
      value: stats.totalPages.toLocaleString(),
      icon: <StarIcon className="h-6 w-6 text-white" />,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      change: { value: stats.newPagesThisMonth, isPositive: true },
    },
    {
      title: "Active Pages",
      value: stats.activePages.toLocaleString(),
      icon: <CheckBadgeIcon className="h-6 w-6 text-white" />,
      color: "text-green-600",
      bgColor: "bg-green-100",
      change: { value: Math.round((stats.activePages / stats.totalPages) * 100) || 0, isPositive: true },
    },
    {
      title: "Verified Pages",
      value: stats.verifiedPages.toLocaleString(),
      icon: <CheckBadgeIconSolid className="h-6 w-6 text-white" />,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      change: { value: Math.round((stats.verifiedPages / stats.totalPages) * 100) || 0, isPositive: true },
    },
    {
      title: "New This Month",
      value: stats.newPagesThisMonth.toLocaleString(),
      icon: <PlusIcon className="h-6 w-6 text-white" />,
      color: "text-pink-600",
      bgColor: "bg-pink-100",
      change: { value: 25, isPositive: true },
    },
  ];

  if (isLoading && fanPages.length === 0) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Fan Page Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage fan pages, verification, and analytics
            </p>
          </div>
          <div className="mt-4 flex space-x-2 sm:mt-0">
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="outline"
            >
              <FunnelIcon className="mr-2 h-5 w-5" />
              Filters
            </Button>
            <Button onClick={() => router.push("/admin/fan-pages/analytics")}>
              <ChartBarIcon className="mr-2 h-5 w-5" />
              Analytics
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {statCards.map((card, index) => (
            <div
              key={index}
              className="relative overflow-hidden rounded-lg bg-white p-6 shadow-md"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`rounded-md p-3 ${card.bgColor}`}>
                    {card.icon}
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">
                      {card.title}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className={`text-2xl font-semibold ${card.color}`}>
                        {card.value}
                      </div>
                      <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                        <span>+{card.change.value}%</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="rounded-lg bg-white p-6 shadow-md">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Search
                </label>
                <Input
                  type="text"
                  placeholder="Search pages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  {categories.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  {statusOptions.map((status) => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex items-end">
                <Button
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedCategory("all");
                    setSelectedStatus("all");
                  }}
                  variant="outline"
                  className="w-full"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Fan Pages Table */}
        <div className="rounded-lg bg-white shadow-md">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Fan Pages ({filteredPages.length})
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Page
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Owner
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Stats
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Created
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {filteredPages.map((page) => (
                  <tr key={page.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <OptimizedImage
                            src={page.profileImage || "/images/default-avatar.png"}
                            alt={page.name}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded-full object-cover"
                          />
                        </div>
                        <div className="ml-4">
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">
                              {page.name}
                            </div>
                            {page.isVerified && (
                              <CheckBadgeIconSolid className="ml-1 h-4 w-4 text-blue-500" />
                            )}
                          </div>
                          <div className="text-sm text-gray-500">@{page.username}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 flex-shrink-0">
                          <OptimizedImage
                            src={page.owner.image || "/images/default-avatar.png"}
                            alt={page.owner.name || "Owner"}
                            width={32}
                            height={32}
                            className="h-8 w-8 rounded-full object-cover"
                          />
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {page.owner.name || "Unknown"}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="outline">
                        {page.category.replace("_", " ").toUpperCase()}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <UsersIcon className="h-4 w-4 text-gray-400 mr-1" />
                          {page.followerCount.toLocaleString()}
                        </div>
                        <div className="flex items-center">
                          <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-1" />
                          {page.postCount.toLocaleString()}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <Badge variant={page.isActive ? "success" : "danger"}>
                          {page.isActive ? "Active" : "Inactive"}
                        </Badge>
                        {page.isVerified && (
                          <Badge variant="primary">Verified</Badge>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(page.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link
                          href={`/admin/fan-pages/${page.id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>
                        <button
                          onClick={() => handleVerifyPage(page.id, !page.isVerified)}
                          className={`${
                            page.isVerified
                              ? "text-red-600 hover:text-red-900"
                              : "text-green-600 hover:text-green-900"
                          }`}
                          disabled={isActionLoading}
                        >
                          {page.isVerified ? (
                            <XMarkIcon className="h-4 w-4" />
                          ) : (
                            <CheckBadgeIcon className="h-4 w-4" />
                          )}
                        </button>
                        <button
                          onClick={() => handleToggleStatus(page.id, !page.isActive)}
                          className={`${
                            page.isActive
                              ? "text-red-600 hover:text-red-900"
                              : "text-green-600 hover:text-green-900"
                          }`}
                          disabled={isActionLoading}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeletePage(page.id)}
                          className="text-red-600 hover:text-red-900"
                          disabled={isActionLoading}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredPages.length === 0 && !isLoading && (
            <div className="py-12 text-center">
              <StarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No fan pages found</h3>
              <p className="mt-1 text-sm text-gray-500">
                No fan pages match your current filters.
              </p>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
