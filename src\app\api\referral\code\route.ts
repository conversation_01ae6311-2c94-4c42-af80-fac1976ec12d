import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { ReferralService } from "@/lib/referral/referralService";
import { generateReferralUrl } from "@/lib/utils/url";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get or create referral code for user
    const referralCode = await ReferralService.getOrCreateReferralCode(session.user.id);
    const referralLink = generateReferralUrl(referralCode, request);

    return NextResponse.json({
      success: true,
      data: {
        code: referralCode,
        link: referralLink,
      },
    });

  } catch (error) {
    console.error("Error fetching referral code:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch referral code"
      },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // For now, we'll just return the existing code
    // In the future, we could implement code regeneration
    const referralCode = await ReferralService.getOrCreateReferralCode(session.user.id);
    const referralLink = generateReferralUrl(referralCode, request);

    return NextResponse.json({
      success: true,
      message: "Referral code generated successfully",
      data: {
        code: referralCode,
        link: referralLink,
      },
    });

  } catch (error) {
    console.error("Error generating referral code:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to generate referral code" 
      },
      { status: 500 }
    );
  }
}
