"use client";

import { useState } from "react";
import { PostCard } from "@/components/feed/PostCard";
import { formatTimeAgo, formatMemoryDate } from "@/lib/utils";
import { PostTime } from "@/components/ui/TimeDisplay";

interface Memory {
  id: string;
  content: string | null;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  type: 'user_post' | 'fan_page_post' | 'group_post';
  yearsAgo?: number;
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  } | null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  } | null;
  group: {
    id: string;
    name: string;
    slug: string;
    profileImage: string | null;
    isPrivate: boolean;
  } | null;
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
  sharedPost?: {
    id: string;
    content: string;
    images: string[] | null;
    videos: string[] | null;
    backgroundColor: string | null;
    feeling: string | null;
    activity: string | null;
    location: string | null;
    formattedContent: boolean | null;
    createdAt: string;
    user: {
      id: string;
      name: string;
      username?: string | null;
      image: string | null;
    };
  } | null;
}

interface MemoryCardProps {
  memory: Memory;
  type: "onThisDay" | "recent";
}

export function MemoryCard({ memory, type }: MemoryCardProps) {
  const [liked, setLiked] = useState(memory.liked);
  const [disliked, setDisliked] = useState(memory.disliked);

  const handleLike = async (postId: string) => {
    try {
      const response = await fetch(`/api/posts/${postId}/like`, {
        method: "POST",
      });

      if (response.ok) {
        setLiked(true);
        setDisliked(false);
      }
    } catch (error) {
      console.error("Error liking post:", error);
    }
  };

  const handleDislike = async (postId: string) => {
    try {
      const response = await fetch(`/api/posts/${postId}/dislike`, {
        method: "POST",
      });

      if (response.ok) {
        setDisliked(true);
        setLiked(false);
      }
    } catch (error) {
      console.error("Error disliking post:", error);
    }
  };

  const memoryDate = new Date(memory.createdAt);
  const formattedDate = formatMemoryDate(memoryDate);

  return (
    <div className={`${type === 'recent' ? 'h-full' : ''} flex flex-col`}>
      {/* Memory Badge */}
      <div className={`mb-3 inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold ${
        type === "onThisDay"
          ? "bg-gradient-to-r from-amber-100 to-orange-100 text-amber-700 border border-amber-200"
          : "bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border border-green-200"
      }`}>
        {type === "onThisDay" ? (
          <>
            <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {memory.yearsAgo === 1 ? "1 year ago" : `${memory.yearsAgo} years ago`} • {formattedDate}
          </>
        ) : (
          <>
            <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <PostTime date={memoryDate} autoUpdate={true} /> • {formattedDate}
          </>
        )}
      </div>

      {/* Post Card with uniform height for recent memories */}
      <div className={`${type === 'recent' ? 'flex-1' : ''} rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300`}>
        <PostCard
          post={memory}
          onLike={() => handleLike(memory.id)}
          onDislike={() => handleDislike(memory.id)}
        />
      </div>
    </div>
  );
}
