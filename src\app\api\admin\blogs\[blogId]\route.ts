import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogComments, blogLikes } from "@/lib/db/schema";
import { z } from "zod";
import { eq } from "drizzle-orm";

const blogUpdateSchema = z.object({
  title: z.string().min(1).max(500).optional(),
  content: z.string().min(1).optional(),
  excerpt: z.string().max(1000).optional(),
  coverImage: z.string().url().optional().nullable().or(z.literal("")),
  categoryId: z.string().optional().nullable(),
  tags: z.array(z.string()).optional(),
  status: z.enum(["draft", "published", "archived"]).optional(),
  readTime: z.number().optional(),
  featured: z.boolean().optional(),
  seoTitle: z.string().max(255).optional(),
  seoDescription: z.string().max(1000).optional(),
});

// Get a single blog with admin details
export async function GET(
  req: Request,
  context: { params: Promise<{ blogId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { blogId } = params;

    // Fetch the blog from the database
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.id, blogId),
      with: {
        author: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
            email: true,
          },
        },
        category: true,
        likes: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        comments: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
          orderBy: (comments, { desc }) => [desc(comments.createdAt)],
        },
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Format response with counts and admin-specific data
    const formattedBlog = {
      ...blog,
      _count: {
        likes: blog.likes.length,
        comments: blog.comments.length,
      },
      _admin: {
        totalViews: blog.viewCount || 0,
        createdAt: blog.createdAt,
        updatedAt: blog.updatedAt,
        publishedAt: blog.publishedAt,
      },
    };

    return NextResponse.json(formattedBlog);
  } catch (error) {
    console.error("Error fetching blog:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a blog
export async function PUT(
  req: Request,
  context: { params: Promise<{ blogId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { blogId } = params;
    const body = await req.json();
    const validatedData = blogUpdateSchema.parse(body);

    // Check if blog exists
    const existingBlog = await db.query.blogs.findFirst({
      where: eq(blogs.id, blogId),
      columns: { id: true, status: true, slug: true, title: true },
    });

    if (!existingBlog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (validatedData.title !== undefined) {
      updateData.title = validatedData.title;

      // Update slug if title changed
      if (validatedData.title !== existingBlog.title) {
        const newSlug = validatedData.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/^-|-$/g, "");

        // Check if new slug already exists
        const slugExists = await db.query.blogs.findFirst({
          where: eq(blogs.slug, newSlug),
        });

        if (slugExists && slugExists.id !== blogId) {
          updateData.slug = `${newSlug}-${Date.now()}`;
        } else {
          updateData.slug = newSlug;
        }
      }
    }

    if (validatedData.content !== undefined) {
      updateData.content = validatedData.content;

      // Recalculate read time if content changed
      if (validatedData.readTime === undefined) {
        const wordCount = validatedData.content.split(/\s+/).length;
        updateData.readTime = Math.ceil(wordCount / 200);
      }
    }

    if (validatedData.excerpt !== undefined) updateData.excerpt = validatedData.excerpt;
    if (validatedData.coverImage !== undefined) {
      updateData.coverImage = validatedData.coverImage === "" ? null : validatedData.coverImage;
    }
    if (validatedData.categoryId !== undefined) updateData.categoryId = validatedData.categoryId;
    if (validatedData.tags !== undefined) updateData.tags = validatedData.tags;
    if (validatedData.readTime !== undefined) updateData.readTime = validatedData.readTime;
    if (validatedData.featured !== undefined) updateData.featured = validatedData.featured;
    if (validatedData.seoTitle !== undefined) updateData.seoTitle = validatedData.seoTitle;
    if (validatedData.seoDescription !== undefined) updateData.seoDescription = validatedData.seoDescription;

    // Handle status change
    if (validatedData.status !== undefined) {
      updateData.status = validatedData.status;

      // Set publishedAt when publishing for the first time
      if (validatedData.status === "published" && existingBlog.status !== "published") {
        updateData.publishedAt = new Date();
      }

      // Clear publishedAt when unpublishing
      if (validatedData.status !== "published" && existingBlog.status === "published") {
        updateData.publishedAt = null;
      }
    }

    // Update the blog
    await db.update(blogs)
      .set(updateData)
      .where(eq(blogs.id, blogId));

    return NextResponse.json({
      message: "Blog updated successfully",
      slug: updateData.slug || existingBlog.slug,
    });
  } catch (error) {
    console.error("Error updating blog:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a blog
export async function DELETE(
  req: Request,
  context: { params: Promise<{ blogId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { blogId } = params;

    // Check if blog exists
    const existingBlog = await db.query.blogs.findFirst({
      where: eq(blogs.id, blogId),
      columns: { id: true },
    });

    if (!existingBlog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Delete related data first (comments, likes)
    await db.delete(blogComments).where(eq(blogComments.blogId, blogId));
    await db.delete(blogLikes).where(eq(blogLikes.blogId, blogId));

    // Delete the blog
    await db.delete(blogs).where(eq(blogs.id, blogId));

    return NextResponse.json({
      message: "Blog deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting blog:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
