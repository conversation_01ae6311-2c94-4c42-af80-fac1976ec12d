"use client";

import { CheckBadgeIcon } from "@heroicons/react/24/solid";

interface VerifiedBadgeProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showTooltip?: boolean;
}

export function VerifiedBadge({ 
  size = 'sm', 
  className = '',
  showTooltip = true 
}: VerifiedBadgeProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5', 
    lg: 'h-6 w-6'
  };

  return (
    <div className={`relative inline-flex ${className}`}>
      <CheckBadgeIcon 
        className={`${sizeClasses[size]} text-blue-500`}
        title={showTooltip ? "Verified User" : undefined}
      />
      {/* Optional animated ring effect */}
      <div className={`absolute inset-0 ${sizeClasses[size]} text-blue-400 animate-ping opacity-20`}>
        <CheckBadgeIcon className={sizeClasses[size]} />
      </div>
    </div>
  );
}
