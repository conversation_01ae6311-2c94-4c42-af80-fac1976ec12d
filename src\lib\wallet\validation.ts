import { z } from "zod";

// Deposit validation schema
export const depositSchema = z.object({
  amount: z.string()
    .min(1, "Amount is required")
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Amount must be a positive number")
    .refine((val) => {
      const num = parseFloat(val);
      return num >= 1;
    }, "Minimum deposit amount is $1")
    .refine((val) => {
      const num = parseFloat(val);
      return num <= 10000;
    }, "Maximum deposit amount is $10,000"),
  paymentGateway: z.string().min(1, "Please select a payment gateway"),
  metadata: z.any().optional(),
});

// Send money validation schema
export const sendMoneySchema = z.object({
  recipient: z.string().min(1, "Recipient is required"),
  amount: z.string()
    .min(1, "Amount is required")
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Amount must be a positive number")
    .refine((val) => {
      const num = parseFloat(val);
      return num >= 1;
    }, "Minimum transfer amount is $1")
    .refine((val) => {
      const num = parseFloat(val);
      return num <= 5000;
    }, "Maximum transfer amount is $5,000"),
  note: z.string().max(500, "Note must be less than 500 characters").optional(),
  pin: z.string()
    .length(4, "PIN must be exactly 4 digits")
    .regex(/^\d{4}$/, "PIN must contain only numbers"),
});

// Cashout validation schema
export const cashoutSchema = z.object({
  agentId: z.string().min(1, "Please select an agent"),
  amount: z.string()
    .min(1, "Amount is required")
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Amount must be a positive number")
    .refine((val) => {
      const num = parseFloat(val);
      return num >= 10;
    }, "Minimum cashout amount is $10")
    .refine((val) => {
      const num = parseFloat(val);
      return num <= 2000;
    }, "Maximum cashout amount is $2,000"),
  note: z.string().max(500, "Note must be less than 500 characters").optional(),
  pin: z.string()
    .length(4, "PIN must be exactly 4 digits")
    .regex(/^\d{4}$/, "PIN must contain only numbers"),
});

// Internal transfer validation schema
export const internalTransferSchema = z.object({
  amount: z.string()
    .min(1, "Amount is required")
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Amount must be a positive number")
    .refine((val) => {
      const num = parseFloat(val);
      return num >= 1;
    }, "Minimum transfer amount is $1"),
  pin: z.string()
    .length(4, "PIN must be exactly 4 digits")
    .regex(/^\d{4}$/, "PIN must contain only numbers"),
});

// PIN setup validation schema
export const pinSetupSchema = z.object({
  pin: z.string()
    .length(4, "PIN must be exactly 4 digits")
    .regex(/^\d{4}$/, "PIN must contain only numbers"),
  confirmPin: z.string()
    .length(4, "PIN must be exactly 4 digits")
    .regex(/^\d{4}$/, "PIN must contain only numbers"),
}).refine((data) => data.pin === data.confirmPin, {
  message: "PINs don't match",
  path: ["confirmPin"],
});

// Transaction filter validation schema
export const transactionFilterSchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  type: z.enum(['deposit', 'send', 'receive', 'cashout', 'internal_transfer', 'earning', 'withdraw']).optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  search: z.string().optional(),
});

// Admin wallet settings validation schema
export const walletSettingsSchema = z.object({
  depositEnabled: z.boolean().default(true),
  sendEnabled: z.boolean().default(true),
  cashoutEnabled: z.boolean().default(true),
  internalTransferEnabled: z.boolean().default(true),
  dailyDepositLimit: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Daily deposit limit must be a positive number"),
  dailySendLimit: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Daily send limit must be a positive number"),
  dailyCashoutLimit: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Daily cashout limit must be a positive number"),
  transferFeePercentage: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0 && num <= 10;
    }, "Transfer fee must be between 0% and 10%"),
  cashoutFeePercentage: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0 && num <= 10;
    }, "Cashout fee must be between 0% and 10%"),
  minDepositAmount: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Minimum deposit amount must be positive"),
  maxDepositAmount: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Maximum deposit amount must be positive"),
  minSendAmount: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Minimum send amount must be positive"),
  maxSendAmount: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Maximum send amount must be positive"),
  minCashoutAmount: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Minimum cashout amount must be positive"),
  maxCashoutAmount: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Maximum cashout amount must be positive"),
});

// Payment gateway validation schema
export const paymentGatewaySchema = z.object({
  name: z.string().min(1, "Gateway name is required"),
  displayName: z.string().min(1, "Display name is required"),
  type: z.enum(['stripe', 'paypal', 'sslcommerz', 'bkash', 'nagad', 'rocket', 'bank', 'uddoktapay', 'manual', 'wallet']),
  isActive: z.boolean().default(false),
  config: z.object({}).passthrough(), // Allow any config structure
  depositFee: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0 && num <= 10;
    }, "Deposit fee must be between 0% and 10%"),
  depositFixedFee: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0;
    }, "Fixed fee must be non-negative"),
  minDeposit: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Minimum deposit must be positive"),
  maxDeposit: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Maximum deposit must be positive"),
  currency: z.string().min(1, "Currency is required"),
  sortOrder: z.number().optional().default(0),
});

// Agent validation schema
export const agentSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  email: z.string().email("Invalid email address").optional(),
  location: z.string().min(1, "Location is required"),
  serviceType: z.string().min(1, "Service type is required"),
  accountNumber: z.string().min(1, "Account number is required"),
  accountName: z.string().min(1, "Account name is required"),
  dailyLimit: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Daily limit must be positive"),
  commission: z.string()
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0 && num <= 10;
    }, "Commission must be between 0% and 10%"),
});

// User search validation schema
export const userSearchSchema = z.object({
  query: z.string().min(1, "Search query is required"),
});

// Export types
export type DepositFormData = z.infer<typeof depositSchema>;
export type SendMoneyFormData = z.infer<typeof sendMoneySchema>;
export type CashoutFormData = z.infer<typeof cashoutSchema>;
export type InternalTransferFormData = z.infer<typeof internalTransferSchema>;
export type PinSetupFormData = z.infer<typeof pinSetupSchema>;
export type TransactionFilterData = z.infer<typeof transactionFilterSchema>;
export type WalletSettingsFormData = z.infer<typeof walletSettingsSchema>;
export type PaymentGatewayFormData = z.infer<typeof paymentGatewaySchema>;
export type AgentFormData = z.infer<typeof agentSchema>;
export type UserSearchFormData = z.infer<typeof userSearchSchema>;
