"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Spinner } from "@/components/ui/Spinner";
import { Modal } from "@/components/ui/Modal";
import { Textarea } from "@/components/ui/Textarea";
import { Label } from "@/components/ui/Label";
import { Switch } from "@/components/ui/Switch";
import { toast } from "react-hot-toast";
import {
  CurrencyDollarIcon,
  ClockIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon
} from "@heroicons/react/24/outline";

interface MonetizationStatus {
  id?: string;
  isEnabled: boolean;
  isApproved: boolean;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  cprRate: string;
  totalReads: number;
  uniqueReads: number;
  totalEarnings: string;
  createdAt?: string;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  approver?: {
    id: string;
    name: string;
  };
  rejector?: {
    id: string;
    name: string;
  };
}

interface MonetizationConfig {
  cprRate: number;
  minPayoutThreshold: number;
  minReadDuration: number;
  isEnabled: boolean;
}

interface MonetizationCardProps {
  blogSlug: string;
  blogTitle: string;
  isAuthor: boolean;
}

export function MonetizationCard({ blogSlug, blogTitle, isAuthor }: MonetizationCardProps) {
  const [monetization, setMonetization] = useState<MonetizationStatus | null>(null);
  const [config, setConfig] = useState<MonetizationConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [isRequesting, setIsRequesting] = useState(false);


  useEffect(() => {
    if (isAuthor) {
      fetchMonetizationStatus();
      fetchGlobalMonetizationStatus();
    }
  }, [blogSlug, isAuthor]);

  const fetchGlobalMonetizationStatus = async () => {
    try {
      const response = await fetch('/api/monetization/status');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setConfig(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching global monetization status:', error);
    }
  };

  const fetchMonetizationStatus = async () => {
    try {
      const response = await fetch(`/api/blogs/${blogSlug}/monetization`);
      const data = await response.json();

      if (data.success) {
        setMonetization(data.data.monetization);
        setConfig(data.data.config);
      } else {
        console.error('Failed to fetch monetization status:', data.message);
      }
    } catch (error) {
      console.error('Error fetching monetization status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestMonetization = async () => {
    setIsRequesting(true);
    try {
      const response = await fetch(`/api/blogs/${blogSlug}/monetization`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Monetization request submitted successfully');
        setShowRequestModal(false);

        fetchMonetizationStatus();
      } else {
        toast.error(data.message || 'Failed to submit request');
      }
    } catch (error) {
      console.error('Error requesting monetization:', error);
      toast.error('Failed to submit request');
    } finally {
      setIsRequesting(false);
    }
  };

  const handleToggleMonetization = async (enabled: boolean) => {
    try {
      const response = await fetch(`/api/blogs/${blogSlug}/monetization`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isEnabled: enabled,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Monetization ${enabled ? 'enabled' : 'disabled'} successfully`);
        fetchMonetizationStatus();
      } else {
        toast.error(data.message || 'Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating monetization:', error);
      toast.error('Failed to update settings');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning" className="flex items-center gap-1">
          <ClockIcon className="h-3 w-3" />
          Pending Review
        </Badge>;
      case 'approved':
        return <Badge variant="success" className="flex items-center gap-1">
          <CheckCircleIcon className="h-3 w-3" />
          Approved
        </Badge>;
      case 'rejected':
        return <Badge variant="destructive" className="flex items-center gap-1">
          <XCircleIcon className="h-3 w-3" />
          Rejected
        </Badge>;
      case 'suspended':
        return <Badge variant="secondary" className="flex items-center gap-1">
          <ExclamationTriangleIcon className="h-3 w-3" />
          Suspended
        </Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (!isAuthor) {
    return null;
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (!config?.isEnabled) {
    return (
      <Card className="border-gray-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-500">
            <CurrencyDollarIcon className="h-5 w-5" />
            Monetization Unavailable
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            Blog monetization is currently disabled by the administrator.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card className={`border-l-4 ${
        monetization?.status === 'approved' ? 'border-l-green-500' :
        monetization?.status === 'pending' ? 'border-l-yellow-500' :
        monetization?.status === 'rejected' ? 'border-l-red-500' :
        'border-l-blue-500'
      }`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CurrencyDollarIcon className="h-5 w-5" />
              Blog Monetization
            </CardTitle>
            {monetization && getStatusBadge(monetization.status)}
          </div>
          <CardDescription>
            Earn money from qualified readers of your blog post
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!config?.isEnabled ? (
            // Monetization globally disabled
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-700 mb-1">Monetization Disabled</h4>
                  <p className="text-sm text-gray-600">
                    Blog monetization is currently disabled by the administrator.
                    Contact support if you have questions.
                  </p>
                </div>
              </div>
            </div>
          ) : !monetization ? (
            // No monetization request yet
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Monetization Benefits</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Earn ${config.cprRate.toFixed(2)} for every 1000 qualified reads</li>
                  <li>• Minimum read time: {Math.floor(config.minReadDuration / 60)} minutes</li>
                  <li>• Minimum payout: ${config.minPayoutThreshold.toFixed(2)}</li>
                  <li>• Automatic payments to your earning wallet</li>
                </ul>
              </div>

              <Button
                onClick={() => setShowRequestModal(true)}
                className="w-full"
              >
                <DocumentTextIcon className="h-4 w-4 mr-2" />
                Request Monetization
              </Button>
            </div>
          ) : (
            // Monetization request exists
            <div className="space-y-4">
              {monetization.status === 'pending' && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h4 className="font-medium text-yellow-900 mb-1">Request Under Review</h4>
                  <p className="text-sm text-yellow-800">
                    Your monetization request is being reviewed by our team.
                    You'll be notified once it's processed.
                  </p>
                  {monetization.createdAt && (
                    <p className="text-xs text-yellow-700 mt-2">
                      Submitted on {formatDate(monetization.createdAt)}
                    </p>
                  )}
                </div>
              )}

              {monetization.status === 'approved' && (
                <div className="space-y-4">
                  {/* Toggle Switch */}
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label className="text-base font-medium">Enable Monetization</Label>
                      <p className="text-sm text-gray-600 mt-1">
                        Turn on/off earning from this blog post
                      </p>
                    </div>
                    <Switch
                      checked={monetization.isEnabled}
                      onCheckedChange={handleToggleMonetization}
                    />
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-lg font-semibold text-blue-600">
                        {monetization.uniqueReads.toLocaleString()}
                      </div>
                      <div className="text-sm text-blue-700">Qualified Reads</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-lg font-semibold text-green-600">
                        ${parseFloat(monetization.totalEarnings).toFixed(2)}
                      </div>
                      <div className="text-sm text-green-700">Total Earnings</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="text-lg font-semibold text-purple-600">
                        ${monetization.cprRate}
                      </div>
                      <div className="text-sm text-purple-700">CPR Rate</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <div className="text-lg font-semibold text-orange-600">
                        {monetization.totalReads.toLocaleString()}
                      </div>
                      <div className="text-sm text-orange-700">Total Reads</div>
                    </div>
                  </div>

                  {monetization.approvedAt && monetization.approver && (
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="text-sm text-green-700">
                        Approved by {monetization.approver.name} on {formatDate(monetization.approvedAt)}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {monetization.status === 'rejected' && (
                <div className="space-y-4">
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h4 className="font-medium text-red-900 mb-1">Request Rejected</h4>
                    <p className="text-sm text-red-800 mb-2">
                      Your monetization request was rejected.
                    </p>
                    {monetization.rejectionReason && (
                      <p className="text-sm text-red-700 italic">
                        Reason: {monetization.rejectionReason}
                      </p>
                    )}
                    {monetization.rejectedAt && (
                      <p className="text-xs text-red-600 mt-2">
                        Rejected on {formatDate(monetization.rejectedAt)}
                      </p>
                    )}
                  </div>

                  <Button
                    onClick={() => setShowRequestModal(true)}
                    variant="outline"
                    className="w-full"
                  >
                    Submit New Request
                  </Button>
                </div>
              )}

              {monetization.status === 'suspended' && (
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-1">Monetization Suspended</h4>
                  <p className="text-sm text-gray-700">
                    Monetization for this blog has been temporarily suspended.
                    Please contact support for more information.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Request Modal */}
      {showRequestModal && (
        <Modal
          isOpen={true}
          onClose={() => {
            setShowRequestModal(false);
            setRequestReason('');
          }}
          title="Request Blog Monetization"
        >
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Monetization Details</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Earn ${config?.cprRate.toFixed(2)} per 1000 qualified reads</li>
                <li>• Readers must spend at least {Math.floor((config?.minReadDuration || 120) / 60)} minutes reading</li>
                <li>• Minimum payout threshold: ${config?.minPayoutThreshold.toFixed(2)}</li>
                <li>• Earnings are automatically added to your wallet</li>
              </ul>
            </div>



            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowRequestModal(false)}
                disabled={isRequesting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleRequestMonetization}
                disabled={isRequesting}
              >
                {isRequesting ? (
                  <Spinner size="sm" />
                ) : (
                  'Submit Request'
                )}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
