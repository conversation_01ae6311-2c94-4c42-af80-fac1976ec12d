import { db } from "@/lib/db";
import { users, subscriptionPlans, userSubscriptions } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

/**
 * Check if a user should be verified based on their active subscriptions
 * and update their verification status accordingly
 */
export async function updateUserVerificationFromSubscription(userId: string): Promise<boolean> {
  try {
    // Get user's active subscriptions with plan details
    const activeSubscriptions = await db
      .select({
        planId: userSubscriptions.planId,
        grantsVerification: subscriptionPlans.grantsVerification,
      })
      .from(userSubscriptions)
      .innerJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id))
      .where(
        and(
          eq(userSubscriptions.userId, userId),
          eq(userSubscriptions.status, 'active')
        )
      );

    // Check if any active subscription grants verification
    const shouldBeVerified = activeSubscriptions.some(sub => sub.grantsVerification);

    // Get current user verification status
    const user = await db
      .select({ isVerified: users.isVerified })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user.length) {
      throw new Error('User not found');
    }

    const currentlyVerified = user[0].isVerified;

    // Update verification status if it needs to change
    if (shouldBeVerified !== currentlyVerified) {
      await db
        .update(users)
        .set({
          isVerified: shouldBeVerified,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId));

      return shouldBeVerified;
    }

    return currentlyVerified;
  } catch (error) {
    console.error('Error updating user verification from subscription:', error);
    throw error;
  }
}

/**
 * Remove verification from users who no longer have qualifying subscriptions
 * This should be called when subscriptions are cancelled or expired
 */
export async function removeVerificationIfNoQualifyingSubscription(userId: string): Promise<void> {
  try {
    // Check if user has any active subscriptions that grant verification
    const qualifyingSubscriptions = await db
      .select({ planId: userSubscriptions.planId })
      .from(userSubscriptions)
      .innerJoin(subscriptionPlans, eq(userSubscriptions.planId, subscriptionPlans.id))
      .where(
        and(
          eq(userSubscriptions.userId, userId),
          eq(userSubscriptions.status, 'active'),
          eq(subscriptionPlans.grantsVerification, true)
        )
      );

    // If no qualifying subscriptions, remove verification
    if (qualifyingSubscriptions.length === 0) {
      await db
        .update(users)
        .set({
          isVerified: false,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId));
    }
  } catch (error) {
    console.error('Error removing verification:', error);
    throw error;
  }
}
