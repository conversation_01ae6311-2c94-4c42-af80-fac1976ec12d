"use client";

import {
  ShoppingBagIcon,
  UsersIcon,
  StarIcon,
} from "@heroicons/react/24/outline";

interface StoreStatsProps {
  productCount: number;
  followerCount: number;
  reviewCount: number;
  averageRating: number | string | null;
}

export function StoreStats({
  productCount,
  followerCount,
  reviewCount,
  averageRating
}: StoreStatsProps) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-blue-100 p-3">
            <ShoppingBagIcon className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Products</p>
            <p className="text-xl font-semibold text-gray-900">{productCount}</p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-green-100 p-3">
            <UsersIcon className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Followers</p>
            <p className="text-xl font-semibold text-gray-900">{followerCount}</p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white p-4 shadow hover:shadow-md transition-all duration-300">
        <div className="flex items-center">
          <div className="mr-4 rounded-full bg-yellow-100 p-3">
            <StarIcon className="h-5 w-5 text-yellow-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Rating</p>
            <p className="text-xl font-semibold text-gray-900">
              {averageRating ? (typeof averageRating === 'number' ? averageRating.toFixed(1) : averageRating) : "N/A"}
              <span className="ml-1 text-sm text-gray-500">
                ({reviewCount} {reviewCount === 1 ? "review" : "reviews"})
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
