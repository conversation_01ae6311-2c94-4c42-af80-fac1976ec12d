import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePostComments, fanPagePostCommentLikes } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

interface RouteParams {
  params: Promise<{
    commentId: string;
  }>;
}

// POST /api/fan-pages/posts/comments/[commentId]/like - Like/Unlike a comment
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { commentId } = await params;

    // Check if comment exists
    const comment = await db
      .select()
      .from(fanPagePostComments)
      .where(eq(fanPagePostComments.id, commentId))
      .limit(1);

    if (comment.length === 0) {
      return NextResponse.json(
        { error: "Comment not found" },
        { status: 404 }
      );
    }

    // Check if user already liked this comment
    const existingLike = await db
      .select()
      .from(fanPagePostCommentLikes)
      .where(
        and(
          eq(fanPagePostCommentLikes.commentId, commentId),
          eq(fanPagePostCommentLikes.userId, session.user.id)
        )
      )
      .limit(1);

    if (existingLike.length > 0) {
      // Unlike - remove the like
      await db
        .delete(fanPagePostCommentLikes)
        .where(eq(fanPagePostCommentLikes.id, existingLike[0].id));

      // Decrease like count
      await db
        .update(fanPagePostComments)
        .set({
          likeCount: Math.max(0, comment[0].likeCount - 1),
        })
        .where(eq(fanPagePostComments.id, commentId));

      return NextResponse.json({
        liked: false,
        likeCount: Math.max(0, comment[0].likeCount - 1),
      });
    } else {
      // Like - add the like
      const likeId = uuidv4();
      await db.insert(fanPagePostCommentLikes).values({
        id: likeId,
        commentId,
        userId: session.user.id,
      });

      // Increase like count
      await db
        .update(fanPagePostComments)
        .set({
          likeCount: comment[0].likeCount + 1,
        })
        .where(eq(fanPagePostComments.id, commentId));

      return NextResponse.json({
        liked: true,
        likeCount: comment[0].likeCount + 1,
      });
    }

  } catch (error) {
    console.error("Error toggling comment like:", error);
    return NextResponse.json(
      { error: "Failed to toggle like" },
      { status: 500 }
    );
  }
}
