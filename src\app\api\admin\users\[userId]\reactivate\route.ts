import { NextResponse } from "next/server";
import { validateApiSession } from "@/lib/utils/session-validation";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Reactivate user account (Admin only)
export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    // Validate session with admin requirement
    const { response: authError, user: currentUser } = await validateApiSession(true);
    if (authError) return authError;

    const { userId } = await context.params;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Check if user can be reactivated
    if (user.status === 'active') {
      return NextResponse.json(
        { message: "User account is already active" },
        { status: 400 }
      );
    }

    if (user.status === 'deleted') {
      return NextResponse.json(
        { message: "Cannot reactivate deleted accounts" },
        { status: 400 }
      );
    }

    // Update user status to active
    await db.update(users)
      .set({
        status: 'active',
        isActive: true,
        suspendedAt: null,
        suspendedReason: null,
        suspendedBy: null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Log the action
    console.log(`Admin ${currentUser.id} reactivated user ${userId}`);

    return NextResponse.json({
      success: true,
      message: "User account reactivated successfully",
    });
  } catch (error: any) {
    console.error("Error reactivating user account:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to reactivate user account"
      },
      { status: 500 }
    );
  }
}
