import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, likes, comments, subscriptions, users } from "@/lib/db/schema";
import { eq, desc, or, and } from "drizzle-orm";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;
    const url = new URL(req.url);
    const filter = url.searchParams.get('filter') || 'all';

    const activities: Array<{
      id: string;
      type: string;
      createdAt: string;
      user: {
        id: string;
        name: string | null;
        username: string | null;
        image: string | null;
      };
      post?: any;
      targetUser?: any;
      photo?: any;
    }> = [];

    // Get user info
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        name: true,
        username: true,
        image: true,
      }
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get posts created by user
    if (filter === 'all' || filter === 'posts') {
      const userPosts = await db
        .select({
          id: posts.id,
          content: posts.content,
          createdAt: posts.createdAt,
        })
        .from(posts)
        .where(eq(posts.userId, userId))
        .orderBy(desc(posts.createdAt))
        .limit(20);

      userPosts.forEach(post => {
        activities.push({
          id: `post-${post.id}`,
          type: 'post_created',
          createdAt: post.createdAt.toISOString(),
          user,
          post: {
            id: post.id,
            content: post.content,
            user: {
              name: user.name,
              username: user.username,
            }
          }
        });
      });
    }

    // Get likes by user
    if (filter === 'all' || filter === 'likes') {
      const userLikes = await db
        .select({
          id: likes.id,
          createdAt: likes.createdAt,
          post: {
            id: posts.id,
            content: posts.content,
            user: {
              name: users.name,
              username: users.username,
            }
          }
        })
        .from(likes)
        .innerJoin(posts, eq(likes.postId, posts.id))
        .innerJoin(users, eq(posts.userId, users.id))
        .where(eq(likes.userId, userId))
        .orderBy(desc(likes.createdAt))
        .limit(20);

      userLikes.forEach(like => {
        activities.push({
          id: `like-${like.id}`,
          type: 'post_liked',
          createdAt: (like.createdAt as Date).toISOString(),
          user,
          post: like.post
        });
      });
    }

    // Get comments by user
    if (filter === 'all' || filter === 'comments') {
      const userComments = await db
        .select({
          id: comments.id,
          createdAt: comments.createdAt,
          post: {
            id: posts.id,
            content: posts.content,
            user: {
              name: users.name,
              username: users.username,
            }
          }
        })
        .from(comments)
        .innerJoin(posts, eq(comments.postId, posts.id))
        .innerJoin(users, eq(posts.userId, users.id))
        .where(eq(comments.userId, userId))
        .orderBy(desc(comments.createdAt))
        .limit(20);

      userComments.forEach(comment => {
        activities.push({
          id: `comment-${comment.id}`,
          type: 'comment_created',
          createdAt: (comment.createdAt as Date).toISOString(),
          user,
          post: comment.post
        });
      });
    }

    // Get subscriptions by user
    if (filter === 'all' || filter === 'subscriptions') {
      const userSubscriptions = await db
        .select({
          id: subscriptions.id,
          createdAt: subscriptions.createdAt,
          targetUser: {
            id: users.id,
            name: users.name,
            username: users.username,
          }
        })
        .from(subscriptions)
        .innerJoin(users, eq(subscriptions.targetUserId, users.id))
        .where(eq(subscriptions.subscriberId, userId))
        .orderBy(desc(subscriptions.createdAt))
        .limit(20);

      userSubscriptions.forEach(subscription => {
        activities.push({
          id: `subscription-${subscription.id}`,
          type: 'user_subscribed',
          createdAt: subscription.createdAt.toISOString(),
          user,
          targetUser: subscription.targetUser
        });
      });
    }

    // Sort activities by date
    activities.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({
      activities: activities.slice(0, 50), // Limit to 50 most recent activities
      total: activities.length
    });

  } catch (error) {
    console.error("Error fetching user activity:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
