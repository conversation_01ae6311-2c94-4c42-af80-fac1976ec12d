import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { z } from "zod";

const calculateFeeSchema = z.object({
  amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Amount must be a positive number"),
});

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = calculateFeeSchema.parse(body);

    // Check if sending is enabled
    const sendEnabled = await WalletService.getWalletSetting('send_enabled');
    if (sendEnabled === 'false') {
      return NextResponse.json(
        { 
          success: false,
          message: "Money transfers are currently disabled" 
        },
        { status: 400 }
      );
    }

    // Get send money settings
    const minAmount = parseFloat(await WalletService.getWalletSetting('send_min_amount') || '1.00');
    const maxAmount = parseFloat(await WalletService.getWalletSetting('send_max_amount') || '5000.00');
    const feePercentage = parseFloat(await WalletService.getWalletSetting('send_fee_percentage') || '1.00');
    const feeFixed = parseFloat(await WalletService.getWalletSetting('send_fee_fixed') || '0.00');

    const amount = parseFloat(validatedData.amount);

    // Validate amount limits
    if (amount < minAmount) {
      return NextResponse.json(
        { 
          success: false,
          message: `Minimum send amount is $${minAmount.toFixed(2)}` 
        },
        { status: 400 }
      );
    }

    if (amount > maxAmount) {
      return NextResponse.json(
        { 
          success: false,
          message: `Maximum send amount is $${maxAmount.toFixed(2)}` 
        },
        { status: 400 }
      );
    }

    // Calculate fees
    const percentageFee = (amount * feePercentage) / 100;
    const totalFee = percentageFee + feeFixed;
    const totalAmountNeeded = amount + totalFee;

    // Get user's general balance
    const userWallet = await WalletService.getOrCreateWallet(session.user.id);
    const generalBalance = parseFloat(userWallet.generalBalance);
    const hasSufficientBalance = generalBalance >= totalAmountNeeded;

    return NextResponse.json({
      success: true,
      data: {
        amount: amount.toFixed(2),
        feePercentage: feePercentage.toFixed(2),
        feeFixed: feeFixed.toFixed(2),
        percentageFee: percentageFee.toFixed(2),
        totalFee: totalFee.toFixed(2),
        totalAmountNeeded: totalAmountNeeded.toFixed(2),
        recipientReceives: amount.toFixed(2),
        hasSufficientBalance,
        currentBalance: generalBalance.toFixed(2),
        limits: {
          min: minAmount.toFixed(2),
          max: maxAmount.toFixed(2),
        },
      },
    });
  } catch (error: any) {
    console.error("Error calculating send money fee:", error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid input data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: "Failed to calculate send money fee" 
      },
      { status: 500 }
    );
  }
}
