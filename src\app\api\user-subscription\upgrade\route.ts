import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { SubscriptionService } from "@/lib/subscription/subscriptionService";
import { z } from "zod";

const upgradeSubscriptionSchema = z.object({
  newPlanId: z.string(),
  paymentMethod: z.string().optional(),
});

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { newPlanId, paymentMethod } = upgradeSubscriptionSchema.parse(body);

    // Get user's current subscription
    const currentSubscription = await SubscriptionService.getUserSubscription(session.user.id);
    
    if (!currentSubscription) {
      return NextResponse.json(
        { message: "No active subscription found. Please create a subscription first." },
        { status: 400 }
      );
    }

    // Check if the new plan is different from current plan
    if (currentSubscription.planId === newPlanId) {
      return NextResponse.json(
        { message: "You are already subscribed to this plan" },
        { status: 400 }
      );
    }

    const newSubscriptionId = await SubscriptionService.upgradeSubscription({
      subscriptionId: currentSubscription.id,
      newPlanId,
      paymentMethod,
    });

    return NextResponse.json({
      success: true,
      data: {
        subscriptionId: newSubscriptionId,
        message: "Subscription upgrade initiated. Please complete payment to activate.",
      },
    });
  } catch (error) {
    console.error("Error upgrading subscription:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
