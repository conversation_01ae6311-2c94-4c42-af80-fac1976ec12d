import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPages, users } from "@/lib/db/schema";
import { eq, count, gte } from "drizzle-orm";

// GET /api/admin/fan-pages/stats - Get fan pages statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0 || !user[0].isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get current date and first day of current month
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get total pages count
    const totalPagesResult = await db
      .select({ count: count() })
      .from(fanPages);

    // Get active pages count
    const activePagesResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(eq(fanPages.isActive, true));

    // Get verified pages count
    const verifiedPagesResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(eq(fanPages.isVerified, true));

    // Get new pages this month
    const newPagesThisMonthResult = await db
      .select({ count: count() })
      .from(fanPages)
      .where(gte(fanPages.createdAt, firstDayOfMonth));

    const stats = {
      totalPages: totalPagesResult[0]?.count || 0,
      activePages: activePagesResult[0]?.count || 0,
      verifiedPages: verifiedPagesResult[0]?.count || 0,
      newPagesThisMonth: newPagesThisMonthResult[0]?.count || 0,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching fan pages stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
