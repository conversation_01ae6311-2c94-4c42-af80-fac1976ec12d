"use client";

import React from 'react';

interface MarkdownPreviewProps {
  content: string;
  className?: string;
}

export const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ content, className = "" }) => {
  // Enhanced markdown to HTML converter
  const convertMarkdownToHTML = (markdown: string): string => {
    let html = markdown;

    // Headers (H1-H6)
    html = html.replace(/^###### (.*$)/gim, '<h6 class="text-sm font-semibold mb-2 mt-3 text-gray-700">$1</h6>');
    html = html.replace(/^##### (.*$)/gim, '<h5 class="text-base font-semibold mb-2 mt-3 text-gray-700">$1</h5>');
    html = html.replace(/^#### (.*$)/gim, '<h4 class="text-lg font-semibold mb-2 mt-4 text-gray-800">$1</h4>');
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mb-3 mt-4 text-gray-800">$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold mb-3 mt-5 text-gray-900">$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-4 mt-6 text-gray-900">$1</h1>');

    // Code blocks (before inline code)
    html = html.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 border border-gray-200 rounded-lg p-4 my-4 overflow-x-auto"><code class="text-sm font-mono">$1</code></pre>');

    // Strikethrough
    html = html.replace(/~~(.*?)~~/g, '<del class="line-through text-gray-500">$1</del>');

    // Underline
    html = html.replace(/<u>(.*?)<\/u>/g, '<span class="underline">$1</span>');

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em class="italic">$1</em>');

    // Inline code
    html = html.replace(/`(.*?)`/g, '<code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono text-red-600">$1</code>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline transition-colors" target="_blank" rel="nofollow noopener noreferrer">$1</a>');

    // Images
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg my-4 shadow-sm" />');

    // Tables
    html = html.replace(/\|(.+)\|/g, (match, content) => {
      const cells = content.split('|').map((cell: string) => cell.trim()).filter((cell: string) => cell);
      return '<tr>' + cells.map((cell: string) => `<td class="border border-gray-300 px-3 py-2">${cell}</td>`).join('') + '</tr>';
    });
    html = html.replace(/(<tr>.*<\/tr>)/s, '<table class="w-full border-collapse border border-gray-300 my-4">$1</table>');

    // Numbered lists
    html = html.replace(/^\d+\. (.*$)/gim, '<li class="ml-4 mb-1">$1</li>');
    html = html.replace(/(<li class="ml-4 mb-1">.*<\/li>)/s, '<ol class="list-decimal list-inside mb-4 space-y-1">$1</ol>');

    // Bullet lists
    html = html.replace(/^\- (.*$)/gim, '<li class="ml-4 mb-1">$1</li>');
    html = html.replace(/(<li class="ml-4 mb-1">.*<\/li>)/s, '<ul class="list-disc list-inside mb-4 space-y-1">$1</ul>');

    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-blue-400 pl-4 italic text-gray-700 my-4 bg-blue-50 py-2">$1</blockquote>');

    // Horizontal rules
    html = html.replace(/^---$/gim, '<hr class="border-t-2 border-gray-300 my-6" />');

    // Line breaks and paragraphs
    html = html.replace(/\n\n/g, '</p><p class="mb-4 leading-relaxed">');
    html = html.replace(/\n/g, '<br />');

    // Wrap in paragraphs
    if (html && !html.startsWith('<')) {
      html = '<p class="mb-4 leading-relaxed">' + html + '</p>';
    }

    return html;
  };

  return (
    <div 
      className={`prose prose-sm max-w-none ${className}`}
      dangerouslySetInnerHTML={{ __html: convertMarkdownToHTML(content) }}
    />
  );
};

export default MarkdownPreview;
