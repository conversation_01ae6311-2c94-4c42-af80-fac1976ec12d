-- Create blogDislikes table
CREATE TABLE IF NOT EXISTS `blogDislikes` (
  `id` varchar(255) NOT NULL,
  `blogId` varchar(255) NOT NULL,
  `userId` varchar(255) NOT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON> KEY (`id`),
  <PERSON><PERSON><PERSON> `blogDislikes_blogId_idx` (`blogId`),
  <PERSON><PERSON><PERSON> `blogDislikes_userId_idx` (`userId`),
  UNIQUE KEY `blogDislikes_blogId_userId_unique` (`blogId`, `userId`),
  CONSTRAINT `blogDislikes_blogId_fkey` FOREIGN KEY (`blogId`) REFERENCES `blogs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `blogDislikes_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
