import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { ViewTrackingService } from "@/lib/analytics/viewTrackingService";
import { z } from "zod";
import { headers } from "next/headers";

const trackViewSchema = z.object({
  sessionId: z.string().min(1),
  fingerprint: z.string().optional(),
  referrer: z.string().optional(),
  viewDuration: z.number().min(0).optional(),
  scrollDepth: z.number().min(0).max(100).optional(),
  country: z.string().optional(),
  city: z.string().optional(),
});

// Track blog view
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;
    const session = await getServerSession(authOptions);
    const headersList = headers();

    // Get blog
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: {
        id: true,
        title: true,
        status: true,
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Only track views for published blogs
    if (blog.status !== 'published') {
      return NextResponse.json(
        { message: "Cannot track views for unpublished blogs" },
        { status: 400 }
      );
    }

    const body = await req.json();
    const validatedData = trackViewSchema.parse(body);

    // Get client information
    const forwardedFor = headersList.get('x-forwarded-for');
    const realIp = headersList.get('x-real-ip');
    const ipAddress = forwardedFor?.split(',')[0] || realIp || '127.0.0.1';
    const userAgent = headersList.get('user-agent') || '';

    // Detect device, browser, OS from user agent
    const device = detectDevice(userAgent);
    const browser = detectBrowser(userAgent);
    const os = detectOS(userAgent);

    // Track the view
    await ViewTrackingService.trackView({
      blogId: blog.id,
      userId: session?.user?.id,
      ipAddress,
      userAgent,
      sessionId: validatedData.sessionId,
      fingerprint: validatedData.fingerprint,
      referrer: validatedData.referrer,
      country: validatedData.country,
      city: validatedData.city,
      device,
      browser,
      os,
      viewDuration: validatedData.viewDuration,
      scrollDepth: validatedData.scrollDepth,
    });

    return NextResponse.json({
      success: true,
      message: "View tracked successfully",
    });

  } catch (error) {
    console.error("Error tracking view:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update view metrics (duration and scroll depth)
export async function PATCH(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;

    // Get blog
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true, status: true },
    });

    if (!blog || blog.status !== 'published') {
      return NextResponse.json(
        { message: "Blog not found or not published" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { sessionId, viewDuration, scrollDepth } = body;

    if (!sessionId || typeof viewDuration !== 'number' || typeof scrollDepth !== 'number') {
      return NextResponse.json(
        { message: "Invalid request data" },
        { status: 400 }
      );
    }

    // Update view metrics
    await ViewTrackingService.updateViewMetrics(
      sessionId,
      blog.id,
      viewDuration,
      scrollDepth
    );

    return NextResponse.json({
      success: true,
      message: "View metrics updated successfully",
    });

  } catch (error) {
    console.error("Error updating view metrics:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper functions
function detectDevice(userAgent: string): string {
  if (/mobile/i.test(userAgent)) return 'mobile';
  if (/tablet|ipad/i.test(userAgent)) return 'tablet';
  return 'desktop';
}

function detectBrowser(userAgent: string): string {
  if (/chrome/i.test(userAgent) && !/edge/i.test(userAgent)) return 'Chrome';
  if (/firefox/i.test(userAgent)) return 'Firefox';
  if (/safari/i.test(userAgent) && !/chrome/i.test(userAgent)) return 'Safari';
  if (/edge/i.test(userAgent)) return 'Edge';
  if (/opera/i.test(userAgent)) return 'Opera';
  return 'Other';
}

function detectOS(userAgent: string): string {
  if (/windows/i.test(userAgent)) return 'Windows';
  if (/macintosh|mac os x/i.test(userAgent)) return 'macOS';
  if (/linux/i.test(userAgent)) return 'Linux';
  if (/android/i.test(userAgent)) return 'Android';
  if (/iphone|ipad|ipod/i.test(userAgent)) return 'iOS';
  return 'Other';
}
