import { MainLayout } from "@/components/layout/MainLayout";
import { getCurrentUser } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { fanPages, fanPageRoles } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { notFound, redirect } from "next/navigation";
import { EditFanPageForm } from "@/components/fan-pages/EditFanPageForm";
import { Button } from "@/components/ui/Button";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

interface EditFanPagePageProps {
  params: Promise<{
    username: string;
  }>;
}

export const dynamic = 'force-dynamic';

export default async function EditFanPagePage({ params }: EditFanPagePageProps) {
  const { username } = await params;
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/signin");
  }

  // Get fan page by username
  const pageResult = await db
    .select({
      id: fanPages.id,
      ownerId: fanPages.ownerId,
      name: fanPages.name,
      username: fanPages.username,
      category: fanPages.category,
      description: fanPages.description,
      profileImage: fanPages.profileImage,
      coverImage: fanPages.coverImage,
      website: fanPages.website,
      email: fanPages.email,
      phone: fanPages.phone,
      location: fanPages.location,
      isVerified: fanPages.isVerified,
      isActive: fanPages.isActive,
      followerCount: fanPages.followerCount,
      postCount: fanPages.postCount,
      createdAt: fanPages.createdAt,
    })
    .from(fanPages)
    .where(and(
      eq(fanPages.username, username),
      eq(fanPages.isActive, true)
    ))
    .limit(1);

  if (pageResult.length === 0) {
    notFound();
  }

  const page = pageResult[0];

  // Check if user is the owner or has admin/editor role
  let isOwner = page.ownerId === user.id;
  let hasEditPermission = isOwner;

  if (!isOwner) {
    // Check if user has admin or editor role for this page
    const roleResult = await db
      .select({
        role: fanPageRoles.role,
      })
      .from(fanPageRoles)
      .where(and(
        eq(fanPageRoles.fanPageId, page.id),
        eq(fanPageRoles.userId, user.id)
      ))
      .limit(1);

    if (roleResult.length > 0) {
      const userRole = roleResult[0].role;
      hasEditPermission = userRole === 'admin' || userRole === 'editor';
    }
  }

  if (!hasEditPermission) {
    redirect(`/pages/${username}`);
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-6">
              <div className="flex items-center space-x-4">
                <Link href={`/pages/${page.username}`}>
                  <Button variant="outline" size="sm">
                    <ArrowLeftIcon className="h-4 w-4 mr-2" />
                    Back to Page
                  </Button>
                </Link>
                <div className="flex items-center space-x-3">
                  {page.profileImage ? (
                    <img
                      src={page.profileImage}
                      alt={page.name}
                      className="h-12 w-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                      <span className="text-white font-semibold text-lg">
                        {page.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <div>
                    <h1 className="text-xl font-bold text-gray-900">
                      Edit {page.name}
                    </h1>
                    <p className="text-sm text-gray-500">@{page.username}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <EditFanPageForm page={{
            ...page,
            isVerified: Boolean(page.isVerified),
            followerCount: page.followerCount || 0
          }} />
        </div>
      </div>
    </MainLayout>
  );
}
