"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Select } from "@/components/ui/Select";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import {
  UserIcon,
  BuildingOfficeIcon,
  MicrophoneIcon,
  FilmIcon,
  TagIcon,
  BriefcaseIcon,
  AcademicCapIcon,
  PencilIcon,
  TrophyIcon,
  UserGroupIcon,
  NewspaperIcon,
  HeartIcon,
  EllipsisHorizontalIcon,
  PhotoIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

const categories = [
  { value: 'musician', label: 'Musician/Band', icon: MicrophoneIcon },
  { value: 'actor', label: 'Actor', icon: FilmIcon },
  { value: 'brand', label: 'Brand', icon: TagIcon },
  { value: 'business', label: 'Business', icon: BriefcaseIcon },
  { value: 'organization', label: 'Organization', icon: BuildingOfficeIcon },
  { value: 'public_figure', label: 'Public Figure', icon: UserIcon },
  { value: 'artist', label: 'Artist', icon: PencilIcon },
  { value: 'writer', label: 'Writer', icon: AcademicCapIcon },
  { value: 'athlete', label: 'Athlete', icon: TrophyIcon },
  { value: 'politician', label: 'Politician', icon: UserGroupIcon },
  { value: 'entertainment', label: 'Entertainment', icon: FilmIcon },
  { value: 'media', label: 'Media', icon: NewspaperIcon },
  { value: 'community', label: 'Community', icon: HeartIcon },
  { value: 'cause', label: 'Cause', icon: HeartIcon },
  { value: 'other', label: 'Other', icon: EllipsisHorizontalIcon },
];

interface FanPage {
  id: string;
  name: string;
  username: string;
  category: string;
  description: string | null;
  profileImage: string | null;
  coverImage: string | null;
  website: string | null;
  email: string | null;
  phone: string | null;
  location: string | null;
  isVerified: boolean;
  followerCount: number;
  postCount: number;
}

interface EditFanPageFormProps {
  page: FanPage;
  onSuccess?: (updatedPage: FanPage) => void;
  onCancel?: () => void;
  isModal?: boolean;
}

export function EditFanPageForm({ page, onSuccess, onCancel, isModal = false }: EditFanPageFormProps) {
  const { data: session } = useSession();
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: page.name || "",
    category: page.category || "",
    description: page.description || "",
    website: page.website || "",
    email: page.email || "",
    phone: page.phone || "",
    location: page.location || "",
  });

  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(page.profileImage);
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(page.coverImage);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError("");
  };

  const handleCategoryChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      category: value
    }));
    setError("");
  };

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error("Profile image must be less than 5MB");
        return;
      }
      setProfileImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error("Cover image must be less than 10MB");
        return;
      }
      setCoverImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (file: File, type: 'profile' | 'cover'): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const response = await fetch('/api/upload/fan-page-image', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      let errorMessage = `Failed to upload ${type} image`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch (jsonError) {
        // If JSON parsing fails, use the response status text
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    return data.url;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      // Validate required fields
      if (!formData.name.trim()) {
        throw new Error("Page name is required");
      }

      if (!formData.category) {
        throw new Error("Category is required");
      }

      // Upload images if new ones are selected
      let profileImageUrl = page.profileImage;
      let coverImageUrl = page.coverImage;

      if (profileImageFile) {
        profileImageUrl = await uploadImage(profileImageFile, 'profile');
      }

      if (coverImageFile) {
        coverImageUrl = await uploadImage(coverImageFile, 'cover');
      }

      // Prepare update data
      const updateData = {
        name: formData.name.trim(),
        category: formData.category,
        description: formData.description.trim() || "",
        website: formData.website.trim() || "",
        email: formData.email.trim() || "",
        phone: formData.phone.trim() || "",
        location: formData.location.trim() || "",
        profileImage: profileImageUrl,
        coverImage: coverImageUrl,
      };

      console.log('Sending update data:', updateData);

      // Update fan page
      const response = await fetch(`/api/fan-pages/${page.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to update fan page';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.error || errorMessage;
          console.error('API Error Details:', errorData);
        } catch (jsonError) {
          // If JSON parsing fails, use the response status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const updatedPage = await response.json();

      toast.success("Fan page updated successfully!");

      if (onSuccess) {
        onSuccess(updatedPage);
      } else {
        router.push(`/pages/${page.username}`);
      }
    } catch (error) {
      console.error('Error updating fan page:', error);
      setError(error instanceof Error ? error.message : 'Failed to update fan page');
      toast.error(error instanceof Error ? error.message : 'Failed to update fan page');
    } finally {
      setIsLoading(false);
    }
  };

  if (!session) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">You must be logged in to edit this fan page.</p>
      </div>
    );
  }

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Profile Image Upload */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Profile Image
        </label>
        <div className="flex items-center space-x-4">
          <div className="relative">
            {profileImagePreview ? (
              <img
                src={profileImagePreview}
                alt="Profile preview"
                className="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
              />
            ) : (
              <div className="h-20 w-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-white font-semibold text-lg">
                  {formData.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            {profileImagePreview && (
              <button
                type="button"
                onClick={() => {
                  setProfileImageFile(null);
                  setProfileImagePreview(page.profileImage);
                }}
                className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
              >
                <XMarkIcon className="h-3 w-3" />
              </button>
            )}
          </div>
          <div>
            <input
              type="file"
              accept="image/*"
              onChange={handleProfileImageChange}
              className="hidden"
              id="profile-image-upload"
            />
            <label
              htmlFor="profile-image-upload"
              className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <PhotoIcon className="h-4 w-4 mr-2" />
              Change Photo
            </label>
            <p className="text-xs text-gray-500 mt-1">Max 5MB, JPG/PNG</p>
          </div>
        </div>
      </div>

      {/* Cover Image Upload */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Cover Image
        </label>
        <div className="space-y-3">
          {coverImagePreview && (
            <div className="relative">
              <img
                src={coverImagePreview}
                alt="Cover preview"
                className="w-full h-32 object-cover rounded-lg border-2 border-gray-200"
              />
              <button
                type="button"
                onClick={() => {
                  setCoverImageFile(null);
                  setCoverImagePreview(page.coverImage);
                }}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          )}
          <div>
            <input
              type="file"
              accept="image/*"
              onChange={handleCoverImageChange}
              className="hidden"
              id="cover-image-upload"
            />
            <label
              htmlFor="cover-image-upload"
              className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <PhotoIcon className="h-4 w-4 mr-2" />
              {coverImagePreview ? 'Change Cover' : 'Add Cover'}
            </label>
            <p className="text-xs text-gray-500 mt-1">Max 10MB, JPG/PNG</p>
          </div>
        </div>
      </div>

      {/* Page Name */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Page Name *
        </label>
        <Input
          id="name"
          name="name"
          type="text"
          value={formData.name}
          onChange={handleChange}
          placeholder="Enter your page name"
          required
          maxLength={255}
        />
      </div>

      {/* Username (Read-only) */}
      <div>
        <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
          Username
        </label>
        <div className="relative">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
            @
          </span>
          <Input
            id="username"
            name="username"
            type="text"
            value={page.username}
            disabled
            className="pl-8 bg-gray-50 text-gray-500"
          />
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Username cannot be changed after page creation
        </p>
      </div>

      {/* Category */}
      <div>
        <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
          Category *
        </label>
        <select
          id="category"
          name="category"
          value={formData.category}
          onChange={(e) => handleCategoryChange(e.target.value)}
          required
          className="block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          <option value="">Select a category</option>
          {categories.map((category) => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Tell people about your page..."
          rows={4}
          maxLength={1000}
        />
        <p className="mt-1 text-sm text-gray-500">
          {formData.description.length}/1000 characters
        </p>
      </div>

      {/* Contact Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
              Website
            </label>
            <Input
              id="website"
              name="website"
              type="url"
              value={formData.website}
              onChange={handleChange}
              placeholder="https://example.com"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
              Phone
            </label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder="+****************"
              maxLength={50}
            />
          </div>

          <div>
            <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
              Location
            </label>
            <Input
              id="location"
              name="location"
              type="text"
              value={formData.location}
              onChange={handleChange}
              placeholder="City, Country"
              maxLength={255}
            />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
        )}
        <Button
          type="submit"
          disabled={isLoading}
          className="min-w-[120px]"
        >
          {isLoading ? (
            <div className="flex items-center">
              <Spinner size="sm" className="mr-2" />
              Updating...
            </div>
          ) : (
            'Update Page'
          )}
        </Button>
      </div>
    </form>
  );

  if (isModal) {
    return formContent;
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Edit Fan Page</h2>
        {formContent}
      </div>
    </div>
  );
}
