import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { stores } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { redirect } from "next/navigation";
import { ProductForm } from "@/components/marketplace/ProductForm";

export default async function CreateProductPage() {
  const user = await requireAuth();
  
  // Check if user has a store
  const userStore = await db
    .select({
      id: stores.id,
      name: stores.name,
    })
    .from(stores)
    .where(eq(stores.ownerId, user.id))
    .limit(1);
  
  // If user doesn't have a store, redirect to create store page
  if (userStore.length === 0) {
    redirect("/marketplace/create-store");
  }

  const store = userStore[0];

  return (
    <MainLayout>
      <div className="mx-auto max-w-3xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
            Add New Product
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            List a new product in your store: {store.name}
          </p>
        </div>
        
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-6">
            <ProductForm storeId={store.id} />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
