import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { blogs, blogCategories, users } from "@/lib/db/schema";
import { like, or, eq, and } from "drizzle-orm";

// Search blogs
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const query = url.searchParams.get("q") || "";
    const limit = parseInt(url.searchParams.get("limit") || "10");

    if (query.length < 2) {
      return NextResponse.json([]);
    }

    // Search in blogs
    const searchResults = await db.query.blogs.findMany({
      where: and(
        eq(blogs.status, "published"),
        or(
          like(blogs.title, `%${query}%`),
          like(blogs.excerpt, `%${query}%`),
          like(blogs.content, `%${query}%`)
        )
      ),
      limit: limit,
      with: {
        author: {
          columns: {
            id: true,
            name: true,
            username: true,
          },
        },
        category: {
          columns: {
            id: true,
            name: true,
            color: true,
          },
        },
      },
    });

    // Format results for frontend
    const formattedResults = searchResults.map((blog) => ({
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      excerpt: blog.excerpt,
      author: blog.author,
      category: blog.category,
      readTime: blog.readTime,
    }));

    return NextResponse.json(formattedResults);
  } catch (error) {
    console.error("Error searching blogs:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
