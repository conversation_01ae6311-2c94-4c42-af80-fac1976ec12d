import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogCategories, users, blogLikes, blogBookmarks } from "@/lib/db/schema";
import { eq, desc, count, and } from "drizzle-orm";

// Get user's blogs
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { userId } = await context.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status") || "all";
    const offset = (page - 1) * limit;

    // Check if user is requesting their own blogs or viewing others' published blogs
    const isOwnProfile = userId === session.user.id;
    const isAdmin = session.user.isAdmin;

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Build where conditions
    const conditions = [eq(blogs.authorId, userId)];

    // If not own profile and not admin, only show published blogs
    if (!isOwnProfile && !isAdmin) {
      conditions.push(eq(blogs.status, "published"));
    } else if (status !== "all") {
      conditions.push(eq(blogs.status, status as any));
    }

    // Get user's blogs with category data
    const userBlogs = await db.select({
      id: blogs.id,
      title: blogs.title,
      slug: blogs.slug,
      excerpt: blogs.excerpt,
      coverImage: blogs.coverImage,
      status: blogs.status,
      featured: blogs.featured,
      viewCount: blogs.viewCount,
      readTime: blogs.readTime,
      tags: blogs.tags,
      publishedAt: blogs.publishedAt,
      createdAt: blogs.createdAt,
      updatedAt: blogs.updatedAt,
      // Category data
      categoryName: blogCategories.name,
      categorySlug: blogCategories.slug,
    })
    .from(blogs)
    .leftJoin(blogCategories, eq(blogs.categoryId, blogCategories.id))
    .where(conditions.length > 1 ? and(...conditions) : conditions[0])
    .orderBy(desc(blogs.createdAt))
    .limit(limit)
    .offset(offset);

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(blogs)
      .where(conditions.length > 1 ? and(...conditions) : conditions[0]);

    const total = totalResult[0]?.count || 0;

    // Get likes and bookmarks for current user if they're viewing their own blogs
    const blogsWithInteractions = await Promise.all(
      userBlogs.map(async (blog) => {
        let isLiked = false;
        let isBookmarked = false;

        // Check if current user liked this blog
        const like = await db.query.blogLikes.findFirst({
          where: and(eq(blogLikes.blogId, blog.id), eq(blogLikes.userId, session.user.id)),
        });
        isLiked = !!like;

        // Check if current user bookmarked this blog
        const bookmark = await db.query.blogBookmarks.findFirst({
          where: and(eq(blogBookmarks.blogId, blog.id), eq(blogBookmarks.userId, session.user.id)),
        });
        isBookmarked = !!bookmark;

        return {
          ...blog,
          isLiked,
          isBookmarked,
          category: blog.categoryName ? {
            name: blog.categoryName,
            slug: blog.categorySlug,
          } : null,
        };
      })
    );

    return NextResponse.json({
      blogs: blogsWithInteractions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user blogs:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
