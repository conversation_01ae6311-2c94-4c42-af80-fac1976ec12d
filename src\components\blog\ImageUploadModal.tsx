"use client";

import React, { useState, useRef, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  XMarkIcon,
  PhotoIcon,
  CloudArrowUpIcon,
  TrashIcon,
  PencilIcon,
  CheckIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

interface ImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (url: string, alt: string) => void;
}

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  alt: string;
  size: number;
}

export const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  isOpen,
  onClose,
  onInsert
}) => {
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [uploading, setUploading] = useState(false);
  const [editingAlt, setEditingAlt] = useState<string | null>(null);
  const [altText, setAltText] = useState('');
  const [urlInput, setUrlInput] = useState('');
  const [urlAlt, setUrlAlt] = useState('');
  const [activeTab, setActiveTab] = useState<'upload' | 'url'>('upload');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setUploading(true);
    
    acceptedFiles.forEach((file) => {
      const reader = new FileReader();
      reader.onload = () => {
        const newImage: UploadedImage = {
          id: Math.random().toString(36).substr(2, 9),
          file,
          url: reader.result as string,
          alt: file.name.replace(/\.[^/.]+$/, ""), // Remove extension
          size: file.size
        };
        
        setImages(prev => [...prev, newImage]);
      };
      reader.readAsDataURL(file);
    });
    
    setUploading(false);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.svg']
    },
    maxSize: 5 * 1024 * 1024, // 5MB
    multiple: true
  });

  const removeImage = (id: string) => {
    setImages(prev => prev.filter(img => img.id !== id));
  };

  const updateAlt = (id: string, newAlt: string) => {
    setImages(prev => prev.map(img => 
      img.id === id ? { ...img, alt: newAlt } : img
    ));
    setEditingAlt(null);
  };

  const handleInsertImage = (image: UploadedImage) => {
    onInsert(image.url, image.alt);
    onClose();
  };

  const handleInsertUrl = () => {
    if (urlInput.trim()) {
      onInsert(urlInput.trim(), urlAlt.trim() || 'Image');
      setUrlInput('');
      setUrlAlt('');
      onClose();
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Insert Image</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('upload')}
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === 'upload'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Upload Files
          </button>
          <button
            onClick={() => setActiveTab('url')}
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === 'url'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            From URL
          </button>
        </div>

        <div className="p-4 max-h-[60vh] overflow-y-auto">
          {activeTab === 'upload' ? (
            <>
              {/* Upload Area */}
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-blue-400 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input {...getInputProps()} />
                <CloudArrowUpIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-700 mb-2">
                  {isDragActive ? 'Drop images here' : 'Drag & drop images here'}
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  or click to browse files
                </p>
                <p className="text-xs text-gray-400">
                  Supports: JPEG, PNG, GIF, WebP, SVG (max 5MB each)
                </p>
              </div>

              {/* Uploaded Images */}
              {images.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-700 mb-3">
                    Uploaded Images ({images.length})
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {images.map((image) => (
                      <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="aspect-video relative">
                          <img
                            src={image.url}
                            alt={image.alt}
                            className="w-full h-full object-cover"
                          />
                          <button
                            onClick={() => removeImage(image.id)}
                            className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                          >
                            <XCircleIcon className="h-4 w-4" />
                          </button>
                        </div>
                        <div className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            {editingAlt === image.id ? (
                              <div className="flex items-center space-x-1 flex-1">
                                <input
                                  type="text"
                                  value={altText}
                                  onChange={(e) => setAltText(e.target.value)}
                                  className="flex-1 text-xs border border-gray-300 rounded px-2 py-1"
                                  placeholder="Alt text"
                                />
                                <button
                                  onClick={() => updateAlt(image.id, altText)}
                                  className="p-1 text-green-600 hover:text-green-700"
                                >
                                  <CheckIcon className="h-3 w-3" />
                                </button>
                              </div>
                            ) : (
                              <>
                                <span className="text-xs text-gray-600 truncate flex-1">
                                  {image.alt}
                                </span>
                                <button
                                  onClick={() => {
                                    setEditingAlt(image.id);
                                    setAltText(image.alt);
                                  }}
                                  className="p-1 text-gray-400 hover:text-gray-600"
                                >
                                  <PencilIcon className="h-3 w-3" />
                                </button>
                              </>
                            )}
                          </div>
                          <p className="text-xs text-gray-400 mb-2">
                            {formatFileSize(image.size)}
                          </p>
                          <button
                            onClick={() => handleInsertImage(image)}
                            className="w-full bg-blue-600 text-white text-xs py-1 rounded hover:bg-blue-700"
                          >
                            Insert
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          ) : (
            /* URL Tab */
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL
                </label>
                <input
                  type="url"
                  value={urlInput}
                  onChange={(e) => setUrlInput(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alt Text
                </label>
                <input
                  type="text"
                  value={urlAlt}
                  onChange={(e) => setUrlAlt(e.target.value)}
                  placeholder="Describe the image"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              {urlInput && (
                <div className="border border-gray-200 rounded-lg p-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Preview:</p>
                  <img
                    src={urlInput}
                    alt={urlAlt || 'Preview'}
                    className="max-w-full h-auto max-h-48 rounded"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
              )}
              <button
                onClick={handleInsertUrl}
                disabled={!urlInput.trim()}
                className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Insert Image
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageUploadModal;
