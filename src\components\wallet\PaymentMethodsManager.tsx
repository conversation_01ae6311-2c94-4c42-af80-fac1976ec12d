"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import {
  CreditCardIcon,
  BanknotesIcon,
  DevicePhoneMobileIcon,
  PlusIcon,
  TrashIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface PaymentMethod {
  id: string;
  type: 'bank' | 'mobile_banking' | 'card';
  name: string;
  details: {
    accountNumber?: string;
    accountName?: string;
    bankName?: string;
    branchName?: string;
    routingNumber?: string;
    mobileNumber?: string;
    provider?: string;
    cardNumber?: string;
    cardHolderName?: string;
  };
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
}

interface PaymentMethodsManagerProps {
  paymentMethods: PaymentMethod[];
  onUpdate: () => void;
  methodType: 'payment' | 'payout';
}

export function PaymentMethodsManager({ paymentMethods, onUpdate, methodType }: PaymentMethodsManagerProps) {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSetDefault = async (methodId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/wallet/payment-methods/${methodId}/default`, {
        method: 'PATCH',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Default payment method updated');
        onUpdate();
      } else {
        toast.error('Failed to update default method');
      }
    } catch (error) {
      console.error('Error setting default:', error);
      toast.error('Failed to update default method');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedMethod) return;

    try {
      setLoading(true);

      const response = await fetch(`/api/wallet/payment-methods/${selectedMethod.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Payment method deleted successfully');
        setShowDeleteModal(false);
        setSelectedMethod(null);
        onUpdate();
      } else {
        toast.error(data.message || 'Failed to delete payment method');
      }
    } catch (error) {
      console.error('Error deleting method:', error);
      toast.error('Failed to delete payment method');
    } finally {
      setLoading(false);
    }
  };

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'bank':
        return <BanknotesIcon className="h-6 w-6" />;
      case 'mobile_banking':
        return <DevicePhoneMobileIcon className="h-6 w-6" />;
      case 'card':
        return <CreditCardIcon className="h-6 w-6" />;
      default:
        return <CreditCardIcon className="h-6 w-6" />;
    }
  };

  const getMethodColor = (type: string) => {
    switch (type) {
      case 'bank':
        return 'text-blue-600 bg-blue-100';
      case 'mobile_banking':
        return 'text-green-600 bg-green-100';
      case 'card':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatMethodDetails = (method: PaymentMethod) => {
    switch (method.type) {
      case 'bank':
        return `${method.details.bankName} - ${method.details.accountNumber?.slice(-4)}`;
      case 'mobile_banking':
        return `${method.details.provider} - ${method.details.mobileNumber}`;
      case 'card':
        return `**** **** **** ${method.details.cardNumber?.slice(-4)}`;
      default:
        return method.name;
    }
  };

  const getEmptyStateContent = () => {
    if (methodType === 'payment') {
      return {
        icon: <CreditCardIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />,
        title: "No Payment Methods",
        description: "Add payment methods for deposits and app purchases"
      };
    } else {
      return {
        icon: <BanknotesIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />,
        title: "No Payout Methods",
        description: "Add payout methods to withdraw from your Earning Wallet"
      };
    }
  };

  if (paymentMethods.length === 0) {
    const emptyState = getEmptyStateContent();
    return (
      <div className="text-center py-12">
        {emptyState.icon}
        <h3 className="text-lg font-medium text-gray-900 mb-2">{emptyState.title}</h3>
        <p className="text-gray-600 mb-6">
          {emptyState.description}
        </p>
        <Button onClick={() => setShowAddModal(true)}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add {methodType === 'payment' ? 'Payment' : 'Payout'} Method
        </Button>

        <AddPaymentMethodModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            onUpdate();
          }}
          methodType={methodType}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Payment Methods List */}
      <div className="space-y-3">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`border rounded-lg p-4 transition-colors ${
              method.isDefault ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-white'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getMethodColor(method.type)}`}>
                  {getMethodIcon(method.type)}
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900">{method.name}</h4>
                    {method.isDefault && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Default
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{formatMethodDetails(method)}</p>
                  <p className="text-xs text-gray-500">
                    Added on {new Date(method.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {!method.isDefault && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSetDefault(method.id)}
                    disabled={loading}
                  >
                    Set Default
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('Delete button clicked for method:', method);
                    setSelectedMethod(method);
                    setShowDeleteModal(true);
                  }}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  disabled={loading}
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add New Button */}
      <Button
        variant="outline"
        onClick={() => setShowAddModal(true)}
        className="w-full"
      >
        <PlusIcon className="h-4 w-4 mr-2" />
        Add New {methodType === 'payment' ? 'Payment' : 'Payout'} Method
      </Button>

      {/* Modals */}
      <AddPaymentMethodModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          setShowAddModal(false);
          onUpdate();
        }}
        methodType={methodType}
      />

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onClose={() => setShowDeleteModal(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <DialogPanel className="mx-auto max-w-sm w-full bg-white rounded-xl shadow-2xl p-6">
            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            </div>
            <DialogTitle className="text-lg font-semibold text-gray-900 text-center mb-2">
              Delete {methodType === 'payment' ? 'Payment' : 'Payout'} Method
            </DialogTitle>
            <p className="text-sm text-gray-600 text-center mb-6">
              Are you sure you want to delete "{selectedMethod?.name}"? This action cannot be undone.
              {paymentMethods.length === 1 && (
                <span className="block mt-2 text-amber-600 font-medium">
                  ⚠️ This is your only {methodType} method. You'll need to add a new one to use {methodType === 'payment' ? 'deposit and purchase' : 'withdrawal'} features.
                </span>
              )}
            </p>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
                className="flex-1"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleDelete}
                className="flex-1 bg-red-600 hover:bg-red-700"
                disabled={loading}
              >
                {loading ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </DialogPanel>
        </div>
      </Dialog>
    </div>
  );
}

// Add Payment Method Modal Component
interface AddPaymentMethodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  methodType: 'payment' | 'payout';
}

function AddPaymentMethodModal({ isOpen, onClose, onSuccess, methodType }: AddPaymentMethodModalProps) {
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<'bank' | 'mobile_banking' | 'card'>('bank');
  const [formData, setFormData] = useState({
    name: '',
    accountNumber: '',
    accountName: '',
    bankName: '',
    branchName: '',
    routingNumber: '',
    mobileNumber: '',
    provider: '',
    cardNumber: '',
    cardHolderName: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/wallet/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: selectedType,
          name: formData.name,
          details: formData,
          methodType: methodType,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Payment method added successfully');
        onSuccess();
        setFormData({
          name: '',
          accountNumber: '',
          accountName: '',
          bankName: '',
          branchName: '',
          routingNumber: '',
          mobileNumber: '',
          provider: '',
          cardNumber: '',
          cardHolderName: '',
        });
      } else {
        toast.error(data.message || 'Failed to add payment method');
      }
    } catch (error) {
      console.error('Error adding payment method:', error);
      toast.error('Failed to add payment method');
    } finally {
      setLoading(false);
    }
  };

  const getPaymentTypes = () => {
    if (methodType === 'payment') {
      return [
        { id: 'card', name: 'Credit/Debit Card', icon: CreditCardIcon },
        { id: 'bank', name: 'Bank Account', icon: BanknotesIcon },
        { id: 'mobile_banking', name: 'Mobile Banking', icon: DevicePhoneMobileIcon },
      ] as const;
    } else {
      return [
        { id: 'bank', name: 'Bank Account', icon: BanknotesIcon },
        { id: 'mobile_banking', name: 'Mobile Banking', icon: DevicePhoneMobileIcon },
      ] as const;
    }
  };

  const paymentTypes = getPaymentTypes();

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full max-h-[95vh] bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Add {methodType === 'payment' ? 'Payment' : 'Payout'} Method
            </DialogTitle>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Payment Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Payment Method Type
                </label>
                <div className="grid grid-cols-1 gap-3">
                  {paymentTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <button
                        key={type.id}
                        type="button"
                        onClick={() => setSelectedType(type.id)}
                        className={`flex items-center p-3 border rounded-lg transition-colors ${
                          selectedType === type.id
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Icon className="h-5 w-5 mr-3" />
                        <span className="font-medium">{type.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Common Fields */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Method Name
                </label>
                <Input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., My Primary Bank Account"
                  required
                />
              </div>

              {/* Type-specific Fields */}
              {selectedType === 'bank' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bank Name
                    </label>
                    <Input
                      type="text"
                      value={formData.bankName}
                      onChange={(e) => setFormData(prev => ({ ...prev, bankName: e.target.value }))}
                      placeholder="e.g., Sonali Bank"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Name
                    </label>
                    <Input
                      type="text"
                      value={formData.accountName}
                      onChange={(e) => setFormData(prev => ({ ...prev, accountName: e.target.value }))}
                      placeholder="Account holder name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Number
                    </label>
                    <Input
                      type="text"
                      value={formData.accountNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, accountNumber: e.target.value }))}
                      placeholder="Bank account number"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Branch Name
                    </label>
                    <Input
                      type="text"
                      value={formData.branchName}
                      onChange={(e) => setFormData(prev => ({ ...prev, branchName: e.target.value }))}
                      placeholder="Branch name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Routing Number
                    </label>
                    <Input
                      type="text"
                      value={formData.routingNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, routingNumber: e.target.value }))}
                      placeholder="Bank routing number"
                    />
                  </div>
                </>
              )}

              {selectedType === 'mobile_banking' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Provider
                    </label>
                    <select
                      value={formData.provider}
                      onChange={(e) => setFormData(prev => ({ ...prev, provider: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">Select Provider</option>
                      <option value="bkash">bKash</option>
                      <option value="nagad">Nagad</option>
                      <option value="rocket">Rocket</option>
                      <option value="upay">Upay</option>
                      <option value="mcash">mCash</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mobile Number
                    </label>
                    <Input
                      type="tel"
                      value={formData.mobileNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, mobileNumber: e.target.value }))}
                      placeholder="e.g., 01712345678"
                      required
                    />
                  </div>
                </>
              )}

              {selectedType === 'card' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Card Holder Name
                    </label>
                    <Input
                      type="text"
                      value={formData.cardHolderName}
                      onChange={(e) => setFormData(prev => ({ ...prev, cardHolderName: e.target.value }))}
                      placeholder="Name on card"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Card Number
                    </label>
                    <Input
                      type="text"
                      value={formData.cardNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, cardNumber: e.target.value }))}
                      placeholder="**** **** **** ****"
                      maxLength={19}
                      required
                    />
                  </div>
                </>
              )}

              {/* Submit Button */}
              <div className="flex space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={loading}
                >
                  {loading ? 'Adding...' : 'Add Method'}
                </Button>
              </div>
            </form>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
