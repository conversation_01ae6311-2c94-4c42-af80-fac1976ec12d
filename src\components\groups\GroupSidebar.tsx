"use client";

import Image from "next/image";
import { UserIcon, BookOpenIcon } from "@heroicons/react/24/outline";

interface User {
  id: string;
  name: string | null;
  username: string | null;
  image: string | null;
}

interface GroupSidebarProps {
  group: {
    id: string;
    description: string | null;
    rules: string | null;
    creatorId: string;
  };
  creator: User | null;
  recentMembers: {
    user: User;
  }[];
  membersCount: number;
}

export function GroupSidebar({ group, creator, recentMembers, membersCount }: GroupSidebarProps) {
  return (
    <div className="lg:col-span-1 space-y-6">
      {/* About section */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="p-6">
          <h2 className="text-lg font-medium text-gray-900">About</h2>
          {group.description ? (
            <p className="mt-2 text-sm text-gray-600">{group.description}</p>
          ) : (
            <p className="mt-2 text-sm italic text-gray-400">No description provided</p>
          )}

          <div className="mt-4 flex items-center">
            <UserIcon className="mr-2 h-5 w-5 text-gray-400" />
            <span className="text-sm text-gray-600">
              Created by{" "}
              <a
                href={`/user/${creator?.username || group.creatorId}`}
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                {creator?.name || 'Unknown'}
              </a>
            </span>
          </div>

          {/* Rules are not yet implemented */}
          {group.rules && (
            <div className="mt-4 border-t border-gray-100 pt-4">
              <h3 className="flex items-center text-sm font-medium text-gray-900">
                <BookOpenIcon className="mr-2 h-5 w-5 text-gray-400" />
                Group Rules
              </h3>
              <p className="mt-2 text-sm text-gray-600">{group.rules}</p>
            </div>
          )}
        </div>
      </div>

      {/* Members section */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Members</h2>
            <a
              href={`/groups/${group.id}/members`}
              className="text-sm font-medium text-blue-600 hover:text-blue-500"
            >
              See all
            </a>
          </div>
          <p className="mt-1 text-sm text-gray-500">{membersCount} members</p>

          <div className="mt-4 grid grid-cols-3 gap-3">
            {recentMembers.slice(0, 6).map(({ user }) => (
              <a
                key={user.id}
                href={`/user/${user.username || user.id}`}
                className="flex flex-col items-center"
              >
                <div className="h-12 w-12 overflow-hidden rounded-full">
                  {user.image ? (
                    <Image
                      src={user.image}
                      alt={user.name || "Member"}
                      width={48}
                      height={48}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center bg-gray-200 text-gray-600">
                      {user.name?.charAt(0).toUpperCase() || "U"}
                    </div>
                  )}
                </div>
                <span className="mt-1 text-xs text-gray-700 truncate max-w-full">
                  {user.name}
                </span>
              </a>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
