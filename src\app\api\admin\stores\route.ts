import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, users, products, storeReviews, storeFollows, storeSettings } from "@/lib/db/schema";
import { z } from "zod";
import { desc, eq, and, or, like, count, inArray, avg } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

const querySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  search: z.string().optional(),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).optional(),
  verified: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

const storeSchema = z.object({
  name: z.string().min(2).max(255),
  slug: z.string().min(3).max(100).regex(/^[a-zA-Z0-9-]+$/),
  description: z.string().max(1000).optional().nullable(),
  location: z.string().max(255).optional().nullable(),
  phone: z.string().max(50).optional().nullable(),
  email: z.string().email().max(255).optional().nullable(),
  website: z.string().max(255).optional().nullable(),
  logo: z.string().url().optional().nullable(),
  banner: z.string().url().optional().nullable(),
  isVerified: z.boolean().optional(),
  ownerId: z.string(),
});

// Get all stores with admin filtering capabilities
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const validatedQuery = querySchema.parse(Object.fromEntries(searchParams));

    // Pagination
    const page = validatedQuery.page ? parseInt(validatedQuery.page) : 1;
    const limit = validatedQuery.limit ? parseInt(validatedQuery.limit) : 10;
    const offset = (page - 1) * limit;

    // Build query
    let query = db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        description: stores.description,
        logo: stores.logo,
        banner: stores.banner,
        location: stores.location,
        phone: stores.phone,
        email: stores.email,
        website: stores.website,
        isVerified: stores.isVerified,
        createdAt: stores.createdAt,
        updatedAt: stores.updatedAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(stores)
      .leftJoin(users, eq(stores.ownerId, users.id));

    // Apply search filter
    if (validatedQuery.search) {
      const searchTerm = `%${validatedQuery.search}%`;
      query = query.where(
        or(
          like(stores.name, searchTerm),
          like(stores.slug, searchTerm),
          like(stores.description || '', searchTerm),
          like(stores.location || '', searchTerm),
          like(users.name || '', searchTerm)
        )
      );
    }

    // Apply verified filter
    if (validatedQuery.verified === 'true') {
      query = query.where(eq(stores.isVerified, true));
    } else if (validatedQuery.verified === 'false') {
      query = query.where(eq(stores.isVerified, false));
    }

    // Apply date filters
    if (validatedQuery.dateFrom) {
      const dateFrom = new Date(validatedQuery.dateFrom);
      query = query.where(
        stores.createdAt >= dateFrom
      );
    }

    if (validatedQuery.dateTo) {
      const dateTo = new Date(validatedQuery.dateTo);
      query = query.where(
        stores.createdAt <= dateTo
      );
    }

    // Count total stores for pagination
    const countQuery = db
      .select({ value: count() })
      .from(stores)
      .leftJoin(users, eq(stores.ownerId, users.id));

    // Apply the same filters to count query
    if (validatedQuery.search) {
      const searchTerm = `%${validatedQuery.search}%`;
      countQuery.where(
        or(
          like(stores.name, searchTerm),
          like(stores.slug, searchTerm),
          like(stores.description || '', searchTerm),
          like(stores.location || '', searchTerm),
          like(users.name || '', searchTerm)
        )
      );
    }

    if (validatedQuery.verified === 'true') {
      countQuery.where(eq(stores.isVerified, true));
    } else if (validatedQuery.verified === 'false') {
      countQuery.where(eq(stores.isVerified, false));
    }

    if (validatedQuery.dateFrom) {
      const dateFrom = new Date(validatedQuery.dateFrom);
      countQuery.where(
        stores.createdAt >= dateFrom
      );
    }

    if (validatedQuery.dateTo) {
      const dateTo = new Date(validatedQuery.dateTo);
      countQuery.where(
        stores.createdAt <= dateTo
      );
    }

    // Apply sorting
    const sortField = validatedQuery.sort || 'createdAt';
    const sortOrder = validatedQuery.order || 'desc';

    switch (sortField) {
      case 'name':
        query = sortOrder === 'asc'
          ? query.orderBy(stores.name)
          : query.orderBy(desc(stores.name));
        break;
      case 'owner.name':
        query = sortOrder === 'asc'
          ? query.orderBy(users.name)
          : query.orderBy(desc(users.name));
        break;
      case 'isVerified':
        query = sortOrder === 'asc'
          ? query.orderBy(stores.isVerified)
          : query.orderBy(desc(stores.isVerified));
        break;
      case 'createdAt':
      default:
        query = sortOrder === 'asc'
          ? query.orderBy(stores.createdAt)
          : query.orderBy(desc(stores.createdAt));
        break;
    }

    // Apply pagination
    query = query.limit(limit).offset(offset);

    // Execute queries
    const [storesResult, countResult] = await Promise.all([
      query,
      countQuery
    ]);

    // Get additional store metrics
    const storeIds = storesResult.map(store => store.id);

    // Get product counts for each store
    const productCounts = storeIds.length > 0
      ? await db
          .select({
            storeId: products.storeId,
            count: count(),
          })
          .from(products)
          .where(inArray(products.storeId, storeIds))
          .groupBy(products.storeId)
      : [];

    // Get follower counts for each store
    const followerCounts = storeIds.length > 0
      ? await db
          .select({
            storeId: storeFollows.storeId,
            count: count(),
          })
          .from(storeFollows)
          .where(inArray(storeFollows.storeId, storeIds))
          .groupBy(storeFollows.storeId)
      : [];

    // Get average ratings for each store
    const ratings = storeIds.length > 0
      ? await db
          .select({
            storeId: storeReviews.storeId,
            averageRating: avg(storeReviews.rating),
            count: count(),
          })
          .from(storeReviews)
          .where(inArray(storeReviews.storeId, storeIds))
          .groupBy(storeReviews.storeId)
      : [];

    // Combine all data
    const storesWithMetrics = storesResult.map(store => {
      const productCount = productCounts.find(p => p.storeId === store.id)?.count || 0;
      const followerCount = followerCounts.find(f => f.storeId === store.id)?.count || 0;
      const rating = ratings.find(r => r.storeId === store.id);

      return {
        ...store,
        metrics: {
          productCount,
          followerCount,
          averageRating: rating?.averageRating || 0,
          reviewCount: rating?.count || 0,
        }
      };
    });

    // Calculate pagination info
    const totalStores = countResult[0]?.value || 0;
    const totalPages = Math.ceil(totalStores / limit);

    return NextResponse.json({
      stores: storesWithMetrics,
      pagination: {
        page,
        limit,
        totalStores,
        totalPages,
      }
    });
  } catch (error) {
    console.error("Error fetching stores:", error);
    return NextResponse.json(
      { message: "Error fetching stores" },
      { status: 500 }
    );
  }
}

// Create a new store as admin
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = storeSchema.parse(body);

    // Check if slug is already taken
    const existingStore = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.slug, validatedData.slug))
      .limit(1);

    if (existingStore.length > 0) {
      return NextResponse.json(
        { message: "Store slug already exists" },
        { status: 400 }
      );
    }

    // Check if owner exists
    const ownerExists = await db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.id, validatedData.ownerId))
      .limit(1);

    if (ownerExists.length === 0) {
      return NextResponse.json(
        { message: "Owner not found" },
        { status: 400 }
      );
    }

    // Create new store
    const storeId = uuidv4();
    await db.insert(stores).values({
      id: storeId,
      name: validatedData.name,
      slug: validatedData.slug,
      description: validatedData.description || null,
      location: validatedData.location || null,
      phone: validatedData.phone || null,
      email: validatedData.email || null,
      website: validatedData.website || null,
      logo: validatedData.logo || null,
      banner: validatedData.banner || null,
      ownerId: validatedData.ownerId,
      isVerified: validatedData.isVerified || false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Create default store settings
    await db.insert(storeSettings).values({
      id: uuidv4(),
      storeId: storeId,
      visibility: "public",
      showOutOfStock: true,
      showProductViews: true,
      emailNotifications: true,
      productViewNotifications: false,
    });

    // Get the created store
    const createdStore = await db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        description: stores.description,
        logo: stores.logo,
        banner: stores.banner,
        location: stores.location,
        phone: stores.phone,
        email: stores.email,
        website: stores.website,
        isVerified: stores.isVerified,
        createdAt: stores.createdAt,
        updatedAt: stores.updatedAt,
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(stores)
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(eq(stores.id, storeId))
      .limit(1);

    return NextResponse.json({
      message: "Store created successfully",
      store: createdStore[0],
    });
  } catch (error) {
    console.error("Error creating store:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Error creating store" },
      { status: 500 }
    );
  }
}

// Bulk delete stores
export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { storeIds } = body;

    if (!storeIds || !Array.isArray(storeIds) || storeIds.length === 0) {
      return NextResponse.json(
        { message: "No store IDs provided" },
        { status: 400 }
      );
    }

    // Check if any stores have products
    const storesWithProducts = await db
      .select({
        storeId: products.storeId,
        count: count(),
      })
      .from(products)
      .where(inArray(products.storeId, storeIds))
      .groupBy(products.storeId);

    if (storesWithProducts.length > 0) {
      return NextResponse.json(
        {
          message: "Cannot delete stores with existing products. Please delete all products first.",
          storesWithProducts: storesWithProducts.map(s => s.storeId)
        },
        { status: 400 }
      );
    }

    // Delete stores
    await db
      .delete(stores)
      .where(inArray(stores.id, storeIds));

    return NextResponse.json({
      message: `${storeIds.length} stores deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting stores:", error);
    return NextResponse.json(
      { message: "Error deleting stores" },
      { status: 500 }
    );
  }
}
