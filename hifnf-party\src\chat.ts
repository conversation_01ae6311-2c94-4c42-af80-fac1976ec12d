import type * as Party from "partykit/server";

interface ChatMessage {
  id: string;
  senderId: string;
  receiverId?: string;
  content: string;
  type: "direct" | "fanpage";
  fanPageId?: string;
  timestamp: string;
  status: "sending" | "sent" | "delivered" | "read";
}

interface TypingStatus {
  userId: string;
  conversationId: string;
  isTyping: boolean;
  timestamp: string;
}

interface ConnectionMeta {
  userId?: string;
  authenticated: boolean;
  lastSeen: number;
  activeConversations: Set<string>;
}

export default class ChatServer implements Party.Server {
  private connections = new Map<string, ConnectionMeta>();
  private userConnections = new Map<string, Set<string>>();
  private typingUsers = new Map<string, Set<string>>(); // conversationId -> Set of typing userIds
  private messageDeliveryQueue = new Map<string, ChatMessage[]>(); // userId -> pending messages

  constructor(readonly room: Party.Room) {}

  onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    console.log(`Chat server - New connection: ${conn.id} to room: ${this.room.id}`);
    
    this.connections.set(conn.id, {
      authenticated: false,
      lastSeen: Date.now(),
      activeConversations: new Set()
    });

    conn.send(JSON.stringify({
      type: "chat_server_ready",
      connectionId: conn.id,
      roomId: this.room.id,
      timestamp: new Date().toISOString()
    }));
  }

  onMessage(message: string, sender: Party.Connection) {
    try {
      const data = JSON.parse(message);
      const connMeta = this.connections.get(sender.id);
      
      if (!connMeta) return;
      
      connMeta.lastSeen = Date.now();

      switch (data.type) {
        case "auth":
          this.handleAuth(sender, data);
          break;
        
        case "send_message":
          if (connMeta.authenticated) {
            this.handleSendMessage(sender, data.message);
          }
          break;
        
        case "typing":
          if (connMeta.authenticated) {
            this.handleTyping(sender, data);
          }
          break;
        
        case "join_conversation":
          if (connMeta.authenticated) {
            this.handleJoinConversation(sender, data.conversationId);
          }
          break;
        
        case "leave_conversation":
          if (connMeta.authenticated) {
            this.handleLeaveConversation(sender, data.conversationId);
          }
          break;
        
        case "mark_read":
          if (connMeta.authenticated) {
            this.handleMarkRead(sender, data);
          }
          break;
        
        case "message_delivered":
          if (connMeta.authenticated) {
            this.handleMessageDelivered(sender, data);
          }
          break;
      }
    } catch (error) {
      console.error("Chat server error:", error);
      sender.send(JSON.stringify({ type: "error", message: "Invalid message format" }));
    }
  }

  onClose(conn: Party.Connection) {
    const connMeta = this.connections.get(conn.id);
    if (connMeta?.userId) {
      // Clear typing status for all conversations
      connMeta.activeConversations.forEach(conversationId => {
        this.clearTypingStatus(connMeta.userId!, conversationId);
      });
      
      // Remove from user connections
      const userConns = this.userConnections.get(connMeta.userId);
      if (userConns) {
        userConns.delete(conn.id);
        if (userConns.size === 0) {
          this.userConnections.delete(connMeta.userId);
        }
      }
    }
    
    this.connections.delete(conn.id);
  }

  private async handleAuth(conn: Party.Connection, message: any) {
    try {
      const userId = await this.verifyToken(message.token);
      
      if (userId) {
        const connMeta = this.connections.get(conn.id)!;
        connMeta.authenticated = true;
        connMeta.userId = userId;
        
        if (!this.userConnections.has(userId)) {
          this.userConnections.set(userId, new Set());
        }
        this.userConnections.get(userId)!.add(conn.id);
        
        conn.send(JSON.stringify({
          type: "chat_auth_success",
          userId,
          timestamp: new Date().toISOString()
        }));
        
        // Send any pending messages
        this.deliverPendingMessages(userId);
      } else {
        conn.send(JSON.stringify({
          type: "chat_auth_error",
          message: "Invalid token"
        }));
      }
    } catch (error) {
      console.error("Chat auth error:", error);
      conn.send(JSON.stringify({
        type: "chat_auth_error",
        message: "Authentication failed"
      }));
    }
  }

  private handleSendMessage(conn: Party.Connection, message: ChatMessage) {
    const connMeta = this.connections.get(conn.id)!;
    const timestamp = new Date().toISOString();
    
    const messageWithTimestamp = {
      ...message,
      timestamp,
      status: "sent" as const
    };
    
    // Send delivery confirmation to sender
    conn.send(JSON.stringify({
      type: "message_sent",
      messageId: message.id,
      timestamp
    }));
    
    if (message.type === "direct" && message.receiverId) {
      // Direct message
      const delivered = this.sendToUser(message.receiverId, {
        type: "new_message",
        message: messageWithTimestamp
      });
      
      if (!delivered) {
        // Queue message for offline user
        this.queueMessage(message.receiverId, messageWithTimestamp);
      }
    } else if (message.type === "fanpage" && message.fanPageId) {
      // Fan page message - broadcast to all connections in the room
      this.room.broadcast(JSON.stringify({
        type: "new_fanpage_message",
        message: messageWithTimestamp
      }), [conn.id]);
    }
  }

  private handleTyping(conn: Party.Connection, data: any) {
    const connMeta = this.connections.get(conn.id)!;
    const { conversationId, isTyping } = data;
    
    if (!connMeta.userId) return;
    
    if (isTyping) {
      this.setTypingStatus(connMeta.userId, conversationId);
    } else {
      this.clearTypingStatus(connMeta.userId, conversationId);
    }
    
    // Broadcast typing status to conversation participants
    this.broadcastToConversation(conversationId, {
      type: "user_typing",
      userId: connMeta.userId,
      conversationId,
      isTyping,
      timestamp: new Date().toISOString()
    }, [conn.id]);
  }

  private handleJoinConversation(conn: Party.Connection, conversationId: string) {
    const connMeta = this.connections.get(conn.id)!;
    connMeta.activeConversations.add(conversationId);
    
    conn.send(JSON.stringify({
      type: "conversation_joined",
      conversationId,
      timestamp: new Date().toISOString()
    }));
  }

  private handleLeaveConversation(conn: Party.Connection, conversationId: string) {
    const connMeta = this.connections.get(conn.id)!;
    connMeta.activeConversations.delete(conversationId);
    
    if (connMeta.userId) {
      this.clearTypingStatus(connMeta.userId, conversationId);
    }
    
    conn.send(JSON.stringify({
      type: "conversation_left",
      conversationId,
      timestamp: new Date().toISOString()
    }));
  }

  private handleMarkRead(conn: Party.Connection, data: any) {
    const connMeta = this.connections.get(conn.id)!;
    const { messageId, conversationId } = data;
    
    // Broadcast read status to conversation participants
    this.broadcastToConversation(conversationId, {
      type: "message_read",
      messageId,
      readBy: connMeta.userId,
      timestamp: new Date().toISOString()
    }, [conn.id]);
  }

  private handleMessageDelivered(conn: Party.Connection, data: any) {
    const { messageId } = data;
    
    // Notify sender that message was delivered
    this.room.broadcast(JSON.stringify({
      type: "message_delivered",
      messageId,
      timestamp: new Date().toISOString()
    }), [conn.id]);
  }

  private setTypingStatus(userId: string, conversationId: string) {
    if (!this.typingUsers.has(conversationId)) {
      this.typingUsers.set(conversationId, new Set());
    }
    this.typingUsers.get(conversationId)!.add(userId);
  }

  private clearTypingStatus(userId: string, conversationId: string) {
    const typingSet = this.typingUsers.get(conversationId);
    if (typingSet) {
      typingSet.delete(userId);
      if (typingSet.size === 0) {
        this.typingUsers.delete(conversationId);
      }
    }
  }

  private sendToUser(userId: string, message: any): boolean {
    const userConns = this.userConnections.get(userId);
    if (userConns && userConns.size > 0) {
      userConns.forEach(connId => {
        const conn = [...this.room.getConnections()].find(c => c.id === connId);
        if (conn) {
          conn.send(JSON.stringify(message));
        }
      });
      return true;
    }
    return false;
  }

  private queueMessage(userId: string, message: ChatMessage) {
    if (!this.messageDeliveryQueue.has(userId)) {
      this.messageDeliveryQueue.set(userId, []);
    }
    this.messageDeliveryQueue.get(userId)!.push(message);
  }

  private deliverPendingMessages(userId: string) {
    const pendingMessages = this.messageDeliveryQueue.get(userId);
    if (pendingMessages && pendingMessages.length > 0) {
      pendingMessages.forEach(message => {
        this.sendToUser(userId, {
          type: "new_message",
          message: {
            ...message,
            status: "delivered"
          }
        });
      });
      this.messageDeliveryQueue.delete(userId);
    }
  }

  private broadcastToConversation(conversationId: string, message: any, exclude: string[] = []) {
    // This is a simplified implementation
    // In a real app, you'd need to track conversation participants
    this.room.broadcast(JSON.stringify(message), exclude);
  }

  private async verifyToken(token: string): Promise<string | null> {
    try {
      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      
      if (payload.exp && payload.exp < Date.now() / 1000) {
        return null;
      }
      
      return payload.sub || payload.id || null;
    } catch (error) {
      console.error("Token verification error:", error);
      return null;
    }
  }
}
