"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  HomeIcon,
  MagnifyingGlassIcon,
  BellIcon,
  ChatBubbleLeftRightIcon,
  UserCircleIcon,
  PlusIcon
} from "@heroicons/react/24/outline";
import {
  HomeIcon as HomeSolidIcon,
  MagnifyingGlassIcon as SearchSolidIcon,
  BellIcon as BellSolidIcon,
  ChatBubbleLeftRightIcon as MessageSolidIcon,
  UserCircleIcon as UserSolidIcon
} from "@heroicons/react/24/solid";
import { cn } from "@/lib/utils";
import { useMobileView } from "@/hooks/useClientSide";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import { NotificationBadge } from "@/components/ui/NotificationBadge";
import { NotificationPreview } from "@/components/ui/NotificationPreview";
import { haptic } from "@/utils/haptics";

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  activeIcon: React.ComponentType<{ className?: string }>;
  badge?: number;
}

export function MobileBottomNav() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const isMobile = useMobileView();
  const [showNotificationPreview, setShowNotificationPreview] = useState(false);
  const [showMessagePreview, setShowMessagePreview] = useState(false);

  const {
    notifications,
    messages,
    notificationCounts,
    messageCounts,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    markMessageThreadAsRead,
    deleteNotification
  } = useNotificationCount();

  // Don't render if not mobile or user not authenticated
  if (!isMobile || !session) {
    return null;
  }

  const navItems: NavItem[] = [
    {
      name: "Home",
      href: "/",
      icon: HomeIcon,
      activeIcon: HomeSolidIcon,
    },
    {
      name: "Search",
      href: "/search",
      icon: MagnifyingGlassIcon,
      activeIcon: SearchSolidIcon,
    },
    {
      name: "Create",
      href: "/create",
      icon: PlusIcon,
      activeIcon: PlusIcon,
    },
    {
      name: "Notifications",
      href: "/notifications",
      icon: BellIcon,
      activeIcon: BellSolidIcon,
      badge: notificationCounts?.unread || 0,
    },
    {
      name: "Messages",
      href: "/messages",
      icon: ChatBubbleLeftRightIcon,
      activeIcon: MessageSolidIcon,
      badge: messageCounts?.total || 0,
    },
  ];

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  const handleCreateClick = () => {
    haptic.medium();
    // Scroll to top and focus on post form
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // Trigger post form expansion
    const event = new CustomEvent('expandPostForm');
    window.dispatchEvent(event);
  };

  return (
    <>
      {/* Spacer to prevent content from being hidden behind the nav */}
      <div className="h-16 sm:hidden" />
      
      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 sm:hidden">
        <div className="grid grid-cols-5 h-16">
          {navItems.map((item) => {
            const active = isActive(item.href);
            const IconComponent = active ? item.activeIcon : item.icon;
            
            if (item.name === "Create") {
              return (
                <button
                  key={item.name}
                  onClick={handleCreateClick}
                  className="flex flex-col items-center justify-center p-2 transition-colors duration-200 active:bg-gray-100"
                >
                  <div className="relative">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <IconComponent className="h-5 w-5 text-white" />
                    </div>
                  </div>
                </button>
              );
            }

            const handleItemClick = (e: React.MouseEvent) => {
              haptic.light();

              // Handle special cases for notifications and messages
              if (item.name === "Notifications" && item.badge && item.badge > 0) {
                e.preventDefault();
                setShowNotificationPreview(true);
                return;
              }

              if (item.name === "Messages" && item.badge && item.badge > 0) {
                e.preventDefault();
                setShowMessagePreview(true);
                return;
              }
            };

            return (
              <Link
                key={item.name}
                href={item.href}
                className="flex flex-col items-center justify-center p-2 transition-colors duration-200 active:bg-gray-100"
                onClick={handleItemClick}
              >
                <NotificationBadge
                  count={item.badge || 0}
                  variant={item.name === "Messages" ? "messages" :
                          (notificationCounts?.priority?.high || 0) > 0 ? "high-priority" : "default"}
                  animate={true}
                  onClick={item.badge && item.badge > 0 ? handleItemClick : undefined}
                >
                  <IconComponent
                    className={cn(
                      "h-6 w-6 transition-colors duration-200",
                      active ? "text-blue-600" : "text-gray-600"
                    )}
                  />
                </NotificationBadge>
              </Link>
            );
          })}
        </div>
      </nav>

      {/* Notification Preview Modal */}
      <NotificationPreview
        isOpen={showNotificationPreview}
        onClose={() => setShowNotificationPreview(false)}
        notifications={notifications}
        messages={messages}
        onMarkAsRead={markNotificationAsRead}
        onMarkAllAsRead={markAllNotificationsAsRead}
        onDeleteNotification={deleteNotification}
        onMarkMessageAsRead={markMessageThreadAsRead}
        type="notifications"
      />

      {/* Message Preview Modal */}
      <NotificationPreview
        isOpen={showMessagePreview}
        onClose={() => setShowMessagePreview(false)}
        notifications={notifications}
        messages={messages}
        onMarkAsRead={markNotificationAsRead}
        onMarkAllAsRead={markAllNotificationsAsRead}
        onDeleteNotification={deleteNotification}
        onMarkMessageAsRead={markMessageThreadAsRead}
        type="messages"
      />
    </>
  );
}
