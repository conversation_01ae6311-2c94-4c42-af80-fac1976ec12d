"use client";

import { useState, useEffect } from "react";

interface UserBadgeData {
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor?: string;
  customBadgeUrl?: string;
  planName?: string;
  badgePriority?: number;
}

interface UseUserBadgeOptions {
  userId: string;
  enabled?: boolean;
}

export function useUserBadge({ userId, enabled = true }: UseUserBadgeOptions) {
  const [badgeData, setBadgeData] = useState<UserBadgeData>({ badgeType: 'none' });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!enabled || !userId) {
      setLoading(false);
      return;
    }

    const fetchUserBadge = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/users/${userId}/badge`);
        
        if (!response.ok) {
          if (response.status === 404) {
            // User has no active subscription
            setBadgeData({ badgeType: 'none' });
            return;
          }
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.success) {
          setBadgeData(data.badge || { badgeType: 'none' });
        } else {
          setBadgeData({ badgeType: 'none' });
        }
      } catch (err) {
        console.error('Error fetching user badge:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        setBadgeData({ badgeType: 'none' });
      } finally {
        setLoading(false);
      }
    };

    fetchUserBadge();
  }, [userId, enabled]);

  return {
    badgeData,
    loading,
    error,
  };
}

// Cache for user badges to avoid repeated API calls
const badgeCache = new Map<string, { data: UserBadgeData; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export function useUserBadgeWithCache({ userId, enabled = true }: UseUserBadgeOptions) {
  const [badgeData, setBadgeData] = useState<UserBadgeData>({ badgeType: 'none' });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!enabled || !userId) {
      setLoading(false);
      return;
    }

    const fetchUserBadge = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check cache first
        const cached = badgeCache.get(userId);
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
          setBadgeData(cached.data);
          setLoading(false);
          return;
        }

        const response = await fetch(`/api/users/${userId}/badge`);
        
        if (!response.ok) {
          if (response.status === 404) {
            const noBadge = { badgeType: 'none' as const };
            setBadgeData(noBadge);
            badgeCache.set(userId, { data: noBadge, timestamp: Date.now() });
            return;
          }
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const badge = data.success ? (data.badge || { badgeType: 'none' }) : { badgeType: 'none' };
        
        setBadgeData(badge);
        badgeCache.set(userId, { data: badge, timestamp: Date.now() });
      } catch (err) {
        console.error('Error fetching user badge:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        setBadgeData({ badgeType: 'none' });
      } finally {
        setLoading(false);
      }
    };

    fetchUserBadge();
  }, [userId, enabled]);

  return {
    badgeData,
    loading,
    error,
  };
}

// Clear cache for a specific user (useful when subscription changes)
export function clearUserBadgeCache(userId: string) {
  badgeCache.delete(userId);
}

// Clear all badge cache
export function clearAllBadgeCache() {
  badgeCache.clear();
}
