"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
  UserIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  UserMinusIcon
} from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";
import { formatDistanceToNow } from "date-fns";
import { unsubscribeFromUser } from "@/app/actions/subscription";

interface Following {
  id: string;
  targetUserId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
}

interface FollowingResponse {
  subscriptions: Following[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

interface FollowingListProps {
  searchQuery?: string;
  onCountChange?: () => void;
}

export function FollowingList({ searchQuery = "", onCountChange }: FollowingListProps) {
  const { data: session } = useSession();
  const [following, setFollowing] = useState<Following[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [pagination, setPagination] = useState({
    page: 1,
    hasMore: true
  });
  const [unsubscribingUsers, setUnsubscribingUsers] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (session?.user) {
      fetchFollowing();
    }
  }, [session]);

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
    if (searchQuery !== localSearchQuery) {
      fetchFollowing(1, false);
    }
  }, [searchQuery]);

  const fetchFollowing = async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const params = new URLSearchParams({
        type: 'subscriptions',
        limit: '20',
        offset: ((page - 1) * 20).toString(),
        ...(localSearchQuery && { search: localSearchQuery })
      });

      const response = await fetch(`/api/subscriptions?${params}`);

      if (!response.ok) {
        throw new Error('Failed to fetch following');
      }

      const data: FollowingResponse = await response.json();

      if (append) {
        setFollowing(prev => [...prev, ...data.subscriptions]);
      } else {
        setFollowing(data.subscriptions);
      }

      setPagination({
        page: data.pagination.page,
        hasMore: data.pagination.hasMore
      });
    } catch (error) {
      console.error('Error fetching following:', error);
      toast.error('Failed to load following');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreFollowing = () => {
    if (pagination.hasMore && !loadingMore) {
      fetchFollowing(pagination.page + 1, true);
    }
  };

  const handleUnsubscribe = async (targetUserId: string, name: string) => {
    if (!session?.user) return;

    setUnsubscribingUsers(prev => new Set(prev).add(targetUserId));

    try {
      const result = await unsubscribeFromUser(targetUserId);

      if (result.success) {
        // Remove from local state
        setFollowing(prev => prev.filter(sub => sub.targetUserId !== targetUserId));
        toast.success(`Unsubscribed from ${name}`);
        // Update parent counts
        onCountChange?.();
      } else {
        toast.error(result.message || 'Failed to unsubscribe');
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
      toast.error('Failed to unsubscribe');
    } finally {
      setUnsubscribingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(targetUserId);
        return newSet;
      });
    }
  };

  const handleStartChat = (following: Following) => {
    // This will be implemented when chat functionality is added
    toast.success(`Starting chat with ${following.name}`);
  };

  // Filter following based on search query
  const filteredFollowing = following.filter(follow =>
    follow.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
    follow.username.toLowerCase().includes(localSearchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg animate-pulse">
            <div className="h-16 w-16 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
            <div className="flex space-x-2">
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (filteredFollowing.length === 0) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {localSearchQuery ? 'No subscriptions found' : 'Not subscribed to anyone yet'}
        </h3>
        <p className="text-gray-600 mb-4">
          {localSearchQuery
            ? `No subscriptions match "${localSearchQuery}"`
            : "Start subscribing to people to see them here."
          }
        </p>
        {!localSearchQuery && (
          <Link
            href="/connection?tab=suggestions"
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <UserIcon className="h-4 w-4 mr-2" />
            Discover People
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {filteredFollowing.map((follow) => (
        <div key={follow.id} className="group relative bg-gradient-to-br from-white to-emerald-50/30 border border-emerald-100 rounded-2xl p-6 hover:shadow-2xl hover:border-emerald-300 transition-all duration-500 overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-emerald-100/20 rounded-full -translate-y-16 translate-x-16 group-hover:scale-150 transition-transform duration-700"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-green-100/20 rounded-full translate-y-12 -translate-x-12 group-hover:scale-150 transition-transform duration-700"></div>

          <div className="relative flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
            {/* Enhanced Profile Section */}
            <div className="flex items-center space-x-4 flex-1">
              <div className="relative">
                <div className="h-16 w-16 sm:h-20 sm:w-20 rounded-2xl ring-4 ring-emerald-100 group-hover:ring-emerald-200 transition-all duration-300 overflow-hidden shadow-lg">
                  {follow.image ? (
                    <OptimizedImage
                      src={follow.image}
                      alt={follow.name}
                      width={80}
                      height={80}
                      className="rounded-2xl object-cover w-full h-full group-hover:scale-110 transition-transform duration-300"
                    />
                  ) : (
                    <div className="h-full w-full bg-gradient-to-br from-emerald-100 to-green-100 rounded-2xl flex items-center justify-center group-hover:from-emerald-200 group-hover:to-green-200 transition-colors duration-300">
                      <UserIcon className="h-8 w-8 sm:h-10 sm:w-10 text-emerald-600" />
                    </div>
                  )}
                </div>
                <div className="absolute -bottom-1 -right-1 h-5 w-5 bg-emerald-400 border-3 border-white rounded-full animate-pulse shadow-lg"></div>
                <div className="absolute -top-1 -left-1 h-4 w-4 bg-green-400 border-2 border-white rounded-full shadow-md"></div>
              </div>

              <div className="flex-1 min-w-0">
                <Link
                  href={`/user/${follow.username}`}
                  className="text-xl font-bold text-gray-900 hover:text-emerald-600 transition-colors duration-200 block truncate group-hover:text-emerald-600"
                >
                  {follow.name}
                </Link>
                <p className="text-sm text-gray-500 truncate font-medium">@{follow.username}</p>
                <div className="flex items-center gap-3 mt-3">
                  <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-800 border border-emerald-200 shadow-sm">
                    ✓ Subscribed
                  </span>
                  <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full font-medium">
                    {formatDistanceToNow(new Date(follow.createdAt), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>

            {/* Premium Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:min-w-[320px]">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleStartChat(follow)}
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 h-11 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-indigo-100 hover:border-blue-300 transition-all duration-300 shadow-sm hover:shadow-md"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
                <span>Message</span>
              </Button>

              <Link
                href={`/user/${follow.username}`}
                className="flex-1 sm:flex-none"
              >
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full h-11 flex items-center justify-center gap-2 bg-gradient-to-r from-gray-50 to-slate-50 border-gray-200 text-gray-700 hover:from-gray-100 hover:to-slate-100 hover:border-gray-300 transition-all duration-300 shadow-sm hover:shadow-md"
                >
                  <UserIcon className="h-4 w-4" />
                  <span>Profile</span>
                </Button>
              </Link>

              <Button
                size="sm"
                variant="outline"
                onClick={() => handleUnsubscribe(follow.targetUserId, follow.name)}
                disabled={unsubscribingUsers.has(follow.targetUserId)}
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 h-11 bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-700 hover:from-red-100 hover:to-rose-100 hover:border-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-sm hover:shadow-md"
              >
                <UserMinusIcon className="h-4 w-4" />
                <span>
                  {unsubscribingUsers.has(follow.targetUserId) ? 'Unsubscribing...' : 'Unsubscribe'}
                </span>
              </Button>
            </div>
          </div>
        </div>
      ))}

      {/* Load More Button */}
      {pagination.hasMore && (
        <div className="text-center pt-4">
          <Button
            variant="outline"
            onClick={loadMoreFollowing}
            disabled={loadingMore}
            className="px-6 sm:px-8"
          >
            {loadingMore ? 'Loading...' : 'Load More Subscriptions'}
          </Button>
        </div>
      )}
    </div>
  );
}
