"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";

interface FanPageMessage {
  id: string;
  content: string;
  read: boolean;
  createdAt: string;
  sender: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface UseFanPageMessagesProps {
  pageId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useFanPageMessages({ 
  pageId, 
  autoRefresh = false, 
  refreshInterval = 30000 
}: UseFanPageMessagesProps) {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<FanPageMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMessages = useCallback(async () => {
    if (!session?.user || !pageId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/fan-pages/${pageId}/messages`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.statusText}`);
      }

      const data = await response.json();
      setMessages(data.messages || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch messages");
      console.error("Error fetching fan page messages:", err);
    } finally {
      setIsLoading(false);
    }
  }, [session?.user, pageId]);

  const sendMessage = useCallback(async (content: string) => {
    if (!session?.user || !pageId || !content.trim()) return;

    try {
      const response = await fetch(`/api/fan-pages/${pageId}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: content.trim() }),
      });

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Add new message to the beginning of the array
      setMessages(prev => [data.data, ...prev]);
      
      return data.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send message");
      console.error("Error sending fan page message:", err);
      throw err;
    }
  }, [session?.user, pageId]);

  const markAsRead = useCallback(async () => {
    if (!session?.user || !pageId) return;

    try {
      const response = await fetch(`/api/fan-pages/${pageId}/messages`, {
        method: "PUT",
      });

      if (!response.ok) {
        throw new Error(`Failed to mark messages as read: ${response.statusText}`);
      }

      // Update local state to mark all messages as read
      setMessages(prev => prev.map(msg => ({ ...msg, read: true })));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to mark messages as read");
      console.error("Error marking messages as read:", err);
    }
  }, [session?.user, pageId]);

  const addMessage = useCallback((message: FanPageMessage) => {
    setMessages(prev => [message, ...prev]);
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchMessages();
  }, [fetchMessages]);

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchMessages, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchMessages]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    markAsRead,
    addMessage,
    refetch: fetchMessages,
  };
}
