"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { PostCard } from "@/components/feed/PostCard";
import { LockClosedIcon } from "@heroicons/react/24/outline";

interface User {
  id: string;
  name: string | null;
  username: string | null;
  image: string | null;
}

interface Group {
  id: string;
  name: string;
  slug: string;
  profileImage: string | null;
  isPrivate: boolean;
}

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  type: 'group_post';
  user: User;
  group: Group;
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
}

interface GroupPostsListProps {
  groupId: string;
  groupName: string;
  posts: Post[];
  canViewPosts: boolean;
  isMember: boolean;
  isPublic: boolean;
}

export function GroupPostsList({
  groupId,
  groupName,
  posts,
  canViewPosts,
  isMember,
  isPublic
}: GroupPostsListProps) {
  const [likedPosts, setLikedPosts] = useState<Set<string>>(new Set());
  const [dislikedPosts, setDislikedPosts] = useState<Set<string>>(new Set());

  if (!canViewPosts) {
    return (
      <div className="rounded-lg bg-white p-8 text-center shadow">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
          <LockClosedIcon className="h-8 w-8 text-blue-600" />
        </div>
        <p className="mt-4 text-sm text-gray-500">
          This is a private group. Join to see posts and discussions.
        </p>
        <div className="mt-4">
          <Button>
            {isPublic ? "Join Group" : "Request to Join"}
          </Button>
        </div>
      </div>
    );
  }

  if (posts.length === 0) {
    return (
      <div className="rounded-lg bg-white p-8 text-center shadow">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
          <LockClosedIcon className="h-8 w-8 text-blue-600" />
        </div>
        <p className="mt-4 text-sm text-gray-500">
          No posts in this group yet.
        </p>
        {isMember && (
          <p className="mt-2 text-sm text-gray-500">
            Be the first to post something!
          </p>
        )}
      </div>
    );
  }

  const handleLike = async (postId: string) => {
    try {
      const response = await fetch(`/api/posts/${postId}/like`, {
        method: "POST",
      });

      if (response.ok) {
        setLikedPosts(prev => {
          const newSet = new Set(prev);
          if (newSet.has(postId)) {
            newSet.delete(postId);
          } else {
            newSet.add(postId);
          }
          return newSet;
        });
        setDislikedPosts(prev => {
          const newSet = new Set(prev);
          newSet.delete(postId);
          return newSet;
        });
      }
    } catch (error) {
      console.error("Error liking post:", error);
    }
  };

  const handleDislike = async (postId: string) => {
    try {
      const response = await fetch(`/api/posts/${postId}/dislike`, {
        method: "POST",
      });

      if (response.ok) {
        setDislikedPosts(prev => {
          const newSet = new Set(prev);
          if (newSet.has(postId)) {
            newSet.delete(postId);
          } else {
            newSet.add(postId);
          }
          return newSet;
        });
        setLikedPosts(prev => {
          const newSet = new Set(prev);
          newSet.delete(postId);
          return newSet;
        });
      }
    } catch (error) {
      console.error("Error disliking post:", error);
    }
  };

  return (
    <div className="space-y-5">
      {posts.map((post) => (
        <div key={post.id} className="transform transition-all duration-300 hover:translate-y-[-2px]">
          <PostCard
            post={{
              ...post,
              liked: likedPosts.has(post.id) || post.liked,
              disliked: dislikedPosts.has(post.id) || post.disliked
            }}
            onLike={() => handleLike(post.id)}
            onDislike={() => handleDislike(post.id)}
          />
        </div>
      ))}
    </div>
  );
}
