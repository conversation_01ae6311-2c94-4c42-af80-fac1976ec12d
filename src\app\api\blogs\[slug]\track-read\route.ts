import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { MonetizationService } from "@/lib/monetization/monetizationService";
import { z } from "zod";
import { headers } from "next/headers";

const trackReadSchema = z.object({
  readDuration: z.number().min(0).max(3600), // Max 1 hour
  sessionId: z.string().min(1),
  referrer: z.string().optional(),
});

// Track blog read
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;
    const session = await getServerSession(authOptions);
    const headersList = headers();

    // Get blog
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: {
        id: true,
        title: true,
        status: true,
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Only track reads for published blogs
    if (blog.status !== 'published') {
      return NextResponse.json(
        { message: "Cannot track reads for unpublished blogs" },
        { status: 400 }
      );
    }

    const body = await req.json();
    const validatedData = trackReadSchema.parse(body);

    // Get client IP and user agent
    const forwardedFor = headersList.get('x-forwarded-for');
    const realIp = headersList.get('x-real-ip');
    const ipAddress = forwardedFor?.split(',')[0] || realIp || '127.0.0.1';
    const userAgent = headersList.get('user-agent') || '';

    // Track the read
    await MonetizationService.trackRead({
      blogId: blog.id,
      userId: session?.user?.id,
      ipAddress,
      userAgent,
      sessionId: validatedData.sessionId,
      referrer: validatedData.referrer,
      readDuration: validatedData.readDuration,
    });

    return NextResponse.json({
      success: true,
      message: "Read tracked successfully",
    });
  } catch (error: any) {
    console.error("Error tracking blog read:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to track read" 
      },
      { status: 500 }
    );
  }
}
