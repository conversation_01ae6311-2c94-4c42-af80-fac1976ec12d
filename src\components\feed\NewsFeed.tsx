"use client";

import { useState, useEffect, useCallback, memo, useMemo } from "react";
import { useSession } from "next-auth/react";
import { PostCard } from "./PostCard";

import { Spinner } from "@/components/ui/Spinner";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";
import { InfiniteScrollLoader } from "@/components/ui/InfiniteScrollLoader";
import { PullToRefresh } from "@/components/ui/PullToRefresh";
import eventBus from "@/lib/eventBus";
import { FeedErrorBoundary } from "@/components/error/ErrorBoundary";
import { ProductsCarousel } from "@/components/marketplace/ProductsCarousel";
import { useFeaturedProducts } from "@/hooks/useFeaturedProducts";

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  type: 'user_post' | 'fan_page_post' | 'blog_post';
  user: {
    id: string;
    name: string;
    image: string | null;
  } | null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  } | null;
  blog?: {
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    coverImage?: string | null;
    readTime?: number | null;
    viewCount?: number;
    category?: {
      id: string;
      name: string;
      color: string;
    } | null;
    tags?: string[] | null;
    featured?: boolean;
  } | null;
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
  sharedPost?: {
    id: string;
    content: string;
    images: string[] | null;
    videos: string[] | null;
    backgroundColor: string | null;
    feeling: string | null;
    activity: string | null;
    location: string | null;
    formattedContent: boolean | null;
    createdAt: string;
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  } | null;
}

// Memoized PostCard component to prevent unnecessary re-renders
const MemoizedPostCard = memo(PostCard);

export function NewsFeed() {
  const { data: session } = useSession();

  // Fetch featured products for carousel
  const {
    products: featuredProducts,
    loading: productsLoading
  } = useFeaturedProducts({
    limit: 8,
    enabled: !!session?.user
  });



  // Fetch function for infinite scroll
  const fetchFeedData = useCallback(async (page: number, limit: number) => {
    try {
      const response = await fetch(`/api/feed?page=${page}&limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Feed API Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        throw new Error(`Failed to fetch feed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      // Validate response structure
      if (!result || typeof result !== 'object') {
        throw new Error('Invalid response format from feed API');
      }

      return {
        data: Array.isArray(result.data) ? result.data : [],
        pagination: result.pagination || {
          page,
          limit,
          total: 0,
          hasMore: false,
        },
      };
    } catch (error) {
      console.error('Error in fetchFeedData:', error);
      throw error;
    }
  }, []);

  // Use infinite scroll hook
  const {
    data: infiniteScrollPosts,
    loading,
    loadingMore,
    error,
    hasMore,
    refresh,
    loadMoreRef,
  } = useInfiniteScroll<Post>({
    fetchFunction: fetchFeedData,
    limit: 10,
    enabled: !!session?.user,
    dependencies: [session?.user?.id],
    onError: (err) => console.error("Feed error:", err),
    throttleMs: 300,
    rootMargin: "100px",
  });

  // Local state for optimistic updates
  const [localPosts, setLocalPosts] = useState<Post[]>([]);

  // Sync local posts with infinite scroll data
  useEffect(() => {
    setLocalPosts(infiniteScrollPosts);
  }, [infiniteScrollPosts]);

  // Use local posts for rendering
  const posts = localPosts;

  // Listen for post-created, post-updated, and post-deleted events
  useEffect(() => {
    // Handler for post-created event
    const handlePostCreated = () => {
      refresh();
    };

    // Handler for post-updated event
    const handlePostUpdated = (postId: string) => {
      // Refresh all posts to get the updated content
      refresh();
    };

    // Handler for post-deleted event
    const handlePostDeleted = (postId: string) => {
      // For now, just refresh the entire feed
      // TODO: Implement optimistic update to remove specific post
      refresh();
    };

    // Subscribe to the events
    eventBus.on('post-created', handlePostCreated);
    eventBus.on('post-updated', handlePostUpdated);
    eventBus.on('post-deleted', handlePostDeleted);

    // Cleanup: unsubscribe when component unmounts
    return () => {
      eventBus.off('post-created', handlePostCreated);
      eventBus.off('post-updated', handlePostUpdated);
      eventBus.off('post-deleted', handlePostDeleted);
    };
  }, [refresh]);

  const handleLike = async (postId: string) => {
    // Find the post to determine if it's a user post or fan page post
    const post = posts.find(p => p.id === postId);
    if (!post) return;

    // Store original state for rollback
    const originalState = {
      liked: post.liked,
      disliked: post.disliked,
      likeCount: post._count.likes,
      dislikeCount: post._count.dislikes
    };

    // Optimistic update
    const newLiked = !post.liked;
    const wasDisliked = post.disliked;

    setLocalPosts((prevPosts) =>
      prevPosts.map((p) => {
        if (p.id === postId) {
          return {
            ...p,
            liked: newLiked,
            disliked: wasDisliked ? false : p.disliked,
            _count: {
              ...p._count,
              likes: p._count.likes + (newLiked ? 1 : -1),
              dislikes: wasDisliked ? p._count.dislikes - 1 : p._count.dislikes,
            },
          };
        }
        return p;
      })
    );

    try {
      // Use different API endpoints for different post types
      let apiUrl: string;

      if (post.type === 'fan_page_post') {
        apiUrl = `/api/fan-pages/posts/${postId}/like`;
      } else if (post.type === 'blog_post' && post.blog?.slug) {
        apiUrl = `/api/blogs/${post.blog.slug}/like`;
      } else {
        apiUrl = `/api/posts/${postId}/like`;
      }

      const response = await fetch(apiUrl, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to like post");
      }
    } catch (error) {
      console.error("Error liking post:", error);

      // Rollback optimistic update on error
      setLocalPosts((prevPosts) =>
        prevPosts.map((p) => {
          if (p.id === postId) {
            return {
              ...p,
              liked: originalState.liked,
              disliked: originalState.disliked,
              _count: {
                ...p._count,
                likes: originalState.likeCount,
                dislikes: originalState.dislikeCount,
              },
            };
          }
          return p;
        })
      );

      // Re-throw error to be handled by usePostCard hook
      throw error;
    }
  };

  const handleDislike = async (postId: string) => {
    // Find the post to determine if it's a user post or fan page post
    const post = posts.find(p => p.id === postId);
    if (!post) return;

    // Store original state for rollback
    const originalState = {
      liked: post.liked,
      disliked: post.disliked,
      likeCount: post._count.likes,
      dislikeCount: post._count.dislikes
    };

    // Optimistic update
    const newDisliked = !post.disliked;
    const wasLiked = post.liked;

    setLocalPosts((prevPosts) =>
      prevPosts.map((p) => {
        if (p.id === postId) {
          return {
            ...p,
            disliked: newDisliked,
            liked: wasLiked ? false : p.liked,
            _count: {
              ...p._count,
              dislikes: p._count.dislikes + (newDisliked ? 1 : -1),
              likes: wasLiked ? p._count.likes - 1 : p._count.likes,
            },
          };
        }
        return p;
      })
    );

    try {
      // Use different API endpoints for different post types
      let apiUrl: string;

      if (post.type === 'fan_page_post') {
        apiUrl = `/api/fan-pages/posts/${postId}/dislike`;
      } else if (post.type === 'blog_post' && post.blog?.slug) {
        apiUrl = `/api/blogs/${post.blog.slug}/dislike`;
      } else {
        apiUrl = `/api/posts/${postId}/dislike`;
      }

      const response = await fetch(apiUrl, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to dislike post");
      }
    } catch (error) {
      console.error("Error disliking post:", error);

      // Rollback optimistic update on error
      setLocalPosts((prevPosts) =>
        prevPosts.map((p) => {
          if (p.id === postId) {
            return {
              ...p,
              liked: originalState.liked,
              disliked: originalState.disliked,
              _count: {
                ...p._count,
                likes: originalState.likeCount,
                dislikes: originalState.dislikeCount,
              },
            };
          }
          return p;
        })
      );

      // Re-throw error to be handled by usePostCard hook
      throw error;
    }
  };

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    await refresh();
  }, [refresh]);

  if (!session?.user) {
    return (
      <div className="rounded-lg p-8 text-center shadow">
        <p className="text-gray-500">
          Please sign in to view your news feed.
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-6 text-center">
        <p className="text-red-700">{error}</p>
        <button
          className="mt-4 rounded-md bg-red-100 px-4 py-2 text-sm font-medium text-red-700 hover:bg-red-200"
          onClick={refresh}
        >
          Try Again
        </button>
      </div>
    );
  }

  if (posts.length === 0 && !loading) {
    return (
      <div className="rounded-lg p-8 text-center shadow">
        <p className="text-gray-500">
          No posts yet. Be the first to post something!
        </p>
      </div>
    );
  }

  return (
    <FeedErrorBoundary>
      <PullToRefresh onRefresh={handleRefresh}>
        <div className="space-y-3 sm:space-y-5">
          {/* Show marketplace carousel at the top if we have products */}
          {featuredProducts.length > 0 && (
            <div className="content-block">
              <ProductsCarousel
                title="🛍️ Featured Products"
                subtitle="Discover amazing deals from our marketplace"
                products={featuredProducts}
                loading={productsLoading}
                showViewAll={true}
                viewAllLink="/marketplace"
                className="transform transition-all duration-300 hover:translate-y-[-2px]"
              />
            </div>
          )}



          {posts.map((post, index) => (
            <div key={post.id} className="content-block">
              <MemoizedPostCard
                post={post}
                onLike={() => handleLike(post.id)}
                onDislike={() => handleDislike(post.id)}
              />
            </div>
          ))}

          {/* Infinite Scroll Loader */}
          <InfiniteScrollLoader
            loading={loadingMore}
            hasMore={hasMore}
            loadMoreRef={loadMoreRef}
          />
        </div>
      </PullToRefresh>
    </FeedErrorBoundary>
  );
}
