import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  users,
  posts,
  blogs,
  groups,
  fanPages,
  products,
  events,
  reports,
  walletTransactions,
  subscriptions,
  subscriptionPlans,
  referrals,
  blogMonetization,
  blogEarnings,
  comments,
  likes,
  blogComments,
  blogLikes
} from "@/lib/db/schema";
import { eq, and, sql, desc, gte, lte, count, sum, isNotNull } from "drizzle-orm";

// Get comprehensive admin dashboard statistics
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get date ranges for calculations
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // User Statistics
    const totalUsersResult = await db
      .select({ count: count() })
      .from(users);

    const activeUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(gte(users.lastLoginAt, sevenDaysAgo));

    const newUsersThisMonthResult = await db
      .select({ count: count() })
      .from(users)
      .where(gte(users.createdAt, startOfMonth));

    const newUsersLastMonthResult = await db
      .select({ count: count() })
      .from(users)
      .where(
        and(
          gte(users.createdAt, startOfLastMonth),
          lte(users.createdAt, endOfLastMonth)
        )
      );

    // Content Statistics
    const totalPostsResult = await db
      .select({ count: count() })
      .from(posts);

    const totalBlogsResult = await db
      .select({ count: count() })
      .from(blogs);

    const publishedBlogsResult = await db
      .select({ count: count() })
      .from(blogs)
      .where(eq(blogs.status, 'published'));

    const postsThisMonthResult = await db
      .select({ count: count() })
      .from(posts)
      .where(gte(posts.createdAt, startOfMonth));

    const postsLastMonthResult = await db
      .select({ count: count() })
      .from(posts)
      .where(
        and(
          gte(posts.createdAt, startOfLastMonth),
          lte(posts.createdAt, endOfLastMonth)
        )
      );

    // Community Statistics
    const totalGroupsResult = await db
      .select({ count: count() })
      .from(groups);

    const totalFanPagesResult = await db
      .select({ count: count() })
      .from(fanPages);

    const totalEventsResult = await db
      .select({ count: count() })
      .from(events);

    // Marketplace Statistics
    const totalProductsResult = await db
      .select({ count: count() })
      .from(products);

    // Reports Statistics
    const totalReportsResult = await db
      .select({ count: count() })
      .from(reports);

    const pendingReportsResult = await db
      .select({ count: count() })
      .from(reports)
      .where(eq(reports.status, 'pending'));

    // Financial Statistics
    const totalRevenueResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'deposit'),
          eq(walletTransactions.status, 'completed')
        )
      );

    const monthlyRevenueResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'deposit'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, startOfMonth)
        )
      );

    const lastMonthRevenueResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'deposit'),
          eq(walletTransactions.status, 'completed'),
          gte(walletTransactions.createdAt, startOfLastMonth),
          lte(walletTransactions.createdAt, endOfLastMonth)
        )
      );

    // Subscription Statistics
    const activeSubscriptionsResult = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'active'));

    const subscriptionRevenueResult = await db
      .select({ total: sum(subscriptions.amount) })
      .from(subscriptions)
      .where(
        and(
          eq(subscriptions.status, 'active'),
          gte(subscriptions.createdAt, startOfMonth)
        )
      );

    // Earning World Statistics
    const totalReferralsResult = await db
      .select({ count: count() })
      .from(referrals);

    const completedReferralsResult = await db
      .select({ count: count() })
      .from(referrals)
      .where(eq(referrals.status, 'completed'));

    const monetizedBlogsResult = await db
      .select({ count: count() })
      .from(blogMonetization)
      .where(eq(blogMonetization.isApproved, true));

    const blogEarningsResult = await db
      .select({ total: sum(blogEarnings.earningAmount) })
      .from(blogEarnings)
      .where(eq(blogEarnings.status, 'paid'));

    // Additional Statistics

    // Content engagement statistics
    const totalBlogViewsResult = await db
      .select({ total: sum(sql`CAST(${blogs.viewCount} AS UNSIGNED)`) })
      .from(blogs);

    const totalPostLikesResult = await db
      .select({ total: count() })
      .from(likes)
      .where(isNotNull(likes.postId));

    const totalCommentsResult = await db
      .select({ total: count() })
      .from(comments);

    // User engagement statistics
    const verifiedUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.isVerified, true));

    const premiumUsersResult = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(
        and(
          eq(subscriptions.status, 'active'),
          sql`${subscriptions.planName} != 'Basic'`
        )
      );

    // Financial breakdown
    const totalWithdrawalsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'withdrawal'),
          eq(walletTransactions.status, 'completed')
        )
      );

    const pendingWithdrawalsResult = await db
      .select({ total: sum(walletTransactions.amount) })
      .from(walletTransactions)
      .where(
        and(
          eq(walletTransactions.type, 'withdrawal'),
          eq(walletTransactions.status, 'pending')
        )
      );

    // Growth statistics (last 7 days vs previous 7 days)
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const previous7Days = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

    const newUsersLast7DaysResult = await db
      .select({ count: count() })
      .from(users)
      .where(gte(users.createdAt, last7Days));

    const newUsersPrevious7DaysResult = await db
      .select({ count: count() })
      .from(users)
      .where(
        and(
          gte(users.createdAt, previous7Days),
          lte(users.createdAt, last7Days)
        )
      );

    const postsLast7DaysResult = await db
      .select({ count: count() })
      .from(posts)
      .where(gte(posts.createdAt, last7Days));

    const postsPrevious7DaysResult = await db
      .select({ count: count() })
      .from(posts)
      .where(
        and(
          gte(posts.createdAt, previous7Days),
          lte(posts.createdAt, last7Days)
        )
      );

    // Calculate values and growth rates
    const totalUsers = totalUsersResult[0]?.count || 0;
    const activeUsers = activeUsersResult[0]?.count || 0;
    const newUsersThisMonth = newUsersThisMonthResult[0]?.count || 0;
    const newUsersLastMonth = newUsersLastMonthResult[0]?.count || 0;
    const userGrowthRate = newUsersLastMonth > 0 
      ? ((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100 
      : 0;

    const totalPosts = totalPostsResult[0]?.count || 0;
    const totalBlogs = totalBlogsResult[0]?.count || 0;
    const publishedBlogs = publishedBlogsResult[0]?.count || 0;
    const postsThisMonth = postsThisMonthResult[0]?.count || 0;
    const postsLastMonth = postsLastMonthResult[0]?.count || 0;
    const postGrowthRate = postsLastMonth > 0 
      ? ((postsThisMonth - postsLastMonth) / postsLastMonth) * 100 
      : 0;

    const totalGroups = totalGroupsResult[0]?.count || 0;
    const totalFanPages = totalFanPagesResult[0]?.count || 0;
    const totalEvents = totalEventsResult[0]?.count || 0;
    const totalProducts = totalProductsResult[0]?.count || 0;

    const totalReports = totalReportsResult[0]?.count || 0;
    const pendingReports = pendingReportsResult[0]?.count || 0;

    const totalRevenue = parseFloat(totalRevenueResult[0]?.total || '0');
    const monthlyRevenue = parseFloat(monthlyRevenueResult[0]?.total || '0');
    const lastMonthRevenue = parseFloat(lastMonthRevenueResult[0]?.total || '0');
    const revenueGrowthRate = lastMonthRevenue > 0 
      ? ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
      : 0;

    const activeSubscriptions = activeSubscriptionsResult[0]?.count || 0;
    const subscriptionRevenue = parseFloat(subscriptionRevenueResult[0]?.total || '0');

    const totalReferrals = totalReferralsResult[0]?.count || 0;
    const completedReferrals = completedReferralsResult[0]?.count || 0;
    const monetizedBlogs = monetizedBlogsResult[0]?.count || 0;
    const blogEarnings = parseFloat(blogEarningsResult[0]?.total || '0');

    // Additional calculated values
    const totalBlogViews = parseInt(totalBlogViewsResult[0]?.total || '0');
    const totalPostLikes = totalPostLikesResult[0]?.total || 0;
    const totalComments = totalCommentsResult[0]?.total || 0;
    const verifiedUsers = verifiedUsersResult[0]?.count || 0;
    const premiumUsers = premiumUsersResult[0]?.count || 0;
    const totalWithdrawals = parseFloat(totalWithdrawalsResult[0]?.total || '0');
    const pendingWithdrawals = parseFloat(pendingWithdrawalsResult[0]?.total || '0');

    // Weekly growth calculations
    const newUsersLast7Days = newUsersLast7DaysResult[0]?.count || 0;
    const newUsersPrevious7Days = newUsersPrevious7DaysResult[0]?.count || 0;
    const weeklyUserGrowthRate = newUsersPrevious7Days > 0
      ? ((newUsersLast7Days - newUsersPrevious7Days) / newUsersPrevious7Days) * 100
      : 0;

    const postsLast7Days = postsLast7DaysResult[0]?.count || 0;
    const postsPrevious7Days = postsPrevious7DaysResult[0]?.count || 0;
    const weeklyPostGrowthRate = postsPrevious7Days > 0
      ? ((postsLast7Days - postsPrevious7Days) / postsPrevious7Days) * 100
      : 0;

    // Engagement rates
    const userEngagementRate = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;
    const blogPublishRate = totalBlogs > 0 ? (publishedBlogs / totalBlogs) * 100 : 0;
    const referralCompletionRate = totalReferrals > 0 ? (completedReferrals / totalReferrals) * 100 : 0;
    const subscriptionConversionRate = totalUsers > 0 ? (activeSubscriptions / totalUsers) * 100 : 0;

    const stats = {
      // User Statistics
      totalUsers,
      activeUsers,
      newUsersThisMonth,
      userGrowthRate: Math.round(userGrowthRate * 100) / 100,
      
      // Content Statistics
      totalPosts,
      totalBlogs,
      publishedBlogs,
      postGrowthRate: Math.round(postGrowthRate * 100) / 100,
      
      // Community Statistics
      totalGroups,
      totalFanPages,
      totalEvents,
      
      // Marketplace Statistics
      totalProducts,
      
      // Reports Statistics
      totalReports,
      pendingReports,
      
      // Financial Statistics
      totalRevenue,
      monthlyRevenue,
      revenueGrowthRate: Math.round(revenueGrowthRate * 100) / 100,
      
      // Subscription Statistics
      activeSubscriptions,
      subscriptionRevenue,
      
      // Earning World Statistics
      totalReferrals,
      completedReferrals,
      monetizedBlogs,
      blogEarnings,

      // Content Engagement Statistics
      totalBlogViews,
      totalPostLikes,
      totalComments,

      // User Segmentation
      verifiedUsers,
      premiumUsers,

      // Financial Breakdown
      totalWithdrawals,
      pendingWithdrawals,

      // Weekly Growth Statistics
      newUsersLast7Days,
      newUsersPrevious7Days,
      weeklyUserGrowthRate: Math.round(weeklyUserGrowthRate * 100) / 100,
      postsLast7Days,
      postsPrevious7Days,
      weeklyPostGrowthRate: Math.round(weeklyPostGrowthRate * 100) / 100,

      // Engagement Rates
      userEngagementRate: Math.round(userEngagementRate * 100) / 100,
      blogPublishRate: Math.round(blogPublishRate * 100) / 100,
      referralCompletionRate: Math.round(referralCompletionRate * 100) / 100,
      subscriptionConversionRate: Math.round(subscriptionConversionRate * 100) / 100,

      // System Health (mock data for now)
      systemHealth: {
        serverUptime: 92,
        databaseLoad: 5,
        storageUsage: 68,
        serverStatus: 'healthy',
        databaseStatus: 'optimal',
        storageStatus: 'moderate'
      }
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Error fetching admin dashboard stats:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch dashboard statistics"
      },
      { status: 500 }
    );
  }
}
