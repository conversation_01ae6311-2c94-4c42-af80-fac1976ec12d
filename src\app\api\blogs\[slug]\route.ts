import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogLikes, blogBookmarks, users } from "@/lib/db/schema";
import { eq, and, sql } from "drizzle-orm";
import { MonetizationService } from "@/lib/monetization/monetizationService";

// Get a single blog by slug
export async function GET(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params;
    const { slug } = params;
    const session = await getServerSession(authOptions);

    // Fetch blog from database
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      with: {
        author: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
            bio: true,
          },
        },
        category: true,
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        bookmarks: true,
      },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user has liked or bookmarked this blog
    let isLiked = false;
    let isBookmarked = false;
    let isFollowing = false;

    if (session?.user) {
      const userLike = blog.likes.find(like => like.userId === session.user.id);
      const userBookmark = blog.bookmarks.find(bookmark => bookmark.userId === session.user.id);

      isLiked = !!userLike;
      isBookmarked = !!userBookmark;

      // TODO: Check if user is following the author
      // This would require a follows table in the schema
      isFollowing = false;
    }

    // Increment view count
    await db
      .update(blogs)
      .set({
        viewCount: sql`${blogs.viewCount} + 1`
      })
      .where(eq(blogs.id, blog.id));

    // Get author's followers count
    const followersCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(eq(users.id, blog.authorId));

    // Format blog for frontend
    const formattedBlog = {
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      content: blog.content,
      excerpt: blog.excerpt,
      coverImage: blog.coverImage,
      readTime: blog.readTime,
      viewCount: blog.viewCount + 1, // Include the increment
      publishedAt: blog.publishedAt?.toISOString(),
      author: {
        ...blog.author,
        followersCount: followersCount[0]?.count || 0,
        isFollowing,
      },
      category: blog.category,
      tags: blog.tags,
      _count: {
        likes: blog.likes.length,
        comments: blog.comments.length,
      },
      isLiked,
      isBookmarked,
    };

    return NextResponse.json(formattedBlog);
  } catch (error) {
    console.error("Error fetching blog:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update a blog
export async function PUT(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { slug } = params;
    const body = await req.json();

    // Check if blog exists and user owns it
    const existingBlog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
    });

    if (!existingBlog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    if (existingBlog.authorId !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      );
    }

    // Update blog
    await db
      .update(blogs)
      .set({
        title: body.title || existingBlog.title,
        content: body.content || existingBlog.content,
        excerpt: body.excerpt !== undefined ? body.excerpt : existingBlog.excerpt,
        coverImage: body.coverImage !== undefined ? body.coverImage : existingBlog.coverImage,
        categoryId: body.categoryId !== undefined ? body.categoryId : existingBlog.categoryId,
        tags: body.tags !== undefined ? body.tags : existingBlog.tags,
        status: body.status || existingBlog.status,
        readTime: body.readTime || existingBlog.readTime,
        featured: body.featured !== undefined ? body.featured : existingBlog.featured,
        seoTitle: body.seoTitle !== undefined ? body.seoTitle : existingBlog.seoTitle,
        seoDescription: body.seoDescription !== undefined ? body.seoDescription : existingBlog.seoDescription,
        publishedAt: body.status === "published" && !existingBlog.publishedAt ? new Date() : existingBlog.publishedAt,
        updatedAt: new Date(),
      })
      .where(eq(blogs.id, existingBlog.id));

    // Handle monetization request if requested and blog is being published
    if (body.requestMonetization && body.status === "published") {
      try {
        // Check if monetization is globally enabled
        const isGloballyEnabled = await MonetizationService.isMonetizationEnabled();
        if (isGloballyEnabled) {
          await MonetizationService.requestMonetization({
            blogId: existingBlog.id,
            authorId: session.user.id
          });
        } else {
          console.warn("Monetization request ignored - globally disabled");
        }
      } catch (monetizationError) {
        console.error("Error requesting monetization:", monetizationError);
        // Don't fail the blog update if monetization request fails
      }
    }

    return NextResponse.json({
      message: body.requestMonetization && body.status === "published"
        ? "Blog updated successfully and monetization request submitted"
        : "Blog updated successfully",
      monetizationRequested: body.requestMonetization && body.status === "published"
    });
  } catch (error) {
    console.error("Error updating blog:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete a blog
export async function DELETE(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { slug } = params;

    // Check if blog exists and user owns it
    const existingBlog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
    });

    if (!existingBlog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    if (existingBlog.authorId !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      );
    }

    // Delete blog
    await db.delete(blogs).where(eq(blogs.id, existingBlog.id));

    return NextResponse.json({ message: "Blog deleted successfully" });
  } catch (error) {
    console.error("Error deleting blog:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
