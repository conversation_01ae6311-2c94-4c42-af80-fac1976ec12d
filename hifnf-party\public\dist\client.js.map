{"version": 3, "sources": ["../../node_modules/partysocket/src/ws.ts", "../../node_modules/partysocket/src/index.ts", "../../src/client.ts"], "sourcesContent": ["// TODO: lose this eslint-disable\n\n/*!\n * Reconnecting WebSocket\n * by <PERSON> <<EMAIL>>\n * https://github.com/pladaria/reconnecting-websocket\n * License MIT\n */\n\nimport type { TypedEventTarget } from \"./type-helper\";\n\nif (!globalThis.EventTarget || !globalThis.Event) {\n  console.error(`\n  PartySocket requires a global 'EventTarget' class to be available!\n  You can polyfill this global by adding this to your code before any partysocket imports: \n  \n  \\`\\`\\`\n  import 'partysocket/event-target-polyfill';\n  \\`\\`\\`\n  Please file an issue at https://github.com/partykit/partykit if you're still having trouble.\n`);\n}\n\nexport class ErrorEvent extends Event {\n  public message: string;\n  public error: Error;\n  // biome-ignore lint/suspicious/noExplicitAny: vibes\n  constructor(error: Error, target: any) {\n    super(\"error\", target);\n    this.message = error.message;\n    this.error = error;\n  }\n}\n\nexport class CloseEvent extends Event {\n  public code: number;\n  public reason: string;\n  public wasClean = true;\n  // biome-ignore lint/style/useDefaultParameterLast: legacy\n  // biome-ignore lint/suspicious/noExplicitAny: legacy\n  constructor(code = 1000, reason = \"\", target: any) {\n    super(\"close\", target);\n    this.code = code;\n    this.reason = reason;\n  }\n}\nexport interface WebSocketEventMap {\n  close: CloseEvent;\n  error: ErrorEvent;\n  message: MessageEvent;\n  open: Event;\n}\n\nconst Events = {\n  Event,\n  ErrorEvent,\n  CloseEvent\n};\n\nfunction assert(condition: unknown, msg?: string): asserts condition {\n  if (!condition) {\n    throw new Error(msg);\n  }\n}\n\nfunction cloneEventBrowser(e: Event) {\n  // biome-ignore lint/suspicious/noExplicitAny: legacy\n  return new (e as any).constructor(e.type, e) as Event;\n}\n\nfunction cloneEventNode(e: Event) {\n  if (\"data\" in e) {\n    const evt = new MessageEvent(e.type, e);\n    return evt;\n  }\n\n  if (\"code\" in e || \"reason\" in e) {\n    const evt = new CloseEvent(\n      // @ts-expect-error we need to fix event/listener types\n      (e.code || 1999) as number,\n      // @ts-expect-error we need to fix event/listener types\n      (e.reason || \"unknown reason\") as string,\n      e\n    );\n    return evt;\n  }\n\n  if (\"error\" in e) {\n    const evt = new ErrorEvent(e.error as Error, e);\n    return evt;\n  }\n\n  const evt = new Event(e.type, e);\n  return evt;\n}\n\nconst isNode =\n  typeof process !== \"undefined\" &&\n  typeof process.versions?.node !== \"undefined\" &&\n  typeof document === \"undefined\";\n\nconst cloneEvent = isNode ? cloneEventNode : cloneEventBrowser;\n\nexport type Options = {\n  // biome-ignore lint/suspicious/noExplicitAny: legacy\n  WebSocket?: any;\n  maxReconnectionDelay?: number;\n  minReconnectionDelay?: number;\n  reconnectionDelayGrowFactor?: number;\n  minUptime?: number;\n  connectionTimeout?: number;\n  maxRetries?: number;\n  maxEnqueuedMessages?: number;\n  startClosed?: boolean;\n  debug?: boolean;\n  // biome-ignore lint/suspicious/noExplicitAny: legacy\n  debugLogger?: (...args: any[]) => void;\n};\n\nconst DEFAULT = {\n  maxReconnectionDelay: 10000,\n  minReconnectionDelay: 1000 + Math.random() * 4000,\n  minUptime: 5000,\n  reconnectionDelayGrowFactor: 1.3,\n  connectionTimeout: 4000,\n  maxRetries: Number.POSITIVE_INFINITY,\n  maxEnqueuedMessages: Number.POSITIVE_INFINITY,\n  startClosed: false,\n  debug: false\n};\n\nlet didWarnAboutMissingWebSocket = false;\n\nexport type UrlProvider = string | (() => string) | (() => Promise<string>);\nexport type ProtocolsProvider =\n  | null\n  | string\n  | string[]\n  | (() => string | string[] | null)\n  | (() => Promise<string | string[] | null>);\n\nexport type Message = string | ArrayBuffer | Blob | ArrayBufferView;\n\nexport default class ReconnectingWebSocket extends (EventTarget as TypedEventTarget<WebSocketEventMap>) {\n  private _ws: WebSocket | undefined;\n  private _retryCount = -1;\n  private _uptimeTimeout: ReturnType<typeof setTimeout> | undefined;\n  private _connectTimeout: ReturnType<typeof setTimeout> | undefined;\n  private _shouldReconnect = true;\n  private _connectLock = false;\n  private _binaryType: BinaryType = \"blob\";\n  private _closeCalled = false;\n  private _messageQueue: Message[] = [];\n\n  private _debugLogger = console.log.bind(console);\n\n  protected _url: UrlProvider;\n  protected _protocols?: ProtocolsProvider;\n  protected _options: Options;\n\n  constructor(\n    url: UrlProvider,\n    protocols?: ProtocolsProvider,\n    options: Options = {}\n  ) {\n    super();\n    this._url = url;\n    this._protocols = protocols;\n    this._options = options;\n    if (this._options.startClosed) {\n      this._shouldReconnect = false;\n    }\n    if (this._options.debugLogger) {\n      this._debugLogger = this._options.debugLogger;\n    }\n    this._connect();\n  }\n\n  static get CONNECTING() {\n    return 0;\n  }\n  static get OPEN() {\n    return 1;\n  }\n  static get CLOSING() {\n    return 2;\n  }\n  static get CLOSED() {\n    return 3;\n  }\n\n  get CONNECTING() {\n    return ReconnectingWebSocket.CONNECTING;\n  }\n  get OPEN() {\n    return ReconnectingWebSocket.OPEN;\n  }\n  get CLOSING() {\n    return ReconnectingWebSocket.CLOSING;\n  }\n  get CLOSED() {\n    return ReconnectingWebSocket.CLOSED;\n  }\n\n  get binaryType() {\n    return this._ws ? this._ws.binaryType : this._binaryType;\n  }\n\n  set binaryType(value: BinaryType) {\n    this._binaryType = value;\n    if (this._ws) {\n      this._ws.binaryType = value;\n    }\n  }\n\n  /**\n   * Returns the number or connection retries\n   */\n  get retryCount(): number {\n    return Math.max(this._retryCount, 0);\n  }\n\n  /**\n   * The number of bytes of data that have been queued using calls to send() but not yet\n   * transmitted to the network. This value resets to zero once all queued data has been sent.\n   * This value does not reset to zero when the connection is closed; if you keep calling send(),\n   * this will continue to climb. Read only\n   */\n  get bufferedAmount(): number {\n    const bytes = this._messageQueue.reduce((acc, message) => {\n      if (typeof message === \"string\") {\n        acc += message.length; // not byte size\n      } else if (message instanceof Blob) {\n        acc += message.size;\n      } else {\n        acc += message.byteLength;\n      }\n      return acc;\n    }, 0);\n    return bytes + (this._ws ? this._ws.bufferedAmount : 0);\n  }\n\n  /**\n   * The extensions selected by the server. This is currently only the empty string or a list of\n   * extensions as negotiated by the connection\n   */\n  get extensions(): string {\n    return this._ws ? this._ws.extensions : \"\";\n  }\n\n  /**\n   * A string indicating the name of the sub-protocol the server selected;\n   * this will be one of the strings specified in the protocols parameter when creating the\n   * WebSocket object\n   */\n  get protocol(): string {\n    return this._ws ? this._ws.protocol : \"\";\n  }\n\n  /**\n   * The current state of the connection; this is one of the Ready state constants\n   */\n  get readyState(): number {\n    if (this._ws) {\n      return this._ws.readyState;\n    }\n    return this._options.startClosed\n      ? ReconnectingWebSocket.CLOSED\n      : ReconnectingWebSocket.CONNECTING;\n  }\n\n  /**\n   * The URL as resolved by the constructor\n   */\n  get url(): string {\n    return this._ws ? this._ws.url : \"\";\n  }\n\n  /**\n   * Whether the websocket object is now in reconnectable state\n   */\n  get shouldReconnect(): boolean {\n    return this._shouldReconnect;\n  }\n\n  /**\n   * An event listener to be called when the WebSocket connection's readyState changes to CLOSED\n   */\n  public onclose: ((event: CloseEvent) => void) | null = null;\n\n  /**\n   * An event listener to be called when an error occurs\n   */\n  public onerror: ((event: ErrorEvent) => void) | null = null;\n\n  /**\n   * An event listener to be called when a message is received from the server\n   */\n  public onmessage: ((event: MessageEvent) => void) | null = null;\n\n  /**\n   * An event listener to be called when the WebSocket connection's readyState changes to OPEN;\n   * this indicates that the connection is ready to send and receive data\n   */\n  public onopen: ((event: Event) => void) | null = null;\n\n  /**\n   * Closes the WebSocket connection or connection attempt, if any. If the connection is already\n   * CLOSED, this method does nothing\n   */\n  public close(code = 1000, reason?: string) {\n    this._closeCalled = true;\n    this._shouldReconnect = false;\n    this._clearTimeouts();\n    if (!this._ws) {\n      this._debug(\"close enqueued: no ws instance\");\n      return;\n    }\n    if (this._ws.readyState === this.CLOSED) {\n      this._debug(\"close: already closed\");\n      return;\n    }\n    this._ws.close(code, reason);\n  }\n\n  /**\n   * Closes the WebSocket connection or connection attempt and connects again.\n   * Resets retry counter;\n   */\n  public reconnect(code?: number, reason?: string) {\n    this._shouldReconnect = true;\n    this._closeCalled = false;\n    this._retryCount = -1;\n    if (!this._ws || this._ws.readyState === this.CLOSED) {\n      this._connect();\n    } else {\n      this._disconnect(code, reason);\n      this._connect();\n    }\n  }\n\n  /**\n   * Enqueue specified data to be transmitted to the server over the WebSocket connection\n   */\n  public send(data: Message) {\n    if (this._ws && this._ws.readyState === this.OPEN) {\n      this._debug(\"send\", data);\n      this._ws.send(data);\n    } else {\n      const { maxEnqueuedMessages = DEFAULT.maxEnqueuedMessages } =\n        this._options;\n      if (this._messageQueue.length < maxEnqueuedMessages) {\n        this._debug(\"enqueue\", data);\n        this._messageQueue.push(data);\n      }\n    }\n  }\n\n  private _debug(...args: unknown[]) {\n    if (this._options.debug) {\n      this._debugLogger(\"RWS>\", ...args);\n    }\n  }\n\n  private _getNextDelay() {\n    const {\n      reconnectionDelayGrowFactor = DEFAULT.reconnectionDelayGrowFactor,\n      minReconnectionDelay = DEFAULT.minReconnectionDelay,\n      maxReconnectionDelay = DEFAULT.maxReconnectionDelay\n    } = this._options;\n    let delay = 0;\n    if (this._retryCount > 0) {\n      delay =\n        minReconnectionDelay *\n        reconnectionDelayGrowFactor ** (this._retryCount - 1);\n      if (delay > maxReconnectionDelay) {\n        delay = maxReconnectionDelay;\n      }\n    }\n    this._debug(\"next delay\", delay);\n    return delay;\n  }\n\n  private _wait(): Promise<void> {\n    return new Promise((resolve) => {\n      setTimeout(resolve, this._getNextDelay());\n    });\n  }\n\n  private _getNextProtocols(\n    protocolsProvider: ProtocolsProvider | null\n  ): Promise<string | string[] | null> {\n    if (!protocolsProvider) return Promise.resolve(null);\n\n    if (\n      typeof protocolsProvider === \"string\" ||\n      Array.isArray(protocolsProvider)\n    ) {\n      return Promise.resolve(protocolsProvider);\n    }\n\n    if (typeof protocolsProvider === \"function\") {\n      const protocols = protocolsProvider();\n      if (!protocols) return Promise.resolve(null);\n\n      if (typeof protocols === \"string\" || Array.isArray(protocols)) {\n        return Promise.resolve(protocols);\n      }\n\n      // @ts-expect-error redundant check\n      if (protocols.then) {\n        return protocols;\n      }\n    }\n\n    throw Error(\"Invalid protocols\");\n  }\n\n  private _getNextUrl(urlProvider: UrlProvider): Promise<string> {\n    if (typeof urlProvider === \"string\") {\n      return Promise.resolve(urlProvider);\n    }\n    if (typeof urlProvider === \"function\") {\n      const url = urlProvider();\n      if (typeof url === \"string\") {\n        return Promise.resolve(url);\n      }\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      if (url.then) {\n        return url;\n      }\n\n      // return url;\n    }\n    throw Error(\"Invalid URL\");\n  }\n\n  private _connect() {\n    if (this._connectLock || !this._shouldReconnect) {\n      return;\n    }\n    this._connectLock = true;\n\n    const {\n      maxRetries = DEFAULT.maxRetries,\n      connectionTimeout = DEFAULT.connectionTimeout\n    } = this._options;\n\n    if (this._retryCount >= maxRetries) {\n      this._debug(\"max retries reached\", this._retryCount, \">=\", maxRetries);\n      return;\n    }\n\n    this._retryCount++;\n\n    this._debug(\"connect\", this._retryCount);\n    this._removeListeners();\n\n    this._wait()\n      .then(() =>\n        Promise.all([\n          this._getNextUrl(this._url),\n          this._getNextProtocols(this._protocols || null)\n        ])\n      )\n      .then(([url, protocols]) => {\n        // close could be called before creating the ws\n        if (this._closeCalled) {\n          this._connectLock = false;\n          return;\n        }\n        if (\n          !this._options.WebSocket &&\n          typeof WebSocket === \"undefined\" &&\n          !didWarnAboutMissingWebSocket\n        ) {\n          console.error(`‼️ No WebSocket implementation available. You should define options.WebSocket. \n\nFor example, if you're using node.js, run \\`npm install ws\\`, and then in your code:\n\nimport PartySocket from 'partysocket';\nimport WS from 'ws';\n\nconst partysocket = new PartySocket({\n  host: \"127.0.0.1:1999\",\n  room: \"test-room\",\n  WebSocket: WS\n});\n\n`);\n          didWarnAboutMissingWebSocket = true;\n        }\n        const WS: typeof WebSocket = this._options.WebSocket || WebSocket;\n        this._debug(\"connect\", { url, protocols });\n        this._ws = protocols ? new WS(url, protocols) : new WS(url);\n\n        this._ws.binaryType = this._binaryType;\n        this._connectLock = false;\n        this._addListeners();\n\n        this._connectTimeout = setTimeout(\n          () => this._handleTimeout(),\n          connectionTimeout\n        );\n      })\n      // via https://github.com/pladaria/reconnecting-websocket/pull/166\n      .catch((err) => {\n        this._connectLock = false;\n        this._handleError(new Events.ErrorEvent(Error(err.message), this));\n      });\n  }\n\n  private _handleTimeout() {\n    this._debug(\"timeout event\");\n    this._handleError(new Events.ErrorEvent(Error(\"TIMEOUT\"), this));\n  }\n\n  private _disconnect(code = 1000, reason?: string) {\n    this._clearTimeouts();\n    if (!this._ws) {\n      return;\n    }\n    this._removeListeners();\n    try {\n      if (\n        this._ws.readyState === this.OPEN ||\n        this._ws.readyState === this.CONNECTING\n      ) {\n        this._ws.close(code, reason);\n      }\n      this._handleClose(new Events.CloseEvent(code, reason, this));\n    } catch (error) {\n      // ignore\n    }\n  }\n\n  private _acceptOpen() {\n    this._debug(\"accept open\");\n    this._retryCount = 0;\n  }\n\n  private _handleOpen = (event: Event) => {\n    this._debug(\"open event\");\n    const { minUptime = DEFAULT.minUptime } = this._options;\n\n    clearTimeout(this._connectTimeout);\n    this._uptimeTimeout = setTimeout(() => this._acceptOpen(), minUptime);\n\n    assert(this._ws, \"WebSocket is not defined\");\n\n    this._ws.binaryType = this._binaryType;\n\n    // send enqueued messages (messages sent before websocket open event)\n    this._messageQueue.forEach((message) => this._ws?.send(message));\n    this._messageQueue = [];\n\n    if (this.onopen) {\n      this.onopen(event);\n    }\n    this.dispatchEvent(cloneEvent(event));\n  };\n\n  private _handleMessage = (event: MessageEvent) => {\n    this._debug(\"message event\");\n\n    if (this.onmessage) {\n      this.onmessage(event);\n    }\n    this.dispatchEvent(cloneEvent(event));\n  };\n\n  private _handleError = (event: ErrorEvent) => {\n    this._debug(\"error event\", event.message);\n    this._disconnect(\n      undefined,\n      event.message === \"TIMEOUT\" ? \"timeout\" : undefined\n    );\n\n    if (this.onerror) {\n      this.onerror(event);\n    }\n    this._debug(\"exec error listeners\");\n    this.dispatchEvent(cloneEvent(event));\n\n    this._connect();\n  };\n\n  private _handleClose = (event: CloseEvent) => {\n    this._debug(\"close event\");\n    this._clearTimeouts();\n\n    if (this._shouldReconnect) {\n      this._connect();\n    }\n\n    if (this.onclose) {\n      this.onclose(event);\n    }\n    this.dispatchEvent(cloneEvent(event));\n  };\n\n  private _removeListeners() {\n    if (!this._ws) {\n      return;\n    }\n    this._debug(\"removeListeners\");\n    this._ws.removeEventListener(\"open\", this._handleOpen);\n    this._ws.removeEventListener(\"close\", this._handleClose);\n    this._ws.removeEventListener(\"message\", this._handleMessage);\n    // @ts-expect-error we need to fix event/listerner types\n    this._ws.removeEventListener(\"error\", this._handleError);\n  }\n\n  private _addListeners() {\n    if (!this._ws) {\n      return;\n    }\n    this._debug(\"addListeners\");\n    this._ws.addEventListener(\"open\", this._handleOpen);\n    this._ws.addEventListener(\"close\", this._handleClose);\n    this._ws.addEventListener(\"message\", this._handleMessage);\n    // @ts-expect-error we need to fix event/listener types\n    this._ws.addEventListener(\"error\", this._handleError);\n  }\n\n  private _clearTimeouts() {\n    clearTimeout(this._connectTimeout);\n    clearTimeout(this._uptimeTimeout);\n  }\n}\n", "import ReconnectingWebSocket from \"./ws\";\n\nimport type * as R<PERSON> from \"./ws\";\n\ntype Maybe<T> = T | null | undefined;\ntype Params = Record<string, Maybe<string>>;\nconst valueIsNotNil = <T>(\n  keyValuePair: [string, Maybe<T>]\n): keyValuePair is [string, T] =>\n  keyValuePair[1] !== null && keyValuePair[1] !== undefined;\n\nexport type PartySocketOptions = Omit<RWS.Options, \"constructor\"> & {\n  id?: string; // the id of the client\n  host: string; // base url for the party\n  room?: string; // the room to connect to\n  party?: string; // the party to connect to (defaults to main)\n  basePath?: string; // the base path to use for the party\n  prefix?: string; // the prefix to use for the party\n  protocol?: \"ws\" | \"wss\";\n  protocols?: string[];\n  path?: string; // the path to connect to\n  query?: Params | (() => Params | Promise<Params>);\n  // headers\n};\n\nexport type PartyFetchOptions = {\n  host: string; // base url for the party\n  room: string; // the room to connect to\n  party?: string; // the party to fetch from (defaults to main)\n  basePath?: string; // the base path to use for the party\n  prefix?: string; // the prefix to use for the party\n  path?: string; // the path to fetch from\n  protocol?: \"http\" | \"https\";\n  query?: Params | (() => Params | Promise<Params>);\n  fetch?: typeof fetch;\n};\n\nfunction generateUUID(): string {\n  // Public Domain/MIT\n  if (typeof crypto !== \"undefined\" && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n  let d = new Date().getTime(); //Timestamp\n  let d2 =\n    (typeof performance !== \"undefined\" &&\n      performance.now &&\n      performance.now() * 1000) ||\n    0; //Time in microseconds since page-load or 0 if unsupported\n  // biome-ignore lint/complexity/useArrowFunction: <explanation>\n  return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function (c) {\n    let r = Math.random() * 16; //random number between 0 and 16\n    if (d > 0) {\n      //Use timestamp until depleted\n      r = (d + r) % 16 | 0;\n      d = Math.floor(d / 16);\n    } else {\n      //Use microseconds since page-load if supported\n      r = (d2 + r) % 16 | 0;\n      d2 = Math.floor(d2 / 16);\n    }\n    return (c === \"x\" ? r : (r & 0x3) | 0x8).toString(16);\n  });\n}\n\nfunction getPartyInfo(\n  partySocketOptions: PartySocketOptions | PartyFetchOptions,\n  defaultProtocol: \"http\" | \"ws\",\n  defaultParams: Record<string, string> = {}\n) {\n  const {\n    host: rawHost,\n    path: rawPath,\n    protocol: rawProtocol,\n    room,\n    party,\n    basePath,\n    prefix,\n    query\n  } = partySocketOptions;\n\n  // strip the protocol from the beginning of `host` if any\n  let host = rawHost.replace(/^(http|https|ws|wss):\\/\\//, \"\");\n  // if user provided a trailing slash, remove it\n  if (host.endsWith(\"/\")) {\n    host = host.slice(0, -1);\n  }\n\n  if (rawPath?.startsWith(\"/\")) {\n    throw new Error(\"path must not start with a slash\");\n  }\n\n  const name = party ?? \"main\";\n  const path = rawPath ? `/${rawPath}` : \"\";\n  const protocol =\n    rawProtocol ||\n    (host.startsWith(\"localhost:\") ||\n    host.startsWith(\"127.0.0.1:\") ||\n    host.startsWith(\"192.168.\") ||\n    host.startsWith(\"10.\") ||\n    (host.startsWith(\"172.\") &&\n      host.split(\".\")[1] >= \"16\" &&\n      host.split(\".\")[1] <= \"31\") ||\n    host.startsWith(\"[::ffff:7f00:1]:\")\n      ? // http / ws\n        defaultProtocol\n      : // https / wss\n        `${defaultProtocol}s`);\n\n  const baseUrl = `${protocol}://${host}/${basePath || `${prefix || \"parties\"}/${name}/${room}`}${path}`;\n\n  const makeUrl = (query: Params = {}) =>\n    `${baseUrl}?${new URLSearchParams([\n      ...Object.entries(defaultParams),\n      ...Object.entries(query).filter(valueIsNotNil)\n    ])}`;\n\n  // allow urls to be defined as functions\n  const urlProvider =\n    typeof query === \"function\"\n      ? async () => makeUrl(await query())\n      : makeUrl(query);\n\n  return {\n    host,\n    path,\n    room,\n    name,\n    protocol,\n    partyUrl: baseUrl,\n    urlProvider\n  };\n}\n\n// things that nathanboktae/robust-websocket claims are better:\n// doesn't do anything in offline mode (?)\n// \"natively aware of error codes\"\n// can do custom reconnect strategies\n\n// TODO: incorporate the above notes\nexport default class PartySocket extends ReconnectingWebSocket {\n  _pk!: string;\n  _pkurl!: string;\n  name!: string;\n  room?: string;\n  host!: string;\n  path!: string;\n\n  constructor(readonly partySocketOptions: PartySocketOptions) {\n    const wsOptions = getWSOptions(partySocketOptions);\n\n    super(wsOptions.urlProvider, wsOptions.protocols, wsOptions.socketOptions);\n\n    this.setWSProperties(wsOptions);\n  }\n\n  public updateProperties(partySocketOptions: Partial<PartySocketOptions>) {\n    const wsOptions = getWSOptions({\n      ...this.partySocketOptions,\n      ...partySocketOptions,\n      host: partySocketOptions.host ?? this.host,\n      room: partySocketOptions.room ?? this.room,\n      path: partySocketOptions.path ?? this.path\n    });\n\n    this._url = wsOptions.urlProvider;\n    this._protocols = wsOptions.protocols;\n    this._options = wsOptions.socketOptions;\n\n    this.setWSProperties(wsOptions);\n  }\n\n  private setWSProperties(wsOptions: ReturnType<typeof getWSOptions>) {\n    const { _pk, _pkurl, name, room, host, path } = wsOptions;\n\n    this._pk = _pk;\n    this._pkurl = _pkurl;\n    this.name = name;\n    this.room = room;\n    this.host = host;\n    this.path = path;\n  }\n\n  public reconnect(\n    code?: number | undefined,\n    reason?: string | undefined\n  ): void {\n    if (!this.room || !this.host) {\n      throw new Error(\n        \"The room and host must be set before connecting, use `updateProperties` method to set them or pass them to the constructor.\"\n      );\n    }\n    super.reconnect(code, reason);\n  }\n\n  get id() {\n    return this._pk;\n  }\n\n  /**\n   * Exposes the static PartyKit room URL without applying query parameters.\n   * To access the currently connected WebSocket url, use PartySocket#url.\n   */\n  get roomUrl(): string {\n    return this._pkurl;\n  }\n\n  // a `fetch` method that uses (almost) the same options as `PartySocket`\n  static async fetch(\n    options: PartyFetchOptions,\n    init?: RequestInit\n  ): Promise<Response> {\n    const party = getPartyInfo(options, \"http\");\n    const url =\n      typeof party.urlProvider === \"string\"\n        ? party.urlProvider\n        : await party.urlProvider();\n    const doFetch = options.fetch ?? fetch;\n    return doFetch(url, init);\n  }\n}\n\nexport { PartySocket };\n\nexport { ReconnectingWebSocket as WebSocket };\n\nfunction getWSOptions(partySocketOptions: PartySocketOptions) {\n  const {\n    id,\n    host: _host,\n    path: _path,\n    party: _party,\n    room: _room,\n    protocol: _protocol,\n    query: _query,\n    protocols,\n    ...socketOptions\n  } = partySocketOptions;\n\n  const _pk = id || generateUUID();\n  const party = getPartyInfo(partySocketOptions, \"ws\", { _pk });\n\n  return {\n    _pk: _pk,\n    _pkurl: party.partyUrl,\n    name: party.name,\n    room: party.room,\n    host: party.host,\n    path: party.path,\n    protocols: protocols,\n    socketOptions: socketOptions,\n    urlProvider: party.urlProvider\n  };\n}\n", "import \"./styles.css\";\n\nimport PartySocket from \"partysocket\";\n\ndeclare const PARTYKIT_HOST: string;\n\nlet pingInterval: ReturnType<typeof setInterval>;\n\n// Let's append all the messages we get into this DOM element\nconst output = document.getElementById(\"app\") as HTMLDivElement;\n\n// Helper function to add a new line to the DOM\nfunction add(text: string) {\n  output.appendChild(document.createTextNode(text));\n  output.appendChild(document.createElement(\"br\"));\n}\n\n// A PartySocket is like a WebSocket, except it's a bit more magical.\n// It handles reconnection logic, buffering messages while it's offline, and more.\nconst conn = new PartySocket({\n  host: PARTYKIT_HOST,\n  room: \"my-new-room\",\n});\n\n// You can even start sending messages before the connection is open!\nconn.addEventListener(\"message\", (event) => {\n  add(`Received -> ${event.data}`);\n});\n\n// Let's listen for when the connection opens\n// And send a ping every 2 seconds right after\nconn.addEventListener(\"open\", () => {\n  add(\"Connected!\");\n  add(\"Sending a ping every 2 seconds...\");\n  // TODO: make this more interesting / nice\n  clearInterval(pingInterval);\n  pingInterval = setInterval(() => {\n    conn.send(\"ping\");\n  }, 1000);\n});\n"], "mappings": ";;;AAWA,IAAI,CAAC,WAAW,eAAe,CAAC,WAAW,OAAO;AAChD,UAAQ,MAAM;;;;;;;;CAQf;AACD;AAEO,IAAM,aAAN,cAAyB,MAAM;EAC7B;EACA;;EAEP,YAAY,OAAc,QAAa;AACrC,UAAM,SAAS,MAAM;AACrB,SAAK,UAAU,MAAM;AACrB,SAAK,QAAQ;EACf;AACF;AAEO,IAAM,aAAN,cAAyB,MAAM;EAC7B;EACA;EACA,WAAW;;;EAGlB,YAAY,OAAO,KAAM,SAAS,IAAI,QAAa;AACjD,UAAM,SAAS,MAAM;AACrB,SAAK,OAAO;AACZ,SAAK,SAAS;EAChB;AACF;AAQA,IAAM,SAAS;EACb;EACA;EACA;AACF;AAEA,SAAS,OAAO,WAAoB,KAAiC;AACnE,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,GAAG;EACrB;AACF;AAEA,SAAS,kBAAkB,GAAU;AAEnC,SAAO,IAAK,EAAU,YAAY,EAAE,MAAM,CAAC;AAC7C;AAEA,SAAS,eAAe,GAAU;AAChC,MAAI,UAAU,GAAG;AACf,UAAMA,OAAM,IAAI,aAAa,EAAE,MAAM,CAAC;AACtC,WAAOA;EACT;AAEA,MAAI,UAAU,KAAK,YAAY,GAAG;AAChC,UAAMA,OAAM,IAAI;;MAEb,EAAE,QAAQ;;MAEV,EAAE,UAAU;MACb;IACF;AACA,WAAOA;EACT;AAEA,MAAI,WAAW,GAAG;AAChB,UAAMA,OAAM,IAAI,WAAW,EAAE,OAAgB,CAAC;AAC9C,WAAOA;EACT;AAEA,QAAM,MAAM,IAAI,MAAM,EAAE,MAAM,CAAC;AAC/B,SAAO;AACT;AA9FA,IAAA;AAgGA,IAAM,SAKN,OAAM,YAAa,eAkBnB,SAAM,KAAU,QAAA,aAAA,OAAA,SAAA,GAAA,UAAA,eACd,OAAA,aAAA;AAAsB,IACtB,aAAA,SAAsB,iBAAmB;AAAI,IAC7C,UAAW;EACX,sBAAA;EACA,sBAAmB,MAAA,KAAA,OAAA,IAAA;EACnB,WAAA;EACA,6BAA4B;EAC5B,mBAAa;EACb,YAAO,OAAA;EACT,qBAAA,OAAA;EAEA,aAAI;EAYJ,OAAqB;AAAmF;AAC9F,IACA,+BAAc;AAAA,IACd,wBAAA,MAAA,+BAAA,YAAA;EACA;EACA,cAAA;EACA;EACA;EACA,mBAAe;EACf,eAAA;EAEA,cAAA;EAEE,eAAA;EACA,gBAAA,CAAA;EACA,eAAA,QAAA,IAAA,KAAA,OAAA;EAEV;EAKE;EACA;EACA,YAAK,KAAA,WAAa,UAAA,CAAA,GAAA;AAClB,UAAK;AACL,SAAI,OAAK;AACP,SAAA,aAAK;AACP,SAAA,WAAA;AACA,QAAI,KAAK,SAAS,aAAa;AAC7B,WAAK,mBAAe;IACtB;AACA,QAAA,KAAK,SAAS,aAAA;AAChB,WAAA,eAAA,KAAA,SAAA;IAEA;AACE,SAAA,SAAO;EACT;EACA,WAAW,aAAO;AAChB,WAAO;EACT;EACA,WAAW,OAAA;AACT,WAAO;EACT;EACA,WAAW,UAAS;AAClB,WAAO;EACT;EAEA,WAAI,SAAa;AACf,WAAO;EACT;EACA,IAAI,aAAO;AACT,WAAO,uBAAsB;EAC/B;EACA,IAAI,OAAA;AACF,WAAO,uBAAsB;EAC/B;EACA,IAAI,UAAS;AACX,WAAO,uBAAsB;EAC/B;EAEA,IAAI,SAAA;AACF,WAAO,uBAAoB;EAC7B;EAEA,IAAI,aAAW;AACb,WAAK,KAAA,MAAA,KAAc,IAAA,aAAA,KAAA;EACnB;EACE,IAAA,WAAS,OAAA;AACX,SAAA,cAAA;AACF,QAAA,KAAA,KAAA;AAAA,WAAA,IAAA,aAAA;IAAA;EAAA;;;;EAOA,IAAA,aAAA;AAAA,WAAA,KAAA,IAAA,KAAA,aAAA,CAAA;EAAA;;;;;;;EAWM,IAAA,iBAAe;AAAA,UACjB,QAAW,KAAA,cAAmB,OAAM,CAAA,KAAA,YAAA;AAClC,UAAA,OAAO,YAAQ,UAAA;AACjB,eAAO,QAAA;MACL,WAAO,mBAAQ,MAAA;AACjB,eAAA,QAAA;MACA,OAAO;AACL,eAAA,QAAA;MACJ;AACF,aAAA;IAAA,GAAA,CAAA;AAAA,WAAA,SAAA,KAAA,MAAA,KAAA,IAAA,iBAAA;EAAA;;;;;EAQA,IAAA,aAAA;AAAA,WAAA,KAAA,MAAA,KAAA,IAAA,aAAA;EAAA;;;;;;EASA,IAAA,WAAA;AAAA,WAAA,KAAA,MAAA,KAAA,IAAA,WAAA;EAAA;;;;EAOoB,IAClB,aAAA;AACA,QAAA,KAAO,KAAK;AAGd,aAAA,KAAA,IAAA;IAAA;AAAA,WAAA,KAAA,SAAA,cAAA,uBAAA,SAKI,uBAAc;EAChB;;;;EACF,IAAA,MAAA;AAKA,WAAI,KAAA,MAAA,KAA2B,IAAA,MAAA;EAC7B;;;;EACF,IAAA,kBAAA;AAKO,WAAA,KAAgD;EAAA;;;;EAKA,UAAA;;;;EAKI,UAAA;;;;EAMpD,YAA0C;;;;;EAM1C,SAAM;;;;;EAKT,MAAA,OAAK,KAAO,QAAA;AACZ,SAAA,eAAA;AACF,SAAA,mBAAA;AACA,SAAI,eAAS;AACX,QAAA,CAAA,KAAK,KAAO;AACZ,WAAA,OAAA,gCAAA;AACF;IACA;AACF,QAAA,KAAA,IAAA,eAAA,KAAA,QAAA;AAAA,WAAA,OAAA,uBAAA;AAAA;IAAA;AAAA,SAAA,IAAA,MAAA,MAAA,MAAA;EAMO;;;;;EAKH,UAAK,MAAA,QAAS;AAChB,SAAA,mBAAO;AACL,SAAA,eAAiB;AACjB,SAAA,cAAc;AAChB,QAAA,CAAA,KAAA,OAAA,KAAA,IAAA,eAAA,KAAA,QAAA;AACF,WAAA,SAAA;IAAA,OAAA;AAAA,WAAA,YAAA,MAAA,MAAA;AAAA,WAAA,SAAA;IAKO;EACL;;;;EAIE,KAAA,MAAM;AAEN,QAAA,KAAI,OAAK,KAAA,IAAc,eAAS,KAAA,MAAA;AAC9B,WAAA,OAAK,QAAO,IAAA;AACZ,WAAA,IAAK,KAAA,IAAA;IAAuB,OAC9B;AACF,YAAA,EAAA,sBAAA,QAAA,oBAAA,IACF,KAAA;AAEQ,UAAA,KAAU,cAAiB,SAAA,qBAAA;AAC7B,aAAK,OAAA,WAAgB,IAAA;AACvB,aAAK,cAAa,KAAQ,IAAG;MAC/B;IACF;EAEQ;EACN,UAAM,MAAA;AAAA,QACJ,KAAA,SAAA,OAAA;AACA,WAAA,aAAA,QAAuB,GAAA,IAAQ;IAAA;EACA;EAEjC,gBAAY;AACZ,UAAI;MACF,8BACE,QACA;MACF,uBAAY,QAAA;MACV,uBAAQ,QAAA;IAAA,IACV,KAAA;AACF,QAAA,QAAA;AACA,QAAA,KAAK,cAAO,GAAc;AAC1B,cACF,uBAEQ,gCAAuB,KAAA,cAAA;AAC7B,UAAA,QAAW,sBAAqB;AAC9B,gBAAA;MACD;IACH;AAEQ,SAAA,OAAA,cACN,KAAA;AAEA,WAAK;EAEL;EAIE,QAAA;AACF,WAAA,IAAA,QAAA,CAAA,YAAA;AAEA,iBAAW,SAAA,KAAA,cAAsB,CAAA;IAC/B,CAAA;EACA;EAEA,kBAAW,mBAAc;AACvB,QAAA,CAAA,kBAAe,QAAQ,QAAS,QAAA,IAAA;AAAA,QAIlC,OAAI,sBAAgB,YAClB,MAAA,QAAO,iBAAA,GACT;AACF,aAAA,QAAA,QAAA,iBAAA;IAEA;AACF,QAAA,OAAA,sBAAA,YAAA;AAEQ,YAAA,YAAY,kBAA2C;AAC7D,UAAI,CAAA,UAAO,QAAA,QAAgB,QAAU,IAAA;AACnC,UAAA,OAAO,cAAgB,YAAW,MAAA,QAAA,SAAA,GAAA;AACpC,eAAA,QAAA,QAAA,SAAA;MACA;AACE,UAAA,UAAY,MAAA;AACZ,eAAI;MACF;IAA0B;AAI5B,UAAI,MAAI,mBAAM;EACZ;EAAO,YACT,aAAA;AAGF,QAAA,OAAA,gBAAA,UAAA;AACA,aAAM,QAAM,QAAa,WAAA;IAC3B;AAEQ,QAAA,OAAW,gBAAA,YAAA;AACjB,YAAI,MAAK,YAAiB;AACxB,UAAA,OAAA,QAAA,UAAA;AACF,eAAA,QAAA,QAAA,GAAA;MACA;AAEA,UAAM,IAAA,MAAA;AACJ,eAAA;MACA;IACF;AAEA,UAAI,MAAK,aAAe;EACtB;EACA,WAAA;AACF,QAAA,KAAA,gBAAA,CAAA,KAAA,kBAAA;AAEA;IAEA;AACA,SAAK,eAAA;AAEL,UAAK;MACG,aACJ,QAAY;MAAA,oBACO,QAAS;IAAA,IAC1B,KAAK;AAAyC,QAC/C,KAAA,eAAA,YAAA;AAEF,WAAK,OAAO,uBAAe,KAAA,aAAA,MAAA,UAAA;AAE1B;IACE;AACA,SAAA;AAAA,SACF,OAAA,WAAA,KAAA,WAAA;AACA,SAAA,iBACiB;AAIf,SAAA,MAAA,EAAc;MAAA,MAAA,QAAA,IAAA;QAAA,KAAA,YAAA,KAAA,IAAA;QAAA,KAAA,kBAAA,KAAA,cAAA,IAAA;MAAA,CAAA;IAAA,EAAA,KAAA,CAAA,CAAA,KAAA,SAAA,MAAA;AAAA,UAAA,KAAA,cAAA;AAAA,aAAA,eAAA;AAAA;MAAA;AAAA,UAavB,CAAA,KAAA,SAAA,aACS,OAAA,cAAA,eACF,CAAA,8BACA;AACA,gBAAY,MAAA;;;;;;;;;;;;;CAgBb;AAGG,uCAAiB;MACvB;AACA,YAAK,KAAA,KAAa,SAAW,aAAW;AAC1C,WAAA,OAAA,WAAA,EAAA,KAAA,UAAA,CAAA;AAEQ,WAAA,MAAY,YAAa,IAAiB,GAAA,KAAA,SAAA,IAAA,IAAA,GAAA,GAAA;AAChD,WAAK,IAAA,aAAe,KAAA;AAChB,WAAC,eAAU;AACb,WAAA,cAAA;AACF,WAAA,kBAAA;QACK,MAAA,KAAA,eAAiB;QAClB;MACF;IAIE,CAAA,EACF,MAAA,CAAA,QAAA;AACA,WAAK,eAAiB;AACxB,WAAS,aAAO,IAAA,OAAA,WAAA,MAAA,IAAA,OAAA,GAAA,IAAA,CAAA;IAEhB,CAAA;EACF;EAEQ,iBAAc;AACpB,SAAK,OAAO,eAAa;AACzB,SAAK,aAAA,IAAc,OAAA,WAAA,MAAA,SAAA,GAAA,IAAA,CAAA;EACrB;EAEQ,YAAA,OAAe,KAAA,QAAiB;AACtC,SAAK,eAAO;AACZ,QAAA,CAAA,KAAQ,KAAA;AAER;IACA;AAEA,SAAA,iBAAiB;AAEjB,QAAA;AAGA,UA1iBJ,KAAAC,IAAAA,eAAAA,KAAAA,QA0iB4C,KAAA,IAAAA,eAAK,KAAL,YAAuB;AAC/D,aAAK,IAAA,MAAA,MAAiB,MAAA;MAEtB;AACE,WAAK,aAAY,IAAA,OAAA,WAAA,MAAA,QAAA,IAAA,CAAA;IACnB,SAAA,OAAA;IAAA;EACA;EACF,cAAA;AAEQ,SAAA,OAAA,aAAkB;AACxB,SAAK,cAAO;EAEZ;EACE,cAAK,CAAA,UAAe;AACtB,SAAA,OAAA,YAAA;AACA,UAAK,EAAA,YAAc,QAAA,UAAiB,IAAA,KAAA;AACtC,iBAAA,KAAA,eAAA;AAEQ,SAAA,iBAAgB,WAAsB,MAAA,KAAA,YAAA,GAAA,SAAA;AAC5C,WAAK,KAAO,KAAA,0BAA4B;AACxC,SAAK,IAAA,aAAA,KAAA;AAAA,SACH,cAAA,QAAA,CAAA,YAAA;AACA,UAAA;AACF,cAAA,MAAA,KAAA,QAAA,OAAA,SAAA,IAAA,KAAA,OAAA;IAEA,CAAA;AACE,SAAA,gBAAkB,CAAA;AACpB,QAAA,KAAA,QAAA;AACA,WAAK,OAAO,KAAA;IACZ;AAEA,SAAK,cAAS,WAAA,KAAA,CAAA;EAChB;EAEQ,iBAAgB,CAAA,UAAsB;AAC5C,SAAK,OAAO,eAAa;AACzB,QAAA,KAAK,WAAe;AAEpB,WAAI,UAAK,KAAA;IACP;AACF,SAAA,cAAA,WAAA,KAAA,CAAA;EAEA;EACE,eAAK,CAAA,UAAa;AACpB,SAAA,OAAA,eAAA,MAAA,OAAA;AACA,SAAK,YAAA,QAAc,MAAW,YAAM,YAAA,YAAA,MAAA;AACtC,QAAA,KAAA,SAAA;AAEQ,WAAA,QAAA,KAAmB;IACzB;AACE,SAAA,OAAA,sBAAA;AACF,SAAA,cAAA,WAAA,KAAA,CAAA;AACA,SAAK,SAAO;EACZ;EACA,eAAS,CAAA,UAAA;AACT,SAAK,OAAI,aAAA;AAET,SAAK,eAAI;AACX,QAAA,KAAA,kBAAA;AAEQ,WAAA,SAAgB;IACtB;AACE,QAAA,KAAA,SAAA;AACF,WAAA,QAAA,KAAA;IACA;AACA,SAAK,cAAI,WAAiB,KAAQ,CAAA;EAClC;EACA,mBAAS;AAET,QAAA,CAAK,KAAI,KAAA;AACX;IAEQ;AACN,SAAA,OAAA,iBAAkB;AAClB,SAAA,IAAA,oBAAkB,QAAc,KAAA,WAAA;AAClC,SAAA,IAAA,oBAAA,SAAA,KAAA,YAAA;AACF,SAAA,IAAA,oBAAA,WAAA,KAAA,cAAA;;;;;;;;;;;;;;;;;;;;;AChnBA,SAAM,eACJ;AA8BF,MAAA,OAAS,WAAuB,eAAA,OAAA,YAAA;AAE9B,WAAI,OAAO,WAAW;EACpB;AACF,MAAA,IAAA,qCAAA,KAAA,GAAA,QAAA;AACA,MAAI,KACJ,OACG,gBAAO,eAKV,YAAO,OACL,YAAa,IAAA,IAAO,OACpB;AAEE,SAAA,uCAAmB,QAAA,SAAA,SAAA,GAAA;AACnB,QAAA,IAAI,KAAK,OAAM,IAAM;AACvB,QAAA,IAAO,GAAA;AAEL,WAAK,IAAA,KAAK,KAAK;AACf,UAAA,KAAK,MAAK,IAAM,EAAK;IACvB,OAAA;AACA,WAAA,KAAQ,KAAM,KAAM;AACrB,WAAA,KAAA,MAAA,KAAA,EAAA;IACH;AAEA,YAAS,MAAA,MACP,IAAA,IAAA,IAAA,GAAA,SACA,EAAA;EAGA,CAAA;AAAM;AACE,SACN,aAAM,oBAAA,iBAAA,gBAAA,CAAA,GAAA;AAAA,QACN;IACA,MAAA;IACA,MAAA;IACA,UAAA;IACA;IACA;IACF;IAGA;IAEA;EACE,IAAA;AACF,MAAA,OAAA,QAAA,QAAA,6BAAA,EAAA;AAEA,MAAI,KAAA,SAAA,GAAA,GAAA;AACF,WAAM,KAAI,MAAM,GAAA,EAAA;EAClB;AAEA,MAAA,WAAa,OAAS,SAAA,QAAA,WAAA,GAAA,GAAA;AACtB,UAAM,IAAA,MAAO,kCAA0B;EACvC;AASoC,QAAA,OAAA,SAAA;AAAA,QAE9B,OAAA,UAAA,IAAA,OAAA,KAAA;AAAA,QAAA,WAAA,gBAEA,KAAG,WAAA,YAAe,KAAA,KAAA,WAAA,YAAA,KAExB,KAAM,WAAU,UAAW,KAE3B,KAAM,WAAWC,KAAAA,KAEb,KAAG,WAAO,MAAQ,KAClB,KAAG,MAAO,GAAA,EAAQA,CAAAA,KAAK,QACvB,KAAA,MAAA,GAAA,EAAA,CAAA,KAAA,QAGJ,KAAM,WAAA,kBACG;;IAKP;;;IAEA,GAAA,eAAA;;AAAA,QACA,UAAA,GAAA,QAAA,MAAA,IAAA,IAAA,YAAA,GAAA,UAAA,SAAA,IAAA,IAAA,IAAA,IAAA,EAAA,GAAA,IAAA;AAAA,QACA,UAAA,CAAA,SAAA,CAAA,MACA,GAAA,OAAU,IAAA,IAAA,gBAAA;IACV,GAAA,OAAA,QAAA,aAAA;IACF,GAAA,OAAA,QAAA,MAAA,EAAA,OAAA,aAAA;EACF,CAAA,CAAA;AAQA,QAAqB,cAQnB,OAAA,UAAqB,aACnB,YAAM,QAAY,MAAA,MAAa,CAAA,IAE/B,QAAM,KAAU;AAHG,SAAA;IAKnB;IACF;IAbA;IACA;IACA;IACA,UAAA;IACA;EACA;AAAA;AAWE,IAAA,cAAM,cAAY,sBAAa;EAAA,YACrB,oBAAA;AAAA,UACL,YAAA,aAAA,kBAAA;AAAA,UACH,UAAM,aAAmB,UAAQ,WAAK,UAAA,aAAA;AAAA,SACtC,qBAAM;AAAgC,SACtC,gBAAM,SAAmB;EAAa;EAGxC;EACA;EACA;EAEA;EACF;EAEQ;EACN,iBAAa,oBAAoB;AAEjC,UAAK,YAAM,aAAA;MACX,GAAK,KAAA;MACL,GAAK;MACL,MAAK,mBAAO,QAAA,KAAA;MACZ,MAAK,mBAAO,QAAA,KAAA;MACZ,MAAK,mBAAO,QAAA,KAAA;IACd,CAAA;AAEO,SAAA,OACL,UAEM;AACN,SAAK,aAAa,UAAM;AACtB,SAAA,WAAU,UAAA;AAAA,SACR,gBAAA,SAAA;EAAA;EACF,gBACF,WAAA;AACA,UAAM,EAAA,KAAA,QAAU,MAAM,MAAM,MAAA,KAAA,IAAA;AAC9B,SAAA,MAAA;AAEA,SAAI,SAAK;AACP,SAAA,OAAY;AACd,SAAA,OAAA;AAAA,SAAA,OAAA;AAAA,SAAA,OAAA;EAAA;EAAA,UAAA,MAAA,QAAA;AAMA,QAAI,CAAA,KAAA,QAAkB,CAAA,KAAA,MAAA;AACpB,YAAO,IAAK;QACd;MAAA;IAGA;AAIE,UAAM,UAAQ,MAAA,MAAa;EAC3B;EAIA,IAAA,KAAM;AACN,WAAO,KAAA;EACT;;;;;EASE,IACA,UAAM;AACN,WAAM,KAAA;EAAA;;EAEA,aACI,MAAA,SAAA,MAAA;AACV,UAAA,QAAO,aAAA,SAAA,MAAA;AACP,UAAA,MACA,OAAG,MAAA,gBAAA,WACD,MAAA,cAEE,MAAM,MAAM,YAAa;AAC/B,UAAM,UAAQ,QAAA,SAAa;AAE3B,WAAO,QAAA,KAAA,IAAA;EAAA;AACL;AACc,SACd,aAAY,oBAAA;AAAA,QACZ;IACA;IACA,MAAM;IACN,MAAA;IACA,OAAA;IACA,MAAA;IACF,UAAA;IACF,OAAA;;;;;;;;;;;;;;;;;;;;ACtPA,IAAI;AAGJ,IAAM,SAAS,SAAS,eAAe,KAAK;AAG5C,SAAS,IAAI,MAAc;AACzB,SAAO,YAAY,SAAS,eAAe,IAAI,CAAC;AAChD,SAAO,YAAY,SAAS,cAAc,IAAI,CAAC;AACjD;AAIA,IAAM,OAAO,IAAI,YAAY;AAAA,EAC3B,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAGD,KAAK,iBAAiB,WAAW,CAAC,UAAU;AAC1C,MAAI,eAAe,MAAM,IAAI,EAAE;AACjC,CAAC;AAID,KAAK,iBAAiB,QAAQ,MAAM;AAClC,MAAI,YAAY;AAChB,MAAI,mCAAmC;AAEvC,gBAAc,YAAY;AAC1B,iBAAe,YAAY,MAAM;AAC/B,SAAK,KAAK,MAAM;AAAA,EAClB,GAAG,GAAI;AACT,CAAC;", "names": ["evt", "_a", "query"]}