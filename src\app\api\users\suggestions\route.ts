import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, subscriptions } from "@/lib/db/schema";
import { eq, and, or, not, sql, desc } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const offset = (page - 1) * limit;

    // Get user's existing subscriptions
    const existingSubscriptions = await db
      .select({
        targetUserId: subscriptions.targetUserId
      })
      .from(subscriptions)
      .where(eq(subscriptions.subscriberId, session.user.id));

    const connectedUserIds = existingSubscriptions.map(sub => sub.targetUserId);

    // Add current user to exclude list
    connectedUserIds.push(session.user.id);

    // Get mutual friends count for each potential suggestion
    const suggestions = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
        bio: users.bio,
        location: users.location,
        createdAt: users.createdAt,
        mutualSubscriptionsCount: sql<number>`(
          SELECT COUNT(DISTINCT s2.targetUserId)
          FROM ${subscriptions} s1
          JOIN ${subscriptions} s2 ON s1.targetUserId = s2.subscriberId
          WHERE s1.subscriberId = ${session.user.id}
          AND s2.targetUserId = users.id
          AND s2.targetUserId != ${session.user.id}
        )`.as('mutualSubscriptionsCount')
      })
      .from(users)
      .where(
        not(sql`${users.id} IN (${sql.join(connectedUserIds.map(id => sql`${id}`), sql`, `)})`)
      )
      .orderBy(
        desc(sql`mutualSubscriptionsCount`),
        desc(users.createdAt)
      )
      .limit(limit)
      .offset(offset);

    // Format the response
    const formattedSuggestions = suggestions.map(user => ({
      id: user.id,
      name: user.name,
      username: user.username,
      image: user.image,
      bio: user.bio,
      location: user.location,
      mutualFriendsCount: user.mutualSubscriptionsCount || 0,
      createdAt: user.createdAt,
    }));

    return NextResponse.json({
      suggestions: formattedSuggestions,
      pagination: {
        page,
        limit,
        hasMore: formattedSuggestions.length === limit
      }
    });

  } catch (error) {
    console.error("Error fetching user suggestions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
