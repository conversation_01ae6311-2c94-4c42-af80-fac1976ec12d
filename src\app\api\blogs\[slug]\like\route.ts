import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogLikes } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

// Toggle like on a blog
export async function POST(
  req: Request,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { slug } = params;

    // First, get the blog to find its ID
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
      columns: { id: true },
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Check if user has already liked this blog
    const existingLike = await db.query.blogLikes.findFirst({
      where: and(
        eq(blogLikes.blogId, blog.id),
        eq(blogLikes.userId, session.user.id)
      ),
    });

    if (existingLike) {
      // Unlike: remove the like
      await db.delete(blogLikes).where(eq(blogLikes.id, existingLike.id));
      
      return NextResponse.json({
        message: "Blog unliked successfully",
        isLiked: false,
      });
    } else {
      // Like: add the like
      const likeId = uuidv4();
      await db.insert(blogLikes).values({
        id: likeId,
        userId: session.user.id,
        blogId: blog.id,
      });

      return NextResponse.json({
        message: "Blog liked successfully",
        isLiked: true,
      });
    }
  } catch (error) {
    console.error("Error toggling blog like:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
