import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, users, comments, likes, postReports } from "@/lib/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { desc, eq, like, and, or, count, sql, asc } from "drizzle-orm";

const postSchema = z.object({
  content: z.string().max(5000).optional().default(''),
  images: z.array(z.string().url()).optional(),
  videos: z.array(z.string().url()).optional(),
  privacy: z.enum(["public", "subscribers", "private"]).default("public"),
  sharedPostId: z.string().optional(),
  backgroundColor: z.string().optional(),
  feeling: z.string().optional(),
  activity: z.string().optional(),
  location: z.string().optional(),
  formattedContent: z.boolean().optional(),
  userId: z.string().optional(), // Allow admin to create post as another user
}).refine(data => {
  // Either content, images, or videos must be provided
  return !!data.content || (data.images && data.images.length > 0) || (data.videos && data.videos.length > 0);
}, {
  message: "Post must contain either text content, images, or videos",
  path: ["content"],
});

const querySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  search: z.string().optional(),
  privacy: z.string().optional(),
  reported: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});

// Get all posts with admin filtering capabilities
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams);

    const validatedQuery = querySchema.parse(queryParams);

    const page = parseInt(validatedQuery.page || "1");
    const limit = parseInt(validatedQuery.limit || "10");
    const offset = (page - 1) * limit;

    // Build query conditions
    let queryConditions = [];

    // Search by content or user
    if (validatedQuery.search) {
      queryConditions.push(
        like(posts.content, `%${validatedQuery.search}%`)
      );
    }

    // Filter by privacy
    if (validatedQuery.privacy && validatedQuery.privacy !== "all") {
      queryConditions.push(eq(posts.privacy, validatedQuery.privacy as any));
    }

    // Filter by reported status
    if (validatedQuery.reported === "true") {
      queryConditions.push(eq(posts.isReported, true));
    }

    // Filter by date range
    if (validatedQuery.dateFrom) {
      const dateFrom = new Date(validatedQuery.dateFrom);
      queryConditions.push(sql`${posts.createdAt} >= ${dateFrom}`);
    }

    if (validatedQuery.dateTo) {
      const dateTo = new Date(validatedQuery.dateTo);
      queryConditions.push(sql`${posts.createdAt} <= ${dateTo}`);
    }

    // Determine sort order
    const sortField = validatedQuery.sortBy || "createdAt";
    const sortOrder = validatedQuery.sortOrder === "asc" ? "asc" : "desc";

    // Execute query with conditions
    const whereCondition = queryConditions.length > 0
      ? and(...queryConditions)
      : undefined;

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(posts)
      .where(whereCondition);

    const totalCount = Number(totalCountResult[0]?.count || 0);

    // Get posts with pagination
    const allPosts = await db.query.posts.findMany({
      where: whereCondition,
      orderBy: sortOrder === "asc"
        ? [posts.createdAt]
        : [desc(posts.createdAt)],
      limit,
      offset,
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        shares: true,
      },
    });

    // Format the response
    const formattedPosts = allPosts.map(post => ({
      ...post,
      _count: {
        likes: post.likes.length,
        comments: post.comments.length,
        shares: post.shares.length,
      },
      reported: post.isReported || false,
    }));

    return NextResponse.json({
      posts: formattedPosts,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a new post as admin
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = postSchema.parse(body);

    // If userId is provided, check if the user exists
    let userId = session.user.id;
    if (validatedData.userId) {
      const userExists = await db.query.users.findFirst({
        where: eq(users.id, validatedData.userId),
      });

      if (!userExists) {
        return NextResponse.json(
          { message: "User not found" },
          { status: 404 }
        );
      }

      userId = validatedData.userId;
    }

    const postId = uuidv4();

    // Insert the post into the database
    await db.insert(posts).values({
      id: postId,
      userId,
      content: validatedData.content,
      images: validatedData.images || null,
      videos: validatedData.videos || null,
      privacy: validatedData.privacy,
      sharedPostId: validatedData.sharedPostId || null,
      backgroundColor: validatedData.backgroundColor || null,
      feeling: validatedData.feeling || null,
      activity: validatedData.activity || null,
      location: validatedData.location || null,
      formattedContent: validatedData.formattedContent || false,
      scheduledAt: null,
      isPublished: true,
    });

    return NextResponse.json(
      { message: "Post created successfully", id: postId },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating post:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
