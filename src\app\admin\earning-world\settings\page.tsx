"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Switch } from "@/components/ui/Switch";
import { Spinner } from "@/components/ui/Spinner";
import { toast } from "react-hot-toast";
import {
  Cog6ToothIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon
} from "@heroicons/react/24/outline";

interface EarningWorldSettings {
  // General Settings
  isEnabled: boolean;
  maintenanceMode: boolean;
  
  // Referral Settings
  referralEnabled: boolean;
  referralRewardAmount: string;
  referralMinPayoutThreshold: string;
  referralMaxPerUser: number;
  referralRequiresVerification: boolean;
  referralRewardBothUsers: boolean;
  referralReferredUserReward: string;
  
  // Blog Monetization Settings
  blogMonetizationEnabled: boolean;
  blogCprRate: string;
  blogMinPayoutThreshold: string;
  blogMinReadDuration: number;
  blogRequiresApproval: boolean;
  
  // Payout Settings
  payoutEnabled: boolean;
  payoutMinAmount: string;
  payoutMaxAmount: string;
  payoutProcessingFee: string;
  payoutAutoApproval: boolean;
  payoutAutoApprovalLimit: string;
  
  // Security Settings
  requireEmailVerification: boolean;
  requirePhoneVerification: boolean;
  enableTwoFactorAuth: boolean;
  maxDailyWithdrawals: number;
  
  // System Limits
  maxEarningWalletBalance: string;
  dailyEarningLimit: string;
  monthlyEarningLimit: string;
  
  // Notification Settings
  notifyAdminOnPayouts: boolean;
  notifyUsersOnEarnings: boolean;
  emailNotificationsEnabled: boolean;
}

type TabType = 'general' | 'referrals' | 'monetization' | 'payouts' | 'security' | 'limits';

export default function AdminEarningWorldSettingsPage() {
  const [activeTab, setActiveTab] = useState<TabType>('general');
  const [settings, setSettings] = useState<EarningWorldSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const tabs = [
    {
      id: 'general' as TabType,
      name: 'General',
      icon: Cog6ToothIcon,
      description: 'General earning world settings',
    },
    {
      id: 'referrals' as TabType,
      name: 'Referrals',
      icon: UserGroupIcon,
      description: 'Referral system configuration',
    },
    {
      id: 'monetization' as TabType,
      name: 'Monetization',
      icon: CurrencyDollarIcon,
      description: 'Blog monetization settings',
    },
    {
      id: 'payouts' as TabType,
      name: 'Payouts',
      icon: BanknotesIcon,
      description: 'Payout system configuration',
    },
    {
      id: 'security' as TabType,
      name: 'Security',
      icon: ShieldCheckIcon,
      description: 'Security and verification settings',
    },
    {
      id: 'limits' as TabType,
      name: 'Limits',
      icon: ChartBarIcon,
      description: 'System limits and thresholds',
    },
  ];

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/earning-world/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data.data);
      } else {
        toast.error('Failed to load settings');
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchSettings();
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      const response = await fetch('/api/admin/earning-world/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        toast.success('Settings updated successfully');
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Failed to update settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to update settings');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key: keyof EarningWorldSettings, value: any) => {
    if (!settings) return;
    setSettings({ ...settings, [key]: value });
  };

  const renderTabContent = () => {
    if (!settings) return null;

    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure general earning world system settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="isEnabled">Enable Earning World System</Label>
                    <p className="text-sm text-gray-500">
                      Master switch for the entire earning world system
                    </p>
                  </div>
                  <Switch
                    id="isEnabled"
                    checked={settings.isEnabled}
                    onCheckedChange={(checked) => updateSetting('isEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                    <p className="text-sm text-gray-500">
                      Temporarily disable earning features for maintenance
                    </p>
                  </div>
                  <Switch
                    id="maintenanceMode"
                    checked={settings.maintenanceMode}
                    onCheckedChange={(checked) => updateSetting('maintenanceMode', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="notifyAdminOnPayouts">Admin Payout Notifications</Label>
                    <p className="text-sm text-gray-500">
                      Notify admins when new payout requests are made
                    </p>
                  </div>
                  <Switch
                    id="notifyAdminOnPayouts"
                    checked={settings.notifyAdminOnPayouts}
                    onCheckedChange={(checked) => updateSetting('notifyAdminOnPayouts', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="notifyUsersOnEarnings">User Earning Notifications</Label>
                    <p className="text-sm text-gray-500">
                      Notify users when they earn money
                    </p>
                  </div>
                  <Switch
                    id="notifyUsersOnEarnings"
                    checked={settings.notifyUsersOnEarnings}
                    onCheckedChange={(checked) => updateSetting('notifyUsersOnEarnings', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailNotificationsEnabled">Email Notifications</Label>
                    <p className="text-sm text-gray-500">
                      Enable email notifications for earning activities
                    </p>
                  </div>
                  <Switch
                    id="emailNotificationsEnabled"
                    checked={settings.emailNotificationsEnabled}
                    onCheckedChange={(checked) => updateSetting('emailNotificationsEnabled', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'referrals':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Referral System Settings</CardTitle>
                <CardDescription>
                  Configure referral program parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="referralEnabled">Enable Referral System</Label>
                  <Switch
                    id="referralEnabled"
                    checked={settings.referralEnabled}
                    onCheckedChange={(checked) => updateSetting('referralEnabled', checked)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="referralRewardAmount">Referral Reward Amount ($)</Label>
                    <Input
                      id="referralRewardAmount"
                      type="number"
                      step="0.01"
                      value={settings.referralRewardAmount}
                      onChange={(e) => updateSetting('referralRewardAmount', e.target.value)}
                      disabled={!settings.referralEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="referralMinPayoutThreshold">Min Payout Threshold ($)</Label>
                    <Input
                      id="referralMinPayoutThreshold"
                      type="number"
                      step="0.01"
                      value={settings.referralMinPayoutThreshold}
                      onChange={(e) => updateSetting('referralMinPayoutThreshold', e.target.value)}
                      disabled={!settings.referralEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="referralMaxPerUser">Max Referrals Per User</Label>
                    <Input
                      id="referralMaxPerUser"
                      type="number"
                      value={settings.referralMaxPerUser}
                      onChange={(e) => updateSetting('referralMaxPerUser', parseInt(e.target.value))}
                      disabled={!settings.referralEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="referralReferredUserReward">Referred User Reward ($)</Label>
                    <Input
                      id="referralReferredUserReward"
                      type="number"
                      step="0.01"
                      value={settings.referralReferredUserReward}
                      onChange={(e) => updateSetting('referralReferredUserReward', e.target.value)}
                      disabled={!settings.referralEnabled || !settings.referralRewardBothUsers}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="referralRequiresVerification">Require Verification</Label>
                  <Switch
                    id="referralRequiresVerification"
                    checked={settings.referralRequiresVerification}
                    onCheckedChange={(checked) => updateSetting('referralRequiresVerification', checked)}
                    disabled={!settings.referralEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="referralRewardBothUsers">Reward Both Users</Label>
                  <Switch
                    id="referralRewardBothUsers"
                    checked={settings.referralRewardBothUsers}
                    onCheckedChange={(checked) => updateSetting('referralRewardBothUsers', checked)}
                    disabled={!settings.referralEnabled}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'monetization':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Blog Monetization Settings</CardTitle>
                <CardDescription>
                  Configure blog monetization parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="blogMonetizationEnabled">Enable Blog Monetization</Label>
                  <Switch
                    id="blogMonetizationEnabled"
                    checked={settings.blogMonetizationEnabled}
                    onCheckedChange={(checked) => updateSetting('blogMonetizationEnabled', checked)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="blogCprRate">CPR Rate ($ per 1000 reads)</Label>
                    <Input
                      id="blogCprRate"
                      type="number"
                      step="0.01"
                      value={settings.blogCprRate}
                      onChange={(e) => updateSetting('blogCprRate', e.target.value)}
                      disabled={!settings.blogMonetizationEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="blogMinPayoutThreshold">Min Payout Threshold ($)</Label>
                    <Input
                      id="blogMinPayoutThreshold"
                      type="number"
                      step="0.01"
                      value={settings.blogMinPayoutThreshold}
                      onChange={(e) => updateSetting('blogMinPayoutThreshold', e.target.value)}
                      disabled={!settings.blogMonetizationEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="blogMinReadDuration">Min Read Duration (seconds)</Label>
                    <Input
                      id="blogMinReadDuration"
                      type="number"
                      value={settings.blogMinReadDuration}
                      onChange={(e) => updateSetting('blogMinReadDuration', parseInt(e.target.value))}
                      disabled={!settings.blogMonetizationEnabled}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="blogRequiresApproval">Require Admin Approval</Label>
                    <p className="text-sm text-gray-500">
                      Blogs must be approved before monetization
                    </p>
                  </div>
                  <Switch
                    id="blogRequiresApproval"
                    checked={settings.blogRequiresApproval}
                    onCheckedChange={(checked) => updateSetting('blogRequiresApproval', checked)}
                    disabled={!settings.blogMonetizationEnabled}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'payouts':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payout System Settings</CardTitle>
                <CardDescription>
                  Configure payout processing parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="payoutEnabled">Enable Payout System</Label>
                  <Switch
                    id="payoutEnabled"
                    checked={settings.payoutEnabled}
                    onCheckedChange={(checked) => updateSetting('payoutEnabled', checked)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="payoutMinAmount">Minimum Payout Amount ($)</Label>
                    <Input
                      id="payoutMinAmount"
                      type="number"
                      step="0.01"
                      value={settings.payoutMinAmount}
                      onChange={(e) => updateSetting('payoutMinAmount', e.target.value)}
                      disabled={!settings.payoutEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="payoutMaxAmount">Maximum Payout Amount ($)</Label>
                    <Input
                      id="payoutMaxAmount"
                      type="number"
                      step="0.01"
                      value={settings.payoutMaxAmount}
                      onChange={(e) => updateSetting('payoutMaxAmount', e.target.value)}
                      disabled={!settings.payoutEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="payoutProcessingFee">Processing Fee ($)</Label>
                    <Input
                      id="payoutProcessingFee"
                      type="number"
                      step="0.01"
                      value={settings.payoutProcessingFee}
                      onChange={(e) => updateSetting('payoutProcessingFee', e.target.value)}
                      disabled={!settings.payoutEnabled}
                    />
                  </div>

                  <div>
                    <Label htmlFor="payoutAutoApprovalLimit">Auto Approval Limit ($)</Label>
                    <Input
                      id="payoutAutoApprovalLimit"
                      type="number"
                      step="0.01"
                      value={settings.payoutAutoApprovalLimit}
                      onChange={(e) => updateSetting('payoutAutoApprovalLimit', e.target.value)}
                      disabled={!settings.payoutEnabled || !settings.payoutAutoApproval}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="payoutAutoApproval">Auto Approval</Label>
                    <p className="text-sm text-gray-500">
                      Automatically approve payouts below the limit
                    </p>
                  </div>
                  <Switch
                    id="payoutAutoApproval"
                    checked={settings.payoutAutoApproval}
                    onCheckedChange={(checked) => updateSetting('payoutAutoApproval', checked)}
                    disabled={!settings.payoutEnabled}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Configure security and verification requirements
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="requireEmailVerification">Require Email Verification</Label>
                  <Switch
                    id="requireEmailVerification"
                    checked={settings.requireEmailVerification}
                    onCheckedChange={(checked) => updateSetting('requireEmailVerification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="requirePhoneVerification">Require Phone Verification</Label>
                  <Switch
                    id="requirePhoneVerification"
                    checked={settings.requirePhoneVerification}
                    onCheckedChange={(checked) => updateSetting('requirePhoneVerification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="enableTwoFactorAuth">Enable Two-Factor Authentication</Label>
                  <Switch
                    id="enableTwoFactorAuth"
                    checked={settings.enableTwoFactorAuth}
                    onCheckedChange={(checked) => updateSetting('enableTwoFactorAuth', checked)}
                  />
                </div>

                <div>
                  <Label htmlFor="maxDailyWithdrawals">Max Daily Withdrawals</Label>
                  <Input
                    id="maxDailyWithdrawals"
                    type="number"
                    value={settings.maxDailyWithdrawals}
                    onChange={(e) => updateSetting('maxDailyWithdrawals', parseInt(e.target.value))}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'limits':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>System Limits</CardTitle>
                <CardDescription>
                  Configure system limits and thresholds
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="maxEarningWalletBalance">Max Earning Wallet Balance ($)</Label>
                    <Input
                      id="maxEarningWalletBalance"
                      type="number"
                      step="0.01"
                      value={settings.maxEarningWalletBalance}
                      onChange={(e) => updateSetting('maxEarningWalletBalance', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="dailyEarningLimit">Daily Earning Limit ($)</Label>
                    <Input
                      id="dailyEarningLimit"
                      type="number"
                      step="0.01"
                      value={settings.dailyEarningLimit}
                      onChange={(e) => updateSetting('dailyEarningLimit', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="monthlyEarningLimit">Monthly Earning Limit ($)</Label>
                    <Input
                      id="monthlyEarningLimit"
                      type="number"
                      step="0.01"
                      value={settings.monthlyEarningLimit}
                      onChange={(e) => updateSetting('monthlyEarningLimit', e.target.value)}
                    />
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-2" />
                    <div>
                      <h3 className="text-sm font-medium text-yellow-800">
                        Important Notice
                      </h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        Setting limits too low may affect user experience. Consider your platform's earning potential when configuring these values.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Earning World Settings</h1>
            <p className="text-gray-600 mt-1">
              Configure earning world system parameters and policies
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={handleSaveSettings} disabled={saving}>
              {saving ? <Spinner size="sm" className="mr-2" /> : <CheckCircleIcon className="h-5 w-5 mr-2" />}
              Save Settings
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5 inline mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {renderTabContent()}
      </div>
    </AdminLayout>
  );
}
