CREATE TABLE `user_payment_methods` (
	`id` varchar(255) NOT NULL,
	`userId` varchar(255) NOT NULL,
	`type` enum('bank','mobile_banking','card') NOT NULL,
	`name` varchar(255) NOT NULL,
	`details` json NOT NULL,
	`isDefault` boolean NOT NULL DEFAULT false,
	`isActive` boolean NOT NULL DEFAULT true,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`updatedAt` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `user_payment_methods_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `payment_gateways` MODIFY COLUMN `type` enum('stripe','paypal','sslcommerz','bkash','nagad','rocket','bank','uddoktapay','manual') NOT NULL;--> statement-breakpoint
ALTER TABLE `users` ADD `phone` varchar(20);