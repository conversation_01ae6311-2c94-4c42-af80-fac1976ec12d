"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { formatTimeAgo } from "@/lib/utils";
import { PostTime } from "@/components/ui/TimeDisplay";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { MonetizationStatusBadge } from "@/components/blog/MonetizationStatusBadge";
import { BlogViewCounter } from "@/components/blog/BlogViewCounter";
import ConfirmationModal from "@/components/ui/ConfirmationModal";
import {
  HeartIcon,
  BookmarkIcon,
  ShareIcon,
  ClockIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  EllipsisVerticalIcon,
  FireIcon
} from "@heroicons/react/24/outline";
import {
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid
} from "@heroicons/react/24/solid";

interface BlogCardProps {
  blog: {
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    coverImage?: string | null;
    readTime?: number | null;
    viewCount?: number;
    publishedAt?: string | null;
    author: {
      id: string;
      name: string;
      image?: string | null;
      username?: string | null;
    };
    category?: {
      id: string;
      name: string;
      color: string;
    } | null;
    tags?: string[] | null;
    _count?: {
      likes: number;
      comments: number;
    };
    isLiked?: boolean;
    isBookmarked?: boolean;
    monetization?: {
      id: string;
      isEnabled: boolean;
      isApproved: boolean;
      status: 'pending' | 'approved' | 'rejected' | 'suspended';
      cprRate: string;
      totalEarnings: string;
      rejectionReason?: string;
    } | null;
  };
  variant?: "default" | "featured" | "compact";
  currentUserId?: string | null;
  onLike?: (blogId: string) => void;
  onBookmark?: (blogId: string) => void;
  onShare?: (blogId: string) => void;
  onEdit?: (blogId: string, slug: string) => void;
  onDelete?: (blogId: string, slug: string) => void;
}

export function BlogCard({
  blog,
  variant = "default",
  currentUserId,
  onLike,
  onBookmark,
  onShare,
  onEdit,
  onDelete
}: BlogCardProps) {
  const [isLiked, setIsLiked] = useState(blog.isLiked || false);
  const [isBookmarked, setIsBookmarked] = useState(blog.isBookmarked || false);
  const [likesCount, setLikesCount] = useState(blog._count?.likes || 0);
  const [commentsCount, setCommentsCount] = useState(blog._count?.comments || 0);
  const [viewCount, setViewCount] = useState(blog.viewCount || 0);

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsLiked(!isLiked);
    setLikesCount(prev => isLiked ? prev - 1 : prev + 1);
    onLike?.(blog.id);
  };

  const handleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsBookmarked(!isBookmarked);
    onBookmark?.(blog.id);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    onShare?.(blog.id);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    onEdit?.(blog.id, blog.slug);
  };

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    setIsDeleteModalOpen(false);
    onDelete?.(blog.id, blog.slug);
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  // Check if current user is the author
  const isAuthor = currentUserId && blog.author.id === currentUserId;

  // Enhanced monetization status display
  const getMonetizationBadge = () => {
    if (!blog.monetization) return null;

    return (
      <MonetizationStatusBadge
        status={blog.monetization.status}
        size="sm"
        className="shadow-sm"
      />
    );
  };

  // Get monetization earnings display
  const getMonetizationEarnings = () => {
    if (!isAuthor || !blog.monetization || blog.monetization.status !== 'approved') return null;

    const earnings = parseFloat(blog.monetization.totalEarnings || '0');
    if (earnings <= 0) return null;

    return (
      <div className="flex items-center text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full">
        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
        ${earnings.toFixed(2)}
      </div>
    );
  };

  if (variant === "featured") {
    return (
      <>
      <Link href={`/blogs/${blog.slug}`}>
        <article className="group relative overflow-hidden rounded-3xl bg-white shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 h-[400px] flex flex-col">
          {/* Cover Image */}
          <div className="relative h-52 overflow-hidden">
            {blog.coverImage ? (
              <Image
                src={blog.coverImage}
                alt={blog.title}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-600 via-purple-700 to-pink-700 flex items-center justify-center relative">
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                <div className="text-white text-opacity-40 relative z-10">
                  <svg className="w-20 h-20" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                </div>
                {/* Decorative elements */}
                <div className="absolute top-8 right-8 w-12 h-12 bg-white/10 rounded-full animate-pulse"></div>
                <div className="absolute bottom-12 left-12 w-6 h-6 bg-white/20 rounded-full"></div>
                <div className="absolute top-1/3 left-1/4 w-4 h-4 bg-white/15 rounded-full"></div>
              </div>
            )}

            {/* Category Badge */}
            {blog.category && (
              <div className="absolute top-6 left-6">
                <Badge
                  variant="primary"
                  className="bg-white/95 backdrop-blur-md text-gray-900 font-bold shadow-xl border border-white/30 px-4 py-2"
                  style={{ backgroundColor: `${blog.category.color}15`, borderColor: blog.category.color }}
                >
                  {blog.category.name}
                </Badge>
              </div>
            )}

            {/* Featured Badge */}
            <div className="absolute top-6 right-6 flex flex-col gap-2">
              <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg flex items-center">
                <FireIcon className="w-3 h-3 mr-1" />
                FEATURED
              </div>
              {/* Monetization Badge */}
              {getMonetizationBadge()}
            </div>

            {/* Reading time overlay */}
            {blog.readTime && (
              <div className="absolute bottom-6 right-6 bg-black/70 backdrop-blur-md text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center shadow-lg">
                <ClockIcon className="w-4 h-4 mr-2" />
                {blog.readTime} min read
              </div>
            )}

            {/* Gradient overlay for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            {/* Action Buttons */}
            <div className="absolute bottom-6 left-6 flex space-x-3 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
              {isAuthor && (
                <>
                  <button
                    onClick={handleEdit}
                    className="p-3 bg-white/95 backdrop-blur-md rounded-full hover:bg-white transition-all shadow-lg"
                    title="Edit post"
                  >
                    <PencilIcon className="w-5 h-5 text-blue-600" />
                  </button>
                  <button
                    onClick={handleDelete}
                    className="p-3 bg-white/95 backdrop-blur-md rounded-full hover:bg-white transition-all shadow-lg"
                    title="Delete post"
                  >
                    <TrashIcon className="w-5 h-5 text-red-600" />
                  </button>
                </>
              )}
              <button
                onClick={handleBookmark}
                className="p-3 bg-white/95 backdrop-blur-md rounded-full hover:bg-white transition-all shadow-lg"
                title={isBookmarked ? 'Remove bookmark' : 'Bookmark'}
              >
                {isBookmarked ? (
                  <BookmarkIconSolid className="w-5 h-5 text-yellow-600" />
                ) : (
                  <BookmarkIcon className="w-5 h-5 text-gray-600" />
                )}
              </button>
              <button
                onClick={handleShare}
                className="p-3 bg-white/95 backdrop-blur-md rounded-full hover:bg-white transition-all shadow-lg"
                title="Share post"
              >
                <ShareIcon className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 flex-1 flex flex-col">
            {/* Author Info */}
            <div className="flex items-center mb-4">
              <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex-shrink-0 ring-2 ring-gray-100">
                {blog.author.image ? (
                  <Image
                    src={blog.author.image}
                    alt={blog.author.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                    {blog.author.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-semibold text-gray-900">{blog.author.name}</p>
                <div className="flex items-center text-xs text-gray-500">
                  {blog.publishedAt ? (
                    <PostTime
                      date={blog.publishedAt}
                      autoUpdate={true}
                    />
                  ) : (
                    <span>Draft</span>
                  )}
                  {blog.viewCount && (
                    <>
                      <span className="mx-2">•</span>
                      <BlogViewCounter
                        initialCount={blog.viewCount}
                        blogSlug={blog.slug}
                        size="sm"
                        showIcon={true}
                      />
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Title */}
            <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors leading-tight">
              {blog.title}
            </h3>

            {/* Monetization Badge */}
            {getMonetizationBadge() && (
              <div className="mb-3">
                {getMonetizationBadge()}
              </div>
            )}

            {/* Monetization Earnings */}
            {getMonetizationEarnings() && (
              <div className="mb-3">
                {getMonetizationEarnings()}
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-auto">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleLike}
                  className="flex items-center space-x-2 text-sm text-gray-500 hover:text-red-500 transition-colors group/like"
                >
                  {isLiked ? (
                    <HeartIconSolid className="w-5 h-5 text-red-500" />
                  ) : (
                    <HeartIcon className="w-5 h-5 group-hover/like:scale-110 transition-transform" />
                  )}
                  <span className="font-medium">{likesCount.toLocaleString()}</span>
                </button>

                <BlogViewCounter
                  initialCount={viewCount}
                  blogSlug={blog.slug}
                  size="sm"
                  showIcon={true}
                  className="text-gray-500"
                />

                {commentsCount > 0 && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span>{commentsCount.toLocaleString()}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-1">
                {isAuthor && (
                  <>
                    <button
                      onClick={handleEdit}
                      className="p-2 hover:bg-blue-50 rounded-lg transition-colors group/edit"
                      title="Edit post"
                    >
                      <PencilIcon className="w-4 h-4 text-blue-600 group-hover/edit:scale-110 transition-transform" />
                    </button>
                    <button
                      onClick={handleDelete}
                      className="p-2 hover:bg-red-50 rounded-lg transition-colors group/delete"
                      title="Delete post"
                    >
                      <TrashIcon className="w-4 h-4 text-red-600 group-hover/delete:scale-110 transition-transform" />
                    </button>
                  </>
                )}
                <button
                  onClick={handleBookmark}
                  className="p-2 hover:bg-yellow-50 rounded-lg transition-colors group/bookmark"
                  title={isBookmarked ? 'Remove bookmark' : 'Bookmark'}
                >
                  {isBookmarked ? (
                    <BookmarkIconSolid className="w-4 h-4 text-yellow-600" />
                  ) : (
                    <BookmarkIcon className="w-4 h-4 text-gray-400 group-hover/bookmark:text-yellow-600 group-hover/bookmark:scale-110 transition-all" />
                  )}
                </button>
                <button
                  onClick={handleShare}
                  className="p-2 hover:bg-green-50 rounded-lg transition-colors group/share"
                  title="Share post"
                >
                  <ShareIcon className="w-4 h-4 text-gray-400 group-hover/share:text-green-600 group-hover/share:scale-110 transition-all" />
                </button>
              </div>
            </div>
          </div>
        </article>
      </Link>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Blog Post"
        message="Are you sure you want to delete this blog post? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
      />
      </>
    );
  }

  if (variant === "compact") {
    return (
      <>
      <Link href={`/blogs/${blog.slug}`}>
        <article className="group bg-white rounded-xl hover:bg-gray-50 transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:shadow-md h-[200px] flex flex-col p-5">
          {/* Header with image and category */}
          <div className="flex space-x-4 mb-3">
            {/* Cover Image */}
            <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0 shadow-sm">
              {blog.coverImage ? (
                <Image
                  src={blog.coverImage}
                  alt={blog.title}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center relative">
                  <div className="absolute inset-0 bg-black/5"></div>
                  <svg className="w-6 h-6 text-white text-opacity-60 relative z-10" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                </div>
              )}

              {/* Category indicator */}
              {blog.category && (
                <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white" style={{ backgroundColor: blog.category.color }}></div>
              )}
            </div>

            {/* Title and meta */}
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-bold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors mb-1 leading-tight">
                {blog.title}
              </h3>

              {/* Monetization Badge for compact */}
              {getMonetizationBadge() && (
                <div className="mb-1">
                  {getMonetizationBadge()}
                </div>
              )}

              {/* Monetization Earnings for compact */}
              {getMonetizationEarnings() && (
                <div className="mb-1">
                  {getMonetizationEarnings()}
                </div>
              )}

              <div className="flex items-center text-xs text-gray-500 space-x-2">
                <span className="font-medium truncate">{blog.author.name}</span>
                {blog.readTime && (
                  <>
                    <span>•</span>
                    <div className="flex items-center">
                      <ClockIcon className="w-3 h-3 mr-1" />
                      <span>{blog.readTime}m</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Content area */}
          <div className="flex-1 flex flex-col justify-between">
            <div className="text-xs text-gray-600 mb-3">
              {blog.publishedAt ? (
                <PostTime
                  date={blog.publishedAt}
                  autoUpdate={true}
                />
              ) : (
                <span>Draft</span>
              )}
            </div>

            {/* Footer stats */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-100">
              <div className="flex items-center space-x-3 text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <HeartIcon className="w-3 h-3" />
                  <span>{likesCount.toLocaleString()}</span>
                </div>
                <BlogViewCounter
                  initialCount={viewCount}
                  blogSlug={blog.slug}
                  size="sm"
                  showIcon={true}
                />
                {commentsCount > 0 && (
                  <div className="flex items-center space-x-1">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span>{commentsCount.toLocaleString()}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-1">
                <button
                  onClick={handleBookmark}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  {isBookmarked ? (
                    <BookmarkIconSolid className="w-3 h-3 text-yellow-600" />
                  ) : (
                    <BookmarkIcon className="w-3 h-3 text-gray-400" />
                  )}
                </button>
                <button
                  onClick={handleShare}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <ShareIcon className="w-3 h-3 text-gray-400" />
                </button>
              </div>
            </div>
          </div>
        </article>
      </Link>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Blog Post"
        message="Are you sure you want to delete this blog post? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
      />
      </>
    );
  }

  // Default variant
  return (
    <>
    <Link href={`/blogs/${blog.slug}`}>
      <article className="group bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-blue-200 transform hover:-translate-y-2 h-[400px] flex flex-col">
        {/* Cover Image */}
        <div className="relative h-52 overflow-hidden">
          {blog.coverImage ? (
            <Image
              src={blog.coverImage}
              alt={blog.title}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 flex items-center justify-center relative">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="text-white text-opacity-40 relative z-10">
                <svg className="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
              {/* Decorative elements */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-white/10 rounded-full"></div>
              <div className="absolute bottom-6 left-6 w-4 h-4 bg-white/20 rounded-full"></div>
            </div>
          )}

          {/* Category Badge */}
          {blog.category && (
            <div className="absolute top-4 left-4">
              <Badge
                variant="primary"
                size="sm"
                className="bg-white/95 backdrop-blur-md text-gray-900 font-semibold shadow-lg border border-white/20"
                style={{ backgroundColor: `${blog.category.color}20`, borderColor: blog.category.color }}
              >
                {blog.category.name}
              </Badge>
            </div>
          )}

          {/* Reading time overlay */}
          {blog.readTime && (
            <div className="absolute bottom-4 right-4 bg-black/60 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
              <ClockIcon className="w-3 h-3 mr-1" />
              {blog.readTime} min
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-6 flex-1 flex flex-col">
          {/* Author Info */}
          <div className="flex items-center mb-4">
            <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex-shrink-0 ring-2 ring-gray-100">
              {blog.author.image ? (
                <Image
                  src={blog.author.image}
                  alt={blog.author.name}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                  {blog.author.name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-semibold text-gray-900">{blog.author.name}</p>
              <div className="flex items-center text-xs text-gray-500">
                {blog.publishedAt ? (
                  <PostTime
                    date={blog.publishedAt}
                    autoUpdate={true}
                  />
                ) : (
                  <span>Draft</span>
                )}
                {blog.viewCount && (
                  <>
                    <span className="mx-2">•</span>
                    <BlogViewCounter
                      initialCount={blog.viewCount}
                      blogSlug={blog.slug}
                      size="sm"
                      showIcon={true}
                    />
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors leading-tight">
            {blog.title}
          </h3>

          {/* Monetization Earnings */}
          {getMonetizationEarnings() && (
            <div className="mb-3">
              {getMonetizationEarnings()}
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-auto">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleLike}
                className="flex items-center space-x-2 text-sm text-gray-500 hover:text-red-500 transition-colors group/like"
              >
                {isLiked ? (
                  <HeartIconSolid className="w-5 h-5 text-red-500" />
                ) : (
                  <HeartIcon className="w-5 h-5 group-hover/like:scale-110 transition-transform" />
                )}
                <span className="font-medium">{likesCount.toLocaleString()}</span>
              </button>

              <BlogViewCounter
                initialCount={viewCount}
                blogSlug={blog.slug}
                size="sm"
                showIcon={true}
                className="text-gray-500"
              />

              {commentsCount > 0 && (
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span>{commentsCount.toLocaleString()}</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-1">
              {isAuthor && (
                <>
                  <button
                    onClick={handleEdit}
                    className="p-2 hover:bg-blue-50 rounded-lg transition-colors group/edit"
                    title="Edit post"
                  >
                    <PencilIcon className="w-4 h-4 text-blue-600 group-hover/edit:scale-110 transition-transform" />
                  </button>
                  <button
                    onClick={handleDelete}
                    className="p-2 hover:bg-red-50 rounded-lg transition-colors group/delete"
                    title="Delete post"
                  >
                    <TrashIcon className="w-4 h-4 text-red-600 group-hover/delete:scale-110 transition-transform" />
                  </button>
                </>
              )}
              <button
                onClick={handleBookmark}
                className="p-2 hover:bg-yellow-50 rounded-lg transition-colors group/bookmark"
                title={isBookmarked ? 'Remove bookmark' : 'Bookmark'}
              >
                {isBookmarked ? (
                  <BookmarkIconSolid className="w-4 h-4 text-yellow-600" />
                ) : (
                  <BookmarkIcon className="w-4 h-4 text-gray-400 group-hover/bookmark:text-yellow-600 group-hover/bookmark:scale-110 transition-all" />
                )}
              </button>
              <button
                onClick={handleShare}
                className="p-2 hover:bg-green-50 rounded-lg transition-colors group/share"
                title="Share post"
              >
                <ShareIcon className="w-4 h-4 text-gray-400 group-hover/share:text-green-600 group-hover/share:scale-110 transition-all" />
              </button>
            </div>
          </div>
        </div>
      </article>
    </Link>

    {/* Delete Confirmation Modal */}
    <ConfirmationModal
      isOpen={isDeleteModalOpen}
      onClose={handleCancelDelete}
      onConfirm={handleConfirmDelete}
      title="Delete Blog Post"
      message="Are you sure you want to delete this blog post? This action cannot be undone."
      confirmText="Delete"
      cancelText="Cancel"
      type="danger"
    />
    </>
  );
}
