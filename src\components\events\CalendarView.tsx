"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";

interface Event {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  location: string | null;
  isOnline: boolean | null;
  visibility: string;
}

interface CalendarViewProps {
  initialDate?: Date;
}

export function CalendarView({ initialDate = new Date() }: CalendarViewProps) {
  const router = useRouter();
  const [events, setEvents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [calendarView, setCalendarView] = useState<"dayGridMonth" | "timeGridWeek" | "timeGridDay">("dayGridMonth");

  useEffect(() => {
    const fetchEvents = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Get start and end dates for a wider range (3 months)
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 2, 0, 23, 59, 59);

        // Build query string
        const queryParams = new URLSearchParams({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        });

        const response = await fetch(`/api/events?${queryParams.toString()}`);

        if (!response.ok) {
          throw new Error("Failed to fetch events");
        }

        const data = await response.json();

        // Transform events to FullCalendar format
        const formattedEvents = data.map((event: Event) => ({
          id: event.id,
          title: event.name,
          start: event.startTime,
          end: event.endTime,
          extendedProps: {
            location: event.location,
            isOnline: event.isOnline,
            visibility: event.visibility
          },
          backgroundColor: getEventColor(event.visibility),
          borderColor: getEventColor(event.visibility),
        }));

        setEvents(formattedEvents);
      } catch (err) {
        console.error("Error fetching events:", err);
        setError("Failed to load events. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Get color based on event visibility
  const getEventColor = (visibility: string) => {
    switch (visibility) {
      case "public":
        return "#3b82f6"; // blue-500
      case "friends":
        return "#8b5cf6"; // purple-500
      case "private":
        return "#ef4444"; // red-500
      default:
        return "#3b82f6"; // blue-500
    }
  };

  // Handle event click
  const handleEventClick = (info: any) => {
    router.push(`/events/${info.event.id}`);
  };

  // Handle date click
  const handleDateClick = (info: any) => {
    // Could be used to create an event on that date
    console.log("Date clicked:", info.dateStr);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-10">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-4 text-red-800">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="p-4 flex items-center justify-between border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">
          Calendar
        </h2>
        <div className="flex space-x-2">
          <Button
            variant={calendarView === "dayGridMonth" ? "primary" : "outline"}
            size="sm"
            onClick={() => setCalendarView("dayGridMonth")}
          >
            Month
          </Button>
          <Button
            variant={calendarView === "timeGridWeek" ? "primary" : "outline"}
            size="sm"
            onClick={() => setCalendarView("timeGridWeek")}
          >
            Week
          </Button>
          <Button
            variant={calendarView === "timeGridDay" ? "primary" : "outline"}
            size="sm"
            onClick={() => setCalendarView("timeGridDay")}
          >
            Day
          </Button>
        </div>
      </div>

      <div className="p-4">
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          initialView={calendarView}
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: ''
          }}
          events={events}
          eventClick={handleEventClick}
          dateClick={handleDateClick}
          height="auto"
          aspectRatio={1.5}
          initialDate={initialDate}
          eventTimeFormat={{
            hour: '2-digit',
            minute: '2-digit',
            meridiem: 'short'
          }}
          dayMaxEvents={3}
          moreLinkClick="day"
          eventDisplay="block"
        />
      </div>
    </div>
  );
}
