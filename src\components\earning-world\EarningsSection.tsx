import { Button } from "@/components/ui/Button";
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline";

interface EarningsSectionProps {
  className?: string;
}

export function EarningsSection({ className = "" }: EarningsSectionProps) {
  // Mock data for earnings
  const earningsSummary = {
    totalEarnings: 1250.75,
    pendingPayouts: 320.50,
    availableBalance: 930.25,
    currency: "USD",
  };

  const recentTransactions = [
    {
      id: "1",
      type: "credit",
      amount: 150.00,
      description: "Content creator bonus",
      date: "2023-12-20",
      status: "completed",
    },
    {
      id: "2",
      type: "credit",
      amount: 75.25,
      description: "Ad revenue share",
      date: "2023-12-15",
      status: "completed",
    },
    {
      id: "3",
      type: "debit",
      amount: 200.00,
      description: "Withdrawal to bank account",
      date: "2023-12-10",
      status: "completed",
    },
    {
      id: "4",
      type: "credit",
      amount: 320.50,
      description: "Marketplace sales",
      date: "2023-12-05",
      status: "pending",
    },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Section Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-xl font-semibold text-gray-900">My Earnings</h2>
        <p className="text-sm text-gray-600 mt-1">Track your earnings and manage your payouts.</p>
      </div>

      {/* Earnings Summary */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4">
          <dt className="text-sm font-medium text-gray-500 truncate">Total Earnings</dt>
          <dd className="mt-1 text-xl font-semibold text-gray-900">
            ${earningsSummary.totalEarnings.toFixed(2)}
          </dd>
        </div>
        
        <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4">
          <dt className="text-sm font-medium text-gray-500 truncate">Pending Payouts</dt>
          <dd className="mt-1 text-xl font-semibold text-gray-900">
            ${earningsSummary.pendingPayouts.toFixed(2)}
          </dd>
        </div>
        
        <div className="bg-white overflow-hidden rounded-lg border border-gray-200 p-4">
          <dt className="text-sm font-medium text-gray-500 truncate">Available Balance</dt>
          <dd className="mt-1 text-xl font-semibold text-gray-900">
            ${earningsSummary.availableBalance.toFixed(2)}
          </dd>
        </div>
      </div>
      
      {/* Payout Options */}
      <div className="border border-gray-200 rounded-lg p-4 bg-white">
        <h4 className="text-sm font-medium text-gray-900 mb-4">Payout Options</h4>
        
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-gray-700">Available for withdrawal: <span className="font-medium">${earningsSummary.availableBalance.toFixed(2)}</span></p>
          </div>
          <Button>
            Withdraw Funds
          </Button>
        </div>
      </div>
      
      {/* Recent Transactions */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-sm font-medium text-gray-900">Recent Transactions</h4>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentTransactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <div className="flex items-center">
                      {transaction.type === 'credit' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500 mr-2" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500 mr-2" />
                      )}
                      <span className="capitalize">{transaction.type}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {transaction.description}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                    {transaction.date}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <span className={`font-medium ${
                      transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.type === 'credit' ? '+' : '-'}
                      ${transaction.amount.toFixed(2)}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                      transaction.status === 'completed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {transaction.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="p-4 border-t border-gray-200 flex justify-center">
          <Button variant="outline" size="sm">
            View All Transactions
          </Button>
        </div>
      </div>
    </div>
  );
}
