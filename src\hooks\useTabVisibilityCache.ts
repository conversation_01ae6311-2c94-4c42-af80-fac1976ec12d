"use client";

import { useEffect, useRef, useCallback } from 'react';

interface CacheEntry {
  data: any;
  timestamp: number;
  lastFetch: number;
}

interface UseTabVisibilityCacheOptions {
  cacheKey: string;
  fetchFunction: () => Promise<any>;
  cacheDuration?: number; // in milliseconds, default 10 minutes
  dependencies?: any[];
  enabled?: boolean;
}

/**
 * Custom hook that implements tab visibility-aware caching
 * Prevents frequent refetching when switching tabs
 */
export function useTabVisibilityCache({
  cacheKey,
  fetchFunction,
  cacheDuration = 10 * 60 * 1000, // 10 minutes default
  dependencies = [],
  enabled = true
}: UseTabVisibilityCacheOptions) {
  const cacheRef = useRef<Map<string, CacheEntry>>(new Map());
  const lastVisibilityChangeRef = useRef<number>(Date.now());
  const isInitialLoadRef = useRef<boolean>(true);

  // Check if data is still valid in cache
  const isCacheValid = useCallback((entry: CacheEntry): boolean => {
    const now = Date.now();
    return (now - entry.timestamp) < cacheDuration;
  }, [cacheDuration]);

  // Get cached data if valid
  const getCachedData = useCallback((): any | null => {
    const entry = cacheRef.current.get(cacheKey);
    if (entry && isCacheValid(entry)) {
      return entry.data;
    }
    return null;
  }, [cacheKey, isCacheValid]);

  // Set data in cache
  const setCachedData = useCallback((data: any) => {
    const now = Date.now();
    cacheRef.current.set(cacheKey, {
      data,
      timestamp: now,
      lastFetch: now
    });
  }, [cacheKey]);

  // Check if we should fetch data
  const shouldFetch = useCallback((): boolean => {
    if (!enabled) return false;

    const entry = cacheRef.current.get(cacheKey);
    const now = Date.now();

    // Always fetch on initial load
    if (isInitialLoadRef.current) {
      return true;
    }

    // Don't fetch if we have valid cached data
    if (entry && isCacheValid(entry)) {
      return false;
    }

    // Don't fetch if tab was just switched (within 1 second)
    if ((now - lastVisibilityChangeRef.current) < 1000) {
      return false;
    }

    return true;
  }, [enabled, cacheKey, isCacheValid]);

  // Fetch data with caching logic
  const fetchWithCache = useCallback(async (): Promise<any> => {
    if (!shouldFetch()) {
      return getCachedData();
    }

    try {
      const data = await fetchFunction();
      setCachedData(data);
      isInitialLoadRef.current = false;
      return data;
    } catch (error) {
      // Return cached data if fetch fails and we have some
      const cachedData = getCachedData();
      if (cachedData) {
        return cachedData;
      }
      throw error;
    }
  }, [shouldFetch, getCachedData, fetchFunction, setCachedData]);

  // Handle visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      lastVisibilityChangeRef.current = Date.now();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);
    window.addEventListener('blur', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
      window.removeEventListener('blur', handleVisibilityChange);
    };
  }, []);

  // Clear cache when dependencies change
  useEffect(() => {
    if (!isInitialLoadRef.current) {
      cacheRef.current.delete(cacheKey);
    }
  }, dependencies);

  // Clear cache on unmount
  useEffect(() => {
    return () => {
      cacheRef.current.delete(cacheKey);
    };
  }, [cacheKey]);

  return {
    fetchWithCache,
    getCachedData,
    clearCache: () => cacheRef.current.delete(cacheKey),
    isCacheValid: () => {
      const entry = cacheRef.current.get(cacheKey);
      return entry ? isCacheValid(entry) : false;
    }
  };
}
