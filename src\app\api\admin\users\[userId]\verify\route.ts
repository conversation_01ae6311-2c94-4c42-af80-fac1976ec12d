import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, notifications } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const verifySchema = z.object({
  isVerified: z.boolean(),
});

interface RouteParams {
  params: Promise<{
    userId: string;
  }>;
}

// PATCH /api/admin/users/[userId]/verify - Update user verification status
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const currentUser = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (currentUser.length === 0 || !currentUser[0].isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = verifySchema.parse(body);

    // Check if target user exists
    const targetUser = await db
      .select({
        id: users.id,
        name: users.name,
        isVerified: users.isVerified,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (targetUser.length === 0) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    const user = targetUser[0];

    // Update verification status
    await db
      .update(users)
      .set({
        isVerified: validatedData.isVerified,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Create notification for user
    const notificationId = uuidv4();
    await db.insert(notifications).values({
      id: notificationId,
      recipientId: userId,
      type: "user_verified", // We'll need to add this type
      title: validatedData.isVerified ? "Account Verified" : "Verification Removed",
      message: validatedData.isVerified 
        ? "Your account has been verified! You now have a verified badge."
        : "Your account verification has been removed.",
    });

    return NextResponse.json({
      message: `User ${validatedData.isVerified ? "verified" : "unverified"} successfully`,
      isVerified: validatedData.isVerified,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating user verification status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
