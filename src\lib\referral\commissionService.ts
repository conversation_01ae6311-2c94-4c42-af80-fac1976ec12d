import { db } from "@/lib/db";
import {
  referrals,
  referralCommissions,
  referralCommissionSettings,
  packageCommissionSettings,
  subscriptionTransactions,
  userSubscriptions,
  wallets,
  walletTransactions
} from "@/lib/db/schema";
import { eq, and, sql, desc } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export interface CommissionCalculationResult {
  eligible: boolean;
  commissionAmount: number;
  commissionType: 'fixed' | 'percentage';
  commissionRate: number;
  isFirstPurchase: boolean;
  reason?: string;
}

export interface CommissionProcessingResult {
  success: boolean;
  commissionId?: string;
  message: string;
}

export class CommissionService {
  /**
   * Calculate commission for a subscription purchase
   */
  static async calculateCommission(
    subscriptionTransactionId: string,
    subscriptionAmount: number,
    planId: string,
    planName: string,
    userId: string
  ): Promise<CommissionCalculationResult> {
    try {
      // Get global commission settings
      const globalSettings = await db.query.referralCommissionSettings.findFirst({
        where: eq(referralCommissionSettings.id, 'default'),
      });

      if (!globalSettings || !globalSettings.isEnabled) {
        return {
          eligible: false,
          commissionAmount: 0,
          commissionType: 'percentage',
          commissionRate: 0,
          isFirstPurchase: false,
          reason: 'Commission system is disabled'
        };
      }

      // Check if user was referred
      const referral = await db.query.referrals.findFirst({
        where: eq(referrals.referredUserId, userId),
      });

      if (!referral) {
        return {
          eligible: false,
          commissionAmount: 0,
          commissionType: 'percentage',
          commissionRate: 0,
          isFirstPurchase: false,
          reason: 'User was not referred'
        };
      }

      // Check referral status
      if (referral.status !== 'completed') {
        return {
          eligible: false,
          commissionAmount: 0,
          commissionType: 'percentage',
          commissionRate: 0,
          isFirstPurchase: false,
          reason: 'Referral is not completed'
        };
      }

      // Check eligibility period
      const referralDate = new Date(referral.createdAt);
      const currentDate = new Date();
      const daysDifference = Math.floor((currentDate.getTime() - referralDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDifference > globalSettings.eligibilityPeriodDays) {
        return {
          eligible: false,
          commissionAmount: 0,
          commissionType: 'percentage',
          commissionRate: 0,
          isFirstPurchase: false,
          reason: `Eligibility period expired (${globalSettings.eligibilityPeriodDays} days)`
        };
      }

      // Check minimum subscription amount
      if (subscriptionAmount < parseFloat(globalSettings.minSubscriptionAmount)) {
        return {
          eligible: false,
          commissionAmount: 0,
          commissionType: 'percentage',
          commissionRate: 0,
          isFirstPurchase: false,
          reason: `Subscription amount below minimum (${globalSettings.minSubscriptionAmount})`
        };
      }

      // Check if this is first purchase
      const previousPurchases = await db.query.subscriptionTransactions.findMany({
        where: and(
          eq(subscriptionTransactions.userId, userId),
          eq(subscriptionTransactions.status, 'completed'),
          eq(subscriptionTransactions.type, 'payment')
        ),
        orderBy: [desc(subscriptionTransactions.createdAt)],
      });

      const isFirstPurchase = previousPurchases.length <= 1; // Current purchase would be included

      // Check if first purchase commissions are enabled
      if (isFirstPurchase && !globalSettings.firstPurchaseEnabled) {
        return {
          eligible: false,
          commissionAmount: 0,
          commissionType: 'percentage',
          commissionRate: 0,
          isFirstPurchase: true,
          reason: 'First purchase commissions are disabled'
        };
      }

      // Check if recurring purchase commissions are enabled
      if (!isFirstPurchase && !globalSettings.recurringPurchaseEnabled) {
        return {
          eligible: false,
          commissionAmount: 0,
          commissionType: 'percentage',
          commissionRate: 0,
          isFirstPurchase: false,
          reason: 'Recurring purchase commissions are disabled'
        };
      }

      // Check max commissions per referral limit
      if (globalSettings.maxCommissionsPerReferral > 0) {
        const existingCommissions = await db.query.referralCommissions.findMany({
          where: eq(referralCommissions.referralId, referral.id),
        });

        if (existingCommissions.length >= globalSettings.maxCommissionsPerReferral) {
          return {
            eligible: false,
            commissionAmount: 0,
            commissionType: 'percentage',
            commissionRate: 0,
            isFirstPurchase,
            reason: `Maximum commissions per referral reached (${globalSettings.maxCommissionsPerReferral})`
          };
        }
      }

      // Get package-specific settings if available
      const packageSettings = await db.query.packageCommissionSettings.findFirst({
        where: eq(packageCommissionSettings.planId, planId),
      });

      let commissionType: 'fixed' | 'percentage';
      let commissionRate: number;

      if (packageSettings && packageSettings.isEnabled) {
        // Use package-specific settings
        commissionType = packageSettings.commissionType;
        commissionRate = isFirstPurchase 
          ? parseFloat(packageSettings.firstPurchaseCommission)
          : parseFloat(packageSettings.recurringCommission);
      } else {
        // Use global settings
        commissionType = globalSettings.defaultCommissionType;
        commissionRate = isFirstPurchase 
          ? parseFloat(globalSettings.defaultCommissionValue)
          : parseFloat(globalSettings.defaultRecurringCommissionValue);
      }

      // Calculate commission amount
      let commissionAmount: number;
      if (commissionType === 'percentage') {
        commissionAmount = (subscriptionAmount * commissionRate) / 100;
      } else {
        commissionAmount = commissionRate;
      }

      // Apply maximum commission limit
      const maxCommissionAmount = packageSettings?.maxCommissionAmount 
        ? parseFloat(packageSettings.maxCommissionAmount)
        : parseFloat(globalSettings.maxCommissionAmount);

      if (commissionAmount > maxCommissionAmount) {
        commissionAmount = maxCommissionAmount;
      }

      return {
        eligible: true,
        commissionAmount,
        commissionType,
        commissionRate,
        isFirstPurchase,
      };

    } catch (error) {
      console.error('Error calculating commission:', error);
      return {
        eligible: false,
        commissionAmount: 0,
        commissionType: 'percentage',
        commissionRate: 0,
        isFirstPurchase: false,
        reason: 'Error calculating commission'
      };
    }
  }

  /**
   * Process commission payment
   */
  static async processCommission(
    subscriptionTransactionId: string,
    subscriptionAmount: number,
    planId: string,
    planName: string,
    userId: string,
    subscriptionId: string
  ): Promise<CommissionProcessingResult> {
    try {
      // Calculate commission
      const calculation = await this.calculateCommission(
        subscriptionTransactionId,
        subscriptionAmount,
        planId,
        planName,
        userId
      );

      if (!calculation.eligible) {
        return {
          success: false,
          message: calculation.reason || 'Commission not eligible'
        };
      }

      // Get referral information
      const referral = await db.query.referrals.findFirst({
        where: eq(referrals.referredUserId, userId),
      });

      if (!referral) {
        return {
          success: false,
          message: 'Referral not found'
        };
      }

      // Check if commission already processed for this transaction
      const existingCommission = await db.query.referralCommissions.findFirst({
        where: eq(referralCommissions.subscriptionTransactionId, subscriptionTransactionId),
      });

      if (existingCommission) {
        return {
          success: false,
          message: 'Commission already processed for this transaction'
        };
      }

      // Create commission record and add to wallet in a transaction
      const commissionId = uuidv4();

      await db.transaction(async (tx) => {
        // Create commission record as paid (since we're adding to wallet immediately)
        await tx.insert(referralCommissions).values({
          id: commissionId,
          referralId: referral.id,
          referrerId: referral.referrerId,
          referredUserId: userId, // The user who made the purchase (referred user)
          subscriptionId,
          subscriptionTransactionId,
          planId,
          planName,
          subscriptionAmount: subscriptionAmount.toString(),
          commissionType: calculation.commissionType,
          commissionRate: calculation.commissionRate.toString(),
          commissionAmount: calculation.commissionAmount.toString(),
          isFirstPurchase: calculation.isFirstPurchase,
          status: 'paid', // Set as paid since we're adding to wallet immediately
          approvedAt: new Date(),
          paidAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Add commission to referrer's earning wallet within the same transaction
        await this.addCommissionToWalletInTransaction(
          tx,
          referral.referrerId,
          calculation.commissionAmount,
          commissionId,
          planName
        );
      });

      // Mark subscription transaction as commission processed
      await db
        .update(subscriptionTransactions)
        .set({
          commissionProcessed: true,
          updatedAt: new Date(),
        })
        .where(eq(subscriptionTransactions.id, subscriptionTransactionId));

      return {
        success: true,
        commissionId,
        message: `Commission of $${calculation.commissionAmount.toFixed(2)} processed successfully`
      };

    } catch (error) {
      console.error('Error processing commission:', error);
      return {
        success: false,
        message: 'Error processing commission'
      };
    }
  }

  /**
   * Add commission to referrer's earning wallet
   */
  private static async addCommissionToWallet(
    referrerId: string,
    commissionAmount: number,
    commissionId: string,
    planName: string
  ): Promise<void> {
    // Get or create earning wallet
    let wallet = await db.query.wallets.findFirst({
      where: and(
        eq(wallets.userId, referrerId),
        eq(wallets.type, 'earning')
      ),
    });

    if (!wallet) {
      // Create earning wallet
      const walletId = uuidv4();
      await db.insert(wallets).values({
        id: walletId,
        userId: referrerId,
        type: 'earning',
        balance: '0.00',
        earningBalance: '0.00',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      wallet = await db.query.wallets.findFirst({
        where: and(
          eq(wallets.userId, referrerId),
          eq(wallets.type, 'earning')
        ),
      });
    }

    if (!wallet) {
      throw new Error('Failed to create or find earning wallet');
    }

    // Update earning wallet balance
    await db
      .update(wallets)
      .set({
        earningBalance: sql`${wallets.earningBalance} + ${commissionAmount}`,
        updatedAt: new Date(),
      })
      .where(eq(wallets.id, wallet.id));

    // Create transaction record
    await db.insert(walletTransactions).values({
      id: uuidv4(),
      userId: referrerId,
      type: 'earning',
      amount: commissionAmount.toString(),
      fee: '0.00',
      netAmount: commissionAmount.toString(),
      walletType: 'earning',
      description: `Referral commission for ${planName} subscription`,
      status: 'completed',
      metadata: {
        commissionId,
        planName,
        type: 'referral_commission',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  /**
   * Add commission to referrer's earning wallet within a transaction
   */
  private static async addCommissionToWalletInTransaction(
    tx: any,
    referrerId: string,
    commissionAmount: number,
    commissionId: string,
    planName: string
  ): Promise<void> {
    // Get or create earning wallet
    let wallet = await db.query.wallets.findFirst({
      where: and(
        eq(wallets.userId, referrerId),
        eq(wallets.type, 'earning')
      ),
    });

    if (!wallet) {
      // Create earning wallet
      const walletId = uuidv4();
      await tx.insert(wallets).values({
        id: walletId,
        userId: referrerId,
        type: 'earning',
        balance: '0.00',
        earningBalance: '0.00',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      wallet = await db.query.wallets.findFirst({
        where: and(
          eq(wallets.userId, referrerId),
          eq(wallets.type, 'earning')
        ),
      });
    }

    if (!wallet) {
      throw new Error('Failed to create or find earning wallet');
    }

    // Update earning wallet balance within transaction
    await tx
      .update(wallets)
      .set({
        earningBalance: sql`${wallets.earningBalance} + ${commissionAmount}`,
        updatedAt: new Date(),
      })
      .where(eq(wallets.id, wallet.id));

    // Create transaction record within transaction
    await tx.insert(walletTransactions).values({
      id: uuidv4(),
      userId: referrerId,
      type: 'earning',
      amount: commissionAmount.toString(),
      fee: '0.00',
      netAmount: commissionAmount.toString(),
      walletType: 'earning',
      description: `Referral commission for ${planName} subscription`,
      status: 'completed',
      metadata: {
        commissionId,
        planName,
        type: 'referral_commission',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
}
