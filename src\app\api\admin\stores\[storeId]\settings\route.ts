import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, storeSettings } from "@/lib/db/schema";
import { z } from "zod";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

const storeSettingsSchema = z.object({
  visibility: z.enum(["public", "private"]),
  showOutOfStock: z.boolean(),
  showProductViews: z.boolean(),
  emailNotifications: z.boolean(),
  productViewNotifications: z.boolean(),
});

// Get store settings
export async function GET(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;

    // Check if store exists
    const storeExists = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (storeExists.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Get store settings
    const settings = await db
      .select()
      .from(storeSettings)
      .where(eq(storeSettings.storeId, storeId))
      .limit(1);

    if (settings.length === 0) {
      // Create default settings if none exist
      const defaultSettings = {
        id: uuidv4(),
        storeId,
        visibility: "public",
        showOutOfStock: true,
        showProductViews: true,
        emailNotifications: true,
        productViewNotifications: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await db.insert(storeSettings).values(defaultSettings);

      return NextResponse.json({
        settings: defaultSettings,
      });
    }

    return NextResponse.json({
      settings: settings[0],
    });
  } catch (error) {
    console.error("Error fetching store settings:", error);
    return NextResponse.json(
      { message: "Error fetching store settings" },
      { status: 500 }
    );
  }
}

// Update store settings
export async function POST(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { storeId } = params;

    // Check if store exists
    const storeExists = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (storeExists.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const validatedData = storeSettingsSchema.parse(body);

    // Check if settings exist
    const existingSettings = await db
      .select({ id: storeSettings.id })
      .from(storeSettings)
      .where(eq(storeSettings.storeId, storeId))
      .limit(1);

    if (existingSettings.length === 0) {
      // Create new settings
      const newSettings = {
        id: uuidv4(),
        storeId,
        ...validatedData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await db.insert(storeSettings).values(newSettings);

      return NextResponse.json({
        message: "Store settings created successfully",
        settings: newSettings,
      });
    } else {
      // Update existing settings
      await db
        .update(storeSettings)
        .set({
          ...validatedData,
          updatedAt: new Date(),
        })
        .where(eq(storeSettings.id, existingSettings[0].id));

      // Get updated settings
      const updatedSettings = await db
        .select()
        .from(storeSettings)
        .where(eq(storeSettings.id, existingSettings[0].id))
        .limit(1);

      return NextResponse.json({
        message: "Store settings updated successfully",
        settings: updatedSettings[0],
      });
    }
  } catch (error) {
    console.error("Error updating store settings:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Error updating store settings" },
      { status: 500 }
    );
  }
}
