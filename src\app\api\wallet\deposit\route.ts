import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";
import { PaymentService } from "@/lib/payment/paymentService";
import { depositSchema } from "@/lib/wallet/validation";

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = depositSchema.parse(body);

    // Check if deposits are enabled
    const depositEnabled = await WalletService.getWalletSetting('deposit_enabled');
    if (depositEnabled === 'false') {
      return NextResponse.json(
        {
          success: false,
          message: "Deposits are currently disabled"
        },
        { status: 400 }
      );
    }

    // Get gateway by name to get the ID
    const gateways = await WalletService.getAllPaymentGateways();
    const gateway = gateways.find(g => g.name === validatedData.paymentGateway && g.isActive);

    if (!gateway) {
      return NextResponse.json(
        {
          success: false,
          message: "Payment gateway not available"
        },
        { status: 400 }
      );
    }

    // Process deposit
    const transactionId = await WalletService.processDeposit(session.user.id, {
      amount: validatedData.amount,
      paymentGateway: validatedData.paymentGateway,
      metadata: validatedData.metadata,
    });

    // Process payment through payment service
    const paymentResult = await PaymentService.processPayment({
      amount: validatedData.amount,
      currency: gateway.currency,
      gatewayId: gateway.id,
      userId: session.user.id,
      metadata: {
        transactionId,
        ...validatedData.metadata
      }
    });

    if (!paymentResult.success) {
      // Update transaction status to failed
      await WalletService.updateTransactionStatus(transactionId, 'failed');

      return NextResponse.json(
        {
          success: false,
          message: paymentResult.error || "Payment processing failed"
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        transactionId,
        paymentUrl: paymentResult.paymentUrl,
        gatewayTransactionId: paymentResult.transactionId,
        message: paymentResult.message || "Deposit initiated successfully",
      },
    });
  } catch (error: any) {
    console.error("Error processing deposit:", error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid input data",
          errors: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: error.message || "Failed to process deposit"
      },
      { status: 500 }
    );
  }
}

// Complete deposit (webhook endpoint)
export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const { transactionId, gatewayTransactionId, status } = body;

    if (!transactionId) {
      return NextResponse.json(
        { message: "Transaction ID is required" },
        { status: 400 }
      );
    }

    if (status === 'completed') {
      await WalletService.completeDeposit(transactionId);

      if (gatewayTransactionId) {
        await WalletService.updateTransactionStatus(
          transactionId,
          'completed',
          gatewayTransactionId
        );
      }
    } else if (status === 'failed') {
      await WalletService.updateTransactionStatus(transactionId, 'failed');
    }

    return NextResponse.json({
      success: true,
      message: "Deposit status updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating deposit status:", error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || "Failed to update deposit status"
      },
      { status: 500 }
    );
  }
}
