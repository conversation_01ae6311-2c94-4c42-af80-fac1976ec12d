"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { 
  UserIcon, 
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  UserMinusIcon
} from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";
import { formatDistanceToNow } from "date-fns";
import { unsubscribeFromUser } from "@/app/actions/subscription";

interface Subscription {
  id: string;
  targetUserId: string;
  name: string;
  username: string;
  image: string | null;
  createdAt: string;
}

interface SubscriptionsResponse {
  subscriptions: Subscription[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

interface SubscriptionsListProps {
  searchQuery?: string;
}

export function SubscriptionsList({ searchQuery = "" }: SubscriptionsListProps) {
  const { data: session } = useSession();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [pagination, setPagination] = useState({
    page: 1,
    hasMore: true
  });
  const [unsubscribingUsers, setUnsubscribingUsers] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (session?.user) {
      fetchSubscriptions();
    }
  }, [session]);

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
    if (searchQuery !== localSearchQuery) {
      fetchSubscriptions(1, false);
    }
  }, [searchQuery]);

  const fetchSubscriptions = async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const params = new URLSearchParams({
        type: 'subscriptions',
        limit: '20',
        offset: ((page - 1) * 20).toString(),
        ...(localSearchQuery && { search: localSearchQuery })
      });

      const response = await fetch(`/api/subscriptions?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscriptions');
      }

      const data: SubscriptionsResponse = await response.json();
      
      if (append) {
        setSubscriptions(prev => [...prev, ...data.subscriptions]);
      } else {
        setSubscriptions(data.subscriptions);
      }
      
      setPagination({
        page: data.pagination.page,
        hasMore: data.pagination.hasMore
      });
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      toast.error('Failed to load subscriptions');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreSubscriptions = () => {
    if (pagination.hasMore && !loadingMore) {
      fetchSubscriptions(pagination.page + 1, true);
    }
  };

  const handleUnsubscribe = async (targetUserId: string, name: string) => {
    if (!session?.user) return;

    setUnsubscribingUsers(prev => new Set(prev).add(targetUserId));

    try {
      const result = await unsubscribeFromUser(targetUserId);

      if (result.success) {
        // Remove from local state
        setSubscriptions(prev => prev.filter(sub => sub.targetUserId !== targetUserId));
        toast.success(`Unsubscribed from ${name}`);
      } else {
        toast.error(result.message || 'Failed to unsubscribe');
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
      toast.error('Failed to unsubscribe');
    } finally {
      setUnsubscribingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(targetUserId);
        return newSet;
      });
    }
  };

  const handleStartChat = (subscription: Subscription) => {
    // This will be implemented when chat functionality is added
    toast.success(`Starting chat with ${subscription.name}`);
  };

  // Filter subscriptions based on search query
  const filteredSubscriptions = subscriptions.filter(subscription =>
    subscription.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
    subscription.username.toLowerCase().includes(localSearchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg animate-pulse">
            <div className="h-16 w-16 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
            <div className="flex space-x-2">
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (filteredSubscriptions.length === 0) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {localSearchQuery ? 'No subscriptions found' : 'No subscriptions yet'}
        </h3>
        <p className="text-gray-600 mb-4">
          {localSearchQuery 
            ? `No subscriptions match "${localSearchQuery}"`
            : "Start subscribing to people to see them here."
          }
        </p>
        {!localSearchQuery && (
          <Link
            href="/subscriptions?tab=suggestions"
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <UserIcon className="h-4 w-4 mr-2" />
            Discover People
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {filteredSubscriptions.map((subscription) => (
        <div key={subscription.id} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors duration-200">
          <div className="relative h-16 w-16 flex-shrink-0">
            {subscription.image ? (
              <OptimizedImage
                src={subscription.image}
                alt={subscription.name}
                width={64}
                height={64}
                className="rounded-full object-cover"
              />
            ) : (
              <div className="h-16 w-16 bg-gray-200 rounded-full flex items-center justify-center">
                <UserIcon className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div>
                <Link 
                  href={`/user/${subscription.username}`}
                  className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200"
                >
                  {subscription.name}
                </Link>
                <p className="text-sm text-gray-500">@{subscription.username}</p>
                <p className="text-xs text-gray-400 mt-1">
                  Subscribed {formatDistanceToNow(new Date(subscription.createdAt), { addSuffix: true })}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2 mt-3">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleStartChat(subscription)}
                className="flex-1 flex items-center justify-center space-x-1"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
                <span>Message</span>
              </Button>
              <Link
                href={`/user/${subscription.username}`}
                className="flex-1"
              >
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full flex items-center justify-center space-x-1"
                >
                  <UserIcon className="h-4 w-4" />
                  <span>Profile</span>
                </Button>
              </Link>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleUnsubscribe(subscription.targetUserId, subscription.name)}
                disabled={unsubscribingUsers.has(subscription.targetUserId)}
                className="flex items-center justify-center space-x-1 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <UserMinusIcon className="h-4 w-4" />
                <span>{unsubscribingUsers.has(subscription.targetUserId) ? 'Unsubscribing...' : 'Unsubscribe'}</span>
              </Button>
            </div>
          </div>
        </div>
      ))}

      {/* Load More Button */}
      {pagination.hasMore && (
        <div className="text-center pt-4">
          <Button
            variant="outline"
            onClick={loadMoreSubscriptions}
            disabled={loadingMore}
            className="px-8"
          >
            {loadingMore ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
}
