-- Enhanced Blog View Tracking Migration
-- This migration adds enhanced view tracking capabilities

-- Create blog_views table for detailed view analytics
CREATE TABLE IF NOT EXISTS `blog_views` (
  `id` varchar(255) NOT NULL PRIMARY KEY,
  `blogId` varchar(255) NOT NULL,
  `userId` varchar(255) NULL,
  `ipAddress` varchar(45) NOT NULL,
  `userAgent` text NULL,
  `sessionId` varchar(255) NOT NULL,
  `fingerprint` varchar(255) NULL,
  `referrer` varchar(500) NULL,
  `country` varchar(100) NULL,
  `city` varchar(100) NULL,
  `device` varchar(50) NULL,
  `browser` varchar(50) NULL,
  `os` varchar(50) NULL,
  `isBot` boolean DEFAULT false,
  `isUnique` boolean DEFAULT true,
  `viewDuration` int DEFAULT 0,
  `scrollDepth` int DEFAULT 0,
  `createdAt` timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX `idx_blog_views_blogId` (`blogId`),
  INDEX `idx_blog_views_userId` (`userId`),
  INDEX `idx_blog_views_sessionId` (`sessionId`),
  INDEX `idx_blog_views_createdAt` (`createdAt`),
  INDEX `idx_blog_views_isBot` (`isBot`),
  INDEX `idx_blog_views_isUnique` (`isUnique`),
  INDEX `idx_blog_views_country` (`country`),
  INDEX `idx_blog_views_device` (`device`)
);

-- Update existing blog_reads table with enhanced fields
ALTER TABLE `blog_reads` 
ADD COLUMN IF NOT EXISTS `scrollDepth` int DEFAULT 0,
ADD COLUMN IF NOT EXISTS `engagementScore` decimal(5,2) DEFAULT 0.00;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_blog_reads_scrollDepth` ON `blog_reads` (`scrollDepth`);
CREATE INDEX IF NOT EXISTS `idx_blog_reads_engagementScore` ON `blog_reads` (`engagementScore`);

-- Add foreign key constraints if they don't exist
-- Note: These will only work if the referenced tables exist
-- ALTER TABLE `blog_views` ADD CONSTRAINT `fk_blog_views_blogId` FOREIGN KEY (`blogId`) REFERENCES `blogs` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `blog_views` ADD CONSTRAINT `fk_blog_views_userId` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- Create a view for quick analytics queries
CREATE OR REPLACE VIEW `blog_analytics_summary` AS
SELECT 
  b.id as blogId,
  b.title,
  b.slug,
  b.viewCount as totalViews,
  COUNT(DISTINCT bv.id) as trackedViews,
  COUNT(DISTINCT CASE WHEN bv.isUnique = 1 THEN bv.id END) as uniqueViews,
  COUNT(DISTINCT CASE WHEN bv.createdAt >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN bv.id END) as todayViews,
  COUNT(DISTINCT CASE WHEN bv.createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN bv.id END) as weeklyViews,
  COUNT(DISTINCT CASE WHEN bv.createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN bv.id END) as monthlyViews,
  AVG(CASE WHEN bv.viewDuration > 0 THEN bv.viewDuration END) as avgViewDuration,
  AVG(CASE WHEN bv.scrollDepth > 0 THEN bv.scrollDepth END) as avgScrollDepth,
  COUNT(DISTINCT bv.country) as countryCount,
  COUNT(DISTINCT bv.device) as deviceCount
FROM blogs b
LEFT JOIN blog_views bv ON b.id = bv.blogId AND bv.isBot = 0
WHERE b.status = 'published'
GROUP BY b.id, b.title, b.slug, b.viewCount;

-- Create indexes for the view
CREATE INDEX IF NOT EXISTS `idx_blogs_status` ON `blogs` (`status`);
CREATE INDEX IF NOT EXISTS `idx_blog_views_composite` ON `blog_views` (`blogId`, `isBot`, `isUnique`, `createdAt`);
