import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { products, stores, users } from "@/lib/db/schema";
import { z } from "zod";
import { desc, asc, eq, and, or, like, count, gte, lte, inArray } from "drizzle-orm";

const querySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(10),
  search: z.string().optional().nullable(),
  condition: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
  priceMin: z.coerce.number().optional().nullable(),
  priceMax: z.coerce.number().optional().nullable(),
  dateFrom: z.string().optional().nullable(),
  dateTo: z.string().optional().nullable(),
  sort: z.string().optional().default("createdAt"),
  order: z.string().optional().default("desc"),
  storeId: z.string().optional().nullable(),
});

// Get all products with admin filtering capabilities
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const validatedQuery = querySchema.parse({
      page: searchParams.get("page") ? parseInt(searchParams.get("page") || "1") : 1,
      limit: searchParams.get("limit") ? parseInt(searchParams.get("limit") || "10") : 10,
      search: searchParams.get("search"),
      condition: searchParams.get("condition"),
      category: searchParams.get("category"),
      priceMin: searchParams.get("priceMin") ? parseInt(searchParams.get("priceMin") || "0") : null,
      priceMax: searchParams.get("priceMax") ? parseInt(searchParams.get("priceMax") || "0") : null,
      dateFrom: searchParams.get("dateFrom"),
      dateTo: searchParams.get("dateTo"),
      sort: searchParams.get("sort") || "createdAt",
      order: searchParams.get("order") || "desc",
      storeId: searchParams.get("storeId"),
    });

    const offset = (validatedQuery.page - 1) * validatedQuery.limit;

    // Build where conditions
    let whereConditions: any[] = [];

    if (validatedQuery.search) {
      whereConditions.push(
        or(
          like(products.title, `%${validatedQuery.search}%`),
          like(products.description || "", `%${validatedQuery.search}%`),
          like(stores.name, `%${validatedQuery.search}%`)
        )
      );
    }

    if (validatedQuery.condition && validatedQuery.condition !== "all") {
      whereConditions.push(eq(products.item_condition, validatedQuery.condition as any));
    }

    if (validatedQuery.category && validatedQuery.category !== "all") {
      whereConditions.push(eq(products.category, validatedQuery.category));
    }

    if (validatedQuery.priceMin !== null && validatedQuery.priceMin !== undefined) {
      whereConditions.push(gte(products.price, validatedQuery.priceMin));
    }

    if (validatedQuery.priceMax !== null && validatedQuery.priceMax !== undefined) {
      whereConditions.push(lte(products.price, validatedQuery.priceMax));
    }

    if (validatedQuery.storeId) {
      whereConditions.push(eq(products.storeId, validatedQuery.storeId));
    }

    if (validatedQuery.dateFrom) {
      const dateFrom = new Date(validatedQuery.dateFrom);
      whereConditions.push(gte(products.createdAt, dateFrom));
    }

    if (validatedQuery.dateTo) {
      const dateTo = new Date(validatedQuery.dateTo);
      dateTo.setHours(23, 59, 59, 999); // End of the day
      whereConditions.push(lte(products.createdAt, dateTo));
    }

    const whereClause = whereConditions.length > 0
      ? (whereConditions.length === 1 ? whereConditions[0] : and(...whereConditions))
      : undefined;

    // Determine sort order
    const sortOrder = validatedQuery.order === "asc" ? asc : desc;
    let orderBy;

    // Handle sorting by different fields
    switch (validatedQuery.sort) {
      case "title":
        orderBy = sortOrder(products.title);
        break;
      case "price":
        orderBy = sortOrder(products.price);
        break;
      case "condition":
        orderBy = sortOrder(products.item_condition);
        break;
      case "category":
        orderBy = sortOrder(products.category);
        break;
      case "store.name":
        orderBy = sortOrder(stores.name);
        break;
      case "viewCount":
        orderBy = sortOrder(products.viewCount);
        break;
      case "createdAt":
      default:
        orderBy = sortOrder(products.createdAt);
        break;
    }

    // Get products with pagination
    const allProducts = await db
      .select({
        id: products.id,
        title: products.title,
        description: products.description,
        price: products.price,
        condition: products.item_condition,
        category: products.category,
        location: products.location,
        photos: products.photos,
        viewCount: products.viewCount,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
        store: {
          id: stores.id,
          name: stores.name,
          slug: stores.slug,
          logo: stores.logo,
          isVerified: stores.isVerified,
          ownerId: stores.ownerId,
        },
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(products)
      .leftJoin(stores, eq(products.storeId, stores.id))
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(whereClause)
      .orderBy(orderBy)
      .limit(validatedQuery.limit)
      .offset(offset);

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(products)
      .leftJoin(stores, eq(products.storeId, stores.id))
      .where(whereClause);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / validatedQuery.limit);

    return NextResponse.json({
      products: allProducts,
      pagination: {
        page: validatedQuery.page,
        limit: validatedQuery.limit,
        totalCount,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { message: "Error fetching products" },
      { status: 500 }
    );
  }
}

// Bulk delete products
export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { productIds } = body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { message: "No product IDs provided" },
        { status: 400 }
      );
    }

    // Delete products
    await db
      .delete(products)
      .where(inArray(products.id, productIds));

    return NextResponse.json({
      message: `${productIds.length} products deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting products:", error);
    return NextResponse.json(
      { message: "Error deleting products" },
      { status: 500 }
    );
  }
}


