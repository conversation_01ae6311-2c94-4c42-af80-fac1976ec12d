import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { z } from "zod";
import { or, like, desc } from "drizzle-orm";

const querySchema = z.object({
  q: z.string().min(2),
  limit: z.coerce.number().default(10),
});

// Search users as admin
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const query = querySchema.parse({
      q: url.searchParams.get("q") || "",
      limit: url.searchParams.get("limit") || 10,
    });

    // Search users by name, username, or email
    const searchResults = await db.select({
      id: users.id,
      name: users.name,
      username: users.username,
      email: users.email,
      image: users.image,
      isAdmin: users.isAdmin,
    })
    .from(users)
    .where(
      or(
        like(users.name, `%${query.q}%`),
        like(users.username, `%${query.q}%`),
        like(users.email, `%${query.q}%`)
      )
    )
    .orderBy(desc(users.isAdmin))
    .limit(query.limit)
    .execute();

    return NextResponse.json({
      users: searchResults,
    });
  } catch (error) {
    console.error("Error searching users:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
