{"version": "5", "dialect": "mysql", "id": "c503c40e-b072-45bd-bb9e-eed0c882ed2d", "prevId": "72ff8d7a-7916-4ae1-b875-afdef5d3160c", "tables": {"accounts": {"name": "accounts", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerAccountId": {"name": "providerAccountId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"accounts_id": {"name": "accounts_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "admin_permissions": {"name": "admin_permissions", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "module": {"name": "module", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"admin_permissions_id": {"name": "admin_permissions_id", "columns": ["id"]}}, "uniqueConstraints": {"admin_permissions_name_unique": {"name": "admin_permissions_name_unique", "columns": ["name"]}, "admin_permissions_code_unique": {"name": "admin_permissions_code_unique", "columns": ["code"]}}, "checkConstraint": {}}, "admin_role_permissions": {"name": "admin_role_permissions", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role_id": {"name": "role_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "permission_id": {"name": "permission_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"admin_role_permissions_id": {"name": "admin_role_permissions_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "admin_roles": {"name": "admin_roles", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"admin_roles_id": {"name": "admin_roles_id", "columns": ["id"]}}, "uniqueConstraints": {"admin_roles_name_unique": {"name": "admin_roles_name_unique", "columns": ["name"]}}, "checkConstraint": {}}, "agents": {"name": "agents", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "serviceType": {"name": "serviceType", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "accountNumber": {"name": "accountNumber", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "accountName": {"name": "accountName", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "dailyLimit": {"name": "dailyLimit", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'10000.00'"}, "currentDailyAmount": {"name": "currentDailyAmount", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "commission": {"name": "commission", "type": "decimal(5,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'2.00'"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "isVerified": {"name": "isVerified", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "rating": {"name": "rating", "type": "decimal(3,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "totalTransactions": {"name": "totalTransactions", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "totalAmount": {"name": "totalAmount", "type": "decimal(15,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"agents_id": {"name": "agents_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "blogBookmarks": {"name": "blogBookmarks", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "blogId": {"name": "blogId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"blogBookmarks_id": {"name": "blogBookmarks_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "blogCategories": {"name": "blogCategories", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'#3b82f6'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"blogCategories_id": {"name": "blogCategories_id", "columns": ["id"]}}, "uniqueConstraints": {"blogCategories_slug_unique": {"name": "blogCategories_slug_unique", "columns": ["slug"]}}, "checkConstraint": {}}, "blogComments": {"name": "blogComments", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "blogId": {"name": "blogId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parentId": {"name": "parentId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"blogComments_id": {"name": "blogComments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "blogLikes": {"name": "blogLikes", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "blogId": {"name": "blogId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"blogLikes_id": {"name": "blogLikes_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "blogs": {"name": "blogs", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true, "autoincrement": false}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "coverImage": {"name": "coverImage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "authorId": {"name": "authorId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "categoryId": {"name": "categoryId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "enum('draft','published','archived')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}, "readTime": {"name": "readTime", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "viewCount": {"name": "viewCount", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "seoTitle": {"name": "seo<PERSON><PERSON>le", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "seoDescription": {"name": "seoDescription", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "publishedAt": {"name": "publishedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"blogs_id": {"name": "blogs_id", "columns": ["id"]}}, "uniqueConstraints": {"blogs_slug_unique": {"name": "blogs_slug_unique", "columns": ["slug"]}}, "checkConstraint": {}}, "cashout_requests": {"name": "cashout_requests", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "agentId": {"name": "agentId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "fee": {"name": "fee", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "netAmount": {"name": "netAmount", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "enum('pending','accepted','processing','completed','declined','cancelled')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "agentNote": {"name": "agentNote", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "transactionId": {"name": "transactionId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "processedAt": {"name": "processedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"cashout_requests_id": {"name": "cashout_requests_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "comments": {"name": "comments", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "postId": {"name": "postId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "parentId": {"name": "parentId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"comments_id": {"name": "comments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "event_attendees": {"name": "event_attendees", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "eventId": {"name": "eventId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "enum('going','interested','not_going')", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"event_attendees_id": {"name": "event_attendees_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "event_comments": {"name": "event_comments", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "eventId": {"name": "eventId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "videos": {"name": "videos", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"event_comments_id": {"name": "event_comments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "event_invites": {"name": "event_invites", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "eventId": {"name": "eventId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "fromUserId": {"name": "fromUserId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "toUserId": {"name": "toUserId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "enum('pending','accepted','declined')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"event_invites_id": {"name": "event_invites_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "events": {"name": "events", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "startTime": {"name": "startTime", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "endTime": {"name": "endTime", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "isOnline": {"name": "isOnline", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "onlineLink": {"name": "onlineLink", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "coverImage": {"name": "coverImage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "hostId": {"name": "hostId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "visibility": {"name": "visibility", "type": "enum('public','private','friends')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'public'"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"events_id": {"name": "events_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "friendships": {"name": "friendships", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user1Id": {"name": "user1Id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user2Id": {"name": "user2Id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "enum('pending','accepted','rejected','blocked')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"friendships_id": {"name": "friendships_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "group_members": {"name": "group_members", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "groupId": {"name": "groupId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "enum('admin','moderator','member','pending')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'member'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"group_members_id": {"name": "group_members_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "group_reports": {"name": "group_reports", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "groupId": {"name": "groupId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reporterId": {"name": "reporterId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reportedUserId": {"name": "reportedUserId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "postId": {"name": "postId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "reason": {"name": "reason", "type": "enum('spam','harassment','inappropriate_content','violation','other')", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "enum('pending','reviewed')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"group_reports_id": {"name": "group_reports_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "groups": {"name": "groups", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "visibility": {"name": "visibility", "type": "enum('public','private-visible','private-hidden')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'public'"}, "coverImage": {"name": "coverImage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "rules": {"name": "rules", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "creatorId": {"name": "creatorId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "postPermission": {"name": "postPermission", "type": "enum('all-members','admin-only')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'all-members'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"groups_id": {"name": "groups_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "likes": {"name": "likes", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "postId": {"name": "postId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "commentId": {"name": "commentId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "enum('like','dislike')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'like'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"likes_id": {"name": "likes_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "messages": {"name": "messages", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "senderId": {"name": "senderId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "receiverId": {"name": "receiverId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "read": {"name": "read", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"messages_id": {"name": "messages_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "notifications": {"name": "notifications", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "recipientId": {"name": "recipientId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "enum('like','comment','friend_request','friend_accept','message','group_invite','group_join_request','group_join_approved','group_post','group_announcement','event_invite','event_reminder','event_update','event_comment','store_follow','store_review','product_new','product_report')", "primaryKey": false, "notNull": true, "autoincrement": false}, "senderId": {"name": "senderId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "postId": {"name": "postId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "commentId": {"name": "commentId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "messageId": {"name": "messageId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "friendshipId": {"name": "friendshipId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "groupId": {"name": "groupId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "eventId": {"name": "eventId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "storeId": {"name": "storeId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "productId": {"name": "productId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "read": {"name": "read", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"notifications_id": {"name": "notifications_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "pageLikes": {"name": "pageLikes", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "pageId": {"name": "pageId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"pageLikes_id": {"name": "pageLikes_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "pages": {"name": "pages", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "coverImage": {"name": "coverImage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "profileImage": {"name": "profileImage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "creatorId": {"name": "creatorId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"pages_id": {"name": "pages_id", "columns": ["id"]}}, "uniqueConstraints": {"pages_username_unique": {"name": "pages_username_unique", "columns": ["username"]}}, "checkConstraint": {}}, "payment_gateways": {"name": "payment_gateways", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "displayName": {"name": "displayName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "enum('stripe','paypal','sslcommerz','bkash','nagad','rocket','bank')", "primaryKey": false, "notNull": true, "autoincrement": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false}, "depositFee": {"name": "depositFee", "type": "decimal(5,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "depositFixedFee": {"name": "depositFixedFee", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "minDeposit": {"name": "minDeposit", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'1.00'"}, "maxDeposit": {"name": "maxDeposit", "type": "decimal(15,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'10000.00'"}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'USD'"}, "sortOrder": {"name": "sortOrder", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"payment_gateways_id": {"name": "payment_gateways_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "pin_codes": {"name": "pin_codes", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "pinHash": {"name": "pinHash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "failedAttempts": {"name": "failedAttempts", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "lockedUntil": {"name": "lockedUntil", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "lastUsedAt": {"name": "lastUsedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"pin_codes_id": {"name": "pin_codes_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "posts": {"name": "posts", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "pageId": {"name": "pageId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "groupId": {"name": "groupId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "videos": {"name": "videos", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "privacy": {"name": "privacy", "type": "enum('public','friends','private')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'public'"}, "sharedPostId": {"name": "sharedPostId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "backgroundColor": {"name": "backgroundColor", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "feeling": {"name": "feeling", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "formattedContent": {"name": "formattedContent", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"posts_id": {"name": "posts_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "product_reports": {"name": "product_reports", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "productId": {"name": "productId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reason": {"name": "reason", "type": "enum('counterfeit','inappropriate','fraud','prohibited','other')", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "enum('pending','reviewed','resolved','dismissed')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"product_reports_id": {"name": "product_reports_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "price": {"name": "price", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "item_condition": {"name": "item_condition", "type": "enum('new','like_new','good','fair','poor')", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "photos": {"name": "photos", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "storeId": {"name": "storeId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "viewCount": {"name": "viewCount", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"products_id": {"name": "products_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "reports": {"name": "reports", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reporterId": {"name": "reporterId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reportedUserId": {"name": "reportedUserId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reason": {"name": "reason", "type": "enum('spam','harassment','inappropriate_content','impersonation','other')", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "enum('pending','reviewed','resolved','dismissed')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"reports_id": {"name": "reports_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "savedPosts": {"name": "savedPosts", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "postId": {"name": "postId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"savedPosts_id": {"name": "savedPosts_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "sessionToken": {"name": "sessionToken", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"sessions_id": {"name": "sessions_id", "columns": ["id"]}}, "uniqueConstraints": {"sessions_sessionToken_unique": {"name": "sessions_sessionToken_unique", "columns": ["sessionToken"]}}, "checkConstraint": {}}, "site_settings": {"name": "site_settings", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "setting_key": {"name": "setting_key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "group_name": {"name": "group_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"site_settings_id": {"name": "site_settings_id", "columns": ["id"]}}, "uniqueConstraints": {"site_settings_setting_key_unique": {"name": "site_settings_setting_key_unique", "columns": ["setting_key"]}}, "checkConstraint": {}}, "store_follows": {"name": "store_follows", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "storeId": {"name": "storeId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"store_follows_id": {"name": "store_follows_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "store_reviews": {"name": "store_reviews", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "storeId": {"name": "storeId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "rating": {"name": "rating", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "isApproved": {"name": "isApproved", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "isReported": {"name": "isReported", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"store_reviews_id": {"name": "store_reviews_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "store_settings": {"name": "store_settings", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "storeId": {"name": "storeId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "visibility": {"name": "visibility", "type": "enum('public','private')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'public'"}, "showOutOfStock": {"name": "showOutOfStock", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "showProductViews": {"name": "showProductViews", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "emailNotifications": {"name": "emailNotifications", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "productViewNotifications": {"name": "productViewNotifications", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"store_settings_id": {"name": "store_settings_id", "columns": ["id"]}}, "uniqueConstraints": {"store_settings_storeId_unique": {"name": "store_settings_storeId_unique", "columns": ["storeId"]}}, "checkConstraint": {}}, "stores": {"name": "stores", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "logo": {"name": "logo", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "banner": {"name": "banner", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "ownerId": {"name": "ownerId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "isVerified": {"name": "isVerified", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"stores_id": {"name": "stores_id", "columns": ["id"]}}, "uniqueConstraints": {"stores_slug_unique": {"name": "stores_slug_unique", "columns": ["slug"]}}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'user'"}, "is_admin": {"name": "is_admin", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "admin_role_id": {"name": "admin_role_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "coverImage": {"name": "coverImage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "birthday": {"name": "birthday", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "columns": ["email"]}}, "checkConstraint": {}}, "verificationTokens": {"name": "verificationTokens", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationTokens_identifier_token_pk": {"name": "verificationTokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wallet_settings": {"name": "wallet_settings", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "enum('string','number','boolean','json')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'string'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'general'"}, "isSystem": {"name": "isSystem", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"wallet_settings_id": {"name": "wallet_settings_id", "columns": ["id"]}}, "uniqueConstraints": {"wallet_settings_key_unique": {"name": "wallet_settings_key_unique", "columns": ["key"]}}, "checkConstraint": {}}, "wallet_transactions": {"name": "wallet_transactions", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "enum('deposit','send','receive','cashout','internal_transfer','earning','withdraw')", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "fee": {"name": "fee", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "netAmount": {"name": "netAmount", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "walletType": {"name": "walletType", "type": "enum('general','earning')", "primaryKey": false, "notNull": true, "autoincrement": false}, "toUserId": {"name": "toUserId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "toAgentId": {"name": "toAgentId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "fromWalletType": {"name": "fromWalletType", "type": "enum('general','earning')", "primaryKey": false, "notNull": false, "autoincrement": false}, "toWalletType": {"name": "toWalletType", "type": "enum('general','earning')", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "enum('pending','processing','completed','failed','cancelled')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "paymentGateway": {"name": "paymentGateway", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "gatewayTransactionId": {"name": "gatewayTransactionId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"wallet_transactions_id": {"name": "wallet_transactions_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wallets": {"name": "wallets", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "generalBalance": {"name": "generalBalance", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "earningBalance": {"name": "earningBalance", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "totalDeposited": {"name": "totalDeposited", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "totalWithdrawn": {"name": "totalWithdrawn", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "totalSent": {"name": "totalSent", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "totalReceived": {"name": "totalReceived", "type": "decimal(15,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"wallets_id": {"name": "wallets_id", "columns": ["id"]}}, "uniqueConstraints": {"wallets_userId_unique": {"name": "wallets_userId_unique", "columns": ["userId"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}