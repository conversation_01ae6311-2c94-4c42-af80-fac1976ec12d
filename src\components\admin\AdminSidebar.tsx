"use client";

import { Fragment, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon, ChevronDownIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import {
  HomeIcon,
  UsersIcon,
  Cog6ToothIcon,
  NewspaperIcon,
  UserGroupIcon,
  CalendarIcon,
  BuildingStorefrontIcon,
  ShieldCheckIcon,
  BanknotesIcon,
  CreditCardIcon,
  CloudIcon,
  ChartBarIcon,
  BellIcon,
  FlagIcon,
  KeyIcon,
  CurrencyDollarIcon,
  StarIcon,
  DocumentChartBarIcon,
  RectangleStackIcon,
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AdminSidebar({ isOpen, onClose }: AdminSidebarProps) {
  const pathname = usePathname();
  const [collapsedGroups, setCollapsedGroups] = useState<Record<string, boolean>>({});

  const toggleGroup = (groupName: string) => {
    setCollapsedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  const isGroupCollapsed = (groupName: string) => {
    return collapsedGroups[groupName] || false;
  };

  const navigationGroups = [
    {
      name: "Main",
      items: [
        { name: "Dashboard", href: "/admin/dashboard", icon: HomeIcon },
      ],
    },
    {
      name: "Configuration",
      items: [
        { name: "Site Settings", href: "/admin/settings", icon: Cog6ToothIcon },
        { name: "UI Controls", href: "/admin/ui", icon: NewspaperIcon },
        { name: "Admin Roles", href: "/admin/roles", icon: KeyIcon },
      ],
    },
    {
      name: "User & Content",
      items: [
        { name: "User Management", href: "/admin/users", icon: UsersIcon },
        { name: "Posts & Feed", href: "/admin/posts", icon: NewspaperIcon },
        { name: "Blogs", href: "/admin/blogs", icon: NewspaperIcon },
        { name: "Reports", href: "/admin/reports", icon: FlagIcon },
      ],
    },
    {
      name: "Features",
      items: [
        { name: "Groups", href: "/admin/groups", icon: UserGroupIcon },
        { name: "Events", href: "/admin/events", icon: CalendarIcon },
        { name: "Fan Pages", href: "/admin/fan-pages", icon: StarIcon },
        { name: "Marketplace", href: "/admin/marketplace", icon: BuildingStorefrontIcon },
        { name: "Stores", href: "/admin/stores", icon: BuildingStorefrontIcon },
      ],
    },
    {
      name: "Finance",
      items: [
        { name: "Wallet System", href: "/admin/wallet", icon: BanknotesIcon },
        { name: "Wallet Transactions", href: "/admin/wallet/transactions", icon: ChartBarIcon },
        { name: "Earning Wallet", href: "/admin/wallet/earnings", icon: CurrencyDollarIcon },
        { name: "Deposits", href: "/admin/payments", icon: CreditCardIcon },
        { name: "Withdrawals", href: "/admin/withdrawals", icon: BanknotesIcon },
      ],
    },
    {
      name: "Earning World",
      items: [
        { name: "Overview", href: "/admin/earning-world", icon: ChartBarIcon },
        { name: "Blog Monetization", href: "/admin/monetization", icon: CurrencyDollarIcon },
        { name: "User Earnings", href: "/admin/earning-world/users", icon: UsersIcon },
        { name: "Referral System", href: "/admin/earning-world/referrals", icon: UserGroupIcon },
        { name: "Payout Management", href: "/admin/earning-world/payouts", icon: BanknotesIcon },
        { name: "Analytics", href: "/admin/earning-world/analytics", icon: ChartBarIcon },
        { name: "Settings", href: "/admin/earning-world/settings", icon: Cog6ToothIcon },
      ],
    },
    {
      name: "Subscriptions",
      items: [
        { name: "Subscription Plans", href: "/admin/subscriptions/plans", icon: RectangleStackIcon },
        { name: "User Subscriptions", href: "/admin/subscriptions/users", icon: UsersIcon },
        { name: "Subscription Analytics", href: "/admin/subscriptions/analytics", icon: DocumentChartBarIcon },
        { name: "Payment Transactions", href: "/admin/subscriptions/transactions", icon: CreditCardIcon },
        { name: "Subscription Settings", href: "/admin/subscriptions/settings", icon: Cog6ToothIcon },
      ],
    },
    {
      name: "System",
      items: [
        { name: "Wallet Settings", href: "/admin/wallet/settings", icon: Cog6ToothIcon },
        { name: "Storage & Cloud", href: "/admin/storage", icon: CloudIcon },
        { name: "Analytics", href: "/admin/analytics", icon: ChartBarIcon },
        { name: "Notifications", href: "/admin/notifications", icon: BellIcon },
      ],
    },
    {
      name: "Development",
      items: [
        { name: "Session Testing", href: "/admin/test-session", icon: ShieldCheckIcon },
      ],
    },
  ];

  // Flatten navigation for mobile view
  const flatNavigation = navigationGroups.flatMap(group => group.items);

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <div className="flex h-full flex-col overflow-y-auto bg-white py-4 pb-12">
                  <div className="flex items-center justify-between px-4">
                    <Link href="/admin/dashboard" className="flex items-center">
                      <div className="mr-2 flex h-8 w-8 items-center justify-center rounded-md bg-blue-600 text-white">
                        <span className="text-lg font-bold">H</span>
                      </div>
                      <span className="text-xl font-bold text-gray-900">HIFNF <span className="text-blue-600">Admin</span></span>
                    </Link>
                    <button
                      type="button"
                      className="-mr-2 flex h-10 w-10 items-center justify-center rounded-md p-2 text-gray-500 hover:bg-gray-100"
                      onClick={onClose}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>

                  <div className="mt-5 flex flex-grow flex-col">
                    <nav className="flex-1 space-y-2 px-2">
                      {flatNavigation.map((item) => {
                        const isActive = pathname === item.href;
                        return (
                          <Link
                            key={item.name}
                            href={item.href}
                            className={cn(
                              isActive
                                ? "bg-blue-50 text-blue-700"
                                : "text-gray-700 hover:bg-gray-50 hover:text-gray-900",
                              "group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-all duration-200"
                            )}
                            onClick={onClose}
                          >
                            <div
                              className={cn(
                                isActive
                                  ? "bg-blue-100 text-blue-700"
                                  : "bg-gray-100 text-gray-500 group-hover:text-gray-700",
                                "mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md transition-colors duration-200"
                              )}
                            >
                              <item.icon className="h-5 w-5" aria-hidden="true" />
                            </div>
                            {item.name}
                            {isActive && (
                              <span className="absolute inset-y-0 left-0 w-1 rounded-tr-md rounded-br-md bg-blue-600" aria-hidden="true" />
                            )}
                          </Link>
                        );
                      })}
                    </nav>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex flex-grow flex-col overflow-y-auto border-r-2 border-gray-200 bg-gradient-to-b from-white to-gray-50 shadow-md">
          <div className="flex h-16 flex-shrink-0 items-center border-b border-gray-200 bg-white px-4">
            <Link href="/admin/dashboard" className="flex items-center">
              <div className="mr-2 flex h-8 w-8 items-center justify-center rounded-md bg-blue-600 text-white">
                <span className="text-lg font-bold">H</span>
              </div>
              <span className="text-xl font-bold text-gray-900">HIFNF <span className="text-blue-600">Admin</span></span>
            </Link>
          </div>
          <div className="flex flex-grow flex-col">
            <div className="mt-4 px-4">
              <div className="relative rounded-lg bg-blue-50 p-4 text-blue-800">
                <div className="flex items-center">
                  <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Need help?</p>
                    <p className="text-xs">Check our documentation</p>
                  </div>
                </div>
              </div>
            </div>
            <nav className="mt-4 flex-1 space-y-1 px-3 py-2">
              {navigationGroups.map((group) => (
                <div key={group.name} className="mb-3">
                  <button
                    onClick={() => toggleGroup(group.name)}
                    className="flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-100"
                  >
                    <span className="uppercase tracking-wider">{group.name}</span>
                    {isGroupCollapsed(group.name) ? (
                      <ChevronRightIcon className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDownIcon className="h-4 w-4 text-gray-500" />
                    )}
                  </button>

                  {!isGroupCollapsed(group.name) && (
                    <div className="mt-1 space-y-1 pl-2">
                      {group.items.map((item) => {
                        const isActive = pathname === item.href;
                        return (
                          <Link
                            key={item.name}
                            href={item.href}
                            className={cn(
                              isActive
                                ? "bg-blue-50 text-blue-700"
                                : "text-gray-700 hover:bg-gray-50 hover:text-gray-900",
                              "group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-all duration-200"
                            )}
                          >
                            <div
                              className={cn(
                                isActive
                                  ? "bg-blue-100 text-blue-700"
                                  : "bg-gray-100 text-gray-500 group-hover:text-gray-700",
                                "mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md transition-colors duration-200"
                              )}
                            >
                              <item.icon
                                className="h-5 w-5"
                                aria-hidden="true"
                              />
                            </div>
                            {item.name}
                            {isActive && (
                              <span className="absolute inset-y-0 left-0 w-1 rounded-tr-md rounded-br-md bg-blue-600" aria-hidden="true" />
                            )}
                          </Link>
                        );
                      })}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            <div className="mt-auto border-t border-gray-200 p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-700">Admin User</p>
                  <p className="text-xs text-gray-500">View Profile</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
