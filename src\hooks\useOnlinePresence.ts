"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { PartyKitManager, PartyKitClient } from "@/lib/partykit/client";
import { useFeatureFlags } from "@/lib/features/flags";

export interface UserPresence {
  userId: string;
  status: "online" | "offline" | "away";
  lastSeen: string;
  isTyping?: boolean;
  typingIn?: string; // conversation ID
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  conversationId: string;
  isTyping: boolean;
  timestamp: string;
}

interface UseOnlinePresenceOptions {
  trackTyping?: boolean;
  awayTimeout?: number; // milliseconds
  enablePresenceIndicators?: boolean;
}

export function useOnlinePresence(options: UseOnlinePresenceOptions = {}) {
  const { data: session } = useSession();
  const { shouldUseRealtime } = useFeatureFlags(session?.user?.id);
  
  const [userPresence, setUserPresence] = useState<Map<string, UserPresence>>(new Map());
  const [typingUsers, setTypingUsers] = useState<Map<string, TypingIndicator[]>>(new Map());
  const [connectionStatus, setConnectionStatus] = useState({
    connected: false,
    authenticated: false
  });
  const [isLoading, setIsLoading] = useState(true);

  const presenceClient = useRef<PartyKitClient | null>(null);
  const awayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  const {
    trackTyping = true,
    awayTimeout = 300000, // 5 minutes
    enablePresenceIndicators = true
  } = options;

  const useRealtime = shouldUseRealtime('onlinePresence');

  // Initialize presence tracking
  useEffect(() => {
    if (!session?.user?.id || !useRealtime) {
      setIsLoading(false);
      return;
    }

    const initializePresence = async () => {
      try {
        presenceClient.current = PartyKitManager.getClient("main", "presence");
        
        // Set up event listeners
        presenceClient.current.on("connected", () => {
          setConnectionStatus(prev => ({ ...prev, connected: true }));
        });

        presenceClient.current.on("authenticated", () => {
          setConnectionStatus(prev => ({ ...prev, authenticated: true }));
          setIsLoading(false);
          
          // Set initial online status
          updatePresenceStatus("online");
        });

        presenceClient.current.on("disconnected", () => {
          setConnectionStatus(prev => ({ ...prev, connected: false, authenticated: false }));
        });

        // Presence event listeners
        presenceClient.current.on("user_presence", handlePresenceUpdate);
        presenceClient.current.on("user_typing", handleTypingUpdate);

        // Connect to PartyKit
        await presenceClient.current.connect();

      } catch (error) {
        console.error("Failed to initialize presence tracking:", error);
        setIsLoading(false);
      }
    };

    initializePresence();

    return () => {
      if (presenceClient.current) {
        updatePresenceStatus("offline");
        presenceClient.current.disconnect();
      }
      
      if (awayTimeoutRef.current) {
        clearTimeout(awayTimeoutRef.current);
      }
      
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
    };
  }, [session?.user?.id, useRealtime]);

  // Track user activity for away status
  useEffect(() => {
    if (!useRealtime || !enablePresenceIndicators) return;

    const handleActivity = () => {
      lastActivityRef.current = Date.now();
      
      // Reset away timeout
      if (awayTimeoutRef.current) {
        clearTimeout(awayTimeoutRef.current);
      }
      
      // Set user as online if they were away
      const currentPresence = userPresence.get(session?.user?.id || "");
      if (currentPresence?.status === "away") {
        updatePresenceStatus("online");
      }
      
      // Set new away timeout
      awayTimeoutRef.current = setTimeout(() => {
        updatePresenceStatus("away");
      }, awayTimeout);
    };

    // Activity event listeners
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    // Initial activity setup
    handleActivity();

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });
    };
  }, [session?.user?.id, useRealtime, enablePresenceIndicators, awayTimeout, userPresence]);

  // Handle visibility change (tab focus/blur)
  useEffect(() => {
    if (!useRealtime || !enablePresenceIndicators) return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Tab is hidden, set as away after a delay
        activityTimeoutRef.current = setTimeout(() => {
          updatePresenceStatus("away");
        }, 30000); // 30 seconds
      } else {
        // Tab is visible, set as online
        if (activityTimeoutRef.current) {
          clearTimeout(activityTimeoutRef.current);
        }
        updatePresenceStatus("online");
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [useRealtime, enablePresenceIndicators]);

  const handlePresenceUpdate = useCallback((data: any) => {
    const { userId, status, timestamp } = data;
    
    setUserPresence(prev => {
      const newMap = new Map(prev);
      newMap.set(userId, {
        userId,
        status,
        lastSeen: timestamp
      });
      return newMap;
    });
  }, []);

  const handleTypingUpdate = useCallback((data: any) => {
    if (!trackTyping) return;
    
    const { userId, conversationId, isTyping, timestamp } = data;
    
    setTypingUsers(prev => {
      const newMap = new Map(prev);
      const conversationTyping = newMap.get(conversationId) || [];
      
      if (isTyping) {
        // Add or update typing indicator
        const existingIndex = conversationTyping.findIndex(t => t.userId === userId);
        const typingIndicator: TypingIndicator = {
          userId,
          userName: data.userName || "Unknown User",
          conversationId,
          isTyping: true,
          timestamp
        };
        
        if (existingIndex >= 0) {
          conversationTyping[existingIndex] = typingIndicator;
        } else {
          conversationTyping.push(typingIndicator);
        }
      } else {
        // Remove typing indicator
        const filtered = conversationTyping.filter(t => t.userId !== userId);
        if (filtered.length === 0) {
          newMap.delete(conversationId);
          return newMap;
        }
        newMap.set(conversationId, filtered);
        return newMap;
      }
      
      newMap.set(conversationId, conversationTyping);
      return newMap;
    });
    
    // Auto-clear typing indicator after 3 seconds
    if (isTyping) {
      setTimeout(() => {
        setTypingUsers(prev => {
          const newMap = new Map(prev);
          const conversationTyping = newMap.get(conversationId) || [];
          const filtered = conversationTyping.filter(t => 
            t.userId !== userId || Date.now() - new Date(t.timestamp).getTime() < 3000
          );
          
          if (filtered.length === 0) {
            newMap.delete(conversationId);
          } else {
            newMap.set(conversationId, filtered);
          }
          
          return newMap;
        });
      }, 3000);
    }
  }, [trackTyping]);

  const updatePresenceStatus = useCallback((status: "online" | "offline" | "away") => {
    if (!presenceClient.current?.isConnected() || !session?.user?.id) return;

    presenceClient.current.send({
      type: "presence",
      status,
      timestamp: new Date().toISOString()
    });

    // Update local state
    setUserPresence(prev => {
      const newMap = new Map(prev);
      newMap.set(session.user.id, {
        userId: session.user.id,
        status,
        lastSeen: new Date().toISOString()
      });
      return newMap;
    });
  }, [session?.user?.id]);

  const sendTypingIndicator = useCallback((conversationId: string, isTyping: boolean) => {
    if (!trackTyping || !presenceClient.current?.isConnected()) return;

    presenceClient.current.send({
      type: "typing",
      conversationId,
      isTyping,
      timestamp: new Date().toISOString()
    });
  }, [trackTyping]);

  const getUserPresence = useCallback((userId: string): UserPresence | null => {
    return userPresence.get(userId) || null;
  }, [userPresence]);

  const getTypingUsers = useCallback((conversationId: string): TypingIndicator[] => {
    return typingUsers.get(conversationId) || [];
  }, [typingUsers]);

  const isUserOnline = useCallback((userId: string): boolean => {
    const presence = userPresence.get(userId);
    return presence?.status === "online";
  }, [userPresence]);

  const isUserTyping = useCallback((userId: string, conversationId?: string): boolean => {
    if (conversationId) {
      const typing = typingUsers.get(conversationId) || [];
      return typing.some(t => t.userId === userId && t.isTyping);
    }
    
    // Check if user is typing in any conversation
    for (const typing of typingUsers.values()) {
      if (typing.some(t => t.userId === userId && t.isTyping)) {
        return true;
      }
    }
    
    return false;
  }, [typingUsers]);

  const getOnlineUsers = useCallback((): UserPresence[] => {
    return Array.from(userPresence.values()).filter(p => p.status === "online");
  }, [userPresence]);

  const getOnlineCount = useCallback((): number => {
    return Array.from(userPresence.values()).filter(p => p.status === "online").length;
  }, [userPresence]);

  return {
    // State
    userPresence: Array.from(userPresence.values()),
    typingUsers: Array.from(typingUsers.entries()).reduce((acc, [conversationId, users]) => {
      acc[conversationId] = users;
      return acc;
    }, {} as Record<string, TypingIndicator[]>),
    connectionStatus,
    isLoading,

    // Actions
    updatePresenceStatus,
    sendTypingIndicator,

    // Utilities
    getUserPresence,
    getTypingUsers,
    isUserOnline,
    isUserTyping,
    getOnlineUsers,
    getOnlineCount,
    isConnected: connectionStatus.connected && connectionStatus.authenticated,
    isEnabled: useRealtime
  };
}
