"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { useSession } from "next-auth/react";
import { messageService, MessageResponse } from "@/lib/services/messageService";
import { toast } from "react-hot-toast";

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  read: boolean;
  createdAt: string;
  isFromPage?: boolean;
  fanPageId?: string;
  sender: {
    id: string;
    name: string;
    image: string;
  };
  receiver: {
    id: string;
    name: string;
    image: string;
  };
}

interface Conversation {
  user: {
    id: string;
    name: string;
    image: string;
  };
  lastMessage: Message;
  unreadCount: number;
  type?: 'user' | 'fanpage';
  fanPageId?: string;
  isPinned?: boolean;
}

interface UseEnhancedMessagingReturn {
  // State
  conversations: Conversation[];
  messages: Message[];
  selectedConversation: string | null;
  isLoading: boolean;
  isSending: boolean;
  isLoadingConversations: boolean;

  // Actions
  sendMessage: (content: string, conversationId: string) => Promise<boolean>;
  sendPageReply: (content: string, pageId: string, recipientId: string) => Promise<boolean>;
  selectConversation: (conversationId: string) => Promise<void>;
  markAsRead: (conversationId: string) => Promise<void>;
  refreshConversations: () => Promise<void>;
  refreshMessages: (conversationId: string) => Promise<void>;

  // Utilities
  getConversationType: (conversationId: string) => 'user' | 'fanpage';
  isPageOwner: (pageId: string) => boolean;
}

export function useEnhancedMessaging(): UseEnhancedMessagingReturn {
  const { data: session } = useSession();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [isLoadingConversations, setIsLoadingConversations] = useState(true);

  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch conversations
  const refreshConversations = useCallback(async () => {
    if (!session?.user?.id) return;

    setIsLoadingConversations(true);
    try {
      const result = await messageService.fetchConversations();

      if (result.success && result.data) {
        setConversations(result.data);
      } else {
        toast.error(result.error || "Failed to load conversations");
      }
    } catch (error) {
      toast.error("Network error while loading conversations");
    } finally {
      setIsLoadingConversations(false);
    }
  }, [session?.user?.id]);

  // Fetch messages for specific conversation
  const refreshMessages = useCallback(async (conversationId: string) => {
    if (!conversationId) return;

    setIsLoading(true);
    try {
      const result = await messageService.fetchMessages(conversationId);

      if (result.success && result.data) {
        setMessages(result.data);

        // Auto-mark as read
        await markAsRead(conversationId);
      } else {
        toast.error(result.error || "Failed to load messages");
      }
    } catch (error) {
      toast.error("Network error while loading messages");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Select conversation
  const selectConversation = useCallback(async (conversationId: string) => {
    setSelectedConversation(conversationId);
    await refreshMessages(conversationId);
  }, [refreshMessages]);

  // Send message
  const sendMessage = useCallback(async (content: string, conversationId: string): Promise<boolean> => {
    if (!content.trim() || !conversationId || !session?.user?.id || isSending) {
      return false;
    }

    setIsSending(true);

    try {
      let result: MessageResponse;

      if (conversationId.startsWith('fanpage_')) {
        const fanPageId = conversationId.replace('fanpage_', '');
        result = await messageService.sendFanPageMessage(content, fanPageId);
      } else {
        result = await messageService.sendUserMessage(content, conversationId);
      }

      if (result.success && result.data) {
        // Add message to local state immediately
        const newMessage = result.data;
        setMessages(prev => [...prev, newMessage]);

        // Update conversation list
        setConversations(prev => {
          const updated = prev.map(conv =>
            conv.user.id === conversationId
              ? { ...conv, lastMessage: newMessage }
              : conv
          );

          // Move conversation to top
          const selectedConv = updated.find(conv => conv.user.id === conversationId);
          if (selectedConv) {
            const others = updated.filter(conv => conv.user.id !== conversationId);
            return [selectedConv, ...others];
          }

          return updated;
        });

        // Show success feedback
        toast.success(result.message || "Message sent successfully", {
          duration: 2000,
          position: 'bottom-right',
        });

        return true;
      } else {
        toast.error(result.error || "Failed to send message");
        return false;
      }
    } catch (error) {
      toast.error("Network error. Please try again.");
      return false;
    } finally {
      setIsSending(false);
    }
  }, [session?.user?.id, isSending]);

  // Send page reply
  const sendPageReply = useCallback(async (content: string, pageId: string, recipientId: string): Promise<boolean> => {
    if (!content.trim() || !pageId || !recipientId || isSending) {
      return false;
    }

    setIsSending(true);

    try {
      const result = await messageService.sendPageReply(content, pageId, recipientId);

      if (result.success) {
        toast.success(result.message || "Reply sent successfully", {
          duration: 2000,
          position: 'bottom-right',
        });
        return true;
      } else {
        toast.error(result.error || "Failed to send reply");
        return false;
      }
    } catch (error) {
      toast.error("Network error. Please try again.");
      return false;
    } finally {
      setIsSending(false);
    }
  }, [isSending]);

  // Mark messages as read
  const markAsRead = useCallback(async (conversationId: string) => {
    try {
      const result = await messageService.markAsRead(conversationId);

      if (result.success) {
        // Update local state
        setConversations(prev =>
          prev.map(conv =>
            conv.user.id === conversationId
              ? { ...conv, unreadCount: 0 }
              : conv
          )
        );

        setMessages(prev =>
          prev.map(msg => ({ ...msg, read: true }))
        );
      }
    } catch (error) {
      console.error("Error marking messages as read:", error);
    }
  }, []);

  // Utility functions
  const getConversationType = useCallback((conversationId: string): 'user' | 'fanpage' => {
    return conversationId.startsWith('fanpage_') ? 'fanpage' : 'user';
  }, []);

  const isPageOwner = useCallback((pageId: string): boolean => {
    // This would need to be implemented based on your page ownership logic
    // For now, returning false as a placeholder
    return false;
  }, []);

  // Auto-refresh conversations periodically
  useEffect(() => {
    if (session?.user?.id) {
      refreshConversations();

      // Set up periodic refresh
      const interval = setInterval(refreshConversations, 30000); // Every 30 seconds

      return () => clearInterval(interval);
    }
  }, [session?.user?.id, refreshConversations]);

  // Auto-refresh current conversation messages
  useEffect(() => {
    if (selectedConversation) {
      const interval = setInterval(() => {
        refreshMessages(selectedConversation);
      }, 10000); // Every 10 seconds

      return () => clearInterval(interval);
    }
  }, [selectedConversation, refreshMessages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    conversations,
    messages,
    selectedConversation,
    isLoading,
    isSending,
    isLoadingConversations,

    // Actions
    sendMessage,
    sendPageReply,
    selectConversation,
    markAsRead,
    refreshConversations,
    refreshMessages,

    // Utilities
    getConversationType,
    isPageOwner,
  };
}
