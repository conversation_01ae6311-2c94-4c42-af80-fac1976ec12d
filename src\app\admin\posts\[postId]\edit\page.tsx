"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import {
  ArrowLeftIcon,
  XMarkIcon,
  PhotoIcon,
  VideoCameraIcon,
  MapPinIcon,
  FaceSmileIcon,
} from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";
import { useDropzone } from "react-dropzone";

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  };
}

export default function AdminPostEditPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [post, setPost] = useState<Post | null>(null);
  const [formData, setFormData] = useState({
    content: "",
    privacy: "public",
    location: "",
    feeling: "",
    activity: "",
    backgroundColor: "",
    formattedContent: false,
  });
  const [images, setImages] = useState<string[]>([]);
  const [videos, setVideos] = useState<string[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchPost();
  }, []);

  const fetchPost = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/posts/${params?.postId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch post");
      }

      const fetchedPost = await response.json();
      setPost(fetchedPost);
      setFormData({
        content: fetchedPost.content || "",
        privacy: fetchedPost.privacy || "public",
        location: fetchedPost.location || "",
        feeling: fetchedPost.feeling || "",
        activity: fetchedPost.activity || "",
        backgroundColor: fetchedPost.backgroundColor || "",
        formattedContent: fetchedPost.formattedContent || false,
      });
      setImages(fetchedPost.images || []);
      setVideos(fetchedPost.videos || []);
    } catch (error) {
      console.error("Error fetching post:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
      'video/*': ['.mp4', '.webm', '.ogg']
    },
    onDrop: (acceptedFiles) => {
      setUploadedFiles([...uploadedFiles, ...acceptedFiles]);

      // In a real implementation, you would upload these files to your server or cloud storage
      // For now, we'll just create object URLs for preview
      acceptedFiles.forEach(file => {
        if (file.type.startsWith('image/')) {
          setImages([...images, URL.createObjectURL(file)]);
        } else if (file.type.startsWith('video/')) {
          setVideos([...videos, URL.createObjectURL(file)]);
        }
      });
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const removeVideo = (index: number) => {
    const newVideos = [...videos];
    newVideos.splice(index, 1);
    setVideos(newVideos);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.content.trim() && images.length === 0 && videos.length === 0) {
      newErrors.content = "Post must contain either text content, images, or videos";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      const response = await fetch(`/api/admin/posts/${params?.postId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: formData.content,
          images: images.length > 0 ? images : undefined,
          videos: videos.length > 0 ? videos : undefined,
          privacy: formData.privacy,
          backgroundColor: formData.backgroundColor || undefined,
          feeling: formData.feeling || undefined,
          activity: formData.activity || undefined,
          location: formData.location || undefined,
          formattedContent: formData.formattedContent,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update post");
      }

      alert("Post updated successfully");
      router.push(`/admin/posts/${params?.postId}`);
    } catch (error) {
      console.error("Error updating post:", error);
      alert("Failed to update post");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!post) {
    return (
      <AdminLayout>
        <div className="flex h-64 flex-col items-center justify-center">
          <p className="text-lg font-medium text-gray-900">Post not found</p>
          <Button className="mt-4" onClick={() => router.push("/admin/posts")}>
            Back to Posts
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="outline"
            className="mr-4"
            onClick={() => router.push(`/admin/posts/${post.id}`)}
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Post
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Edit Post</h1>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <form onSubmit={handleSubmit}>
          <div className="p-6">
            {/* Post author info */}
            <div className="mb-6 flex items-center">
              <div className="h-10 w-10 flex-shrink-0">
                {post.user.image ? (
                  <Image
                    src={post.user.image}
                    alt={post.user.name}
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded-full"
                  />
                ) : (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                    <span className="text-sm font-medium text-gray-500">
                      {post.user.name.charAt(0)}
                    </span>
                  </div>
                )}
              </div>
              <div className="ml-3">
                <div className="text-sm font-medium text-gray-900">
                  {post.user.name}
                  {post.user.username && (
                    <span className="ml-1 text-gray-500">@{post.user.username}</span>
                  )}
                </div>
                <div className="text-xs text-gray-500">
                  Original post date: {new Date(post.createdAt).toLocaleString()}
                </div>
              </div>
            </div>

            {/* Post content */}
            <div className="mb-6">
              <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                Content
              </label>
              <div className="mt-1">
                <textarea
                  id="content"
                  name="content"
                  rows={4}
                  value={formData.content}
                  onChange={handleInputChange}
                  className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm ${
                    errors.content ? "border-red-300" : ""
                  }`}
                  placeholder="What's on your mind?"
                />
                {errors.content && (
                  <p className="mt-1 text-sm text-red-600">{errors.content}</p>
                )}
              </div>
            </div>

            {/* Media section */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Media</label>
              <div className="mt-1 flex flex-wrap gap-4">
                {/* Images */}
                {images.map((image, index) => (
                  <div key={`image-${index}`} className="relative h-32 w-32">
                    <Image
                      src={image}
                      alt={`Image ${index + 1}`}
                      width={128}
                      height={128}
                      className="h-32 w-32 rounded-md object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-1 right-1 rounded-full bg-gray-800 bg-opacity-75 p-1 text-white hover:bg-opacity-100"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Videos */}
                {videos.map((video, index) => (
                  <div key={`video-${index}`} className="relative h-32 w-32">
                    <video
                      src={video}
                      className="h-32 w-32 rounded-md object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeVideo(index)}
                      className="absolute top-1 right-1 rounded-full bg-gray-800 bg-opacity-75 p-1 text-white hover:bg-opacity-100"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Upload dropzone */}
                <div
                  {...getRootProps()}
                  className="flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                >
                  <input {...getInputProps()} />
                  <PhotoIcon className="h-8 w-8 text-gray-400" />
                  <p className="mt-1 text-xs text-gray-500">Add media</p>
                </div>
              </div>
            </div>

            {/* Privacy setting */}
            <div className="mb-6">
              <label htmlFor="privacy" className="block text-sm font-medium text-gray-700">
                Privacy
              </label>
              <select
                id="privacy"
                name="privacy"
                value={formData.privacy}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="public">Public</option>
                <option value="subscribers">Subscribers</option>
                <option value="private">Private</option>
              </select>
            </div>

            {/* Additional fields */}
            <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                  Location
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 sm:text-sm">
                    <MapPinIcon className="h-4 w-4" />
                  </span>
                  <input
                    type="text"
                    name="location"
                    id="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="Add location"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="feeling" className="block text-sm font-medium text-gray-700">
                  Feeling
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 sm:text-sm">
                    <FaceSmileIcon className="h-4 w-4" />
                  </span>
                  <input
                    type="text"
                    name="feeling"
                    id="feeling"
                    value={formData.feeling}
                    onChange={handleInputChange}
                    className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="How are you feeling?"
                  />
                </div>
              </div>
            </div>

            <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label htmlFor="activity" className="block text-sm font-medium text-gray-700">
                  Activity
                </label>
                <input
                  type="text"
                  name="activity"
                  id="activity"
                  value={formData.activity}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  placeholder="What are you doing?"
                />
              </div>
              <div>
                <label htmlFor="backgroundColor" className="block text-sm font-medium text-gray-700">
                  Background Color
                </label>
                <input
                  type="text"
                  name="backgroundColor"
                  id="backgroundColor"
                  value={formData.backgroundColor}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  placeholder="e.g. #f0f8ff"
                />
              </div>
            </div>

            <div className="mb-6">
              <div className="flex items-center">
                <input
                  id="formattedContent"
                  name="formattedContent"
                  type="checkbox"
                  checked={formData.formattedContent}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="formattedContent" className="ml-2 block text-sm text-gray-900">
                  Content contains formatting (HTML/Markdown)
                </label>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 bg-gray-50 px-4 py-3 text-right sm:px-6">
            <Button
              type="button"
              variant="outline"
              className="mr-3"
              onClick={() => router.push(`/admin/posts/${post.id}`)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? <Spinner size="sm" className="mr-2" /> : null}
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
