import { NextResponse } from "next/server";
import { MonetizationService } from "@/lib/monetization/monetizationService";

// Get global monetization status
export async function GET() {
  try {
    const isEnabled = await MonetizationService.isMonetizationEnabled();
    const config = await MonetizationService.getMonetizationConfig();

    return NextResponse.json({
      success: true,
      data: {
        isEnabled,
        cprRate: config.cprRate,
        minPayoutThreshold: config.minPayoutThreshold,
        minReadDuration: config.minReadDuration,
      },
    });
  } catch (error: any) {
    console.error("Error fetching monetization status:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to fetch monetization status" 
      },
      { status: 500 }
    );
  }
}
