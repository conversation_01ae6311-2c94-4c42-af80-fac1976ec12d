// Server-side utilities for sending real-time updates to PartyKit

export interface NotificationPayload {
  id: string;
  type: "like" | "comment" | "message" | "follow" | "fan_page_message" | "fan_page_reply" | "subscription";
  recipientId: string;
  senderId: string;
  title?: string;
  message?: string;
  data?: any;
  timestamp: string;
  priority?: "low" | "medium" | "high";
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export interface MessagePayload {
  id: string;
  senderId: string;
  receiverId?: string;
  content: string;
  type: "direct" | "fanpage";
  fanPageId?: string;
  timestamp: string;
  status: "sent" | "delivered" | "read";
}

export interface PresencePayload {
  userId: string;
  status: "online" | "offline" | "away";
  timestamp: string;
}

class PartyKitServerClient {
  private baseUrl: string;
  private apiToken: string;

  constructor() {
    this.baseUrl = process.env.PARTYKIT_HOST || "http://localhost:1999";
    this.apiToken = process.env.PARTYKIT_API_TOKEN || "";
  }

  async sendNotification(notification: NotificationPayload): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/parties/notifications/${notification.recipientId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiToken}`,
          'User-Agent': 'HiFnF-Server/1.0'
        },
        body: JSON.stringify({
          type: 'send_notification',
          notification
        })
      });

      if (!response.ok) {
        console.error(`Failed to send notification to PartyKit: ${response.status} ${response.statusText}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending notification to PartyKit:', error);
      return false;
    }
  }

  async sendMessage(message: MessagePayload): Promise<boolean> {
    try {
      const roomId = message.type === "direct" 
        ? `${message.senderId}-${message.receiverId}` 
        : `fanpage-${message.fanPageId}`;

      const response = await fetch(`${this.baseUrl}/parties/chat/${roomId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiToken}`,
          'User-Agent': 'HiFnF-Server/1.0'
        },
        body: JSON.stringify({
          type: 'send_message',
          message
        })
      });

      if (!response.ok) {
        console.error(`Failed to send message to PartyKit: ${response.status} ${response.statusText}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending message to PartyKit:', error);
      return false;
    }
  }

  async updatePresence(presence: PresencePayload): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/parties/main/presence`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiToken}`,
          'User-Agent': 'HiFnF-Server/1.0'
        },
        body: JSON.stringify({
          type: 'presence',
          ...presence
        })
      });

      if (!response.ok) {
        console.error(`Failed to update presence in PartyKit: ${response.status} ${response.statusText}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating presence in PartyKit:', error);
      return false;
    }
  }

  async broadcastToRoom(roomId: string, message: any): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/parties/main/${roomId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiToken}`,
          'User-Agent': 'HiFnF-Server/1.0'
        },
        body: JSON.stringify(message)
      });

      if (!response.ok) {
        console.error(`Failed to broadcast to room ${roomId}: ${response.status} ${response.statusText}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`Error broadcasting to room ${roomId}:`, error);
      return false;
    }
  }

  // Helper method to check if PartyKit is available
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'User-Agent': 'HiFnF-Server/1.0'
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      return response.ok;
    } catch (error) {
      console.error('PartyKit health check failed:', error);
      return false;
    }
  }
}

// Singleton instance
export const partyKitServer = new PartyKitServerClient();

// Helper functions for common operations
export async function emitNotification(notification: NotificationPayload): Promise<boolean> {
  // Check if real-time notifications are enabled
  const realtimeEnabled = process.env.NEXT_PUBLIC_FEATURE_REALTIME_NOTIFICATIONS === "true";
  
  if (!realtimeEnabled) {
    console.log('Real-time notifications disabled, skipping PartyKit emission');
    return false;
  }

  return await partyKitServer.sendNotification(notification);
}

export async function emitMessage(message: MessagePayload): Promise<boolean> {
  // Check if real-time messaging is enabled
  const realtimeEnabled = process.env.NEXT_PUBLIC_FEATURE_REALTIME_MESSAGING === "true";
  
  if (!realtimeEnabled) {
    console.log('Real-time messaging disabled, skipping PartyKit emission');
    return false;
  }

  return await partyKitServer.sendMessage(message);
}

export async function emitPresenceUpdate(presence: PresencePayload): Promise<boolean> {
  // Check if online presence is enabled
  const presenceEnabled = process.env.NEXT_PUBLIC_FEATURE_ONLINE_PRESENCE === "true";
  
  if (!presenceEnabled) {
    console.log('Online presence disabled, skipping PartyKit emission');
    return false;
  }

  return await partyKitServer.updatePresence(presence);
}

// Utility to create notification payloads from database records
export function createNotificationPayload(
  dbNotification: any,
  sender?: any
): NotificationPayload {
  return {
    id: dbNotification.id,
    type: dbNotification.type,
    recipientId: dbNotification.recipientId,
    senderId: dbNotification.senderId,
    title: dbNotification.title,
    message: dbNotification.message,
    data: dbNotification.data ? JSON.parse(dbNotification.data) : undefined,
    timestamp: dbNotification.createdAt || new Date().toISOString(),
    priority: dbNotification.priority || "medium",
    actionUrl: dbNotification.actionUrl,
    metadata: {
      sender: sender ? {
        id: sender.id,
        name: sender.name,
        image: sender.image
      } : undefined,
      postId: dbNotification.postId,
      commentId: dbNotification.commentId,
      messageId: dbNotification.messageId,
      fanPageId: dbNotification.fanPageId,
      ...dbNotification.metadata
    }
  };
}

// Utility to create message payloads from database records
export function createMessagePayload(
  dbMessage: any,
  type: "direct" | "fanpage" = "direct"
): MessagePayload {
  return {
    id: dbMessage.id,
    senderId: dbMessage.senderId,
    receiverId: dbMessage.receiverId,
    content: dbMessage.content,
    type,
    fanPageId: dbMessage.fanPageId,
    timestamp: dbMessage.createdAt || new Date().toISOString(),
    status: "sent"
  };
}

// Rate limiting for PartyKit requests
class RateLimiter {
  private requests = new Map<string, number[]>();
  private maxRequests = 100; // per minute
  private windowMs = 60000; // 1 minute

  canMakeRequest(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    return true;
  }
}

export const rateLimiter = new RateLimiter();
