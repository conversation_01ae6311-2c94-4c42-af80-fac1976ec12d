"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { EventsList } from "@/components/events/EventsList";
import { CalendarView } from "@/components/events/CalendarView";
import { CreateEventForm } from "@/components/events/CreateEventForm";
import { CalendarIcon, PlusIcon, ViewColumnsIcon, Squares2X2Icon } from "@heroicons/react/24/outline";

export default function EventsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeView, setActiveView] = useState<"list" | "calendar">("list");
  const [activeFilter, setActiveFilter] = useState<"upcoming" | "past" | "invites" | "hosting">("upcoming");
  const [isCreateEventOpen, setIsCreateEventOpen] = useState(false);

  // Get current date for filtering
  const now = new Date();
  const today = now.toISOString();

  // Define filters based on active filter
  const getFilterForActiveTab = () => {
    switch (activeFilter) {
      case "upcoming":
        return { startDate: today };
      case "past":
        return { endDate: today };
      case "hosting":
        return { hostId: "current" }; // This will be replaced with the actual user ID in the API
      case "invites":
        return {}; // Invites are handled differently in the API
      default:
        return {};
    }
  };

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Events
          </h1>
          <Button onClick={() => setIsCreateEventOpen(true)}>
            <PlusIcon className="h-5 w-5 mr-1" />
            Create Event
          </Button>
        </div>

        <div className="mb-8 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="flex-1 sm:max-w-xs">
            <Input
              type="search"
              placeholder="Search events..."
              className="w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex space-x-2">
              <Button
                variant={activeFilter === "upcoming" ? "primary" : "outline"}
                onClick={() => setActiveFilter("upcoming")}
              >
                Upcoming
              </Button>
              <Button
                variant={activeFilter === "past" ? "primary" : "outline"}
                onClick={() => setActiveFilter("past")}
              >
                Past
              </Button>
              <Button
                variant={activeFilter === "invites" ? "primary" : "outline"}
                onClick={() => setActiveFilter("invites")}
              >
                Invites
              </Button>
              <Button
                variant={activeFilter === "hosting" ? "primary" : "outline"}
                onClick={() => setActiveFilter("hosting")}
              >
                Hosting
              </Button>
            </div>
            <div className="border-l border-gray-300 h-6 mx-2"></div>
            <div className="flex space-x-2">
              <Button
                variant={activeView === "list" ? "primary" : "outline"}
                size="sm"
                onClick={() => setActiveView("list")}
              >
                <Squares2X2Icon className="h-5 w-5" />
              </Button>
              <Button
                variant={activeView === "calendar" ? "primary" : "outline"}
                size="sm"
                onClick={() => setActiveView("calendar")}
              >
                <CalendarIcon className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {activeView === "list" ? (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {activeFilter === "upcoming" && (
              <EventsList
                title="Your Events"
                emptyMessage="You don't have any upcoming events. Create or join events to connect with people."
                filter={{
                  ...getFilterForActiveTab(),
                  search: searchQuery || undefined
                }}
              />
            )}

            {activeFilter === "past" && (
              <EventsList
                title="Past Events"
                emptyMessage="You don't have any past events."
                filter={{
                  ...getFilterForActiveTab(),
                  search: searchQuery || undefined
                }}
              />
            )}

            {activeFilter === "invites" && (
              <EventsList
                title="Event Invites"
                emptyMessage="You don't have any event invites."
                filter={{
                  ...getFilterForActiveTab(),
                  search: searchQuery || undefined
                }}
              />
            )}

            {activeFilter === "hosting" && (
              <EventsList
                title="Events You're Hosting"
                emptyMessage="You're not hosting any events yet. Create an event to get started."
                filter={{
                  ...getFilterForActiveTab(),
                  search: searchQuery || undefined
                }}
              />
            )}

            <EventsList
              title="Suggested Events"
              emptyMessage="No suggested events found."
              filter={{
                search: searchQuery || undefined,
                startDate: today,
                visibility: "public"
              }}
            />

            <EventsList
              title="This Weekend"
              emptyMessage="No events this weekend."
              filter={{
                search: searchQuery || undefined,
                startDate: today,
                // End date would be set to the end of the weekend
                endDate: new Date(now.getTime() + (7 - now.getDay()) * 24 * 60 * 60 * 1000).toISOString(),
                visibility: "public"
              }}
            />
          </div>
        ) : (
          <CalendarView />
        )}
      </div>

      <CreateEventForm
        isOpen={isCreateEventOpen}
        onClose={() => setIsCreateEventOpen(false)}
      />
    </MainLayout>
  );
}
