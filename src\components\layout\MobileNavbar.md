# MobileNavbar Component

একটি আধুনিক এবং ব্যবহারকারী-বান্ধব মোবাইল নেভিগেশন কম্পোনেন্ট যা শুধুমাত্র মোবাইল ডিভাইসের জন্য ডিজাইন করা হয়েছে।

## বৈশিষ্ট্যসমূহ

### 🎨 ডিজাইন
- **গ্রেডিয়েন্ট ব্যাকগ্রাউন্ড**: সুন্দর নীল গ্রেডিয়েন্ট হেডার
- **গ্লাস মরফিজম**: ব্যাকড্রপ ব্লার ইফেক্ট
- **অ্যানিমেশন**: মসৃণ ট্রানজিশন এবং হোভার ইফেক্ট
- **রেসপন্সিভ**: শুধুমাত্র মোবাইল ডিভাইসে দৃশ্যমান

### 🔍 সার্চ ফিচার
- **ইনলাইন সার্চ বার**: হেডারে এমবেড করা সার্চ
- **সাজেশন সাপোর্ট**: রিয়েল-টাইম সার্চ সাজেশন
- **কীবোর্ড নেভিগেশন**: এন্টার কী দিয়ে সার্চ

### 📱 মোবাইল মেনু
- **স্লাইড-আউট মেনু**: ডান দিক থেকে স্লাইড করে আসে
- **ক্লিকেবল প্রোফাইল সেকশন**: ইউজার ইনফো এবং প্রোফাইল লিংক
- **নেভিগেশন লিংক**: সাইটের প্রধান পেজগুলো
- **অ্যাকাউন্ট সেকশন**: প্রোফাইল, সেটিংস, সাইন আউট

### 🔔 নোটিফিকেশন
- **ব্যাজ সাপোর্ট**: আনরিড নোটিফিকেশন কাউন্ট
- **ভিজুয়াল ইন্ডিকেটর**: মেনু বাটনে রেড ডট
- **রিয়েল-টাইম আপডেট**: লাইভ নোটিফিকেশন কাউন্ট

### 🎯 ইন্টারঅ্যাকশন
- **হ্যাপটিক ফিডব্যাক**: টাচ ইন্টারঅ্যাকশনে ভাইব্রেশন
- **অ্যাক্টিভ স্কেল**: বাটন প্রেসে স্কেল ইফেক্ট
- **হোভার ইফেক্ট**: আইকন স্কেল এবং কালার চেঞ্জ

## ব্যবহার

```tsx
import { MobileNavbar } from "@/components/layout/MobileNavbar";

// MainLayout এ ব্যবহার
export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <MobileNavbar />
      <main>{children}</main>
    </div>
  );
}
```

## কম্পোনেন্ট স্ট্রাকচার

### হেডার সেকশন
- **লোগো**: ব্র্যান্ড আইডেন্টিটি
- **সার্চ বার**: ইনলাইন সার্চ ফিচার
- **মেনু বাটন**: হ্যামবার্গার মেনু

### মেনু প্যানেল
1. **ক্লিকেবল প্রোফাইল হেডার**
   - ইউজার অ্যাভাটার
   - নাম এবং ইউজারনেম
   - অনলাইন স্ট্যাটাস ইন্ডিকেটর
   - "View Profile" লেবেল
   - প্রোফাইল পেজে নেভিগেট করে

2. **মূল নেভিগেশন** (লেফট সাইডবার থেকে)
   - News Feed
   - Connections
   - Saved
   - Memories
   - Groups
   - Events
   - Pages
   - Marketplace
   - Blogs
   - Photos
   - Videos

3. **ওয়ালেট ও আর্নিং**
   - Wallet (ব্যালেন্স সহ)
   - Earning World (আর্নিং ব্যালেন্স সহ)

4. **অ্যাকাউন্ট সেকশন**
   - My Profile
   - Settings
   - Sign Out

## স্টাইলিং

### কালার স্কিম
- **প্রাইমারি**: নীল (#3B82F6)
- **সেকেন্ডারি**: সবুজ (#10B981)
- **অ্যাকসেন্ট**: লাল (#EF4444)
- **নিউট্রাল**: গ্রে শেড

### অ্যানিমেশন
- **ট্রানজিশন**: 200ms ease-in-out
- **স্কেল**: 0.95 অ্যাক্টিভ স্টেটে
- **হোভার**: 1.1 স্কেল আইকনে

## ডিপেন্ডেন্সি

- `@headlessui/react`: মেনু ট্রানজিশন
- `@heroicons/react`: আইকন
- `next/image`: অপটিমাইজড ইমেজ
- `next-auth/react`: অথেন্টিকেশন
- `tailwindcss`: স্টাইলিং

## হুক ব্যবহার

- `useMobileView`: মোবাইল ডিটেকশন
- `useNotificationCount`: নোটিফিকেশন কাউন্ট
- `useSession`: ইউজার সেশন
- `usePathname`: অ্যাক্টিভ রুট

## পারফরমেন্স

- **কন্ডিশনাল রেন্ডারিং**: শুধু মোবাইলে লোড
- **লেজি লোডিং**: ইমেজ অপটিমাইজেশন
- **মেমোইজেশন**: অপ্রয়োজনীয় রি-রেন্ডার এড়ানো

## অ্যাক্সেসিবিলিটি

- **ARIA লেবেল**: স্ক্রিন রিডার সাপোর্ট
- **কীবোর্ড নেভিগেশন**: ট্যাব সাপোর্ট
- **ফোকাস ম্যানেজমেন্ট**: ভিজুয়াল ফোকাস ইন্ডিকেটর

## ভবিষ্যৎ উন্নতি

- [ ] জেস্চার সাপোর্ট (swipe to open/close)
- [ ] ডার্ক মোড সাপোর্ট
- [ ] কাস্টমাইজেবল কুইক অ্যাকশন
- [ ] অ্যানিমেটেড আইকন
- [ ] ভয়েস সার্চ ইন্টিগ্রেশন
