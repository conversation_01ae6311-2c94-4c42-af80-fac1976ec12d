import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { products, stores, users, notifications, storeFollows } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { desc, eq, and, or, like, gte, lte, count } from "drizzle-orm";

const productSchema = z.object({
  title: z.string().min(3).max(255),
  description: z.string().max(2000).optional().nullable(),
  price: z.number().min(0),
  condition: z.enum(['new', 'like_new', 'good', 'fair', 'poor']),
  category: z.string().min(1).max(100),
  location: z.string().max(255).optional().nullable(),
  photos: z.array(z.string().url()).min(1).max(10),
  storeId: z.string().min(1),
});

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    const condition = searchParams.get("condition") || "";
    const minPrice = searchParams.get("minPrice") ? parseInt(searchParams.get("minPrice") || "0") : null;
    const maxPrice = searchParams.get("maxPrice") ? parseInt(searchParams.get("maxPrice") || "0") : null;
    const storeId = searchParams.get("storeId") || "";
    const limit = parseInt(searchParams.get("limit") || "20");
    const page = parseInt(searchParams.get("page") || "1");
    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = [];

    if (search) {
      whereConditions.push(
        or(
          like(products.title, `%${search}%`),
          like(products.description || "", `%${search}%`)
        )
      );
    }

    if (category) {
      whereConditions.push(eq(products.category, category));
    }

    if (condition) {
      whereConditions.push(eq(products.item_condition, condition as "new" | "like_new" | "good" | "fair" | "poor"));
    }

    if (minPrice !== null) {
      whereConditions.push(gte(products.price, minPrice));
    }

    if (maxPrice !== null) {
      whereConditions.push(lte(products.price, maxPrice));
    }

    if (storeId) {
      whereConditions.push(eq(products.storeId, storeId));
    }

    const whereClause = whereConditions.length > 0
      ? and(...whereConditions)
      : undefined;

    // Get products
    const allProducts = await db
      .select({
        id: products.id,
        title: products.title,
        description: products.description,
        price: products.price,
        condition: products.item_condition,
        category: products.category,
        location: products.location,
        photos: products.photos,
        viewCount: products.viewCount,
        createdAt: products.createdAt,
        store: {
          id: stores.id,
          name: stores.name,
          slug: stores.slug,
          logo: stores.logo,
          isVerified: stores.isVerified,
        },
        owner: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(products)
      .leftJoin(stores, eq(products.storeId, stores.id))
      .leftJoin(users, eq(stores.ownerId, users.id))
      .where(whereClause)
      .orderBy(desc(products.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(products)
      .where(whereClause);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      products: allProducts,
      pagination: {
        total: totalCount,
        totalPages,
        currentPage: page,
        limit,
      },
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { message: "Error fetching products" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = productSchema.parse(body);

    // Check if store exists and belongs to the user
    const existingStore = await db
      .select()
      .from(stores)
      .where(
        and(
          eq(stores.id, validatedData.storeId),
          eq(stores.ownerId, session.user.id)
        )
      )
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found or you don't have permission to add products to it" },
        { status: 404 }
      );
    }

    // Create product
    const productId = uuidv4();
    await db.insert(products).values({
      id: productId,
      title: validatedData.title,
      description: validatedData.description || null,
      price: validatedData.price,
      item_condition: validatedData.condition,
      category: validatedData.category,
      location: validatedData.location || null,
      photos: validatedData.photos,
      storeId: validatedData.storeId,
      viewCount: 0,
    });

    // Get the created product
    const createdProduct = await db
      .select()
      .from(products)
      .where(eq(products.id, productId))
      .limit(1);

    // Create notifications for store followers - only if storeFollows table exists
    try {
      const storeFollowers = await db
        .select({
          userId: storeFollows.userId,
        })
        .from(storeFollows)
        .where(eq(storeFollows.storeId, validatedData.storeId));

      // Batch insert notifications
      if (storeFollowers.length > 0) {
        const notificationValues = storeFollowers.map(follower => ({
          id: uuidv4(),
          recipientId: follower.userId,
          type: "product_new" as const,
          senderId: session.user.id,
          storeId: validatedData.storeId,
          productId,
          read: false,
        }));

        await db.insert(notifications).values(notificationValues);
      }
    } catch (notificationError) {
      // If there's an error with notifications, log it but don't fail the product creation
      console.error("Error creating notifications (non-critical):", notificationError);
    }

    return NextResponse.json(
      { message: "Product created successfully", product: createdProduct[0] },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating product:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Error creating product" },
      { status: 500 }
    );
  }
}
