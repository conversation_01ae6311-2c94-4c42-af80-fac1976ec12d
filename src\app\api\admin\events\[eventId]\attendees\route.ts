import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { events, eventAttendees, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, desc } from "drizzle-orm";

const attendeeSchema = z.object({
  userId: z.string(),
  status: z.enum(["going", "interested", "not_going"]),
});

const attendeeActionSchema = z.object({
  userId: z.string(),
  action: z.enum(["add", "remove", "update"]),
  status: z.enum(["going", "interested", "not_going"]).optional(),
});

// Get all attendees for an event
export async function GET(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { eventId } = params;
    const url = new URL(req.url);
    const status = url.searchParams.get("status") || "all"; // all, going, interested, not_going

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Fetch attendees separately to avoid collation issues
    let attendeesList = await db.query.eventAttendees.findMany({
      where: eq(eventAttendees.eventId, eventId),
      orderBy: desc(eventAttendees.createdAt),
    });

    // Filter by status if specified
    if (status !== "all") {
      attendeesList = attendeesList.filter(a => a.status === status);
    }

    // Fetch user data for each attendee
    const attendeesData = await Promise.all(
      attendeesList.map(async (attendee) => {
        const userData = await db.query.users.findFirst({
          where: eq(users.id, attendee.userId),
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        });
        return { attendee, user: userData };
      })
    );

    // Format the response
    const formattedAttendees = attendeesData
      .filter(({ user }) => user) // Filter out entries where user is undefined
      .map(({ attendee, user }) => ({
        id: attendee.id,
        status: attendee.status,
        createdAt: attendee.createdAt,
        updatedAt: attendee.updatedAt,
        user: {
          id: user!.id,
          name: user!.name,
          username: user!.username,
          image: user!.image,
        },
      }));

    return NextResponse.json({
      attendees: formattedAttendees,
      count: formattedAttendees.length,
    });
  } catch (error) {
    console.error("Error fetching attendees:", error);
    return NextResponse.json(
      { message: "Failed to fetch attendees" },
      { status: 500 }
    );
  }
}

// Perform admin actions on event attendees
export async function POST(
  req: Request,
  context: { params: Promise<{ eventId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle Promise params
    const params = await context.params;
    const { eventId } = params;
    const body = await req.json();
    const { userId, action, status } = attendeeActionSchema.parse(body);

    // Check if the event exists
    const event = await db.query.events.findFirst({
      where: eq(events.id, eventId),
    });

    if (!event) {
      return NextResponse.json(
        { message: "Event not found" },
        { status: 404 }
      );
    }

    // Check if the user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Check if the user is already an attendee
    const existingAttendee = await db.query.eventAttendees.findFirst({
      where: and(
        eq(eventAttendees.eventId, eventId),
        eq(eventAttendees.userId, userId)
      ),
    });

    // Perform the requested action
    if (action === "add") {
      if (!status) {
        return NextResponse.json(
          { message: "Status is required for add action" },
          { status: 400 }
        );
      }

      if (existingAttendee) {
        return NextResponse.json(
          { message: "User is already an attendee" },
          { status: 400 }
        );
      }

      // Add the user as an attendee
      const attendeeId = uuidv4();
      await db.insert(eventAttendees).values({
        id: attendeeId,
        eventId,
        userId,
        status,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Fetch the created attendee
      const createdAttendee = await db.query.eventAttendees.findFirst({
        where: eq(eventAttendees.id, attendeeId),
        with: {
          user: true,
        },
      });

      return NextResponse.json({
        message: "Attendee added successfully",
        attendee: {
          ...createdAttendee,
          user: {
            id: createdAttendee?.user.id,
            name: createdAttendee?.user.name,
            username: createdAttendee?.user.username,
            image: createdAttendee?.user.image,
          },
        },
      });
    } else if (action === "update") {
      if (!status) {
        return NextResponse.json(
          { message: "Status is required for update action" },
          { status: 400 }
        );
      }

      if (!existingAttendee) {
        return NextResponse.json(
          { message: "User is not an attendee" },
          { status: 404 }
        );
      }

      // Update the attendee status
      await db.update(eventAttendees)
        .set({
          status,
          updatedAt: new Date(),
        })
        .where(eq(eventAttendees.id, existingAttendee.id));

      // Fetch the updated attendee
      const updatedAttendee = await db.query.eventAttendees.findFirst({
        where: eq(eventAttendees.id, existingAttendee.id),
        with: {
          user: true,
        },
      });

      return NextResponse.json({
        message: "Attendee updated successfully",
        attendee: {
          ...updatedAttendee,
          user: {
            id: updatedAttendee?.user.id,
            name: updatedAttendee?.user.name,
            username: updatedAttendee?.user.username,
            image: updatedAttendee?.user.image,
          },
        },
      });
    } else if (action === "remove") {
      if (!existingAttendee) {
        return NextResponse.json(
          { message: "User is not an attendee" },
          { status: 404 }
        );
      }

      // Remove the attendee
      await db.delete(eventAttendees)
        .where(eq(eventAttendees.id, existingAttendee.id));

      return NextResponse.json({
        message: "Attendee removed successfully",
      });
    }

    return NextResponse.json(
      { message: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error managing attendees:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Failed to manage attendees" },
      { status: 500 }
    );
  }
}
