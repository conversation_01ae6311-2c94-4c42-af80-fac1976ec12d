"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { PostCard } from "@/components/feed/PostCard";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import Link from "next/link";
import { MainLayout } from "@/components/layout/MainLayout";
import { PublicLayout } from "@/components/layout/PublicLayout";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { RightSidebar } from "@/components/layout/RightSidebar";

interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  };
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
  sharedPost?: {
    id: string;
    content: string;
    images: string[] | null;
    videos: string[] | null;
    backgroundColor: string | null;
    feeling: string | null;
    activity: string | null;
    location: string | null;
    formattedContent: boolean | null;
    createdAt: string;
    user: {
      id: string;
      name: string;
      username?: string | null;
      image: string | null;
    };
  } | null;
}

export default function PostPage() {
  const params = useParams();
  const { data: session } = useSession();
  const [post, setPost] = useState<Post | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const postId = params?.postId as string;

  useEffect(() => {
    async function fetchPost() {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/posts/${postId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("Post not found");
          }
          throw new Error("Failed to fetch post");
        }

        const data = await response.json();

        // Ensure all required fields are present
        const processedPost = {
          ...data,
          videos: data.videos || null,
          privacy: data.privacy || 'public',
          backgroundColor: data.backgroundColor || null,
          feeling: data.feeling || null,
          activity: data.activity || null,
          location: data.location || null,
          formattedContent: data.formattedContent || null,
          type: data.type || 'user_post',
          fanPage: data.fanPage || null,
          group: data.group || null,
          user: {
            ...data.user,
            username: data.user.username || null
          },
          sharedPost: data.sharedPost ? {
            ...data.sharedPost,
            videos: data.sharedPost.videos || null,
            backgroundColor: data.sharedPost.backgroundColor || null,
            feeling: data.sharedPost.feeling || null,
            activity: data.sharedPost.activity || null,
            location: data.sharedPost.location || null,
            formattedContent: data.sharedPost.formattedContent || null,
            user: {
              ...data.sharedPost.user,
              username: data.sharedPost.user.username || null
            }
          } : null
        };

        setPost(processedPost);
      } catch (err) {
        console.error("Error fetching post:", err);
        setError(err instanceof Error ? err.message : "Failed to load post");
      } finally {
        setIsLoading(false);
      }
    }

    if (session?.user) {
      fetchPost();
    }
  }, [postId, session]);

  const handleLike = async () => {
    if (!post) return;

    try {
      // Call the API to like/unlike the post
      const response = await fetch(`/api/posts/${post.id}/like`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to like post");
      }

      // Update the state optimistically
      setPost(prevPost => {
        if (!prevPost) return null;

        const newLiked = !prevPost.liked;
        // If post was disliked and now being liked, remove the dislike
        const wasDisliked = prevPost.disliked;

        return {
          ...prevPost,
          liked: newLiked,
          disliked: wasDisliked ? false : prevPost.disliked,
          _count: {
            ...prevPost._count,
            likes: prevPost._count.likes + (newLiked ? 1 : -1),
            dislikes: wasDisliked ? prevPost._count.dislikes - 1 : prevPost._count.dislikes,
          },
        };
      });
    } catch (error) {
      console.error("Error liking post:", error);
    }
  };

  const handleDislike = async () => {
    if (!post) return;

    try {
      // Call the API to dislike/undislike the post
      const response = await fetch(`/api/posts/${post.id}/dislike`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to dislike post");
      }

      // Update the state optimistically
      setPost(prevPost => {
        if (!prevPost) return null;

        const newDisliked = !prevPost.disliked;
        // If post was liked and now being disliked, remove the like
        const wasLiked = prevPost.liked;

        return {
          ...prevPost,
          disliked: newDisliked,
          liked: wasLiked ? false : prevPost.liked,
          _count: {
            ...prevPost._count,
            dislikes: prevPost._count.dislikes + (newDisliked ? 1 : -1),
            likes: wasLiked ? prevPost._count.likes - 1 : prevPost._count.likes,
          },
        };
      });
    } catch (error) {
      console.error("Error disliking post:", error);
    }
  };

  if (!session?.user) {
    return (
      <PublicLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-white p-8 text-center shadow">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">Join the Conversation</h2>
            <p className="mb-6 text-gray-500">
              Sign in to view this post and join the conversation.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/login">
                <Button>Sign In</Button>
              </Link>
              <Link href="/register">
                <Button variant="outline">Create Account</Button>
              </Link>
            </div>
          </div>
        </div>
      </PublicLayout>
    );
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center py-12">
            <Spinner />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-red-50 p-6 text-center">
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!post) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-white p-8 text-center shadow">
            <p className="text-gray-500">
              Post not found.
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-5 lg:grid-cols-4">
          {/* Left sidebar */}
          <LeftSidebar />

          {/* Main content */}
          <div className="lg:col-span-2 space-y-5">
            <div className="transform transition-all duration-300 hover:translate-y-[-2px]">
              <PostCard
                post={{
                  ...post,
                  type: 'user_post' as const,
                  fanPage: null,
                  group: null
                }}
                onLike={handleLike}
                onDislike={handleDislike}
              />
            </div>
          </div>

          {/* Right sidebar */}
          <RightSidebar />
        </div>
      </div>
    </MainLayout>
  );
}
